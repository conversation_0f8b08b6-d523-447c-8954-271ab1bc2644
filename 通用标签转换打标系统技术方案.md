# 通用标签转换打标系统技术方案

## 1. 系统概述

设计一套通用的标签转换和打标系统，支持多种标签平台之间的人群转换，具备大数据量处理能力和完善的故障恢复机制。

## 2. 数据库设计

### 2.1 通用标签转换记录表

```sql
-- 通用标签转换记录表
CREATE TABLE `tag_convert_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID，全局唯一',
  `source_platform` varchar(32) NOT NULL COMMENT '源平台：zhuge(诸葛)、turing(图灵)、alipay(支付宝)、wechat(微信)等',
  `target_platform` varchar(32) NOT NULL COMMENT '目标平台',
  `source_crowd_id` varchar(64) NOT NULL COMMENT '源人群ID',
  `target_crowd_id` varchar(64) DEFAULT NULL COMMENT '目标人群ID',
  `source_crowd_name` varchar(128) NOT NULL COMMENT '源人群名称',
  `target_crowd_name` varchar(128) DEFAULT NULL COMMENT '目标人群名称',
  `source_crowd_count` bigint(20) DEFAULT 0 COMMENT '源人群个数',
  `target_crowd_count` bigint(20) DEFAULT 0 COMMENT '目标人群个数',
  `convert_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '转换状态：0-待处理，1-处理中，2-成功，3-失败，4-部分成功',
  `convert_type` varchar(32) NOT NULL COMMENT '转换类型：crowd_to_crowd(人群转人群)、tag_to_tag(标签转标签)',
  `source_tag_info` json DEFAULT NULL COMMENT '源标签信息（JSON格式）',
  `target_tag_info` json DEFAULT NULL COMMENT '目标标签信息（JSON格式）',
  `convert_config` json DEFAULT NULL COMMENT '转换配置（JSON格式）',
  `progress_info` json DEFAULT NULL COMMENT '进度信息（JSON格式）',
  `error_message` text COMMENT '错误信息',
  `operator` varchar(64) NOT NULL COMMENT '操作人',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `estimated_duration` int(11) DEFAULT NULL COMMENT '预估耗时（秒）',
  `actual_duration` int(11) DEFAULT NULL COMMENT '实际耗时（秒）',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `max_retry_count` int(11) DEFAULT 3 COMMENT '最大重试次数',
  `priority` tinyint(4) DEFAULT 5 COMMENT '优先级：1-最高，5-普通，10-最低',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `creator_id` varchar(64) DEFAULT NULL COMMENT '创建人工号',
  `creator_name` varchar(64) DEFAULT NULL COMMENT '创建人名称',
  `last_operator_id` varchar(64) DEFAULT NULL COMMENT '最近一次更新工号',
  `last_operator_name` varchar(64) DEFAULT NULL COMMENT '最近一次更新名称',
  `deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_source_platform_crowd` (`source_platform`, `source_crowd_id`),
  KEY `idx_target_platform_crowd` (`target_platform`, `target_crowd_id`),
  KEY `idx_convert_status` (`convert_status`),
  KEY `idx_operator` (`operator`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_priority_status` (`priority`, `convert_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用标签转换记录表';

-- 按月分表
CREATE TABLE `tag_convert_record_202501` LIKE `tag_convert_record`;
CREATE TABLE `tag_convert_record_202502` LIKE `tag_convert_record`;
-- ...
```

### 2.2 转换任务分片表

```sql
-- 转换任务分片表
CREATE TABLE `tag_convert_shard` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(64) NOT NULL COMMENT '主任务ID',
  `shard_id` varchar(64) NOT NULL COMMENT '分片ID',
  `shard_index` int(11) NOT NULL COMMENT '分片索引',
  `total_shards` int(11) NOT NULL COMMENT '总分片数',
  `start_offset` bigint(20) NOT NULL COMMENT '开始偏移量',
  `end_offset` bigint(20) NOT NULL COMMENT '结束偏移量',
  `shard_size` bigint(20) NOT NULL COMMENT '分片大小',
  `processed_count` bigint(20) DEFAULT 0 COMMENT '已处理数量',
  `success_count` bigint(20) DEFAULT 0 COMMENT '成功数量',
  `fail_count` bigint(20) DEFAULT 0 COMMENT '失败数量',
  `shard_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '分片状态：0-待处理，1-处理中，2-成功，3-失败',
  `worker_node_id` varchar(64) DEFAULT NULL COMMENT '执行节点ID',
  `worker_ip` varchar(32) DEFAULT NULL COMMENT '执行节点IP',
  `last_heartbeat` datetime DEFAULT NULL COMMENT '最后心跳时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `checkpoint_data` json DEFAULT NULL COMMENT '检查点数据（JSON格式）',
  `error_message` text COMMENT '错误信息',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_shard_id` (`shard_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_shard_status` (`shard_status`),
  KEY `idx_worker_node_id` (`worker_node_id`),
  KEY `idx_last_heartbeat` (`last_heartbeat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转换任务分片表';
```

### 2.3 用户打标记录表

```sql
-- 用户打标记录表（分库分表）
CREATE TABLE `user_tag_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `record_id` varchar(64) NOT NULL COMMENT '记录ID，全局唯一',
  `task_id` varchar(64) NOT NULL COMMENT '关联的转换任务ID',
  `shard_id` varchar(64) DEFAULT NULL COMMENT '关联的分片ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `user_id_type` varchar(32) NOT NULL COMMENT '用户ID类型：taobao_id、alipay_id、wechat_union_id等',
  `platform` varchar(32) NOT NULL COMMENT '打标平台',
  `tag_operation` varchar(16) NOT NULL COMMENT '标签操作：add(添加)、remove(删除)、update(更新)',
  `tag_info` json NOT NULL COMMENT '标签信息（JSON格式）',
  `tag_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '打标状态：0-待处理，1-处理中，2-成功，3-失败',
  `retry_count` int(11) DEFAULT 0 COMMENT '重试次数',
  `error_code` varchar(32) DEFAULT NULL COMMENT '错误码',
  `error_message` text COMMENT '错误信息',
  `request_time` datetime DEFAULT NULL COMMENT '请求时间',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  `duration_ms` int(11) DEFAULT NULL COMMENT '耗时（毫秒）',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_record_id` (`record_id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_platform_status` (`platform`, `tag_status`),
  KEY `idx_gmt_create` (`gmt_create`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户打标记录表';

-- 分表策略：按user_id哈希分64张表
-- user_tag_record_00, user_tag_record_01, ..., user_tag_record_63
```

### 2.4 故障恢复表

```sql
-- 故障恢复表
CREATE TABLE `task_recovery_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `shard_id` varchar(64) DEFAULT NULL COMMENT '分片ID',
  `recovery_type` varchar(32) NOT NULL COMMENT '恢复类型：timeout(超时)、node_failure(节点故障)、manual(手动)',
  `failure_reason` varchar(128) DEFAULT NULL COMMENT '故障原因',
  `recovery_action` varchar(32) NOT NULL COMMENT '恢复动作：retry(重试)、reassign(重新分配)、skip(跳过)',
  `original_node_id` varchar(64) DEFAULT NULL COMMENT '原执行节点ID',
  `new_node_id` varchar(64) DEFAULT NULL COMMENT '新执行节点ID',
  `recovery_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '恢复状态：0-待处理，1-处理中，2-成功，3-失败',
  `checkpoint_before` json DEFAULT NULL COMMENT '恢复前检查点',
  `checkpoint_after` json DEFAULT NULL COMMENT '恢复后检查点',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人（手动恢复时）',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_recovery_type` (`recovery_type`),
  KEY `idx_recovery_status` (`recovery_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='故障恢复日志表';
```

## 3. 大数据量处理方案

### 3.1 分片策略

```java
@Component
public class TaskShardingStrategy {
    
    // 分片配置
    private static final long MAX_SHARD_SIZE = 100000; // 每个分片最大10万用户
    private static final int MAX_SHARDS = 1000; // 最大分片数
    private static final int MIN_SHARDS = 1; // 最小分片数
    
    public List<TaskShard> calculateShards(long totalCount) {
        // 计算分片数量
        int shardCount = (int) Math.ceil((double) totalCount / MAX_SHARD_SIZE);
        shardCount = Math.max(MIN_SHARDS, Math.min(shardCount, MAX_SHARDS));
        
        // 计算每个分片的大小
        long baseShardSize = totalCount / shardCount;
        long remainder = totalCount % shardCount;
        
        List<TaskShard> shards = new ArrayList<>();
        long currentOffset = 0;
        
        for (int i = 0; i < shardCount; i++) {
            long shardSize = baseShardSize + (i < remainder ? 1 : 0);
            
            TaskShard shard = new TaskShard();
            shard.setShardIndex(i);
            shard.setStartOffset(currentOffset);
            shard.setEndOffset(currentOffset + shardSize - 1);
            shard.setShardSize(shardSize);
            
            shards.add(shard);
            currentOffset += shardSize;
        }
        
        return shards;
    }
}
```

### 3.2 异步处理架构

```java
// 主任务消息
@Data
public class TagConvertMainTask {
    private String taskId;
    private String sourcePlatform;
    private String targetPlatform;
    private String sourceCrowdId;
    private String targetCrowdId;
    private ConvertConfig config;
    private String operator;
    private int priority;
}

// 分片任务消息
@Data
public class TagConvertShardTask {
    private String taskId;
    private String shardId;
    private int shardIndex;
    private long startOffset;
    private long endOffset;
    private ConvertConfig config;
    private boolean isRetry;
    private TaskCheckpoint checkpoint;
}
```

### 3.3 消息队列配置

```yaml
# RocketMQ配置
rocketmq:
  producer:
    group: tag-convert-producer
    send-message-timeout: 30000
    retry-times-when-send-failed: 3
  consumer:
    group: tag-convert-consumer
    consume-thread-min: 20
    consume-thread-max: 50
    consume-message-batch-max-size: 10
    
# 队列主题配置
topics:
  main-task: tag_convert_main_task
  shard-task: tag_convert_shard_task
  retry-task: tag_convert_retry_task
  dead-letter: tag_convert_dead_letter
```

## 4. 故障恢复机制

### 4.1 心跳检测

```java
@Component
public class TaskHeartbeatManager {
    
    private static final int HEARTBEAT_INTERVAL = 30; // 30秒
    private static final int TIMEOUT_THRESHOLD = 180; // 3分钟超时
    
    @Scheduled(fixedRate = 30000)
    public void updateHeartbeat() {
        String nodeId = SystemUtils.getNodeId();
        List<String> runningShards = getRunningShards();
        
        for (String shardId : runningShards) {
            tagConvertService.updateShardHeartbeat(shardId, nodeId);
        }
    }
    
    @Scheduled(fixedRate = 60000)
    public void detectTimeoutShards() {
        DateTime timeoutThreshold = DateTime.now().minusSeconds(TIMEOUT_THRESHOLD);
        List<TagConvertShard> timeoutShards = 
            tagConvertService.findTimeoutShards(timeoutThreshold);
            
        for (TagConvertShard shard : timeoutShards) {
            handleTimeoutShard(shard);
        }
    }
    
    private void handleTimeoutShard(TagConvertShard shard) {
        // 记录故障恢复日志
        TaskRecoveryLog recoveryLog = new TaskRecoveryLog();
        recoveryLog.setTaskId(shard.getTaskId());
        recoveryLog.setShardId(shard.getShardId());
        recoveryLog.setRecoveryType("timeout");
        recoveryLog.setOriginalNodeId(shard.getWorkerNodeId());
        recoveryLog.setRecoveryAction("retry");
        
        taskRecoveryService.createRecoveryLog(recoveryLog);
        
        // 重新分配分片
        reassignShard(shard);
    }
}
```

### 4.2 检查点机制

```java
@Data
public class TaskCheckpoint {
    private String shardId;
    private long processedCount;
    private long successCount;
    private long failCount;
    private List<String> processedUserIds;
    private List<String> failedUserIds;
    private String currentBatchId;
    private DateTime checkpointTime;
    private Map<String, Object> contextData;
}

@Service
public class CheckpointManager {
    
    private static final int CHECKPOINT_INTERVAL = 1000; // 每1000个用户保存一次
    
    public void saveCheckpoint(String shardId, TaskCheckpoint checkpoint) {
        // 保存到Redis
        String key = "checkpoint:" + shardId;
        redisTemplate.opsForValue().set(key, JSON.toJSONString(checkpoint), 
            Duration.ofDays(7));
            
        // 异步保存到数据库
        tagConvertService.updateShardCheckpoint(shardId, checkpoint);
    }
    
    public TaskCheckpoint loadCheckpoint(String shardId) {
        // 先从Redis加载
        String key = "checkpoint:" + shardId;
        String checkpointJson = redisTemplate.opsForValue().get(key);
        
        if (StringUtils.isEmpty(checkpointJson)) {
            // 从数据库加载
            checkpointJson = tagConvertService.getShardCheckpoint(shardId);
        }
        
        return StringUtils.isNotEmpty(checkpointJson) ? 
            JSON.parseObject(checkpointJson, TaskCheckpoint.class) : null;
    }
}
```

### 4.3 故障恢复流程

```java
@Service
public class TaskRecoveryService {
    
    public void recoverFailedTasks() {
        // 查找需要恢复的任务
        List<TagConvertShard> failedShards = findFailedShards();
        
        for (TagConvertShard shard : failedShards) {
            try {
                recoverShard(shard);
            } catch (Exception e) {
                log.error("恢复分片失败: shardId={}", shard.getShardId(), e);
            }
        }
    }
    
    private void recoverShard(TagConvertShard shard) {
        // 检查重试次数
        if (shard.getRetryCount() >= 3) {
            markShardAsFinalFailed(shard);
            return;
        }
        
        // 加载检查点
        TaskCheckpoint checkpoint = checkpointManager.loadCheckpoint(shard.getShardId());
        
        // 创建恢复任务
        TagConvertShardTask recoveryTask = new TagConvertShardTask();
        recoveryTask.setTaskId(shard.getTaskId());
        recoveryTask.setShardId(shard.getShardId());
        recoveryTask.setIsRetry(true);
        recoveryTask.setCheckpoint(checkpoint);
        
        // 发送到重试队列
        messageProducer.sendRetryTask(recoveryTask);
        
        // 更新分片状态
        shard.setShardStatus(ShardStatusEnum.PENDING.getCode());
        shard.setRetryCount(shard.getRetryCount() + 1);
        tagConvertService.updateShard(shard);
    }
}
```

## 5. 幂等性保证

### 5.1 用户级幂等

```java
@Service
public class IdempotentService {
    
    public boolean isUserAlreadyTagged(String userId, String platform, 
                                     String tagId, String operation) {
        String key = String.format("tagged:%s:%s:%s:%s", 
            userId, platform, tagId, operation);
        return redisTemplate.hasKey(key);
    }
    
    public void markUserAsTagged(String userId, String platform, 
                               String tagId, String operation) {
        String key = String.format("tagged:%s:%s:%s:%s", 
            userId, platform, tagId, operation);
        redisTemplate.opsForValue().set(key, "1", Duration.ofDays(30));
    }
    
    public void batchMarkUsersAsTagged(List<String> userIds, String platform, 
                                     String tagId, String operation) {
        Map<String, String> batch = new HashMap<>();
        for (String userId : userIds) {
            String key = String.format("tagged:%s:%s:%s:%s", 
                userId, platform, tagId, operation);
            batch.put(key, "1");
        }
        
        redisTemplate.opsForValue().multiSet(batch);
        
        // 设置过期时间
        for (String key : batch.keySet()) {
            redisTemplate.expire(key, Duration.ofDays(30));
        }
    }
}
```

## 6. 监控和告警

### 6.1 关键指标

```java
@Component
public class TagConvertMetrics {
    
    @EventListener
    public void onTaskStarted(TaskStartedEvent event) {
        meterRegistry.counter("tag.convert.task.started",
            "source_platform", event.getSourcePlatform(),
            "target_platform", event.getTargetPlatform()).increment();
    }
    
    @EventListener
    public void onTaskCompleted(TaskCompletedEvent event) {
        meterRegistry.counter("tag.convert.task.completed",
            "status", event.getStatus()).increment();
            
        meterRegistry.timer("tag.convert.task.duration")
            .record(event.getDuration(), TimeUnit.MILLISECONDS);
    }
    
    @EventListener
    public void onUserTagged(UserTaggedEvent event) {
        meterRegistry.counter("tag.convert.user.tagged",
            "platform", event.getPlatform(),
            "status", event.getStatus()).increment();
    }
    
    @Scheduled(fixedRate = 60000)
    public void collectMetrics() {
        // 活跃任务数
        long activeTasks = tagConvertService.countActiveTasks();
        meterRegistry.gauge("tag.convert.active.tasks", activeTasks);
        
        // 队列积压
        long queueBacklog = getQueueBacklog();
        meterRegistry.gauge("tag.convert.queue.backlog", queueBacklog);
        
        // 失败率
        double failureRate = calculateFailureRate();
        meterRegistry.gauge("tag.convert.failure.rate", failureRate);
    }
}
```

### 6.2 告警配置

```yaml
alerts:
  - name: 任务失败率过高
    condition: tag_convert_failure_rate > 0.1
    duration: 5m
    action: 钉钉告警
    
  - name: 队列积压严重
    condition: tag_convert_queue_backlog > 10000
    duration: 2m
    action: 钉钉告警 + 自动扩容
    
  - name: 处理速度过慢
    condition: rate(tag_convert_user_tagged_total[5m]) < 1000
    duration: 10m
    action: 钉钉告警
    
  - name: 节点故障
    condition: up{job="tag-convert-worker"} == 0
    duration: 1m
    action: 钉钉告警 + 任务重新分配
```

## 7. 容量规划

### 7.1 性能基准

```
单节点处理能力：
- CPU: 8核
- 内存: 16GB
- 网络: 1Gbps
- 处理速度: 1000用户/秒

集群配置（1亿用户）：
- 目标完成时间: 2小时
- 需要处理速度: 1亿 / (2 * 3600) ≈ 14000用户/秒
- 需要节点数: 14000 / 1000 = 14个节点
- 考虑冗余: 建议20个节点
```

### 7.2 资源配置

```yaml
# Kubernetes配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tag-convert-worker
spec:
  replicas: 20
  template:
    spec:
      containers:
      - name: worker
        image: tag-convert-worker:latest
        resources:
          requests:
            cpu: 4
            memory: 8Gi
          limits:
            cpu: 8
            memory: 16Gi
        env:
        - name: WORKER_THREADS
          value: "10"
        - name: BATCH_SIZE
          value: "1000"
        - name: MAX_RETRY_COUNT
          value: "3"

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: tag-convert-worker-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: tag-convert-worker
  minReplicas: 10
  maxReplicas: 100
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Pods
    pods:
      metric:
        name: queue_messages_per_pod
      target:
        type: AverageValue
        averageValue: "100"
```

## 8. 使用示例

### 8.1 诸葛到图灵转换

```json
{
  "sourcePlatform": "zhuge",
  "targetPlatform": "turing",
  "sourceCrowdId": "zhuge_crowd_12345",
  "sourceCrowdName": "高价值用户群体",
  "targetTagInfo": {
    "firstLevelTag": "用户画像",
    "secondLevelTag": "高价值用户",
    "tagId": "turing_tag_67890"
  },
  "convertConfig": {
    "batchSize": 1000,
    "maxRetryCount": 3,
    "timeoutSeconds": 300,
    "enableCheckpoint": true
  },
  "operator": "张三"
}
```

### 8.2 微信到支付宝转换

```json
{
  "sourcePlatform": "wechat",
  "targetPlatform": "alipay",
  "sourceCrowdId": "wechat_group_54321",
  "sourceCrowdName": "活跃用户群",
  "targetTagInfo": {
    "tagType": "behavior",
    "tagName": "活跃用户",
    "tagValue": "high_activity"
  },
  "convertConfig": {
    "idMappingType": "unionid_to_alipayid",
    "batchSize": 500,
    "maxRetryCount": 5
  },
  "operator": "李四"
}
```

## 9. 实施步骤

### 9.1 第一阶段：基础设施
1. 创建数据库表结构
2. 部署消息队列集群
3. 部署Redis集群
4. 搭建监控系统

### 9.2 第二阶段：核心功能
1. 实现任务分片逻辑
2. 实现故障检测和恢复
3. 实现检查点机制
4. 实现幂等性保证

### 9.3 第三阶段：平台适配
1. 实现诸葛平台适配器
2. 实现图灵平台适配器
3. 实现支付宝平台适配器
4. 实现微信平台适配器

### 9.4 第四阶段：优化部署
1. 性能测试和调优
2. 压力测试验证
3. 生产环境部署
4. 监控告警完善

这套方案提供了通用的标签转换框架，支持多平台扩展、大数据量处理和完善的故障恢复机制，能够满足各种标签转换场景的需求。