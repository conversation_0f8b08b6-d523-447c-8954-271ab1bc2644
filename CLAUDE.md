# TripSCRM 系统架构文档

## 1. 项目概述

TripSCRM 是一个基于企业微信的客户关系管理系统，主要围绕任务框架、素材管理、活动SOP等核心功能构建，底层依赖企业微信的成员、客户、群聊等基础关系管理。

### 1.1 项目结构
```
tripscrm
├── tripscrm-client     # 客户端模块，定义服务接口
├── tripscrm-dal        # 数据访问层，包含数据库实体和DAO
├── tripscrm-service    # 业务逻辑层，实现核心业务逻辑
└── tripscrm-start      # 启动模块，包含控制器和配置
```

### 1.2 技术栈
- Java 8
- Spring Boot 2.5.12
- MyBatis
- Maven
- Pandora Boot
- Knife4j (Swagger API文档)
- Velocity (模板引擎)
- HSF (高性能服务框架)
- TDDL (分布式数据库访问层)
- Tair (分布式缓存)
- MetaQ (消息队列)
- Sentinel (流量控制)

## 2. 核心模块

### 2.1 任务框架 (Task Framework)

任务框架是系统的核心执行引擎，负责定时任务、事件触发任务等的管理和执行。

#### 2.1.1 核心组件
- **TaskQuery**: 任务查询条件封装类
- **TaskInstance**: 任务实例
- **TaskExecuteResult**: 任务执行结果
- **EventSource**: 事件源管理

#### 2.1.2 功能特性
- 支持定时任务和事件触发任务
- 任务状态管理（待执行、执行中、已完成、已失败等）
- 任务执行结果跟踪
- 任务与活动、素材的关联管理

### 2.2 素材管理 (Material Management)

素材管理模块负责各种内容素材的存储、分类和使用跟踪。

#### 2.2.1 核心组件
- **MaterialInfo**: 素材基本信息
- **MaterialTemplateInfo**: 素材模板信息
- **MaterialInfoQuery**: 素材查询条件

#### 2.2.2 功能特性
- 素材的增删改查
- 素材模板管理
- 素材与任务、活动的关联
- 素材使用统计和跟踪

### 2.3 活动SOP (Activity SOP)

活动SOP模块提供了标准化的活动管理流程，支持多种类型的活动配置和执行。

#### 2.3.1 核心组件
- **ActivitySopVO**: 活动SOP视图对象
- **AbstractActivitySopConfigVO**: SOP配置抽象类
- **各种具体SOP配置类**:
  - InviteJoinGroupActivitySopConfigVO (邀请入群活动)
  - CallPushActivitySopConfigVO (电话推送活动)
  - CustomerRecallSopConfigVO (客户召回活动)
  - GroupActivitySopConfigVO (群活动)
  - WechatAddCustomerByGroupActivitySopConfigVO (微信群加客户活动)

#### 2.3.2 功能特性
- 活动创建、更新、查询
- 活动上线/下线状态管理
- 活动与任务的关联
- 活动执行效果跟踪

## 3. 底层企微关系管理

### 3.1 企微成员管理 (Enterprise WeChat User Management)

#### 3.1.1 核心服务
- **EnterpriseWechatUserService**: 企微成员服务接口
- 主要功能:
  - 成员查询（支持模糊搜索）
  - 成员在线状态查询
  - 手机号添加好友
  - 客户分配
  - 群组继承
  - 用户删除

### 3.2 企微客户管理 (Enterprise WeChat Customer Management)

#### 3.2.1 核心服务
- **EnterpriseWechatCustomerService**: 企微客户服务接口
- 主要功能:
  - 获取客户的企业好友关系
  - 检查企微关系是否存在
  - 批量查询客户关注的企微好友
  - 客户信息分页查询
  - 客户标签查询

### 3.3 企微群聊管理 (Enterprise WeChat Group Chat Management)

#### 3.3.1 核心服务
- **EnterpriseWechatGroupChatService**: 企微群聊服务接口
- 主要功能:
  - 获取群内成员列表
  - 获取群聊信息
  - 异步创建群聊
  - 异步邀请成员入群
  - 群主转让
  - 管理员设置/修改
  - 查询用户管理的群列表
  - 移除群成员
  - 发布群公告

## 4. 系统架构

### 4.1 分层架构
```
┌─────────────────────────────────────┐
│              Controller             │  请求入口层 (tripscrm-start)
├─────────────────────────────────────┤
│              Service                │  业务逻辑层 (tripscrm-service)
├─────────────────────────────────────┤
│         Client/DAO/Manager          │  数据访问层 (tripscrm-dal)
├─────────────────────────────────────┤
│              Dal/Model              │  数据模型层 (tripscrm-dal)
└─────────────────────────────────────┘
```

### 4.2 核心流程
1. 用户通过Controller发起请求
2. Service层处理业务逻辑
3. 通过Client调用底层接口或通过DAO访问数据库
4. 返回结果给用户

### 4.3 数据流向
1. 任务框架通过事件源监听触发任务执行
2. 任务执行时可能使用素材管理中的素材
3. 任务执行结果影响活动SOP的状态
4. 底层企微关系管理为上层功能提供基础数据支持

## 5. 关键特性

### 5.1 任务驱动
系统以任务为核心驱动力，所有的业务操作最终都会转化为任务来执行。

### 5.2 模板化配置
通过素材模板和活动SOP模板，实现配置的标准化和复用。

### 5.3 企业微信深度集成
充分利用企业微信的能力，包括成员管理、客户关系、群聊管理等。

### 5.4 灵活的扩展性
通过抽象类和接口设计，支持不同类型活动和任务的扩展。

## 6. 依赖组件

### 6.1 核心框架依赖
- **Spring Boot**: 2.5.12
- **MyBatis**: 2.1.0
- **Pandora Boot**: 2025-02-release-fix
- **Knife4j**: 4.3.0 (API文档)
- **Velocity**: 1.0.4.RELEASE (模板引擎)

### 6.2 中间件依赖
- **HSF**: 高性能服务框架
- **TDDL**: 分布式数据库访问层
- **Tair**: 分布式缓存 (c7ba8e0d894d4269, 5348dcf6319d43dd)
- **MetaQ**: 消息队列
- **Sentinel**: 流量控制 3.9.34
- **Tbsession**: 会话管理 4.0.30

### 6.3 数据库和存储
- **MySQL**: 主要数据存储
- **Lindorm**: 2.4.3 (可能用于日志或时序数据存储)
- **OSS**: 阿里云对象存储 (用于文件存储)

### 6.4 第三方服务依赖
- **企业微信API**: 核心依赖
- **支付宝开放平台**: 支付相关功能
- **阿里云服务**: 包括ODPS、OpenSearch等
- **携程等OTA平台**: 旅行相关数据接口
- **钉钉**: 2.1.10 (可能用于内部沟通集成)

### 6.5 工具和库依赖
- **FastJSON**: 2.0.35 (JSON处理)
- **OkHttp**: 4.10.0 (HTTP客户端)
- **Retrofit**: 2.9.0 (REST API客户端)
- **Guava**: 32.1.2-jre (Google工具库)
- **EasyExcel**: 3.1.1 (Excel处理)
- **Groovy**: 2.4.7 (脚本语言)
- **Cron Utils**: 9.2.1 (定时任务解析)

### 6.6 监控和运维
- **EagleEye**: 阿里巴巴鹰眼监控系统
- **ATEYE**: 2.4.1 (监控客户端)
- **SwitchCenter**: 2.1.4 (配置中心)
- **Jacoco**: 0.8.8 (代码覆盖率)
- **Checkstyle**: 3.1.2 (代码风格检查)