<h1 id="cCLYn">诸葛人群图灵平台打标技术方案</h1>
<h2 id="qpxv9">1. 需求概述</h2>
[https://aliyuque.antfin.com/suyan.msy/yxo30t/an90g4anoqp7xpxu?singleDoc#](https://aliyuque.antfin.com/suyan.msy/yxo30t/an90g4anoqp7xpxu?singleDoc#) 《人群转化小工具》

<h2 id="C6Jgp">2. 数据模型</h2>
<h3 id="KjQ0Y">2.1 核心数据表</h3>
<h4 id="omue7">转换任务表 (tag_convert_task)</h4>
| 字段 | 类型 | 说明 |
| --- | --- | --- |
| id | varchar(64) | 主键ID |
| source_platform | varchar(32) | 源平台：诸葛、图灵、微信等 |
| target_platform | varchar(32) | 目标平台:诸葛、图灵、微信等 |
| target_crowd_id | varchar(64) | 目标平台人群ID |
| target_crowd_name | varchar(128) | 目标平台人群名称 |
| source_crowd_id | varchar(64) | 源人群ID |
| source_crowd_name | varchar(128) | 源人群名称 |
| first_level_tag | varchar(32) | 一级标签 |
| second_level_tag | varchar(32) | 二级标签(1打标,0去标) |
| total_count | bigint(20) | 总用户数 |
| convert_count | bigint(20) | 转化人数 |
| status | tinyint(4) | 任务状态 |
| convert_time | datetime | 转化时间 |
| operator | varchar(64) | 操作人 |




<h4 id="gKAW5">用户打标记录表 (user_tag_record)</h4>
| 字段 | 类型 | 说明 |
| --- | --- | --- |
| task_id | varchar(64) | 关联任务ID |
| user_id | varchar(64) | 用户ID |
| platform | varchar(32) | 打标平台 |
| tag_operation | varchar(16) | 标签操作 |
| tag_id | varchar(128) | 标签ID |
| status | tinyint(4) | 打标状态 |


<h3 id="ov1Z3">2.2 业务规则</h3>
<h4 id="ZpQ0m">任务唯一性约束</h4>
+ **组合键**：`source_platform+target_platform+first_level_tag + source_crowd_id`
+ **作用**：确保相同一级标签、源人群和转换平台的任务不会重复创建
+ **示例**：诸葛 + 图灵 + 支端社群同步 + 诸葛人群12345 

<h3 id="cbNv6">2.3 状态定义</h3>
<h4 id="sUCq3">任务状态 (status)</h4>
+ **0 - 待处理**：任务已创建，等待开始处理
+ **1 - 处理中**：任务正在执行转换操作
+ **2 - 转换成功**：任务执行完成，用户转换成功
+ **3 - 转换失败**：任务执行失败，需要重试或人工处理

<h4 id="f7G7M">打标状态 (user_tag_record.status)</h4>
+ **1 - 打标成功**：用户标签操作成功
+ **2 - 打标失败**：用户标签操作失败

<h4 id="BioGM">转换平台类型 (convert_platform)</h4>
+ **zhuge->turing**：诸葛人群转换到图灵标签
+ **zhuge->alipay**：诸葛人群转换到支付宝标签  
+ **turing->alipay**：图灵标签转换到支付宝标签
+ **alipay->wechat**：支付宝标签转换到微信标签

<h4 id="vIcFn">标签操作类型 (tag_operation)</h4>
+ **add**：添加标签
+ **remove**：移除标签

<h2 id="L7okd">3. 技术架构</h2>
<h3 id="fbQm7">3.1 整体架构图</h3>


```plain
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面       │───▶│   Controller    │───▶│   Service层     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   诸葛人群查询   │◀───│   业务编排层     │───▶│   ID转换服务    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ TripCrowdCommon │    │ 记录管理服务     │    │ OneIdMapping    │
│ Service         │    │                 │    │ Service         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │   数据持久化     │    │   图灵打标服务   │
                    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                            ┌─────────────────┐
                                            │  Proxy图灵接口  │
                                            └─────────────────┘
```

<h3 id="Jskud">3.2 核心模块</h3>
<h4 id="CV1j5">3.2.1 任务管理模块</h4>
+ **功能**：任务创建、状态跟踪、进度管理
+ **职责**：
    - 接收转换任务请求
    - 管理任务生命周期
    - 提供任务查询接口

<h4 id="a90aY">3.2.2 平台适配模块</h4>
+ **功能**：适配不同标签平台的接口差异
+ **支持平台**：
    - 诸葛平台：人群数据查询
    - 图灵平台：标签打标接口

<h4 id="NJgn5">3.2.3 数据处理模块</h4>
+ **功能**：大数据量分批处理
+ **特性**：
    - 异步处理机制
    - 批量数据处理

<h4 id="FEmQI">3.2.4 故障恢复模块</h4>
+ **功能**：任务故障检测和自动恢复
+ **机制**：
    - 超时检测
    - 自动重试

<h2 id="k7gIR">4. 时序图</h2>
<h3 id="iVcxy">4.1 完整流程时序图</h3>
![](https://intranetproxy.alipay.com/skylark/lark/__mermaid_v3/da73c18bba0c6ca87c3a53d08a5f0cb1.svg)

<h3 id="dBcvx">4.2 故障恢复流程时序图</h3>
![](https://intranetproxy.alipay.com/skylark/lark/__mermaid_v3/d51adc7d3815f8f63d605116262240d8.svg)

<h3 id="a20Tj"></h3>




<h2 id="VeVWQ">6.工作量/排期</h2>
| **模块** | **一级模块** | **二级模块** | **工作量** | **负责人** | **进度** |
| :---: | :---: | --- | :---: | :---: | :---: |
| | 网关层 | 图灵打标接口 | 0.5d | 王瑞 |  |
| | 业务层<br/> | 人群打标 | 1.5d | |  |
| | | 标签转换记录管理 | 1d |  | |
| | | 故障恢复处理 | 1d |  | |
| 前端 | B端 |  | 2d<br/> | 金昊进 |  |
| 联调 | 联调 |  | 1d |  |  |
| 测试 | 测试 |  |  | 将昼 | <font style="color:#121416;"></font> |








