package com.alibaba.tripscrm.service.model.domain.response;

import lombok.Data;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * 通用返回结果
 *
 * <AUTHOR>
 * @date 2023/3/28
 */
@Data
public class BaseResult<T> implements Serializable {
    private static final long serialVersionUID = -5401579872414728775L;
    private Integer status = HttpServletResponse.SC_OK;
    private boolean success;
    private boolean warn = false;
    private String errorCode;
    private String errorMsg;
    private T data;

    public BaseResult(boolean success, String errorCode, String errorMsg, T data) {
        this.success = success;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.data = data;
    }

    public static <T> BaseResult<T> success() {
        return new BaseResult<>(true, null, null, null);
    }

    public static <T> BaseResult<T> success(T data) {
        return new BaseResult<>(true, null, null, data);
    }

    public static <T> BaseResult<T> success(T data, String msg) {
        return new BaseResult<>(true, null, msg, data);
    }

    public static <T> BaseResult<T> warn(String msg) {
        BaseResult result = new BaseResult<>(false, null, msg, null);
        result.setWarn(true);
        return result;
    }

    public static <T> BaseResult<T> fail(String msg) {
        return new BaseResult<>(false, null, msg, null);
    }

    public static <T> BaseResult<T> fail(String errorCode, String errorMsg) {
        return new BaseResult<>(false, errorCode, errorMsg, null);
    }
}
