package com.alibaba.tripscrm.service.service.second;


/**
 * <AUTHOR>
 * @date 2023-10-26 20:39:12
 */
public interface ShortLinkService {
    /**
     * 将链接转换成短链接
     *
     * @param originUrl 原始链接
     * @return 短链接
     */
    String convertToShortUrl(String originUrl);

    /**
     * 生成微信太阳码scene
     * @param targetUrl
     * @return
     */
    String getUnlimitQrCodeScene(String targetUrl);

    /**
     * 获取支付宝短链接
     * @param
     * @return
     */
    String getAlipayShortUrl(String url, String params, String pageType);

}
