package com.alibaba.tripscrm.service.middleware.schedulerx.alipay;

import cn.hutool.core.collection.CollectionUtil;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapReduceJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.domain.request.TripSCRMGroupTemplateSyncRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMSyncGroupService;
import com.alibaba.tripscrm.service.convert.AlipayGroupConverter;
import com.alibaba.tripscrm.service.enums.alipay.AlipayGroupChangeTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.domain.query.GroupTemplateQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupNewQuery;
import com.alibaba.tripscrm.service.model.dto.group.ChatSynchronizerDTO;
import com.alibaba.tripscrm.service.model.dto.group.GroupTemplateInfoDTO;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupDTO;
import com.alibaba.tripscrm.service.service.group.GroupNewService;
import com.alibaba.tripscrm.service.service.group.GroupTemplateInfoService;
import com.alibaba.tripscrm.service.synchronizer.GroupChatSynchronizer;
import com.alibaba.tripscrm.service.synchronizer.SyncResult;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.api.service.alipay.group.AlipayGroupService;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupDetailDTO;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupInstanceInfoDTO;
import com.alibaba.tripzoo.proxy.request.alipay.AlipayGroupInfoPageRequest;
import com.alibaba.tripzoo.proxy.request.alipay.GroupInstanceInfoPageRequest;
import com.alibaba.tripzoo.proxy.result.PageInfoDTO;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.constant.TairConstant.ALIPAY_GROUP_ID_CACHE;
import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/18 13:54
 * @Filename：AlipayGroupSyncProcessor
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlipayGroupSyncProcessor extends MapReduceJobProcessor {
    private final TripSCRMSyncGroupService syncGroupService;
    private final AlipayGroupService alipayGroupService;
    private final AlipayGroupConverter alipayGroupConverter;
    private final GroupChatSynchronizer groupChatSynchronizer;
    private final GroupNewService groupNewService;
    private final GroupTemplateInfoService groupTemplateInfoService;
    private final LdbTairManager ldbTairManager;

    private static final String SINGLE_GROUP_CHAT_SYNC_TASK = "SINGLE_GROUP_CHAT_SYNC_TASK";
    //删除群id分片id
    private static final Integer DELEDTE_SHARDING_ID = -1;


    @Override
    public ProcessResult process(JobContext context) throws Exception {

        try {
            SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);
            //主任务拆分
            if (isRootTask(context)) {
                splitSubProcess();
            }
            //子任务调度
            if (SINGLE_GROUP_CHAT_SYNC_TASK.equals(context.getTaskName())) {
                // 如果是子任务，直接从context中获取chatIdSet进行同步
                syncChatInfo(context);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("同步支付宝社群失败", e.getMessage(), e, LogListUtil.newArrayList(context.getTask()));
            return new ProcessResult(false);
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }

        return new ProcessResult(true);
    }

    private void splitSubProcess() {
        // 主任务逻辑：更新所有群组信息
        List<GroupDetailDTO> groupDetailDTOList = pageGroupDetailDTOAll();

        if (CollectionUtils.isEmpty(groupDetailDTOList)) {
            return;
        }
        List<TripSCRMGroupTemplateSyncRequest> deleteGroupIdList = getDeleteGroupIdList(groupDetailDTOList);

        Set<String> chatIdSet = new HashSet<>();
        Set<String> deleteChatIdSet = new HashSet<>();
        //拿到所有群id
        Map<Integer, Set<String>> chatIdMap = getAllAlipayChatId(groupDetailDTOList, deleteGroupIdList, chatIdSet, deleteChatIdSet);
        String date = DateUtils.getOtherSimpleDayDateString(new Date());
        //缓存所有群id
        ldbTairManager.put(ALIPAY_GROUP_ID_CACHE + date, JSONObject.toJSONString(chatIdMap), EXPIRE_TIME_DAY_UNIT);

        //1.同步新增/修改群组信息
        executeSyncGroupTemplateTask(groupDetailDTOList);
        //2.同步解散群组 查询db群组 看看是否有存在 不在list中的群组 如果不在就需要同步删除
        if (!CollectionUtils.isEmpty(deleteGroupIdList)) {
            executeDeleteSyncGroupTemplateTask(deleteGroupIdList);
        }

        // 生成分片
        List<Integer> shardingList = new ArrayList<>();
        for (Integer i = -1; i < SwitchConfig.ALIPAY_GROUP_SYNC_SHARD_MAX; i++) {
            shardingList.add(i);
        }
        map(shardingList, SINGLE_GROUP_CHAT_SYNC_TASK);
    }

    /**
     * 获取删除的群组ID列表
     **/
    private List<TripSCRMGroupTemplateSyncRequest> getDeleteGroupIdList(List<GroupDetailDTO> groupDetailDTOList) {

        List<String> deleteGroupIdList = new ArrayList<>();
        GroupTemplateQuery query = new GroupTemplateQuery();
        query.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);
        query.setPlatformType(PlatformTypeEnum.ALI_PAY);
        query.setDeleted(0);
        List<GroupTemplateInfoDTO> select = groupTemplateInfoService.select(query);
        if (CollectionUtil.isEmpty(select)) {
            return Collections.emptyList();
        }
        Set<String> groupIdSet = groupDetailDTOList.stream().map(GroupDetailDTO -> GroupDetailDTO.getGroupBaseInfoDTO().getGroupId()).collect(Collectors.toSet());
        List<GroupTemplateInfoDTO> needDelList = select.stream().filter(groupTemplateInfoDTO -> !groupIdSet.contains(groupTemplateInfoDTO.getGroupTemplateId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needDelList)) {
            return Collections.emptyList();
        }
        return needDelList.stream().map(alipayGroupConverter::convert2DeleteGroupTemplateBuildRequest).collect(Collectors.toList());
    }

    private List<GroupDetailDTO> pageGroupDetailDTOAll() {

        List<GroupDetailDTO> groupDetailDTOList = new ArrayList<>();

        AlipayGroupInfoPageRequest request = new AlipayGroupInfoPageRequest();
        request.setPageNum(1L);
        request.setPageSize(50L);
        ResultDO<PageInfoDTO<GroupDetailDTO>> pageResultDO;
        // 分页查询所有群组
        do {
            pageResultDO = alipayGroupService.pageQuery(request);
            if (!pageResultDO.getSuccess() || Objects.isNull(pageResultDO.getModel()) || pageResultDO.getModel().getList().isEmpty()) {
                PlatformLogUtil.logFail("查询支付宝群组信息失败", LogListUtil.newArrayList(request, pageResultDO));
                return groupDetailDTOList;
            }
            groupDetailDTOList.addAll(pageResultDO.getModel().getList());
            request.setPageNum(request.getPageNum() + 1);

        } while (Objects.nonNull(pageResultDO) && pageResultDO.getModel().getTotal() > groupDetailDTOList.size());
        return groupDetailDTOList;
    }

    //获取支付宝已经解散的群id
    private void getDeleteChatId(List<TripSCRMGroupTemplateSyncRequest> deleteGroupIdList, Set<String> deleteChatIdSet) {
        if (CollectionUtils.isEmpty(deleteGroupIdList)) {
            return;
        }

        for (TripSCRMGroupTemplateSyncRequest templateSyncRequest : deleteGroupIdList) {
            if (Objects.isNull(templateSyncRequest)) {
                PlatformLogUtil.logFail("群组信息为空", LogListUtil.newArrayList(templateSyncRequest));
                return;
            }
            // 查询db 看是否有存在 不在list中的群聊 如果不在就需要同步删除
            List<String> deleteChatIdList = getDeleteChatIdList(templateSyncRequest.getGroupTemplateId(), null);
            if (!CollectionUtils.isEmpty(deleteChatIdList)) {
                PlatformLogUtil.logInfo("删除群聊列表", LogListUtil.newArrayList(deleteChatIdList));
                deleteChatIdSet.addAll(deleteChatIdList);
            }
        }

    }

    //删除群组任务
    private void executeDeleteSyncGroupTemplateTask(List<TripSCRMGroupTemplateSyncRequest> deleteGroupIdList) {

        for (TripSCRMGroupTemplateSyncRequest templateSyncRequest : deleteGroupIdList) {
            if (Objects.isNull(templateSyncRequest)) {
                PlatformLogUtil.logFail("群组信息为空", LogListUtil.newArrayList(templateSyncRequest));
                return;
            }

            TripSCRMResult<Boolean> result = syncGroupService.syncGroupTemplateInfo(templateSyncRequest);
            if (!result.isSuccess() || !result.getData()) {
                PlatformLogUtil.logFail("同步群组信息失败", LogListUtil.newArrayList(templateSyncRequest, result));
                return;
            }
        }

    }

    //拿到支付宝的所有群id
    private Map<Integer, Set<String>> getAllAlipayChatId(List<GroupDetailDTO> groupDetailDTOList, List<TripSCRMGroupTemplateSyncRequest> deleteGroupIdList, Set<String> chatIdSet, Set<String> deleteChatIdSet) {
        if (CollectionUtils.isEmpty(groupDetailDTOList)) {
            return Collections.emptyMap();
        }

        for (GroupDetailDTO groupDetailDTO : groupDetailDTOList) {
            if (Objects.isNull(groupDetailDTO)) {
                PlatformLogUtil.logFail("群组信息为空", LogListUtil.newArrayList(groupDetailDTO));
                break;
            }

            // 分页查询所有群聊
            List<String> allChatIdList = getChatIdList(groupDetailDTO);
            if (!CollectionUtils.isEmpty(allChatIdList)) {
                chatIdSet.addAll(allChatIdList);
            }
            // 查询db 看看知否有存在 不在list中的群聊 如果不在就需要同步删除
            List<String> needDeleteChatIdList = getDeleteChatIdList(groupDetailDTO.getGroupBaseInfoDTO().getGroupId(), allChatIdList);
            if (!CollectionUtils.isEmpty(needDeleteChatIdList)) {
                PlatformLogUtil.logInfo("删除群聊列表", LogListUtil.newArrayList(needDeleteChatIdList));
                deleteChatIdSet.addAll(needDeleteChatIdList);
            }

        }
        getDeleteChatId(deleteGroupIdList, deleteChatIdSet);
        //将所有群id放到相应的分片上
        Map<Integer, Set<String>> chatIdSetMap = chatIdSet.stream().collect(Collectors.groupingBy(
                chatId -> Math.abs(chatId.hashCode()) % SwitchConfig.ALIPAY_GROUP_SYNC_SHARD_MAX, Collectors.toSet()
        ));
        //需要删除的群单独放一个分片子任务中
        if (!CollectionUtils.isEmpty(deleteChatIdSet)) {
            chatIdSetMap.put(DELEDTE_SHARDING_ID, deleteChatIdSet);
        }
        return chatIdSetMap;
    }

    //群组同步任务
    private void executeSyncGroupTemplateTask(List<GroupDetailDTO> groupDetailDTOList) {

        for (GroupDetailDTO groupDetailDTO : groupDetailDTOList) {
            if (Objects.isNull(groupDetailDTO)) {
                PlatformLogUtil.logFail("群组信息为空", LogListUtil.newArrayList(groupDetailDTO));
                return;
            }

            TripSCRMGroupTemplateSyncRequest request = alipayGroupConverter.convert2GroupTemplateBuildRequest(groupDetailDTO);
            TripSCRMResult<Boolean> result = syncGroupService.syncGroupTemplateInfo(request);
            if (!result.isSuccess() || !result.getData()) {
                PlatformLogUtil.logFail("同步群组信息失败", LogListUtil.newArrayList(groupDetailDTO, result));
                return;
            }
        }

    }

    //群组下需要删除群聊
    private List<String> getDeleteChatIdList(String groupTemplateId, List<String> chatIdList) {
        WechatGroupNewQuery query = new WechatGroupNewQuery();
        query.setGroupTemplateId(groupTemplateId);
        query.setIsDeleted(Byte.valueOf("0"));
        query.setPlatformType(PlatformTypeEnum.ALI_PAY);
        query.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);
        List<WechatGroupDTO> select = groupNewService.select(query);
        if (CollectionUtils.isEmpty(select)) {
            return null;
        }
        List<String> dbChatIdList = select.stream().map(WechatGroupDTO::getChatId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dbChatIdList)) {
            return null;
        }
        if (CollectionUtils.isEmpty(chatIdList)) {
            return dbChatIdList;
        }
        Set<String> chatIdSet = new HashSet<>(chatIdList);
        return dbChatIdList.stream().filter(chatId -> !chatIdSet.contains(chatId)).collect(Collectors.toList());
    }

    private List<String> getChatIdList(GroupDetailDTO groupDetailDTO) {
        List<String> chatIdList = new ArrayList<>();

        GroupInstanceInfoPageRequest instanceInfoPageRequest = new GroupInstanceInfoPageRequest();
        instanceInfoPageRequest.setGroupId(groupDetailDTO.getGroupBaseInfoDTO().getGroupId());
        instanceInfoPageRequest.setPageNum(1L);
        instanceInfoPageRequest.setPageSize(50L);
        ResultDO<PageInfoDTO<GroupInstanceInfoDTO>> pageInfoDTOResultDO;
        do {
            pageInfoDTOResultDO = alipayGroupService.pageGroupInstanceInfo(instanceInfoPageRequest);
            if (!pageInfoDTOResultDO.getSuccess() || Objects.isNull(pageInfoDTOResultDO.getModel()) || pageInfoDTOResultDO.getModel().getList().isEmpty()) {
                PlatformLogUtil.logFail("查询支付宝群组信息失败", LogListUtil.newArrayList(instanceInfoPageRequest, pageInfoDTOResultDO));
                return chatIdList;
            }
            List<GroupInstanceInfoDTO> groupInstanceInfoDTOS = pageInfoDTOResultDO.getModel().getList();
            List<String> chatId = groupInstanceInfoDTOS.stream().map(GroupInstanceInfoDTO::getGroupInstanceId).collect(Collectors.toList());

            chatIdList.addAll(chatId);
            instanceInfoPageRequest.setPageNum(instanceInfoPageRequest.getPageNum() + 1);

        } while (Objects.nonNull(pageInfoDTOResultDO) && pageInfoDTOResultDO.getModel().getTotal() > chatIdList.size());

        return chatIdList;
    }

    /**
     * 群聊同步任务
     *
     * @param context
     */
    private void syncChatInfo(JobContext context) {
        Integer sharding = (Integer) context.getTask();
        //筛选出属于这个分片的群聊
        JSONObject jsonObject = JSONObject.parseObject((String) ldbTairManager.get(ALIPAY_GROUP_ID_CACHE + DateUtils.getOtherSimpleDayDateString(new Date())));
        if (Objects.isNull(jsonObject)) {
            return;
        }

        Map<Integer, Set<String>> jsonObjectMap = JSONObject.parseObject(jsonObject.toString(), new TypeReference<Map<Integer, Set<String>>>() {
        });

        Set<String> chatIdSet = jsonObjectMap.get(sharding);
        if (CollectionUtils.isEmpty(chatIdSet)) {
            return;
        }

        for (String chatId : chatIdSet) {
            BaseSynchronizerContext<ChatSynchronizerDTO> syncContext = new BaseSynchronizerContext<>();
            ChatSynchronizerDTO data = new ChatSynchronizerDTO();
            data.setPlatformType(PlatformTypeEnum.ALI_PAY);
            data.setChatId(chatId);
            data.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);
            if (Objects.equals(DELEDTE_SHARDING_ID, sharding)) {
                data.setChangeType(AlipayGroupChangeTypeEnum.DEL_GROUP);
            }
            syncContext.setData(data);
            SyncResult<ChatSynchronizerDTO> sync = groupChatSynchronizer.sync(syncContext);
        }

    }

    @Override
    public ProcessResult reduce(JobContext jobContext) throws Exception {
        return new ProcessResult(true);
    }

}