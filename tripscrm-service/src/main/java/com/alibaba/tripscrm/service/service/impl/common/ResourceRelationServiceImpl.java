package com.alibaba.tripscrm.service.service.impl.common;

import com.alibaba.tripscrm.dal.mapper.tddl.ResourceRelationMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationParam;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationWayEnum;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.dto.ResourceRelationDTO;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/5 14:05
 **/
@Service
public class ResourceRelationServiceImpl implements ResourceRelationService {

    @Resource
    private ResourceRelationMapper resourceRelationMapper;

    @AteyeInvoker(description = "新增资源关系", paraDesc = "relationType&sourceType&sourceId&targetType&targetId&targetName&way")
    public boolean add(Byte relationType, Byte sourceType, String sourceId, Byte targetType, String targetId, String targetName, Byte way) {
        ResourceRelationDO resourceRelationDO = new ResourceRelationDO();
        resourceRelationDO.setGmtCreate(new Date());
        resourceRelationDO.setGmtModified(new Date());
        resourceRelationDO.setRelationType(relationType);
        resourceRelationDO.setWay(Optional.ofNullable(ResourceRelationWayEnum.codeOf(way)).map(ResourceRelationWayEnum::getCode).orElse(null));
        resourceRelationDO.setSourceType(sourceType);
        resourceRelationDO.setSourceId(sourceId);
        resourceRelationDO.setTargetType(targetType);
        resourceRelationDO.setTargetId(targetId);
        resourceRelationDO.setTargetName(targetName);
        return add(resourceRelationDO);
    }

    @Override
    public boolean add(ResourceRelationDO resourceRelationDO) {
        if (resourceRelationDO == null) {
            return false;
        }
        return resourceRelationMapper.insert(resourceRelationDO) > 0;
    }

    @Override
    public boolean batchAdd(List<ResourceRelationDO> resourceRelationList) {
        if (CollectionUtils.isEmpty(resourceRelationList)) {
            return true;
        }
        return resourceRelationMapper.batchInsert(resourceRelationList) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return false;
        }
        return resourceRelationMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public boolean deleteByParam(ResourceRelationQuery query) {
        if (query == null) {
            return false;
        }
        // 删除条件
        ResourceRelationParam param = new ResourceRelationParam();
        ResourceRelationParam.Criteria criteria = param.createCriteria();
        if (query.getType() != null) {
            criteria.andRelationTypeEqualTo(query.getType());
        }
        if (query.getSourceType() != null) {
            criteria.andSourceTypeEqualTo(query.getSourceType());
        }
        if (query.getSourceId() != null) {
            criteria.andSourceIdEqualTo(query.getSourceId());
        }
        if (query.getTargetType() != null) {
            criteria.andTargetTypeEqualTo(query.getTargetType());
        }
        if (query.getTargetId() != null) {
            criteria.andTargetIdEqualTo(query.getTargetId());
        }
        return resourceRelationMapper.deleteByParam(param) > 0;
    }

    @Override
    public boolean batchDelete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }
        ResourceRelationParam param = new ResourceRelationParam();
        ResourceRelationParam.Criteria criteria = param.createCriteria();
        criteria.andIdIn(ids);
        return resourceRelationMapper.deleteByParam(param) > 0;
    }

    @AteyeInvoker(description = "批量删除关系", paraDesc = "ids")
    public boolean batchDelete(String ids) {
        if (StringUtils.isBlank(ids)) {
            return false;
        }
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        return batchDelete(idList);
    }

    @Override
    public ResourceRelationDO queryById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return null;
        }
        return resourceRelationMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ResourceRelationDO> query(ResourceRelationQuery query) {
        ResourceRelationParam param = new ResourceRelationParam();
        // 分页条件
        param.setPage(query.isPage());
        param.setPageSize(query.getPageSize());
        param.setPageStart(query.getPageNum());
        // 查询条件
        ResourceRelationParam.Criteria criteria = param.createCriteria();
        if (NumberUtils.validLong(query.getMinId())) {
            criteria.andIdGreaterThanOrEqualTo(query.getMinId());
            param.appendOrderByClause(ResourceRelationParam.OrderCondition.ID, ResourceRelationParam.SortType.ASC);
        }
        if (query.getType() != null) {
            criteria.andRelationTypeEqualTo(query.getType());
        }
        if (query.getSourceType() != null) {
            criteria.andSourceTypeEqualTo(query.getSourceType());
        }
        if (!StringUtils.isBlank(query.getSourceId())) {
            criteria.andSourceIdEqualTo(query.getSourceId());
        }
        if (!CollectionUtils.isEmpty(query.getSourceIdList())) {
            criteria.andSourceIdIn(query.getSourceIdList());
        }
        if (query.getTargetType() != null) {
            criteria.andTargetTypeEqualTo(query.getTargetType());
        }
        if (StringUtils.isNotBlank(query.getTargetName())) {
            criteria.andTargetNameLike(String.format("%%%s%%", query.getTargetName()));
        }
        if (StringUtils.isNotBlank(query.getTargetId())) {
            criteria.andTargetIdEqualTo(query.getTargetId());
        }
        if (Objects.nonNull(query.getStartCreateTime())) {
            criteria.andGmtCreateGreaterThanOrEqualTo(query.getStartCreateTime());
        }
        return resourceRelationMapper.selectByParam(param);
    }

    @Override
    public Long count(ResourceRelationQuery query) {
        ResourceRelationParam param = new ResourceRelationParam();
        ResourceRelationParam.Criteria criteria = param.createCriteria();
        if (query.getType() != null) {
            criteria.andRelationTypeEqualTo(query.getType());
        }
        if (query.getSourceType() != null) {
            criteria.andSourceTypeEqualTo(query.getSourceType());
        }
        if (!StringUtils.isBlank(query.getSourceId())) {
            criteria.andSourceIdEqualTo(query.getSourceId());
        }
        if (query.getTargetType() != null) {
            criteria.andTargetTypeEqualTo(query.getTargetType());
        }
        return resourceRelationMapper.countByParam(param);
    }

    @Override
    public boolean updateResourceRelation(ResourceRelationDTO resourceRelationDTO) {
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setPage(false);
        query.setType(resourceRelationDTO.getRelationType());
        query.setSourceType(resourceRelationDTO.getResourceType());
        query.setSourceId(resourceRelationDTO.getResourceId());
        List<Long> oldIdList = Optional.of(query(query)).orElse(new ArrayList<>()).stream().map(ResourceRelationDO::getId).collect(Collectors.toList());
        boolean deleteResult = batchDelete(oldIdList);
        if (!deleteResult) {
            return false;
        }
        return batchAdd(resourceRelationDTO.getResourceRelationDOList());
    }
}
