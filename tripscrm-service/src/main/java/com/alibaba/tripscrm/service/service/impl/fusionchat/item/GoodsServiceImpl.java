package com.alibaba.tripscrm.service.service.impl.fusionchat.item;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.domain.fusionchat.dto.ItemSearchDTO;
import com.alibaba.tripscrm.service.model.domain.fusionchat.vo.GoodsVO;
import com.alibaba.tripscrm.service.model.domain.query.ItemQuery;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripscrm.service.service.fusionchat.item.ItemService;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alitrip.travel.travelitems.domain.AppInfoDO;
import com.alitrip.travel.travelitems.model.search.SearchQueryCondition;
import com.alitrip.travel.travelitems.model.search.TravelItemSearchDO;
import com.alitrip.travel.travelitems.model.search.TravelItemSearchResult;
import com.alitrip.travel.travelitems.service.TravelItemSearchService;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/29 09:57
 **/
@AllArgsConstructor
@Service
public class GoodsServiceImpl implements ItemService<GoodsVO> {

    /**
     * 商品图片访问前缀
     */
    private static final String GOODS_IMAGE_PRE = "https://img.alicdn.com/imgextra/";
    private static final String IMAGE_PRE = "http";

    private final TravelItemSearchService travelItemSearchService;
    private final MaterialLinkService materialLinkService;

    @Override
    public ItemTypeEnum getType() {
        return ItemTypeEnum.GOODS;
    }

    @Override
    public ItemSearchDTO<GoodsVO> queryItem(ItemQuery query) {
        if (StringUtils.isBlank(query.getSearchKey())) {
            return search(query);
        }
        if (StringUtils.isNumeric(query.getSearchKey())) {
            query.setItemId(query.getSearchKey());
        } else {
            query.setName(query.getSearchKey());
        }
        return search(query);
    }

    @Override
    public String queryLink(ItemTypeEnum itemTypeEnum, String itemId, LinkTypeEnum targetTypeEnum, Boolean convertShortLink, MaterialTrackRelationDTO trackRelationDTO) {
        if (itemTypeEnum == null || StringUtils.isBlank(itemTypeEnum.getLink())) {
            PlatformLogUtil.logFail("获取商品访问链接参数缺失", LogListUtil.newArrayList(itemTypeEnum, itemId));
            return null;
        }
        fillTrackRelation(trackRelationDTO);

        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(String.format(itemTypeEnum.getLink(), itemId));
        request.setTargetLinkType(targetTypeEnum);
        request.setConvertShortLink(convertShortLink);
        request.setTitle("");
        request.setUseInWechat(true);
        request.setScrmTrackId(materialLinkService.getBuriedPointId(trackRelationDTO));
        request.setMaterialId(Optional.ofNullable(trackRelationDTO).map(MaterialTrackRelationDTO::getMaterialId).orElse(null));
        String tempUrl = materialLinkService.convertLink(request);
        return MaterialUtils.replaceMiniProgramPathWithDotHtml(tempUrl);
    }

    /**
     * 补充点击关系信息
     * @param materialTrackRelationDTO 点击关系
     */
    private void fillTrackRelation(MaterialTrackRelationDTO materialTrackRelationDTO) {
        if (materialTrackRelationDTO == null) {
            return;
        }
        materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.GOODS_PROMOTION);
        if (ChatTypeEnum.GROUP.equals(materialTrackRelationDTO.getChatTypeEnum())) {
            materialTrackRelationDTO.setSendChatId(materialTrackRelationDTO.getTargetId());
        } else if (ChatTypeEnum.SINGLE_FOR_CUSTOMER.equals(materialTrackRelationDTO.getChatTypeEnum())) {
            materialTrackRelationDTO.setSendUnionId(materialTrackRelationDTO.getTargetId());
        }
    }

    /**
     * 商品搜索外调
     * @param query 查询条件
     * @return 商品列表
     */
    private ItemSearchDTO<GoodsVO> search(ItemQuery query) {
        ItemSearchDTO<GoodsVO> goodsDTO = new ItemSearchDTO<>();
        // 请求体构建
        AppInfoDO appInfoDO = new AppInfoDO("tripscrm");
        SearchQueryCondition searchQueryCondition = new SearchQueryCondition();
        if (StringUtils.isNotBlank(query.getSellerId()) && StringUtils.isNumeric(query.getSellerId())) {
            searchQueryCondition.setSellerIds(Lists.newArrayList(Long.valueOf(query.getSellerId())));
        }
        if (StringUtils.isNotBlank(query.getItemId()) && StringUtils.isNumeric(query.getItemId())) {
            searchQueryCondition.setItemIds(Lists.newArrayList(Long.valueOf(query.getItemId())));
        }
        if (StringUtils.isNotBlank(query.getName())) {
            searchQueryCondition.setTitle(query.getName());
        }
        if (query.getPageNum() != null && query.getPageSize() != null) {
            searchQueryCondition.setStartPage(query.getPageNum() - 1);
            searchQueryCondition.setPageSize(query.getPageSize());
        }
        // 0和1在商品侧代表正常在售
        searchQueryCondition.setItemStatus(Lists.newArrayList(0,1));
        // 外调
        TravelItemSearchResult searchResult = travelItemSearchService.search(searchQueryCondition, appInfoDO);
        if (searchResult == null || !searchResult.isSuccess()) {
            PlatformLogUtil.logFail("商品查询报错", LogListUtil.newArrayList(query, searchResult));
            return goodsDTO;
        }
        // 实体转化
        goodsDTO.setItemList(Optional.ofNullable(searchResult.getItemList()).orElse(new ArrayList<>())
                .stream().map(this::convert).collect(Collectors.toList()));
        goodsDTO.setCount(searchResult.getTotal());
        return goodsDTO;
    }

    /**
     * 结构转化
     * @param travelItemSearchDO 商品DO
     * @return 物料VO
     */
    private GoodsVO convert(TravelItemSearchDO travelItemSearchDO) {
        GoodsVO goodsVO = new GoodsVO();
        goodsVO.setItemId(String.valueOf(travelItemSearchDO.getItemId()));
        goodsVO.setItemType(ItemTypeEnum.GOODS.getItemType());
        goodsVO.setTitle(travelItemSearchDO.getTitle());
        goodsVO.setImage(dealGoodsImages(travelItemSearchDO.getImages()).get(0));
        goodsVO.setPrice(travelItemSearchDO.getPrice());
        goodsVO.setInStock(travelItemSearchDO.getQuantity());
        return goodsVO;
    }

    /**
     * 处理商品图片
     * @param images 商品返回图片
     * @return 可用图片列表
     */
    private List<String> dealGoodsImages(String images) {
        if (StringUtils.isBlank(images)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(images.split(";")).map(this::getCanUseUrl).collect(Collectors.toList());
    }

    /**
     * 处理商品侧返回的图片
     * @param imageSourceUrl 商品侧返回的原始链接
     * @return 可访问图片
     */
    private String getCanUseUrl(String imageSourceUrl) {
        if (StringUtils.isBlank(imageSourceUrl) || StringUtils.startsWith(imageSourceUrl, IMAGE_PRE)) {
            return imageSourceUrl;
        }
        return GOODS_IMAGE_PRE + imageSourceUrl;
    }

}
