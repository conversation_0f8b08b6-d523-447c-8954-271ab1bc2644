package com.alibaba.tripscrm.service.service.strategy.variable.inner;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.enums.material.InnerServiceEnum;
import com.alibaba.tripscrm.service.model.dto.material.service.InnerServiceRuleDTO;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/24 11:00
 */
@Component
public abstract class AbstractVariableDataInnerService implements VariableDataInnerService {
    private static final Map<InnerServiceEnum, AbstractVariableDataInnerService> INNER_SERVICE_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void abstractInit() {
        INNER_SERVICE_MAP.put(getInnerServiceEnum(), this);
    }

    public static String getValue(InnerServiceRuleDTO innerServiceRuleDTO, Map<String, Object> carryMap) {
        if (Objects.isNull(innerServiceRuleDTO) || Objects.isNull(innerServiceRuleDTO.getInnerServiceEnum())) {
            return null;
        }

        if (!INNER_SERVICE_MAP.containsKey(innerServiceRuleDTO.getInnerServiceEnum())) {
            return null;
        }

        String result = INNER_SERVICE_MAP.get(innerServiceRuleDTO.getInnerServiceEnum()).doGetValue(carryMap);
        PlatformLogUtil.logInfo("内置变量替换结果", LogListUtil.newArrayList(innerServiceRuleDTO, result));
        return result;
    }

    @Override
    public abstract String doGetValue(Map<String, Object> paramMap);

    @Override
    public abstract InnerServiceEnum getInnerServiceEnum();
}
