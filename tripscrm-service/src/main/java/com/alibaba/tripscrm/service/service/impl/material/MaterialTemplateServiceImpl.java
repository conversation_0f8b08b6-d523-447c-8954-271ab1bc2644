package com.alibaba.tripscrm.service.service.impl.material;

import com.alibaba.tripscrm.dal.mapper.tddl.MaterialTemplateInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.MaterialTemplateInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialTemplateInfoQuery;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.material.MaterialTemplatePreEnum;
import com.alibaba.tripscrm.service.service.material.MaterialTemplateService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MaterialTemplateServiceImpl implements MaterialTemplateService {

    private final MaterialTemplateInfoMapper materialTemplateInfoMapper;

    @Override
    public Long upsert(MaterialTemplateInfoDO templateInfoDO) {
        try {
            if (Objects.isNull(templateInfoDO)) {
                PlatformLogUtil.logFail("param check fail", LogListUtil.newArrayList(templateInfoDO));
                return null;
            }
            if (NumberUtils.validLong(templateInfoDO.getId())) {
                // id有效，更新素材
                return (long) materialTemplateInfoMapper.updateByPrimaryKey(templateInfoDO);
            } else {
                // id无效，保存素材
                templateInfoDO.setDeleted((byte) WxConstants.not_deleted);
                return (long) materialTemplateInfoMapper.insert(templateInfoDO);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(templateInfoDO));
            return null;
        }
    }

    @Override
    public List<MaterialTemplateInfoDO> list(MaterialTemplateInfoQuery query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("param check fail", LogListUtil.newArrayList(query));
                return null;
            }
            List<MaterialTemplateInfoDO> list = materialTemplateInfoMapper.selectByParam(query);
            // 结果处理
            if (Objects.isNull(list)) {
                return null;
            }
            // 预发替换内容
            if (EnvUtils.isPre()) {
                list = list.stream().map(materialTemplateInfoDO -> {
                    MaterialTemplatePreEnum materialTemplatePreEnum = MaterialTemplatePreEnum.templateIdOf(materialTemplateInfoDO.getId());
                    if (materialTemplatePreEnum != null && StringUtils.hasText(materialTemplatePreEnum.getTemplateContent())) {
                        materialTemplateInfoDO.setContent(materialTemplatePreEnum.getTemplateContent());
                    }
                    return materialTemplateInfoDO;
                }).collect(Collectors.toList());
            }
            return list;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public Integer delete(Long id) {
        try {
            if (!NumberUtils.validLong(id)) {
                PlatformLogUtil.logFail("param check fail", LogListUtil.newArrayList(id));
                return null;
            }
            return materialTemplateInfoMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(id));
            return null;
        }
    }
}
