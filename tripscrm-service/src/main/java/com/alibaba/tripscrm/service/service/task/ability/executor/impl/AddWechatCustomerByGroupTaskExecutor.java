package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.CustomerRelationMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskAttributionConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.AddCustomerByGroupRequest;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2024-01-11 13:55:34
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AddWechatCustomerByGroupTaskExecutor extends AbstractTaskExecutor {
    private final WechatUserService wechatUserService;
    private final EnterpriseWechatManager enterpriseWechatManager;
    private final CustomerRelationMapper customerRelationMapper;
    private final ActivityContextService activityContextService;
    private final LdbTairManager ldbTairManager;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String chatId = getFinalTargetId(context, taskDataBody);
        Map<String, Object> extInfo = context.getExtInfo();
        if (!extInfo.containsKey("externalUserId")) {
            PlatformLogUtil.logFail("externalUserId is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!extInfo.containsKey("eventType")) {
            PlatformLogUtil.logFail("eventType is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        String eventType = (String) extInfo.get("eventType");
        if (!needExecute(context, todoTaskVO, eventType)) {
            todoTaskVO.setSuccess(true);
            return;
        }

        String externalUserId = (String) extInfo.get("externalUserId");
        // 查询活动上下文
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        if (Objects.isNull(activityContext)) {
            PlatformLogUtil.logFail("not found activityContext", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            todoTaskVO.setSuccess(true);
            todoTaskVO.setAttribution(TaskAttributionConstant.NOT_FOUND_ACTIVITY_CONTEXT);
            return;
        }

        activityContext.getExtraJson().putIfAbsent("chatIdList", new ArrayList<>());
        List<String> chatIdList = activityContext.getExtraJson().getObject("chatIdList", new TypeReference<List<String>>() {
        });

        if (!chatIdList.contains(chatId)) {
            PlatformLogUtil.logFail("chatId not in activityContext", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            todoTaskVO.setSuccess(true);
            return;
        }

        String userId = getSendUserId(context, todoTaskVO);
        if (StringUtils.isBlank(userId)) {
            todoTaskVO.setSuccess(true);
            return;
        }

        if (!doFatigue(context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            return;
        }

        // 生成客户上下文
        ActivityTaskInfoBO targetActivityContext = getCustomerActivityContext(context, todoTaskVO);
        Long contextId = Objects.nonNull(targetActivityContext) ? targetActivityContext.getContextId() : activityContextService.generateContextId();
        ActivityTaskInfoBO customerActivityTaskInfoBO = new ActivityTaskInfoBO();
        customerActivityTaskInfoBO.setContextId(contextId);
        customerActivityTaskInfoBO.setActivityId(activityContext.getActivityId());
        customerActivityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_EXTERNAL_USERID);
        customerActivityTaskInfoBO.setTargetId(externalUserId);
        customerActivityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraInfoJson = new JSONObject();
        extraInfoJson.put("contextId", activityContext.getContextId());
        extraInfoJson.put("taskId", context.getTaskId());
        extraInfoJson.put("addWechatCustomerByGroup", eventType);
        extraInfoJson.put("dataTime", System.currentTimeMillis());
        customerActivityTaskInfoBO.setExtraJson(extraInfoJson);
        activityContextService.upsert(customerActivityTaskInfoBO);
        PlatformLogUtil.logFail("upsertActivityContext", LogListUtil.newArrayList(customerActivityTaskInfoBO));

        // 发送加好友申请
        AddCustomerByGroupRequest request = new AddCustomerByGroupRequest();
        request.setChatId(chatId);
        request.setExternalUserId(externalUserId);
        request.setUserId(getSendUserId(context, todoTaskVO));
        request.setMessage(getMessage(context, todoTaskVO));

        // 记录发送群内添加好友请求数据
        ldbTairManager.incr(TairConstant.ADD_WECHAT_CUSTOMER_BY_GROUP_REQUEST_COUNT_PREFIX + context.getTaskId(), 1, 0, 86400 * 365 * 10);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> enterpriseWechatManager.asyncAddCustomerByGroup(request));
        if (!result.isSuccess()) {
            undoFatigue(context, todoTaskVO);
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED, result.getMsg());
        }

        // 记录请求proxy数据
        JSONObject data = new JSONObject();
        data.put("chatId", chatId);
        data.put("wechatUserId", getSendUserId(context, todoTaskVO));
        data.put("extraInfo", context.getExtInfo());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
    }

    protected ActivityTaskInfoBO getCustomerActivityContext(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        Map<String, Object> extInfo = context.getExtInfo();
        String externalUserId = (String) extInfo.get("externalUserId");
        Long activityId = context.getTaskInfoDOSnapshot().getActivityId();
        // 根据 targetId + targetType + activityId 查询上下文
        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(activityId);
        query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_EXTERNAL_USERID);
        query.setTargetId(externalUserId);
        List<ActivityTaskInfoBO> customerActivityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
        if (CollectionUtils.isEmpty(customerActivityTaskInfoBOS)) {
            PlatformLogUtil.logFail("queryByActivityAndTarget result is null", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        return ActivityContextService.getNewestBo(customerActivityTaskInfoBOS);
    }

    private boolean doFatigue(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        Long dayVersion = DateUtils.getDayVersion(new Date());
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        Map<String, Object> extInfo = context.getExtInfo();
        if (!extInfo.containsKey("externalUserId")) {
            PlatformLogUtil.logFail("externalUserId is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        String externalUserId = (String) extInfo.get("externalUserId");
        String sendUserId = getSendUserId(context, todoTaskVO);
        String key = TairConstant.TASK_EXECUTE_DUPLICATE_CHECK_PREFIX + dayVersion + "_" + externalUserId + "_" + sendUserId;
        if (ldbTairManager.incr(key, 1, 0, 86400) > 1) {
            PlatformLogUtil.logFail("duplicate execute", LogListUtil.newArrayList(key, taskDataBody));
            return false;
        }

        PlatformLogUtil.logFail("pass", LogListUtil.newArrayList(key, taskDataBody));
        return true;
    }

    private void undoFatigue(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        Long dayVersion = DateUtils.getDayVersion(new Date());
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        Map<String, Object> extInfo = context.getExtInfo();
        if (!extInfo.containsKey("externalUserId")) {
            PlatformLogUtil.logFail("externalUserId is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        String externalUserId = (String) extInfo.get("externalUserId");
        String sendUserId = getSendUserId(context, todoTaskVO);
        String key = TairConstant.TASK_EXECUTE_DUPLICATE_CHECK_PREFIX + dayVersion + "_" + externalUserId + "_" + sendUserId;
        ldbTairManager.delete(key);
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(taskDataBody));
    }

    private boolean needExecute(TaskExecuteContext context, TodoTaskVO todoTaskVO, String eventType) {
        if (!Objects.equals(eventType, "atUserInGroup")) {
            return false;
        }

        JSONObject extraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        if (!extraInfoJson.containsKey("addCustomerIfAt")) {
            PlatformLogUtil.logFail("addCustomerIfAt is empty", LogListUtil.newArrayList(todoTaskVO.getData().get(0)));
            return false;
        }

        return extraInfoJson.getBoolean("addCustomerIfAt");
    }

    /**
     * 获取验证语
     */
    private String getMessage(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("message")) {
            return (String) taskDataBody.getContext().get("message");
        }

        JSONObject extraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        if (!extraInfoJson.containsKey("addCustomerSendMessageList")) {
            PlatformLogUtil.logFail("messageList is empty", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        List<String> messageList = extraInfoJson.getObject("addCustomerSendMessageList", new TypeReference<List<String>>() {
        });
        return messageList.get(ThreadLocalRandom.current().nextInt(messageList.size()));
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            return (String) taskDataBody.getContext().get("sendUserId");
        }

        Map<String, Object> extInfo = context.getExtInfo();
        if (!extInfo.containsKey("userId")) {
            PlatformLogUtil.logFail("userId is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!extInfo.containsKey("externalUserId")) {
            PlatformLogUtil.logFail("externalUserId is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        String userId = (String) extInfo.get("userId");
        String externalUserId = (String) extInfo.get("externalUserId");

        JSONObject extraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        if (!extraInfoJson.containsKey("userIdList")) {
            PlatformLogUtil.logFail("userIdList is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        List<String> userIdList = extraInfoJson.getObject("userIdList", new TypeReference<List<String>>() {
        });
        if (!userIdList.contains(userId)) {
            PlatformLogUtil.logFail("@userId is not in taskConfig", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(userId));

        // 企微成员不存在，或者不在线
        if (CollectionUtils.isEmpty(wechatUserList) || !Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
            PlatformLogUtil.logFail("robot is not online", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE);
        }

        CustomerRelationDO customerRelationDO = customerRelationMapper.getByUserIdAndExternalUserIdAndCorpId(userId, externalUserId, SpaceInfoThreadLocalUtils.getCorpId());
        if (Objects.nonNull(customerRelationDO) && !Objects.equals(CustomerRelationStatusEnum.NON_FRIEND, CustomerRelationStatusEnum.of(customerRelationDO.getStatus().intValue()))) {
            PlatformLogUtil.logFail("has friend or single relation", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        taskDataBody.getContext().put("sendUserId", userId);
        return userId;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (taskDataBody.getContext().containsKey("chatId")) {
            return (String) taskDataBody.getContext().get("chatId");
        }

        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("empty targetId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!Lists.newArrayList(ActivityTargetTypeEnum.WX_CHAT_ID).contains(activityTargetType)) {
            PlatformLogUtil.logFail("only accept chatId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        String chatId = taskDataBody.getTargetId();
        taskDataBody.getContext().put("chatId", chatId);
        return chatId;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_CHAT_ID;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.ADD_WECHAT_CUSTOMER_BY_GROUP;
    }
}
