package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.vo.task.TaskAsyncRequestDataVO;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.ForwardMessageTargetTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/6/6 14:33
 **/
@Component
@AllArgsConstructor
public class ForwardMessageCallbackProcessor implements ProxyCallbackProcessor {
    private final LdbTairManager ldbTairManager;
    private final MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.FORWARD_MESSAGE_TASK;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (scrmCallbackMsg == null || StringUtils.isBlank(scrmCallbackMsg.getRequestId())) {
            PlatformLogUtil.logFail("转发消息回调内容为空或者请求id为空");
            return false;
        }
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("转发消息回调显示失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        // 1.判断是否需要往事件源发送消息
        Map<String, String> extraInfo = getExtraInfo(scrmCallbackMsg,content);
        if (Objects.isNull(extraInfo)){
            return false;
        }
        // 2.往事件源发送消息
        dealEventSource(content, scrmCallbackMsg.getPlatformCorpId(),extraInfo);
        return true;
    }

    private Map<String, String>  getExtraInfo(ScrmCallbackMsg scrmCallbackMsg, JSONObject content) {
        if (!content.containsKey(CallbackConstant.USER_ID) || !content.containsKey("msgId")) {
            return null;
        }
        Object o = ldbTairManager.get(TairConstant.LDB_PROXY_REQUEST_DATA_PREFIX + scrmCallbackMsg.getRequestId());
        if (Objects.isNull(o)) {
            return null;
        }
        TaskAsyncRequestDataVO taskAsyncRequestDataVO = JSONObject.parseObject(o.toString(), new TypeReference<TaskAsyncRequestDataVO>() {
        });
        Map<String, String> extraInfo = taskAsyncRequestDataVO.getExtraInfo();
        if(Objects.isNull(extraInfo) || !extraInfo.containsKey("forwardMessageType") || !extraInfo.containsKey("targetId")){
            return null;
        }
        String forwardMessageType = extraInfo.get("forwardMessageType");
        if(ForwardMessageTargetTypeEnum.INTERNAL_FRIEND.getCode().equals(Integer.valueOf(forwardMessageType))){
            return extraInfo;
        }

        return null;
    }

    private void dealEventSource(JSONObject content, String platformCorpId, Map<String, String> extraInfo) {
        JSONObject message = new JSONObject();
        if (!content.containsKey(CallbackConstant.USER_ID) || !content.containsKey(CallbackConstant.MSG_ID)) {
            return;
        }
        message.put(TaskConstant.SEND_USER_ID, content.getString(CallbackConstant.USER_ID));
        message.put(CallbackConstant.MSG_ID, content.getString(CallbackConstant.MSG_ID));
        message.put(TaskConstant.CORP_ID, platformCorpId);
        message.put(TaskConstant.TARGET_ID, extraInfo.get(TaskConstant.TARGET_ID));
        message.put(TaskConstant.CONTEXT_ID,extraInfo.get(TaskConstant.CONTEXT_ID));
        message.put(TaskConstant.TARGET_TYPE, ActivityTargetTypeEnum.WX_CHAT_ID.getCode());
        String key = content.getString(CallbackConstant.USER_ID)+"_"+ content.getString(CallbackConstant.MSG_ID)+"_"+extraInfo.get(TaskConstant.TARGET_ID);
        metaqProducer.send(MQEnum.FORWARD_MESSAGE_TO_GROUP,key,"", message.toJSONString(), MetaQDeleyLevel.LEVEL_3.getLevel());
    }



}
