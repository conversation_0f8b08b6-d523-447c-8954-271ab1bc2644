package com.alibaba.tripscrm.service.service.impl.second;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.trip.tripdata.common.domain.page.Page;
import com.alibaba.trip.tripdivision.client.TrdiDivisionMappingService;
import com.alibaba.trip.tripdivision.client.TrdiDivisionQueryService;
import com.alibaba.trip.tripdivision.client.TrdiDivisionReadService;
import com.alibaba.trip.tripdivision.client.domain.TrdiDivisionSearchParam;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.tripscrm.service.service.second.DivisionService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.mtop.common.Result;
import com.taobao.trip.train.client.TrainCityStationServiceClient;
import com.taobao.trip.train.dataobject.TrStationCityDo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-07-17 16:15:54
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class DivisionServiceImpl implements DivisionService {
    private final TrdiDivisionReadService trdiDivisionReadService;
    private final TrdiDivisionQueryService trdiDivisionQueryService;
    private final TrdiDivisionMappingService trdiDivisionMappingService;
    private final TrainCityStationServiceClient trainCityStationServiceClient;

    @Override
    @AteyeInvoker(description = "根据城市名称获取行政区划Id", paraDesc = "cityName")
    public Long getDivisionIdByCityName(String cityName) {
        Result<TrdiDivisionDO> result = trdiDivisionQueryService.queryDomesticDivisionByCityName(cityName);
        if (Objects.isNull(result) || !result.isSuccess() || result.getModel() == null) {
            PlatformLogUtil.logFail("根据cityName获取divisionId，结果为空", LogListUtil.newArrayList(cityName, result));
            return null;
        }

        PlatformLogUtil.logInfo("根据cityName获取divisionId", LogListUtil.newArrayList(cityName, result));
        return result.getModel().getId();
    }

    @Override
    public Page<TrdiDivisionDO> searchDivision(String keyword) {
        TrdiDivisionSearchParam trdiDivisionSearchParam = new TrdiDivisionSearchParam();
        trdiDivisionSearchParam.setKeywords(keyword);
        return trdiDivisionReadService.searchDivision(trdiDivisionSearchParam);
    }

    @Override
    public List<TrdiDivisionDO> getDivision(List<Long> ids) {
        return trdiDivisionReadService.getDivisionByIds(ids);
    }

    @Override
    public String getCityCode3ByDivisionId(Long divisionId) {
        TrdiDivisionDO divisionDO = trdiDivisionReadService.getDivision(divisionId);
        if (Objects.isNull(divisionDO) || !StringUtils.hasText(divisionDO.getCityCode3())) {
            PlatformLogUtil.logFail("根据divisionId获取cityCode3，结果为空", LogListUtil.newArrayList(divisionId, divisionDO));
            return null;
        }

        PlatformLogUtil.logInfo("根据divisionId获取cityCode3", LogListUtil.newArrayList(divisionId, divisionDO));
        return divisionDO.getCityCode3();
    }

    @Override
    public Long getDivisionIdByCityCode3(String cityCode3) {
        Result<TrdiDivisionDO> result = trdiDivisionMappingService.queryByCityCode3(cityCode3);
        if (Objects.isNull(result) || !result.isSuccess() || result.getModel() == null) {
            PlatformLogUtil.logFail("根据cityCode3获取divisionId，结果为空", LogListUtil.newArrayList(cityCode3, result));
            return null;
        }

        PlatformLogUtil.logInfo("根据cityCode3获取divisionId", LogListUtil.newArrayList(cityCode3, result));
        return result.getModel().getId();
    }

    @Override
    public TrStationCityDo getTrStationCityDoByName(String stationName) {
        try {
            if (!StringUtils.hasText(stationName)) {
                PlatformLogUtil.logFail("根据火车站名查询火车站城市信息，火车站名为空", LogListUtil.newArrayList(stationName));
                return null;
            }

            TrStationCityDo trStationCityDo = trainCityStationServiceClient.getStationCityInfoLocalCache(stationName);
            if (Objects.isNull(trStationCityDo) || !NumberUtils.validLong(trStationCityDo.getCityCode())) {
                PlatformLogUtil.logFail("根据火车站名查询火车站城市信息，结果为空", LogListUtil.newArrayList(stationName, trStationCityDo));
                return null;
            }

            PlatformLogUtil.logInfo("根据火车站名查询火车站城市信息", LogListUtil.newArrayList(stationName, trStationCityDo));
            return trStationCityDo;
        } catch (Exception e) {
            PlatformLogUtil.logException("根据火车站名查询火车站城市信息出错", e.getMessage(), e, LogListUtil.newArrayList(stationName));
            return null;
        }
    }
}