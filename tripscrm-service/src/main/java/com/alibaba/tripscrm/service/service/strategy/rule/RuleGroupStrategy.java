package com.alibaba.tripscrm.service.service.strategy.rule;

import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.RuleExecuteContext;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:56
 */
public interface RuleGroupStrategy {
    /**
     * 规则组执行
     */
    TripSCRMResult<Void> run(Long id, RuleExecuteContext context);

    /**
     * 规则执行
     */
    TripSCRMResult<Void> run(RuleExecuteContext context);

    /**
     * 规则类型
     *
     * @return
     */
    RuleTypeEnum getRuleType();
}
