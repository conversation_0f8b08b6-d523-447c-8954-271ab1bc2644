package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RuleQueryRequest extends BasePageRequest implements Serializable {
    /**
     * 规则ID
     */
    @Min(value = 1L, message = "id非法")
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 创建者
     */
    private String creatorId;

    /**
     * 管理员
     */
    private String memberIds;

    /**
     * 规则组Id
     */
    private Long groupId;

    /**
     * 创建时间（开始时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createStartTime;

    /**
     * 创建时间（结束时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createEndTime;

    /**
     * 类型列表
     */
    private List<Byte> typeList;

}
