package com.alibaba.tripscrm.service.service.impl.tag;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.trip.tripdata.common.domain.page.Page;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.TagInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.TagInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TagInfoParam;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.convert.TagConverter;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.model.domain.query.TagGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.TagInfoQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.second.DivisionService;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripzoo.proxy.api.service.TagService;
import com.alibaba.tripzoo.proxy.request.AddCustomerTagRequest;
import com.alibaba.tripzoo.proxy.request.DelCustomerTagRequest;
import com.alibaba.tripzoo.proxy.request.UpdateCustomerTagRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.AddCustomerTagResponse;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/10 17:25
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TagInfoServiceImpl implements TagInfoService {
    private final TagInfoMapper tagInfoMapper;
    private final TagGroupService tagGroupService;
    private final DivisionService divisionService;
    private final TagConverter tagConverter;
    private final TagService tagService;

    @Override
    public TagInfoDTO selectByTagId(String tagId) {
        if (!StringUtils.hasText(tagId)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!tagId.contains("_")) {
            // 普通标签
            return Optional.ofNullable(tagInfoMapper.selectByPrimaryKey(Long.parseLong(tagId))).map(tagConverter::convert2DTO).orElse(null);
        }

        // 行政区划标签
        String[] splits = tagId.split("_");
        if (splits.length != 2) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return selectDivisionTagById(selectByTagId(splits[0]), splits[1]);
    }

    @Override
    public List<TagInfoDTO> selectByTagIdList(List<String> tagIdList) {
        if (CollectionUtils.isEmpty(tagIdList)) {
            return Lists.newArrayList();
        }

        return tagIdList.stream().map(this::selectByTagId).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<TagInfoDTO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        TagInfoParam param = new TagInfoParam();
        param.or().andIdIn(idList);
        return Optional.of(tagInfoMapper.selectByParam(param)).orElse(new ArrayList<>()).stream().map(tagConverter::convert2DTO).collect(Collectors.toList());
    }

    @Override
    public List<TagInfoDTO> selectByCondition(TagInfoQuery query) {
        TagInfoParam param = buildParam(query);
        return Optional.ofNullable(tagInfoMapper.selectByParam(param)).orElse(new ArrayList<>()).stream().map(tagConverter::convert2DTO).collect(Collectors.toList());
    }

    @Override
    public Integer insertSelective(TagInfoDTO record, Boolean isSync) {
        PlatformLogUtil.logInfo("写入单个标签数据", LogListUtil.newArrayList(record));
        if (Objects.isNull(record) || !NumberUtils.validLong(record.getSpaceId()) || !NumberUtils.validLong(record.getGroupId()) || !StringUtils.hasText(record.getName())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (NumberUtils.isEqual(record.getTagType(), TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode()) && Objects.nonNull(isSync) && isSync) {
            String source = addCustomerTag(record);
            if (!StringUtils.hasLength(source)) {
                PlatformLogUtil.logFail("调用企微创建标签接口失败", LogListUtil.newArrayList(record));
                return 0;
            }
            record.setSource(source);
        }
        TagInfoDO tagInfoDO = tagConverter.convert2DO(record);
        int result = tagInfoMapper.insertSelective(tagInfoDO);
        record.setId(tagInfoDO.getId());
        return result;
    }

    @Override
    public Integer updateSelective(TagInfoDTO record, TagInfoQuery condition, Boolean isSync) {
        if (Objects.isNull(condition) || CollectionUtils.isEmpty(condition.getIdList())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        List<TagInfoDTO> tagInfoDTOS = selectByTagIdList(condition.getIdList().stream().map(Object::toString).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(tagInfoDTOS) || !NumberUtils.validInteger(tagInfoDTOS.get(0).getTagType()) || Objects.isNull(TagTypeEnum.of(tagInfoDTOS.get(0).getTagType()))) {
            PlatformLogUtil.logFail("标签数据更新失败，标签不存在或标签类型参数缺失", LogListUtil.newArrayList(record, condition, condition.getIdList()));
            return 0;
        }
        TagInfoParam param = buildParam(condition);
        TagInfoDO tagInfoDO = tagConverter.convert2DO(record);
        tagInfoDO.setId(null);
        tagInfoDO.setGmtModified(new Date());
        // 如果标签类型为企微标签，需要调用企微更新标签接口
        if (Objects.equals(TagTypeEnum.of(tagInfoDTOS.get(0).getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG) && Objects.nonNull(isSync) && isSync) {
            if (!StringUtils.hasLength(tagInfoDTOS.get(0).getSource())) {
                PlatformLogUtil.logFail("调用企微更新标签接口失败，source参数缺失", LogListUtil.newArrayList(record));
                return 0;
            }
            record.setSource(tagInfoDTOS.get(0).getSource());
            record.setTagType(tagInfoDTOS.get(0).getTagType());
            if (!updateCustomerTag(record)) {
                PlatformLogUtil.logFail("调用企微更新标签接口失败", LogListUtil.newArrayList(record));
                return 0;
            }


        }
        return tagInfoMapper.updateByParamSelective(tagInfoDO, param);
    }

    @Override
    public Integer deleteByIdList(List<Long> ids, Boolean isSync) {
        if (CollectionUtils.isEmpty(ids)) {
            PlatformLogUtil.logFail("标签数据删除失败，id列表为空", LogListUtil.newArrayList(ids));
            return 0;
        }
        List<TagInfoDTO> tagInfoDTOList = selectByIdList(ids);
        if (CollectionUtils.isEmpty(tagInfoDTOList)) {
            PlatformLogUtil.logFail("标签数据删除失败，该标签列表不存在", LogListUtil.newArrayList(ids));
            return 0;
        }
        TagInfoParam param = new TagInfoParam();
        if (isSync) {
            List<Long> successDeleteIds = new ArrayList<>();
            for (TagInfoDTO tagInfoDTO : tagInfoDTOList) {
                // 如果标签类型为企微标签，需要调用企微删除标签接口
                if (Objects.equals(TagTypeEnum.of(tagInfoDTO.getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG)) {
                    if (!deleteCustomerTag(tagInfoDTO)) {
                        PlatformLogUtil.logFail("调用删除企微端标签接口失败", LogListUtil.newArrayList(tagInfoDTO));
                        continue;
                    }
                    successDeleteIds.add(tagInfoDTO.getId());
                    continue;
                }
                successDeleteIds.add(tagInfoDTO.getId());
            }
            if (CollectionUtils.isEmpty(successDeleteIds)) {
                PlatformLogUtil.logInfo("调用删除企微端标签接口成功的标签id列表为空", LogListUtil.newArrayList(successDeleteIds));
                return 0;
            }
            PlatformLogUtil.logInfo("调用删除企微端标签接口成功的标签id列表", LogListUtil.newArrayList(successDeleteIds));
            param.or().andIdIn(successDeleteIds);
        } else {
            param.or().andIdIn(ids);
        }
        return tagInfoMapper.deleteByParam(param);
    }

    @Override
    public PageInfo<TagInfoDTO> pageQuery(TagInfoQuery query) {
        try {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
            TagInfoParam param = buildParam(query);
            param.appendOrderByClause(TagInfoParam.OrderCondition.GMTMODIFIED, TagInfoParam.SortType.DESC);
            List<TagInfoDO> list = tagInfoMapper.selectByParam(param);
            // 结果处理
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }

            PageInfo<TagInfoDO> pageInfo = new PageInfo<>(list);
            return PageUtils.getPageInfo(pageInfo, tagConverter::convert2DTO);
        } catch (Exception e) {
            PlatformLogUtil.logException("标签数据分页查询出错", e.getMessage(), e, LogListUtil.newArrayList(query));
            throw e;
        }
    }

    @Override
    public PageInfo<TagInfoDTO> searchDivisionCodeTag(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TagGroupQuery tagGroupQuery = new TagGroupQuery();
        tagGroupQuery.setTagType(Lists.newArrayList(TagTypeEnum.DIVISION_CODE.getCode()));
        List<TagGroupDTO> tagGroupList = tagGroupService.selectByCondition(tagGroupQuery);
        if (CollectionUtils.isEmpty(tagGroupList) || tagGroupList.size() != 1) {
            PlatformLogUtil.logFail("行政区划标签组查询出错", LogListUtil.newArrayList(keyword, tagGroupList));
            throw new TripscrmException(TripSCRMErrorCode.DB_FAILED);
        }

        TagInfoQuery tagInfoQuery = new TagInfoQuery();
        tagInfoQuery.setGroupId(tagGroupList.get(0).getId());
        List<TagInfoDTO> tagInfoList = selectByCondition(tagInfoQuery);
        if (CollectionUtils.isEmpty(tagInfoList) || tagInfoList.size() != 1) {
            PlatformLogUtil.logFail("行政区划标签查询出错", LogListUtil.newArrayList(keyword, tagInfoList));
            throw new TripscrmException(TripSCRMErrorCode.DB_FAILED);
        }

        List<TagInfoDTO> list = searchDivisionTagList(tagInfoList.get(0), keyword);
        return PageUtils.getPageInfo(list, a -> a);
    }

    private List<TagInfoDTO> searchDivisionTagList(TagInfoDTO tagInfoDTO, String keyword) {
        List<TrdiDivisionDO> list = new ArrayList<>();
        // 先根据关键词搜索
        Page<TrdiDivisionDO> page = divisionService.searchDivision(keyword);
        if (Objects.nonNull(page) && !org.apache.commons.collections4.CollectionUtils.isEmpty(page.getData())) {
            list.addAll(page.getData());
        }

        // 再根据 code 搜索
        if (org.apache.commons.lang3.StringUtils.isNumeric(keyword)) {
            List<TrdiDivisionDO> division = divisionService.getDivision(Lists.newArrayList(Long.parseLong(keyword)));
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(division)) {
                list.addAll(division);
            }
        }

        return list.stream().map(trdiDivisionDO -> {
            TagInfoDTO tagInfo = new TagInfoDTO();
            BeanUtils.copyProperties(tagInfoDTO, tagInfo);
            tagInfo.setName(trdiDivisionDO.getName() + "【" + trdiDivisionDO.getId() + "】");
            tagInfo.setSubCode(String.valueOf(trdiDivisionDO.getId()));
            return tagInfo;
        }).collect(Collectors.toList());
    }

    private TagInfoDTO selectDivisionTagById(TagInfoDTO tagInfoDTO, String subCode) {
        List<TrdiDivisionDO> divisionList = divisionService.getDivision(Lists.newArrayList(Long.parseLong(subCode)));
        if (CollectionUtils.isEmpty(divisionList)) {
            return null;
        }

        TagInfoDTO tagInfo = new TagInfoDTO();
        BeanUtils.copyProperties(tagInfoDTO, tagInfo);
        tagInfo.setName(divisionList.get(0).getName() + "【" + divisionList.get(0).getId() + "】");
        tagInfo.setSubCode(String.valueOf(divisionList.get(0).getId()));
        return tagInfo;
    }

    @Override
    public Integer batchInsertSelective(List<TagInfoDTO> records, Boolean isSync) {
        if (CollectionUtils.isEmpty(records)) {
            PlatformLogUtil.logFail("写入标签数据失败，标签信息为空", LogListUtil.newArrayList(records));
            return 0;
        }
        Integer effectLines = 0;
        for (TagInfoDTO tagInfoDTO : records) {
            if (insertSelective(tagInfoDTO, isSync) < 1) {
                PlatformLogUtil.logFail("写入标签数据失败", LogListUtil.newArrayList(tagInfoDTO));
            } else {
                effectLines++;
            }
        }
        return effectLines;
    }

    @Override
    @Cacheable(key = "'id:' + #id", value = "tagInfoCacheManager", unless = "#result == null")
    public TagInfoDTO queryByIdWithCache(String id) {
        if (!StringUtils.hasText(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return selectByTagId(id);
    }

    @Override
    @Cacheable(key = "'groupId:' + #groupId + ',source:' + #source", value = "tagInfoCacheManager", unless = "#result == null")
    public TagInfoDTO queryByGroupIdAndSourceWithCache(Long groupId, String source) {
        if (!NumberUtils.validLong(groupId) || !StringUtils.hasText(source)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TagInfoQuery tagInfoQuery = new TagInfoQuery();
        tagInfoQuery.setGroupId(groupId);
        tagInfoQuery.setSourceList(Lists.newArrayList(source));
        return selectByCondition(tagInfoQuery).stream()
                .filter(tagInfoDTO -> Objects.equals(tagInfoDTO.getSource(), source))
                .findFirst()
                .orElse(null);
    }

    private TagInfoParam buildParam(TagInfoQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TagInfoParam param = new TagInfoParam();
        TagInfoParam.Criteria criteria = param.or();

        if (!CollectionUtils.isEmpty(query.getIdList())) {
            criteria.andIdIn(query.getIdList());
        }

        if (NumberUtils.validLong(query.getGroupId())) {
            criteria.andGroupIdEqualTo(query.getGroupId());
        }

        if (!CollectionUtils.isEmpty(query.getTagType())) {
            criteria.andTagTypeIn(query.getTagType().stream().map(Integer::byteValue).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(query.getSourceList())) {
            criteria.andSourceIn(query.getSourceList());
        }

        if (!CollectionUtils.isEmpty(query.getSpaceIdList())) {
            criteria.andSpaceIdIn(query.getSpaceIdList());
        }

        if (StringUtils.hasText(query.getNameLike())) {
            criteria.andNameLike('%' + query.getNameLike() + '%');
        }

        if (StringUtils.hasText(query.getName())) {
            criteria.andNameEqualTo(query.getName());
        }

        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        return param;
    }

    /**
     * 调用企微创建标签接口
     *
     * @param tagInfoDTO 标签信息
     * @return 创建的标签source
     */
    private String addCustomerTag(TagInfoDTO tagInfoDTO) {
        try {
            PlatformLogUtil.logInfo("标签类型为企微标签，调用企微创建标签接口", LogListUtil.newArrayList(tagInfoDTO));
            if (Objects.isNull(tagInfoDTO) || !StringUtils.hasLength(tagInfoDTO.getName()) || !NumberUtils.validLong(tagInfoDTO.getGroupId()) || !Objects.equals(TagTypeEnum.of(tagInfoDTO.getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG)) {
                PlatformLogUtil.logInfo("调用企微创建标签接口失败，参数非法", LogListUtil.newArrayList(tagInfoDTO));
                return null;
            }
            AddCustomerTagRequest request = new AddCustomerTagRequest();
            TagGroupDTO tagGroupDTO = tagGroupService.selectById(tagInfoDTO.getGroupId());
            if (Objects.isNull(tagGroupDTO)) {
                PlatformLogUtil.logFail("标签组为空", LogListUtil.newArrayList(tagGroupDTO));
                return null;
            }
            if (StringUtils.hasLength(tagGroupDTO.getSource())) {
                request.setGroupId(tagGroupDTO.getSource());
            }
            request.setGroupName(tagGroupDTO.getName());
            List<AddCustomerTagRequest.Tag> tagList = new ArrayList<>();
            AddCustomerTagRequest.Tag tag = new AddCustomerTagRequest.Tag();
            tag.setName(tagInfoDTO.getName());
            tagList.add(tag);
            request.setTagList(tagList);
            request.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            ResultDO<AddCustomerTagResponse> result = tagService.addCustomerTag(request);
            if (Objects.isNull(result) || !result.getSuccess()) {
                PlatformLogUtil.logFail("添加客户标签失败", LogListUtil.newArrayList(request, result));
                return null;
            }
            if (Objects.isNull(result.getModel()) || !StringUtils.hasLength(result.getModel().getGroupId()) || CollectionUtils.isEmpty(result.getModel().getTagList())) {
                PlatformLogUtil.logFail("添加客户标签失败, 返回参数为空", LogListUtil.newArrayList(request, result));
                return null;
            }

            if (!StringUtils.hasLength(tagGroupDTO.getSource())) {
                PlatformLogUtil.logInfo("更新标签组source", LogListUtil.newArrayList(tagGroupDTO, result.getModel()));
                TagGroupQuery condition = new TagGroupQuery();
                condition.setId(tagGroupDTO.getId());
                TagGroupDTO param = new TagGroupDTO();
                param.setSource(result.getModel().getGroupId());
                Integer effectLines = tagGroupService.updateSelective(param, condition, false);
                if (effectLines != 1) {
                    PlatformLogUtil.logFail("更新标签组source失败", LogListUtil.newArrayList(tagGroupDTO, result.getModel()));
                    return null;
                }
            }

            Optional<String> tagId = result.getModel().getTagList().stream()
                    .filter(x -> StringUtils.pathEquals(x.getName(), tagInfoDTO.getName()))
                    .findFirst()
                    .map(AddCustomerTagResponse.Tag::getId);
            if (!tagId.isPresent()) {
                PlatformLogUtil.logFail("调用企微创建标签接口创建失败，不存在同名tag", LogListUtil.newArrayList(tagInfoDTO, request, result));
                return null;
            }
            PlatformLogUtil.logInfo("调用企微创建标签接口创建成功", LogListUtil.newArrayList(request, result, tagId.get()));
            return tagId.get();
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.WECHAT_ENTERPRISE_TAG_MANAGEMENT_INTERFACE_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(tagInfoDTO));
            return null;
        }
    }

    /**
     * 调用企微更新标签接口
     *
     * @param tagInfoDTO 标签信息
     * @return 是否更新成功
     */
    private Boolean updateCustomerTag(TagInfoDTO tagInfoDTO) {
        try {
            PlatformLogUtil.logInfo("标签类型为企微标签，调用企微更新标签接口", LogListUtil.newArrayList(tagInfoDTO));
            if (Objects.isNull(tagInfoDTO) || !StringUtils.hasLength(tagInfoDTO.getName()) || !StringUtils.hasLength(tagInfoDTO.getSource()) || !Objects.equals(TagTypeEnum.of(tagInfoDTO.getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG)) {
                PlatformLogUtil.logFail("调用企微更新标签接口出错，参数非法", LogListUtil.newArrayList(tagInfoDTO));
                return false;
            }
            if (NumberUtils.isEqual(tagInfoDTO.getDeleted(), IsDeleteEnum.YES.getCode())) {
                return deleteCustomerTag(tagInfoDTO);
            }
            UpdateCustomerTagRequest request = new UpdateCustomerTagRequest();
            request.setId(tagInfoDTO.getSource());
            request.setName(tagInfoDTO.getName());
            request.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            ResultDO<Boolean> result = tagService.updateCustomerTag(request);
            if (Objects.isNull(result) || !result.getSuccess()) {
                PlatformLogUtil.logFail("更新客户标签出错", LogListUtil.newArrayList(request, result));
                return false;
            }
            PlatformLogUtil.logInfo("调用企微更新标签接口成功", LogListUtil.newArrayList(request, result));
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.WECHAT_ENTERPRISE_TAG_MANAGEMENT_INTERFACE_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(tagInfoDTO));
            return false;
        }
        return true;
    }

    /**
     * 调用企微删除标签接口
     *
     * @param tagInfoDTO 标签信息
     * @return 是否删除成功
     */
    private Boolean deleteCustomerTag(TagInfoDTO tagInfoDTO) {
        try {
            PlatformLogUtil.logInfo("标签类型为企微标签，调用企微删除标签接口", LogListUtil.newArrayList(tagInfoDTO));
            if (Objects.isNull(tagInfoDTO) || !StringUtils.hasLength(tagInfoDTO.getSource()) || !Objects.equals(TagTypeEnum.of(tagInfoDTO.getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG)) {
                PlatformLogUtil.logFail("调用企微删除标签接口出错，参数非法", LogListUtil.newArrayList(tagInfoDTO));
                return false;
            }
            DelCustomerTagRequest request = new DelCustomerTagRequest();
            request.setTagIdList(Lists.newArrayList(tagInfoDTO.getSource()));
            request.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            ResultDO<Boolean> result = tagService.delCustomerTag(request);
            if (Objects.isNull(result) || !result.getSuccess()) {
                PlatformLogUtil.logFail("删除客户标签出错", LogListUtil.newArrayList(request, result));
                return false;
            }
            PlatformLogUtil.logInfo("调用企微删除标签接口成功", LogListUtil.newArrayList(request, result));
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.WECHAT_ENTERPRISE_TAG_MANAGEMENT_INTERFACE_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(tagInfoDTO));
            return false;
        }
        return true;
    }
}
