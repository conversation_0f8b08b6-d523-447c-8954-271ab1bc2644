package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.log.BatchOperateLogInfoService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/6/6 11:16
 **/
@Service
@AllArgsConstructor
public class RenameGroupCallbackProcessor implements ProxyCallbackProcessor {

    private final LdbTairManager ldbTairManager;
    private final BatchOperateLogInfoService batchOperateLogInfoService;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.GROUP_RENAME_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (scrmCallbackMsg == null || StringUtils.isBlank(scrmCallbackMsg.getRequestId())) {
            PlatformLogUtil.logFail("修改群名称回调内容为空或者请求id为空");
            return false;
        }
        boolean dealResult = true;
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("修改群名称回调显示失败", LogListUtil.newArrayList(scrmCallbackMsg));
            dealResult = false;
        }
        // 批量群名操作结果处理
        dealBatchOperate(scrmCallbackMsg.getRequestId(), dealResult);
        return true;
    }

    /**
     * 处理批量操作群名称
     * @param requestId 请求id
     * @param dealResult 处理结果
     */
    private void dealBatchOperate(String requestId, Boolean dealResult) {
        Object o = ldbTairManager.get(TairConstant.BATCH_GROUP_OPERATE_MAPPING + requestId);
        if (Objects.isNull(o)) {
            return;
        }
        batchOperateLogInfoService.updateLogInfoByRequestId((Long) o, requestId, dealResult);
    }

}
