package com.alibaba.tripscrm.service.manager.opensearch;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupCustomerCountQuery;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.aliyun.opensearch.sdk.generated.search.*;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/24 21:59
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupCustomerRelationOpenSearchManager extends BaseOpenSearchManager {

    public PageInfo<String> pageQuery(WechatCustomerGroupQuery query) {
        try {
            if (Objects.isNull(query) || !StringUtils.hasText(query.getExternalUserId())) {
                return PageInfo.emptyPageInfo();
            }

            SearchParams searchParams = buildPageSearchParams(query, null);
            List<String> queryList = new ArrayList<>();
            if (StringUtils.hasText(query.getOwnerUserId())) {
                queryList.add(String.format("owner_user:'%s'", query.getOwnerUserId()));
            }
            if (StringUtils.hasText(query.getExternalUserId())) {
                queryList.add(String.format("user_id:'%s'", query.getExternalUserId()));
            }
            searchParams.setQuery(String.join(" AND ", queryList));

            // 过滤条件
            List<String> filterList = new ArrayList<>();
            filterList.add(String.format("status='%d'", GroupRelationStatusEnum.MEMBER.getCode()));
            // 添加租户过滤参数
            String corpId = SpaceInfoThreadLocalUtils.getCorpId();
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            if(NumberUtils.biggerThanZero(query.getPlatformType())){
                filterList.add(String.format("platform_type=%d", query.getPlatformType()));
            }
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);

            PageInfo<String> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), x -> x.getString("chat_id"));
            PlatformLogUtil.logInfo("Opensearch查询客户的群聊成功", searchParams, pageInfo);
            return pageInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的群聊出错", e.getMessage(), e, LogListUtil.newArrayList(query));
            return PageInfo.emptyPageInfo();
        }
    }

    /**
     * 查询企微客户群成员数量
     *
     * @param query 查询条件
     * @return key -> 群成员状态（1群内，2退群），value -> 数量
     */
    public Long getGroupCustomerCount(WechatGroupCustomerCountQuery query) {
        try {
            if (CollectionUtils.isEmpty(query.getChatIdList())) {
                return 0L;
            }

            SearchParams searchParams = buildSearchParams();
            Set<Aggregate> aggregates = new HashSet<>();
            Aggregate aggregate = new Aggregate();
            aggregate.setGroupKey("chat_id").setAggFun("count()");
            aggregates.add(aggregate);
            searchParams.setAggregates(aggregates);

            List<String> queryList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(query.getChatIdList())) {
                queryList.add("(" + query.getChatIdList().stream().map(chatId -> String.format("chat_id:'%s'", chatId)).collect(Collectors.joining(" OR ")) + ")");
            }
            if (StringUtils.hasText(query.getState())) {
                queryList.add(String.format("state:'%s'", query.getState()));
            }
            searchParams.setQuery(String.join(" AND ", queryList));

            // 过滤条件
            List<String> filterList = new ArrayList<>();
            if (Optional.ofNullable(query.getHasFollowUser()).orElse(false)) {
                filterList.add("fieldlen(follow_user)>0");
            }
            if(NumberUtils.biggerThanZero(query.getPlatformType())){
                filterList.add(String.format("platform_type=%d", query.getPlatformType()));
            }
            if (Optional.ofNullable(query.getNotInGroup()).orElse(false)) {
                filterList.add(String.format("status='%d'", GroupRelationStatusEnum.NOT_MEMBER.getCode()));
            }
            // 添加租户过滤参数
            String corpId = SpaceInfoThreadLocalUtils.getCorpId();
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            Map<String, Map<String, Long>> facet = getResultFacetCount(getSearcherClient().execute(searchParams));
            if (!facet.containsKey("chat_id")) {
                return 0L;
            }

            Map<String, Long> chatId2Count = facet.get("chat_id");
            PlatformLogUtil.logFail("Opensearch查询企微客户群成员数量成功", LogListUtil.newArrayList(searchParams, facet));
            return chatId2Count.values().stream().mapToLong(Long::longValue).sum();
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询企微客户群成员数量异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }

        return 0L;
    }

    @Override
    protected String getAppName() {
        return "scrm_group_customer_full";
    }

    @Override
    protected List<String> getFetchFields() {
        return Lists.newArrayList("corp_id", "user_id", "chat_id", "name", "owner_user","group_template_id", "platform_type");
    }
}
