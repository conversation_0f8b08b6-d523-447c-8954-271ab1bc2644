package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResponseTypeEnum;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.task.AutoResponseMsgSourceEnum;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.domain.request.SendMsgToCustomerRequest;
import com.alibaba.tripscrm.service.model.domain.request.SendMsgToGroupRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.task.AutoResponseTaskExtInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动回复任务执行器
 *
 * <AUTHOR>
 * @Date 2024/4/1 11:50
 **/
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AutoResponseTaskExecutor extends AbstractTaskExecutor {
    private final WechatUserService wechatUserService;
    private final EnterpriseWechatManager enterpriseWechatManager;
    private final WechatCustomerService wechatCustomerService;
    private final ResourceRelationService resourceRelationService;
    private final MaterialService materialService;
    private final TaskService taskService;
    private final TagRelationService tagRelationService;
    private final IsvRouteService isvRouteService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> dataBodyList = todoTaskVO.getData();
        if (CollectionUtils.isEmpty(dataBodyList)) {
            PlatformLogUtil.logFail("自动回复数据为空", LogListUtil.newArrayList(todoTaskVO.getTaskId(), todoTaskVO));
            return;
        }
        // 参数校验
        if (!paramVerify(dataBodyList.get(0), context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            return;
        }
        // 是否跳过
        if (skipProcess(dataBodyList.get(0), context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            todoTaskVO.setFailReason("不符合执行条件");
            return;
        }
        // 埋点日志
        PlatformLogUtil.logFail("自动回复匹配成功，开始执行发送数据", LogListUtil.newArrayList(todoTaskVO));
        // 业务处理
        businessProcess(context, todoTaskVO);
    }


    /**
     * 业务处理
     *
     * @param todoTaskVO 任务数据
     */
    private void businessProcess(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 素材获取
        AutoResponseTaskExtInfoDTO autoResponseMatchRule = getAutoResponseMatchRule(context, todoTaskVO);
        List<MaterailInfoDO> finalMaterialInfoList = getFinalMaterialInfo(context, todoTaskVO, autoResponseMatchRule);
        if (CollectionUtils.isEmpty(finalMaterialInfoList)) {
            PlatformLogUtil.logFail("自动回复的素材列表为空", LogListUtil.newArrayList(todoTaskVO));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MATERIAL_IN_TASK);
        }
        // 消息发送
        for (MaterailInfoDO materailInfoDO : finalMaterialInfoList) {
            asyncSendMessage(context, todoTaskVO, materailInfoDO);
        }
    }

    /**
     * 获取最终的素材信息
     *
     * @param todoTaskVO                 需要执行的任务信息
     * @param autoResponseTaskExtInfoDTO 自动回复规则
     * @return 素材信息
     */
    private List<MaterailInfoDO> getFinalMaterialInfo(TaskExecuteContext context, TodoTaskVO todoTaskVO, AutoResponseTaskExtInfoDTO autoResponseTaskExtInfoDTO) {
        List<MaterailInfoDO> materialInfoList = getMaterialInfoList(context, todoTaskVO);
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return null;
        }
        ResponseTypeEnum responseTypeEnum = ResponseTypeEnum.codeOf(autoResponseTaskExtInfoDTO.getResponseType());
        if (ResponseTypeEnum.ANY.equals(responseTypeEnum)) {
            Random random = new Random();
            return Lists.newArrayList(materialInfoList.get(random.nextInt(materialInfoList.size())));
        }
        return materialInfoList;
    }

    /**
     * 获取素材信息列表
     *
     * @param todoTaskVO 任务数据
     * @return 素材列表
     */
    private List<MaterailInfoDO> getMaterialInfoList(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        AutoResponseTaskExtInfoDTO autoResponseMatchRule = getAutoResponseMatchRule(context, todoTaskVO);
        if (autoResponseMatchRule == null) {
            return Lists.newArrayList(getMaterialInfo(context, todoTaskVO));
        }
        List<Long> materialIdList = autoResponseMatchRule.getResponseMaterialIdList();
        if (CollectionUtils.isEmpty(materialIdList)) {
            return Lists.newArrayList(getMaterialInfo(context, todoTaskVO));
        }
        List<MaterailInfoDO> materialInfoList = materialService.listByIds(materialIdList);
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return Lists.newArrayList(getMaterialInfo(context, todoTaskVO));
        }
        return materialInfoList;
    }

    /**
     * 异步发送消息
     *
     * @param todoTaskVO 任务信息
     */
    private void asyncSendMessage(TaskExecuteContext context, TodoTaskVO todoTaskVO, MaterailInfoDO materialInfoDO) {
        switch (getFinalTargetType(context)) {
            case WX_CHAT_ID:
                sendMessageToGroup(context, todoTaskVO, materialInfoDO);
                break;
            case WX_EXTERNAL_USERID:
                sendMessageToCustomer(context, todoTaskVO, materialInfoDO);
                break;
            default:
                break;
        }
    }

    /**
     * 发送消息给客户
     *
     * @param todoTaskVO     执行的任务信息
     * @param materialInfoDO 素材信息处理
     */
    private void sendMessageToCustomer(TaskExecuteContext context, TodoTaskVO todoTaskVO, MaterailInfoDO materialInfoDO) {
        // 参数获取
        String sendUserId = getSendUserId(context, todoTaskVO);
        // 只处理第一条
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String externalUserId = getFinalTargetId(context, taskDataBody);
        // 关系构建
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(context, todoTaskVO, MaterialSendSceneTypeConstant.AUTO_RESPONSE_SILIAO, sendUserId, null, getUnionId(externalUserId));
        // 素材上下文
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setWechatUserId(sendUserId);
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        if (StringUtils.isNotBlank(todoTaskVO.getData().get(0).getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo()));
        }
        materialContentConvertContext.setOriginContent(materialInfoDO.getContent());
        // 请求体拼接
        SendMsgToCustomerRequest request = new SendMsgToCustomerRequest();
        request.setUserId(sendUserId);
        request.setExternalUserId(externalUserId);
        request.setMaterailInfoDO(materialInfoDO);
        request.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        request.setMainTaskInstanceId(todoTaskVO.getMainTaskInstanceId());
        request.setAbTestBucketId(getAbTestBucketId(todoTaskVO));
        request.setMaterialContentConvertContext(materialContentConvertContext);
        request.setTaskType(TaskType.AUTO_RESPONSE);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> enterpriseWechatManager.asyncSendMessageToCustomer(request));
        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        PlatformLogUtil.logFail("AutoResponseTaskExecutor.sendMessageToCustomer.result", LogListUtil.newArrayList(externalUserId, sendUserId, result));
    }

    /**
     * 获取客户的unionId
     *
     * @param externalUserId 拓展用户id
     * @return 客户的unionId
     */
    private String getUnionId(String externalUserId) {
        List<WechatCustomerVO> wechatCustomerList = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(externalUserId));
        if (CollectionUtils.isEmpty(wechatCustomerList)) {
            return null;
        }
        return wechatCustomerList.get(0).getUnionId();
    }

    /**
     * 发送消息到群聊
     *
     * @param todoTaskVO     执行的任务信息
     * @param materialInfoDO 素材信息
     */
    private void sendMessageToGroup(TaskExecuteContext context, TodoTaskVO todoTaskVO, MaterailInfoDO materialInfoDO) {
        // 参数获取
        String sendUserId = getSendUserId(context, todoTaskVO);

        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String chatId = getFinalTargetId(context, taskDataBody);
        if (StringUtils.isBlank(sendUserId)) {
            sendUserId = getChatSendUserIdByIsvRoute(chatId, context.getTaskId(), context.getTaskDataVO(), context.getInstanceId());
        }
        // 关系构建
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(context, todoTaskVO, MaterialSendSceneTypeConstant.AUTO_RESPONSE_QUNLIAO, sendUserId, chatId, null);
        // 素材上下文
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setWechatUserId(sendUserId);
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        if (StringUtils.isNotBlank(todoTaskVO.getData().get(0).getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo()));
        }
        materialContentConvertContext.setOriginContent(materialInfoDO.getContent());
        // 请求体拼接
        SendMsgToGroupRequest sendMsgToGroupRequest = new SendMsgToGroupRequest();
        sendMsgToGroupRequest.setChatId(chatId);
        sendMsgToGroupRequest.setUserId(sendUserId);
        sendMsgToGroupRequest.setMainTaskInstanceId(todoTaskVO.getMainTaskInstanceId());
        sendMsgToGroupRequest.setAbTestBucketId(getAbTestBucketId(todoTaskVO));
        sendMsgToGroupRequest.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        sendMsgToGroupRequest.setMaterialContentConvertContext(materialContentConvertContext);
        sendMsgToGroupRequest.setMaterailInfoDO(materialInfoDO);
        sendMsgToGroupRequest.setTaskType(TaskType.GROUP_CHAT_MESSAGE);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> enterpriseWechatManager.asyncSendMessageToGroup(sendMsgToGroupRequest));
        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        PlatformLogUtil.logInfo("自动回复发送消息到群聊结果", LogListUtil.newArrayList(chatId, sendUserId, result, context.getTaskId()));
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return MapUtils.getString(context.getExtInfo(), "targetId");

    }

    /**
     * 根据群账号选取策略获取发送人
     * @param chatId 群聊id
     * @param taskId 任务Id
     * @return sendUserId
     */
    private String getChatSendUserIdByIsvRoute(String chatId, Long taskId, TaskDataVO taskDataVO, Long instanceId) {
        if (StringUtils.isEmpty(chatId)) {
            PlatformLogUtil.logFail("群聊自动回复获取发送人失败, chatId为空", LogListUtil.newArrayList(chatId, taskId));
            throw new TripscrmException(TripSCRMErrorCode.GROUP_INFO_NOT_EXISTS);
        }
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setChatId(chatId);
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        isvRouteContext.setRiskActionEnum(RiskActionEnum.GROUP_SEND_MESSAGE);
        TripSCRMResult<WechatUserDTO> isvRouteResult = isvRouteService.matchWechatUser(isvRouteContext);
        if (!isvRouteResult.isSuccess() || Objects.isNull(isvRouteResult.getData()) || StringUtils.isBlank(isvRouteResult.getData().getUserId())) {
            PlatformLogUtil.logFail("群聊自动回复获取获取发送人失败，根据服务商策略获取发送人失败", LogListUtil.newArrayList(isvRouteContext, isvRouteResult, taskId));
            throw new TripscrmException(TripSCRMErrorCode.MATCH_WECHAT_USER_BY_ISV_ROUTE_STRATEGY_FAIL);
        }
        return isvRouteResult.getData().getUserId();
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        String msgSourceType = MapUtils.getString(context.getExtInfo(), "msgSourceType");
        if (StringUtils.isBlank(msgSourceType) || AutoResponseMsgSourceEnum.codeOf(msgSourceType) == null) {
            PlatformLogUtil.logFail("自动回复获取不到最终的目标类型", LogListUtil.newArrayList(context.getTaskId(), context.getExtInfo()));
            return null;
        }
        return AutoResponseMsgSourceEnum.codeOf(msgSourceType).getTargetTypeEnum();
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        String msgSourceType = MapUtils.getString(context.getExtInfo(), "msgSourceType");
        if (StringUtils.isBlank(msgSourceType) || AutoResponseMsgSourceEnum.codeOf(msgSourceType) == null) {
            PlatformLogUtil.logFail("自动回复获取不到最终的目标ID", LogListUtil.newArrayList(context.getTaskId(), taskDataBody));
            return null;
        }
        String key = AutoResponseMsgSourceEnum.codeOf(msgSourceType).getKey();
        return MapUtils.getString(context.getExtInfo(), key);
    }

    private boolean paramVerify(TaskDataVO.DataBodyVO dataBodyVO, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 目标信息需要存在（群/客户）
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(dataBodyVO.getTargetType());
        if (targetTypeEnum == null) {
            PlatformLogUtil.logFail("自动回复目标类型不存在", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            return false;
        }
        if (StringUtils.isBlank(dataBodyVO.getTargetId())) {
            PlatformLogUtil.logFail("自动回复目标ID不存在", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            return false;
        }
        // 任务空间校验
        if (getTaskBelongSpace(context, todoTaskVO) == null) {
            PlatformLogUtil.logFail("自动回复参数校验不通过", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            return false;
        }
        // 目标空间校验
        Long belongSpace = wechatUserService.getSpaceId(getSendUserId(context, todoTaskVO), SpaceInfoThreadLocalUtils.getCorpId());
        if (belongSpace == null) {
            PlatformLogUtil.logFail("自动回复找不到目标对应的空间", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            return false;
        }
        // 发送人校验
        if (StringUtils.isBlank(getSendUserId(context, todoTaskVO))) {
            PlatformLogUtil.logFail("自动回复发送人为空", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            return false;
        }
        // 消息内容
        if (MapUtils.isEmpty(context.getExtInfo()) || StringUtils.isBlank(MapUtils.getString(context.getExtInfo(), "message"))) {
            PlatformLogUtil.logFail("自动回复消息为空", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            return false;
        }
        return true;
    }

    /**
     * 获取任务的归属空间
     *
     * @param todoTaskVO 任务数据
     * @return 归属空间ID
     */
    private Long getTaskBelongSpace(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (todoTaskVO == null || context == null || context.getTaskInfoDOSnapshot() == null) {
            return null;
        }
        return context.getTaskInfoDOSnapshot().getSpaceId();
    }

    private boolean skipProcess(TaskDataVO.DataBodyVO dataBodyVO, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 任务空间和成员空间统一性判断
        Long taskBelongSpace = getTaskBelongSpace(context, todoTaskVO);
        Long targetBelongSpace = wechatUserService.getSpaceId(getSendUserId(context, todoTaskVO), SpaceInfoThreadLocalUtils.getCorpId());
        if (!taskBelongSpace.equals(targetBelongSpace)) {
            PlatformLogUtil.logFail("自动回复，空间不一致", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            todoTaskVO.setSuccess(true);
            return true;
        }
        // 规则数据校验
        AutoResponseTaskExtInfoDTO autoResponseMatchRule = getAutoResponseMatchRule(context, todoTaskVO);
        if (autoResponseMatchRule == null) {
            PlatformLogUtil.logFail("自动回复规则为空", LogListUtil.newArrayList(context.getTaskId(), todoTaskVO));
            todoTaskVO.setSuccess(false);
            todoTaskVO.setFailReason("自动回复规则为空");
            return true;
        }
        // 范围匹配结果
        boolean rangeMatchResult = isRangeMatch(autoResponseMatchRule, context, todoTaskVO);
        if (!rangeMatchResult) {
            PlatformLogUtil.logFail("自动回复范围匹配未命中", LogListUtil.newArrayList(todoTaskVO, autoResponseMatchRule, context));
            todoTaskVO.setSuccess(true);
            todoTaskVO.setFailReason("未命中范围");
            return true;
        }
        // 规则匹配结果
        boolean contentMatchResult = isMatch(context.getExtInfo(), autoResponseMatchRule, context.getTaskInfoDOSnapshot().getSpaceId());
        if (!contentMatchResult) {
            PlatformLogUtil.logFail("自动回复规则匹配未命中", LogListUtil.newArrayList(todoTaskVO, autoResponseMatchRule, context));
            todoTaskVO.setSuccess(true);
            todoTaskVO.setFailReason("未命中匹配规则");
            return true;
        }
        return false;
    }

    /**
     * 范围匹配
     *
     * @param autoResponseMatchRule 匹配规则
     * @param todoTaskVO            任务信息
     * @return 匹配结果
     */
    private boolean isRangeMatch(AutoResponseTaskExtInfoDTO autoResponseMatchRule, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        boolean isMatch = false;
        switch (getFinalTargetType(context)) {
            case WX_CHAT_ID:
                isMatch = matchChatRange(context, todoTaskVO);
                break;
            case WX_EXTERNAL_USERID:
                isMatch = matchUserRange(autoResponseMatchRule, context, todoTaskVO);
                break;
            default:
                break;
        }
        return isMatch;
    }

    /**
     * 匹配成员范围
     *
     * @param autoResponseMatchRule 匹配规则
     * @return 匹配结果
     */
    private boolean matchUserRange(AutoResponseTaskExtInfoDTO autoResponseMatchRule, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (!needProcessUser(autoResponseMatchRule)) {
            PlatformLogUtil.logFail("自动回复，不需要处理覆盖用户或者覆盖客户信息为空", LogListUtil.newArrayList(todoTaskVO.getTaskId(), autoResponseMatchRule));
            return false;
        }
        List<String> applyUserList = Lists.newArrayList();
        if (Optional.ofNullable(autoResponseMatchRule.getApplyAllUserId()).orElse(false)) {
            List<WechatUserDTO> wechatUserList = wechatUserService.listBySpaceId(getTaskBelongSpace(context, todoTaskVO));
            if (CollectionUtils.isNotEmpty(wechatUserList)) {
                applyUserList = wechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList());
            }
        } else {
            applyUserList = Optional.ofNullable(autoResponseMatchRule.getApplyUserList()).orElse(new ArrayList<>());
        }
        return applyUserList.contains(getSendUserId(context, todoTaskVO));
    }

    /**
     * 是否需要处理用户信息
     *
     * @param autoResponseTaskExtInfoDTO 自动回复规则配置
     * @return 是否需要处理
     */
    private boolean needProcessUser(AutoResponseTaskExtInfoDTO autoResponseTaskExtInfoDTO) {
        if (autoResponseTaskExtInfoDTO == null) {
            return false;
        }
        if (autoResponseTaskExtInfoDTO.getApplyAllUserId() == null && CollectionUtils.isEmpty(autoResponseTaskExtInfoDTO.getApplyUserList())) {
            return false;
        }
        return true;
    }

    /**
     * 匹配群聊范围
     *
     * @param todoTaskVO 任务数据
     * @return 匹配结果
     */
    private boolean matchChatRange(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (StringUtils.isBlank(context.getTaskInfoDOSnapshot().getExtInfo())) {
            return false;
        }

        AutoResponseTaskExtInfoDTO autoResponseTaskExtInfoDTO = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo(), AutoResponseTaskExtInfoDTO.class);
        // 所有群
        if (Optional.ofNullable(autoResponseTaskExtInfoDTO.getApplyAllGroup()).orElse(false)) {
            return true;
        }

        String chatId = getFinalTargetId(context, todoTaskVO.getData().get(0));
        List<String> chatIdList = Optional.ofNullable(autoResponseTaskExtInfoDTO.getChatIdList()).orElse(new ArrayList<>());
        // 指定群
        if (chatIdList.contains(chatId)) {
            return true;
        }

        // 命中标签
        if (!CollectionUtils.isEmpty(autoResponseTaskExtInfoDTO.getIncludeTagList())) {
            ItemTagRelationQuery tagRelationQuery = new ItemTagRelationQuery();
            tagRelationQuery.setItemId(chatId);
            tagRelationQuery.setItemType(BizTypeEnum.WECHAT_GROUP.getCode());
            List<ItemTagRelationDTO> tagRelationList = tagRelationService.selectByCondition(tagRelationQuery);
            boolean hit = tagRelationList
                    .stream()
                    .anyMatch(itemTagRelationDTO -> autoResponseTaskExtInfoDTO.getIncludeTagList().contains(itemTagRelationDTO.getTag()));
            if (hit) {
                return true;
            }
        }

        if (CollectionUtils.isEmpty(autoResponseTaskExtInfoDTO.getActivityIdList())) {
            return false;
        }

        // 查询活动对应的任务
        TaskQuery query = new TaskQuery();
        query.setActivityIdList(autoResponseTaskExtInfoDTO.getActivityIdList());
        query.setType(TaskType.MANAGER_WECHAT_JOIN_GROUP.getCode());
        List<TaskInfoDO> taskList = taskService.query(query);
        if (CollectionUtils.isEmpty(taskList)) {
            return false;
        }

        // 查询群聊是否关联群活码
        ResourceRelationQuery resourceRelationQuery = new ResourceRelationQuery();
        resourceRelationQuery.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
        resourceRelationQuery.setSourceType(ResourceTypeEnum.TASK.getCode().byteValue());
        resourceRelationQuery.setTargetType(ResourceTypeEnum.CHAT_ID.getCode().byteValue());
        resourceRelationQuery.setSourceIdList(taskList.stream().map(TaskInfoDO::getId).map(String::valueOf).collect(Collectors.toList()));
        resourceRelationQuery.setTargetId(chatId);
        List<ResourceRelationDO> resourceRelationList = resourceRelationService.query(resourceRelationQuery);
        return !CollectionUtils.isEmpty(resourceRelationList);
    }

    /**
     * 是否匹配规则
     *
     * @param extInfo               拓展信息
     * @param autoResponseMatchRule 自动回复匹配规则
     * @param spaceId               空间Id
     * @return
     */
    private boolean isMatch(Map<String, Object> extInfo, AutoResponseTaskExtInfoDTO autoResponseMatchRule, Long spaceId) {
        String message = MapUtils.getString(extInfo, "message");
        List<String> atUserIdList = (List<String>) MapUtils.getObject(extInfo, "atUserIdList");
        // 全匹配结果
        boolean allMatchResult = isMatchMessage(message, autoResponseMatchRule.getAllMatchKeywordList(), true, SwitchConfig.REMOVE_SPACE, autoResponseMatchRule.getAllMatch());
        // 半匹配结果
        boolean anyMatchResult = isMatchMessage(message, autoResponseMatchRule.getAnyMatchKeywordList(), false, SwitchConfig.REMOVE_SPACE, autoResponseMatchRule.getAnyMatch());
        // 匹配@结果
        boolean matchAtResult = isMatchAt(autoResponseMatchRule.getAtUserIdList(), autoResponseMatchRule.getApplyAllAt(), atUserIdList, spaceId, autoResponseMatchRule.getIsAt());
        // 满足任意条件
        return allMatchResult || anyMatchResult || matchAtResult;
    }

    /**
     * 匹配@成员
     *
     * @param applyUserIdList    应用范围内的userIdList
     * @param coverAllUserId     是否覆盖空间下所有成员
     * @param msgCoverUserIdList 消息覆盖的userIdList
     * @param apply              启用状态
     * @param spaceId            任务所属空间id
     * @return 匹配结果
     */
    private boolean isMatchAt(List<String> applyUserIdList, Boolean coverAllUserId, List<String> msgCoverUserIdList, Long spaceId, Boolean apply) {
        // 不启用
        if (!Optional.ofNullable(apply).orElse(false)) {
            return false;
        }
        // 配置覆盖成员为空
        if (CollectionUtils.isEmpty(applyUserIdList) && !Optional.ofNullable(coverAllUserId).orElse(false)) {
            return false;
        }
        // 消息无@成员
        if (CollectionUtils.isEmpty(msgCoverUserIdList)) {
            return false;
        }
        // 任务覆盖成员列表
        List<String> applyList;
        if (coverAllUserId) {
            List<WechatUserDTO> wechatUserList = wechatUserService.listBySpaceId(spaceId);
            if (CollectionUtils.isEmpty(wechatUserList)) {
                return false;
            }
            applyList = wechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList());
        } else {
            applyList = applyUserIdList;
        }
        // @列表匹配
        for (String msgCoverUserId : msgCoverUserIdList) {
            if (applyList.contains(msgCoverUserId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否匹配规则
     *
     * @param messageContent 消息内容
     * @param keywordList    关键字列表
     * @param allMatch       是否全匹配
     * @param removeSpace    是否移除字符串中的空格
     * @param apply          启用状态
     * @return 匹配结果
     */
    private boolean isMatchMessage(String messageContent, List<String> keywordList, Boolean allMatch, Boolean removeSpace, Boolean apply) {
        if (!Optional.ofNullable(apply).orElse(false) || StringUtils.isBlank(messageContent) || CollectionUtils.isEmpty(keywordList)) {
            return false;
        }
        if (Optional.ofNullable(removeSpace).orElse(false)) {
            messageContent = messageContent.replaceAll("\\s+", "");
        }
        // 半匹配
        if (!Optional.ofNullable(allMatch).orElse(false)) {
            return anyMatch(keywordList, messageContent);
        }
        // 全匹配
        return allMatch(keywordList, messageContent);
    }

    /**
     * 半匹配结果
     *
     * @param keywordList    关键字列表
     * @param messageContent 消息内容
     * @return 匹配结果
     */
    private Boolean anyMatch(List<String> keywordList, String messageContent) {
        if (CollectionUtils.isEmpty(keywordList) || StringUtils.isBlank(messageContent)) {
            return false;
        }
        for (String keyword : keywordList) {
            if (messageContent.contains(keyword)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 全匹配结果
     *
     * @param keywordList    关键字列表
     * @param messageContent 消息内容
     * @return 匹配结果
     */
    private Boolean allMatch(List<String> keywordList, String messageContent) {
        if (CollectionUtils.isEmpty(keywordList) || StringUtils.isBlank(messageContent)) {
            return false;
        }
        for (String keyword : keywordList) {
            if (messageContent.equals(keyword)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取自动回复规则配置
     *
     * @param todoTaskVO 任务数据
     * @return 自动回复规则
     */
    private AutoResponseTaskExtInfoDTO getAutoResponseMatchRule(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (todoTaskVO == null || context == null || context.getTaskInfoDOSnapshot() == null) {
            PlatformLogUtil.logFail("自动回复任务数据获取不到", LogListUtil.newArrayList(todoTaskVO));
            return null;
        }
        String extInfo = context.getTaskInfoDOSnapshot().getExtInfo();
        if (StringUtils.isBlank(extInfo)) {
            PlatformLogUtil.logFail("自动回复任务规则获取不到", LogListUtil.newArrayList(todoTaskVO));
            return null;
        }
        return JSONObject.parseObject(extInfo, AutoResponseTaskExtInfoDTO.class);
    }

    @Override
    public boolean checkActivityContextValid(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        return true;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.AUTO_RESPONSE;
    }
}
