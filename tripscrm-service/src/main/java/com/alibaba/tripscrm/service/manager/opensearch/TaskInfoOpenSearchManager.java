package com.alibaba.tripscrm.service.manager.opensearch;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.model.domain.query.TaskInfoQuery;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.aliyun.opensearch.sdk.generated.search.Order;
import com.aliyun.opensearch.sdk.generated.search.SearchParams;
import com.aliyun.opensearch.sdk.generated.search.Sort;
import com.aliyun.opensearch.sdk.generated.search.SortField;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/24 18:54
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskInfoOpenSearchManager extends BaseOpenSearchManager {

    /**
     * 查询任务Id
     *
     * @param query 查询条件
     * @return PageInfo<Long>
     */
    public PageInfo<Long> getTaskIdList(TaskInfoQuery query) {
        List<Long> res = new ArrayList<>();
        try {
            SearchParams searchParams = buildPageSearchParams(query, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (NumberUtils.validLong(query.getId())) {
                queryList.add(String.format("id:'%d'", query.getId()));
            }
            if (StringUtils.hasText(query.getName())) {
                queryList.add(String.format("name:'%s'", query.getName()));
            }
            if (StringUtils.hasText(query.getCreator())) {
                queryList.add(String.format("creator:'%s'", query.getCreator()));
            }
            if (!CollectionUtils.isEmpty(query.getTaskTypeCodeList())) {
                queryList.add("(" + query.getTaskTypeCodeList().stream().map(taskTypeCode -> String.format("type:'%s'", taskTypeCode)).collect(Collectors.joining(" OR ")) + ")");
            }
            if (StringUtils.hasText(query.getManager())) {
                queryList.add(String.format("manager:'%s'", query.getManager()));
            }
            if (NumberUtils.validLong(query.getActivityId())) {
                queryList.add(String.format("activity_id:'%d'", query.getActivityId()));
            }
            if (NumberUtils.validLong(query.getSpaceId())) {
                queryList.add(String.format("space_id:'%d'", query.getSpaceId()));
            }
            if (NumberUtils.validLong(query.getEventSourceId())) {
                queryList.add(String.format("event_source_id:'%d'", query.getEventSourceId()));
            }
            // 标签
            if (StringUtils.hasText(query.getTags())) {
                queryList.add("(" + Arrays.stream(query.getTags().split(",")).map(tagId -> String.format("tag:'%s'", tagId)).collect(Collectors.joining(" OR ")) + ")");
            }

            // 创建时间
            if (Objects.nonNull(query.getCreateStartTime()) && Objects.nonNull(query.getCreateEndTime())) {
                queryList.add(String.format("gmt_create:[%d,%d]", query.getCreateStartTime().getTime(), query.getCreateEndTime().getTime()));
            }
            if (Objects.nonNull(query.getCreateStartTime()) && Objects.isNull(query.getCreateEndTime())) {
                queryList.add(String.format("gmt_create:[%d,)", query.getCreateStartTime().getTime()));
            }
            if (Objects.isNull(query.getCreateStartTime()) && Objects.nonNull(query.getCreateEndTime())) {
                queryList.add(String.format("gmt_create:(,%d]", query.getCreateEndTime().getTime()));
            }

            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            // 过滤条件
            List<String> filterList = new ArrayList<>();

            if (Objects.nonNull(query.getEnv())) {
                filterList.add(String.format("env=\"%s\"", query.getEnv()));
            }
            if (Objects.nonNull(query.getStatus())) {
                filterList.add(String.format("status=\"%s\"", query.getStatus()));
            }
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);
            PageInfo<Long> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convert2TaskId);
            PlatformLogUtil.logFail("Opensearch查询任务Id成功", LogListUtil.newArrayList(searchParams, pageInfo));
            return pageInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询任务Id异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }

        return PageUtils.getPageInfo(res, a -> a);
    }

    private Long convert2TaskId(JSONObject o) {
        return o.getLong("id");
    }


    @Override
    protected String getAppName() {
        return "scrm_task_info";
    }

    @Override
    protected List<String> getFetchFields() {
        return Lists.newArrayList("id");
    }
}
