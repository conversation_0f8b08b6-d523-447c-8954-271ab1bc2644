package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.api.service.ForwardMessageService;
import com.alibaba.tripzoo.proxy.enums.ErrorCodeEnum;
import com.alibaba.tripzoo.proxy.enums.ForwardMessageTargetTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.request.MergeMessageRelayRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 转发消息任务执行器
 *
 * <AUTHOR>
 * @Date 2024/1/24 10:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ForwardMessageTaskExecutor extends AbstractTaskExecutor {
    private final ForwardMessageService forwardMessageService;
    private final ActivityContextService activityContextService;
    private final WechatUserService wechatUserService;


    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);

        //构建请求
        MergeMessageRelayRequest request = getMergeMessageRelayRequest(context, todoTaskVO, taskDataBody);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> asyncForwardMessage(request));

        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }
        String targetId = getFinalTargetId(context, taskDataBody);
        String sendUserId = getSendUserId(context, todoTaskVO);

        JSONObject data = new JSONObject();
        data.put(TaskConstant.TARGET_ID, targetId);
        data.put("wechatUserId", sendUserId);
        data.put("extraInfo", context.getExtInfo());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);

    }

    private MergeMessageRelayRequest getMergeMessageRelayRequest(TaskExecuteContext context, TodoTaskVO todoTaskVO, TaskDataVO.DataBodyVO taskDataBody) {
        MergeMessageRelayRequest request = new MergeMessageRelayRequest();
        String targetId = getFinalTargetId(context, taskDataBody);
        String sendUserId = getSendUserId(context, todoTaskVO);

        // 素材
        String msgId = getMsgId(context, todoTaskVO, taskDataBody);
        request.setMsgIdList(Lists.newArrayList(msgId));
        request.setUserId(sendUserId);
        ForwardMessageTargetTypeEnum forwardMessageType = getForwardMessageType(context);
        request.setType(forwardMessageType);
        request.setTargetId(targetId);
        //如果tagetId和userId是同一个人,就直接发送到群里
        if (StringUtils.equals(targetId, sendUserId)) {
            request.setType(ForwardMessageTargetTypeEnum.GROUP);
            JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
            if(Objects.isNull(extInfoJson) || StringUtils.isBlank(extInfoJson.getString("chatId"))){
                PlatformLogUtil.logFail("转发消息,不支持的目标消息类型", LogListUtil.newArrayList(extInfoJson));
                throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
            }
            request.setTargetId(extInfoJson.getString("chatId"));
        }
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        return request;
    }

    private ForwardMessageTargetTypeEnum getForwardMessageType(TaskExecuteContext context) {
        ActivityTargetTypeEnum finalTargetType = getFinalTargetType(context);
        switch (finalTargetType) {
            case WX_CHAT_ID:
                return ForwardMessageTargetTypeEnum.GROUP;
            case WX_EXTERNAL_USERID:
                return ForwardMessageTargetTypeEnum.CUSTOMER;
            case WX_USER_ID:
                return ForwardMessageTargetTypeEnum.INTERNAL_FRIEND;
            default:
                PlatformLogUtil.logFail("转发消息,不支持的目标消息类型", LogListUtil.newArrayList(finalTargetType));
                throw new TripscrmException(TripSCRMErrorCode.FORWARD_MESSAGE_TYPE_ERROR);
        }
    }

    private TripSCRMResult<String> asyncForwardMessage(MergeMessageRelayRequest mergeMessageRelayRequest) {
        ResultDO<String> resultDO = forwardMessageService.mergeMessageRelay(mergeMessageRelayRequest);
        PlatformLogUtil.logInfo("异步转发消息", LogListUtil.newArrayList(mergeMessageRelayRequest, resultDO));
        if (Objects.isNull(resultDO)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED, resultDO.getResultMessage());
        }

        if (!resultDO.getSuccess()) {
            ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
            switch (errorCodeEnum) {
                case WECHAT_CUSTOMER_DATA_ERROR:
                case NO_FRIEND_RELATION:
                case WECHAT_USER_NOT_ONLINE:
                    return TripSCRMResult.fail(TripSCRMErrorCode.FORWARD_MESSAGE_FAIL);
                case RATE_LIMIT_FILTER:
                    return TripSCRMResult.fail(TripSCRMErrorCode.RPC_RATE_LIMIT_FILTER);
                default:
                    return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }
        }
        if (Objects.isNull(resultDO.getModel())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
        return TripSCRMResult.success(resultDO.getModel(), resultDO.getResultMessage());
    }

    private String getMsgId(TaskExecuteContext context, TodoTaskVO todoTaskVO, TaskDataVO.DataBodyVO taskDataBody) {
        Map<String, Object> extInfo = context.getExtInfo();
        if (extInfo != null && extInfo.containsKey(TaskConstant.MSG_ID)) {
            String msgId = (String) extInfo.get(TaskConstant.MSG_ID);
            PlatformLogUtil.logFail("获得消息id", LogListUtil.newArrayList(taskDataBody, msgId));
            return msgId;
        }

        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materailInfoDO) || StringUtils.isBlank(materailInfoDO.getContent())) {
            PlatformLogUtil.logFail("material get fail", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MATERIAL_IN_TASK);
        }
        JSONObject messageListJson = JSON.parseObject(materailInfoDO.getContent());
        JSONArray messageList = messageListJson.getJSONArray("messageList");
        if (messageList == null || messageList.isEmpty()) {
            throw new TripscrmException(TripSCRMErrorCode.MATERIAL_NOT_EXIST);
        }
        JSONObject messageJson = messageList.getJSONObject(0);
        if(!messageJson.containsKey(TaskConstant.MSG_ID) || StringUtils.isBlank(messageJson.getString(TaskConstant.MSG_ID))){
            throw new TripscrmException(TripSCRMErrorCode.CHAT_RECORD_MSG_ID_NOT_EXIST);
        }
        String msgId = messageJson.getString(TaskConstant.MSG_ID);
        return msgId;
    }

    @Override
    protected ActivityTaskInfoBO getActivityContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        //TODO 要改
        Map<String, Object> extInfo = context.getExtInfo();
        if (extInfo != null && extInfo.containsKey(TaskConstant.CONTEXT_ID)) {
            String contextId = (String) extInfo.get(TaskConstant.CONTEXT_ID);
            PlatformLogUtil.logFail("私聊消息发送过程中从上下文扩展获取contextId", LogListUtil.newArrayList(context, contextId));

            ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(Long.valueOf(contextId));

            if(Objects.isNull(activityTaskInfoBO) || !activityTaskInfoBO.getTargetId().equals(String.valueOf(context.getTaskId()))){
                return null;
            }

            return activityTaskInfoBO;
        }
        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        query.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_TASK_ID);
        JSONObject config = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getConfig());
        if(Objects.nonNull(config) && config.containsKey(TaskConstant.SUB_TASK_ID)){
            query.setTargetId(config.getString(TaskConstant.SUB_TASK_ID));
        }
        List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
        return ActivityContextService.getNewestBo(activityTaskInfoBOS);
    }


    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey(TaskConstant.SEND_USER_ID)) {
            return (String) taskDataBody.getContext().get(TaskConstant.SEND_USER_ID);
        }
        // 获取指定的发送者id
        String specifiedSendUserId = getSpecifiedSendUserId(context, taskDataBody);
        if (specifiedSendUserId != null) {
            taskDataBody.getContext().put("sendUserId", specifiedSendUserId);
            return specifiedSendUserId;
        }

        // 指定了发送者，需要判断发送者在不在线
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        if (extInfoJson.containsKey(TaskConstant.SEND_USER_ID)) {
            String sendUserId = extInfoJson.getString(TaskConstant.SEND_USER_ID);
            List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(sendUserId));
            if (CollectionUtils.isNotEmpty(wechatUserList) && Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
                taskDataBody.getContext().put(TaskConstant.SEND_USER_ID, sendUserId);
                return getSendUserId(context, todoTaskVO);
            }
        }
        PlatformLogUtil.logFail("getWechatUserId empty", LogListUtil.newArrayList(taskDataBody));
        throw new TripscrmException(TripSCRMErrorCode.TASK_QUERY_TASK_SEND_USER_FAIL);
    }

    private String getSpecifiedSendUserId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        String sendUserId;
        // 先取事件源或接口指定的发送者id
        Map<String, Object> extInfo = context.getExtInfo();
        if (extInfo != null && extInfo.containsKey(TaskConstant.SEND_USER_ID)) {
            sendUserId = (String) extInfo.get(TaskConstant.SEND_USER_ID);
            PlatformLogUtil.logFail("私聊消息发送过程中从上下文扩展获取sendUserId", LogListUtil.newArrayList(taskDataBody, sendUserId));
            checkSendUserOnline(taskDataBody, sendUserId);
            return sendUserId;
        }

        // 再取离线表指定的发送者id
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        if (extInfoJson.containsKey(TaskConstant.SEND_USER_ID)) {
            sendUserId = extInfoJson.getString(TaskConstant.SEND_USER_ID);
            PlatformLogUtil.logFail("私聊消息发送过程中从taskDataBody取sendUserId", LogListUtil.newArrayList(taskDataBody, sendUserId));
            checkSendUserOnline(taskDataBody, sendUserId);
            return sendUserId;
        }

        return null;
    }

    private void checkSendUserOnline(TaskDataVO.DataBodyVO taskDataBody, String sendUserId) {
        // 判断指定的发送者id是否在线
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(sendUserId));
        if (CollectionUtils.isNotEmpty(wechatUserList) && Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
            return;
        }

        PlatformLogUtil.logFail("私聊消息发送过程中指定的发送者id不在线", LogListUtil.newArrayList(taskDataBody, sendUserId));
        throw new TripscrmException(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE);
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        Integer targetType = context.getTaskDataVO().getData().get(0).getTargetType();
        if (NumberUtils.validInteger(targetType) && ActivityTargetTypeEnum.codeOf(targetType) != null) {
            return ActivityTargetTypeEnum.codeOf(targetType);
        }
        Map<String, Object> extInfo = context.getExtInfo();
        if (extInfo != null && extInfo.containsKey(TaskConstant.TARGET_TYPE)) {
            targetType = (Integer) extInfo.get(TaskConstant.TARGET_TYPE);
            PlatformLogUtil.logFail("转发消息发送过程中从上下文扩展获取targetType", LogListUtil.newArrayList(targetType, targetType));
            return ActivityTargetTypeEnum.codeOf(targetType);
        }
        PlatformLogUtil.logFail("转发消息取不到最终的目标类型", LogListUtil.newArrayList(context.getTaskId(), context.getTaskDataVO().getData().get(0)));
        throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);

    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (StringUtils.isNotBlank(taskDataBody.getTargetId())) {
            return taskDataBody.getTargetId();
        }
        Map<String, Object> extInfo = context.getExtInfo();
        if (extInfo != null && extInfo.containsKey(TaskConstant.TARGET_ID)) {
            String targetId = (String) extInfo.get(TaskConstant.TARGET_ID);
            PlatformLogUtil.logFail("转发消息发送过程中从上下文扩展获取finalTargetId", LogListUtil.newArrayList(taskDataBody, targetId));
            return targetId;
        }
        PlatformLogUtil.logFail("empty targetId", LogListUtil.newArrayList(taskDataBody));
        throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
    }

    @Override
    protected String getUniqueKey(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        JSONObject extraInfo = JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo());
        if (Objects.isNull(extraInfo) || !extraInfo.containsKey("uuid")) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        String uuid = extraInfo.getObject("uuid", new TypeReference<String>() {
        });

        return TairConstant.LDB_TASK_FINISH_TARGET_DATA_PREFIX + context.getInstanceId() + "_" + todoTaskVO.getData().get(0).getTargetId() + "_" + uuid;
    }


    @Override
    protected TaskType getTaskType() {
        return TaskType.FORWARD_MESSAGE;
    }

    @Override
    protected Map<String, String> getTaskAsyncExecuteExtraInfo(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        Map<String, String> result = Maps.newHashMap();
        result.put(TaskConstant.TARGET_ID,extInfoJson.getString("chatId"));
        ForwardMessageTargetTypeEnum forwardMessageType = getForwardMessageType(context);
        result.put(TaskConstant.FORWARD_MESSAGE_TYPE, String.valueOf(forwardMessageType.getCode()));
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        if(Objects.nonNull(activityContext)){
            result.put(TaskConstant.CONTEXT_ID, String.valueOf(activityContext.getContextId()));
        }
        return result;
    }
}
