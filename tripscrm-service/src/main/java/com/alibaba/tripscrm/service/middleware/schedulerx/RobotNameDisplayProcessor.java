package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.wx.WXBizMsgCryptConfig;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.alibaba.tripzoo.proxy.api.service.RobotService;
import com.alibaba.tripzoo.proxy.enums.AccountDisplayTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.request.SetNameDisplayRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 机器人对外名称展示处理器【默认统一对外展示成员别名】
 * <AUTHOR>
 * @Date 2024/5/9 14:14
 **/
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RobotNameDisplayProcessor extends JavaProcessor {

    private final RobotService robotService;
    private final WechatUserService wechatUserService;
    private final WXCorpStorage wxCorpStorage;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        for (WXBizMsgCryptConfig corpConfig : wxCorpStorage.listConfig()) {
            SpaceInfoThreadLocalUtils.setCorpId(corpConfig.getCorpId());
            // 获取所有成员信息
            List<WechatUserDTO> wechatUserList = wechatUserService.listAll();
            if (CollectionUtils.isEmpty(wechatUserList)) {
                PlatformLogUtil.logFail("修改成员对外展示名称类型-成员列表为空");
                return new ProcessResult(true);
            }
            List<WechatUserDTO> onlineWechatUserList = wechatUserList.stream()
                    .filter(wechatUserDTO -> RobotStatusEnum.ONLINE.getCode().equals(wechatUserDTO.getOnlineStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(onlineWechatUserList)) {
                PlatformLogUtil.logFail("修改成员对外展示名称类型-在线成员列表为空");
                return new ProcessResult(true);
            }
            // 设置对外展示别名
            for (WechatUserDTO wechatUserDTO : onlineWechatUserList) {
                SetNameDisplayRequest request = new SetNameDisplayRequest();
                request.setUserId(wechatUserDTO.getUserId());
                request.setAccountDisplayTypeEnum(AccountDisplayTypeEnum.ALIAS);
                request.setCorpId(corpConfig.getCorpId());
                ResultDO<String> resultDO = robotService.setNameDisplay(request);
                PlatformLogUtil.logFail("修改成员对外展示名称类型-调用接口结果", LogListUtil.newArrayList(request, resultDO));
            }
        }
        return new ProcessResult(true);
    }
}

