package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.ScrmConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.material.MaterialContentConvertService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.SendPersonalWelcomeRequest;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/8 15:27
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class PersonalWelcomeTaskExecutor extends AbstractTaskExecutor {
    private final ActivityContextService activityContextService;
    private final MaterialContentConvertService materialContentConvertService;
    private final EnterpriseWechatManager enterpriseWechatManager;
    private final MaterialTransferService materialTransferService;
    private final WechatCustomerService wechatCustomerService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> todoTaskList = todoTaskVO.getData();
        if (CollectionUtils.isEmpty(todoTaskList)) {
            return;
        }
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskList.get(0);

        // 参数校验
        paramVerify(taskDataBody, context, todoTaskVO);

        ActivityTaskInfoBO targetActivityContext = getTargetActivityContext(context, taskDataBody);
        if (Objects.nonNull(targetActivityContext)) {
            Map<String, Object> extraJson = targetActivityContext.getExtraJson();
            // 是否已经发送过个人欢迎语
            if (MapUtils.getBoolean(extraJson, "isSendPersonWelcome", false)) {
                todoTaskVO.setSuccess(true);
                return;
            }

            targetActivityContext.getExtraJson().put("isSendPersonWelcome", true);
            activityContextService.upsert(targetActivityContext);
            PlatformLogUtil.logFail("upsertTargetActivityContext", LogListUtil.newArrayList(targetActivityContext));
        }

        // 素材处理
        String externalUserId = getFinalTargetId(context, taskDataBody);
        List<WechatCustomerVO> wechatCustomerVOList = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(externalUserId));
        if (wechatCustomerVOList.isEmpty()) {
            throw new RuntimeException("客户关系不存在");
        }

        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.GEMA_HYY);
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(todoTaskVO.getMainTaskInstanceId());
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setSendUnionId(wechatCustomerVOList.get(0).getUnionId());
        materialTrackRelationDTO.setWxUserId(getSendUserId(context, todoTaskVO));

        MaterailInfoDO materialInfo = getMaterialInfo(context, todoTaskVO);
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setExternalUserId(externalUserId);
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        materialContentConvertContext.setOriginContent(materialInfo.getContent());
        String userId = getSendUserId(context, todoTaskVO);
        materialContentConvertContext.setWechatUserId(userId);
        // 欢迎语发送
        SendPersonalWelcomeRequest request = new SendPersonalWelcomeRequest();
        request.setWelcomeCode((String) context.getExtInfo().get(ScrmConstant.WELCOME_CODE));
        List<WxMessageBO> wxMessageList;
        wxMessageList = materialTransferService.buildWxMessages(getMaterialInfo(context, todoTaskVO), materialTrackRelationDTO, materialContentConvertContext, context.getTaskInfoDOSnapshot().getType());
        request.setMessageList(wxMessageList);
        List<WxMessageBO> textMessage = wxMessageList.stream().filter(wxMessageBO -> WxAttachmentTypeEnum.TEXT.equals(wxMessageBO.getMsgType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(textMessage)) {
            request.setContent(textMessage.get(0).getMsgContent());
        }
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        TripSCRMResult<Boolean> result = enterpriseWechatManager.sendPersonWelcomeMessage(request);
        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        JSONObject data = new JSONObject();
        data.put("extraInfo", materialContentConvertContext.getExtraInfo());
        data.put("sendUserId", userId);
        data.put("materialId", materialInfo.getId());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            return (String) taskDataBody.getContext().get("sendUserId");
        }

        Map<String, Object> extInfo = context.getExtInfo();
        if (!extInfo.containsKey("userId")) {
            return null;
        }

        String userId = (String) extInfo.get("userId");
        taskDataBody.getContext().put("sendUserId", userId);
        return userId;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_EXTERNAL_USERID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("empty targetId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!Lists.newArrayList(ActivityTargetTypeEnum.WX_EXTERNAL_USERID).contains(activityTargetType)) {
            PlatformLogUtil.logFail("only accept externalUserId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        return taskDataBody.getTargetId();
    }

    private boolean paramVerify(TaskDataVO.DataBodyVO dataBodyVO, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (StringUtils.isBlank(dataBodyVO.getTargetId())) {
            PlatformLogUtil.logFail(TripSCRMErrorCode.PERSONAL_WELCOME_TARGET_ID_EMPTY.getDescCn(), LogListUtil.newArrayList(dataBodyVO));
            throw new TripscrmException(TripSCRMErrorCode.PERSONAL_WELCOME_TARGET_ID_EMPTY.getDescCn());
        }
        if (MapUtils.isEmpty(context.getExtInfo())) {
            PlatformLogUtil.logFail(TripSCRMErrorCode.PERSONAL_WELCOME_CONTEXT_EMPTY.getDescCn(), LogListUtil.newArrayList(dataBodyVO));
            throw new TripscrmException(TripSCRMErrorCode.PERSONAL_WELCOME_CONTEXT_EMPTY.getDescCn());
        }
        if (!context.getExtInfo().containsKey(ScrmConstant.WELCOME_CODE)) {
            PlatformLogUtil.logFail(TripSCRMErrorCode.PERSONAL_WELCOME_CODE_EMPTY.getDescCn(), LogListUtil.newArrayList(dataBodyVO));
            throw new TripscrmException(TripSCRMErrorCode.PERSONAL_WELCOME_CODE_EMPTY.getDescCn());
        }
        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail(TripSCRMErrorCode.PERSONAL_WELCOME_MATERIAL_GET_FAIL.getDescCn(), LogListUtil.newArrayList(dataBodyVO));
            throw new TripscrmException(TripSCRMErrorCode.PERSONAL_WELCOME_MATERIAL_GET_FAIL.getDescCn());
        }

        return true;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.PERSONAL_WELCOME;
    }
}
