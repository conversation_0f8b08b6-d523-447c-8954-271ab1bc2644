package com.alibaba.tripscrm.service.service.task.ability.main;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.producer.selector.SelectMessageQueueByRandom;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 任务执行_主任务-子任务分发
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskDispatchProcessor implements ExecuteProcessor {
    private final MetaqProducer metaqProducer;

    @Override
    @TaskExecuteLog("任务执行_主任务_子任务分发")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        Long taskId = context.getTaskId();
        List<TodoTaskVO> todoTaskVOList = context.getTodoTaskVOList();
        if (CollectionUtils.isEmpty(todoTaskVOList)) {
            PlatformLogUtil.logFail("主任务执行-子任务分发，待处理子任务数据为空", LogListUtil.newArrayList(taskId, context));
            throw new TripscrmException(TripSCRMErrorCode.NOT_FOUND_TASK_EXECUTE_DATA);
        }

        // 取出拆分批数
        int splitSize = todoTaskVOList.size();
        TaskInfoDO taskSnapshot = context.getTaskInfoDOSnapshot();
        try {
            // 循环发送消息，分布式处理任务
            for (int i = 0; i < todoTaskVOList.size(); i++) {
                TodoTaskVO todoTaskVO = todoTaskVOList.get(i);
                Long instanceId = todoTaskVO.getInstanceId();
                TaskExecuteParam param = new TaskExecuteParam();
                param.setTaskId(taskSnapshot.getId());
                param.setInstanceId(instanceId);
                param.setTriggerType(TaskTriggerTypeEnum.getByCode(taskSnapshot.getTriggerType()));
                param.setSubTask(true);
                // 非测试场景，精减发送body内容，不发送任务数据，处理时从oss实时查询
                if (context.getTestFlag()) {
                    TaskDataVO taskDataVO = new TaskDataVO();
                    taskDataVO.setData(todoTaskVO.getData());
                    taskDataVO.setTotalCount((long) todoTaskVO.getData().size());
                    param.setTaskDataVO(taskDataVO);
                }
                boolean sendMqResult = metaqProducer.send(MQEnum.SUB_INNER_TASK_TRIGGER_TOPIC, "dispatch_normal_" + instanceId, JSON.toJSONString(param), new SelectMessageQueueByRandom());
                PlatformLogUtil.logFail("主任务执行-子任务分发，发送MQ消息", LogListUtil.newArrayList(taskId, instanceId, sendMqResult));
            }
        } catch (Exception ex) {
            PlatformLogUtil.logException("【任务执行异常】任务调度失败", ex.getMessage(), ex, LogListUtil.newArrayList(taskId, context.getInstanceId(), splitSize));
            if (!ex.getMessage().equals(TripSCRMErrorCode.TASK_EXECUTE_NOT_IN_SEND_TIME.getDesc())) {
                DingTalkApi.sendTaskMessage(String.format("【任务执行异常】任务调度失败，任务ID:%s，实例ID:%s",
                        taskId, context.getInstanceId()));
            }
            throw ex;
        }
        PlatformLogUtil.logInfo("主任务执行-子任务分发，子任务分发完成", LogListUtil.newArrayList(taskId, context.getInstanceId()));
        return TripSCRMResult.success(null);
    }
}
