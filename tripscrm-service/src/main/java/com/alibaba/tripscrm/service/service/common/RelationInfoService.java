package com.alibaba.tripscrm.service.service.common;

import com.alibaba.tripscrm.dal.model.domain.data.RelationInfoDO;
import com.alibaba.tripscrm.service.enums.task.RelationInfoTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;

import java.util.List;

/**
 * 关系信息服务
 */
public interface RelationInfoService {

    /**
     * 增加关系
     * @param relationInfoDO 关系对象
     * @return 新增结果
     */
    Boolean addRelation(RelationInfoDO relationInfoDO);

    /**
     * 主键删除关系
     * @param id 关系Id
     * @return 删除结果
     */
    Boolean deleteRelationById(Long id);

    /**
     * 批量主键批量删除
     * @param ids 关系id列表
     * @return 删除结果
     */
    Boolean batchDelete(List<Long> ids);

    /**
     * 根据信息删除
     * @param typeEnum 关系类型
     * @param sourceType 源类型
     * @param sourceId 源ID
     * @param targetType 目标类型
     * @param targetId 目标ID
     */
    Boolean deleteByInfo(RelationInfoTypeEnum typeEnum, ResourceTypeEnum sourceType, String sourceId, ResourceTypeEnum targetType, String targetId);

    /**
     * 根据类型和源数据查询
     * @param typeEnum 关系类型
     * @param sourceType 源类型
     * @param sourceId 源ID
     * @return 关系数据
     */
    List<RelationInfoDO> queryByTypeAndSourceInfo(RelationInfoTypeEnum typeEnum, ResourceTypeEnum sourceType, String sourceId);

    /**
     * 根据类型和目标数据查询
     * @param typeEnum 关系类型
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 关系数据
     */
    List<RelationInfoDO> queryByTypeAndTargetInfo(RelationInfoTypeEnum typeEnum, ResourceTypeEnum targetType, String targetId);

    /**
     * 主键查询
     * @param id 主键
     * @return 查询结果
     */
    RelationInfoDO queryById(Long id);

}
