package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MomentInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.moment.MomentInfoService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.MomentService;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MomentPushStatusEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.SendMomentRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import com.taobao.unifiedsession.core.commons.limiter.RateLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 朋友圈任务执行器
 *
 * <AUTHOR>
 * @Date 2024/1/24 10:33
 **/
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MomentTaskExecutor extends AbstractTaskExecutor {

    private final MaterialTransferService materialTransferService;
    private final MomentService momentService;
    private final MomentInfoService momentInfoService;

    @Switch(description = "每分钟最大消息触达数", name = "sendMessageCountPerMinute")
    public static Integer sendMessageCountPerMinute = 60;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> dataBodyVOS = todoTaskVO.getData();
        if (CollectionUtils.isEmpty(dataBodyVOS)) {
            return;
        }

        PlatformLogUtil.logInfo("朋友圈任务开始执行", LogListUtil.newArrayList(dataBodyVOS.get(0)));
        // 构建请求
        SendMomentRequest request = buildRequest(context, todoTaskVO);
        PlatformLogUtil.logInfo("发送朋友圈请求开始", LogListUtil.newArrayList(request, todoTaskVO.getTaskId()));
        // 发送朋友圈
        ResultDO<String> resultDO = momentService.sendMoment(request);
        if (!resultDO.getSuccess() || StringUtils.isBlank(resultDO.getModel())) {
            PlatformLogUtil.logFail("朋友圈调用HSF失败", LogListUtil.newArrayList(todoTaskVO.getTaskId(), request, resultDO));
            todoTaskVO.setSuccess(false);
        }
        // 朋友圈留存
        if (!saveMomentInfo(context, todoTaskVO, resultDO.getModel())) {
            PlatformLogUtil.logFail("朋友圈任务发送信息保存失败", LogListUtil.newArrayList(todoTaskVO.getTaskId(), request, resultDO));
            todoTaskVO.setSuccess(false);
        }
        todoTaskVO.setSuccess(true);
    }

    /**
     * 通过类型过滤消息列表
     *
     * @param resourceList 原始消息列表
     * @param typeEnum     过滤条件
     * @return 过滤结果
     */
    private List<MessageBO> getMessageListByType(List<MessageBO> resourceList, MessageTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(resourceList)) {
            return Lists.newArrayList();
        }
        return Optional.of(resourceList
                        .stream()
                        .filter(messageBO -> typeEnum.getCode().equals(messageBO.getMsgType().getCode()))
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    /**
     * 保存朋友圈信息
     *
     * @param todoTaskVO 处理中的任务
     */
    private boolean saveMomentInfo(TaskExecuteContext context, TodoTaskVO todoTaskVO, String jobId) {
        String sendUserIdStr = getSendUserId(context, todoTaskVO);
        if (StringUtils.isBlank(sendUserIdStr)) {
            return false;
        }
        String sendUserId = getFinalTargetId(context, todoTaskVO.getData().get(0));
        MomentInfoDO momentInfoDO = new MomentInfoDO();
        momentInfoDO.setGmtCreate(new Date());
        momentInfoDO.setGmtModified(new Date());
        momentInfoDO.setJobId(StringUtils.isBlank(jobId) ? String.valueOf(context.getTaskId()) : jobId);
        momentInfoDO.setUserId(sendUserId);
        momentInfoDO.setTaskId(context.getTaskId());
        momentInfoDO.setPushStatus(todoTaskVO.getSuccess() ?  MomentPushStatusEnum.NOT_PUBLISH.getCode().byteValue(): MomentPushStatusEnum.NOT_PUBLISH.getCode().byteValue());
        momentInfoDO.setTaskInstanceId(todoTaskVO.getMainTaskInstanceId());
        List<MessageBO> messageBOList = getMessageBOS(context, todoTaskVO);
        if(!CollectionUtils.isEmpty(messageBOList)){
            momentInfoDO.setContent(JSONObject.toJSONString(messageBOList));
        }
        return momentInfoService.insert(momentInfoDO);
    }

    /**
     * 构建朋友圈任务的请求体
     *
     * @param todoTaskVO 处理中的任务数据
     * @return 朋友圈请求体
     */
    private SendMomentRequest buildRequest(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        SendMomentRequest request = new SendMomentRequest();
        String sendUserId = getFinalTargetId(context, todoTaskVO.getData().get(0));
        if (StringUtils.isBlank(sendUserId)) {
            PlatformLogUtil.logFail("构建朋友圈任务的请求体，参数非法", LogListUtil.newArrayList(todoTaskVO));
            throw new TripscrmException(TripSCRMErrorCode.MOMENT_TASK_SEND_USER_ID_IS_EMPTY.getDesc());
        }
        request.setUserId(sendUserId);

        List<MessageBO> messageBOList = getMessageBOS(context, todoTaskVO);

        List<MessageBO> textMessage = messageBOList
                .stream()
                .filter(messageBO -> MessageTypeEnum.TEXT.getCode().equals(messageBO.getMsgType().getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(textMessage)) {
            request.setContent(textMessage.get(0).getMsgContent());
            request.setMomentType(MessageTypeEnum.TEXT.getCode().toString());
        }
        // 图片
        List<MessageBO> imageMessage = getMessageListByType(messageBOList, MessageTypeEnum.IMAGE);
        if(CollectionUtils.isNotEmpty(imageMessage)){
            List<SendMomentRequest.Media> medias = imageMessage.stream().map(messageBO -> {
                SendMomentRequest.Media media = new SendMomentRequest.Media();
                media.setHref(messageBO.getMsgContent());
                media.setVideoTime("0");
                return media;
            }).collect(Collectors.toList());
            request.setMedias(medias);
            request.setMomentType(MessageTypeEnum.IMAGE.getCode().toString());
        }
        // 视频
        List<MessageBO> videoMessage = getMessageListByType(messageBOList, MessageTypeEnum.VIDEO);
        if(CollectionUtils.isNotEmpty(videoMessage)){
            SendMomentRequest.Media media = new SendMomentRequest.Media();
            media.setHref(videoMessage.get(0).getHref());
            media.setVideoTime("0");
            if(videoMessage.get(0).getVoiceTime() != null){
                media.setVideoTime(videoMessage.get(0).getVoiceTime().toString());
            }
            if(videoMessage.get(0).getMsgContent() != null){
                media.setThumbHref(videoMessage.get(0).getMsgContent());
            }
            request.setMedias(Arrays.asList(media));
            request.setMomentType(MessageTypeEnum.VIDEO.getCode().toString());
        }
        // 图文
        List<MessageBO> newsMessageList = getMessageListByType(messageBOList, MessageTypeEnum.NEWS);
        if(CollectionUtils.isNotEmpty(newsMessageList)){
            MessageBO newsMessage = newsMessageList.get(0);
            SendMomentRequest.Link link = new SendMomentRequest.Link();
            link.setHref(newsMessage.getHref());
            link.setTitle(newsMessage.getTitle());
            request.setLink(link);

            SendMomentRequest.Media media = new SendMomentRequest.Media();
            media.setHref(newsMessage.getMsgContent());
            media.setVideoTime("0");
            request.setMedias(Arrays.asList(media));
            request.setMomentType(MessageTypeEnum.NEWS.getCode().toString());
        }
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        return request;
    }

    private List<MessageBO> getMessageBOS(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.PENGYQ);
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(todoTaskVO.getMainTaskInstanceId());
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));

        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        List<MessageBO> messageBOList = materialTransferService.buildMessages(getMaterialInfo(context, todoTaskVO), materialTrackRelationDTO, materialContentConvertContext, context.getTaskInfoDOSnapshot().getType());
        return messageBOList;
    }

    @Override
    public RateLimiter getRateLimiter(TaskExecuteContext context) {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        if (isStreamTask) {
            PlatformLogUtil.logFail("isStreamTask");
            return null;
        }

        double qps = sendMessageCountPerMinute / 60d;
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(qps));
        return RateLimiter.create(sendMessageCountPerMinute / 60d);
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return getFinalTargetId(context, todoTaskVO.getData().get(0));
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_USER_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (!com.taobao.csp.courier.StringUtils.hasText(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("目标Id为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        if (!Objects.equals(taskDataBody.getTargetType(), ActivityTargetTypeEnum.WX_USER_ID.getCode())) {
            PlatformLogUtil.logFail("只能处理WX_USER_ID类型", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        return taskDataBody.getTargetId();
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.SEND_TO_MOMENTS;
    }
}
