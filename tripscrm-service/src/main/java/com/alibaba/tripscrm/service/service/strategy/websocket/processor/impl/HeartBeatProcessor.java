package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;

/**
 * 心跳 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class HeartBeatProcessor implements WsEventProcessor {
    @Resource
    private WebSocketFactory webSocketFactory;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.HEART_BEAT;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        if (wsEvent.getUserId() == null) {
            throw new TripscrmException("缺少必要的参数 userId");
        }
        // 心跳推送
        webSocketFactory.pushMessageBySession(session, wsEvent, false);
    }
}
