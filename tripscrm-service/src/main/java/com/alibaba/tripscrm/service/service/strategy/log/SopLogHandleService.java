package com.alibaba.tripscrm.service.service.strategy.log;

import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.model.domain.log.SopLogContentBO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/4 13:57
 **/
public class SopLogHandleService extends AbstractLogHandleService<SopLogContentBO> {

    @Override
    public LogShowTypeEnum handleType() {
        return null;
    }

    /**
     * 替换日志内容信息
     * @param sopLogContentBO 日志内容对象
     */
    @Override
    public void replaceLogContentInfo(SopLogContentBO sopLogContentBO, ResourceTypeEnum resourceTypeEnum) {
        switch (resourceTypeEnum) {
            case MANAGEMENT_GROUP:
                fillManagementGroupInfo(sopLogContentBO);
                break;
            case MATERIAL:
                fillMaterialInfo(sopLogContentBO);
                break;
            case Tag:
                fillTagInfo(sopLogContentBO);
                break;
            case CHAT_ID:
                fillChatInfo(sopLogContentBO);
                break;
            case MEMBER:
                fillMemberInfo(sopLogContentBO);
                break;
            default:
                break;
        }
    }

    /**
     * 填充管理组信息
     * @param sopLogVO 日志对象
     */
    private void fillManagementGroupInfo(SopLogContentBO sopLogVO) {
        sopLogVO.setSourceValue(queryManagementGroupName(sopLogVO.getSourceValue()));
        sopLogVO.setPresentValue(queryManagementGroupName(sopLogVO.getPresentValue()));
    }

    /**
     * 填充素材信息
     * @param sopLogVO 日志对象
     */
    private void fillMaterialInfo(SopLogContentBO sopLogVO) {
        sopLogVO.setSourceValue(queryMaterialName(sopLogVO.getSourceValue()));
        sopLogVO.setPresentValue(queryMaterialName(sopLogVO.getPresentValue()));
    }

    /**
     * 填充群聊信息
     * @param sopLogVO sopLogVO
     */
    private void fillChatInfo(SopLogContentBO sopLogVO) {
        String sourceTagName = Optional.ofNullable(queryGroupNameList(sopLogVO.getSourceValue())).orElse(new ArrayList<>()).stream().collect(Collectors.joining(","));
        sopLogVO.setSourceValue(sourceTagName);
        String presentTagName = Optional.ofNullable(queryGroupNameList(sopLogVO.getPresentValue())).orElse(new ArrayList<>()).stream().collect(Collectors.joining(","));
        sopLogVO.setPresentValue(presentTagName);
    }

    /**
     * 填充标签信息
     * @param sopLogVO sopLogVO
     */
    private void fillTagInfo(SopLogContentBO sopLogVO) {
        String sourceTagName = Optional.ofNullable(queryTagNameList(sopLogVO.getSourceValue())).orElse(new ArrayList<>()).stream().collect(Collectors.joining(","));
        sopLogVO.setSourceValue(sourceTagName);
        String presentTagName = Optional.ofNullable(queryTagNameList(sopLogVO.getPresentValue())).orElse(new ArrayList<>()).stream().collect(Collectors.joining(","));
        sopLogVO.setPresentValue(presentTagName);
    }

    /**
     * 填充员工信息
     * @param sopLogVO sopLogVO
     */
    private void fillMemberInfo(SopLogContentBO sopLogVO) {
        if (StringUtils.isNotBlank(sopLogVO.getSourceValue())) {
            sopLogVO.setSourceValue(getUserName(sopLogVO.getSourceValue()));
        }
        if (StringUtils.isNotBlank(sopLogVO.getPresentValue())) {
            sopLogVO.setPresentValue(getUserName(sopLogVO.getPresentValue()));
        }
    }

}
