package com.alibaba.tripscrm.service.service.risk;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.dal.repository.WechatUserRepository;
import com.alibaba.tripscrm.service.enums.risk.RiskItemEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTargetEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.model.domain.risk.UserRiskSchema;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 风险控制器抽象类，控制风控对象进入保护和退出保护
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public abstract class RiskController<T> {
    @Resource
    private RuleService ruleService;
    @Resource
    private WechatUserRepository wechatUserRepository;

    /**
     * 判断进入保护
     */
    public abstract Boolean judgeIntoProtect(T param);

    /**
     * 判断退出保护
     */
    public abstract Boolean judgeOutProtect(T param);

    /**
     * 获取风控配置
     *
     * @param corpId corpId
     * @param userId userId
     * @return return
     */
    public UserRiskSchema getRiskSchema(String corpId, String userId) {
        WechatUserDO wechatUser = wechatUserRepository.getByUserIdAndCorpId(userId, corpId);
        // 获取风控配置，先取类型维度再取机器人维度
        RuleDO rule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_RISK.getCode().byteValue(),
                RuleTargetEnum.WECHAT_USER.getCode().byteValue(), corpId + "|" + userId);
        if (rule == null) {
            if (wechatUser.getRobotType() != null) {
                rule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_TYPE_RISK.getCode().byteValue(),
                        RuleTargetEnum.ROBOT_TYPE.getCode().byteValue(), RobotTypeEnum.of(wechatUser.getRobotType().intValue()).getDesc());
                if (rule == null) {
                    return null;
                }
            } else {
                return null;
            }
        }
        return JSONObject.parseObject(rule.getConfig(), UserRiskSchema.class);
    }

    /**
     * 当前风控类目
     *
     * @return
     */
    protected abstract RiskItemEnum riskItem();
}

