package com.alibaba.tripscrm.service.service.impl.wechat.groupmsg;

import com.alibaba.tripscrm.dal.mapper.tddl.WechatGroupMsgMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgParam;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgParam.Criteria;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupMsgQuery;
import com.alibaba.tripscrm.service.service.wechat.groupmsg.WechatGroupMsgService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatGroupMsgServiceImpl implements WechatGroupMsgService {
    private final WechatGroupMsgMapper wechatGroupMsgMapper;

    /**
     * 根据参数统计总数
     *
     * @param query
     */
    @Override
    public long count(WechatGroupMsgQuery query) {
        WechatGroupMsgParam wechatGroupMsgParam = buildParam(query);
        return wechatGroupMsgMapper.countByParam(wechatGroupMsgParam);
    }

    /**
     * 根据参数查询
     *
     * @param query
     */
    @Override
    public WechatGroupMsgDO find(WechatGroupMsgQuery query) {
        List<WechatGroupMsgDO> list = list(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 列表查询
     *
     * @param query
     */
    @Override
    public List<WechatGroupMsgDO> list(WechatGroupMsgQuery query) {
        WechatGroupMsgParam wechatGroupMsgParam = buildParam(query);
        List<WechatGroupMsgDO> list = wechatGroupMsgMapper.selectByParam(wechatGroupMsgParam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list;
    }

    /**
     * 创建
     *
     * @param record
     */
    @Override
    public int insertSelective(WechatGroupMsgDO record) {
        return wechatGroupMsgMapper.insertSelective(record);
    }

    /**
     * 修改
     *
     * @param record
     * @param condition
     */
    @Override
    public int updateSelective(WechatGroupMsgDO record, WechatGroupMsgQuery condition) {
        WechatGroupMsgParam wechatGroupMsgParam = buildParam(condition);
        return wechatGroupMsgMapper.updateByParamSelective(record, wechatGroupMsgParam);
    }

    private WechatGroupMsgParam buildParam(WechatGroupMsgQuery query) {
        WechatGroupMsgParam wechatGroupMsgParam = new WechatGroupMsgParam();
        Criteria criteria = wechatGroupMsgParam.createCriteria();
        if (NumberUtils.biggerThanZero(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (StringUtils.hasText(query.getMsgId())) {
            criteria.andMsgIdEqualTo(query.getMsgId());
        }
        if (Objects.nonNull(query.getChatType())) {
            criteria.andChatTypeEqualTo(query.getChatType().getCode());
        }
        if (StringUtils.hasText(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }
        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        if (Objects.nonNull(query.getCreateType())) {
            criteria.andCreateTypeEqualTo(query.getCreateType());
        }
        if (Objects.nonNull(query.getStartCreateTime())) {
            criteria.andCreateTimeGreaterThanOrEqualTo(query.getStartCreateTime());
        }
        return wechatGroupMsgParam;
    }
}