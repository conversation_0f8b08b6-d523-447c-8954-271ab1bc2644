package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-05 22:59:56
 */
public interface TaskExecuteRecordService {

    /**
     * 生成recordId
     *
     * @return recordId
     */
    Long generateRecordId();

    /**
     * 插入或更新：不存在插入，存在更新
     *
     * @param bo 数据
     * @return 影响行数
     */
    int upsert(TaskExecuteRecordBO bo);

    /**
     * 通过recordId删除
     *
     * @param recordId recordId
     * @return 影响行数
     */
    int deleteByRecordId(Long recordId);

    /**
     * 通过recordId查询
     *
     * @param recordId recordId
     * @return bo
     */
    TaskExecuteRecordBO queryByRecordId(Long recordId);

    /**
     * 根据模板以及实例查询
     *
     * @param query 查询条件，必须包含target_id + target_type + task_id + main_task_instance_id字段
     * @return bo
     */
    List<TaskExecuteRecordBO> queryByTargetAndInstance(TaskExecuteRecordBO query);
}
