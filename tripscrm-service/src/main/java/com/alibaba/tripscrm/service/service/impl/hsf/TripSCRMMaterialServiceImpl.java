package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.response.material.TripSCRMMaterialInfoResponse;
import com.alibaba.tripscrm.domain.request.TripSCRMMaterialInfoQueryRequest;
import com.alibaba.tripscrm.domain.request.material.TripSCRMMaterialUpsertRequest;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMMaterialService;
import com.alibaba.tripscrm.service.model.vo.material.MaterialInfoVO;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.github.pagehelper.PageInfo;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.Objects;

@HSFProvider(serviceInterface = TripSCRMMaterialService.class)
/**
 * Trip SCRM 素材服务 HSF 实现。
 * 提供素材的新增/更新与分页查询能力，依赖底层 {@link MaterialService} 完成数据操作，
 * 并通过 {@link TripSCRMResult} 统一返回调用结果。
 */
public class TripSCRMMaterialServiceImpl implements TripSCRMMaterialService {

    @Resource
    private MaterialService materialService;

    /**
     * 新增或更新素材。
     * 参数校验通过后将请求转换为 DO 并调用持久化服务。
     * 异常场景记录平台日志并返回失败结果。
     *
     * @param request 素材新增或更新请求
     * @return 成功返回素材主键 ID
     */
    @Override
    public TripSCRMResult<Long> upsert(TripSCRMMaterialUpsertRequest request) {
        if (Objects.isNull(request) || !validateUpsertRequest(request)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        try {
            MaterailInfoDO doObj = new MaterailInfoDO();
            BeanUtils.copyProperties(request, doObj);
            Long count = materialService.upsert(doObj);
            return TripSCRMResult.success(count);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        }
    }

    /**
     * 分页查询素材列表。
     * 将查询请求转换为内部 VO，拉取分页数据并封装为通用分页对象。
     * 异常场景记录平台日志并返回失败结果。
     *
     * @param request 查询参数，包含过滤条件与分页信息
     * @return 分页结果，含素材列表与分页元信息
     */
    @Override
    public TripSCRMResult<PageInfoDTO<TripSCRMMaterialInfoResponse>> list(TripSCRMMaterialInfoQueryRequest request) {
        if (Objects.isNull(request) || !NumberUtils.validInteger(request.getPageNum()) || !NumberUtils.validInteger(request.getPageSize())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        try {
            MaterialInfoVO vo = new MaterialInfoVO();
            BeanUtils.copyProperties(request, vo);
            PageInfo<MaterailInfoDO> pageInfo = materialService.list(vo);
            if (Objects.isNull(pageInfo)) {
                return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
            }
            PageInfoDTO<TripSCRMMaterialInfoResponse> pageDTO = PageUtils.getPageInfoDTO(pageInfo, this::toResponse);
            if (Objects.isNull(pageDTO)) {
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }
            return TripSCRMResult.success(pageDTO);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        }
    }

    /**
     * 校验新增/更新请求的必填项。
     *
     * @param request 请求对象
     * @return true 表示校验通过
     */
    private boolean validateUpsertRequest(TripSCRMMaterialUpsertRequest request) {
        return request.getSpaceId() != null
                && request.getName() != null
                && request.getType() != null
                && request.getContent() != null;
    }

    /**
     * 将数据对象转换为对外响应对象。
     *
     * @param d 素材数据对象
     * @return 响应对象；入参为 null 时返回 null
     */
    private TripSCRMMaterialInfoResponse toResponse(MaterailInfoDO d) {
        if (Objects.isNull(d)) {
            return null;
        }
        TripSCRMMaterialInfoResponse resp = new TripSCRMMaterialInfoResponse();
        BeanUtils.copyProperties(d, resp);
        return resp;
    }
}