package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.repository.WechatGroupRepository;
import com.alibaba.tripscrm.domain.*;
import com.alibaba.tripscrm.domain.request.WechatGroupInfoRequest;
import com.alibaba.tripscrm.domain.request.WechatUserInfoRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatReceiveTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.CustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.manager.opensearch.GroupUserOpenSearchManager;
import com.alibaba.tripscrm.service.manager.opensearch.WechatUserOpenSearchManager;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupInfoMemberVO;
import com.alibaba.tripscrm.service.model.domain.query.CustomerInfoQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatInfoQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatUserPageQuery;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetMessageListResponseBody;
import com.alibaba.tripscrm.service.service.wechat.GroupRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.wechat.WechatInfoService;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/1/5 10:25
 */
@Slf4j
@HSFProvider(serviceInterface = WechatInfoService.class)
public class WechatInfoServiceImpl implements WechatInfoService {

    @Resource
    private CustomerRelationOpenSearchManager customerRelationOpenSearchManager;
    @Resource
    private GroupUserOpenSearchManager groupUserOpenSearchManager;
    @Resource
    private WechatUserOpenSearchManager wechatUserOpenSearchManager;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private WechatGroupRepository wechatGroupRepository;
    @Resource
    private GroupRelationService groupRelationService;

    @Override
    @ServiceLog("内部接口-查询全量企微成员Id")
    public TripSCRMResult<List<String>> getAllUserIdList() {
        WechatUserPageQuery query = new WechatUserPageQuery();
        query.setPageSize(500);
        PageInfo<WechatUserDTO> pageInfo = wechatUserOpenSearchManager.pageQuery(query);
        List<String> userIdList = pageInfo.getList().stream().map(WechatUserDTO::getUserId).collect(Collectors.toList());
        return TripSCRMResult.success(userIdList);
    }

    @Override
    @ServiceLog("内部接口-查询指定企微成员信息")
    public TripSCRMResult<WeChatUserInfo> getUserInfo(WechatUserInfoRequest request) {
        if (Objects.isNull(request.getUserId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_ID_EMPTY.getCode(), TripSCRMErrorCode.WECHAT_USER_ID_EMPTY.getDescCn());
        }
        WechatUserDTO wechatUserDTO = wechatUserOpenSearchManager.getByUserId(request.getUserId(), request.getCorpId());
        if (Objects.isNull(wechatUserDTO)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_NOT_EXISTS.getCode(), TripSCRMErrorCode.WECHAT_USER_NOT_EXISTS.getDescCn());
        }
        WeChatUserInfo userInfo = new WeChatUserInfo();
        BeanUtils.copyProperties(wechatUserDTO, userInfo);
        return TripSCRMResult.success(userInfo);
    }

    @Override
    @ServiceLog("内部接口-查询客户数量")
    public TripSCRMResult<Long> getCustomerNum(WechatUserInfoRequest request) {
        if (Objects.isNull(request.getUserId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_ID_EMPTY.getCode(), TripSCRMErrorCode.WECHAT_USER_ID_EMPTY.getDescCn());
        }

        CustomerInfoQuery query = new CustomerInfoQuery();
        BeanUtils.copyProperties(request, query);
        query.setCountFields(Lists.newArrayList("follow_user_id"));
        query.setCorpId(request.getCorpId());
        List<FieldCountInfo> countInfoList = customerRelationOpenSearchManager.getCustomerCount(query);
        if (CollectionUtils.isNotEmpty(countInfoList)) {
            Map<String, Long> mapCount = countInfoList.get(0).getCountMap();
            Long count = mapCount.get(request.getUserId());
            return TripSCRMResult.success(count != null ? count : 0L);
        }

        return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_HAS_NO_CUSTOMER.getCode(), TripSCRMErrorCode.WECHAT_USER_HAS_NO_CUSTOMER.getDescCn());
    }

    @Override
    @ServiceLog("内部接口-获取客户群Id列表")
    public TripSCRMResult<List<String>> getChatIds(WechatGroupInfoRequest request) {
        if (Objects.isNull(request.getUserId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_ID_EMPTY.getCode(), TripSCRMErrorCode.WECHAT_USER_ID_EMPTY.getDescCn());
        }

        WechatInfoQuery query = new WechatInfoQuery();
        query.setUserIdList(Lists.newArrayList(request.getUserId()));
        query.setCorpId(request.getCorpId());
        return TripSCRMResult.success(groupUserOpenSearchManager.getChatIds(query));
    }

    @Override
    @ServiceLog("内部接口-查询客户群信息")
    public TripSCRMResult<WechatGroupDTO> getChatInfo(WechatGroupInfoRequest request) {
        WechatGroupDTO wechatGroupDTO = new WechatGroupDTO();
        if (Objects.isNull(request.getChatId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_CHAT_ID_EMPTY.getCode(), TripSCRMErrorCode.WECHAT_CHAT_ID_EMPTY.getDescCn());
        }

        WechatGroupDO wechatGroupDO = wechatGroupRepository.getByChatIdAndCorpId(request.getChatId(), request.getCorpId());
        if (Objects.nonNull(wechatGroupDO)) {
            List<WechatUserDTO> wechatUserList = wechatUserService.listAll();
            Map<String, WechatUserDTO> wechatUserMap = wechatUserList.stream().collect(Collectors.toMap(WechatUserDTO::getUserId, Function.identity()));
            BeanUtils.copyProperties(wechatGroupDO, wechatGroupDTO);
            if (wechatUserMap.containsKey(wechatGroupDO.getOwnerUser())) {
                WechatUserDTO ownerUser = new WechatUserDTO();
                BeanUtils.copyProperties(wechatUserMap.get(wechatGroupDO.getOwnerUser()), ownerUser);
                wechatGroupDTO.setOwnerUser(ownerUser);
            }
            if (wechatUserMap.containsKey(wechatGroupDO.getAdminUser())) {
                WechatUserDTO adminUser = new WechatUserDTO();
                BeanUtils.copyProperties(wechatUserMap.get(wechatGroupDO.getAdminUser()), adminUser);
                wechatGroupDTO.setAdminUser(adminUser);
            }
            if (Objects.nonNull(request.getNeedMemberList()) && request.getNeedMemberList()) {
                List<GroupUserDTO> groupUserList = new ArrayList<>();
                List<WechatGroupInfoMemberVO> memberVOList = groupRelationService.listByChatId(request.getChatId(), request.getCorpId());
                memberVOList.forEach(t -> {
                    GroupUserDTO groupUserDTO = new GroupUserDTO();
                    BeanUtils.copyProperties(t, groupUserDTO);
                    groupUserList.add(groupUserDTO);
                });
                wechatGroupDTO.setGroupUserList(groupUserList);
            }
            return TripSCRMResult.success(wechatGroupDTO);
        }

        return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_GROUP_NOT_EXISTS.getCode(), TripSCRMErrorCode.WECHAT_GROUP_NOT_EXISTS.getDescCn());
    }

    @Override
    @ServiceLog("内部接口-查询聊天记录")
    public TripSCRMResult<List<WechatMessageDTO>> getChatMessage(WechatGroupInfoRequest request) {
        if (Objects.isNull(request.getUserId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_ID_EMPTY.getCode(), TripSCRMErrorCode.WECHAT_USER_ID_EMPTY.getDescCn());
        }
        if (Objects.isNull(request.getChatId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_CHAT_ID_EMPTY.getCode(), TripSCRMErrorCode.WECHAT_CHAT_ID_EMPTY.getDescCn());
        }

        Integer chatType = FusionChatReceiveTypeEnum.GROUP.getValue();
        List<GetMessageListResponseBody> messageList = chatMessageService.listByChatId(request.getUserId(), request.getChatId(), chatType);
        List<WechatMessageDTO> wechatMessageDTOList = new ArrayList<>();
        for (GetMessageListResponseBody body : messageList) {
            WechatMessageDTO wechatMessageDTO = new WechatMessageDTO();
            BeanUtils.copyProperties(body, wechatMessageDTO);
            wechatMessageDTOList.add(wechatMessageDTO);
        }

        return TripSCRMResult.success(wechatMessageDTOList);
    }

    @Override
    @ServiceLog("内部接口-获取空间下的企微成员信息")
    public TripSCRMResult<List<WeChatUserInfo>> listBySpaceId(Long spaceId) {
        if (!NumberUtils.validLong(spaceId)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS.getCode(), TripSCRMErrorCode.INVALID_PARAMS.getDescCn());
        }

        List<WechatUserDTO> wechatUserList = wechatUserService.listBySpaceId(spaceId);
        List<WeChatUserInfo> validWechatUserList = wechatUserList.stream()
                // 过滤非正常的企微号
                .filter(wechatUserDTO -> Objects.equals(WechatUserStatusEnum.ACTIVE, WechatUserStatusEnum.of(wechatUserDTO.getStatus())))
                .map(wechatUserDTO -> {
                    WeChatUserInfo userInfo = new WeChatUserInfo();
                    BeanUtils.copyProperties(wechatUserDTO, userInfo);
                    return userInfo;
                })
                .collect(Collectors.toList());
        return TripSCRMResult.success(validWechatUserList);
    }
}
