package com.alibaba.tripscrm.service.manager.alipay;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.request.alipay.msg.AliPayDirectMsgRequest;
import com.alibaba.tripscrm.service.model.domain.request.alipay.msg.AliPayGroupMsgRequest;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.alipay.group.AliPayGroupMsgService;
import com.alibaba.tripzoo.proxy.enums.SendStrategyEnum;
import com.alibaba.tripzoo.proxy.model.alipay.group.AssistantMsgCreateResultDTO;

import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.alipay.AlipayGroupMessageCreateRequest;
import com.alibaba.tripzoo.proxy.request.alipay.AlipayMerchantGroupAssistantMsgCreateProxyRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.taobao.csp.courier.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 支付宝消息管理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AliPayMsgManager {

    private final AliPayGroupMsgService aliPayGroupMsgService;

    private final MaterialTransferService materialTransferService;
    /**
     * 异步发送支付宝单聊消息
     * 
     * @param request 请求参数
     * @return 结果
     */
    public TripSCRMResult<String> asyncSendSingleMessage(AliPayDirectMsgRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("支付宝消息管理，异步发送单聊消息，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        
        // 如果没有设置requestId，生成一个
        if (request.getRequestId() == null || request.getRequestId().isEmpty()) {
            request.setRequestId(UUID.randomUUID().toString());
        }
        AlipayMerchantGroupAssistantMsgCreateProxyRequest proxyRequest = convertAlipayMerchantGroupAssistantMsgCreateProxyRequest(request);
        ResultDO<AssistantMsgCreateResultDTO> resultDO = aliPayGroupMsgService.createAssistantMessage(proxyRequest);

        if (Objects.isNull(resultDO) || Objects.isNull(resultDO.getSuccess()) || !resultDO.getSuccess() || Objects.isNull(resultDO.getModel()) || !StringUtils.hasLength(resultDO.getModel().getContentId())) {
            PlatformLogUtil.logFail("支付宝消息管理，异步发送单聊消息，执行失败", LogListUtil.newArrayList(request, resultDO));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
        
        return TripSCRMResult.success(resultDO.getModel().getContentId());
    }

    /**
     * 异步发送支付宝群发消息
     *
     * @param request 请求参数
     * @return 结果
     */
    public TripSCRMResult<String> asyncSendGroupMessage(AliPayGroupMsgRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("支付宝消息管理，异步发送群发消息，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        // 如果没有设置requestId，生成一个
        if (request.getRequestId() == null || request.getRequestId().isEmpty()) {
            request.setRequestId(UUID.randomUUID().toString());
        }
        AlipayGroupMessageCreateRequest proxyRequest = convertAlipayGroupMessageCreateRequest(request);
        ResultDO<AssistantMsgCreateResultDTO> resultDO = aliPayGroupMsgService.createGroupMessage(proxyRequest);
        if (Objects.isNull(resultDO) || Objects.isNull(resultDO.getSuccess()) || !resultDO.getSuccess() || Objects.isNull(resultDO.getModel()) || !StringUtils.hasLength(resultDO.getModel().getContentId())) {
            PlatformLogUtil.logFail("支付宝消息管理，异步发送群发消息，执行失败", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        return TripSCRMResult.success(resultDO.getModel().getContentId());

    }

    /**
     * AliPayDirectMsgRequest转AlipayMerchantGroupAssistantMsgCreateProxyRequest
     *
     * @param request 请求参数
     * @return AlipayMerchantGroupAssistantMsgCreateProxyRequest
     */
    private AlipayMerchantGroupAssistantMsgCreateProxyRequest convertAlipayMerchantGroupAssistantMsgCreateProxyRequest(AliPayDirectMsgRequest request) {
        AlipayMerchantGroupAssistantMsgCreateProxyRequest proxyRequest = new AlipayMerchantGroupAssistantMsgCreateProxyRequest();
        proxyRequest.setCrowdCode(request.getCrowdCode());
        proxyRequest.setCrowdType(request.getCrowdType());
        proxyRequest.setAssistantMsgName(request.getAssistantMsgName());
        proxyRequest.setPublishTime(request.getPublishTime());
        proxyRequest.setGroupIdList(request.getAlipayGroupTemplateIdList());
        List<MessageBO> messageBOS;
        try {
            messageBOS = materialTransferService.buildMessages(request.getMaterailInfoDO(), request.getMaterialTrackRelationDTO(), request.getMaterialContentConvertContext(), request.getTaskType().getCode());
        } catch (Exception e) {
            PlatformLogUtil.logException("支付宝消息管理，AliPayDirectMsgRequest转AlipayMerchantGroupAssistantMsgCreateProxyRequest出错，构建素材内容出错", e.getMessage(), e, LogListUtil.newArrayList(request));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT, e.getMessage());
        }
        proxyRequest.setMessageList(messageBOS);
        proxyRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());

        return proxyRequest;
    }

    /**
     * AliPayGroupMsgRequest转AlipayMerchantGroupAssistantMsgCreateProxyRequest
     *
     * @param request 请求参数
     * @return AlipayMerchantGroupAssistantMsgCreateProxyRequest
     */
    private AlipayGroupMessageCreateRequest convertAlipayGroupMessageCreateRequest(AliPayGroupMsgRequest request) {
        AlipayGroupMessageCreateRequest proxyRequest = new AlipayGroupMessageCreateRequest();
        proxyRequest.setTitle(request.getAssistantMsgName());
        proxyRequest.setPublishTime(request.getPublishTime());
        proxyRequest.setGroupIds(request.getAlipayGroupTemplateIdList());
        proxyRequest.setSenStrategy(SendStrategyEnum.SCHEDULED.getCode());
        proxyRequest.setBizId(request.getRequestId());
        List<MessageBO> messageBOS;
        try {
            messageBOS = materialTransferService.buildMessages(request.getMaterailInfoDO(), request.getMaterialTrackRelationDTO(), request.getMaterialContentConvertContext(), request.getTaskType().getCode());
        } catch (Exception e) {
            PlatformLogUtil.logException("支付宝消息管理，AliPayGroupMsgRequest转AlipayGroupMessageCreateRequest出错，构建素材内容出错", e.getMessage(), e, LogListUtil.newArrayList(request));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT, e.getMessage());
        }
        proxyRequest.setMessageList(messageBOS);
        proxyRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());

        return proxyRequest;
    }
}
