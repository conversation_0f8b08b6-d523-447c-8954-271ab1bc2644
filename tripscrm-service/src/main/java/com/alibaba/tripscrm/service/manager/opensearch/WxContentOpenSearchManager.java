package com.alibaba.tripscrm.service.manager.opensearch;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.alibaba.tripscrm.domain.WxArticleDTO;
import com.alibaba.tripscrm.domain.WxCommunityContentDTO;
import com.alibaba.tripscrm.domain.WxCommunityTagDTO;
import com.alibaba.tripscrm.service.convert.CppConverter;
import com.alibaba.tripscrm.service.model.domain.query.WxCommunityContentQuery;
import com.alibaba.tripscrm.service.model.domain.query.WxQrCodeQuery;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.model.WechatContactMeBO;
import com.alibaba.tripzoo.proxy.model.WechatJoinGroupBO;
import com.aliyun.opensearch.DocumentClient;
import com.aliyun.opensearch.SearcherClient;
import com.aliyun.opensearch.sdk.generated.commons.OpenSearchResult;
import com.aliyun.opensearch.sdk.generated.search.*;
import com.aliyun.opensearch.sdk.generated.search.general.SearchResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.contentplatform.article.base.read.resp.ArticleDTO;
import com.taobao.contentplatform.content.base.element.JsonArrayElement;
import com.taobao.contentplatform.content.base.element.PicElement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-02 16:12:48
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class WxContentOpenSearchManager {
    private final CppConverter cppConverter;

    private final String WX_COMMUNITY_CONTENT_APP_NAME = "wx_community_content";
    private final String WX_COMMUNITY_CONTENT_TABLE_NAME = "cp_biz_fliggywx";

    private final String WX_OFFICIAL_ACCOUNT_ARTICLE_APP_NAME = "wx_official_account_article";
    private final String WECHAT_OFFICIAL_ACCOUNT_ARTICL_TABLE_NAME = "wechat_official_account_articl";

    private final String WECHAT_CONTACT_ME_APP_NAME = "wechat_contact_me";
    private final String WECHAT_CONTACT_ME_TABLE_NAME = "wechat_contact_me";

    private final String WECHAT_JOIN_GROUP_APP_NAME = "wechat_join_group";
    private final String WECHAT_JOIN_GROUP_TABLE_NAME = "wechat_join_group";

    private final String WX_COMMUNITY_TAG_APP_NAME = "wx_community_tag";

    private final List<String> WX_COMMUNITY_CONTENT_FULL_FIELDS = Lists.newArrayList(
            "id",
            "title",
            "fliggywx_summary",
            "gmt_create",
            "gmt_modified",
            "fliggywx_status",
            "status",

            "fliggywx_creator",
            "fliggywx_owner",
            "fliggywx_updater",

            "fliggywx_month_labels",
            "fliggywx_qr_code_activity_id",
            "fliggywx_qr_code_state",
            "fliggywx_qr_code_summary",
            "fliggywx_qr_code_title",
            "fliggywx_qr_code_type",
            "fliggywx_wx_article_id",
            "fliggywx_wx_article_pk_id",
            "fliggywx_wx_article_url",

            "fliggywx_related_items",
            "labels",
            "pics",

            "operate_audit_status",
            "operate_audit_reason",

            "user_avatar",
            "user_id",
            "user_name",

            "operate_security_reason",
            "operate_security_status",
            "fliggywx_env",

            "content_type"
    );

    private final List<String> WX_OFFICIAL_ACCOUNT_ARTICLE_FULL_FIELDS = Lists.newArrayList(
            "id",
            "title",
            "url",
            "article_id",
            "digest",
            "content_source_url",
            "author",
            "gmt_modified"
    );

    private final List<String> WECHAT_CONTACT_ME_FULL_FIELDS = Lists.newArrayList(
            "id",
            "qr_code",
            "config_id",
            "state",
            "corp_id",
            "user",
            "type",
            "gmt_modified"
    );

    private final List<String> WECHAT_JOIN_GROUP_FULL_FIELDS = Lists.newArrayList(
            "id",
            "qr_code",
            "config_id",
            "state",
            "corp_id",
            "chat_id_list",
            "gmt_modified"
    );

    private final List<String> WX_COMMUNITY_TAG_FULL_FIELDS = Lists.newArrayList(
            "id",
            "name"
    );

    private final String COUNT = "count";
    private final String DISTINCT_COUNT = "distinct_count";
    private final String VALUE = "value";
    private final String KEY = "key";
    private final String RESULT = "result";
    private final String ITEMS = "items";
    private final String TOTAL = "total";
    private final String FACET = "facet";
    private final String ERRORS = "errors";

    private final SearcherClient searcherClient;
    private final DocumentClient documentClient;

    /**
     * 查询微信内容信息
     *
     * @param query 查询条件
     * @return PageInfoDTO<CommunityContentDTO>
     */
    public PageInfo<WxCommunityContentDTO> listByPage(WxCommunityContentQuery query) {
        List<WxCommunityContentDTO> res = new ArrayList<>();
        try {
            SearchParams searchParams = buildPageSearchParams(query, WX_COMMUNITY_CONTENT_APP_NAME, WX_COMMUNITY_CONTENT_FULL_FIELDS, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (StringUtils.isNotBlank(query.getTitle())) {
                queryList.add(String.format("title:'%s'", query.getTitle()));
            }
            if (StringUtils.isNotBlank(query.getCreatorId())) {
                queryList.add(String.format("fliggywx_creator:'%s'", query.getCreatorId()));
            }
            if (Objects.nonNull(query.getManagerIds())) {
                queryList.add(String.format("fliggywx_owner:'%s'", query.getManagerIds()));
            }
            if (Objects.nonNull(query.getGmtCreateStart()) && Objects.nonNull(query.getGmtCreateEnd())) {
                queryList.add(String.format("gmt_create:[%s,%s]", query.getGmtCreateStart(), query.getGmtCreateEnd()));
            }
            if (Objects.nonNull(query.getGmtModifiedStart()) && Objects.nonNull(query.getGmtModifiedEnd())) {
                queryList.add(String.format("gmt_modified:[%s,%s]", query.getGmtModifiedStart(), query.getGmtModifiedEnd()));
            }
            if (Objects.isNull(query.getGmtCreateStart()) || Objects.isNull(query.getGmtCreateEnd())) {
                queryList.add("gmt_create:[1709294095000,)");
            }
            if (Objects.nonNull(query.getCppContentId())) {
                queryList.add(String.format("id:'%s'", query.getCppContentId()));
            }
            //搜索词查询
            if (StringUtils.isNotBlank(query.getSearchWord())) {
                queryList.add(String.format("title:'%s'", query.getSearchWord()));
                queryList.add(String.format("fliggywx_summary:'%s'", query.getSearchWord()));
            }
            //标签查询
            if (StringUtils.isNotBlank(query.getSearchWord())) {
                queryList.add(String.format("labels:'%s'", query.getLabel()));
            }

            queryList.add(String.format("fliggywx_env:'%s'", EnvUtils.getEnvironment()));

            // 我的tab
            if (Objects.equals("myself", query.getTabType()) && StringUtils.isNotBlank(query.getCreatorId())) {
                queryList.add(String.format("fliggywx_creator:'%s'", query.getCreatorId()));
            }
            // 测试tab
            if (Objects.equals("test", query.getTabType())) {
                queryList.add("title:'测试'");
            }

            if (CollectionUtils.isNotEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            // 线上tab
            if (Objects.equals("online", query.getTabType())) {
                searchParams.setQuery(String.format("%s ANDNOT %s", searchParams.getQuery(), "title:'测试'"));
            }

            // 过滤条件
            List<String> filterList = new ArrayList<>();
            Integer fliggywxStatus = query.getFliggywxStatus();
            if (Objects.nonNull(fliggywxStatus)) {
                // 审核通过
                if (Objects.equals(fliggywxStatus, 3)) {
                    filterList.add("fliggywx_status='2'");
                    filterList.add("operate_security_status='3'");
                }
                // 审核不通过
                else if (Objects.equals(fliggywxStatus, 4)) {
                    filterList.add("operate_security_status='2'");
                }
                // 审核中
                else if (Objects.equals(fliggywxStatus, 2)) {
                    filterList.add("fliggywx_status='2'");
                    filterList.add("notin(operate_security_status, \"2|3\")");
                }
                // 其他
                else {
                    filterList.add(String.format("fliggywx_status='%d'", query.getFliggywxStatus()));
                }
            }
            filterList.add("status!='-1'");

            if (CollectionUtils.isNotEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("gmt_create", Order.DECREASE));
            searchParams.setSort(sorter);

            return getResultItems(query.getPageNum(), query.getPageSize(), searcherClient.execute(searchParams), cppConverter::convertFromMap);
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询成员信息异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }
        return PageUtils.getPageInfo(res, a -> a);
    }

    /**
     * C端查询微信内容信息
     *
     * @param query 查询条件
     * @return PageInfoDTO<CommunityContentDTO>
     */
    public PageInfo<WxCommunityContentDTO> listByKeyWord(WxCommunityContentQuery query) {
        List<WxCommunityContentDTO> res = new ArrayList<>();
        try {
            SearchParams searchParams = buildPageSearchParams(query, WX_COMMUNITY_CONTENT_APP_NAME, WX_COMMUNITY_CONTENT_FULL_FIELDS, null);

            List<String> queryList = new ArrayList<>();
            //搜索词查询
            if (StringUtils.isNotBlank(query.getSearchWord())) {
                queryList.add(String.format("(title:'%s' OR fliggywx_summary:'%s')", query.getSearchWord(), query.getSearchWord()));
            }
            //标签查询
            List<String> tagIds = query.getTagIds();
            if (CollectionUtils.isNotEmpty(tagIds)) {
                List<String> labelsList = tagIds.stream().map(e -> String.format("labels:'%s'", e)).collect(Collectors.toList());
                String join = Joiner.on(" OR ").join(labelsList);
                if (tagIds.size() > 1) {
                    join = String.format("(%s)", join);
                }
                queryList.add(join);
            }
            // 日期
            if (Objects.nonNull(query.getGmtCreateStart()) && Objects.nonNull(query.getGmtCreateEnd())) {
                queryList.add(String.format("gmt_create:[%s,%s]", query.getGmtCreateStart(), query.getGmtCreateEnd()));
            }
            if (Objects.isNull(query.getGmtCreateStart()) || Objects.isNull(query.getGmtCreateEnd())) {
                queryList.add("gmt_create:[1709294095000,)");
            }

            queryList.add(String.format("fliggywx_env:'%s'", EnvUtils.getEnvironment()));

            if (CollectionUtils.isNotEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            // 过滤条件 fliggywx_status  operate_security_status status
            List<String> filterList = new ArrayList<>();
            // 安全审核通过
            filterList.add("operate_security_status='3'");
            // 公开可见
            filterList.add("status ='0'");
            // 编辑中、下线
            filterList.add("notin(fliggywx_status, \"1|5\")");

            if (CollectionUtils.isNotEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("gmt_create", Order.DECREASE));
            searchParams.setSort(sorter);

            return getResultItems(query.getPageNum(), query.getPageSize(), searcherClient.execute(searchParams), cppConverter::convertFromMap);
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询成员信息异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }

        return PageUtils.getPageInfo(res, a -> a);
    }

    /**
     * 根据内容id查询内容
     *
     * @param query 查询条件
     * @return CommunityContentDTO
     */
    public WxCommunityContentDTO query(WxCommunityContentQuery query) {
        try {
            SearchParams searchParams = buildPageSearchParams(query, WX_COMMUNITY_CONTENT_APP_NAME, WX_COMMUNITY_CONTENT_FULL_FIELDS, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (Objects.nonNull(query.getCppContentId())) {
                queryList.add(String.format("id:'%s'", query.getCppContentId()));
            }
            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            List<WxCommunityContentDTO> resultItems = getResultItems(searcherClient.execute(searchParams), cppConverter::convertFromMap);
            if (CollectionUtils.isEmpty(resultItems)) {
                return null;
            }
            return resultItems.get(0);
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的企微成员好友（行程管家专用）异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }
        return null;
    }

    /**
     * 根据内容id查询内容
     *
     * @param query 查询条件
     * @return CommunityContentDTO
     */
    public WechatJoinGroupBO queryWxJoinGroup(WxQrCodeQuery query) {
        try {
            SearchParams searchParams = buildPageSearchParams(10, WECHAT_JOIN_GROUP_APP_NAME, WECHAT_JOIN_GROUP_FULL_FIELDS, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (Objects.nonNull(query.getState())) {
                queryList.add(String.format("state:'%s'", query.getState()));
            }
            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            List<WechatJoinGroupBO> resultItems = getResultItems(searcherClient.execute(searchParams), map -> {
                WechatJoinGroupBO bo = new WechatJoinGroupBO();
                bo.setState(MapUtils.getString(map, "state"));
                bo.setQrCode(MapUtils.getString(map, "qr_code"));
                bo.setConfigId(MapUtils.getString(map, "config_id"));

                return bo;
            });
            if (CollectionUtils.isEmpty(resultItems)) {
                return null;
            }
            return resultItems.get(0);
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询群活码异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }
        return null;
    }

    /**
     * 根据内容id查询内容
     *
     * @param query 查询条件
     * @return CommunityContentDTO
     */
    public WechatContactMeBO queryWxContactMe(WxQrCodeQuery query) {
        try {
            SearchParams searchParams = buildPageSearchParams(10, WECHAT_CONTACT_ME_APP_NAME, WECHAT_CONTACT_ME_FULL_FIELDS, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (Objects.nonNull(query.getState())) {
                queryList.add(String.format("state:'%s'", query.getState()));
            }
            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            List<WechatContactMeBO> resultItems = getResultItems(searcherClient.execute(searchParams), map -> {
                WechatContactMeBO wechatContactMeBO = new WechatContactMeBO();
                wechatContactMeBO.setState(MapUtils.getString(map, "state"));
                wechatContactMeBO.setQrCode(MapUtils.getString(map, "qr_code"));
                wechatContactMeBO.setType(MapUtils.getInteger(map, "type"));
                wechatContactMeBO.setConfigId(MapUtils.getString(map, "config_id"));

                return wechatContactMeBO;
            });
            if (CollectionUtils.isEmpty(resultItems)) {
                return null;
            }
            return resultItems.get(0);
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询个人活码异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }
        return null;
    }

    /**
     * 根据内容id查询内容
     *
     * @param wxArticlePkIds 查询条件
     * @return CommunityContentDTO
     */
    public List<WxArticleDTO> listWxArticleList(List<Long> wxArticlePkIds) {
        try {
            if (CollectionUtils.isEmpty(wxArticlePkIds)) {
                return null;
            }
            SearchParams searchParams = buildPageSearchParams(wxArticlePkIds.size(), WX_OFFICIAL_ACCOUNT_ARTICLE_APP_NAME, WX_OFFICIAL_ACCOUNT_ARTICLE_FULL_FIELDS, null);

            // 查询条件
            List<String> wxArticlePkIdStrList = wxArticlePkIds.stream().map(e -> String.format("'%s'", e)).collect(Collectors.toList());
            String queryStr = Joiner.on("|").join(wxArticlePkIdStrList);
            searchParams.setQuery(String.format("id:%s", queryStr));

            return getResultItems(searcherClient.execute(searchParams), cppConverter::convertWxArticleFromMap);
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的企微成员好友（行程管家专用）异常", e.getMessage(), e, LogListUtil.newArrayList(wxArticlePkIds));
        }
        return null;
    }

    /**
     * 更新内容文档
     *
     * @param dto 查询条件
     * @return CommunityContentDTO
     */
    public Boolean update(ArticleDTO dto) {
        try {
            Map<String, Object> doc = Maps.newLinkedHashMap();
            doc.put("id", dto.getId());
            doc.put("fliggywx_creator", dto.getFliggywxCreator());

            List<String> fliggywxMonthLabels = dto.getFliggywxMonthLabels();
            if (CollectionUtils.isNotEmpty(fliggywxMonthLabels)) {
                doc.put("fliggywx_month_labels", Joiner.on(";").join(fliggywxMonthLabels));
            }

            doc.put("fliggywx_owner", dto.getFliggywxOwner());
            doc.put("fliggywx_qr_code_activity_id", dto.getFliggywxQrCodeActivityId());
            doc.put("fliggywx_qr_code_state", dto.getFliggywxQrCodeState());
            doc.put("fliggywx_qr_code_summary", dto.getFliggywxQrCodeSummary());
            doc.put("fliggywx_qr_code_title", dto.getFliggywxQrCodeTitle());
            doc.put("fliggywx_qr_code_type", dto.getFliggywxQrCodeType());

            JsonArrayElement fliggywxRelatedItems = dto.getFliggywxRelatedItems();
            if (Objects.nonNull(fliggywxRelatedItems)) {
                JSONArray value = fliggywxRelatedItems.getValue();
                if (CollectionUtils.isNotEmpty(value)) {
                    JSONObject jsonObject = new JSONObject();

                    jsonObject.put("value", value);
                    jsonObject.put("id", 0);
                    jsonObject.put("type", "JsonArray");

                    doc.put("fliggywx_related_items", JSON.toJSONString(jsonObject));
                }
            }

            doc.put("fliggywx_status", dto.getFliggywxStatus());
            doc.put("fliggywx_updater", dto.getFliggywxUpdater());
            doc.put("fliggywx_wx_article_id", dto.getFliggywxWxArticleId());
            doc.put("fliggywx_wx_article_pk_id", dto.getFliggywxWxArticlePkId());
            doc.put("fliggywx_wx_article_url", dto.getFliggywxWxArticleUrl());
            doc.put("gmt_create", dto.getGmtCreate());
            doc.put("gmt_modified", dto.getGmtModified());

            List<String> labels = dto.getLabels();
            if (CollectionUtils.isNotEmpty(labels)) {
                doc.put("labels", Joiner.on(";").join(labels));
            }

            doc.put("operate_security_reason", dto.getOperateSecurityReason());
            doc.put("operate_security_status", dto.getOperateSecurityStatus());
            
            List<PicElement> pics = dto.getPics();
            if (CollectionUtils.isNotEmpty(pics)) {
                List<JSONObject> picList = pics.stream().map(pic -> {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", 0);
                    jsonObject.put("type", "Pic");
                    jsonObject.put("url", pic.getUrl());
                    jsonObject.put("height", pic.getHeight());
                    jsonObject.put("width", pic.getWidth());
                    return jsonObject;
                }).collect(Collectors.toList());
                doc.put("pics", JSON.toJSONString(picList));
            }

            doc.put("status", dto.getStatus());
            doc.put("title", dto.getTitle());
            doc.put("fliggywx_summary", dto.getSummary());
            doc.put("user_avatar", dto.getUserAvatar());
            doc.put("user_id", dto.getUserId());
            doc.put("user_name", dto.getUserName());
            doc.put("fliggywx_env", dto.getFliggywxEnv());
            documentClient.add(doc);

            OpenSearchResult result = documentClient.commit(WX_COMMUNITY_CONTENT_APP_NAME, WX_COMMUNITY_CONTENT_TABLE_NAME);
            if (Objects.equals(result.getResult(), "true")) {
                return true;
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的企微成员好友（行程管家专用）异常", e.getMessage(), e, LogListUtil.newArrayList(dto));
        }
        return false;
    }

    /**
     * 根据内容id查询内容
     *
     * @param query 查询条件
     * @return CommunityContentDTO
     */
    public Boolean delete(WxCommunityContentQuery query) {
        try {

            // 删除文档只需指定需删除文档主键值即可
            Map<String, Object> doc = Maps.newLinkedHashMap();
            doc.put("id", query.getCppContentId());
            documentClient.remove(doc);

            OpenSearchResult result = documentClient.commit(WX_COMMUNITY_CONTENT_APP_NAME, WX_COMMUNITY_CONTENT_TABLE_NAME);
            if (Objects.equals(result.getResult(), "true")) {
                return true;
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的企微成员好友（行程管家专用）异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }
        return false;
    }

    private SearchParams buildPageSearchParams(BasePageRequest query, String appName, List<String> fields, String kvPairs) {
        // 分页条件
        Config config = new Config(Lists.newArrayList(appName));
        config.setStart((query.getPageNum() - 1) * query.getPageSize());
        config.setHits(query.getPageSize());
        config.setSearchFormat(SearchFormat.JSON);
        config.setFetchFields(fields);
        config.setKvpairs(kvPairs);
        return new SearchParams(config);
    }

    private SearchParams buildPageSearchParams(int pageSize, String appName, List<String> fields, String kvPairs) {
        // 分页条件
        Config config = new Config(Lists.newArrayList(appName));
        config.setStart(0);
        config.setHits(pageSize);
        config.setSearchFormat(SearchFormat.JSON);
        config.setFetchFields(fields);
        config.setKvpairs(kvPairs);
        return new SearchParams(config);
    }

    private <R> PageInfo<R> getResultItems(Integer pageNum, Integer pageSize, SearchResult result, Function<JSONObject, R> function) {
        List<R> res = new ArrayList<>();
        JSONObject obj = JSONObject.parseObject(result.getResult());

        JSONArray errors = obj.getJSONArray(ERRORS);
        if (CollectionUtils.isNotEmpty(errors)) {
            throw new RuntimeException(JSON.toJSONString(errors));
        }

        if (!obj.containsKey(RESULT)) {
            return PageUtils.getPageInfo(res, a -> a);
        }

        JSONObject resultJo = obj.getJSONObject(RESULT);
        if (!resultJo.containsKey(ITEMS)) {
            return PageUtils.getPageInfo(res, a -> a);
        }

        JSONArray items = resultJo.getJSONArray(ITEMS);
        for (int i = 0; i < items.size(); i++) {
            res.add(function.apply(items.getJSONObject(i)));
        }

        return PageUtils.getPageInfo(pageNum, pageSize, resultJo.getInteger(TOTAL), res);
    }


    private <R> List<R> getResultItems(SearchResult result, Function<JSONObject, R> function) {
        List<R> res = new ArrayList<>();
        JSONObject obj = JSONObject.parseObject(result.getResult());
        if (!obj.containsKey(RESULT)) {
            return res;
        }
        JSONObject resultJo = obj.getJSONObject(RESULT);
        if (!resultJo.containsKey(ITEMS)) {
            return res;
        }
        JSONArray items = resultJo.getJSONArray(ITEMS);
        for (int i = 0; i < items.size(); i++) {
            res.add(function.apply(items.getJSONObject(i)));
        }
        return res;
    }

    public List<WxCommunityTagDTO> listTagByIds(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        try {
            SearchParams searchParams = buildPageSearchParams(ids.size(), WX_COMMUNITY_TAG_APP_NAME, WX_COMMUNITY_TAG_FULL_FIELDS, null);

            List<String> queryList = new ArrayList<>();
            //标签查询
            List<String> labelsList = ids.stream().map(e -> String.format("id:'%s'", e)).collect(Collectors.toList());
            String join = Joiner.on(" OR ").join(labelsList);
            if (ids.size() > 1) {
                join = String.format("(%s)", join);
            }
            queryList.add(join);

            if (CollectionUtils.isNotEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            List<WxCommunityTagDTO> resultItems = getResultItems(searcherClient.execute(searchParams), cppConverter::convertTagFromMap);
            return resultItems;
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询成员信息异常", e.getMessage(), e, LogListUtil.newArrayList(ids));
        }
        return null;
    }
}
