package com.alibaba.tripscrm.service.model.domain.context;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/3
 */
@Data
@AllArgsConstructor
public class BizDelayContext implements Serializable {
    /**
     * 延迟时间
     */
    Date delayTime;

    /**
     * 真实需要处理的数据
     */
    String delayMessage;

    /**
     * 延迟key，方便在metaq中查找消息
     */
    String delayKey;

    /**
     * 延迟接力次数
     */
    Integer delayNum;
}
