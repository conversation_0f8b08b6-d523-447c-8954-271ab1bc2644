package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 微信成员信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatUserListQuery extends BasePageRequest {
    /**
     * 成员名称/真名
     */
    private String name;

    /**
     * 机器人类型：0=未激活，1=助手号，2=员工号
     */
    private Integer robotType;

    /**
     * 1：比例，10：百业
     */
    private Integer isvType;

    /**
     * 新手任务完成状态，0：未完成，1：已完成
     */
    private Integer activateStatus;

    /**
     * 在线状态，0=在线，1=离线，2=未激活
     */
    private Integer onlineStatus;

    /**
     * 在线状态，0=在线，1=离线，2=未激活
     */
    private List<Integer> statusList = new ArrayList<>();

    /**
     * 成员性别，0=未知，1=男，2=女
     */
    private Integer gender;

    /**
     * 部门 Id
     */
    private Integer departmentId;

    /**
     * 包含所有子部门
     */
    private List<Integer> allDepartmentId;

    /**
     * 业务空间 Id
     */
    @JsonIgnore
    private transient Long spaceId;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 企微成员Id
     */
    private String userId;

    /**
     * 风控模块编码
     */
    private String riskModuleCode;
    /**
     * 使用者工号
     */
    private String manager;

    /**
     * 企微成员列表
     */
    private List<String> userIdList;

    public List<Integer> getStatusList() {
        if (Objects.isNull(onlineStatus) || !CollectionUtils.isEmpty(statusList)) {
            return statusList;
        }

        return Lists.newArrayList(onlineStatus);
    }
}
