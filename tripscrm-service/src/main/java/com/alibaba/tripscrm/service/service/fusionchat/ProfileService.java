package com.alibaba.tripscrm.service.service.fusionchat;

import com.alibaba.tripscrm.domain.result.TripSCRMResult;

import java.util.List;
import java.util.Map;

/**
 * 画像信息服务
 *
 * <AUTHOR>
 */
public interface ProfileService {

    /**
     * 获取用户常驻地信息
     *
     * @param userId 淘系用户id
     * @return Map<String, String>
     */
    Map<String, String> getUserStayCity(String userId);

    /**
     * 获取用户的指定标签的值
     *
     * @param userId    淘系用户id
     * @param labelName 标签名称
     * @return 标签值
     */
    TripSCRMResult<String> getProfileValue(String userId, String labelName);

    /**
     * 获取用户的指定标签的值
     *
     * @param userId        淘系用户id
     * @param labelNameList 标签名称
     * @return 标签值
     */
    TripSCRMResult<Map<String, String>> getProfileValue(String userId, List<String> labelNameList);

    /**
     * 更新诸葛标签
     * @param userId 用户id
     * @param tagKey 标签key
     * @param tagValue tagValue
     */
    TripSCRMResult<Boolean> updateTagValue(String userId, String tagKey, String tagValue);
}
