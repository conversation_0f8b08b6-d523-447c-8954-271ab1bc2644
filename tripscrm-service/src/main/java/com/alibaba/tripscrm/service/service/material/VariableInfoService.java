package com.alibaba.tripscrm.service.service.material;

import com.alibaba.tripscrm.service.model.domain.query.VariableInfoQuery;
import com.alibaba.tripscrm.service.model.dto.material.VariableInfoDTO;

import java.util.List;

public interface VariableInfoService {

    /**
     * 新增变量
     * @param variableInfoDTO 变量对象
     * @return 处理结果
     */
    Boolean add(VariableInfoDTO variableInfoDTO);

    /**
     * 主键删除
     * @param id 主键
     * @return 处理结果
     */
    Boolean deleteById(Long id);

    /**
     * 主键更行
     * @param variableInfoDTO 变量对象
     * @return 处理结果
     */
    Boolean updateById(VariableInfoDTO variableInfoDTO);

    /**
     * 条件查询
     * @param query 查询条件
     * @return 变量列表
     */
    List<VariableInfoDTO> query(VariableInfoQuery query);

    /**
     * 主键查询
     * @param id 主键
     * @return 查询结果
     */
    VariableInfoDTO queryById(Long id);

}
