package com.alibaba.tripscrm.service.service.impl.knowledge;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.tddl.KnowledgeCategoryMapper;
import com.alibaba.tripscrm.dal.model.domain.data.KnowledgeCategoryDO;
import com.alibaba.tripscrm.dal.model.domain.data.KnowledgeCategoryParam;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.CategoryReq;
import com.alibaba.tripscrm.service.model.dto.knowledge.KnowledgeCategoryDTO;
import com.alibaba.tripscrm.service.service.knowledge.KnowledgeCategoryService;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/9/15
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class KnowledgeCategoryServiceImpl implements KnowledgeCategoryService {

    private final KnowledgeCategoryMapper knowledgeCategoryMapper;

    @Override
    public PageInfoDTO<KnowledgeCategoryDTO> pageQuery(CategoryReq query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("pageQuery参数校验失败");
                return null;
            }
            KnowledgeCategoryParam param = buildParam(query);
            if (!Objects.equals(-1, query.getPageSize())) {
                PageHelper.startPage(query.getPageNum(), query.getPageSize());
            }
            List<KnowledgeCategoryDO> knowledgeCategoryDOList = knowledgeCategoryMapper.selectByParam(param);
            if (Objects.isNull(knowledgeCategoryDOList)) {
                return null;
            }
            return PageUtils.getPageInfoDTO(knowledgeCategoryDOList, this::convertDTO);
        } catch (Exception e) {
            PlatformLogUtil.logException("pageQuery执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public KnowledgeCategoryDTO selectById(Long id) {
        if (Objects.isNull(id)) {
            PlatformLogUtil.logFail("selectById参数校验失败");
            return null;
        }
        KnowledgeCategoryDO knowledgeCategoryDO = knowledgeCategoryMapper.selectByPrimaryKey(id);
        if (Objects.isNull(knowledgeCategoryDO)) {
            return null;
        }
        return convertDTO(knowledgeCategoryDO);
    }

    @Override
    public List<KnowledgeCategoryDTO> selectByParam(CategoryReq query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("query参数校验失败");
                return null;
            }
            KnowledgeCategoryParam param = buildParam(query);
            List<KnowledgeCategoryDO> knowledgeCategoryDOList = knowledgeCategoryMapper.selectByParam(param);
            if (CollectionUtils.isEmpty(knowledgeCategoryDOList)) {
                return new ArrayList<>();
            }
            return knowledgeCategoryDOList.stream().map(this::convertDTO).collect(Collectors.toList());
        } catch (Exception e) {
            PlatformLogUtil.logException("query执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public Long update(KnowledgeCategoryDTO knowledgeCategory) {
        if (Objects.isNull(knowledgeCategory)) {
            PlatformLogUtil.logFail("update参数校验失败");
            return 0L;
        }
        KnowledgeCategoryDO knowledgeCategoryDO = new KnowledgeCategoryDO();
        knowledgeCategoryDO.setId(knowledgeCategory.getId());
        knowledgeCategoryDO.setName(knowledgeCategory.getName());
        knowledgeCategoryDO.setDescription(knowledgeCategory.getDescription());
        knowledgeCategoryDO.setLastOperatorId(knowledgeCategory.getLastOperatorId());
        knowledgeCategoryDO.setLastOperatorName(knowledgeCategory.getLastOperatorName());
        knowledgeCategoryDO.setGmtModified(new Date());
        return (long) knowledgeCategoryMapper.updateByPrimaryKeySelective(knowledgeCategoryDO);
    }

    @Override
    public List<KnowledgeCategoryDTO> selectByName(String name, Long spaceId) {
        if (StringUtils.isBlank(name)) {
            PlatformLogUtil.logFail("selectByName参数校验失败");
            return null;
        }

        KnowledgeCategoryParam param = new KnowledgeCategoryParam();
        KnowledgeCategoryParam.Criteria criteria = param.createCriteria();
        criteria.andSpaceIdEqualTo(spaceId);
        criteria.andNameEqualTo(name);
        criteria.andDeletedEqualTo(0);
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());
        List<KnowledgeCategoryDO> knowledgeCategoryDOList = knowledgeCategoryMapper.selectByParam(param);
        if (CollectionUtils.isEmpty(knowledgeCategoryDOList)) {
            return new ArrayList<>();
        }
        return knowledgeCategoryDOList.stream().map(this::convertDTO).collect(Collectors.toList());
    }

    @Override
    public boolean deleteById(Long id, String userId, String userName) {
        if (Objects.isNull(id) || StringUtils.isBlank(userId) || StringUtils.isBlank(userName)) {
            PlatformLogUtil.logFail("参数校验失败");
            return false;
        }

        KnowledgeCategoryDO knowledgeCategoryDO = new KnowledgeCategoryDO();
        knowledgeCategoryDO.setId(id);
        knowledgeCategoryDO.setDeleted(1);
        knowledgeCategoryDO.setLastOperatorId(userId);
        knowledgeCategoryDO.setLastOperatorName(userName);
        knowledgeCategoryDO.setGmtModified(new Date());
        return knowledgeCategoryMapper.updateByPrimaryKeySelective(knowledgeCategoryDO) > 0;
    }

    @Override
    public Long insert(KnowledgeCategoryDTO knowledgeCategory) {
        if (Objects.isNull(knowledgeCategory)) {
            PlatformLogUtil.logFail("insert参数校验失败");
            return 0L;
        }
        KnowledgeCategoryDO knowledgeCategoryDO = new KnowledgeCategoryDO();
        knowledgeCategoryDO.setName(knowledgeCategory.getName());
        knowledgeCategoryDO.setDescription(knowledgeCategory.getDescription());
        knowledgeCategoryDO.setSpaceId(knowledgeCategory.getSpaceId());
        knowledgeCategoryDO.setCreatorId(knowledgeCategory.getCreatorId());
        knowledgeCategoryDO.setCreatorName(knowledgeCategory.getCreatorName());
        knowledgeCategoryDO.setLastOperatorId(knowledgeCategory.getLastOperatorId());
        knowledgeCategoryDO.setLastOperatorName(knowledgeCategory.getLastOperatorName());
        knowledgeCategoryDO.setGmtCreate(new Date());
        knowledgeCategoryDO.setGmtModified(new Date());
        knowledgeCategoryDO.setDeleted(0);
        knowledgeCategoryDO.setEnv(EnvUtils.getEnvironment());
        return (long) knowledgeCategoryMapper.insert(knowledgeCategoryDO);
    }

    private KnowledgeCategoryParam buildParam(CategoryReq query) {
        KnowledgeCategoryParam param = new KnowledgeCategoryParam();
        KnowledgeCategoryParam.Criteria criteria = param.createCriteria();
        if (Objects.nonNull(query.getSpaceId())) {
            criteria.andSpaceIdEqualTo(query.getSpaceId());
        }
        if (CollectionUtils.isNotEmpty(query.getIds())) {
            criteria.andIdIn(query.getIds());
        }
        if (StringUtils.isNotBlank(query.getName())) {
            criteria.andNameLike("%" + query.getName() + "%");
        }
        criteria.andDeletedEqualTo(0);
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());
        param.appendOrderByClause(KnowledgeCategoryParam.OrderCondition.GMTMODIFIED, KnowledgeCategoryParam.SortType.DESC);
        return param;
    }

    private KnowledgeCategoryDTO convertDTO(KnowledgeCategoryDO knowledgeCategoryDO) {
        if (Objects.isNull(knowledgeCategoryDO)) {
            return null;
        }
        KnowledgeCategoryDTO knowledgeCategoryDTO = new KnowledgeCategoryDTO();
        knowledgeCategoryDTO.setId(knowledgeCategoryDO.getId());
        knowledgeCategoryDTO.setName(knowledgeCategoryDO.getName());
        knowledgeCategoryDTO.setDescription(knowledgeCategoryDO.getDescription());
        knowledgeCategoryDTO.setSpaceId(knowledgeCategoryDO.getSpaceId());
        knowledgeCategoryDTO.setCreatorId(knowledgeCategoryDO.getCreatorId());
        knowledgeCategoryDTO.setCreatorName(knowledgeCategoryDO.getCreatorName());
        knowledgeCategoryDTO.setLastOperatorId(knowledgeCategoryDO.getLastOperatorId());
        knowledgeCategoryDTO.setLastOperatorName(knowledgeCategoryDO.getLastOperatorName());
        knowledgeCategoryDTO.setGmtCreate(knowledgeCategoryDO.getGmtCreate());
        knowledgeCategoryDTO.setGmtModified(knowledgeCategoryDO.getGmtModified());
        knowledgeCategoryDTO.setEnv(knowledgeCategoryDO.getEnv());
        knowledgeCategoryDTO.setDeleted(knowledgeCategoryDO.getDeleted());
        return knowledgeCategoryDTO;
    }
}
