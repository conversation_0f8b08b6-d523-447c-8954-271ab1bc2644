package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.service.impl.seller.SellerCorpValidPlatformWechatUserServiceImpl;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/11 16:49
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerCorpValidPlatformWechatUserDataSyncProcessor extends JavaProcessor {
    private final SellerCorpValidPlatformWechatUserServiceImpl sellerCorpValidPlatformWechatUserService;
    private final WechatUserService wechatUserService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.sellerCorpId);
            WechatUserQuery query = new WechatUserQuery();
            query.setCorpId(SwitchConfig.sellerCorpId);
            query.setSpaceId(SwitchConfig.sellerCorpPlatformSpaceId);
            query.setStatus(WechatUserStatusEnum.ACTIVE.getCode().byteValue());
            List<WechatUserDTO> wechatUserList = wechatUserService.listByCondition(query);

            if (CollectionUtils.isEmpty(wechatUserList)) {
                PlatformLogUtil.logFail("定时任务同步商家企业用户运营组可用企微号信息，企微号列表为空", LogListUtil.newArrayList());
                return new ProcessResult(true);
            }

            int validCount = 0;
            for (WechatUserDTO wechatUserDTO : wechatUserList) {
                validCount += sellerCorpValidPlatformWechatUserService.sync(wechatUserDTO.getUserId());
            }

            if (validCount < SwitchConfig.sellerCorpValidPlatformUserCountForAlarm) {
                PlatformLogUtil.logFail("同步商家企业用户运营组可用企微号信息，企微成员不可用", LogListUtil.newArrayList());
            }
            PlatformLogUtil.logInfo("定时任务同步商家企业用户运营组可用企微号信息，同步完毕", LogListUtil.newArrayList(validCount));
            return new ProcessResult(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("定时任务同步商家企业用户运营组可用企微号信息，同步完毕", e.getMessage(), e, LogListUtil.newArrayList());
            return new ProcessResult(false);
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }
}
