package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.service.manager.wechat.WechatGroupAutoOpenWorkManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 企微客户群自动关注额度检查
 *
 * <AUTHOR>
 * @since 2024/5/20 15:17
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatGroupAutoOpenWorkQuotaCheckProcessor extends JavaProcessor {
    private final WechatGroupAutoOpenWorkManager wechatGroupAutoOpenWorkManager;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            List<String> wechatUserClockInCorpDepartmentIdList = SwitchConfig.wechatUserClockInCorpDepartmentIdList;
            for (String corpDepartmentId : wechatUserClockInCorpDepartmentIdList) {
                String[] splits = corpDepartmentId.split("_");
                wechatGroupAutoOpenWorkManager.checkQuota(splits[0], Integer.parseInt(splits[1]));
            }
            return new ProcessResult(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("企微客户群自动关注额度检查定时任务执行失败", e.getMessage(), e, LogListUtil.newArrayList());
            return new ProcessResult(false);
        }
    }
}
