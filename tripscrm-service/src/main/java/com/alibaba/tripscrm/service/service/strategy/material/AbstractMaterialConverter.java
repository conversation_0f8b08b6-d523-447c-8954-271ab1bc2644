package com.alibaba.tripscrm.service.service.strategy.material;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.gear.env.EnvUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialParamKeyEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.response.MaterialContentConvertResult;
import com.alibaba.tripscrm.service.model.dto.ItemInfoDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialContentDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.material.ItemVO;
import com.alibaba.tripscrm.service.service.material.MaterialContentConvertService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public abstract class AbstractMaterialConverter<T extends MaterialContentDTO> {

    @Resource
    private MaterialService materialService;

    @Resource
    private MaterialContentConvertService materialContentConvertService;

    public static final Set<String> ALIPAY_SCENETYPE_SET = new HashSet<>(Arrays.asList(MaterialSendSceneTypeConstant.ALIPAY_DIRECT_MSG, MaterialSendSceneTypeConstant.ALIPAY_GROUP_MSG));


    /**
     * 构建消息对象
     *
     * @param content                  素材内容
     * @param materialTrackRelationDTO 埋点对象
     * @param context                  素材处理上下文
     * @return 消息对象
     */
    public List<MessageBO> buildMessageBO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        return buildMessageBO(getMaterialContentDTO(content, materialTrackRelationDTO, context), context, materialTrackRelationDTO.getSceneType());
    }

    /**
     * 构建消息对象
     *
     * @param content                  素材内容
     * @param materialTrackRelationDTO 埋点对象
     * @param context                  素材处理上下文
     * @param sendMessage              是否为发送消息场景
     * @return 消息对象
     */
    public List<WxMessageBO> buildWxMessageBO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        return buildWxMessageBO(getMaterialContentDTO(content, materialTrackRelationDTO, context), context, sendMessage);
    }

    /**
     * 获取素材内容对象
     *
     * @param content                  素材内容
     * @param materialTrackRelationDTO 埋点信息
     * @param context                  素材转化上下文
     * @return 素材内容对象
     */
    public T getMaterialContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        // 动参替换
        context.setOriginContent(content);
        MaterialContentConvertResult convert = materialContentConvertService.convert(context);
        JSONObject jsonObject = JSONObject.parseObject(convert.getContent());
        jsonObject.put("atAll", convert.getAtAll());
        // 内容组装
        return getContentDTO(jsonObject.toString(), materialTrackRelationDTO, context);
    }

    /**
     * 获取素材内容DTO
     *
     * @param content                  素材内容
     * @param materialTrackRelationDTO 埋点对象
     * @return 素材内容DTO
     */
    abstract T getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context);

    /**
     * 获取素材供给列表
     */
    public abstract List<MaterialSupplyDTO> getMaterialSupplyList(String content);

    /**
     * 构建消息对象
     *
     * @param materialDTO 素材
     * @return 消息对象
     */
    abstract List<MessageBO> buildMessageBO(T materialDTO, MaterialContentConvertContext context, String sceneType);

    /**
     * 构建消息对象
     *
     * @param materialDTO 素材
     * @param sendMessage 是否是发送消息的场景
     * @return 消息对象
     */
    abstract List<WxMessageBO> buildWxMessageBO(T materialDTO, MaterialContentConvertContext context, Boolean sendMessage);

    /**
     * 内容类型
     *
     * @return 内容类型枚举
     */
    public abstract MaterialContentTypeEnum contentType();

    /**
     * 构建跳转链接
     *
     * @param materialSupplyDTO 供给
     * @param sceneType
     * @return 跳转链接
     */
    protected String buildPathUrl(MaterialSupplyDTO materialSupplyDTO, Map<String, String> paramMap, String pagePath, String scrmTrackId, String sceneType) {
        if (Objects.isNull(materialSupplyDTO) || StringUtils.isBlank(materialSupplyDTO.getSupplyType())) {
            PlatformLogUtil.logFail("构造供给链接失败，供给类型入参为空", LogListUtil.newArrayList(materialSupplyDTO, pagePath, paramMap));
            return pagePath;
        }
        MaterialSupplyTypeEnum supplyTypeEnum = MaterialSupplyTypeEnum.getSupplyType(materialSupplyDTO.getSupplyType());
        if (supplyTypeEnum == null) {
            PlatformLogUtil.logFail("构造供给链接失败，供给类型不存在", LogListUtil.newArrayList(materialSupplyDTO, pagePath, paramMap));
            return pagePath;
        }
        //支付宝url转换
        if (StringUtils.isNotBlank(sceneType) && ALIPAY_SCENETYPE_SET.contains(sceneType)) {
            return buildAlipayPathUrl(materialSupplyDTO, paramMap, pagePath);
        }

        String pathUrl = pagePath;
        switch (supplyTypeEnum) {
            case PAGE_MAKER:
                pathUrl = getPageMakerLink(materialSupplyDTO.getSupplyId());
                break;
            case COMMODITY:
                pathUrl = MaterialConstant.GOODS_MINI_PROGRAM_URL_PRE + materialSupplyDTO.getSupplyId();
                break;
            case SHOP:
                pathUrl = MaterialConstant.HOTEL_H5_PRE + materialSupplyDTO.getSupplyId();
                break;
            case DMESTIC_FLIGHT_LISTING:
                pathUrl = getDemsticFlightListingUrl(paramMap);
                break;
            case TRAIN_TICKET_LISTING:
                pathUrl = getTrainTicketListingUrl(paramMap);
                break;
            case NON_PACKAGE_HOTEL_LISTING:
                pathUrl = getNonPackageHotelListingUrl(paramMap);
                break;
            case CHANNEL_LIVE_VIDEO:
                pathUrl = MaterialConstant.CHANNEL_LIVE_URL_PRE;
                break;
            case CHANNEL_SHORT_VIDEO:
                pathUrl = MaterialConstant.CHANNEL_VIDEO_URL_PRE;
                break;
            case INTERNATIONAL_FLIGHT_LISTING:
                pathUrl = getInternationalTicketListingUrl(paramMap);
                break;
            case VACATION_SCENIC_TICKET_LISTING:
                pathUrl = getVacationScenicTicketListingUrl(paramMap);
                break;
            case WECHAT_CASH_RED_PACKET_ACTIVITY:
                pathUrl = String.format(SwitchConfig.wechatCashRedPacketActivityLandingPageUrl, materialSupplyDTO.getSupplyId(), scrmTrackId);
                break;
            case VACATION_SEARCH_LISTING:
                pathUrl = getVacationSearchListingUrl(paramMap);
                break;
            default:
                break;
        }
        return pathUrl;
    }

    /**
     * 构建支付宝消息跳转链接
     *
     * @param materialSupplyDTO 供给
     * @return 跳转链接
     */
    protected String buildAlipayPathUrl(MaterialSupplyDTO materialSupplyDTO, Map<String, String> paramMap, String pagePath) {
        if (Objects.isNull(materialSupplyDTO) || StringUtils.isBlank(materialSupplyDTO.getSupplyType())) {
            PlatformLogUtil.logFail("构造供给链接失败，供给类型入参为空", LogListUtil.newArrayList(materialSupplyDTO, pagePath, paramMap));
            return pagePath;
        }
        MaterialSupplyTypeEnum supplyTypeEnum = MaterialSupplyTypeEnum.getSupplyType(materialSupplyDTO.getSupplyType());
        if (supplyTypeEnum == null) {
            PlatformLogUtil.logFail("构造供给链接失败，供给类型不存在", LogListUtil.newArrayList(materialSupplyDTO, pagePath, paramMap));
            return pagePath;
        }
        String pathUrl = pagePath;
        switch (supplyTypeEnum) {
            case PAGE_MAKER:
                pathUrl = getPageMakerLink(materialSupplyDTO.getSupplyId());
                break;
            case COMMODITY:
                pathUrl = MaterialConstant.GOODS_PC_URL + materialSupplyDTO.getSupplyId();
                break;
            case SHOP:
                pathUrl = MaterialConstant.HOTEL_H5_PRE + materialSupplyDTO.getSupplyId();
                break;
            default:
                break;
        }
        return pathUrl;
    }


    protected String getDemsticFlightListingUrl(Map<String, String> paramMap) {
        if (CollectionUtils.isEmpty(paramMap)) {
            PlatformLogUtil.logFail("构造国内机票Listing页失败，参数为空", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        if (!paramMap.containsKey(MaterialParamKeyEnum.FLIGHT_LEAVE_DATE_FIELD.getCode())
                || !paramMap.containsKey(MaterialParamKeyEnum.FLIGHT_DEP_CITY_CODE_FIELD.getCode())
                || !paramMap.containsKey(MaterialParamKeyEnum.FLIGHT_ARR_CITY_CODE_FIELD.getCode())) {
            PlatformLogUtil.logFail("构造国内机票Listing页失败，参数缺失", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        return MaterialUtils.concatHttpUrlParam(getDomesticFlightListingUrlByEnv(), paramMap);
    }

    protected String getTrainTicketListingUrl(Map<String, String> paramMap) {
        if (CollectionUtils.isEmpty(paramMap)) {
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        if (!paramMap.containsKey(MaterialParamKeyEnum.TRAIN_TICKET_DEP_DATE_FIELD.getCode()) || !paramMap.containsKey(MaterialParamKeyEnum.TRAIN_TICKET_DEP_LOCATION_FIELD.getCode()) || !paramMap.containsKey(MaterialParamKeyEnum.TRAIN_TICKET_ARR_LOCATION_FIELD.getCode())) {
            PlatformLogUtil.logFail("构造火车票Listing页失败，参数缺失", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        return MaterialUtils.concatHttpUrlParam(MaterialConstant.TRAIN_TICKET_LISTING_H5_PRE, paramMap);
    }

    public String getNonPackageHotelListingUrl(Map<String, String> paramMap) {
        if (CollectionUtils.isEmpty(paramMap)) {
            PlatformLogUtil.logFail("构造国内酒店Listing页失败，参数为空", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        if (!paramMap.containsKey(MaterialParamKeyEnum.NON_PACKAGE_HOTEL_CITY_CODE_FIELD.getCode()) || !paramMap.containsKey(MaterialParamKeyEnum.NON_PACKAGE_HOTEL_CHECK_IN_FIELD.getCode()) || !paramMap.containsKey(MaterialParamKeyEnum.NON_PACKAGE_HOTEL_CHECK_OUT_FIELD.getCode())) {
            PlatformLogUtil.logFail("构造国内酒店Listing页失败，参数缺失", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        return MaterialUtils.concatHttpUrlParam(getNonPackageHotelUrlByEnv(), paramMap);
    }

    protected String getInternationalTicketListingUrl(Map<String, String> paramMap) {
        if (CollectionUtils.isEmpty(paramMap)) {
            PlatformLogUtil.logFail("构造国际机票Listing页失败，参数为空");
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }
        if (!paramMap.containsKey(MaterialParamKeyEnum.FLIGHT_LEAVE_DATE_FIELD.getCode())
                || !paramMap.containsKey(MaterialParamKeyEnum.FLIGHT_DEP_CITY_CODE_FIELD.getCode())
                || !paramMap.containsKey(MaterialParamKeyEnum.FLIGHT_ARR_CITY_CODE_FIELD.getCode())) {
            PlatformLogUtil.logFail("构造国际机票Listing页失败，参数缺失", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }
        return MaterialUtils.concatHttpUrlParam(getInternationalFlightListingUrlByEnv(), paramMap);
    }

    protected String getVacationScenicTicketListingUrl(Map<String, String> paramMap) {
        if (CollectionUtils.isEmpty(paramMap)) {
            PlatformLogUtil.logFail("构造度假-门票Listing页失败，参数为空", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        if (!paramMap.containsKey(MaterialParamKeyEnum.VACATION_ARR_CITY_NAME.getCode())) {
            PlatformLogUtil.logFail("构造度假-门票Listing页失败，参数缺失", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        if (StringUtils.equals(SwitchConfig.SEARCH_NOT_PAY_VACATION_DEFAULT_KEYWORD, paramMap.get(MaterialParamKeyEnum.VACATION_ARR_CITY_NAME.getCode()))) {
            PlatformLogUtil.logInfo("构造度假-门票Listing页，目的地缺失，替换为默认值", paramMap);
            return MaterialConstant.VACATION_DEFAULT_URL;
        }
        Map<String, String> urlParamMap = new HashMap<>();
        urlParamMap.put(MaterialParamKeyEnum.VACATION_KEYWORD.getCode(), paramMap.get(MaterialParamKeyEnum.VACATION_ARR_CITY_NAME.getCode()));
        return MaterialUtils.concatHttpUrlParam(MaterialConstant.VACATION_SCENIC_TICKET_LISTING_H5_PRE, urlParamMap);
    }

    protected String getVacationSearchListingUrl(Map<String, String> paramMap) {
        if (CollectionUtils.isEmpty(paramMap)) {
            PlatformLogUtil.logFail("构造度假-度假小搜Listing页失败，参数为空");
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }
        if (!paramMap.containsKey(MaterialParamKeyEnum.VACATION_ARR_CITY_NAME.getCode())) {
            PlatformLogUtil.logFail("构造度假-度假小搜Listing页失败，参数缺失");
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        if (StringUtils.equals(SwitchConfig.SEARCH_NOT_PAY_VACATION_DEFAULT_KEYWORD, paramMap.get(MaterialParamKeyEnum.VACATION_ARR_CITY_NAME.getCode()))) {
            PlatformLogUtil.logInfo("构造度假-度假小搜Listing页，目的地缺失，替换为默认值", paramMap);
            return MaterialConstant.VACATION_DEFAULT_URL;
        }
        Map<String, String> urlParamMap = new HashMap<>();
        urlParamMap.put(MaterialParamKeyEnum.VACATION_KEYWORD.getCode(), paramMap.get(MaterialParamKeyEnum.VACATION_ARR_CITY_NAME.getCode()));
        return MaterialUtils.concatHttpUrlParam(MaterialConstant.VACATION_SEARCH_TICKET_LISTING_H5_PRE, urlParamMap);
    }

    /**
     * 获取页匠页跳转链接
     *
     * @param supplyId 供给id
     * @return 跳转链接
     */
    protected String getPageMakerLink(String supplyId) {
        if (StringUtils.isBlank(supplyId) || !StringUtils.isNumeric(supplyId)) {
            return null;
        }
        ItemInfoDTO itemInfoDTO = new ItemInfoDTO();
        itemInfoDTO.setItemId(supplyId);
        itemInfoDTO.setItemType(ItemTypeEnum.PAGE_MAKER.getItemType());
        ItemVO pathMaker = materialService.queryItemById(itemInfoDTO);
        if (pathMaker == null) {
            return null;
        }
        return pathMaker.getUrl();
    }

    private String getNonPackageHotelUrlByEnv() {
        if (EnvUtil.isPre()) {
            return MaterialConstant.NON_PACKAGE_HOTEL_LISTING_H5_PRE;
        } else {
            return MaterialConstant.NON_PACKAGE_HOTEL_LISTING_H5;
        }
    }

    private String getInternationalFlightListingUrlByEnv() {
        if (EnvUtil.isPre()) {
            return MaterialConstant.INTERNATIONAL_FLIGHT_LISTING_H5_PRE;
        } else {
            return MaterialConstant.INTERNATIONAL_FLIGHT_LISTING_H5;
        }
    }

    private String getDomesticFlightListingUrlByEnv() {
        if (EnvUtil.isPre()) {
            return MaterialConstant.DMESTIC_FLIGHT_LISTING_H5_PRE;
        } else {
            return MaterialConstant.DMESTIC_FLIGHT_LISTING_H5;
        }
    }

    /**
     *
     * @param content
     * @param paramType
     * @return
     */
    public abstract String buildSubscribeMsgTemplateContent(String content, String paramType);

}
