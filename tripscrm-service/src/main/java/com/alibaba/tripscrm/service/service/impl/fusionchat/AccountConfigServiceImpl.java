package com.alibaba.tripscrm.service.service.impl.fusionchat;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.AccountConfigDO;
import com.alibaba.tripscrm.dal.repository.AccountConfigRepository;
import com.alibaba.tripscrm.service.model.domain.fusionchat.AccountFusionChatConfigBody;
import com.alibaba.tripscrm.service.service.fusionchat.AccountConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 平台账号配置 Manager实现类
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j
public class AccountConfigServiceImpl implements AccountConfigService {
    @Resource
    private AccountConfigRepository accountConfigRepository;

    @Override
    public void saveFusionChatConfig(String accountId, String userId, Boolean top) {
        AccountConfigDO accountConfig = accountConfigRepository.getByAccountId(accountId);
        String fusionChatConfig = accountConfig.getFusionChatConfig();
        Map<String, AccountFusionChatConfigBody> fusionChatMap = new HashMap<>(8);
        if(fusionChatConfig!=null){
            fusionChatMap = JSONObject.parseObject(fusionChatConfig, new TypeReference<Map<String, AccountFusionChatConfigBody>>() {});
        }
        AccountFusionChatConfigBody accountFusionChatConfigBody = fusionChatMap.get(userId);
        if(accountFusionChatConfigBody==null){
            accountFusionChatConfigBody = new AccountFusionChatConfigBody();
        }
        accountFusionChatConfigBody.setUserId(userId);
        accountFusionChatConfigBody.setTopNo(top?1:0);
        fusionChatMap.put(userId, accountFusionChatConfigBody);
        accountConfig.setFusionChatConfig(JSONObject.toJSONString(fusionChatMap));
        accountConfigRepository.update(accountConfig);
    }

    @Override
    public Map<String, AccountFusionChatConfigBody> queryFusionChatConfig(String accountId) {
        AccountConfigDO accountConfig = accountConfigRepository.getByAccountId(accountId);
        String fusionChatConfig = accountConfig.getFusionChatConfig();
        if(fusionChatConfig==null){
            return Collections.emptyMap();
        }
        return JSONObject.parseObject(fusionChatConfig, new TypeReference<Map<String, AccountFusionChatConfigBody>>() {});
    }
}