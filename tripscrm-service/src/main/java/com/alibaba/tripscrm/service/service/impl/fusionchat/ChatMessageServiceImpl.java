package com.alibaba.tripscrm.service.service.impl.fusionchat;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ChatMessageParam;
import com.alibaba.tripscrm.dal.model.domain.data.WechatCustomerDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.dal.model.lindorm.ChatMessageDO;
import com.alibaba.tripscrm.dal.repository.ChatMessageRepository;
import com.alibaba.tripscrm.dal.repository.WechatCustomerRepository;
import com.alibaba.tripscrm.dal.repository.WechatUserRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatSenderTypeEnum;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageCreateParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageUpdateParam;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetMessageListResponseBody;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.BeanCopyUtils;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会话消息 Manager实现类
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class ChatMessageServiceImpl implements ChatMessageService {
    @Resource
    private ChatMessageRepository chatMessageRepository;
    @Resource
    private WechatCustomerService wechatCustomerService;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private WechatCustomerRepository wechatCustomerRepository;
    @Resource
    private WechatUserRepository wechatUserRepository;
    @Resource
    private AccountService accountService;

    @Override
    public String createChatMessage(ChatMessageCreateParam param) {
        ChatMessageDO create = BeanCopyUtils.copy(param, ChatMessageCreateParam.class, ChatMessageDO.class);
        JSONObject extInfo = new JSONObject();
        if (StringUtils.hasLength(param.getLockUser())) {
            extInfo.put("lockUser", param.getLockUser());
        }
        if (StringUtils.hasLength(param.getLockUserName())) {
            extInfo.put("lockUserName", param.getLockUserName());
        }
        if (StringUtils.hasLength(param.getRequestId())) {
            extInfo.put("requestId", param.getRequestId());
        }
        create.setExtInfo(extInfo.toJSONString());
        chatMessageRepository.create(create);
        return create.getMessageId();
    }

    @Override
    public String updateChatMessage(ChatMessageUpdateParam param) {
        ChatMessageDO update = chatMessageRepository.getById(param.getReceiveId(), param.getTimestamp(), param.getMessageId());
        update.setStatus(param.getStatus());
        chatMessageRepository.update(update);
        return param.getMessageId();
    }

    @Override
    public String chatId2ReceiveId(String userId, String chatId, Integer chatType) {
        String receiveId = null;
        switch (ChatTypeEnum.parse(chatType)) {
            case GROUP:
                receiveId = chatId;
                break;
            case SINGLE_FOR_CUSTOMER:
                // 构建单聊的会话id，与客户的单聊，固定会话id为userId#externalUserId
                receiveId = String.format("%s#%s", userId, chatId);
                break;
            case SINGLE_FOR_USER:
                // 构建单聊的会话id，与成员的单聊，固定会话id为userId1#userId2（排序）
                List<String> userIds = Stream.of(userId, chatId).sorted().collect(Collectors.toList());
                receiveId = String.format("%s#%s", userIds.get(0), userIds.get(1));
                break;
            default:
                break;
        }
        // 为了达到散列的目的，这里使用md5(receiveId)取后4位拼接在receiveId前面，后4位随机性更大一点
        String receiveIdMd5 = DigestUtils.md5DigestAsHex(receiveId.getBytes());
        return receiveIdMd5.substring(receiveIdMd5.length() - 4) + receiveId;
    }

    @Override
    public List<GetMessageListResponseBody> listByChatId(String userId, String chatId, Integer chatType) {
        String receiveId = chatId2ReceiveId(userId, chatId, chatType);
        // 获取消息列表
        List<ChatMessageDO> chatMessages = chatMessageRepository.listByReceiveId(receiveId, ChatTypeEnum.parse(chatType).getReceiveType().getValue());
        // 获取发送人列表
        return getGetMessageListResponseBodies(chatMessages);
    }

    @Override
    public FusionChatUserBody getUserInfo(String senderId, Integer senderType) {
        FusionChatUserBody fusionChatUserBody = new FusionChatUserBody();
        String corpId = SpaceInfoThreadLocalUtils.getCorpId();
        switch (FusionChatSenderTypeEnum.parse(senderType)) {
            case USER:
                WechatUserDO wechatUser = wechatUserRepository.getByUserIdAndCorpId(senderId, corpId);
                if (wechatUser != null) {
                    fusionChatUserBody.setUserName(wechatUser.getName());
                    fusionChatUserBody.setUserAvatar(wechatUser.getAvatar());
                }
                break;
            case CUSTOMER:
                WechatCustomerDO wechatCustomer = wechatCustomerRepository.getByExternalUserIdAndCorpId(senderId, corpId);
                if (wechatCustomer != null) {
                    fusionChatUserBody.setUserName(wechatCustomer.getName());
                    fusionChatUserBody.setUserAvatar(wechatCustomer.getAvatar());
                }
                break;
            default:
                break;
        }
        return fusionChatUserBody;
    }

    @Override
    public List<GetMessageListResponseBody> listByParam(ChatMessageQueryRequest query) {
        String receiveId = chatId2ReceiveId(query.getSenderId(), query.getChatId(), query.getChatType());
        // 获取消息列表
        ChatMessageParam param = BeanCopyUtils.copy(query, ChatMessageQueryRequest.class, ChatMessageParam.class);
        param.setReceiveId(receiveId);
        param.setReceiveType(ChatTypeEnum.parse(query.getChatType()).getReceiveType().getValue());
        List<ChatMessageDO> chatMessages = chatMessageRepository.listByParam(param);
        // 获取发送人列表
        return getGetMessageListResponseBodies(chatMessages);
    }

    private List<GetMessageListResponseBody> getGetMessageListResponseBodies(List<ChatMessageDO> chatMessages) {
        Set<String> userSenderIds = chatMessages.stream().filter(x -> x.getSenderType().equals(FusionChatSenderTypeEnum.USER.getValue())).map(ChatMessageDO::getSenderId).collect(Collectors.toSet());
        List<String> customerSenderIds = chatMessages.stream().filter(x -> x.getSenderType().equals(FusionChatSenderTypeEnum.CUSTOMER.getValue())).map(ChatMessageDO::getSenderId).collect(Collectors.toList());
        List<WechatCustomerVO> customers = wechatCustomerService.listByExternalUserIdList(customerSenderIds);
        Map<String, WechatCustomerVO> customerMap = customers.stream().collect(Collectors.toMap(WechatCustomerVO::getExternalUserId, Function.identity()));
        List<WechatUserDTO> users = wechatUserService.listById(new ArrayList<>(userSenderIds));
        Map<String, WechatUserDTO> userMap = users.stream().collect(Collectors.toMap(WechatUserDTO::getUserId, Function.identity(), (x1, x2) -> x1));
        List<GetMessageListResponseBody> result = new ArrayList<>(chatMessages.size());
        for (ChatMessageDO chatMessage : chatMessages) {
            GetMessageListResponseBody responseBody = new GetMessageListResponseBody();
            responseBody.setSenderId(chatMessage.getSenderId());
            responseBody.setMessageId(chatMessage.getMessageId());
            responseBody.setTimestamp(chatMessage.getTimestamp());
            FusionChatMessageBody fusionChatMessageBody = MessageUtils.dbContent2FusionChatMessage(chatMessage.getContent());
            responseBody.setMsgType(fusionChatMessageBody.getMsgType());
            responseBody.setMsgContent(fusionChatMessageBody.getMsgContent());
            responseBody.setStatus(chatMessage.getStatus());
            responseBody.setCorpId(chatMessage.getCorpId());
            responseBody.setSenderType(chatMessage.getSenderType());
            responseBody.setAtAll(fusionChatMessageBody.getAtAll());
            responseBody.setAtUserIdList(fusionChatMessageBody.getAtUserIdList());
            responseBody.setAtExternalUserIdList(fusionChatMessageBody.getAtExternalUserIdList());
            switch (FusionChatSenderTypeEnum.parse(chatMessage.getSenderType())) {
                case USER:
                    WechatUserDTO wechatUserDTO = userMap.get(responseBody.getSenderId());
                    if (Objects.isNull(wechatUserDTO)) {
                        break;
                    }

                    responseBody.setSenderAvatar(wechatUserDTO.getAvatarUrl());
                    responseBody.setSenderName(wechatUserDTO.getName());
                    if (!StringUtils.hasText(chatMessage.getExtInfo())) {
                        break;
                    }

                    JSONObject extInfo = JSONObject.parseObject(chatMessage.getExtInfo());
                    if (!extInfo.containsKey("lockUser")) {
                        break;
                    }

                    responseBody.setRequestId(extInfo.getString("requestId"));
                    String lockUser = extInfo.getString("lockUser");
                    User account = accountService.getUserByAccountId(lockUser);
                    if (Objects.nonNull(account)) {
                        responseBody.setSenderName(wechatUserDTO.getName() + "【" + account.getUserName() + "】");
                    }
                    break;
                case CUSTOMER:
                    WechatCustomerVO wechatCustomerVO = customerMap.get(responseBody.getSenderId());
                    if (wechatCustomerVO != null) {
                        responseBody.setSenderName(wechatCustomerVO.getName());
                        responseBody.setSenderAvatar(wechatCustomerVO.getAvatarUrl());
                    }
                    break;
                default:
                    break;
            }
            result.add(responseBody);
        }
        return result;
    }
}