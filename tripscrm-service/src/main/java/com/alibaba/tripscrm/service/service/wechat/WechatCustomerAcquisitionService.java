package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.WechatCustomerAcquisitionLinkDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatCustomerAcquisitionListQuery;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerAcquisitionLinkVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-25 20:21
 */
public interface WechatCustomerAcquisitionService {
    /**
     * 查询全部的获客链接信息
     *
     * @return 获客链接列表
     */
    List<WechatCustomerAcquisitionLinkDO> selectAll();

    /**
     * 分页获取获客链接
     *
     * @param query 查询条件
     * @return 企微客户信息
     */
    PageInfo<WechatCustomerAcquisitionLinkVO> pageQuery(WechatCustomerAcquisitionListQuery query);

    /**
     * 新增获客链接
     *
     * @param wechatCustomerAcquisitionLinkVO 获客链接
     * @return Id
     */
    Long create(WechatCustomerAcquisitionLinkVO wechatCustomerAcquisitionLinkVO);

    /**
     * 批量更新获客链接
     *
     * @param wechatCustomerAcquisitionLinkVOList 获客链接
     * @return 影响行数
     */
    Integer batchUpdate(List<WechatCustomerAcquisitionLinkVO> wechatCustomerAcquisitionLinkVOList);

    /**
     * 更新获客链接
     *
     * @param wechatCustomerAcquisitionLinkDO 获客链接
     * @param spaceId                         业务空间Id
     * @return 影响行数
     */
    Integer update(WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO, Long spaceId);

    /**
     * 根据id查询获客链接
     *
     * @param id      主键
     * @param spaceId 业务空间 Id
     * @return WechatCustomerAcquisitionLinkVO
     */
    WechatCustomerAcquisitionLinkVO getById(Long id, Long spaceId);

    /**
     * 根据id删除获客链接（软删除）
     *
     * @param id      主键
     * @param spaceId 业务空间 Id
     * @return WechatCustomerAcquisitionLinkDO
     */
    Integer deleteById(Long id, Long spaceId);

    /**
     * 追加自定义参数，并转换成短链接
     *
     * @param originUrl       原始长链接
     * @param customerChannel 自定义参数
     * @return 转换后的短链接
     */
    String convertToShortCustomerUrl(String originUrl, String customerChannel);
}
