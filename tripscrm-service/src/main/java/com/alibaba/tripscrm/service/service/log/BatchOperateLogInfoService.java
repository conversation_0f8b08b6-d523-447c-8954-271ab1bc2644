package com.alibaba.tripscrm.service.service.log;

import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoDO;
import com.alibaba.tripscrm.service.model.domain.query.GroupQuery;
import com.alibaba.tripscrm.service.model.vo.log.BatchUpdateVO;
import com.alibaba.tripscrm.service.model.vo.log.UpdateDetailVO;
import com.alibaba.tripscrm.service.model.dto.BatchOperateLogDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/4 17:57
 **/
public interface BatchOperateLogInfoService {

    /**
     * 新增操作
     * @param operationLogInfoDO 日志对象
     * @return 操作结果
     */
    Boolean insert(OperationLogInfoDO operationLogInfoDO);

    /**
     * 查询批量更新组信息
     * @param query 查询条件
     * @return 更新组信息
     */
    List<BatchUpdateVO> queryBatchUpdateInfoList(GroupQuery query);

    /**
     * 条件查询批量操作组的数量
     * @param query 查询条件
     * @return 查询结果
     */
    Long countBatchUpdateGroup(GroupQuery query);

    /**
     * 操作日志基础对象转化为VO对象
     * @param operationLogInfoDO 操作日志DO对象
     * @return VO对象
     */
    UpdateDetailVO convertToVO(OperationLogInfoDO operationLogInfoDO);

    /**
     * 根据管理组id查询详情列表
     * @param groupId 管理组id
     * @return 详情列表
     */
    List<UpdateDetailVO> queryDetailListByGroupId(Long groupId);

    /**
     * 根据管理组id查询记录数量
     * @param groupId 管理组id
     * @return 记录数量
     */
    Long countByGroupId(Long groupId);

    /**
     * 根据异步回调的请求id和状态更新日志状态
     * @param id 日志id
     * @param requestId 请求id
     * @param success 成功状态
     * @return 修改结果
     */
    Boolean updateLogInfoByRequestId(Long id, String requestId, Boolean success);

    /**
     * 根据主键查询
     * @param id 逐渐id
     * @return 操作详情
     */
    BatchOperateLogDTO queryById(Long id);

    /**
     * 根据主键更新
     * @param batchOperateLogDTO 操作详情对象
     * @return 操作结果
     */
    Boolean updateById(BatchOperateLogDTO batchOperateLogDTO);

}
