package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.task.TaskAbTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import com.alibaba.tripscrm.service.service.task.base.AbTestBucketService;
import com.alibaba.tripscrm.service.util.log.ttlog.TtTaskAbLogUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 单条任务数据处理_AB分桶
 *
 * <AUTHOR>
 * @since 2024/4/22 15:22
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AbTestBizProcessor implements BizProcessor {
    private final AbTestBucketService abTestBucketService;

    @Override
    @TaskExecuteLog("单条任务数据处理_AB分桶")
    public TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        todoTaskVO.setAbTestBucketVO(getAbBucket(context, taskDataBody));
        return TripSCRMResult.success(null);
    }


    private AbTestBucketVO getAbBucket(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        TaskAbTypeEnum taskAbType = TaskAbTypeEnum.of(Optional.ofNullable(context.getTaskInfoDOSnapshot().getAbType()).orElse((byte) 0).intValue());
        if (Objects.isNull(taskAbType)) {
            return null;
        }

        try {
            AbTestBucketVO bucket = null;
            AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(TaskType.getByCode(context.getTaskInfoDOSnapshot().getType()));
            String finalTargetId = taskExecutor.getFinalTargetId(context, taskDataBody);
            List<AbTestBucketVO> abBucketList = context.getAbBucketListSnapshot();
            if (!CollectionUtils.isEmpty(abBucketList)) {
                bucket = abTestBucketService.getBucket(abBucketList, finalTargetId);
            }

            if (Objects.nonNull(bucket)) {
                TtTaskAbLogUtils.log(context.getTaskId(), finalTargetId, taskExecutor.getFinalTargetType(context).getCode(), bucket.getId(), bucket.getBucketName());
            }

            return bucket;
        } catch (TripscrmException ex) {
            // 如果id转换失败，使用传入的 targetId，这里最终也无法执行原子能力，只是为了统计数据
            return abTestBucketService.getBucket(context.getTaskId(), taskDataBody.getTargetId());
        }
    }
}