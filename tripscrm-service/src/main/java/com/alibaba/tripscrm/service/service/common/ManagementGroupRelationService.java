package com.alibaba.tripscrm.service.service.common;

import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.query.GroupQuery;
import com.alibaba.tripscrm.service.model.vo.manage.ManageGroupRelationVO;
import com.alibaba.tripscrm.service.model.vo.manage.ManagementGroupVO;

/**
 * 管理组-资源关系服务
 * <AUTHOR>
 * @Date 2024/3/5 16:48
 **/
public interface ManagementGroupRelationService {

    /**
     * 新增管理组
     * @param manageGroupRelationVO 管理组对象
     * @return 新增结果
     */
    Boolean add(ManageGroupRelationVO manageGroupRelationVO);

    /**
     * 更新管理组及信息
     * @param manageGroupRelationVO 管理组视图对象
     * @return 更新结果
     */
    Boolean updateByManagementGroupId(ManageGroupRelationVO manageGroupRelationVO);

    /**
     * 删除管理组
     * @param id 管理组id
     * @param operatorUserId 操作人员id
     * @return 删除结果
     */
    Boolean deleteById(Long id, String operatorUserId);

    /**
     * 查询管理组列表
     * @param query 查询条件
     * @return 管理组列表
     */
    PageResultVO<ManagementGroupVO> list(GroupQuery query);

    /**
     * 查询管理组信息
     * @param id 管理组id
     * @return 管理组视图
     */
    ManagementGroupVO getById(Long id);
}
