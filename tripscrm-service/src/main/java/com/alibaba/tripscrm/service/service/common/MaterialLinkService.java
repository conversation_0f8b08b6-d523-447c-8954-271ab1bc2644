package com.alibaba.tripscrm.service.service.common;

import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;

/**
 * <AUTHOR>
 */
public interface MaterialLinkService {
    /**
     * 转化链接
     *
     * @param request 转化参数
     * @return 转化链接
     */
    String convertLink(MaterialLinkConvertRequest request);

    /**
     * 获取埋点ID
     *
     * @param buriedPointInfo 埋点所需信息
     * @return 用户关联的埋点ID
     */
    String getBuriedPointId(MaterialTrackRelationDTO buriedPointInfo);
}
