package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatContactMeDO;
import com.alibaba.tripscrm.domain.WxQrCodeDTO;
import com.alibaba.tripscrm.domain.request.TripSCRMContactMeLinkRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMEnteriseCreateWechatContactMeRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMEnteriseWechatConcatMeRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.constant.ScrmConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.CallPushDataUpdateEventTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatContactMeService;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.wechat.EnterpriseWechatConcatMeService;
import com.taobao.common.keycenter.security.Cryptograph;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/5/14 14:20
 **/
@Service
@AllArgsConstructor
@HSFProvider(serviceInterface = EnterpriseWechatConcatMeService.class)
public class EnterpriseWechatConcatMeServiceImpl implements EnterpriseWechatConcatMeService {
    private final WechatContactMeService wechatContactMeService;
    private final LdbTairManager ldbTairManager;
    private final ActivityContextService activityContextService;
    private final Cryptograph cryptograph;
    private final MetaqProducer metaqProducer;
    private final ResourceRelationService resourceRelationService;

    @Override
    @ServiceLog("企业微信-根据state获取个人活码信息")
    public TripSCRMResult<String> getConcatMeQrCode(TripSCRMEnteriseWechatConcatMeRequest request) {
        if (request == null || StringUtils.isBlank(request.getState())) {
            PlatformLogUtil.logFail("获取个人活码参数错误", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        try {
            // 缓存获取
            Object o = ldbTairManager.get(TairConstant.CONCAT_ME_QR_CODE + request.getState());
            if (o != null) {
                sendVisitLandingPageMessage(request);
                return TripSCRMResult.success((String) o);
            }
            // 数据库获取
            WechatContactMeDO wechatContactMeDO = wechatContactMeService.getByState(request.getState());
            if (wechatContactMeDO == null || StringUtils.isBlank(wechatContactMeDO.getQrCode())) {
                PlatformLogUtil.logFail("当前state对应无活码数据", LogListUtil.newArrayList(request));
                return TripSCRMResult.fail(TripSCRMErrorCode.CONCAT_ME_STATE_NO_MAPPING_DATA);
            }
            // 放入缓存
            boolean tairResult = ldbTairManager.put(TairConstant.CONCAT_ME_QR_CODE + request.getState(), wechatContactMeDO.getQrCode(), 60 * 60);
            PlatformLogUtil.logFail("获取到个人活码数据", LogListUtil.newArrayList(request, wechatContactMeDO, tairResult));
            sendVisitLandingPageMessage(request);
            return TripSCRMResult.success(wechatContactMeDO.getQrCode());
        } catch (Exception e) {
            PlatformLogUtil.logException("推广获取活码失败", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @Override
    @ServiceLog("企业微信-创建原始个人活码")
    public TripSCRMResult<WxQrCodeDTO> create(TripSCRMEnteriseCreateWechatContactMeRequest request) {
        if (CollectionUtils.isEmpty(request.getUserIdList()) || Objects.isNull(request.getSpaceId()) || Objects.isNull(request.getSkipVerify())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        try {
            WechatContactMeDO wechatContactMeDO = wechatContactMeService.create(request.getUserIdList(), request.getSkipVerify(), activityContextService.generateContextId().toString(), request.getSpaceId());
            WxQrCodeDTO wxQrCodeDTO = new WxQrCodeDTO();
            wxQrCodeDTO.setQrCode(wechatContactMeDO.getQrCode());
            wxQrCodeDTO.setState(wechatContactMeDO.getState());
            return TripSCRMResult.success(wxQrCodeDTO);
        } catch (Exception exception) {
            PlatformLogUtil.logException("新增个人活码失败", exception.getMessage(), exception, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION, exception.getMessage());
        }
    }

    @Override
    @ServiceLog("企业微信-获取个人活码对应页匠页链接")
    public TripSCRMResult<String> getContactMeLink(TripSCRMContactMeLinkRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getSpaceId()) || Objects.isNull(request.getState())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        String pageUrl = SwitchConfig.CONTACT_ME_MARKER_PAGE;
        // 个性化链接查询
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.SPACE_LINK_RELATION.getCode());
        query.setSourceType(ResourceTypeEnum.SPACE.getCode().byteValue());
        query.setSourceId(request.getSpaceId());
        query.setTargetType(ResourceTypeEnum.CONCAT_ME_PAGE_MARKER_LINK.getCode().byteValue());
        List<ResourceRelationDO> resourceRelationList = resourceRelationService.query(query);
        if (CollectionUtils.isNotEmpty(resourceRelationList) && StringUtils.isNotBlank(resourceRelationList.get(0).getTargetId())) {
            pageUrl = resourceRelationList.get(0).getTargetId();
        }
        // 转短链
        String link = MaterialUtils.convert2MiniProgramPath(String.format(pageUrl, request.getState()), null, null);
        return TripSCRMResult.success(link);
    }

    private void sendVisitLandingPageMessage(TripSCRMEnteriseWechatConcatMeRequest request) {
        if (StringUtils.isBlank(request.getEncryptedId())) {
            PlatformLogUtil.logFail("处理用户推送记录参数不足", LogListUtil.newArrayList(request));
            return;
        }
        if (StringUtils.isBlank(request.getUnionId())) {
            PlatformLogUtil.logFail("处理用户推送记录缺少unionId", LogListUtil.newArrayList(request));
        }

        PlatformLogUtil.logFail("处理用户推送记录", LogListUtil.newArrayList(request));

        try {
            String uid;
            if (request.getEncryptedId().startsWith("encode_")) {
                uid = cryptograph.decrypt(request.getEncryptedId().substring(7), ScrmConstant.KEY_CENTER_KEY);
            } else {
                uid = cryptograph.decrypt(request.getEncryptedId(), ScrmConstant.KEY_CENTER_KEY);
            }
            JSONObject data = new JSONObject();
            data.put("targetId", uid);
            data.put("targetType", ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode());
            data.put("unionId", request.getUnionId());
            data.put("eventType", CallPushDataUpdateEventTypeEnum.VISIT_LANDING_PAGE.getCode());
            data.put("corpId", WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            metaqProducer.send(MQEnum.SCRM_CALL_PUSH_DATA_PROCESS, uid, CallPushDataUpdateEventTypeEnum.VISIT_LANDING_PAGE.getCode(), data.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("处理用户推送记录，执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
        }
    }
}
