package com.alibaba.tripscrm.service.service.strategy.material;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.NewMaterialDTO;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class NewsMaterialConverter extends AbstractMaterialConverter<NewMaterialDTO> {

    @Override
    public NewMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        return JSONObject.parseObject(content, NewMaterialDTO.class);
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        return new ArrayList<>();
    }

    @Override
    public List<MessageBO> buildMessageBO(NewMaterialDTO materialDTO, MaterialContentConvertContext context) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgType(MessageTypeEnum.NEWS);
        messageBO.setHref(materialDTO.getUrl());
        messageBO.setTitle(materialDTO.getTitle());
        messageBO.setDesc(materialDTO.getDescribe());
        messageBO.setMsgContent(materialDTO.getPicture().get(0));
        messageBO.setMsgNum(materialDTO.getIndex());
        return Lists.newArrayList(messageBO);
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(NewMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        WxMessageBO wxMessageBO = new WxMessageBO();
        wxMessageBO.setMsgType(WxAttachmentTypeEnum.LINK);
        wxMessageBO.setTitle(materialDTO.getTitle());
        wxMessageBO.setPicUrl(materialDTO.getPicture().get(0));
        wxMessageBO.setDesc(materialDTO.getDescribe());
        wxMessageBO.setPathUrl(materialDTO.getUrl());
        return Lists.newArrayList(wxMessageBO);
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.NEWS;
    }
}
