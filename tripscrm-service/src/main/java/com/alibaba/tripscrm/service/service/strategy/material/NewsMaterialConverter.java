package com.alibaba.tripscrm.service.service.strategy.material;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.*;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.message.ImageTextMsgDTO;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import de.danielbechler.util.Collections;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class NewsMaterialConverter extends AbstractMaterialConverter<NewMaterialDTO> {
    private final MaterialLinkService materialLinkService;

    @Override
    public NewMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        NewMaterialDTO newMaterialDTO = JSONObject.parseObject(content, NewMaterialDTO.class);
        if (!Objects.equals(MaterialSendSceneTypeConstant.ALIPAY_GROUP_MSG, materialTrackRelationDTO.getSceneType())) {
            return newMaterialDTO;
        }
        if (Collections.isEmpty(newMaterialDTO.getTextLinkList())) {
            return newMaterialDTO;
        }
        //支付宝文本链接转换
        for (NewMaterialDTO.TextLink textLink : newMaterialDTO.getTextLinkList()) {
            materialTrackRelationDTO.setMsgParagraphId(-1);
            String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
            // 链接生成
            String pathUrl = getPathUrl(materialTrackRelationDTO, textLink, scrmTrackId);
            if (StringUtils.isBlank(pathUrl)) {
                continue;
            }
            // 链接处理
            String tempUrl = getTempUrl(materialTrackRelationDTO, pathUrl, scrmTrackId);
            textLink.setLink(tempUrl);
        }
        return newMaterialDTO;
    }

    private String getPathUrl(MaterialTrackRelationDTO materialTrackRelationDTO, NewMaterialDTO.TextLink textLink, String scrmTrackId) {

        if (StringUtils.isNotBlank(textLink.getLink())) {
            return textLink.getLink();
        }
        return buildPathUrl(new MaterialSupplyDTO(textLink.getSupplyType(), textLink.getSupplyId()), null, textLink.getLink(), scrmTrackId, materialTrackRelationDTO.getSceneType());
    }

    private String getTempUrl(MaterialTrackRelationDTO materialTrackRelationDTO, String pathUrl, String scrmTrackId) {
        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(pathUrl);
        request.setTargetLinkType(LinkTypeEnum.ALIPAY_LINK);
        request.setConvertShortLink(true);
        request.setTitle("");
        request.setScrmTrackId(scrmTrackId);
        request.setMaterialId(materialTrackRelationDTO.getMaterialId());
        String tempUrl = materialLinkService.convertLink(request);
        return tempUrl;
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        NewMaterialDTO newMaterialDTO = JSONObject.parseObject(content, NewMaterialDTO.class);
        if (Collections.isEmpty(newMaterialDTO.getTextLinkList())) {
            return new ArrayList<>();
        }
        List<MaterialSupplyDTO> materialSupplyList = newMaterialDTO.getTextLinkList()
                .stream()
                .filter(textLink -> StringUtils.isNotBlank(textLink.getSupplyId()) && StringUtils.isNotBlank(textLink.getSupplyType()))
                .map(textLink -> new MaterialSupplyDTO(textLink.getSupplyType(), textLink.getSupplyId()))
                .collect(Collectors.toList());
        return materialSupplyList;
    }

    @Override
    public List<MessageBO> buildMessageBO(NewMaterialDTO materialDTO, MaterialContentConvertContext context, String sceneType) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgType(MessageTypeEnum.NEWS);
        messageBO.setHref(materialDTO.getUrl());
        messageBO.setTitle(materialDTO.getTitle());
        messageBO.setDesc(materialDTO.getDescribe());
        messageBO.setMsgContent(materialDTO.getPicture().get(0));
        messageBO.setMsgNum(materialDTO.getIndex());
        // 支付宝消息
        if (Objects.equals(MaterialSendSceneTypeConstant.ALIPAY_GROUP_MSG, sceneType)) {
            return buildAlipayMessageBO(materialDTO, messageBO);
        }
        return Lists.newArrayList(messageBO);
    }

    /**
     * 构建支付宝图文消息
     *
     * @param materialDTO
     * @param messageBO
     * @return
     */
    public List<MessageBO> buildAlipayMessageBO(NewMaterialDTO materialDTO, MessageBO messageBO) {
        ImageTextMsgDTO imageTextMsgDTO = new ImageTextMsgDTO();
        imageTextMsgDTO.setImageId(materialDTO.getImageId());
        imageTextMsgDTO.setUrl(materialDTO.getUrl());
        //支付宝文字链接转换
        if (Collections.isEmpty(materialDTO.getTextLinkList())) {
            return Lists.newArrayList(messageBO);
        }
        List<ImageTextMsgDTO.TextLink> textLinkList = new ArrayList<>();

        for (NewMaterialDTO.TextLink textLink : materialDTO.getTextLinkList()) {
            ImageTextMsgDTO.TextLink textLinkDTO = imageTextMsgDTO.new TextLink();
            textLinkDTO.setLink(textLink.getLink());
            textLinkDTO.setText(textLink.getText());
            textLinkList.add(textLinkDTO);
        }
        imageTextMsgDTO.setTextLinkList(textLinkList);
        messageBO.setExtFields(JSONObject.toJSONString(imageTextMsgDTO));
        return Lists.newArrayList(messageBO);
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(NewMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        WxMessageBO wxMessageBO = new WxMessageBO();
        wxMessageBO.setMsgType(WxAttachmentTypeEnum.LINK);
        wxMessageBO.setTitle(materialDTO.getTitle());
        wxMessageBO.setPicUrl(materialDTO.getPicture().get(0));
        wxMessageBO.setDesc(materialDTO.getDescribe());
        wxMessageBO.setPathUrl(materialDTO.getUrl());
        return Lists.newArrayList(wxMessageBO);
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.NEWS;
    }

    @Override
    public String buildSubscribeMsgTemplateContent(String content, String paramType) {
        return "";
    }
}
