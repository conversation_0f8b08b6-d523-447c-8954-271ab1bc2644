package com.alibaba.tripscrm.service.service.strategy.isv.route.customer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmBlackListDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.system.BlackListTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.query.ScrmBlackListQuery;
import com.alibaba.tripscrm.service.service.ScrmBlackListService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.isv.route.AbstractActionRouteStrategy;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.google.common.collect.Lists;
import com.taobao.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/27 10:38
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerSendAddFriendRequestByPhoneRouteStrategy extends AbstractActionRouteStrategy {
    private final WechatUserService wechatUserService;
    private final ScrmBlackListService scrmBlackListService;
    private final WechatCustomerService wechatCustomerService;
    private final SpaceService spaceService;
    private final LdbTairManager ldbTairManager;

    @Override
    protected TripSCRMResult<List<String>> getAllWechatUserIdList(IsvRouteContext isvRouteContext) {
        return null;
    }

    @Override
    protected TripSCRMResult<List<WechatUserDTO>> getValidOnlineUserList(IsvRouteContext isvRouteContext) {
        WechatUserQuery query = new WechatUserQuery();
        if (!CollectionUtils.isEmpty(isvRouteContext.getUserIdList())) {
            query.setUserIdList(isvRouteContext.getUserIdList());
        }
        query.setCorpId(spaceService.getCorpIdBySpaceId(isvRouteContext.getSpaceId()));
        query.setSpaceId(isvRouteContext.getSpaceId());
        query.setQueryRobotInfo(true);
        List<WechatUserDTO> wechatUserList = wechatUserService.listValidWechatUserByCondition(query);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        List<WechatUserDTO> finalWechatUserList = filter(wechatUserList);
        if (CollectionUtils.isEmpty(finalWechatUserList)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        return TripSCRMResult.success(finalWechatUserList);
    }

    private List<WechatUserDTO> filter(List<WechatUserDTO> wechatUserList) {
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return wechatUserList;
        }

        // 黑名单过滤
        List<WechatUserDTO> blackListFilterResult = blackListFilter(wechatUserList);
        if (CollectionUtil.isEmpty(blackListFilterResult)) {
            return Lists.newArrayList();
        }
        // 频控
        List<WechatUserDTO> fatigueFilterResult = fatigueFilter(blackListFilterResult);
        if (CollectionUtil.isEmpty(fatigueFilterResult)) {
            return Lists.newArrayList();
        }
        // 已存在好友数目限制过滤
        List<WechatUserDTO> customerCountFilterResult = customerCountFilter(fatigueFilterResult);
        if (CollectionUtil.isEmpty(customerCountFilterResult)) {
            return Lists.newArrayList();
        }
        return customerCountFilterResult;
    }

    private List<WechatUserDTO> blackListFilter(List<WechatUserDTO> wechatUserList) {
        List<WechatUserDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return result;
        }

        List<List<WechatUserDTO>> partitions = Lists.partition(wechatUserList, 50);
        for (List<WechatUserDTO> partition : partitions) {
            ScrmBlackListQuery query = ScrmBlackListQuery.builder()
                    .targetType(ActivityTargetTypeEnum.WX_USER_ID)
                    .targetIdList(partition.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()))
                    .type(BlackListTypeEnum.ALL)
                    .build();
            Set<String> set = scrmBlackListService.selectByCondition(query).stream().map(ScrmBlackListDO::getTargetId).collect(Collectors.toSet());
            result.addAll(partition.stream().filter(x -> !set.contains(x.getUserId())).collect(Collectors.toList()));
        }
        return result;
    }

    private List<WechatUserDTO> fatigueFilter(List<WechatUserDTO> wechatUserList) {
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return Lists.newArrayList();
        }

        Date date = new Date();
        Long dayVersion = DateUtils.getDayVersion(date);
        Long minuteVersion = DateUtils.getMinuteVersion(date);
        Long windowVersion = minuteVersion / 10;

        List<WechatUserDTO> validUserList = wechatUserList.stream().filter(wechatUserDTO -> {
            String dayKey = TairConstant.WECHAT_USER_ADD_CUSTOMER_COUNT_IN_WINDOW_PREFIX + dayVersion + "_d_" + SpaceInfoThreadLocalUtils.getCorpId() + "_" + wechatUserDTO.getUserId();
            Integer addFriendsNumToday = (Integer) ldbTairManager.get(dayKey);
            if (Optional.ofNullable(addFriendsNumToday).orElse(0) > SwitchConfig.ADD_FRIENDS_SINGLE_USER_ID_DAY_MAX_LIMIT) {
                PlatformLogUtil.logFail("获取可执行手机号加好友行动项对应的企微号，匹配企微号时，当前账号触发当天添加好友数上限", LogListUtil.newArrayList(dayVersion, wechatUserDTO.getUserId()));
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(validUserList)) {
            PlatformLogUtil.logFail("获取可执行手机号加好友行动项对应的企微号，匹配企微号时，所有账号均触发当天添加好友数上限", LogListUtil.newArrayList(dayVersion));
            return Lists.newArrayList();
        }

        List<WechatUserDTO> windowValidUserList = validUserList.stream().filter(wechatUserDTO -> {
            String windowKey = TairConstant.WECHAT_USER_ADD_CUSTOMER_COUNT_IN_WINDOW_PREFIX + windowVersion + "_w_" + SpaceInfoThreadLocalUtils.getCorpId() + "_" + wechatUserDTO.getUserId();
            Integer addFriendsNumTenMinutes = (Integer) ldbTairManager.get(windowKey);
            if (Optional.ofNullable(addFriendsNumTenMinutes).orElse(0) > SwitchConfig.ADD_FRIENDS_SINGLE_USER_ID_TEN_MINUTES_MAX_LIMIT) {
                PlatformLogUtil.logFail("获取可执行手机号加好友行动项对应的企微号，匹配企微号时，当前账号触发当前时间窗口添加好友数上限", LogListUtil.newArrayList(dayVersion, windowVersion, wechatUserDTO.getUserId()));
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        if (CollectionUtil.isEmpty(windowValidUserList)) {
            PlatformLogUtil.logFail("获取可执行手机号加好友行动项对应的企微号，匹配企微号时，所有账号均触发当前时间窗口添加好友数上限", LogListUtil.newArrayList(dayVersion, windowVersion));
            return Lists.newArrayList();
        }

        return windowValidUserList;
    }

    private List<WechatUserDTO> customerCountFilter(List<WechatUserDTO> userIdList) {
        Long dayVersion = DateUtils.getDayVersion(new Date());
        List<WechatUserDTO> validUserList = userIdList.stream().filter(wechatUserDTO -> {
            String corpId = SpaceInfoThreadLocalUtils.getCorpId();
            String key = TairConstant.WECHAT_USER_CUSTOMER_COUNT_PREFIX + dayVersion + "_" + corpId + "_" + wechatUserDTO.getUserId();
            Long customerCount = Optional.ofNullable(ldbTairManager.get(key))
                    .map(o -> (Long) o).orElse(-1L);

            // 0-19000直接认为可用
            if (customerCount >= 0L && customerCount < SwitchConfig.ADD_FRIENDS_USER_ID_FRIENDS_NUM_MAX_LIMIT - 1000L) {
                return true;
            }

            // 超过19900，就认为到上限了
            if (customerCount >= SwitchConfig.ADD_FRIENDS_USER_ID_FRIENDS_NUM_MAX_LIMIT - 100L) {
                return false;
            }

            // 没有在缓存中，或者超过19000，就走DB
            customerCount = wechatCustomerService.getCustomerCount(wechatUserDTO.getUserId(), false);
            return customerCount < SwitchConfig.ADD_FRIENDS_USER_ID_FRIENDS_NUM_MAX_LIMIT - 100L;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validUserList)) {
            PlatformLogUtil.logFail("获取可执行手机号加好友行动项对应的企微号，匹配企微号时，所有账号均已达到好友上限", LogListUtil.newArrayList(dayVersion));
            return Lists.newArrayList();
        }
        return validUserList;
    }

    @Override
    protected List<RiskActionEnum> getRiskActionEnumList() {
        return Lists.newArrayList(RiskActionEnum.CUSTOMER_SEND_ADD_FRIEND_REQUEST_BY_PHONE);
    }
}
