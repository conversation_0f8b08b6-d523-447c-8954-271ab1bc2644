package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;

/**
 * 群活码管理任务配置处理器
 *
 * <AUTHOR>
 * @date 2023-12-30 15:26:28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ManagerWechatJoinGroupTaskConfigProcessor extends AbstractTaskConfigProcessor {
    @Switch(description = "企微客户群成员变更（群活码配置管理）事件源", name = "managerWechatJoinGroupEventSourceId")
    public static Long managerWechatJoinGroupEventSourceId = 19L;

    private final MaterialService materialService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        JSONObject extraInfoJson = super.getExtraInfo(taskInfoDO);
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        if (!configJson.containsKey("materialId")) {
            return extraInfoJson;
        }

        Long materialId = configJson.getLong("materialId");
        MaterailInfoDO materailInfoDO = materialService.queryById(materialId);
        if (Objects.nonNull(materailInfoDO)) {
            extraInfoJson.put("materialName", materailInfoDO.getName());
        }
        return extraInfoJson;
    }

    @Override
    public TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName) {
        TaskInfoDO taskInfoDO = super.getInitConfig(activityId, spaceId, taskName);
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        Date effectEndTime = new Date(LocalDate.of(2040, 12, 30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        taskInfoDO.setEffectEndTime(effectEndTime);
        taskInfoDO.setEventSourceId(managerWechatJoinGroupEventSourceId);
        return taskInfoDO;
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.MANAGER_WECHAT_JOIN_GROUP;
    }
}
