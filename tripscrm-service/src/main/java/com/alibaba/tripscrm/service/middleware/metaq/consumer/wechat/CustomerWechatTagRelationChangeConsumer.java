package com.alibaba.tripscrm.service.middleware.metaq.consumer.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.dto.wechat.CustomerRelationDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.CustomerTagDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.CustomerTagSynchronizerDTO;
import com.alibaba.tripscrm.service.synchronizer.CustomerTagRelationSynchronizer;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.model.CustomerTagRelationBO;
import com.alibaba.tripzoo.proxy.model.CustomerTagRelationListBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 企微标签变更
 * topic: tripscrm_customer_tag_relation_sync
 * consumerId: CID_tripscrm_customer_tag_relation_sync

 *
 * <AUTHOR>
 * @since 2024/01/02
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerWechatTagRelationChangeConsumer implements MessageListenerConcurrently {

    private final CustomerTagRelationSynchronizer customerTagRelationSynchronizer;
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("接收到客户企微标签关系变更消息", LogListUtil.newArrayList(msg, context));
            String receivedMsg = new String(msg.getBody());
            String tags = msg.getTags();
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("处理客户企微标签关系变更消息失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private Boolean dealWithMessage(String message) {
        try {
            if (!paramVerify(message)) {
                PlatformLogUtil.logFail("客户标签关系变更回调参数校验失败", LogListUtil.newArrayList(message));
                return false;
            }
            JSONObject content = JSONObject.parseObject(message);
            // 从回调中获取企微成员id列表、标签source列表、标签组source列表
            CustomerTagRelationListBO customerTagRelationList = JSONObject.parseObject(content.getString(CallbackConstant.CUSTOMER_TAG_RELATION_LIST), new TypeReference<CustomerTagRelationListBO>() {});
            if (CollectionUtils.isEmpty(customerTagRelationList.getCustomerTagRelationBOList())) {
                PlatformLogUtil.logFail("客户标签关系变更回调参数缺失", LogListUtil.newArrayList(message));
                return false;
            }
            // 将标签信息存入同步器上下文
            List<CustomerRelationDTO> followUserList = getCustomerRelationDTOS(customerTagRelationList);

            BaseSynchronizerContext<CustomerTagSynchronizerDTO> context = new BaseSynchronizerContext<>();
            CustomerTagSynchronizerDTO data = new CustomerTagSynchronizerDTO();
            data.setFollowUserList(followUserList);
            context.setData(data);
            context.setCorpId(content.getString(CallbackConstant.CORP_ID));
            context.setExternalUserId(content.getString(CallbackConstant.EXTERNAL_USER_ID));
            // 同步
            customerTagRelationSynchronizer.sync(context);
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("处理客户企微标签关系变更失败", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
    }

    private List<CustomerRelationDTO> getCustomerRelationDTOS(CustomerTagRelationListBO customerTagRelationList) {
        List<CustomerRelationDTO> followUserList = new ArrayList<>();
        for (CustomerTagRelationBO customerTagRelation : customerTagRelationList.getCustomerTagRelationBOList()) {
            if (Objects.isNull(customerTagRelation) || !StringUtils.hasLength(customerTagRelation.getUserId()) || CollectionUtils.isEmpty(customerTagRelation.getTagSourceList())) {
                continue;
            }
            CustomerRelationDTO customerRelationDTO = new CustomerRelationDTO();
            List<CustomerTagDTO> customerTagDTOList = new ArrayList<>();
            for (String tagSource : customerTagRelation.getTagSourceList()) {
                CustomerTagDTO customerTagDTO = new CustomerTagDTO();
                customerTagDTO.setTagSource(tagSource);
                customerTagDTOList.add(customerTagDTO);
            }
            customerRelationDTO.setUserId(customerTagRelation.getUserId());
            customerRelationDTO.setTagInfoList(customerTagDTOList);
            followUserList.add(customerRelationDTO);
        }
        return followUserList;
    }

    private Boolean paramVerify(String message) {
        if (!StringUtils.hasLength(message)) {
            PlatformLogUtil.logFail("客户标签变更回调参数为空", LogListUtil.newArrayList(message));
            return false;
        }

        JSONObject content = JSONObject.parseObject(message);

        if (!content.containsKey(CallbackConstant.EXTERNAL_USER_ID) || !content.containsKey(CallbackConstant.CORP_ID) || !content.containsKey(CallbackConstant.CUSTOMER_TAG_RELATION_LIST)) {
            PlatformLogUtil.logFail("客户标签变更回调参数缺失", LogListUtil.newArrayList(message));
            return false;
        }
        if (!StringUtils.pathEquals(SwitchConfig.ADD_FRIENDS_CALL_USER_ID_CORP_ID, content.getString(CallbackConstant.CORP_ID))) {
            PlatformLogUtil.logFail("客户标签变更回调参数corpId不匹配", LogListUtil.newArrayList(message));
            return false;
        }
        return true;
    }
}
