package com.alibaba.tripscrm.service.middleware.metaq.consumer.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.dal.model.domain.data.EventSourceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.task.EventSourceManager;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-17 15:58:52
 */
@Slf4j
@Component
public class EventSourceMessageListener implements MessageListenerConcurrently {
    @Resource
    private EventSourceManager eventSourceManager;
    @Resource
    private TaskService taskService;
    @Resource
    private TaskExecuteStrategy taskExecuteStrategy;
    @Resource
    private LdbTairManager ldbTairManager;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> messageList, ConsumeConcurrentlyContext context) {
        if (CollectionUtils.isEmpty(messageList)) {
            PlatformLogUtil.logFail("事件源监听器，消息为空", LogListUtil.newArrayList(messageList));
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        List<EventSourceDO> eventSourceList;
        try {
            String topic = messageList.get(0).getTopic();
            eventSourceList = eventSourceManager.getEventSourceList(topic);
            if (CollectionUtils.isEmpty(eventSourceList)) {
                // topic 没有对应的事件源
                PlatformLogUtil.logFail("事件源监听器，topic没有对应的事件源", LogListUtil.newArrayList(topic, messageList));
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            eventSourceList = checkValid(eventSourceList);
        } catch (Exception ex) {
            PlatformLogUtil.logException("事件源监听器，前置校验出错", ex.getMessage(), ex, LogListUtil.newArrayList(messageList));
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        for (MessageExt msg : messageList) {
            try {
                if (msg.getReconsumeTimes() == 0 && ldbTairManager.incr(TairConstant.METAQ_MESSAGE_ID_DISTINCT_KEY_PREFIX + msg.getMsgId(), 1, 0, 60) > 1) {
                    PlatformLogUtil.logFail("事件源监听器，消息重复消费，跳过执行", LogListUtil.newArrayList(msg));
                    continue;
                }
                String receivedMsg = new String(msg.getBody());
                if (!dealWithMessage(receivedMsg, eventSourceList)) {
                    PlatformLogUtil.logFail("事件源监听器，接收消息，执行失败", LogListUtil.newArrayList(msg, receivedMsg));
                    continue;
                }
                PlatformLogUtil.logInfo("事件源监听器，接收消息，执行完毕", LogListUtil.newArrayList(msg, receivedMsg));
            } catch (Exception ex) {
                PlatformLogUtil.logException("事件源监听器，接收消息，执行出错", ex.getMessage(), ex, LogListUtil.newArrayList(messageList));
            }
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private boolean dealWithMessage(String message, List<EventSourceDO> eventSourceList) {
        for (EventSourceDO eventSource : eventSourceList) {
            long startTime = System.currentTimeMillis();
            // MetaQ 消息体
            JSONObject messageJson = JSONObject.parseObject(message);
            // 事件源中配置的参数路径映射
            JSONArray paramsJson = JSONObject.parseArray(eventSource.getParams());
            Map<String, Object> extraInfo = new HashMap<>();
            // 从消息体中提取参数
            for (int i = 0; i < paramsJson.size(); i++) {
                String name = paramsJson.getJSONObject(i).getString("name");
                String path = paramsJson.getJSONObject(i).getString("path");
                path = StringUtils.startsWithIgnoreCase(path, "$.") ? path : "$." + path;
                if (JSONPath.contains(messageJson, path)) {
                    extraInfo.put(name, JSONPath.eval(messageJson, path));
                }
            }

            // targetId 是必须要有的参数，否则无法执行任务
            String targetId = MapUtils.getString(extraInfo, TaskConstant.TARGET_ID);
            if (!StringUtils.hasText(targetId)) {
                PlatformLogUtil.logFail("事件源监听器，处理消息，消息内容缺少targetId", LogListUtil.newArrayList(message));
                continue;
            }

            // 判断task的corpId和messageJson中的corpId做过滤
            String eventCorpId = MapUtils.getString(messageJson, TaskConstant.CORP_ID, WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            if (!StringUtils.hasText(eventCorpId)) {
                PlatformLogUtil.logFail("事件源监听器，处理消息，消息内容缺少corpId", LogListUtil.newArrayList(message));
                continue;
            }

            List<String> corpIdList = Arrays.stream(eventCorpId.split(",")).collect(Collectors.toList());
            // 查询所有绑定该事件源的任务
            List<TaskInfoDO> taskInfoList = taskService.listByEventSourceIdAndStatus(eventSource.getId(), TaskStatusEnum.ONLINE);
            for (TaskInfoDO taskInfoDO : taskInfoList) {
                try {
                    singleTaskExecute(message, eventSource, taskInfoDO, messageJson, targetId, corpIdList, extraInfo);
                } catch (Exception ex) {
                    PlatformLogUtil.logException("事件源监听器，处理消息，单个任务执行出错", ex.getMessage(), ex, LogListUtil.newArrayList(message, eventSource, taskInfoDO));
                }
            }
            long endTime = System.currentTimeMillis();
            PlatformLogUtil.logInfo("事件源监听器，处理消息，整体结束，耗时统计", LogListUtil.newArrayList(endTime - startTime, eventSource.getId(), taskInfoList.size()));
        }
        return true;
    }

    private void singleTaskExecute(String message, EventSourceDO eventSource, TaskInfoDO taskInfoDO, JSONObject messageJson, String targetId, List<String> corpIdList, Map<String, Object> extraInfo) {
        if (messageJson.containsKey(TaskConstant.TASK_ID) && !Objects.equals(taskInfoDO.getId(), messageJson.getLong(TaskConstant.TASK_ID))) {
            PlatformLogUtil.logFail("任务不属于事件指定的任务，跳过执行", LogListUtil.newArrayList(taskInfoDO.getId(), targetId, messageJson.getLong(TaskConstant.TASK_ID)));
            return;
        }
        if (messageJson.containsKey(TaskConstant.ACTIVITY_ID) && !Objects.equals(taskInfoDO.getActivityId(), messageJson.getLong(TaskConstant.ACTIVITY_ID))) {
            PlatformLogUtil.logFail("任务不属于事件指定的活动，跳过执行", LogListUtil.newArrayList(taskInfoDO.getId(), targetId, messageJson.getLong(TaskConstant.ACTIVITY_ID)));
            return;
        }
        Long taskId = taskInfoDO.getId();
        String corpId = taskService.getCorpIdByTaskId(taskId);
        if (!corpIdList.contains(corpId)) {
            return;
        }
        // 目标类型，默认是淘宝Id
        Integer targetType = Optional.ofNullable(eventSource.getTargetType()).orElse((byte) 1).intValue();
        if (extraInfo.containsKey(TaskConstant.TARGET_TYPE)) {
            targetType = Integer.parseInt(extraInfo.get(TaskConstant.TARGET_TYPE).toString());
        }
        TaskExecuteParam param = TaskExecuteParam.buildParam(new Date(), taskId, TaskTriggerTypeEnum.EVENT, targetType, targetId, true, extraInfo, new ArrayList<>());
        TripSCRMResult<String> result = taskExecuteStrategy.runMain(param);
        PlatformLogUtil.logInfo("事件源监听器，处理消息，单个任务执行结束", LogListUtil.newArrayList(message, eventSource, taskId, result));
    }

    private List<EventSourceDO> checkValid(List<EventSourceDO> eventSourceList) {
        return eventSourceList.stream().filter(eventSource -> {
            if (eventSourceManager.checkEventSourceValid(eventSource)) {
                return true;
            }
            // 事件源已不生效
            PlatformLogUtil.logInfo("事件源监听器，前置校验，事件源已失效", LogListUtil.newArrayList(eventSource));
            return false;
        }).collect(Collectors.toList());
    }
}