package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WxEnterpriseUserQuery extends BasePageRequest {
    /**
     * 分组
     */
    private String groupId = "jimei";

    /**
     * 支持模糊查询
     */
    private String name;
    /**
     * 业务分组
     */
    private String bizGroupId;
}
