package com.alibaba.tripscrm.service.service.impl.hsf.config;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskInvokeStatusEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskInvokeSubStatusEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.SpaceInfo;
import com.alibaba.tripscrm.service.model.domain.request.RiskActionRequest;
import com.alibaba.tripscrm.service.model.domain.request.RiskInvokeRecordCreateParam;
import com.alibaba.tripscrm.service.model.domain.risk.ActionCheckResult;
import com.alibaba.tripscrm.service.model.domain.risk.RateLimitResult;
import com.alibaba.tripscrm.service.service.risk.RiskActionChecker;
import com.alibaba.tripscrm.service.service.risk.controller.RiskActionLimitController;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.SpringBeanUtils;
import com.alibaba.tripzoo.proxy.enums.ErrorCodeEnum;
import com.alibaba.tripzoo.proxy.request.base.CorpRobotRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.taobao.hsf.domain.HSFResponse;
import com.taobao.hsf.invocation.Invocation;
import com.taobao.hsf.invocation.InvocationHandler;
import com.taobao.hsf.invocation.RPCResult;
import com.taobao.hsf.invocation.filter.ClientFilter;
import com.taobao.hsf.util.concurrent.DefaultListenableFuture;
import com.taobao.hsf.util.concurrent.Futures;
import com.taobao.hsf.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

/**
 * HSF 拦截器
 * https://yuque.alibaba-inc.com/chgygl/xmp9di/xpufzh
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Slf4j
public class HsfClientForRiskFilter implements ClientFilter {

    /**
     * HSF调用入口拦截
     *
     * @return 调用结果
     */
    @Override
    public ListenableFuture<RPCResult> invoke(InvocationHandler invocationHandler, Invocation invocation) throws Throwable {
        Class<?> ifClazz = invocation.getClientInvocationContext().getMethodModel().getMetadata().getIfClazz();
        String riskActionCode = ifClazz.getSimpleName() + "#" + invocation.getMethodName();
        // 仅标注的riskAction方法需要进行风控，不风控的直接返回
        RiskActionEnum riskAction = RiskActionEnum.parse(riskActionCode);
        if (riskAction == null) {
            return invocationHandler.invoke(invocation);
        }
        // 获取需要风控企微成员userId，若获取不到则直接返回
        CorpRobotRequest corpRobotRequest = this.getUserId(invocation);
        if (corpRobotRequest == null) {
            return invocationHandler.invoke(invocation);
        }
        // 在风控下执行方法，返回结果
        ListenableFuture<RPCResult> rpcResultListenableFuture = this.invokeInRisk(invocationHandler, invocation, corpRobotRequest, riskAction);
        // 风控埋点
        this.invokeRecord(invocation, rpcResultListenableFuture, corpRobotRequest, riskAction);
        // 返回结果
        return rpcResultListenableFuture;
    }

    /**
     * 在风控下执行方法，返回结果
     *
     * @return 调用结果
     */
    private ListenableFuture<RPCResult> invokeInRisk(InvocationHandler invocationHandler, Invocation invocation, CorpRobotRequest corpRobotRequest, RiskActionEnum riskAction) throws Throwable {
        RiskActionLimitController riskActionLimitController = SpringBeanUtils.getBean(RiskActionLimitController.class);
        RiskActionChecker riskActionChecker = SpringBeanUtils.getBean(RiskActionChecker.class);
        ListenableFuture<RPCResult> rpcResultListenableFuture;
        try {
            // 风控检查
            ActionCheckResult actionCheckResult = riskActionChecker.riskCheck(corpRobotRequest.getCorpId(), corpRobotRequest.getUserId(), riskAction);
            Object resultObj;
            switch (actionCheckResult.getRiskStatus()) {
                case NORMAL:
                    // 正常可用，进入风控控制
                    RateLimitResult rateLimitResult = new RateLimitResult(true);
                    if (riskActionLimitController.judgeIntoProtect(
                            new RiskActionRequest(corpRobotRequest.getCorpId(), corpRobotRequest.getUserId(), riskAction, invocation, rateLimitResult))) {
                        // 触发限流
                        DefaultListenableFuture<RPCResult> result = Futures.createSettableFuture();
                        RPCResult rpcResult = new RPCResult();
                        HSFResponse hsfResponse = new HSFResponse();
                        hsfResponse.setAppResponse(ResultDO.falseInstance(ErrorCodeEnum.BIZ_RATE_LIMIT));
                        rpcResult.setHsfResponse(hsfResponse);
                        result.set(rpcResult);
                        rpcResultListenableFuture = result;
                        resultObj = rateLimitResult;
                    } else {
                        // 未触发限流
                        rpcResultListenableFuture = invocationHandler.invoke(invocation);
                        resultObj = rpcResultListenableFuture.get().getAppResponse();
                    }
                    break;
                case ESCAPE:
                    // 不受风控
                    rpcResultListenableFuture = invocationHandler.invoke(invocation);
                    resultObj = rpcResultListenableFuture.get().getAppResponse();
                    break;
                case HOLD:
                case PROTECT:
                case ABNORMAL:
                    // 机器人/行动项降级保护
                    DefaultListenableFuture<RPCResult> result = Futures.createSettableFuture();
                    RPCResult rpcResult = new RPCResult();
                    HSFResponse hsfResponse = new HSFResponse();
                    hsfResponse.setAppResponse(ResultDO.falseInstance(ErrorCodeEnum.BIZ_ACTION_RISK_LIMIT));
                    rpcResult.setHsfResponse(hsfResponse);
                    result.set(rpcResult);
                    rpcResultListenableFuture = result;
                    resultObj = actionCheckResult;
                    break;
                default:
                    rpcResultListenableFuture = invocationHandler.invoke(invocation);
                    resultObj = rpcResultListenableFuture.get().getAppResponse();
                    break;
            }
            invokeRecord(invocation, resultObj, corpRobotRequest, riskAction);
        } catch (Exception e) {
            PlatformLogUtil.logException("风控控制，出现异常", e.getMessage(), e, LogListUtil.newArrayList());
            rpcResultListenableFuture = invocationHandler.invoke(invocation);
        }
        return rpcResultListenableFuture;
    }

    /**
     * 根据调用结果进行风控埋点
     */
    private void invokeRecord(Invocation invocation, Object resultObj, CorpRobotRequest corpRobotRequest, RiskActionEnum riskAction) throws Throwable {
        MetaqProducer metaqProducer = SpringBeanUtils.getBean(MetaqProducer.class);
        String corpId = SpaceInfoThreadLocalUtils.getCorpId();
        SpaceInfo spaceInfo = SpaceInfoThreadLocalUtils.getSpaceInfo();
        String invokeParam = JSONObject.toJSONString(invocation.getMethodArgs());
        String invokeResult = null;
        RiskInvokeStatusEnum invokeStatus = null;
        RiskInvokeSubStatusEnum invokeSubStatus = null;
        String limitAction = null;
        try {
            if (resultObj instanceof ResultDO) {
                // 正常请求
                ResultDO result = (ResultDO) resultObj;
                invokeStatus = result.getSuccess() ? RiskInvokeStatusEnum.SUCCESS : RiskInvokeStatusEnum.FAIL;
                invokeResult = JSONObject.toJSONString(resultObj);
            } else if (resultObj instanceof RateLimitResult) {
                // 请求限流
                RateLimitResult rateLimitResult = (RateLimitResult) resultObj;
                invokeStatus = RiskInvokeStatusEnum.RISK;
                invokeResult = JSONObject.toJSONString(rateLimitResult);
                invokeSubStatus = rateLimitResult.getLimitType().getInvokeSubStatusForLimit();
                limitAction = rateLimitResult.getHitActionCode();
            } else if (resultObj instanceof ActionCheckResult) {
                // 降级请求：保护/挂起/异常
                ActionCheckResult actionCheckResult = (ActionCheckResult) resultObj;
                invokeStatus = RiskInvokeStatusEnum.RISK;
                invokeResult = JSONObject.toJSONString(actionCheckResult);
                invokeSubStatus = actionCheckResult.getInvokeSubStatus();
                limitAction = riskAction.getActionCode();
            } else {
                // 异常请求
                invokeStatus = RiskInvokeStatusEnum.FAIL;
                invokeResult = JSONObject.toJSONString(resultObj);
            }
        } finally {
            metaqProducer.send(MQEnum.RISK_INVOKE_RECORD, UUID.randomUUID().toString(),
                    RiskInvokeRecordCreateParam.newInstance(corpId, corpRobotRequest.getUserId())
                            .setAction(riskAction.getActionCode(), limitAction)
                            .setSource(spaceInfo.getSourceType(), spaceInfo.getSourceId())
                            .setInvoke(invokeParam, invokeResult, invokeStatus, invokeSubStatus)
                            .string());
            PlatformLogUtil.logInfo("风控控制-生产风控埋点", LogListUtil.newArrayList(corpRobotRequest.getUserId(), limitAction, invokeSubStatus, riskAction.getActionCode(), invocation.getMethodArgs(), resultObj));
        }
    }

    /**
     * 获取当前操作的企微成员id
     *
     * @param invocation invocation
     * @return return
     */
    private CorpRobotRequest getUserId(Invocation invocation) {
        // 获取实际的入参
        Object[] arguments = invocation.getMethodArgs();
        for (Object argument : arguments) {
            if (argument instanceof CorpRobotRequest) {
                return (CorpRobotRequest) argument;
            }
        }
        return null;
    }

    @Override
    public void onResponse(Invocation invocation, RPCResult rpcResult) {

    }
}