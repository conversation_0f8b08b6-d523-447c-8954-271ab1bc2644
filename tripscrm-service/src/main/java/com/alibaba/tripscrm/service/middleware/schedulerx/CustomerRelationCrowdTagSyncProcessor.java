package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.manager.middleware.OdpsManager;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.RecordReader;
import com.aliyun.odps.tunnel.InstanceTunnel;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;

/**
 * 客户关系诸葛标签同步定时任务
 * 诸葛标签：实时-是否是对应企微部门的好友
 *
 * <AUTHOR>
 * @since 2025/06/30
 */

@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerRelationCrowdTagSyncProcessor extends MapJobProcessor {
    private final OdpsManager odpsManager;
    private final WechatCustomerService wechatCustomerService;
    private final TagRelationService tagRelationService;

    @Switch(description = "企微客户全量查询离线表SQL", name = "wechatCustomerAllSearchOdpsSql")
    private static String WECHAT_CUSTOMER_ALL_SEARCH_ODPS_SQL = "SELECT external_user_id, corp_id, union_id FROM trip_ods.s_wechat_customer_tripscrm_app WHERE ds = MAX_PT('trip_ods.s_wechat_customer_tripscrm_app') and  (ABS(HASH(external_user_id)) %% %d = %d);";

    @Switch(name = "wechatCustomerAllSearchOdpsSleepPeriod", description = "downloadSession获取失败时间隔多久再试一次。默认值30秒")
    private static Long CUSTOMER_RELATION_TAG_SYNC_ODPS_SEARCH_SLEEP_PERIOD = 30 * 1000L;

    @Switch(name = "wechatCustomerAllFragSize", description = "企微客户全量查询离线表分片大小")
    private static Integer WECHAT_CUSTOMER_ALL_FRAG_SIZE = 100;

    @Switch(name = "customerRelationCrowdTagSyncProcessorStartIndex", description = "企微客户诸葛标签同步定时任务开始序号")
    private static Integer CUSTOMER_RELATION_CROWD_TAG_SYNC_PROCESSOR_START_INDEX = 0;

    @Switch(name = "customerRelationCrowdTagSyncProcessorEndIndex", description = "企微客户诸葛标签同步定时任务结束序号")
    private static Integer CUSTOMER_RELATION_CROWD_TAG_SYNC_PROCESSOR_END_INDEX = 100;

    private static final String TASK_NAME = "CUSTOMER_RELATION_CROWD_TAG_SYNC";

    @Override
    public ProcessResult process(JobContext jobContext) throws Exception {
        try {
            if (isRootTask(jobContext)) {
                for (int i = CUSTOMER_RELATION_CROWD_TAG_SYNC_PROCESSOR_START_INDEX; i < CUSTOMER_RELATION_CROWD_TAG_SYNC_PROCESSOR_END_INDEX; i++) {
                    map(Collections.singletonList(i), TASK_NAME);
                }
            } else if (TASK_NAME.equals(jobContext.getTaskName())) {
                return processSubTask((int) jobContext.getTask());
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("客户关系诸葛标签同步定时任务执行, 任务执行出错", e.getMessage(), e, LogListUtil.newArrayList(jobContext));
            return new ProcessResult(false);
        }
        return new ProcessResult(true);
    }

    /**
     * 处理子任务的方法。
     *
     * @param subTaskIndex 子任务序号
     * @return 返回处理结果
     */
    private ProcessResult processSubTask(int subTaskIndex) {
        String subSql = String.format(WECHAT_CUSTOMER_ALL_SEARCH_ODPS_SQL, WECHAT_CUSTOMER_ALL_FRAG_SIZE, subTaskIndex);
        try {
            // 获取下载会话
            InstanceTunnel.DownloadSession downloadSession = odpsManager.getDefaultDownloadSession(subSql);

            if (downloadSession == null) {
                Thread.sleep(CUSTOMER_RELATION_TAG_SYNC_ODPS_SEARCH_SLEEP_PERIOD);
                downloadSession = odpsManager.getDefaultDownloadSession(subSql);
            }

            // 检查下载会话是否为null
            if (downloadSession == null) {
                // 记录失败日志并抛出异常
                PlatformLogUtil.logFail("客户关系诸葛标签同步定时任务执行，下载odps数据出错", LogListUtil.newArrayList(subSql));
                throw new TripscrmException(TripSCRMErrorCode.QUERY_ODPS_FAIL);
            }

            // 获取记录总数
            long recordCount = downloadSession.getRecordCount();

            // 如果记录数为0，则记录失败日志并抛出异常
            if (recordCount == 0) {
                PlatformLogUtil.logFail("客户关系诸葛标签同步定时任务执行，odps数据为空", LogListUtil.newArrayList(subSql));
                throw new TripscrmException(TripSCRMErrorCode.EMPTY_ODPS_DATA);
            }

            // 记录成功日志
            PlatformLogUtil.logInfo("客户关系诸葛标签同步定时任务执行，odps数据查询完毕", LogListUtil.newArrayList(subTaskIndex, recordCount));

            int successCount = 0;
            int failCount = 0;
            // 使用 try-with-resources 确保 RecordReader 正确关闭
            try (RecordReader recordReader = downloadSession.openRecordReader(0, recordCount)) {
                Record record;
                // 循环读取记录
                while ((record = recordReader.read()) != null) {
                    String externalUserId = record.getString("external_user_id");
                    String corpId = record.getString("corp_id");
                    String unionId = record.getString("union_id");
                    String uid = wechatCustomerService.getUidByExternalUserIdAndCorpIdAndUnionId(externalUserId, corpId, unionId);
                    tagRelationService.addBasicTag(externalUserId, corpId, uid);
                    if (!StringUtils.hasLength(uid)) {
                        PlatformLogUtil.logFail("客户关系诸葛标签同步定时任务执行，子任务执行失败, 查询客户淘宝id失败", LogListUtil.newArrayList(externalUserId, unionId, corpId));
                        failCount++;
                        continue;
                    }
                    boolean result = wechatCustomerService.updateCustomerRelationCrowdTag(unionId, uid);
                    if (!result) {
                        PlatformLogUtil.logFail("客户关系诸葛标签同步定时任务执行，子任务执行失败, 客户标签更新失败", LogListUtil.newArrayList(externalUserId, unionId, corpId));
                        failCount++;
                        continue;
                    }
                    successCount++;

                }
                PlatformLogUtil.logInfo("客户关系诸葛标签同步定时任务执行，子任务执行完毕", LogListUtil.newArrayList(subTaskIndex, recordCount, successCount, failCount));
                return new ProcessResult(true);
            } catch (IOException e) {
                PlatformLogUtil.logException("读取ODPS记录时发生IO异常", e.getMessage(), e, LogListUtil.newArrayList(downloadSession, recordCount, subTaskIndex));
                return new ProcessResult(false);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("客户关系诸葛标签同步定时任务执行，子任务执行异常", e.getMessage(), e, LogListUtil.newArrayList(subTaskIndex));
            return new ProcessResult(false);
        }
    }

    /**
     * 手动触发子任务
     * @param startIndex
     * @param endIndex
     */
    @AteyeInvoker(description = "手动触发企微客户诸葛标签同步定时任务", paraDesc = "startIndex&endIndex")
    public void processSubTask(Integer startIndex, Integer endIndex) {
        for (int i = startIndex; i < endIndex; i++) {
            processSubTask(i);
        }
    }
}
