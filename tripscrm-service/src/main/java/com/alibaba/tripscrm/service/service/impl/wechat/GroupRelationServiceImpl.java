package com.alibaba.tripscrm.service.service.impl.wechat;

import com.alibaba.tripscrm.dal.mapper.tddl.GroupRelationMapper;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.model.domain.query.GroupMemberCountQuery;
import com.alibaba.tripscrm.dal.model.domain.result.GroupMemberCountResult;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.dal.repository.WechatGroupRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupInfoMemberVO;
import com.alibaba.tripscrm.service.service.wechat.GroupRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.model.UserGroupConfigBO;
import com.alibaba.tripzoo.proxy.request.UserGroupConfigRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

;

/**
 * 群成员 Manager实现类
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j
public class GroupRelationServiceImpl implements GroupRelationService {
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private GroupRelationRepository groupRelationRepository;
    @Resource
    private GroupRelationMapper groupRelationMapper;
    @Resource
    private WechatCustomerService wechatCustomerService;
    @Resource
    private WechatGroupRepository wechatGroupRepository;
    @Resource
    private GroupService groupService;

    @Override
    public Map<String, Integer> batchGetGroupMemberCount(List<String> chatIdList) {
        Map<String, Integer> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(chatIdList)) {
            return map;
        }

        List<List<String>> partitions = Lists.partition(chatIdList, 3);
        for (List<String> partition : partitions) {
            GroupMemberCountQuery groupMemberCountQuery = new GroupMemberCountQuery();
            groupMemberCountQuery.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
            List<String> shardingKeyList = partition.stream().map(groupRelationRepository::chatId2ShardingKey).collect(Collectors.toList());
            groupMemberCountQuery.setShardingKeyList(shardingKeyList);
            groupMemberCountQuery.setStatus(GroupRelationStatusEnum.MEMBER.getCode().byteValue());
            List<GroupMemberCountResult> memberCountList = groupRelationMapper.countByShardingKeyList(groupMemberCountQuery);
            for (GroupMemberCountResult groupMemberCountResult : memberCountList) {
                map.put(groupRelationRepository.shardingKey2ChatIdOrUserId(groupMemberCountResult.getShardingKey()), groupMemberCountResult.getCount().intValue());
            }
        }

        return map;
    }

    @Override
    public List<WechatGroupInfoMemberVO> listByChatId(String chatId, String corpId) {
        // 校验
        if (StringUtils.isBlank(chatId)) {
            return Lists.newArrayList();
        }
        WechatGroupDO wechatGroupDO = wechatGroupRepository.getByChatIdAndCorpId(chatId, corpId);
        if (wechatGroupDO == null) {
            return Lists.newArrayList();
        }
        // 查询
        return listByChatIdAndUserType(chatId, Lists.newArrayList(GroupUserTypeEnum.USER, GroupUserTypeEnum.CUSTOMER));
    }

    @Override
    public List<WechatGroupInfoMemberVO> listByChatIdAndUserType(String chatId, List<GroupUserTypeEnum> userTypeEnumList) {
        // 参数校验
        if (StringUtils.isBlank(chatId) || CollectionUtils.isEmpty(userTypeEnumList)) {
            return Lists.newArrayList();
        }
        // 成员列表
        List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKey(groupRelationRepository.chatId2ShardingKey(chatId)
                , SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isEmpty(groupRelationList)) {
            return Lists.newArrayList();
        }
        List<Byte> userTypeCodeList = userTypeEnumList.stream().map(userTypeEnum -> userTypeEnum.getCode().byteValue()).collect(Collectors.toList());
        Map<Byte, List<GroupRelationDO>> userTypeGroupMap = groupRelationList.stream()
                .filter(groupRelationDO -> userTypeCodeList.contains(groupRelationDO.getUserType())).collect(Collectors.groupingBy(GroupRelationDO::getUserType));
        if (MapUtils.isEmpty(userTypeGroupMap)) {
            return Lists.newArrayList();
        }
        // 信息填充
        ArrayList<WechatGroupInfoMemberVO> result = new ArrayList<>();
        for (Map.Entry<Byte, List<GroupRelationDO>> entry : userTypeGroupMap.entrySet()) {
            Set<String> userIdList = entry.getValue().stream().map(GroupRelationDO::getUserId).collect(Collectors.toSet());
            result.addAll(buildGroupUserInfo(chatId, GroupUserTypeEnum.of(entry.getKey().intValue()), new ArrayList<>(userIdList)));
        }
        return result;
    }

    /**
     * 构建群聊成员信息
     * @param chatId 群聊Id
     * @param userTypeEnum 成员类型code
     * @param userIdList 成员id列表
     */
    private List<WechatGroupInfoMemberVO> buildGroupUserInfo(String chatId, GroupUserTypeEnum userTypeEnum, List<String> userIdList) {
        if (userTypeEnum == null || StringUtils.isBlank(chatId) || CollectionUtils.isEmpty(userIdList)) {
            return Lists.newArrayList();
        }
        // 群聊校验
        WechatGroupDO wechatGroupDO = wechatGroupRepository.getByChatIdAndCorpId(chatId, SpaceInfoThreadLocalUtils.getCorpId());
        if (wechatGroupDO == null) {
            return Lists.newArrayList();
        }
        // 数据获取
        List<WechatGroupInfoMemberVO> result = new ArrayList<>();
        switch (userTypeEnum) {
            case USER:
                result = buildGroupStaffInfo(wechatGroupDO, userIdList);
                break;
            case CUSTOMER:
                result = buildGroupCustomerInfo(wechatGroupDO, userIdList);
                break;
            default:
                break;
        }
        return result;
    }

    /**
     * 构建群聊中企业成员的信息
     * @param wechatGroupDO 群聊信息
     * @param userIdList 企微成员id列表
     * @return 群成员对象
     */
    private List<WechatGroupInfoMemberVO> buildGroupStaffInfo(WechatGroupDO wechatGroupDO, List<String> userIdList) {
        if (CollectionUtils.isEmpty(userIdList) || wechatGroupDO == null) {
            return Lists.newArrayList();
        }
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(userIdList);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return Lists.newArrayList();
        }
        ArrayList<WechatGroupInfoMemberVO> result = new ArrayList<>();
        // 群信息获取
        String ownerUserId = Optional.ofNullable(wechatGroupDO.getOwnerUser()).orElse("");
        List<String> adminUserIdList = Arrays.stream(Optional.ofNullable(wechatGroupDO.getAdminUser()).orElse("")
                .split(",")).collect(Collectors.toList());
        // 基本信息填充
        for (WechatUserDTO wechatUserDTO : wechatUserList) {
            WechatGroupInfoMemberVO memberVO = new WechatGroupInfoMemberVO();
            memberVO.setUserId(wechatUserDTO.getUserId());
            memberVO.setUserType(GroupUserTypeEnum.USER.getCode().byteValue());
            memberVO.setName(wechatUserDTO.getName());
            memberVO.setAvatar(wechatUserDTO.getAvatarUrl());
            memberVO.setOwner(ownerUserId.equals(wechatUserDTO.getUserId()));
            memberVO.setIsAdmin(adminUserIdList.contains(wechatUserDTO.getUserId()));
            memberVO.setCorpName(wechatUserDTO.getCorpName());
            result.add(memberVO);
        }
        // 群配置信息填充
        fillStaffGroupConfigInfo(wechatGroupDO, result);
        return result;
    }

    /**
     * 填充群成员的群配置信息
     * @param wechatGroupDO 群聊信息
     * @param wechatGroupInfoMemberList 企微群成员列表
     */
    private void fillStaffGroupConfigInfo(WechatGroupDO wechatGroupDO, List<WechatGroupInfoMemberVO> wechatGroupInfoMemberList) {
        UserGroupConfigRequest request = new UserGroupConfigRequest();
        request.setChatIdList(Lists.newArrayList(wechatGroupDO.getChatId()));
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<List<UserGroupConfigBO>> resultDO = groupService
                .getUserGroupConfigInfo(request);
        if (!resultDO.getSuccess() || CollectionUtils.isEmpty(resultDO.getModel())) {
            return ;
        }
        Map<String, UserGroupConfigBO> userGroupConfigMap = resultDO.getModel().stream().collect(Collectors.toMap(UserGroupConfigBO::getUserId, userGroupConfigBO -> userGroupConfigBO));
        for (WechatGroupInfoMemberVO memberVO : wechatGroupInfoMemberList) {
            memberVO.setInConcat(Optional.ofNullable(userGroupConfigMap.getOrDefault(memberVO.getUserId(), new UserGroupConfigBO()).getInConcat()).orElse((byte)0));
            memberVO.setIsOpen(Optional.ofNullable(userGroupConfigMap.getOrDefault(memberVO.getUserId(), new UserGroupConfigBO()).getIsOpen()).orElse((byte)0));
        }
    }

    /**
     * 构建群聊中客户
     * @param wechatGroupDO 群聊信息
     * @param externalUserIdList 企微客户id列表
     * @return 群成员对象
     */
    private List<WechatGroupInfoMemberVO> buildGroupCustomerInfo(WechatGroupDO wechatGroupDO, List<String> externalUserIdList) {
        if (CollectionUtils.isEmpty(externalUserIdList) || wechatGroupDO == null) {
            return Lists.newArrayList();
        }
        List<WechatCustomerVO> wechatCustomerList = wechatCustomerService.listByExternalUserIdList(externalUserIdList);
        if (CollectionUtils.isEmpty(wechatCustomerList)) {
            return Lists.newArrayList();
        }
        ArrayList<WechatGroupInfoMemberVO> result = new ArrayList<>();
        // 群信息获取
        String ownerUserId = Optional.ofNullable(wechatGroupDO.getOwnerUser()).orElse("");
        List<String> adminUserIdList = Arrays.stream(Optional.ofNullable(wechatGroupDO.getAdminUser()).orElse("")
                .split(",")).collect(Collectors.toList());
        // 客户信息填充
        for (WechatCustomerVO wechatCustomerVO : wechatCustomerList) {
            WechatGroupInfoMemberVO memberVO = new WechatGroupInfoMemberVO();
            memberVO.setUserId(wechatCustomerVO.getExternalUserId());
            memberVO.setUserType(GroupUserTypeEnum.CUSTOMER.getCode().byteValue());
            memberVO.setName(wechatCustomerVO.getName());
            memberVO.setAvatar(wechatCustomerVO.getAvatarUrl());
            memberVO.setOwner(ownerUserId.equals(wechatCustomerVO.getExternalUserId()));
            memberVO.setIsAdmin(adminUserIdList.contains(wechatCustomerVO.getExternalUserId()));
            memberVO.setCorpName("微信");
            result.add(memberVO);
        }
        // 未知客户信息填充
        if (externalUserIdList.size() > wechatCustomerList.size()) {
            List<String> customerUserIdList = wechatCustomerList.stream().map(WechatCustomerVO::getExternalUserId).collect(Collectors.toList());
            List<String> unKnowCustomer = new ArrayList<>(CollectionUtils.subtract(externalUserIdList, customerUserIdList));
            for (String externalUserId : unKnowCustomer) {
                WechatGroupInfoMemberVO memberVO = new WechatGroupInfoMemberVO();
                memberVO.setUserId(externalUserId);
                memberVO.setUserType(GroupUserTypeEnum.CUSTOMER.getCode().byteValue());
                memberVO.setName("未知客户");
                memberVO.setOwner(ownerUserId.equals(externalUserId));
                memberVO.setIsAdmin(adminUserIdList.contains(externalUserId));
                memberVO.setCorpName("微信");
                result.add(memberVO);
            }
        }
        return result;
    }

}