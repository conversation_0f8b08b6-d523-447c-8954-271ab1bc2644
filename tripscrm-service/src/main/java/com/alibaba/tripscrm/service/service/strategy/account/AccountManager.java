package com.alibaba.tripscrm.service.service.strategy.account;

import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.UserInfoVO;

import java.util.List;

/**
 * 账号管理接口
 * <AUTHOR>
 */
public interface AccountManager {

    /**
     * 是否支持当前类型
     * @param serverName 当前请求域名
     * @return 是否支持
     */
    boolean support(String serverName);

    /**
     * 是否支持当前类型
     * @param serverName 当前请求域名
     * @return 是否支持
     */
    boolean supportQuery(String serverName);

    /**
     * 获取用户信息，只能在web主线程中使用
     * @return 用户信息
     */
    User getUserInWebThread();

    /**
     * 获取用户姓名-账号
     *
     * @return 用户信息
     */
    String getUserNameAndEmpId();


    /**
     * 根据账号获取用户信息
     *
     * @param accountId 账号
     * @return 用户信息
     */
    UserInfoVO getUserAndAccountIdByAccountId(String accountId);

    /**
     * 批量根据账号获取用户信息
     *
     * @param accountIds 账号列表
     * @return 用户信息列表
     */
    List<UserInfoVO> batchGetUserByEmpId(List<String> accountIds);

    /**
     * 根据用户id获取用户信息
     * @param accountId 账号id
     * @return 用户信息
     */
    User getUserByAccountId(String accountId);


    /**
     * 搜索用户信息
     * @param keyword 关键字
     * @return 员工信息
     */
    List<UserInfoVO> searchUser(String keyword);

    /**
     * 从当前线程中获取用户信息
     * @return UserInfoVO
     */
    UserInfoVO getUserVoInWebThread();

}
