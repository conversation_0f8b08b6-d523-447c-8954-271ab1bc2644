package com.alibaba.tripscrm.service.service.impl.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatGroupPoolMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupPoolDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupPoolParam;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.convert.WechatGroupPoolConverter;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupPoolQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatGroupPoolDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupPoolService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.alibaba.tripzoo.proxy.request.wechat.group.GroupModifyConfigRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.enterprisewechat.GroupModifyConfigResponse;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatGroupPoolServiceImpl implements WechatGroupPoolService {
    private final WechatGroupPoolService wechatGroupPoolService;
    private final WechatGroupPoolMapper wechatGroupPoolMapper;
    private final WechatGroupPoolConverter wechatGroupPoolConverter;
    private final WechatGroupService wechatGroupService;
    private final WechatUserService wechatUserService;
    private final GroupService groupService;
    private final LdbTairManager ldbTairManager;
    private final MetaqProducer metaqProducer;

    private final static Date MIN_DATE = new Date(1753239600000L);

    @Override
    public void syncAll() {
        for (long minId = 1; minId >= 0; ) {
            WechatGroupPoolParam param = new WechatGroupPoolParam();
            WechatGroupPoolParam.Criteria criteria = param.or();
            criteria.andCorpIdEqualTo(SpaceInfoThreadLocalUtils.getCorpId());
            criteria.andIdGreaterThanOrEqualTo(minId);
            criteria.andDeletedEqualTo((byte) 0);
            criteria.andEnvEqualTo(EnvUtils.getEnvironment());
            param.setPagination(1, 300);
            param.appendOrderByClause(WechatGroupPoolParam.OrderCondition.ID, WechatGroupPoolParam.SortType.ASC);
            List<WechatGroupPoolDO> list = wechatGroupPoolMapper.selectByParam(param);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            for (WechatGroupPoolDO wechatGroupPoolDO : list) {
                wechatGroupPoolService.sync(wechatGroupPoolDO.getChatId());
            }
            minId = list.stream().mapToLong(WechatGroupPoolDO::getId).max().orElse(-1L) + 1;
        }
    }

    @Override
    public long countByCondition(WechatGroupPoolQuery query) {
        WechatGroupPoolParam wechatGroupPoolParam = buildParam(query);
        return wechatGroupPoolMapper.countByParam(wechatGroupPoolParam);
    }

    @Override
    public List<WechatGroupPoolDTO> listByCondition(WechatGroupPoolQuery query) {
        WechatGroupPoolParam wechatGroupPoolParam = buildParam(query);
        List<WechatGroupPoolDO> list = wechatGroupPoolMapper.selectByParam(wechatGroupPoolParam);

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(wechatGroupPoolConverter::convertFromDO).collect(Collectors.toList());
    }

    public Integer compareAndMarkAsUsed(Long id, String sellerId) {
        WechatGroupPoolQuery query = new WechatGroupPoolQuery();
        query.setId(id);
        query.setStatus((byte) 0);
        WechatGroupPoolDTO record = new WechatGroupPoolDTO();
        record.setStatus((byte) 1);
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("sellerId", sellerId);
        record.setExtraInfo(extraInfo.toJSONString());
        WechatGroupPoolParam wechatGroupPoolParam = buildParam(query);
        WechatGroupPoolDO wechatGroupPoolDO = wechatGroupPoolConverter.convertFromDTO(record);
        return wechatGroupPoolMapper.updateByParamSelective(wechatGroupPoolDO, wechatGroupPoolParam);
    }

    @Override
    @TairLock(value = "'WechatGroupPoolService.getOneValidGroupAndMarkUsed' + #userId + '_' + #corpId", waitMilliseconds = 3000)
    public TripSCRMResult<WechatGroupPoolDTO> getOneValidGroupAndMarkUsed(String userId, String corpId, String sellerId) {
        WechatGroupPoolQuery query = new WechatGroupPoolQuery();
        query.setOwner(userId);
        query.setCorpId(corpId);
        query.setDeleted((byte) 0);
        query.setStatus((byte) 0);
        List<WechatGroupPoolDTO> wechatGroupPoolList = listByCondition(query);
        if (CollectionUtils.isEmpty(wechatGroupPoolList)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_GROUP);
        }

        // 重试5次
        for (int i = 0; i < 5; i++) {
            WechatGroupPoolDTO wechatGroupPoolDTO = wechatGroupPoolList.get(ThreadLocalRandom.current().nextInt(wechatGroupPoolList.size()));
            if (compareAndMarkAsUsed(wechatGroupPoolDTO.getId(), sellerId) > 0) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("wechatUserId", wechatGroupPoolDTO.getOwner());
                metaqProducer.send(MQEnum.SCRM_SELLER_CORP_VALID_PLATFORM_WECHAT_USER_SYNC, null, null, jsonObject.toJSONString());
                return TripSCRMResult.success(wechatGroupPoolDTO);
            }
        }

        return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_GROUP);
    }

    @AteyeInvoker(description = "刷新企微群聊池数据", paraDesc = "chatId")
    public void testSync(String chatId) {
        try {
            SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.sellerCorpId);
            wechatGroupPoolService.sync(chatId);
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    @Override
    @TairLock(value = "'WechatGroupPoolService.sync' + #chatId", waitMilliseconds = 5000)
    public void sync(String chatId) {
        if (!StringUtils.hasText(chatId)) {
            return;
        }

        WechatGroupPoolQuery query = new WechatGroupPoolQuery();
        query.setChatIdList(Lists.newArrayList(chatId));
        query.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        List<WechatGroupPoolDTO> wechatGroupPoolList = listByCondition(query);
        if (CollectionUtils.isEmpty(wechatGroupPoolList)) {
            return;
        }

        WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(chatId);
        WechatGroupPoolDTO wechatGroupPoolDTO = new WechatGroupPoolDTO();
        wechatGroupPoolDTO.setChatId(chatId);
        if (Objects.isNull(wechatGroupDO) || Objects.equals(wechatGroupDO.getIsDeleted(), (byte) 1)) {
            wechatGroupPoolDTO.setDeleted((byte) 1);
        } else {
            wechatGroupPoolDTO.setOwner(wechatGroupDO.getOwnerUser());
        }

        if (disableInviteConfirm(chatId, wechatGroupPoolList.get(0), wechatGroupDO)) {
            JSONObject jsonObject = JSONObject.parseObject(wechatGroupPoolList.get(0).getExtraInfo());
            jsonObject.put("version", 2);
            wechatGroupPoolDTO.setExtraInfo(jsonObject.toJSONString());
        }

        updateSelective(wechatGroupPoolDTO, query);
    }

    private boolean disableInviteConfirm(String chatId, WechatGroupPoolDTO wechatGroupPoolDTO, WechatGroupDO wechatGroupDO) {
        JSONObject jsonObject = JSONObject.parseObject(wechatGroupPoolDTO.getExtraInfo());
        if (jsonObject.containsKey("version") || wechatGroupPoolDTO.getGmtCreate().compareTo(MIN_DATE) > 0) {
            return false;
        }


        String corpId = wechatGroupDO.getCorpId();
        String userId = wechatGroupDO.getOwnerUser();

        Long minuteWindowVersion = DateUtils.getMinuteVersion(new Date());
        Long hourWindowVersion = minuteWindowVersion / 60;
        Long dayWindowVersion = DateUtils.getDayVersion(new Date());
        String minuteWindowKey = TairConstant.WECHAT_USER_CREATE_GROUP_COUNT_IN_WINDOW_PREFIX + "yyy_" + minuteWindowVersion + "_m_" + corpId + "_" + userId;
        String hourWindowKey = TairConstant.WECHAT_USER_CREATE_GROUP_COUNT_IN_WINDOW_PREFIX + "yyy_" + hourWindowVersion + "_h_" + corpId + "_" + userId;
        String dayWindowKey = TairConstant.WECHAT_USER_CREATE_GROUP_COUNT_IN_WINDOW_PREFIX + "yyy_" + dayWindowVersion + "_d_" + corpId + "_" + userId;
        Integer hourCreateCount = (Integer) ldbTairManager.get(hourWindowKey);
        if (Optional.ofNullable(hourCreateCount).orElse(0) > SwitchConfig.wechatGroupModifyConfigCountPerHour) {
            PlatformLogUtil.logFail("企微群聊池刷新-修改群聊配置，当前账号触发小时级创建群聊数量上限", LogListUtil.newArrayList(hourWindowKey, hourCreateCount, userId, corpId));
            return false;
        }
        Integer minuteCreateCount = (Integer) ldbTairManager.get(minuteWindowKey);
        if (Optional.ofNullable(minuteCreateCount).orElse(0) > SwitchConfig.wechatGroupModifyConfigCountPerMinute) {
            PlatformLogUtil.logFail("企微群聊池刷新-修改群聊配置，当前账号触发分钟级创建群聊数量上限", LogListUtil.newArrayList(minuteWindowKey, minuteCreateCount, userId, corpId));
            return false;
        }
        Integer dayCreateCount = (Integer) ldbTairManager.get(dayWindowKey);
        if (Optional.ofNullable(dayCreateCount).orElse(0) > SwitchConfig.wechatGroupModifyConfigCountPerDay) {
            PlatformLogUtil.logFail("企微群聊池刷新-修改群聊配置，当前账号触发天级创建群聊数量上限", LogListUtil.newArrayList(dayWindowKey, dayCreateCount, userId, corpId));
            return false;
        }

        WechatUserQuery query = new WechatUserQuery();
        query.setCorpId(SwitchConfig.sellerCorpId);
        query.setSpaceId(SwitchConfig.sellerCorpPlatformSpaceId);
        query.setStatus(WechatUserStatusEnum.ACTIVE.getCode().byteValue());
        query.setUserIdList(Lists.newArrayList(userId));
        query.setOnlineStatus(RobotStatusEnum.ONLINE.getCode().byteValue());
        List<WechatUserDTO> wechatUserList = wechatUserService.listValidWechatUserByCondition(query);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            PlatformLogUtil.logFail("企微群聊池刷新-修改群聊配置，企微号不在线", LogListUtil.newArrayList());
            return false;
        }

        // 存量群聊，且不可邀请入群
        GroupModifyConfigRequest request = new GroupModifyConfigRequest();
        request.setChatId(chatId);
        request.setUserId(wechatGroupDO.getOwnerUser());
        request.setCorpId(wechatGroupDO.getCorpId());
        request.setDisableRename(true);
        request.setDisableAddContact(true);
        request.setEnableInviteConfirm(false);
        ResultDO<GroupModifyConfigResponse> resultDO = groupService.asyncModifyConfig(request);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
            PlatformLogUtil.logFail("企微群聊池刷新-修改群聊配置，执行失败", LogListUtil.newArrayList(request, resultDO));
            return false;
        }

        ldbTairManager.incr(minuteWindowKey, 1, 0, 100);
        ldbTairManager.incr(hourWindowKey, 1, 0, 4000);
        ldbTairManager.incr(dayWindowKey, 1, 0, 90000);
        return resultDO.getSuccess();
    }


    @Override
    public int insertSelective(WechatGroupPoolDTO record) {
        if (Objects.isNull(record) || !StringUtils.hasText(record.getCorpId()) || !StringUtils.hasText(record.getChatId()) || !StringUtils.hasText(record.getOwner())) {
            throw new TripscrmException(ErrorCodeEnum.PARAM_INVALID);
        }

        record.setEnv(EnvUtils.getEnvironment());
        WechatGroupPoolDO wechatGroupPoolDO = wechatGroupPoolConverter.convertFromDTO(record);
        return wechatGroupPoolMapper.insertSelective(wechatGroupPoolDO);
    }

    @Override
    public int updateSelective(WechatGroupPoolDTO record, WechatGroupPoolQuery query) {
        if (Objects.isNull(record) || Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WechatGroupPoolParam wechatGroupPoolParam = buildParam(query);
        WechatGroupPoolDO wechatGroupPoolDO = wechatGroupPoolConverter.convertFromDTO(record);
        return wechatGroupPoolMapper.updateByParamSelective(wechatGroupPoolDO, wechatGroupPoolParam);
    }

    private static WechatGroupPoolParam buildParam(WechatGroupPoolQuery query) {
        if (Objects.isNull(query) || (!NumberUtils.biggerThanZero(query.getId()) && Objects.isNull(query.getCorpId()))) {
            throw new TripscrmException(ErrorCodeEnum.PARAM_INVALID);
        }

        WechatGroupPoolParam param = new WechatGroupPoolParam();
        WechatGroupPoolParam.Criteria criteria = param.or();
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());

        if (StringUtils.hasText(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }
        if (NumberUtils.biggerThanZero(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (StringUtils.hasText(query.getOwner())) {
            criteria.andOwnerEqualTo(query.getOwner());
        }
        if (!CollectionUtils.isEmpty(query.getChatIdList())) {
            criteria.andChatIdIn(query.getChatIdList());
        }
        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        return param;
    }
}