package com.alibaba.tripscrm.service.service.task.ability.factory;

import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务工厂，获取不同任务的实现
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
@Slf4j
@Component
public class TaskExecutorFactory {

    public static Map<String, AbstractTaskExecutor> taskExecutorMap = new HashMap<>();

    /**
     * 初始化任务类工厂
     */
    public static void registry(TaskType taskType, AbstractTaskExecutor executor) {
        taskExecutorMap.put(taskType.getCode(), executor);
    }

    /**
     * 工厂获取任务实现类bean
     */
    public static AbstractTaskExecutor getTaskBeanByType(TaskType taskType) {
        return taskExecutorMap.get(taskType.getCode());
    }
}
