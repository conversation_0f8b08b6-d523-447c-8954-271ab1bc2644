package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

/**
 * 用户会话 创建对象
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class ChatConversationCreateParam {
    /**
     * 企微成员id
     */
    private String userId;
    /**
     * 企微组织id
     */
    private String corpId;
    /**
     * 会话id 1:群聊会话id，2:与企微客户的私聊会话id，3:与企微成员的私聊会话id
     */
    private String chatId;
    /**
     * 会话类型 1:群聊，2:与企微客户的私聊，3:与企微成员的私聊
     * @see ChatTypeEnum
     */
    private Integer chatType;
}