package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.enums.wechat.CreateWeComRecordStatusEnum;
import com.alibaba.tripscrm.service.model.dto.wechat.CreateWeComRecordDTO;
import com.alibaba.tripscrm.service.service.CreateWeComRecordService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 申请链接上号审核结果【比邻回调】
 *
 * <AUTHOR>
 * @date 2025-01-21 13:53
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ApplyWeComRobotByLinkApprovalCallbackProcessor implements ProxyCallbackProcessor {

    private final CreateWeComRecordService createWeComRecordService;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.APPLY_WE_COM_ROBOT_BY_LINK_APPROVAL;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (Objects.isNull(scrmCallbackMsg.getContent()) || !scrmCallbackMsg.getResult() || StringUtils.isEmpty(scrmCallbackMsg.getRequestId())) {
            PlatformLogUtil.logFail("接收申请链接上号审核结果回调失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }

        Boolean allow = Boolean.parseBoolean(scrmCallbackMsg.getContent());
        PlatformLogUtil.logInfo("接收申请链接上号审核结果回调成功", LogListUtil.newArrayList(scrmCallbackMsg));
        // 根据requestId匹配上号记录
        CreateWeComRecordDTO createWeComRecordDTO = createWeComRecordService.selectByDateAndOrderId(new Date(System.currentTimeMillis() -  24L * 3 * 60 * 60 * 1000), new Date(), scrmCallbackMsg.getRequestId());
        if (Objects.isNull(createWeComRecordDTO)) {
            PlatformLogUtil.logFail("未查询到上号记录", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        // 更新上号记录申请状态
        CreateWeComRecordDTO newRecord = new CreateWeComRecordDTO();
        newRecord.setId(createWeComRecordDTO.getId());
        if (!allow) {
            newRecord.setStatus( CreateWeComRecordStatusEnum.REVIEW_FAIL.getCode());
        }
        Integer effectLines = createWeComRecordService.updateByIdSelective(newRecord);
        if (effectLines < 1) {
            PlatformLogUtil.logFail("更新上号记录申请状态失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        return true;
    }
}
