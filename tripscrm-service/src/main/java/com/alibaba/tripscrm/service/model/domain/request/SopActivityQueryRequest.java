package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SopActivityQueryRequest extends BasePageRequest implements Serializable {
    private static final long serialVersionUID = -4284265202362983343L;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 创建者
     */
    private String creatorId;

    /**
     * 活动状态
     */
    private String status;

    /**
     * 管理员
     */
    private String memberIds;

    /**
     * 规则类型
     */
    private Integer sopType;

    /**
     * 创建时间（开始时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createStartTime;

    /**
     * 创建时间（结束时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createEndTime;

    /**
     * 空间 ID
     */
    private Long spaceId;
}
