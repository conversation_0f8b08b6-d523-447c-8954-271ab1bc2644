package com.alibaba.tripscrm.service.service.strategy.websocket.param.response;

import lombok.Data;

import java.util.Date;

/**
 * 获取当前企微号的会话列表 推送体
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class GetUserConversationResponseBody {
    /**
     * 企微成员ID
     */
    private String userId;
    /**
     * 企微组织ID
     */
    private String corpId;
    /**
     * 会话名称
     */
    private String chatName;
    /**
     * 会话头像
     */
    private String chatAvatar;
    /**
     * 会话id 1:群聊会话id，2:私聊时，企微客户的userId，3:私聊时，企微成员的userId
     */
    private String chatId;
    /**
     * 会话类型 1:群聊，2:与企微客户的私聊，3:与企微成员的私聊
     * @see ChatTypeEnum
     */
    private Integer chatType;
    /**
     * 所属组织名称
     */
    private String corpName;
    /**
     * 会话中最后一条消息发送人名称
     */
    private String lastMessageSenderName;
    /**
     * 会话中最后一条消息内容
     */
    private String lastMessageContent;
    /**
     * 会话中最后一条消息时间
     */
    private Date lastMessageCreateTime;
    /**
     * 会话更新时间戳（用于会话排序）
     */
    private Long updateTimestamp;
    /**
     * 未读消息数量
     */
    private Integer unreadCount;
    /**
     * 置顶序号，Sequence生成，0表示未置顶
     */
    private Integer topNo;
    /**
     * 是否免打扰
     */
    private Boolean dnd;
    /**
     * 该群聊是否关注
     */
    private Boolean groupWork = false;
    /**
     * 客户管理组ID
     */
    private Long customerManageGroupId;
    /**
     * 客户管理组名称
     */
    private String customerManageGroupName;
//    /**
//     * 群头像
//     */
//    private List<String> groupAvatar;
}
