package com.alibaba.tripscrm.service.service.impl.hsf;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.TripSCRMEnterpriseWechatAddFriendByMobileResponse;
import com.alibaba.tripscrm.domain.TripSCRMEnterpriseWechatRobotStatusResponse;
import com.alibaba.tripscrm.domain.TripSCRMEnterpriseWechatUserResponse;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.RobotStatusEnum;
import com.alibaba.tripscrm.domain.request.*;
import com.alibaba.tripscrm.domain.response.wechat.group.TripSCRMEnterpriseWechatTransferCustomerRes;
import com.alibaba.tripscrm.domain.response.wechat.group.TripSCRMEnterpriseWechatTransferCustomerRes.TripScrmCustomerResult;
import com.alibaba.tripscrm.domain.response.wechat.group.TripSCRMEnterpriseWechatTransferGroupRes;
import com.alibaba.tripscrm.domain.response.wechat.group.TripSCRMEnterpriseWechatTransferGroupRes.TripSCRMFaildGroupResult;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.manager.opensearch.WechatUserOpenSearchManager;
import com.alibaba.tripscrm.service.model.domain.query.WechatUserPageQuery;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.wechat.EnterpriseWechatUserService;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.api.service.RobotService;
import com.alibaba.tripzoo.proxy.model.RobotBO;
import com.alibaba.tripzoo.proxy.request.QueryRobotInfoRequest;
import com.alibaba.tripzoo.proxy.request.WechatSendAddFriendRequest;
import com.alibaba.tripzoo.proxy.request.WechatTransferCustomerRequest;
import com.alibaba.tripzoo.proxy.request.WechatTransferGroupRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.WechatTransferCustomerResponse;
import com.alibaba.tripzoo.proxy.result.WechatTransferCustomerResponse.CustomerResult;
import com.alibaba.tripzoo.proxy.result.WechatTransferGroupResponse;
import com.alibaba.tripzoo.proxy.result.WechatTransferGroupResponse.FailedGroupResult;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.csp.courier.StringUtils;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@HSFProvider(serviceInterface = EnterpriseWechatUserService.class)
public class EnterpriseWechatUserServiceImpl implements EnterpriseWechatUserService {
    @Resource
    private WechatUserOpenSearchManager wechatUserOpenSearchManager;
    @Resource
    private RobotService robotService;
    @Resource
    private CustomerService customerService;
    @Resource
    private EnterpriseWechatManager enterpriseWechatManager;
    @Resource
    private ActivityContextService activityContextService;
    @Resource
    private WechatUserService wechatUserService;

    @Override
    @ServiceLog("查询企微成员的在线状态")
    public TripSCRMResult<TripSCRMEnterpriseWechatRobotStatusResponse> queryRobotStatus(
            TripSCRMEnterpriseWechatRobotStatusQueryRequest queryRequest) {
        QueryRobotInfoRequest queryRobotInfoRequest = new QueryRobotInfoRequest();
        queryRobotInfoRequest.setUserId(queryRequest.getUserId());
        queryRobotInfoRequest.setCorpId(queryRequest.getCorpId());
        queryRobotInfoRequest.setIsvType(null);

        ResultDO<List<RobotBO>> listResultDO = queryRequest.isQueryLastedStatus() ? robotService.queryRobotStatusFromIsv(queryRobotInfoRequest) : robotService.queryRobotInfo(queryRobotInfoRequest);
        if (!listResultDO.getSuccess() || CollectionUtils.isEmpty(listResultDO.getModel())) {
            PlatformLogUtil.logFail("查询企微成员机器人状态，企微成员不存在", LogListUtil.newArrayList(queryRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_NOT_EXISTS);
        }

        TripSCRMEnterpriseWechatRobotStatusResponse response = new TripSCRMEnterpriseWechatRobotStatusResponse();
        response.setCorpId(queryRequest.getCorpId());
        response.setUserId(queryRequest.getUserId());
        response.setStatus(RobotStatusEnum.OFFLINE);
        boolean isRecycle = listResultDO.getModel()
                .stream()
                .allMatch(r -> Objects.equals(RobotStatusEnum.RECYCLED.getCode(), r.getStatus().getCode()));
        if (isRecycle) {
            response.setStatus(RobotStatusEnum.RECYCLED);
        }

        boolean isOnline = listResultDO.getModel()
                .stream()
                .anyMatch(r -> Objects.equals(RobotStatusEnum.ONLINE.getCode(), r.getStatus().getCode()));
        if (isOnline) {
            response.setStatus(RobotStatusEnum.ONLINE);
        }

        return TripSCRMResult.success(response);
    }

    @Override
    @ServiceLog("根据条件查询企微成员支持模糊搜索")
    public TripSCRMResult<List<TripSCRMEnterpriseWechatUserResponse>> query(TripSCRMEnterpriseWechatUserQueryRequest queryRequest) {
        if (!StringUtils.hasText(queryRequest.getUserId()) && !StringUtils.hasText(queryRequest.getNameLike())) {
            PlatformLogUtil.logFail("根据条件查询企微成员支持模糊搜索，参数非法", LogListUtil.newArrayList(queryRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (StringUtils.hasText(queryRequest.getUserId())) {
            WechatUserDTO wechatUserDTO = wechatUserOpenSearchManager.getByUserId(queryRequest.getUserId(), queryRequest.getCorpId());
            if (Objects.isNull(wechatUserDTO)) {
                PlatformLogUtil.logFail("根据userId查询企微成员，企微成员不存在", LogListUtil.newArrayList(queryRequest));
                return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_NOT_EXISTS);
            }
            return TripSCRMResult.success(convert(Lists.newArrayList(wechatUserDTO)));
        }

        WechatUserPageQuery wechatUserPageQuery = new WechatUserPageQuery();
        wechatUserPageQuery.setNameLike(queryRequest.getNameLike());
        wechatUserPageQuery.setCorpId(queryRequest.getCorpId());
        wechatUserPageQuery.setPageSize(queryRequest.getPageSize());
        wechatUserPageQuery.setPageNum(queryRequest.getPageNum());

        PageInfo<WechatUserDTO> userList = wechatUserOpenSearchManager.pageQuery(wechatUserPageQuery);
        List<WechatUserDTO> wechatUserList = Optional.ofNullable(userList).map(PageInfo::getList).orElse(new ArrayList<>());
        return TripSCRMResult.success(convert(wechatUserList));
    }

    private List<TripSCRMEnterpriseWechatUserResponse> convert(List<WechatUserDTO> wechatUserList) {
        List<TripSCRMEnterpriseWechatUserResponse> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return resultList;
        }
        for (WechatUserDTO wechatUserDTO : wechatUserList) {
            TripSCRMEnterpriseWechatUserResponse result = new TripSCRMEnterpriseWechatUserResponse();
            result.setUserId(wechatUserDTO.getUserId());
            result.setCorpId(wechatUserDTO.getCorpId());
            result.setDepartmentId(wechatUserDTO.getDepartmentId());
            result.setName(wechatUserDTO.getName());
            result.setAlias(wechatUserDTO.getAlias());
            result.setRealName(wechatUserDTO.getRealName());
            result.setAvatar(wechatUserDTO.getAvatarUrl());
            result.setGender(wechatUserDTO.getGender().byteValue());
            if (wechatUserDTO.getOnlineStatus() != null) {
                result.setRobotStatus(wechatUserDTO.getOnlineStatus().byteValue());
            }
            if (wechatUserDTO.getRobotType() != null) {
                result.setRobotType(wechatUserDTO.getRobotType().byteValue());
            }
            if (wechatUserDTO.getStatus() != null) {
                result.setStatus(wechatUserDTO.getStatus().byteValue());
            }
            if (wechatUserDTO.getActivateStatus() != null) {
                result.setActivateStatus(wechatUserDTO.getActivateStatus().byteValue());
            }
            resultList.add(result);
        }
        return resultList;
    }

    @Override
    @ServiceLog("通过手机号添加好友")
    public TripSCRMResult<TripSCRMEnterpriseWechatAddFriendByMobileResponse> sendAddFriendRequestByMobile(AddFriendByMobileRequest request) {
        if (!StringUtils.hasText(request.getCorpId()) || !StringUtils.hasText(request.getMobile())) {
            PlatformLogUtil.logFail("通过手机号添加好友，参数非法", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatSendAddFriendRequest proxyRequest = new WechatSendAddFriendRequest();
        proxyRequest.setCorpId(request.getCorpId());
        proxyRequest.setMobile(request.getMobile());
        proxyRequest.setUserId(request.getUserId());
        String state = StringUtils.isEmpty(request.getState())?String.valueOf(activityContextService.generateContextId()):request.getState();
        proxyRequest.setState(state);
        ResultDO<Boolean> resultDO = customerService.sendAddFriendRequestByMobile(proxyRequest);
        if (!resultDO.getSuccess()) {
            PlatformLogUtil.logFail("通过手机号添加好友失败", request.getState(), LogListUtil.newArrayList(request, resultDO));
            return TripSCRMResult.fail(TripSCRMErrorCode.SEND_ADD_FRIEND_REQUEST_BY_MOBILE_ERROR);
        }
        return TripSCRMResult.success(new TripSCRMEnterpriseWechatAddFriendByMobileResponse(state));
    }

    @Override
    @ServiceLog("分配成员客户")
    public TripSCRMResult<TripSCRMEnterpriseWechatTransferCustomerRes> transferCustomer(TransferCustomerRequest request) {
        if (request == null) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatTransferCustomerRequest req = JSON.parseObject(JSON.toJSONString(request), WechatTransferCustomerRequest.class);
        TripSCRMResult<WechatTransferCustomerResponse> result = enterpriseWechatManager.transferCustomer(req);
        if (!result.isSuccess() || result.getData() == null) {
            return TripSCRMResult.fail(result.getCode(), result.getMsg());
        }
        WechatTransferCustomerResponse data = result.getData();
        TripSCRMEnterpriseWechatTransferCustomerRes resp = new TripSCRMEnterpriseWechatTransferCustomerRes();
        if (data.getCustomers() != null) {
            List<TripScrmCustomerResult> customers = new ArrayList<>();
            for (CustomerResult c : data.getCustomers()) {
                TripScrmCustomerResult cc = new TripScrmCustomerResult();
                cc.setExternalUserId(c.getExternalUserId());
                cc.setErrCode(c.getErrCode());
                customers.add(cc);
            }
            resp.setCustomers(customers);
        }
        return TripSCRMResult.success(resp);
    }

    @Override
    @ServiceLog("继承群组")
    public TripSCRMResult<TripSCRMEnterpriseWechatTransferGroupRes> transferGroup(TransferGroupRequest request) {
        if (request == null || request.getGroupChatIdList() == null || request.getGroupChatIdList().isEmpty()
            || !org.springframework.util.StringUtils.hasText(request.getHandoverUserId())
            || !org.springframework.util.StringUtils.hasText(request.getTakeoverUserId())
            || !org.springframework.util.StringUtils.hasText(request.getCorpId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        // 组装TransferCustomerRequest
        WechatTransferGroupRequest groupRequest = new WechatTransferGroupRequest();
        groupRequest.setTakeoverUserId(request.getTakeoverUserId());
        groupRequest.setChatIdList(request.getGroupChatIdList());
        groupRequest.setCorpId(request.getCorpId());
        groupRequest.setIsResigned(request.getIsResigned());
        // 调用transferGroup
        TripSCRMResult<WechatTransferGroupResponse> transferGroupResult = enterpriseWechatManager.transferGroup(groupRequest);
        if (!transferGroupResult.isSuccess() || transferGroupResult.getData() == null) {
            return TripSCRMResult.fail(transferGroupResult.getCode(), transferGroupResult.getMsg());
        }
        WechatTransferGroupResponse transferGroupRes = transferGroupResult.getData();
        TripSCRMEnterpriseWechatTransferGroupRes groupRes = new TripSCRMEnterpriseWechatTransferGroupRes();
        if (transferGroupRes.getFailedGroups() != null) {
            List<TripSCRMFaildGroupResult> groups = new ArrayList<>();
            for (FailedGroupResult c : transferGroupRes.getFailedGroups()) {
                TripSCRMFaildGroupResult g = new TripSCRMFaildGroupResult();
                g.setGroupChatId(c.getChatId());
                g.setErrCode(c.getErrCode());
                g.setErrMsg(c.getErrMsg());
                groups.add(g);
            }
            groupRes.setFailedGroups(groups);
        }
        return TripSCRMResult.success(groupRes);
    }

    @Override
    public TripSCRMResult<Boolean> deleteUser(DeleteUserRequest request) {
        if (request == null || !StringUtils.hasText(request.getUserId())
            || !StringUtils.hasText(request.getCorpId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        TripSCRMResult<Boolean> result = wechatUserService.deleteUser(request);
        if (!result.isSuccess() || result.getData() == null) {
            return TripSCRMResult.fail(result.getCode(), result.getMsg());
        }
        return TripSCRMResult.success(true);
    }

}
