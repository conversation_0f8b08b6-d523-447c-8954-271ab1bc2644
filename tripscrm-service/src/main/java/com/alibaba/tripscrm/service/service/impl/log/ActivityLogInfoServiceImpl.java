package com.alibaba.tripscrm.service.service.impl.log;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoDO;
import com.alibaba.tripscrm.service.model.vo.log.OperationLogInfoVO;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.log.SopLogContentBO;
import com.alibaba.tripscrm.service.model.domain.query.OperationLogInfoQuery;
import com.alibaba.tripscrm.service.service.log.ActivityLogInfoService;
import com.alibaba.tripscrm.service.service.log.OperationLogInfoService;
import com.alibaba.tripscrm.service.service.strategy.log.factory.AbstractLogFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/5 15:48
 **/
@Service
public class ActivityLogInfoServiceImpl implements ActivityLogInfoService {

    @Resource
    private OperationLogInfoService operationLogInfoService;
    @Resource
    private AbstractLogFactory abstractLogFactory;

    @Override
    public Boolean insert(OperationLogInfoDO operationLogInfoDO) {
        return operationLogInfoService.insert(operationLogInfoDO);
    }

    @Override
    public PageResultVO<OperationLogInfoVO> queryLogInfoVOList(OperationLogInfoQuery query) {
        Long count = operationLogInfoService.countByQuery(query);
        if (count == 0L) {
            return PageResultVO.successResult(Lists.newArrayList(), count.intValue(), query.getPageNum(), query.getPageSize());
        }
        List<OperationLogInfoDO> operationLogInfoDOS = operationLogInfoService.queryLogInfoDOList(query);
        if (CollectionUtils.isEmpty(operationLogInfoDOS)) {
            return PageResultVO.successResult(Lists.newArrayList(), count.intValue(), query.getPageNum(), query.getPageSize());
        }
        List<OperationLogInfoVO> operationLogInfoVOS = operationLogInfoDOS.stream().map(operationLogInfoDO -> convert(operationLogInfoDO)).collect(Collectors.toList());
        return PageResultVO.successResult(operationLogInfoVOS, count.intValue(), query.getPageNum(), query.getPageSize());
    }

    /**
     * DO对象转化为VO对象
     * @param operationLogInfoDO 日志DO对象
     * @return VO对象
     */
    private OperationLogInfoVO convert(OperationLogInfoDO operationLogInfoDO) {
        OperationLogInfoVO operationLogInfoVO = new OperationLogInfoVO();
        BeanUtils.copyProperties(operationLogInfoDO, operationLogInfoVO);
        String operatorContent = operationLogInfoDO.getOperatorContent();
        if (StringUtils.isBlank(operatorContent)) {
            return operationLogInfoVO;
        }
        operationLogInfoVO.setOperatorContent(getSopLogContentList(operationLogInfoDO.getOperatorContent()));
        return operationLogInfoVO;
    }

    /**
     * 获取SOP日志内容列表
     * @param contentStr 内容字符串
     * @return 内容列表
     */
    public List<String> getSopLogContentList(String contentStr) {
        ArrayList<String> result = new ArrayList<>();
        List<SopLogContentBO> sopLogContentList = JSONObject.parseObject(contentStr, new TypeReference<List<SopLogContentBO>>() {});
        if (CollectionUtils.isEmpty(sopLogContentList)) {
            return result;
        }
        for (SopLogContentBO sopLogContentBO : sopLogContentList) {
            String logShowStr = abstractLogFactory.getLogShowStr(sopLogContentBO);
            if (StringUtils.isBlank(logShowStr)) {
                continue;
            }
            result.add(logShowStr);
        }
        return result;
    }

}
