package com.alibaba.tripscrm.service.service.group;

import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupNewQuery;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupDTO;

import java.util.List;

/**
 * @description：微信群组服务接口
 * @Author：wangrui
 * * @create：2025/8/15 16:07
 * @Filename：WechatGroupService
 */
public interface GroupNewService {

    /**
     * 新增微信群组
     * @param wechatGroupDTO
     * @return
     */
    Integer insertSelective(WechatGroupDTO wechatGroupDTO);

    /**
     * 分页查询微信群组
     * @param query
     * @return
     */
    PageInfoDTO<WechatGroupDTO> pageQuery(WechatGroupNewQuery query);

    /**
     * 查询微信群组
     * @param query
     * @return
     */
    List<WechatGroupDTO> select(WechatGroupNewQuery query);

    /**
     * 更新微信群组
     * @param wechatGroupDTO
     * @return
     */
    Integer updateSelective(WechatGroupDTO wechatGroupDTO);

    /**
     * 删除微信群组
     * @param query
     * @return
     */
    Integer delete(WechatGroupNewQuery query);
}
