package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.risk.RiskInvokeSubStatusEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskInvokeStatusEnum;
import com.alibaba.tripscrm.service.enums.risk.SourceTypeEnum;
import lombok.Data;

/**
 * 风控调用记录 创建对象
 *
 * <AUTHOR>
 * @date 2024/07/01
 */
@Data
public class RiskInvokeRecordCreateParam {
    /**
     * 企微组织id
     */
    private String corpId;
    /**
     * 企微成员id
     */
    private String userId;
    /**
     * 行动项：发消息/创建群聊/...
     */
    private String action;
    /**
     * 流量来源id：小二id、任务id、小二id
     */
    private String sourceId;
    /**
     * 流量来源类型：0、未知操作 1、用户操作，2、系统操作，3、任务操作，4、聚合聊天
     *
     * @see SourceTypeEnum
     */
    private Integer sourceType;
    /**
     * 调用入参
     */
    private String invokeParam;
    /**
     * 调用结果
     */
    private String invokeResult;
    /**
     * 调用状态：成功/失败/限流
     *
     * @see RiskInvokeStatusEnum
     */
    private Integer invokeStatus;
    /**
     * 降级类型
     */
    private Integer invokeSubStatus;
    /**
     * 限流命中的行动项
     */
    private String limitAction;

    public static RiskInvokeRecordCreateParam newInstance(String corpId, String userId) {
        RiskInvokeRecordCreateParam param = new RiskInvokeRecordCreateParam();
        param.corpId = corpId;
        param.userId = userId;
        return param;
    }

    public RiskInvokeRecordCreateParam setAction(String action, String limitAction) {
        this.action = action;
        this.limitAction = limitAction;
        return this;
    }

    public RiskInvokeRecordCreateParam setSource(SourceTypeEnum sourceType, String sourceId) {
        this.sourceType = sourceType != null ? sourceType.getValue() : SourceTypeEnum.UNKNOWN.getValue();
        this.sourceId = sourceId;
        return this;
    }

    public RiskInvokeRecordCreateParam setInvoke(String invokeParam, String invokeResult, RiskInvokeStatusEnum invokeStatus, RiskInvokeSubStatusEnum invokeSubStatus) {
        this.invokeParam = invokeParam;
        this.invokeResult = invokeResult;
        this.invokeStatus = invokeStatus != null ? invokeStatus.getValue() : null;
        this.invokeSubStatus = invokeSubStatus != null ? invokeSubStatus.getValue() : null;
        return this;
    }

    public String string() {
        return JSONObject.toJSONString(this);
    }
}