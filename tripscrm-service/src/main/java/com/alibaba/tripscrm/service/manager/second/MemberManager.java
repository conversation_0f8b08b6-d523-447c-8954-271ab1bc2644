package com.alibaba.tripscrm.service.manager.second;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.ffa.touch.prod.customize.client.common.MemberInfoService;
import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberDTO;
import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberInfoRequest;
import com.fliggy.ffa.touch.shared.model.common.result.FtResult;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/16 14:47
 **/
@Component
@Slf4j
public class MemberManager {

    /**
     * 会员信息请求的激活渠道值
     */
    public static final Integer MEMBER_WX_ACTIVATE_CHANNEL = 10009;
    /**
     * 会员信息请求的场景参数值
     */
    public static final String MEMBER_WX_REQUEST_SCENE = "wechat-mini-program-center";

    /**
     * 会员信息请求的场景默认参数值
     */
    public static final String DEFAULT_SCENE = "default-scene";

    /**
     * 会员信息请求返回的FIELDS
     */
    public static final List<String> MEMBER_INFO_WX_FIELDS = Arrays.asList("SNS", "LEVEL_OR_ACTIVATE");

    /**
     * 里程
     */
    public static final String POINT = "POINT";

    /**
     * 查询ffa失败后的默认昵称
     */
    private static final String DEFAULT_NICK = "淘宝用户";

    /**
     * 查询ffa失败后的默认头像url 这个头像url为 淘宝的卡通小人头
     */
    private static final String DEFAULT_AVATAR
            = "http://img.alicdn.com/sns_logo/i3/TB1yeWeIFXXXXX5XFXXuAZJYXXX-210-210.png";

    @Resource
    private MemberInfoService memberInfoService;

    /**
     * 构建会员请求对象
     * @param userId 用户id
     * @return MemberInfoRequest
     */
    private MemberInfoRequest buildMemberInfoRequest(Long userId) {
        MemberInfoRequest memberInfoRequest = new MemberInfoRequest();
        memberInfoRequest.setActivateChannel(MEMBER_WX_ACTIVATE_CHANNEL);
        memberInfoRequest.setFields(MEMBER_INFO_WX_FIELDS);
        memberInfoRequest.setScene(MEMBER_WX_REQUEST_SCENE);
        memberInfoRequest.setUserId(userId);
        return memberInfoRequest;
    }

    /**
     * 获取用户昵称
     *
     * @param userId 用户id
     * @return {@link String}
     */
    @AteyeInvoker(description = "根据用户id查询用户昵称和头像", paraDesc = "userId")
    public Pair<String, String> getAvatarNickPair(Long userId) {
        String avatar = null;
        String nick = null;

        try {
            MemberInfoRequest memberInfoRequest = buildMemberInfoRequest(userId);
            FtResult<MemberDTO> ftResultMember = memberInfoService.getByUserIdV2(memberInfoRequest);
            if (ftResultMember == null || !ftResultMember.isSuccess() || ftResultMember.getData() == null) {
                PlatformLogUtil.logFail("根据用户id查询用户昵称和头像，响应为空", LogListUtil.newArrayList(userId, memberInfoRequest, ftResultMember));
            } else {
                avatar = ftResultMember.getData().getAvatar();
                nick = ftResultMember.getData().getDisplayName();
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("根据用户id查询用户昵称和头像，出现异常", e.getMessage(), e, LogListUtil.newArrayList());
        }

        if (StringUtils.isBlank(avatar) || StringUtils.isBlank(nick)) {
            return Pair.of(DEFAULT_AVATAR, DEFAULT_NICK);
        }
        return Pair.of(avatar, nick);
    }

    /**
     * 查询用户的飞猪会员信息
     * @param userId 淘系用户id
     * @return 会员信息
     */
    public MemberDTO queryMemberInfo(Long userId) {
        MemberInfoRequest memberInfoRequest = buildMemberInfoRequest(userId);
        FtResult<MemberDTO> ftResultMember = memberInfoService.getByUserIdV2(memberInfoRequest);
        if (ftResultMember.isSuccess()) {
            return ftResultMember.getData();
        }
        return null;
    }

    /**
     * 查询用户的里程余额
     *
     * @param userId 淘系用户id
     * @return 会员信息
     */
    public MemberDTO queryMemberPoint(Long userId) {
        MemberInfoRequest request = new MemberInfoRequest();
        request.setUserId(userId);
        request.setScene(DEFAULT_SCENE);
        request.setFields(Collections.singletonList(POINT));
        request.setActivateChannel(MEMBER_WX_ACTIVATE_CHANNEL);
        FtResult<MemberDTO> ftResultMember = memberInfoService.getByUserIdV2(request);
        if (ftResultMember.isSuccess()) {
            return ftResultMember.getData();
        }
        return null;
    }

}
