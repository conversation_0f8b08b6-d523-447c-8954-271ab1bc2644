package com.alibaba.tripscrm.service.service.task.ability.old.second;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.fliggyplaycore.client.biz.wechat.WechatShareService;
import com.fliggy.fliggyplaycore.client.biz.wechat.model.ShareWordQueryResponse;
import com.fliggy.fliggyplaycore.client.enums.ShareSceneEnum;
import com.fliggy.fliggyplaycore.client.model.ServiceResult;
import com.fliggy.fliggyplaycore.client.share.ShareActOperationService;
import com.fliggy.fliggyplaycore.client.share.ShareActQueryService;
import com.fliggy.fliggyplaycore.client.share.model.EstablishRsp;
import com.fliggy.fliggyplaycore.client.share.model.ShareActInfoDTO;
import com.fliggy.fliggyplaycore.client.share.model.ShareActQueryReq;
import com.fliggy.fliggyplaycore.client.share.model.ShareRelationEstablishReq;
import com.fliggy.fliggyplaycore.client.task.TaskOperationService;
import com.fliggy.fliggyplaycore.client.task.model.TaskFinishReq;
import com.fliggy.fliggyplaycore.client.task.model.TaskFinishRsp;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * 飞猪玩法平台服务
 *
 * <AUTHOR>
 * @date 2023/5/15
 */
@Slf4j
@Component
public class FliggyPlayService {

    @Resource
    private TaskOperationService taskOperationService;

    @Resource
    private ShareActOperationService shareActOperationService;

    @Resource
    private ShareActQueryService shareActQueryService;

    @Resource
    private WechatShareService wechatShareService;

    /**
     * 完成任务
     *
     * @param playId
     * @param userId
     * @param outBizNo
     * @return
     */
    public TripSCRMResult<Boolean> finishTask(String playId, Long userId, String outBizNo) {
        try {
            TaskFinishReq taskFinishReq = new TaskFinishReq();
            taskFinishReq.setUserId(userId);
            taskFinishReq.setPlayId(playId);
            taskFinishReq.setOutBizNo(outBizNo);
            ServiceResult<TaskFinishRsp> serviceResult = taskOperationService.finishTask(taskFinishReq);
            if (Objects.isNull(serviceResult) || !serviceResult.isSuccess()) {
                PlatformLogUtil.logFail("fail", LogListUtil.newArrayList(taskFinishReq, serviceResult));
                return TripSCRMResult.fail(serviceResult == null ? "Service return null" : serviceResult.getMsgInfo());
            }
            PlatformLogUtil.logFail("success", LogListUtil.newArrayList(taskFinishReq, serviceResult));
            return TripSCRMResult.success(Boolean.TRUE);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(playId, userId));
            return TripSCRMResult.fail("exception happen");
        }
    }

    @AteyeInvoker(description = "helpPredict")
    public TripSCRMResult<Boolean> helpPredict(String playId, Long userId, String word) {
        ShareRelationEstablishReq shareRelationEstablishReq = new ShareRelationEstablishReq();
        shareRelationEstablishReq.setUserId(userId);
        shareRelationEstablishReq.setPlayId(playId);
        shareRelationEstablishReq.setWord(word);
        ServiceResult<EstablishRsp> serviceResult = shareActOperationService.helpPredict(shareRelationEstablishReq);
        if (Objects.isNull(serviceResult)) {
            PlatformLogUtil.logFail("null", LogListUtil.newArrayList(shareRelationEstablishReq, serviceResult));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), SwitchConfig.fissionHelpWelcomeText.get("exception"));
        }

        if (!serviceResult.isSuccess()) {
            PlatformLogUtil.logFail("fail", LogListUtil.newArrayList(shareRelationEstablishReq, serviceResult));
            return TripSCRMResult.fail(serviceResult.getErrorCode());
        }

        PlatformLogUtil.logFail("success", LogListUtil.newArrayList(shareRelationEstablishReq, serviceResult));
        return TripSCRMResult.success(true);
    }

    /**
     * 助力者助力
     *
     * @param playId
     * @param userId
     * @param outBizNo
     * @return
     */
    public TripSCRMResult<Boolean> establishRelation(String playId, Long userId, String word, String outBizNo) {
        try {
            ShareRelationEstablishReq shareRelationEstablishReq = new ShareRelationEstablishReq();
            shareRelationEstablishReq.setUserId(userId);
            shareRelationEstablishReq.setPlayId(playId);
            shareRelationEstablishReq.setWord(word);
            shareRelationEstablishReq.setOutBizNo(outBizNo);
            ServiceResult<EstablishRsp> serviceResult = shareActOperationService.establishRelation(shareRelationEstablishReq);
            // 助力兜底文案
            if (Objects.isNull(serviceResult)) {
                PlatformLogUtil.logFail("fail", LogListUtil.newArrayList(shareRelationEstablishReq, serviceResult));
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), SwitchConfig.fissionHelpWelcomeText.get("backUp"));
            }
            if (serviceResult.isSuccess()) {
                PlatformLogUtil.logFail("success", LogListUtil.newArrayList(shareRelationEstablishReq, serviceResult));
                return TripSCRMResult.success(Boolean.TRUE, SwitchConfig.fissionHelpWelcomeText.get("success"));
            }
            PlatformLogUtil.logFail("fail", LogListUtil.newArrayList(shareRelationEstablishReq, serviceResult));
            if (SwitchConfig.fissionHelpWelcomeText.containsKey(serviceResult.getErrorCode())) {
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), SwitchConfig.fissionHelpWelcomeText.get(serviceResult.getErrorCode()));
            }
            // 兜底文案
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), SwitchConfig.fissionHelpWelcomeText.get("backUp"));
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(playId, userId));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), SwitchConfig.fissionHelpWelcomeText.get("exception"));
        }
    }

    /**
     * 根据玩法ID查询活动信息
     *
     * @param playId
     * @param userId
     * @return
     */
    @AteyeInvoker(description = "queryPlayActInfoByActId")
    public TripSCRMResult<ShareActInfoDTO> queryPlayActInfoByActId(String playId, Long userId) {
        try {
            ShareActQueryReq shareActQueryReq = new ShareActQueryReq();
            shareActQueryReq.setPlayId(playId);
            shareActQueryReq.setUserId(userId);
            ServiceResult<ShareActInfoDTO> shareActInfoDTOServiceResult = shareActQueryService.queryActInfo(shareActQueryReq);
            if (Objects.isNull(shareActInfoDTOServiceResult) || !shareActInfoDTOServiceResult.isSuccess()) {
                PlatformLogUtil.logFail("fail", LogListUtil.newArrayList(playId, userId));
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), "查询玩法信息失败");
            }
            return TripSCRMResult.success(shareActInfoDTOServiceResult.getResultData());
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(playId, userId));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), "查询玩法信息异常");
        }
    }

    /**
     * 根据分享word查询活动信息
     *
     * @param shareWord
     * @param userId
     * @return
     */
    @AteyeInvoker(description = "queryPlayActInfoByShareWord")
    public TripSCRMResult<ShareWordQueryResponse> queryPlayActInfoByShareWord(String shareScene, String shareWord, Long userId) {
        try {
            ShareActQueryReq shareActQueryReq = new ShareActQueryReq();
            shareActQueryReq.setShareWord(shareWord);
            shareActQueryReq.setUserId(userId);
            if (Objects.equals(ShareSceneEnum.WX_CASH.name(), shareScene)) {
                shareActQueryReq.setShareScene(shareScene);
            }

            ServiceResult<ShareWordQueryResponse> shareWordQueryResponseServiceResult = wechatShareService.queryActInfoByShareWord(shareActQueryReq);
            if (Objects.isNull(shareWordQueryResponseServiceResult) || !shareWordQueryResponseServiceResult.isSuccess()) {
                PlatformLogUtil.logFail("fail", LogListUtil.newArrayList(shareWord, userId));
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), "查询玩法信息失败");
            }
            return TripSCRMResult.success(shareWordQueryResponseServiceResult.getResultData());
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(shareWord, userId));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED.getCode(), "查询玩法信息异常");
        }
    }

}
