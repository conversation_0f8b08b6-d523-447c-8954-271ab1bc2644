package com.alibaba.tripscrm.service.model.domain;

import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomain;
import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomainField;

/**
 * 聚合单条数据返回值
 */
public class AMDPCombine1784 {
    /**
     * 员工域
     */
    @AmdpDomain(
            code = "EMP_EMPLOYEE"
    )
    private EmpEmployee empEmployee;

    public EmpEmployee getEmpEmployee() {
        return this.empEmployee;
    }

    public void setEmpEmployee(EmpEmployee empEmployee) {
        this.empEmployee = empEmployee;
    }

    /**
     * 员工域
     */
    @AmdpDomain(
            code = "EMP_EMPLOYEE"
    )
    public static class EmpEmployee {
        /**
         * 员工工号
         */
        @AmdpDomainField(
                code = "workNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String workNo;

        /**
         * 部门编号
         */
        @AmdpDomainField(
                code = "deptNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String deptNo;

        /**
         * 花名
         */
        @AmdpDomainField(
                code = "nickName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String nickName;

        /**
         * 员工姓名
         */
        @AmdpDomainField(
                code = "name",
                domainCode = "EMP_EMPLOYEE"
        )
        private String name;

        /**
         * 员工类型
         */
        @AmdpDomainField(
                code = "empType",
                domainCode = "EMP_EMPLOYEE"
        )
        private String empType;

        /**
         * 工作状态
         */
        @AmdpDomainField(
                code = "workStatus",
                domainCode = "EMP_EMPLOYEE"
        )
        private String workStatus;

        /**
         * 头像信息
         */
        @AmdpDomainField(
                code = "personalPhoto",
                domainCode = "EMP_EMPLOYEE"
        )
        private String personalPhoto;

        /**
         * 头像信息URL
         */
        @AmdpDomainField(
                code = "personalPhotoUrl",
                domainCode = "EMP_EMPLOYEE"
        )
        private String personalPhotoUrl;

        /**
         * 部门简称(主职)
         */
        @AmdpDomainField(
                code = "deptShortName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String deptShortName;

        /**
         * 部门名称(主职)
         */
        @AmdpDomainField(
                code = "deptName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String deptName;

        /**
         * 部门英文名称(主职)
         */
        @AmdpDomainField(
                code = "deptEnName",
                domainCode = "EMP_EMPLOYEE"
        )
        private String deptEnName;

        /**
         * 展示工号
         */
        @AmdpDomainField(
                code = "identWorkNo",
                domainCode = "EMP_EMPLOYEE"
        )
        private String identWorkNo;

        public String getWorkNo() {
            return this.workNo;
        }

        public void setWorkNo(String workNo) {
            this.workNo = workNo;
        }

        public String getDeptNo() {
            return this.deptNo;
        }

        public void setDeptNo(String deptNo) {
            this.deptNo = deptNo;
        }

        public String getNickName() {
            return this.nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public String getName() {
            return this.name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmpType() {
            return this.empType;
        }

        public void setEmpType(String empType) {
            this.empType = empType;
        }

        public String getWorkStatus() {
            return this.workStatus;
        }

        public void setWorkStatus(String workStatus) {
            this.workStatus = workStatus;
        }

        public String getPersonalPhoto() {
            return this.personalPhoto;
        }

        public void setPersonalPhoto(String personalPhoto) {
            this.personalPhoto = personalPhoto;
        }

        public String getPersonalPhotoUrl() {
            return this.personalPhotoUrl;
        }

        public void setPersonalPhotoUrl(String personalPhotoUrl) {
            this.personalPhotoUrl = personalPhotoUrl;
        }

        public String getDeptShortName() {
            return this.deptShortName;
        }

        public void setDeptShortName(String deptShortName) {
            this.deptShortName = deptShortName;
        }

        public String getDeptName() {
            return this.deptName;
        }

        public void setDeptName(String deptName) {
            this.deptName = deptName;
        }

        public String getDeptEnName() {
            return this.deptEnName;
        }

        public void setDeptEnName(String deptEnName) {
            this.deptEnName = deptEnName;
        }

        public String getIdentWorkNo() {
            return this.identWorkNo;
        }

        public void setIdentWorkNo(String identWorkNo) {
            this.identWorkNo = identWorkNo;
        }
    }
}
