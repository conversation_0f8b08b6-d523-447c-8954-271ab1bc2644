package com.alibaba.tripscrm.service.service.strategy.websocket.vo;

import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/6/25 16:47
 **/
@Data
public class SupportChatVO {

    /**
     * 聊天类型
     * @see ChatTypeEnum
     */
    private Integer chatType;

    /**
     * 聊天id
     */
    private String chatId;

    /**
     * 聊天名称
     */
    private String chatName;

    /**
     * 会话头像
     */
    private String chatAvatar;

    /**
     * 关注结果
     */
    private Boolean groupWork = false;
}
