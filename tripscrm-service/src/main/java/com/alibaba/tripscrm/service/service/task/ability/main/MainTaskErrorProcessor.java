package com.alibaba.tripscrm.service.service.task.ability.main;

import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;

/**
 * 任务执行_主任务_出错后执行的逻辑
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MainTaskErrorProcessor implements ExecuteProcessor {
    private final LdbTairManager ldbTairManager;
    private final TaskService taskService;
    private final TaskInstanceService taskInstanceService;

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    @TaskExecuteLog("任务执行_主任务_出错后执行的逻辑")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        if (isStreamTask) {
            return TripSCRMResult.success(null);
        }

        PlatformLogUtil.logInfo("主任务执行-出错后执行的逻辑", LogListUtil.newArrayList(context));
        taskInstanceService.updateStatus(context.getInstanceId(), TaskInstanceStatusEnum.RUN_FAIL);
        // 乐观锁更新任务状态
        Integer ret = taskService.compareAndUpdateStatus(context.getTaskId(), TaskStatusEnum.OFFLINE_IN_PROGRESS, TaskStatusEnum.EDITING);
        if (ret > 0) {
            ldbTairManager.put(TairConstant.LDB_TASK_STATUS_PREFIX + context.getTaskId(), TaskStatusEnum.EDITING.getStatus(), EXPIRE_TIME_DAY_UNIT);
        }

        return TripSCRMResult.success(null);
    }
}
