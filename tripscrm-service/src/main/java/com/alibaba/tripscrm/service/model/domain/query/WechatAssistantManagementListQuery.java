package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微信成员信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatAssistantManagementListQuery extends BasePageRequest {

    /**
     * isv类型，1-比邻，10-百业
     */
    public Byte isvType;

    /**
     * 企业id
     */
    public String corpId;
}
