package com.alibaba.tripscrm.service.service.second;

import com.alibaba.trip.tripdata.common.domain.page.Page;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.taobao.trip.train.dataobject.TrStationCityDo;

import java.util.List;

/**
 * 行政区划相关接口
 *
 * <AUTHOR>
 * @date 2023-07-17 16:14:47
 */
public interface DivisionService {
    /**
     * 根据城市名称查询行政区划id
     *
     * @param cityName 城市名称
     * @return 行政区划id
     */
    Long getDivisionIdByCityName(String cityName);

    /**
     * 搜索行政区划
     *
     * @param keyword 搜索关键词
     * @return 分页结果
     */
    Page<TrdiDivisionDO> searchDivision(String keyword);

    /**
     * 根据id查询行政区划
     *
     * @param ids 行政区划 id
     * @return 行政区划
     */
    List<TrdiDivisionDO> getDivision(List<Long> ids);

    /**
     * 根据行政区划id获取三字码
     *
     * @param divisionId
     * @return
     */
    String getCityCode3ByDivisionId(Long divisionId);

    /**
     * 根据三字码获取行政区划id
     *
     * @param cityCode3
     * @return
     */
    Long getDivisionIdByCityCode3(String cityCode3);

    /**
     * 根据火车站名获取行政区划信息
     *
     * @param stationName
     * @return
     */
    TrStationCityDo getTrStationCityDoByName(String stationName);
}
