package com.alibaba.tripscrm.service.service.strategy.isv.route.group;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.google.common.collect.Lists;
import com.taobao.csp.courier.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/27 10:44
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupAddCustomerRouteStrategy extends AbstractGroupActionRouteStrategy {
    private final WechatCustomerService wechatCustomerService;

    @Override
    protected TripSCRMResult<List<String>> getAllWechatUserIdList(IsvRouteContext isvRouteContext) {
        if (!StringUtils.hasText(isvRouteContext.getChatId()) || !StringUtils.hasText(isvRouteContext.getExternalUserId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<String> userIdList = getGroupUserList(isvRouteContext);
        if (CollectionUtils.isEmpty(userIdList)) {
            PlatformLogUtil.logFail("获取添加群内客户为好友行动项对应的企微号，没有匹配到企微账号", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        // 剔除已有好友关系的企微账号
        FollowUserQuery followUserQuery = new FollowUserQuery();
        followUserQuery.setExternalUserId(isvRouteContext.getExternalUserId());
        followUserQuery.setUserIdList(userIdList);
        followUserQuery.setRobotStatus(RobotStatusEnum.ONLINE.getCode());
        followUserQuery.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode(), CustomerRelationStatusEnum.SINGLE.getCode()));
        FollowUserInfo followUserInfo = wechatCustomerService.listFollowUser(followUserQuery);
        if (Objects.nonNull(followUserInfo) && !CollectionUtils.isEmpty(followUserInfo.getRelationList())) {
            List<String> followUserIdList = followUserInfo.getRelationList().stream().map(FollowUserInfo.CustomerRelation::getUserId).collect(Collectors.toList());
            userIdList.removeIf(followUserIdList::contains);
        }
        return TripSCRMResult.success(userIdList);
    }

    @Override
    protected List<RiskActionEnum> getRiskActionEnumList() {
        return Lists.newArrayList(RiskActionEnum.GROUP_ADD_CUSTOMER_BY_GROUP);
    }

    @Override
    protected boolean onlyOwnerPermission(IsvRouteContext isvRouteContext) {
        return false;
    }

    @Override
    protected boolean needOwnerOrAdminPermission(IsvRouteContext isvRouteContext) {
        return false;
    }
}