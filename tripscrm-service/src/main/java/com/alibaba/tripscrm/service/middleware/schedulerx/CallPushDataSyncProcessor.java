package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.mapper.tddl.UserPushToIsvRecordMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CallPushActivityRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.UserPushToIsvRecordDO;
import com.alibaba.tripscrm.service.service.callpush.CallPushActivityRecordService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 外呼推送历史数据迁移
 * <AUTHOR>
 * @since 2024/8/22 19:50
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CallPushDataSyncProcessor extends JavaProcessor {
    private final CallPushActivityRecordService callPushActivityRecordService;
    private final UserPushToIsvRecordMapper userPushToIsvRecordMapper;

    @Switch(description = "外呼推送历史数据迁移开关", name = "syncDataSwitch")
    public static boolean syncDataSwitch = false;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        for (long minId = 0; minId >= 0;) {
            List<UserPushToIsvRecordDO> userPushToIsvRecordDOS = userPushToIsvRecordMapper.selectByIdGreatThan(minId, 100);
            if (CollectionUtils.isEmpty(userPushToIsvRecordDOS)) {
                break;
            }

            minId = userPushToIsvRecordDOS.stream().map(UserPushToIsvRecordDO::getId).max(Long::compare).orElse(-1L);

            if (!syncDataSwitch) {
                continue;
            }
            for (UserPushToIsvRecordDO userPushToIsvRecordDO : userPushToIsvRecordDOS) {
                syncData(userPushToIsvRecordDO);
            }
        }

        return new ProcessResult(true);
    }

    @AteyeInvoker(description = "外呼历史数据迁移", paraDesc = "userPushToIsvRecordId")
    public void sync(Long userPushToIsvRecordId) {
        UserPushToIsvRecordDO userPushToIsvRecordDO = userPushToIsvRecordMapper.selectByPrimaryKey(userPushToIsvRecordId);
        syncData(userPushToIsvRecordDO);
    }

    private void syncData(UserPushToIsvRecordDO userPushToIsvRecordDO) {
        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setDayVersion(DateUtils.getDayVersion(userPushToIsvRecordDO.getGmtCreate()).intValue());
        callPushActivityRecordDO.setActivityId(801L);
        callPushActivityRecordDO.setUid(userPushToIsvRecordDO.getUserId());
        callPushActivityRecordDO.setUidEncrypt(userPushToIsvRecordDO.getUserIdEncrypt());
        callPushActivityRecordDO.setCellPhone(userPushToIsvRecordDO.getCellPhone());
        callPushActivityRecordDO.setCellPhoneEncrypt(userPushToIsvRecordDO.getCellPhoneEncrypt());
        callPushActivityRecordDO.setUnionId(userPushToIsvRecordDO.getUnionId());
        callPushActivityRecordDO.setMsgUrl(userPushToIsvRecordDO.getMsgUrl());

        callPushActivityRecordDO.setPushStage(userPushToIsvRecordDO.getPushStage());
        callPushActivityRecordDO.setCallStage(userPushToIsvRecordDO.getCallStage());
        callPushActivityRecordDO.setMsgStage(userPushToIsvRecordDO.getMsgStage());
        callPushActivityRecordDO.setAddByMsgStage(userPushToIsvRecordDO.getJoinWxStage());
        callPushActivityRecordDO.setCallBackContent(userPushToIsvRecordDO.getCallBackContent());

        try {
            callPushActivityRecordService.insert(callPushActivityRecordDO);
            PlatformLogUtil.logFail("外呼历史数据迁移成功", LogListUtil.newArrayList(userPushToIsvRecordDO, callPushActivityRecordDO));
        } catch (Exception e) {
            PlatformLogUtil.logException("外呼历史数据迁移失败", e.getMessage(), e, LogListUtil.newArrayList(userPushToIsvRecordDO, callPushActivityRecordDO));
        }
    }
}
