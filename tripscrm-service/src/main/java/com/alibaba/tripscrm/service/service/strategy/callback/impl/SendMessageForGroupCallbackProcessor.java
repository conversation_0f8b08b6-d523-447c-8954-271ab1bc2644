package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.dal.repository.WechatGroupRepository;
import com.alibaba.tripscrm.domain.enums.EnterpriseWechatChatMessageTypeEnum;
import com.alibaba.tripscrm.domain.message.EnterpriseWechatChatMessageDTO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatSenderTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageCreateParam;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.SendMessageRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.AsyncSendMessageProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.CreateSingleChatProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.ReceiveMessageProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.callback.TaskAsyncRequestCallbackProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.result.AbstractWechatMessageBO;
import com.alibaba.tripzoo.proxy.result.WechatGroupMessageBO;
import com.fliggy.pokemon.lock.client.api.TairLockManager;
import com.google.common.collect.Lists;
import com.taobao.item.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.alibaba.tripscrm.service.constant.TairConstant.MESSAGE_ID_MAPPING_PREFIX;
import static com.alibaba.tripscrm.service.service.impl.fusionchat.ChatConversationServiceImpl.DEFAULT_GROUP_AVATAR;

/**
 * 发送消息到群聊【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class SendMessageForGroupCallbackProcessor implements ProxyCallbackProcessor {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private AsyncSendMessageProcessor asyncSendMessageProcessor;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private ChatConversationRepository chatConversationRepository;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private TairLockManager tairLockManager;
    @Resource
    private TaskAsyncRequestCallbackProcessor taskAsyncRequestCallbackProcessor;
    @Resource
    private WechatGroupRepository wechatGroupRepository;
    @Resource
    private CreateSingleChatProcessor createSingleChatProcessor;
    @Resource
    private ReceiveMessageProcessor receiveMessageProcessor;
    @Resource
    private MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.SEND_MESSAGE_TO_GROUP_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        processTaskExecuteResult(scrmCallbackMsg);
        processFusionChat(scrmCallbackMsg);
        return true;
    }

    private void processTaskExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            taskAsyncRequestCallbackProcessor.process(scrmCallbackMsg);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行群聊任务异步请求回调处理异常", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private void processFusionChat(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            // 执行主动发送消息的后置动作
            WsEvent wsEvent = asyncSendMessageProcessor.afterCreate(scrmCallbackMsg.getRequestId());
            if (wsEvent == null) {
                processMessageFromOther(scrmCallbackMsg);
                return;
            }
            processMessageFromFusionChat(scrmCallbackMsg, wsEvent);
        } catch (Exception e) {
            PlatformLogUtil.logException("企微成员发送群聊消息结果回调，处理异常", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private void processMessageFromOther(ScrmCallbackMsg scrmCallbackMsg) {
        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            // 若失败
            PlatformLogUtil.logFail("企微成员发送群聊消息结果回调处理，结果为失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        boolean persist = content.containsKey("persist") ? content.getBoolean("persist") : true;
        if (!persist) {
            PlatformLogUtil.logFail("企微成员发送群聊消息结果回调处理，无需处理", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(scrmCallbackMsg.getContent());
        if (!jsonObject.containsKey("msgId") || !jsonObject.containsKey("message")) {
            PlatformLogUtil.logFail("企微成员发送群聊消息结果回调处理，缺少msgId或message", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        String msgId = jsonObject.getString("msgId");
        WechatGroupMessageBO wechatGroupMessageBO = jsonObject.getObject("message", new TypeReference<WechatGroupMessageBO>() {
        });

        String userId = wechatGroupMessageBO.getUserId();
        String chatId = wechatGroupMessageBO.getChatId();
        if (!StringUtils.hasText(userId)) {
            PlatformLogUtil.logFail("企微成员发送群聊消息结果回调处理，缺少企微成员信息", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        if (!StringUtils.hasText(chatId)) {
            PlatformLogUtil.logFail("企微成员发送群聊消息结果回调处理，缺少客户群信息", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        ChatTypeEnum chatType = ChatTypeEnum.GROUP;
        String senderId = userId;
        GroupUserTypeEnum senderType = GroupUserTypeEnum.USER;
        Long timestamp = wechatGroupMessageBO.getTimestamp() * 1000;
        AbstractWechatMessageBO.MessageContent messageContent = wechatGroupMessageBO.getMessageContent();
        MessageTypeEnum messageTypeEnum = wechatGroupMessageBO.getMessageTypeEnum();
        // 将外部消息格式转换成聚合聊天消息格式
        FusionChatMessageBody fusionChatMessageBody = MessageUtils.receiveMessage2fusionChatMessage(messageTypeEnum, messageContent);
        if (fusionChatMessageBody == null) {
            PlatformLogUtil.logFail("企微成员发送群聊消息结果回调处理，将外部消息格式转换成聚合聊天消息格式失败", LogListUtil.newArrayList(scrmCallbackMsg, messageTypeEnum.getCode(), messageContent));
            return;
        }

        // 保存聊天记录
        ChatMessageCreateParam createParam = new ChatMessageCreateParam();
        createParam.setSenderId(senderId);
        createParam.setSenderType(senderType.getCode());
        createParam.setReceiveId(chatMessageService.chatId2ReceiveId(userId, chatId, chatType.getValue()));
        createParam.setReceiveType(chatType.getReceiveType().getValue());
        if (Lists.newArrayList(MessageTypeEnum.TEXT, MessageTypeEnum.REFERENCE).contains(messageTypeEnum)) {
            String msgAndAtInfo = MessageUtils.buildTextMessageJsonStr(messageContent.getAtAll(), messageContent.getAtList(), messageContent.getAtExternalUserIdList(), messageContent.getContent());
            createParam.setContent(MessageUtils.fusionChatMessage2DbContent(fusionChatMessageBody.getMsgType(), msgAndAtInfo));
        } else {
            createParam.setContent(MessageUtils.fusionChatMessage2DbContent(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
        }
        createParam.setTimestamp(timestamp);
        createParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
        createParam.setRequestId(scrmCallbackMsg.getRequestId());
        String messageId = chatMessageService.createChatMessage(createParam);
        // 自己发的消息，在LDB中增加msgId与messageId的映射，用于消息撤回，与含有企微客户的群聊聊限2分钟内
        ldbTairManager.put(MESSAGE_ID_MAPPING_PREFIX + messageId, msgId, 60 * 2);
        ldbTairManager.put(MESSAGE_ID_MAPPING_PREFIX + msgId, messageId, 60 * 10);
        PlatformLogUtil.logInfo("企微成员发送群聊消息结果回调处理，保存聊天记录成功", LogListUtil.newArrayList(scrmCallbackMsg, createParam, msgId, messageId));
        processSendMessage(scrmCallbackMsg.getPlatformCorpId(), userId, chatId, messageTypeEnum, fusionChatMessageBody, messageId, timestamp, scrmCallbackMsg.getRequestId(), messageContent.getAtList(), messageContent.getAtExternalUserIdList(), messageContent.getAtAll());
    }

    private void processSendMessage(String corpId, String userId, String chatId, String fusionChatMsgType, String msgContent, Long timestamp, String messageId, String requestId) {
        EnterpriseWechatChatMessageDTO message = new EnterpriseWechatChatMessageDTO();
        message.setSenderId(userId);
        message.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
        message.setReceiverId(chatId);
        message.setReceiverType(ActivityTargetTypeEnum.WX_CHAT_ID.getCode());
        FusionChatMessageTypeEnum messageTypeEnum = FusionChatMessageTypeEnum.parse(fusionChatMsgType);
        message.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(messageTypeEnum.name()));
        FusionChatMessageBody fusionChatMessageBody = MessageUtils.dbContent2FusionChatMessage(msgContent);
        message.setMsgContent(fusionChatMessageBody.getMsgContent());
        message.setMessageId(messageId);
        message.setTimestamp(timestamp);
        message.setCorpId(corpId);
        message.setRequestId(requestId);
        setAtInfo(fusionChatMessageBody.getAtAll(), fusionChatMessageBody.getAtUserIdList(), fusionChatMessageBody.getAtExternalUserIdList(), message);
        PlatformLogUtil.logInfo("发送企业微信聊天记录消息，发送群聊消息", LogListUtil.newArrayList(message));
        metaqProducer.send(MQEnum.ENTERPRISE_WECHAT_CHAT_MESSAGE, null, corpId, JSONObject.toJSONString(message));
    }

    /**
     * 设置@信息
     *
     * @param atAll 是否at所有人
     * @param atUserIdList 被at的成员id列表
     * @param atExternalUserIdList 被at的客户id列表
     * @param message 消息体
     */
    private void setAtInfo(Boolean atAll, List<String> atUserIdList, List<String> atExternalUserIdList, EnterpriseWechatChatMessageDTO message) {
        if (CollectionUtils.isNotEmpty(atExternalUserIdList)) {
            message.setAtExternalUserIdList(String.join(",", atExternalUserIdList));
        }
        if (CollectionUtils.isNotEmpty(atUserIdList)) {
            message.setAtUserIdList(String.join(",", atUserIdList));
        }
        if (Objects.nonNull(atAll) && atAll) {
            message.setAtUserIdList(SwitchConfig.GROUP_CHAT_AT_ALL_STR);
            message.setAtExternalUserIdList(SwitchConfig.GROUP_CHAT_AT_ALL_STR);
        }
    }

    private void processSendMessage(String corpId, String userId, String chatId, MessageTypeEnum messageTypeEnum, FusionChatMessageBody fusionChatMessageBody, String messageId, Long timestamp, String requestId, List<String> atUserIdList, List<String> atExternalUserIdList, Boolean atAll) {
        EnterpriseWechatChatMessageDTO message = new EnterpriseWechatChatMessageDTO();
        message.setSenderId(userId);
        message.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
        message.setReceiverId(chatId);
        message.setReceiverType(ActivityTargetTypeEnum.WX_CHAT_ID.getCode());
        message.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(messageTypeEnum.name()));
        message.setMsgContent(fusionChatMessageBody.getMsgContent());
        message.setMessageId(messageId);
        message.setTimestamp(timestamp);
        message.setCorpId(corpId);
        message.setRequestId(requestId);
        setAtInfo(atAll, atUserIdList, atExternalUserIdList, message);
        PlatformLogUtil.logInfo("发送企业微信聊天记录消息，发送群聊消息", LogListUtil.newArrayList(message));
        metaqProducer.send(MQEnum.ENTERPRISE_WECHAT_CHAT_MESSAGE, null, corpId, JSONObject.toJSONString(message));
    }

    private void processMessageFromFusionChat(ScrmCallbackMsg scrmCallbackMsg, WsEvent wsEvent) {
        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("企微成员发送群聊消息结果回调处理【聚合聊天】，回调结果为失败", LogListUtil.newArrayList(scrmCallbackMsg));
            wsEvent.setSuccess(false);
            wsEvent.setErrorMessage(scrmCallbackMsg.getMessage() + "_" + scrmCallbackMsg.getContent());
            webSocketFactory.pushMessageByDistributed(wsEvent);
            return;
        }

        PlatformLogUtil.logInfo("企微成员发送群聊消息结果回调处理【聚合聊天】，回调结果为成功", LogListUtil.newArrayList(scrmCallbackMsg));
        SendMessageRequest sendMessageRequest = wsEvent.getData().toJavaObject(SendMessageRequest.class);
        String chatId = sendMessageRequest.getChatId();
        String groupUniqueKey = TairConstant.UPDATE_GROUP_MESSAGE_LOCK
                + chatId
                + scrmCallbackMsg.getPlatformCorpId();

        // 处理群聊聊天记录，必须加锁，不然有线程安全问题
        tairLockManager.tryRunWithLock(
                groupUniqueKey,
                1000,
                () -> processFusionChatMessage(scrmCallbackMsg, wsEvent),
                () -> PlatformLogUtil.logFail("企微成员发送群聊消息结果回调处理【聚合聊天】，保存聊天记录，获取锁失败！！", LogListUtil.newArrayList(scrmCallbackMsg, groupUniqueKey))
        );
    }

    private void processFusionChatMessage(ScrmCallbackMsg scrmCallbackMsg, WsEvent wsEvent) {
        try {
            // 获取发送人信息
            SendMessageRequest sendMessageRequest = wsEvent.getData().toJavaObject(SendMessageRequest.class);
            String msgContent = sendMessageRequest.getMsgContent();
            String chatId = sendMessageRequest.getChatId();
            String msgType = sendMessageRequest.getMsgType();
            String userId = wsEvent.getUserId();
            ChatTypeEnum chatType = ChatTypeEnum.GROUP;
            Long timestamp = System.currentTimeMillis();
            FusionChatUserBody fusionChatUserBody = chatMessageService.getUserInfo(userId, FusionChatSenderTypeEnum.USER.getValue());
            processChatMessage(scrmCallbackMsg, wsEvent, userId, chatId, chatType, msgType, msgContent, timestamp, fusionChatUserBody);
            // 发送成功后，更新sendMessageRecord
            asyncSendMessageProcessor.deleteSendMessageRecord(scrmCallbackMsg.getRequestId());
        } catch (Exception e) {
            PlatformLogUtil.logException("企微成员发送群聊消息结果回调处理【聚合聊天】，保存聊天记录失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }


    private String processChatMessage(ScrmCallbackMsg scrmCallbackMsg, WsEvent wsEvent, String userId, String chatId, ChatTypeEnum chatType, String msgType, String msgContent, Long timestamp, FusionChatUserBody fusionChatUserBody) {
        String newMessageId = "";
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        boolean persist = content.containsKey("persist") ? content.getBoolean("persist") : true;
        if (persist) {
            String msgId = content.containsKey("msgId") ? content.getString("msgId") : "";
            // 保存聊天记录
            ChatMessageCreateParam createParam = new ChatMessageCreateParam();
            createParam.setSenderId(userId);
            createParam.setSenderType(GroupUserTypeEnum.USER.getCode());
            createParam.setReceiveId(chatMessageService.chatId2ReceiveId(userId, chatId, chatType.getValue()));
            createParam.setReceiveType(chatType.getReceiveType().getValue());
            createParam.setContent(MessageUtils.fusionChatMessage2DbContent(msgType, msgContent));
            createParam.setTimestamp(timestamp);
            createParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            createParam.setRequestId(scrmCallbackMsg.getRequestId());
            newMessageId = chatMessageService.createChatMessage(createParam);
            // 自己发的消息，在LDB中增加msgId与messageId的映射，用于消息撤回，与含有企微客户的群聊聊限2分钟内
            ldbTairManager.put(MESSAGE_ID_MAPPING_PREFIX + newMessageId, msgId, 60 * 2);
            ldbTairManager.put(MESSAGE_ID_MAPPING_PREFIX + msgId, newMessageId, 60 * 10);
            PlatformLogUtil.logInfo("企微成员发送群聊消息结果回调处理【聚合聊天】，保存聊天记录成功", LogListUtil.newArrayList(userId, msgId, newMessageId));
        }


        List<String> userIdListInGroup = wechatGroupService.listUserIdByChatIdAndUserTypeAndCorpId(chatId, GroupUserTypeEnum.USER, scrmCallbackMsg.getPlatformCorpId());
        for (String userIdInGroup : userIdListInGroup) {
            if (persist) {
                // 收到消息，更新用户会话的最后一条消息
                ChatConversationDO chatConversation = chatConversationRepository.getByUniqueKeyWithNull(userIdInGroup, chatId, chatType.getValue());
                boolean pushCreateConversation = Objects.isNull(chatConversation) || Optional.ofNullable(chatConversation.getDeleted()).orElse(false);
                Date now = new Date();
                ChatConversationDO chatConversationDO = new ChatConversationDO();
                chatConversationDO.setUserId(userIdInGroup);
                chatConversationDO.setChatId(chatId);
                chatConversationDO.setChatType(chatType.getValue());
                chatConversationDO.setCorpId(scrmCallbackMsg.getPlatformCorpId());
                chatConversationDO.setLastMessageSenderName(fusionChatUserBody.getUserName());
                chatConversationDO.setLastMessageContent(MessageUtils.fusionChatMessage2Text(msgType, msgContent));
                chatConversationDO.setLastMessageCreateTime(new Date(timestamp));
                chatConversationDO.setDnd(false);
                chatConversationDO.setTopNo(0);
                chatConversationDO.setUnreadCount(0);
                chatConversationDO.setGmtCreate(now);
                chatConversationDO.setGmtModified(now);
                chatConversationDO.setUpdateTimestamp(timestamp);
                chatConversationDO.setDeleted(false);
                chatConversationService.upsertChatConversation(chatConversationDO);

                // 创建会话，推送至前端
                if (pushCreateConversation) {
                    WechatGroupDO wechatGroup = wechatGroupRepository.getByChatIdAndCorpId(chatId, scrmCallbackMsg.getPlatformCorpId());
                    createSingleChatProcessor.pushMessageByDistributed(userIdInGroup, chatId, chatType, wechatGroup.getName(), DEFAULT_GROUP_AVATAR, null, timestamp);
                }

                // 更新会话未读数
                chatConversationService.addChatConversationUnreadCount(userIdInGroup, userId, chatId, scrmCallbackMsg.getPlatformCorpId(), chatConversationDO.getLastMessageContent());
            }

            if (Objects.equals(userIdInGroup, userId)) {
                asyncSendMessageProcessor.pushMessageByDistributed(wsEvent, chatId, chatType, newMessageId, userId, null, timestamp, fusionChatUserBody, new FusionChatMessageBody(msgType, msgContent));
            } else {
                receiveMessageProcessor.pushMessageByDistributed(userIdInGroup, chatId, chatType, userId, null, timestamp.toString(), timestamp, scrmCallbackMsg.getPlatformCorpId(), new FusionChatMessageBody(msgType, msgContent), fusionChatUserBody);
            }
        }
        processSendMessage(scrmCallbackMsg.getPlatformCorpId(), userId, chatId, msgType, msgContent, timestamp, newMessageId, scrmCallbackMsg.getRequestId());
        return newMessageId;
    }
}
