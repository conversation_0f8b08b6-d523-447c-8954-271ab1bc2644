package com.alibaba.tripscrm.service.service.task.ability.old;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatCustomerAcquisitionListQuery;
import com.alibaba.tripscrm.domain.TaskContext;
import com.alibaba.tripscrm.domain.WelcomeTaskContext;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.EnterpriseWeChatEventEnum;
import com.alibaba.tripscrm.service.enums.system.MonitorErrorEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.*;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskBuildContext;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerAcquisitionLinkVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WxGroupInfoVO;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.ma.WxGroupMaService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.task.ability.old.second.TouchService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerAcquisitionService;
import com.alibaba.tripscrm.service.util.system.JsonUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.log.task.TaskCoreLogUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.trip.wireless.mc.client.hsf.EnterpriseWeChatService;
import com.taobao.trip.wireless.mc.client.hsf.domain.weChat.WxContactMeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 欢迎语任务实现类
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WelcomeTaskServiceImpl extends AbstractTaskTemplate {
    private final WechatCustomerAcquisitionService wechatCustomerAcquisitionService;
    private final EnterpriseWeChatService enterpriseWeChatService;
    private final ActivityContextService activityContextService;
    private final TagRelationService tagRelationService;
    private final WxGroupMaService wxGroupMaService;
    private final TouchService touchService;
    private final TaskFactory taskFactory;

    @PostConstruct
    public void init() {
        setTaskFactory();
    }

    @Override
    protected boolean taskExe(ActivityTaskInfoBO taskInfoBO, TaskBuildContext taskBuildContext) {
        if (!Boolean.parseBoolean(taskBuildContext.getExtraInfo().getOrDefault("needPush", "true"))) {
            return true;
        }

        WelcomeTaskContext welcomeTaskContext = (WelcomeTaskContext) getContext(taskInfoBO.getExtraJson());
        Map<String, String> extInfo = welcomeTaskContext.getTouchParam();
        extInfo.put("taskId", String.valueOf(taskInfoBO.getContextId()));

        // 获客助手连接
        if (BizTypeEnum.CUSTOMER_ACQUISITION_LINK.equals(BizTypeEnum.findByCode(taskBuildContext.getBizType()))) {
            String link = getCustomerAcquisitionLink(welcomeTaskContext, taskInfoBO, taskBuildContext);
            if (StringUtils.isEmpty(link)) {
                PlatformLogUtil.logFail("link empty", LogListUtil.newArrayList(taskInfoBO));
                return false;
            }

            extInfo.put("customer_channel", "hkzs_" + taskInfoBO.getContextId());
            extInfo.put("url", link);
        }

        if (!touchService.send(taskInfoBO.getTargetId(), welcomeTaskContext.getActionCode(), welcomeTaskContext.getBizCode(), extInfo)) {
            PlatformLogUtil.logFail("send fail", LogListUtil.newArrayList(taskInfoBO, welcomeTaskContext, extInfo));
            return false;
        }

        PlatformLogUtil.logFail("send success", LogListUtil.newArrayList(taskInfoBO, welcomeTaskContext, extInfo));
        return true;
    }

    private String getCustomerAcquisitionLink(WelcomeTaskContext welcomeTaskContext, ActivityTaskInfoBO taskInfoBO, TaskBuildContext taskBuildContext) {
        ItemTagRelationQuery tagRelationQuery = new ItemTagRelationQuery();
        if (StringUtils.isNumeric(welcomeTaskContext.getPersonMaTagId())) {
            tagRelationQuery.setTagId(Long.parseLong(welcomeTaskContext.getPersonMaTagId()));
        } else {
            String[] splits = welcomeTaskContext.getPersonMaTagId().split("_");
            tagRelationQuery.setTagId(Long.parseLong(splits[0]));
            tagRelationQuery.setSubCodeList(Lists.newArrayList(splits[1]));
        }
        tagRelationQuery.setItemType(taskBuildContext.getBizType());
        PageInfo<ItemTagRelationDTO> tagRelationPageInfo = tagRelationService.pageQuery(tagRelationQuery);
        if (tagRelationPageInfo == null || CollectionUtils.isEmpty(tagRelationPageInfo.getList())) {
            PlatformLogUtil.logFail("tagRelation pageQuery fail", LogListUtil.newArrayList(taskInfoBO, taskBuildContext, tagRelationQuery));
            TaskCoreLogUtils.log(MonitorErrorEnum.QUERY_MA_TAG_ID_ERROR, TaskType.WELCOME.getCode(), BizTypeEnum.CUSTOMER_ACQUISITION_LINK.getCode().longValue(), ActivityTargetTypeEnum.PHONE_MOBILE.getCode(), taskBuildContext.getTargetId(), "queryByTagId fail");
            return null;
        }

        WechatCustomerAcquisitionListQuery query = new WechatCustomerAcquisitionListQuery();
        query.setSpaceId(Long.parseLong(String.valueOf(JSONObject.parseObject(taskBuildContext.getContext()).getOrDefault("spaceId", 0L))));
        query.setIdList(tagRelationPageInfo.getList().stream().map(ItemTagRelationDTO::getItemId).map(Long::parseLong).collect(Collectors.toList()));
        PageInfo<WechatCustomerAcquisitionLinkVO> wechatCustomerAcquisitionLinkDOPageInfo = wechatCustomerAcquisitionService.pageQuery(query);
        if (Objects.isNull(wechatCustomerAcquisitionLinkDOPageInfo) || CollectionUtils.isEmpty(wechatCustomerAcquisitionLinkDOPageInfo.getList())) {
            PlatformLogUtil.logFail("wechatCustomerAcquisition pageQuery fail", LogListUtil.newArrayList(taskInfoBO, taskBuildContext, query));
            return null;
        }
        //上下文添加获客链接id
        JSONObject extraJson = taskInfoBO.getExtraJson();
        extraJson.put("linkId", wechatCustomerAcquisitionLinkDOPageInfo.getList().get(0).getLinkId());
        saveContext(taskInfoBO);
        return wechatCustomerAcquisitionService.convertToShortCustomerUrl(wechatCustomerAcquisitionLinkDOPageInfo.getList().get(0).getUrl(), "hkzs_" + taskInfoBO.getContextId());
    }

    /**
     * 填充回执信息
     *
     * @param eventMetaInfo
     * @param unionId
     * @return
     */
    @Override
    public EventMetaInfo getCallBackInfo(Long activityId, EventMetaInfo eventMetaInfo, String unionId) {
        try {
            //判断事件类型是否是添加用户
            if (!EnterpriseWeChatEventEnum.ADD_EXTERNAL_CONTACT.getName().equals(eventMetaInfo.getChangeType())) {
                PlatformLogUtil.logFail("changeType is not add_external_contact", LogListUtil.newArrayList(eventMetaInfo, unionId));
                return eventMetaInfo;
            }

            if (StringUtils.isEmpty(eventMetaInfo.getState())) {
                PlatformLogUtil.logFail("state is empty", LogListUtil.newArrayList(eventMetaInfo, unionId));
                return eventMetaInfo;
            }

            if (!eventMetaInfo.getState().startsWith("hkzs_")) {
                handleContactMe(activityId, eventMetaInfo, unionId);
                return eventMetaInfo;
            }

            handleAcquisition(eventMetaInfo);
            return eventMetaInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(activityId, eventMetaInfo, unionId));
            return eventMetaInfo;
        }
    }

    private void handleAcquisition(EventMetaInfo eventMetaInfo) {
        String contextId = eventMetaInfo.getState().substring(5);
        ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(Long.parseLong(contextId));
        PlatformLogUtil.logFail("user context", LogListUtil.newArrayList(activityTaskInfoBO));
        WelcomeTaskContext welcomeTaskContext = JSON.parseObject(activityTaskInfoBO.getExtraJson().toJSONString(), WelcomeTaskContext.class);
        welcomeTaskContext.setTouchParam(org.springframework.util.CollectionUtils.isEmpty(welcomeTaskContext.getTouchParam()) ? new HashMap<>() : welcomeTaskContext.getTouchParam());
        // 找到第一个
        WxGroupInfoVO wxGroupInfoVO = wxGroupMaService.findFirstNotFullGroupMa(welcomeTaskContext.getGroupMaTagId());
        if (Objects.isNull(wxGroupInfoVO) || StringUtils.isBlank(wxGroupInfoVO.getQrCode())) {
            PlatformLogUtil.logFail("no group ma", LogListUtil.newArrayList(welcomeTaskContext, wxGroupInfoVO));
            return;
        }

        welcomeTaskContext.getTouchParam().put("qrCode", wxGroupInfoVO.getQrCode());
        welcomeTaskContext.getTouchParam().put("taskId", String.valueOf(activityTaskInfoBO.getContextId()));
        eventMetaInfo.setExtraJson(JsonUtils.jsonMerge(JSONObject.parseObject(JSON.toJSONString(welcomeTaskContext.getTouchParam())), eventMetaInfo.getExtraJson()));
        if (StringUtils.isNotBlank(welcomeTaskContext.getWelcomeMessageTemplateId())) {
            eventMetaInfo.getExtraJson().put("welcomeMessageTemplateId", welcomeTaskContext.getWelcomeMessageTemplateId());
        }
    }

    private void handleContactMe(Long activityId, EventMetaInfo eventMetaInfo, String unionId) {
        // 查询个码标签和活动id
        WxContactMeVO wxContactMeVO = enterpriseWeChatService.getWxContactConfigByState(eventMetaInfo.getState());
        if (wxContactMeVO == null || !NumberUtils.validLong(wxContactMeVO.getId())) {
            PlatformLogUtil.logFail("getWxContactConfigByState is null", LogListUtil.newArrayList(eventMetaInfo, wxContactMeVO));
            return;
        }
        // 查询更新上下文
        ActivityTaskInfoBO taskInfoBO = new ActivityTaskInfoBO();
        taskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_UNION_ID);
        taskInfoBO.setTargetId(unionId);
        taskInfoBO.setActivityId(activityId);
        List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(taskInfoBO);
        if (CollectionUtils.isEmpty(activityTaskInfoBOS)) {
            PlatformLogUtil.logFail("activityTaskInfoBOS is null", LogListUtil.newArrayList(eventMetaInfo, taskInfoBO));
            return;
        }
        ActivityTaskInfoBO activityTaskInfoBO = activityTaskInfoBOS.get(0);
        // 第一期，取第一个，后续有归因策略
        if (SwitchConfig.CONTEXT_ORDER) {
            activityTaskInfoBO = ActivityContextService.getNewestBo(activityTaskInfoBOS);
        }
        PlatformLogUtil.logFail("user context", LogListUtil.newArrayList(activityTaskInfoBO));
        WelcomeTaskContext welcomeTaskContext = JSON.parseObject(activityTaskInfoBO.getExtraJson().toJSONString(), WelcomeTaskContext.class);
        welcomeTaskContext.setTouchParam(org.springframework.util.CollectionUtils.isEmpty(welcomeTaskContext.getTouchParam()) ? new HashMap<>() : welcomeTaskContext.getTouchParam());
        // 找到第一个
        WxGroupInfoVO wxGroupInfoVO = wxGroupMaService.findFirstNotFullGroupMa(welcomeTaskContext.getGroupMaTagId());
        if (Objects.isNull(wxGroupInfoVO) || StringUtils.isBlank(wxGroupInfoVO.getQrCode())) {
            PlatformLogUtil.logFail("no group ma", LogListUtil.newArrayList(welcomeTaskContext, wxGroupInfoVO));
            return;
        }

        welcomeTaskContext.getTouchParam().put("qrCode", wxGroupInfoVO.getQrCode());
        welcomeTaskContext.getTouchParam().put("taskId", String.valueOf(activityTaskInfoBO.getContextId()));
        if (StringUtils.isNotBlank(welcomeTaskContext.getWelcomeMessageTemplateId())) {
            welcomeTaskContext.getTouchParam().put("welcomeMessageTemplateId", String.valueOf(welcomeTaskContext.getWelcomeMessageTemplateId()));
        }
        eventMetaInfo.setExtraJson(JsonUtils.jsonMerge(JSONObject.parseObject(JSON.toJSONString(welcomeTaskContext.getTouchParam())), eventMetaInfo.getExtraJson()));
    }

    @Override
    protected TaskContext getContext(JSONObject contextJson) {
        return JSON.parseObject(contextJson.toJSONString(), WelcomeTaskContext.class);
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.WELCOME;
    }

    @Override
    protected void setTaskFactory() {
        taskFactory.setTaskTemplateMap(getTaskType(), this.getClass());
    }
}
