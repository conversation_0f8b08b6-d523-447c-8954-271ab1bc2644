package com.alibaba.tripscrm.service.service.callpush;

import com.alibaba.tripscrm.dal.model.domain.data.CallPushActivityRecordDO;
import com.alibaba.tripscrm.service.model.domain.query.CallPushActivityRecordCondition;
import com.alibaba.tripscrm.service.model.domain.query.CallPushActivityRecordQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/3 12:05
 */
public interface CallPushActivityRecordService {
    /**
     * 根据uid查询
     */
    List<CallPushActivityRecordDO> selectByCondition(CallPushActivityRecordQuery query);

    /**
     * 插入单条记录
     */
    Integer insert(CallPushActivityRecordDO record);

    /**
     * 删除单条记录
     */
    Integer delete(Long id);

    /**
     * 更新数据
     */
    Integer updateByCondition(CallPushActivityRecordDO record, CallPushActivityRecordCondition condition);

    /**
     * 更新单条记录
     */
    Integer updateById(CallPushActivityRecordDO record);

    /**
     * 更新单条记录
     */
    Integer updateByUidAndActivityIdAndDayVersion(CallPushActivityRecordDO record);
}
