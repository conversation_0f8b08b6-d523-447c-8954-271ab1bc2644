package com.alibaba.tripscrm.service.service.strategy.sop;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoDO;
import com.alibaba.tripscrm.service.enums.system.LogFieldEnum;
import com.alibaba.tripscrm.service.enums.system.OperationLogStatusEnum;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.log.SopLogContentBO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.model.vo.activity.AbstractActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.service.log.OperationLogInfoService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.log.OperatorLogUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 活动SOP抽象类
 *
 * <AUTHOR>
 * @date 2023-12-30 14:50:08
 */
@Component
public abstract class AbstractActivitySop<T extends AbstractActivitySopConfigVO> {
    @Resource
    private ActivityInfoService activityInfoService;
    @Resource
    private AccountService accountService;
    @Resource
    private OperationLogInfoService operationLogInfoService;

    /**
     * 校验活动配置是否有效
     *
     * @param config 活动配置
     */
    public void preCheck(T config, Boolean isUpdate) {
        if (Objects.isNull(config)) {
            throw new RuntimeException("配置不可为空");
        }

        if (isUpdate && !NumberUtils.validLong(config.getActivityId())) {
            throw new RuntimeException("活动Id不可为空");
        }

        if (!StringUtils.hasText(config.getName())) {
            throw new RuntimeException("活动名称不可为空");
        }

        if (Objects.isNull(config.getEffectStartTime())) {
            config.setEffectStartTime(new Date());
        }

        if (Objects.isNull(config.getEffectEndTime())) {
            Date effectEndTime = new Date(LocalDate.of(2040, 12, 30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
            config.setEffectEndTime(effectEndTime);
        }

        if (config.getEffectStartTime().after(config.getEffectEndTime())) {
            throw new RuntimeException("活动开始时间不可晚于结束时间");
        }

        if (!NumberUtils.validLong(config.getActivityId())) {
            boolean hasDuplicateName = activityInfoService.checkDuplicateName(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(), config.getName(), null);
            if (hasDuplicateName) {
                throw new RuntimeException("已存在重名活动");
            }
        }

        config.setDesc(StringUtils.hasText(config.getDesc()) ? config.getDesc() : "");
        checkConfigValid(config, isUpdate);
    }

    /**
     * 创建活动，并按顺序创建活动下的任务
     *
     * @param activitySopVO 活动配置
     * @return 活动Id
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(ActivitySopVO<T> activitySopVO) {
        preCheck(activitySopVO.getConfig(), false);
        Long activityId = createActivity(activitySopVO.getConfig());
        if (Objects.isNull(activityId)) {
            throw new RuntimeException("创建活动失败");
        }

        createTaskList(activitySopVO.getConfig(), activityId);
        return activityId;
    }


    /**
     * 更新活动，并按顺序更新活动下的任务
     *
     * @param activitySopVO 活动配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(ActivitySopVO<T> activitySopVO) {
        preCheck(activitySopVO.getConfig(), true);
        Long logId = preLogRecord(activitySopVO);
        Integer effectLines = updateActivity(activitySopVO.getConfig());
        if (Objects.isNull(effectLines) || effectLines == 0) {
            throw new RuntimeException("更新活动失败");
        }
        updateTaskList(activitySopVO.getConfig());
        if (logId == 0L) {
            operationLogInfoService.updateStatusById(logId, OperationLogStatusEnum.SUCCESS);
        }
    }


    private Long createActivity(T config) {
        User user = accountService.getUserInWebThread();
        if(user == null){
            user = new User();
            user.setIsInnerStaff(true);
            user.setUserName("刘育龙");
            user.setUserId("WB01127364");
        }
        ActivityInfoDO activityInfoDO = new ActivityInfoDO();
        activityInfoDO.setName(config.getName());
        activityInfoDO.setDescription(config.getDesc());
        activityInfoDO.setContext(JSONObject.toJSONString(config));
        activityInfoDO.setStatus(TaskStatusEnum.EDITING.getStatus());
        activityInfoDO.setCreatorId(user.getUserId());
        activityInfoDO.setCreatorName(user.getUserName());
        activityInfoDO.setMemberIds(StringUtils.hasText(config.getMemberIds()) ? config.getMemberIds() : user.getUserId());
        String memberNameList = Arrays.stream(activityInfoDO.getMemberIds().split(","))
                .map(x -> Optional.ofNullable(accountService.getUserByAccountId(x)).map(User::getUserName).orElse(null))
                .filter(StringUtils::hasText)
                .collect(Collectors.joining(","));
        activityInfoDO.setMemberNames(memberNameList);
        activityInfoDO.setLastOperatorId(user.getUserId());
        activityInfoDO.setLastOperatorName(user.getUserName());
        activityInfoDO.setSopType(getSopType().getCode());
        activityInfoDO.setSpaceId(NumberUtils.validLong(config.getSpaceId()) ? config.getSpaceId() : SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        return activityInfoService.add(activityInfoDO);
    }

    private Integer updateActivity(T config) {
        User user = accountService.getUserInWebThread();
        ActivityInfoDO activityInfoDO = new ActivityInfoDO();
        activityInfoDO.setId(config.getActivityId());
        activityInfoDO.setName(config.getName());
        activityInfoDO.setDescription(config.getDesc());
        activityInfoDO.setContext(JSONObject.toJSONString(config));
        activityInfoDO.setMemberIds(StringUtils.hasText(config.getMemberIds()) ? config.getMemberIds() : user.getUserId());
        String memberNameList = Arrays.stream(activityInfoDO.getMemberIds().split(","))
                .map(x -> Optional.ofNullable(accountService.getUserByAccountId(x)).map(User::getUserName).orElse(null))
                .filter(StringUtils::hasText)
                .collect(Collectors.joining(","));
        activityInfoDO.setMemberNames(memberNameList);
        if(Objects.nonNull(user)){
            activityInfoDO.setLastOperatorId(user.getUserId());
            activityInfoDO.setLastOperatorName(user.getUserName());
        }
        return activityInfoService.update(activityInfoDO);
    }

    /**
     * 前置日志记录
     * @param activitySopVO sop配置
     */
    private Long preLogRecord(ActivitySopVO<T> activitySopVO) {
        if (!SwitchConfig.SOP_LOG_WHITE_LIST.contains(activitySopVO.getSopType())) {
            return 0L;
        }
        ActivityInfoDO activityInfoDO = activityInfoService.queryInfoById(activitySopVO.getConfig().getActivityId());
        if (activityInfoDO == null) {
            return 0L;
        }
        List<SopLogContentBO> diffInfo = OperatorLogUtils.getDiffInfo(activitySopVO.getConfig(), activityInfoDO.getContext());
        PlatformLogUtil.logFail("活动SOP，前置日志记录", LogListUtil.newArrayList(diffInfo));
        List<LogFieldEnum> sopLogFieldEnumList = LogFieldEnum.getFieldListBySopType(getSopType());
        if (CollectionUtils.isEmpty(sopLogFieldEnumList)) {
            return 0L;
        }
        List<String> sopLogFieldList = sopLogFieldEnumList.stream().map(LogFieldEnum::getCode).collect(Collectors.toList());
        diffInfo = diffInfo.stream().filter(sopLogVO -> sopLogFieldList.contains(sopLogVO.getName())).collect(Collectors.toList());
        // 落库
        if (CollectionUtils.isEmpty(diffInfo)) {
            return 0L;
        }
        OperationLogInfoDO operationLogInfoDO = OperatorLogUtils.buildActivityTypeLogDO(activitySopVO.getOperatorId(), activitySopVO.getOperatorName(), String.valueOf(activitySopVO.getActivityId()), OperationLogStatusEnum.FAIL, diffInfo);
        operationLogInfoService.insert(operationLogInfoDO);
        return operationLogInfoDO.getId();
    }

    /**
     * 子类实现的配置检查
     *
     * @param config 活动配置
     * @param isUpdate 更新
     */
    protected abstract void checkConfigValid(T config, Boolean isUpdate);

    /**
     * 创建活动，并按顺序创建活动下的任务
     *
     * @param config     活动配置
     * @param activityId 活动Id
     */
    protected abstract void createTaskList(T config, Long activityId);

    /**
     * 更新活动，并按顺序更新活动下的任务
     *
     * @param config 活动配置
     */
    protected abstract void updateTaskList(T config);

    /**
     * Sop类型
     *
     * @return SopTypeEnum
     */
    public abstract SopTypeEnum getSopType();

    /**
     * 任务名称
     *
     * @return 任务名称
     */
    protected String getTaskName(T config) {
        return config.getName() + "_" + UUID.randomUUID().toString().substring(0, 10);
    }

    /**
     * 填充额外信息（前端展示使用）
     *
     * @param activitySopVO activitySopVO
     */
    public abstract void fillInfo(ActivitySopVO<T> activitySopVO);
}
