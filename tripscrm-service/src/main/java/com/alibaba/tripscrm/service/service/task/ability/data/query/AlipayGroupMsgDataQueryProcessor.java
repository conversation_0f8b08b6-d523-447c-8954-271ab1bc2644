package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 支付宝群发消息任务执行前数据查询
 *
 * <AUTHOR>
 * @since 2024-08-02 15:05:38
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AlipayGroupMsgDataQueryProcessor extends AbstractTaskDataProcessor {
    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.ALIPAY_GROUP_MSG);
    }

    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        try {
            JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
            List<String> groupTemplateIdList = extInfo.getJSONArray(TaskConstant.ALIPAY_GROUP_TEMPLATE_ID_LIST).toJavaList(String.class);
            if (CollectionUtils.isEmpty(groupTemplateIdList)) {
                PlatformLogUtil.logFail("支付宝群发消息任务执行前数据查询失败，人群id或群组id列表为空", LogListUtil.newArrayList(context));
                throw new TripscrmException(TripSCRMErrorCode.ALIPAY_MSG_EXT_INFO_INVALID);
            }
            TaskDataVO taskDataVO = new TaskDataVO();
            taskDataVO.setTotalCount(1L);
            TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
            dataBodyVO.setTargetId(String.join(",", groupTemplateIdList));
            dataBodyVO.setTargetType(ActivityTargetTypeEnum.ALIPAY_GROUP_TEMPLATE_ID_LIST.getCode());
            dataBodyVO.setExtInfo(taskInfoDO.getExtInfo());
            taskDataVO.setData(Lists.newArrayList(dataBodyVO));
            return taskDataVO;
        } catch (Exception e) {
            PlatformLogUtil.logException("支付宝群发消息任务执行前数据查询出错", e.getMessage(), e, LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.ALIPAY_MSG_EXT_INFO_INVALID);
        }
    }
}
