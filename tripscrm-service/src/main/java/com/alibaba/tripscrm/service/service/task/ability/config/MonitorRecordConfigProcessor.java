package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MonitorRecordConfigProcessor extends AbstractTaskConfigProcessor {

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {

    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {

    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {

    }

    @Override
    public TaskType getTaskType() {
        return TaskType.MONITOR_RECORD;
    }
}
