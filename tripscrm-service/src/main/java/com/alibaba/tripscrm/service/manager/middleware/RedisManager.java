package com.alibaba.tripscrm.service.manager.middleware;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.taobao.eagleeye.redis.clients.jedis.Jedis;
import com.taobao.eagleeye.redis.clients.jedis.JedisPool;
import com.taobao.eagleeye.redis.clients.jedis.JedisPoolConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2024/6/27 16:57
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@ConditionalOnExpression("!'${env}'.equalsIgnoreCase('daily')")
public class RedisManager {
    // 初始化连接超时时间
    private static final int DEFAULT_CONNECTION_TIMEOUT = 5000;
    // 查询超时时间
    private static final int DEFAULT_SO_TIMEOUT = 2000;

    /**
     * 连接池
     */
    private static JedisPool jedisPool;

    /**
     * 代理连接地址，用控制台上的"代理地址"
     */
    @Value("${redis.host}")
    private String host;

    @Value("${redis.port}")
    private Integer port;

    @Value("${redis.instanceId}")
    private String instanceId;

    private final CredentialProvider credentialProvider;

    /**
     * 执行命令
     */
    public void execute(Consumer<Jedis> command) {
        try (Jedis jedis = jedisPool.getResource()) {
            // 查询前获取一个连接
            command.accept(jedis);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行redis命令失败", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    @PostConstruct
    public void init() {
        if (!StringUtils.hasLength(instanceId)) {
            return;
        }

        String rn = ResourceNames.ofAliyunKvStoreInstanceId(instanceId);
        Credential credential = credentialProvider.getCredential(rn);
        // 拼接 Redis 连接使用的密码："{username}:{password}"
        String password = credential.getUsername() + ":" + credential.getPassword();

        // 设置参考：https://alidocs.dingtalk.com/i/nodes/dQPGYqjpJYZnRbNYCDpy5d1x8akx1Z5N
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxTotal(32);
        config.setMaxIdle(32);
        config.setMinIdle(16);
        // 如果是集群版带Proxy节点，则打开此行，可避免连接不均衡问题
        config.setLifo(false);

        // 只需要初始化一次
        try {
            jedisPool = new JedisPool(config, host, port, DEFAULT_CONNECTION_TIMEOUT, DEFAULT_SO_TIMEOUT, password, 0, null);
            try (Jedis jedis = jedisPool.getResource()) {
                if (!"PONG".equals(jedis.ping())) {
                    throw new TripscrmException("Init Failed");
                }
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("redis客户端初始化失败", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    @PreDestroy
    public void close() {
        if (Objects.nonNull(jedisPool)) {
            jedisPool.close();
        }
    }

    /**
     * 获取单个key的值
     *
     * @param key 要获取的key
     * @return key对应的value
     */
    public String get(String key) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get(key);
        } catch (Exception e) {
            PlatformLogUtil.logException("获取redis key失败", e.getMessage(), e, LogListUtil.newArrayList());
            return null;
        }
    }

    /**
     * 获取多个key的值
     *
     * @param keys 要获取的key
     * @return keys对应的value
     */
    public List<String> mget(List<String> keys) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.mget(keys.toArray(new String[0]));
        } catch (Exception e) {
            PlatformLogUtil.logException("获取redis keys失败", e.getMessage(), e, LogListUtil.newArrayList());
            return null;
        }
    }
//public void execute(Consumer<Jedis> command) {
//
//}
}
