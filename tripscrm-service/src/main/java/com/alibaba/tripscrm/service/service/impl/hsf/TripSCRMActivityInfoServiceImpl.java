package com.alibaba.tripscrm.service.service.impl.hsf;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.ActivityInfo;
import com.alibaba.tripscrm.domain.ActivityPageInfo;
import com.alibaba.tripscrm.domain.WelcomeTaskContext;
import com.alibaba.tripscrm.domain.request.TripSCRMActivityInfoRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMActivityInfoService;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MonitorErrorEnum;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskBuildContext;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.vo.wechat.ContactMeVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WxGroupInfoVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.ma.WxGroupMaService;
import com.alibaba.tripscrm.service.service.ma.WxPersonMaService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.task.ability.old.WelcomeTaskServiceImpl;
import com.alibaba.tripscrm.service.util.log.task.TaskCoreLogUtils;
import com.alibaba.tripscrm.service.util.log.ttlog.TtMobileLogUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 飞猪活动信息hsf接口
 *
 * <AUTHOR>
 * @date 2023/4/11
 */
@Slf4j
@HSFProvider(serviceInterface = TripSCRMActivityInfoService.class)
public class TripSCRMActivityInfoServiceImpl implements TripSCRMActivityInfoService {

    @Autowired
    private TagRelationService tagRelationService;

    @Autowired
    private WxPersonMaService wxPersonMaService;

    @Autowired
    private WxGroupMaService wxGroupMaService;

    @Autowired
    private ActivityContextService activityContextService;

    @Autowired
    private WelcomeTaskServiceImpl welcomeTaskService;

    @Override
    @ServiceLog("老活动-查询活动页面信息")
    public TripSCRMResult<ActivityPageInfo> queryPageInfo(TripSCRMActivityInfoRequest request) {
        ItemTagRelationQuery tagRelationQuery = new ItemTagRelationQuery();
        tagRelationQuery.setTagId(request.getTagId());
        tagRelationQuery.setSubCodeList(Lists.newArrayList(request.getSubCode()));
        tagRelationQuery.setItemType(request.getBizType());
        PageInfo<ItemTagRelationDTO> tagRelationPageInfo = tagRelationService.pageQuery(tagRelationQuery);
        if (tagRelationPageInfo == null || CollectionUtils.isEmpty(tagRelationPageInfo.getList())) {
            PlatformLogUtil.logFail("pageQuery fail", LogListUtil.newArrayList(request, tagRelationQuery, tagRelationPageInfo));
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }

        ActivityPageInfo activityPageInfo = new ActivityPageInfo();
        activityPageInfo.setBizType(request.getBizType());
        //获取个码信息
        if (request.getBizType().equals(BizTypeEnum.PERSON_MA_OLD.getCode())) {
            ContactMeVO contactMeVO = wxPersonMaService.queryContactMeById(Long.parseLong(tagRelationPageInfo.getList().get(0).getItemId()));
            if (contactMeVO == null) {
                PlatformLogUtil.logFail("queryContactMeById fail", LogListUtil.newArrayList(request, tagRelationPageInfo));
                return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
            }
            activityPageInfo.setMaterial(contactMeVO.getQrUrl());
        }
        //获取群码信息
        if (request.getBizType().equals(BizTypeEnum.GROUP_MA_OLD.getCode())) {
            WxGroupInfoVO wxGroupInfoVO = wxGroupMaService.queryWxGroupMaInfoById(Long.parseLong(tagRelationPageInfo.getList().get(0).getItemId()));
            if (wxGroupInfoVO == null) {
                PlatformLogUtil.logFail("queryWxGroupMaInfoById fail", LogListUtil.newArrayList(request, tagRelationPageInfo));
                return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
            }
            activityPageInfo.setMaterial(wxGroupInfoVO.getQrCode());
        }
        //保存上下文信息
        ActivityTaskInfoBO taskInfoBO = new ActivityTaskInfoBO();
        taskInfoBO.setContextId(activityContextService.generateContextId());
        taskInfoBO.setActivityId(request.getActivityId());
        taskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.codeOf(request.getTargetType()));
        taskInfoBO.setTargetId(String.valueOf(request.getUserId()));
        String tagId = Objects.nonNull(request.getSubCode()) ? (request.getTagId() + "_" + request.getSubCode()) : String.valueOf(request.getTagId());
        taskInfoBO.setTagIdList(Lists.newArrayList(tagId));
        taskInfoBO.setExtraJson(JSONObject.parseObject(request.getContext()));
        if (activityContextService.upsert(taskInfoBO) < 1) {
            PlatformLogUtil.logFail("activityTaskInfoService upsert fail", LogListUtil.newArrayList(request, taskInfoBO));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
        PlatformLogUtil.logFail("activityTaskInfoService upsert success", LogListUtil.newArrayList(request, taskInfoBO));
        return TripSCRMResult.success(activityPageInfo);
    }

    @Override
    @ServiceLog("老活动-根据id查询活动页面信息")
    public TripSCRMResult<ActivityPageInfo> queryPageInfoByTaskId(TripSCRMActivityInfoRequest request) {
        try {
            if (Objects.isNull(request) || !NumberUtils.validLong(request.getTaskId()) || !NumberUtils.validInteger(request.getBizType())) {
                PlatformLogUtil.logFail("queryPageInfoByTaskId param error", LogListUtil.newArrayList(request));
                TaskCoreLogUtils.log(MonitorErrorEnum.QUERY_MA_PARAM_INVALID, "welcome", BizTypeEnum.PERSON_MA_OLD.getCode().longValue(), ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode(), request.getUserId().toString(), "queryPageInfoByTaskId param error");
                return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
            }
            TaskCoreLogUtils.log(MonitorErrorEnum.QUERY_MA_COUNT, "welcome", BizTypeEnum.PERSON_MA_OLD.getCode().longValue(), ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode(), request.getUserId().toString(), "query count with param valid");
            ActivityPageInfo activityPageInfo = new ActivityPageInfo();
            activityPageInfo.setBizType(request.getBizType());
            // 查询活动上下文
            ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(request.getTaskId());
            if (Objects.isNull(activityTaskInfoBO)) {
                PlatformLogUtil.logFail("queryByTaskId is null", LogListUtil.newArrayList(request));
                TaskCoreLogUtils.log(MonitorErrorEnum.QUERY_MA_TASK_ID_ERROR, "welcome", BizTypeEnum.PERSON_MA_OLD.getCode().longValue(), ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode(), request.getUserId().toString(), "queryByTaskId fail");
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }
            WelcomeTaskContext welcomeTaskContext = JSON.parseObject(activityTaskInfoBO.getExtraJson().toJSONString(), WelcomeTaskContext.class);
            // 更新活动上下文
            activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_UNION_ID);
            activityTaskInfoBO.setTargetId(request.getUnionId());
            int upsertRet = activityContextService.upsert(activityTaskInfoBO);
            if (!NumberUtils.validInteger(upsertRet)) {
                PlatformLogUtil.logFail("上下文更新失败", LogListUtil.newArrayList(request, activityTaskInfoBO));
            } else {
                PlatformLogUtil.logFail("上下文更新成功", LogListUtil.newArrayList(request, activityTaskInfoBO));
            }

            // tt日志，打印手机号和用户userid的关系
            if (activityTaskInfoBO.getExtraJson() != null && activityTaskInfoBO.getExtraJson().containsKey("PC_MOBILE")) {
                TtMobileLogUtils.log(activityTaskInfoBO.getExtraJson().getString("PC_MOBILE"), request.getUserId(), request.getUnionId());
            }

            //获取个码信息
            if (request.getBizType().equals(BizTypeEnum.PERSON_MA_OLD.getCode())) {
                // 查找交叉关系
                ItemTagRelationQuery tagRelationQuery = new ItemTagRelationQuery();
                if (StringUtils.isNumeric(welcomeTaskContext.getPersonMaTagId())) {
                    tagRelationQuery.setTagId(Long.parseLong(welcomeTaskContext.getPersonMaTagId()));
                } else {
                    String[] splits = welcomeTaskContext.getPersonMaTagId().split("_");
                    tagRelationQuery.setTagId(Long.parseLong(splits[0]));
                    tagRelationQuery.setSubCodeList(Lists.newArrayList(splits[1]));
                }
                tagRelationQuery.setItemType(request.getBizType());
                PageInfo<ItemTagRelationDTO> tagRelationPageInfo = tagRelationService.pageQuery(tagRelationQuery);
                if (tagRelationPageInfo == null || CollectionUtils.isEmpty(tagRelationPageInfo.getList())) {
                    PlatformLogUtil.logFail("pageQuery fail", LogListUtil.newArrayList(request, tagRelationQuery, tagRelationPageInfo));
                    TaskCoreLogUtils.log(MonitorErrorEnum.QUERY_MA_TAG_ID_ERROR, "welcome", BizTypeEnum.PERSON_MA_OLD.getCode().longValue(), ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode(), request.getUserId().toString(), "queryMaByTagId fail");
                    return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
                }
                ContactMeVO contactMeVO = wxPersonMaService.queryContactMeById(Long.parseLong(tagRelationPageInfo.getList().get(0).getItemId()));
                if (contactMeVO == null) {
                    PlatformLogUtil.logFail("queryContactMeById fail", LogListUtil.newArrayList(request, tagRelationPageInfo, contactMeVO));
                    TaskCoreLogUtils.log(MonitorErrorEnum.QUERY_MA_CONTEXT_ERROR, "welcome", BizTypeEnum.PERSON_MA_OLD.getCode().longValue(), ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode(), request.getUserId().toString(), "queryMaDetailById fail");
                    return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
                }
                activityPageInfo.setMaterial(contactMeVO.getQrUrl());
            }
            //获取群码信息
            if (request.getBizType().equals(BizTypeEnum.GROUP_MA_OLD.getCode())) {
                WxGroupInfoVO wxGroupInfoVO = wxGroupMaService.findFirstNotFullGroupMa(welcomeTaskContext.getGroupMaTagId());
                if (Objects.isNull(wxGroupInfoVO) || StringUtils.isBlank(wxGroupInfoVO.getQrCode())) {
                    PlatformLogUtil.logFail("no group ma", LogListUtil.newArrayList(request, welcomeTaskContext));
                    TaskCoreLogUtils.log(MonitorErrorEnum.QUERY_MA_CONTEXT_ERROR, "welcome", BizTypeEnum.PERSON_MA_OLD.getCode().longValue(), ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode(), request.getUserId().toString(), "queryMaDetailById fail");
                    return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
                }

                PlatformLogUtil.logFail("find group success", LogListUtil.newArrayList(request, wxGroupInfoVO));
                activityPageInfo.setMaterial(wxGroupInfoVO.getQrCode());
            }
            return TripSCRMResult.success(activityPageInfo);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            TaskCoreLogUtils.log(MonitorErrorEnum.QUERY_MA_EXCEPTION, "welcome", BizTypeEnum.PERSON_MA_OLD.getCode().longValue(), ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode(), request.getUserId().toString(), "exception happen");
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @Override
    @ServiceLog("老活动-根据活动和userid查询下上文内容")
    public TripSCRMResult<List<ActivityInfo>> queryActivityInfoByUserIdAndActivityId(Long activityId, Long userId) {
        if (activityId == null || userId == null) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        List<ActivityInfo> activityInfos = new ArrayList<>();
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.TAOBAO_USER_ID);
        activityTaskInfoBO.setTargetId(String.valueOf(userId));
        activityTaskInfoBO.setActivityId(activityId);
        List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(activityTaskInfoBO);
        if (CollectionUtils.isEmpty(activityTaskInfoBOS)) {
            JSONObject requestJson = new JSONObject();
            requestJson.put("activityId", activityId);
            requestJson.put("userId", userId);
            PlatformLogUtil.logFail("queryByActivityAndTarget is null", LogListUtil.newArrayList(requestJson, activityTaskInfoBO));
            return TripSCRMResult.success(activityInfos);
        }
        activityTaskInfoBOS.forEach(taskInfoBO -> activityInfos.add(convert2ActivityInfo(taskInfoBO)));
        return TripSCRMResult.success(activityInfos);
    }

    @Override
    @ServiceLog("老活动-触发scrm活动任务")
    public TripSCRMResult<Long> buildTask(TripSCRMActivityInfoRequest request) {
        try {
            PlatformLogUtil.logInfo("日志信息", LogListUtil.newArrayList(request));
            TaskBuildContext taskBuildContext = new TaskBuildContext();
            BeanUtils.copyProperties(request, taskBuildContext);
            Long taskId = welcomeTaskService.buildTask(taskBuildContext);
            if (NumberUtils.validLong(taskId)) {
                return TripSCRMResult.success(taskId);
            }
            // 执行活动
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
    }

    private ActivityInfo convert2ActivityInfo(ActivityTaskInfoBO taskInfoBO) {
        ActivityInfo activityInfo = new ActivityInfo();
        activityInfo.setActivityId(taskInfoBO.getActivityId());
        activityInfo.setContext(JSON.toJSONString(taskInfoBO.getExtraJson()));
        activityInfo.setTagIdList(taskInfoBO.getTagIdList().stream().filter(StringUtils::isNumeric).map(Long::parseLong).collect(Collectors.toList()));
        activityInfo.setNewTagIdList(taskInfoBO.getTagIdList());
        activityInfo.setTargetId(taskInfoBO.getTargetId());
        activityInfo.setTargetTypeCode(taskInfoBO.getTargetTypeEnum().getCode());
        activityInfo.setTaskId(taskInfoBO.getContextId());
        return activityInfo;
    }
}
