package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.vo.wechat.MomentInfoVO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.moment.MomentInfoService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 朋友圈任务配置处理器
 * <AUTHOR>
 * @Date 2024/1/29 11:24
 **/
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MomentTaskConfigProcessor extends AbstractTaskConfigProcessor{

    private final MomentInfoService momentInfoService;
    private final TagInfoService tagInfoService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        JSONObject extraInfo = super.getExtraInfo(taskInfoDO);
        MomentInfoVO momentInfoVO = momentInfoService.queryMomentInfo(taskInfoDO);
        if (!extraInfo.containsKey("tagList")) {
            return (JSONObject) JSONObject.toJSON(momentInfoVO);
        }
        List<Long> tagList = extraInfo.getObject("tagList", new TypeReference<List<Long>>() {});
        List<String> tagIdList = tagList.stream().map(String::valueOf).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tagIdList)) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(tagIdList);
            List<String> tagNameList = tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList());
            momentInfoVO.setTagList(tagList);
            momentInfoVO.setTagNameList(tagNameList);
        }
        return (JSONObject) JSONObject.toJSON(momentInfoVO);
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {

    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {

    }

    @Override
    public TaskType getTaskType() {
        return TaskType.SEND_TO_MOMENTS;
    }
}
