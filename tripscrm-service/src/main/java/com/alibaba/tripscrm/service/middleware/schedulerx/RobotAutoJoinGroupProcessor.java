package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.risk.SourceTypeEnum;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.wx.WXBizMsgCryptConfig;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.alibaba.tripzoo.proxy.api.service.RobotService;
import com.alibaba.tripzoo.proxy.model.RobotConfigBO;
import com.alibaba.tripzoo.proxy.request.AutoJoinGroupSwitchRequest;
import com.alibaba.tripzoo.proxy.request.RobotConfigRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 打开机器人自动入群开关
 *
 * <AUTHOR>
 * @Date 2024/4/23 14:48
 **/
@Component
public class RobotAutoJoinGroupProcessor extends JavaProcessor {

    @Resource
    private RobotService robotService;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private WXCorpStorage wxCorpStorage;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            for (WXBizMsgCryptConfig corpConfig : wxCorpStorage.listConfig()) {
                String corpId = corpConfig.getCorpId();
                SpaceInfoThreadLocalUtils.setCorpId(corpId).setSourceType(SourceTypeEnum.SYSTEM_OPT);
                // 激活的机器人列表
                List<WechatUserDTO> wechatUserList = wechatUserService.listAll().stream().filter(wechatUserDTO -> wechatUserDTO.getStatus() == 1).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(wechatUserList)) {
                    PlatformLogUtil.logFail("不存在激活的成员列表", LogListUtil.newArrayList());
                    return new ProcessResult(true);
                }
                // 未打开入群开关的机器人
                RobotConfigRequest request = new RobotConfigRequest();
                request.setUserIdList(wechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()));
                request.setCorpId(corpId);
                ResultDO<List<RobotConfigBO>> configResultDO = robotService.robotConfigs(request);
                if (!configResultDO.getSuccess() || CollectionUtils.isEmpty(configResultDO.getModel())) {
                    PlatformLogUtil.logFail("请求机器人开关信息失败", LogListUtil.newArrayList(wechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()), configResultDO));
                    return new ProcessResult(false, configResultDO.getResultMessage());
                }
                List<RobotConfigBO> needOpenSwitchRobotList = configResultDO.getModel().stream().filter(robotConfigBO -> !robotConfigBO.getAutoJoinGroup()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(needOpenSwitchRobotList)) {
                    PlatformLogUtil.logFail("不存在需要打开开关的机器人", LogListUtil.newArrayList());
                    return new ProcessResult(true);
                }
                // 打开开关
                for (RobotConfigBO robotConfigBO : needOpenSwitchRobotList) {
                    AutoJoinGroupSwitchRequest autoJoinGroupSwitchRequest = new AutoJoinGroupSwitchRequest();
                    autoJoinGroupSwitchRequest.setCorpId(corpId);
                    autoJoinGroupSwitchRequest.setUserId(robotConfigBO.getUserId());
                    autoJoinGroupSwitchRequest.setEnable(true);
                    ResultDO<Boolean> resultDO = robotService.autoJoinGroupSwitch(autoJoinGroupSwitchRequest);
                    PlatformLogUtil.logInfo("机器人自动入群开关操作", LogListUtil.newArrayList(robotConfigBO.getUserId(), corpId, resultDO));
                }
            }
            return new ProcessResult(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("打开机器人自动入群开关异常", e.getMessage(), e, LogListUtil.newArrayList());
            return new ProcessResult(false, e.getMessage());
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }
}
