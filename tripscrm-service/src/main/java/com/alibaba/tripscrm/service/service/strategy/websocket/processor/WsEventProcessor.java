package com.alibaba.tripscrm.service.service.strategy.websocket.processor;

import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import org.springframework.web.socket.WebSocketSession;

/**
 * websocket事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
public interface WsEventProcessor {
    /**
     * ws通讯类型
     *
     * @return ws通讯类型
     */
    WsEventTypeEnum type();

    /**
     * 该通讯类型的接收处理
     *
     * @param session 当前session
     * @param wsEvent 通讯类型
     */
    void handle(WebSocketSession session, WsEvent wsEvent);
}
