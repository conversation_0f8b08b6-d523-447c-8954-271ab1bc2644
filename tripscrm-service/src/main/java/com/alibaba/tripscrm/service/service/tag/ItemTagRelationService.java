package com.alibaba.tripscrm.service.service.tag;

import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationCoverRequest;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ItemTagRelationService {
    /**
     * 根据条件查询
     *
     * @param query
     */
    List<ItemTagRelationDTO> selectByCondition(ItemTagRelationQuery query);

    /**
     * 根据条件查询标签
     *
     * @param query
     */
    List<TagInfoDTO> selectTagInfoByCondition(ItemTagRelationQuery query);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageInfo<ItemTagRelationDTO> pageQuery(ItemTagRelationQuery query);

    /**
     * 批量创建
     *
     * @param list
     */
    Integer batchUpsertSelective(List<ItemTagRelationDTO> list);

    /**
     * 创建
     *
     * @param record
     * @return
     */
    Integer upsertSelective(ItemTagRelationDTO record);

    /**
     * 根据条件修改
     *
     * @param record
     * @param condition
     */
    Integer updateSelective(ItemTagRelationDTO record, ItemTagRelationQuery condition);

    /**
     * 标签关系覆盖
     *
     * @param request
     */
    @Deprecated
    void cover(ItemTagRelationCoverRequest request);

    /**
     * 给客户打基本标签
     *
     * @param externalUserId 客户externalUserId
     * @param corpId 企业id
     * @param uid 客户淘宝id
     */
    void addBasicTag(String externalUserId, String corpId, String uid);
}