package com.alibaba.tripscrm.service.service.strategy.websocket;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.model.exception.WarnException;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 聚合聊天websocket事件路由分发
 *
 * <AUTHOR>
 * @date 2023/9/28
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class WsEventRouter {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private Map<String, WsEventProcessor> wsEventProcessors;
    private final Map<String, WsEventProcessor> wsEventProcessorMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (Map.Entry<String, WsEventProcessor> entry : wsEventProcessors.entrySet()) {
            wsEventProcessorMap.put(entry.getValue().type().getValue(), entry.getValue());
        }
    }

    /**
     * 路由ws事件到对应处理器
     *
     * @param session session
     * @param msgBody msgBody
     */
    public void routeProcessor(WebSocketSession session, String msgBody) {
        WsEvent wsEvent = new WsEvent();
        try {
            wsEvent = JSONObject.parseObject(msgBody, WsEvent.class);
            WsEventProcessor processor = wsEventProcessorMap.get(wsEvent.getType());
            if (processor == null) {
                throw new Exception("找不到相应的processor");
            }
            wsEvent.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
            processor.handle(session, wsEvent);
        } catch (WarnException e) {
            // 若失败则推送失败
            wsEvent.setSuccess(false);
            wsEvent.setWarn(true);
            wsEvent.setErrorMessage(e.getErrorMsg());
            webSocketFactory.pushMessageBySession(session, wsEvent, true);
        } catch (Exception e) {
            PlatformLogUtil.logException("路由ws事件到对应处理器失败", e.getMessage(), e, LogListUtil.newArrayList(session.getId(), wsEvent));
            // 若失败则推送失败
            wsEvent.setSuccess(false);
            wsEvent.setErrorMessage(e.getMessage());
            webSocketFactory.pushMessageBySession(session, wsEvent, true);
        }
    }
}
