package com.alibaba.tripscrm.service.service.strategy.log;

import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.model.domain.log.BatchOperateLogContentBO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/4 14:27
 **/
public class BatchOperateLogHandleService extends AbstractLogHandleService<BatchOperateLogContentBO>{

    @Override
    public LogShowTypeEnum handleType() {
        return null;
    }

    @Override
    protected void replaceLogContentInfo(BatchOperateLogContentBO batchOperateLogContentBO, ResourceTypeEnum resourceTypeEnum) {
        switch (resourceTypeEnum) {
            case CHAT_ID:
                fillGroupInfo(batchOperateLogContentBO);
                break;
            case WECHAT_USER:
                fillWechatUserInfo(batchOperateLogContentBO);
                break;
            case EXTERNAL_USER_ID:
                fillWechatCustomerInfo(batchOperateLogContentBO);
                break;
            default:
                break;
        }
    }

    /**
     * 填充群聊信息
     * @param batchOperateLogContentBO 批量操作日志内容
     */
    private void fillGroupInfo(BatchOperateLogContentBO batchOperateLogContentBO) {
        List<String> groupNameList = queryGroupNameList(batchOperateLogContentBO.getInvalidTargetIdList());
        if (CollectionUtils.isEmpty(groupNameList)) {
            return;
        }
        batchOperateLogContentBO.setInvalidTargetNameList(groupNameList);
    }

    /**
     * 填充成员信息
     * @param batchOperateLogContentBO 批量操作日志内容
     */
    private void fillWechatUserInfo(BatchOperateLogContentBO batchOperateLogContentBO) {
        List<String> wechatUserNameList = queryUserName(batchOperateLogContentBO.getInvalidTargetIdList());
        if (CollectionUtils.isEmpty(wechatUserNameList)) {
            return ;
        }
        batchOperateLogContentBO.setInvalidTargetNameList(wechatUserNameList);
    }

    /**
     * 填充企微客户信息
     * @param batchOperateLogContentBO 批量操作日志内容
     */
    private void fillWechatCustomerInfo(BatchOperateLogContentBO batchOperateLogContentBO) {
        List<String> wechatCustomerNameList = queryCustomerName(batchOperateLogContentBO.getInvalidTargetIdList());
        if (CollectionUtils.isEmpty(wechatCustomerNameList)) {
            return ;
        }
        batchOperateLogContentBO.setInvalidTargetNameList(wechatCustomerNameList);
    }

}
