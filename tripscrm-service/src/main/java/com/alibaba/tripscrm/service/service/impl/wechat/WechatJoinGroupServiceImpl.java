package com.alibaba.tripscrm.service.service.impl.wechat;

import com.alibaba.tripscrm.dal.mapper.tddl.WechatJoinGroupMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatJoinGroupDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatJoinGroupQuery;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.wechat.WechatJoinGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.JoinGroupService;
import com.alibaba.tripzoo.proxy.model.WechatJoinGroupBO;
import com.alibaba.tripzoo.proxy.request.AddJoinGroupRequest;
import com.alibaba.tripzoo.proxy.request.DeleteJoinGroupRequest;
import com.alibaba.tripzoo.proxy.request.UpdateJoinGroupRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024-01-01 16:49:17
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatJoinGroupServiceImpl implements WechatJoinGroupService {
    private final JoinGroupService joinGroupService;
    private final WechatJoinGroupMapper wechatJoinGroupMapper;
    private final SpaceService spaceService;

    @Override
    public WechatJoinGroupDO create(List<String> chatIdList, String state, Long spaceId, String corpId) {
        // 调用
        AddJoinGroupRequest request = new AddJoinGroupRequest();
        request.setChatIdList(chatIdList);
        request.setState(state);
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<WechatJoinGroupBO> resultDO = joinGroupService.addJoinGroup(request);

        if (Objects.isNull(resultDO)) {
            throw new RuntimeException("系统出错");
        }

        if (!resultDO.getSuccess()) {
            throw new RuntimeException(resultDO.getResultMessage());
        }
        if (!StringUtils.hasText(corpId)) {
            corpId = spaceService.getCorpIdBySpaceId(spaceId);
        }
        WechatJoinGroupBO wechatJoinGroupBO = resultDO.getModel();
        WechatJoinGroupDO wechatJoinGroupDO = new WechatJoinGroupDO();
        wechatJoinGroupDO.setConfigId(wechatJoinGroupBO.getConfigId());
        wechatJoinGroupDO.setScene(wechatJoinGroupBO.getScene());
        wechatJoinGroupDO.setRemark(Optional.ofNullable(wechatJoinGroupBO.getRemark()).orElse(""));
        wechatJoinGroupDO.setState(wechatJoinGroupBO.getState());
        wechatJoinGroupDO.setQrCode(wechatJoinGroupBO.getQrCode());
        wechatJoinGroupDO.setAutoCreateRoom(Optional.ofNullable(wechatJoinGroupBO.getAutoCreateRoom()).orElse(0));
        wechatJoinGroupDO.setRoomBaseId(Optional.ofNullable(wechatJoinGroupBO.getRoomBaseId()).orElse(-1));
        wechatJoinGroupDO.setRoomBaseName(Optional.ofNullable(wechatJoinGroupBO.getRoomBaseName()).orElse(""));
        wechatJoinGroupDO.setCorpId(corpId);
        if (CollectionUtils.isEmpty(wechatJoinGroupBO.getChatIdList())) {
            wechatJoinGroupBO.setChatIdList(new ArrayList<>());
        }

        wechatJoinGroupDO.setChatIdList(wechatJoinGroupBO.getChatIdList().stream().map(String::valueOf).collect(Collectors.joining(",")));
        int effectLines = wechatJoinGroupMapper.insert(wechatJoinGroupDO);
        if (effectLines == 0) {
            throw new RuntimeException("数据写入失败");
        }

        return wechatJoinGroupDO;
    }

    public Integer update(Long id, String chatIdListStr) {
        return update(id, Arrays.stream(chatIdListStr.split(",")).collect(Collectors.toList()));
    }

    @AteyeInvoker(description = "更新活码自动创建状态")
    public Boolean updateCodeStatus(String state, Integer autoCreate) {
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupMapper.selectByState(state);
        if (wechatJoinGroupDO == null) {
            return false;
        }
        wechatJoinGroupDO.setAutoCreateRoom(autoCreate);
        return wechatJoinGroupMapper.updateByPrimaryKey(wechatJoinGroupDO) > 0;
    }

    @Override
    public Integer update(Long id, List<String> chatIdList) {
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wechatJoinGroupDO)) {
            throw new RuntimeException("要更新的群活码信息不存在");
        }

        // 调用
        UpdateJoinGroupRequest request = new UpdateJoinGroupRequest();
        request.setChatIdList(chatIdList);
        request.setConfigId(wechatJoinGroupDO.getConfigId());
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<WechatJoinGroupBO> resultDO = joinGroupService.updateJoinGroup(request);

        if (Objects.isNull(resultDO)) {
            throw new RuntimeException("系统出错");
        }

        if (!resultDO.getSuccess()) {
            throw new RuntimeException(resultDO.getResultMessage());
        }

        WechatJoinGroupBO wechatJoinGroupBO = resultDO.getModel();
        wechatJoinGroupDO = new WechatJoinGroupDO();
        wechatJoinGroupDO.setId(id);
        wechatJoinGroupDO.setConfigId(wechatJoinGroupBO.getConfigId());
        wechatJoinGroupDO.setScene(wechatJoinGroupBO.getScene());
        wechatJoinGroupDO.setRemark(wechatJoinGroupBO.getRemark());
        wechatJoinGroupDO.setState(wechatJoinGroupBO.getState());
        wechatJoinGroupDO.setQrCode(wechatJoinGroupBO.getQrCode());
        wechatJoinGroupDO.setAutoCreateRoom(wechatJoinGroupBO.getAutoCreateRoom());
        wechatJoinGroupDO.setRoomBaseId(wechatJoinGroupBO.getRoomBaseId());
        wechatJoinGroupDO.setRoomBaseName(Optional.ofNullable(wechatJoinGroupBO.getRoomBaseName()).orElse(""));

        if (CollectionUtils.isEmpty(wechatJoinGroupBO.getChatIdList())) {
            wechatJoinGroupBO.setChatIdList(new ArrayList<>());
        }

        wechatJoinGroupDO.setChatIdList(wechatJoinGroupBO.getChatIdList().stream().map(String::valueOf).collect(Collectors.joining(",")));

        return wechatJoinGroupMapper.updateByPrimaryKey(wechatJoinGroupDO);
    }

    @Override
    public Integer modifyChatIdList(Long id, List<String> addChatIdList, List<String> deleteChatIdList) {
        addChatIdList = Optional.ofNullable(addChatIdList).orElse(new ArrayList<>());
        deleteChatIdList = Optional.ofNullable(deleteChatIdList).orElse(new ArrayList<>());
        WechatJoinGroupDO wechatJoinGroupDO = getById(id);
        List<String> chatIdList = Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).collect(Collectors.toList());
        chatIdList.addAll(addChatIdList);
        chatIdList.removeAll(deleteChatIdList);
        chatIdList = chatIdList.stream().distinct().collect(Collectors.toList());
        return update(id, chatIdList);
    }

    @AteyeInvoker(description = "修改群活码绑定群聊", paraDesc = "id&addChatIdList&deleteChatIdList")
    public Integer modifyChatIdList(Long id, String addChatIdList, String deleteChatIdList) {
        List<String> addList = Arrays.stream(addChatIdList.split(",")).collect(Collectors.toList());
        List<String> removeList = Arrays.stream(deleteChatIdList.split(",")).collect(Collectors.toList());
        return modifyChatIdList(id, addList, removeList);
    }



    @Override
    @AteyeInvoker(description = "删除群活码信息", paraDesc = "id")
    public Integer delete(Long id) {
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wechatJoinGroupDO)) {
            throw new RuntimeException("数据不存在");
        }

        wechatJoinGroupDO.setDeleted(1);
        DeleteJoinGroupRequest request = new DeleteJoinGroupRequest();
        request.setConfigId(wechatJoinGroupDO.getConfigId());
        request.setCorpId(wechatJoinGroupDO.getCorpId());
        ResultDO<Void> resultDO = joinGroupService.deleteJoinGroup(request);
        if (Objects.isNull(resultDO)) {
            throw new RuntimeException("系统出错");
        }

        if (!resultDO.getSuccess()) {
            throw new RuntimeException(resultDO.getResultMessage());
        }

        return wechatJoinGroupMapper.updateByPrimaryKey(wechatJoinGroupDO);
    }

    @Override
    public WechatJoinGroupDO getById(Long id) {
        return wechatJoinGroupMapper.selectByPrimaryKey(id);
    }

    @Override
    public WechatJoinGroupDO getByState(String state) {
        return wechatJoinGroupMapper.selectByState(state);
    }

    @Override
    public WechatJoinGroupDO selectByConfigId(String configId) {
        return wechatJoinGroupMapper.selectByConfigId(configId);
    }

    @Override
    public List<WechatJoinGroupDO> getByCondition(WechatJoinGroupQuery query) {
        return wechatJoinGroupMapper.selectByCondition(query);
    }
}
