package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

/**
 * 发送评论请求
 */
@Data
public class SendCommentRequest{
    /**
     * 朋友圈任务id
     */
    private Long id;

    /**
     * 企微成员id
     */
    private String userIds;

    /**
     * 回复评论id。若父级id为0，则代表评论的是此条朋友圈
     */
    private Long commentId;

    /**
     * 回复客户名称
     */
    private String customerName;

    /**
     * 素材id
     */
    private String materialId;

    /**
     * 评论内容
     */
    private String comment;

}
