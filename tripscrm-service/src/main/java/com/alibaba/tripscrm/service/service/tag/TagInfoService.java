package com.alibaba.tripscrm.service.service.tag;

import com.alibaba.tripscrm.service.model.domain.query.TagInfoQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * SCRM标签服务
 * <AUTHOR>
 */
public interface TagInfoService {
    /**
     * 根据id查询标签TagInfo
     */
    TagInfoDTO selectByTagId(String tagId);

    /**
     * 根据id查询标签TagInfo
     */
    List<TagInfoDTO> selectByTagIdList(List<String> tagIdList);

    /**
     * 根据id查询标签TagInfo
     */
    List<TagInfoDTO> selectByIdList(List<Long> idList);

    /**
     * 根据条件查询
     *
     * @param query
     * @return 标签TagInfoQuery对象列表
     */
    List<TagInfoDTO> selectByCondition(TagInfoQuery query);

    /**
     * 新增一个标签TagInfoQuery
     *
     * @param record 标签TagInfoQuery对象实体
     * @param isSync 是否同步企微端
     * @return 影响行数
     */
    Integer insertSelective(TagInfoDTO record, Boolean isSync);

    /**
     * 根据标签TagInfoQueryId更新标签TagInfoQuery信息
     *
     * @param record 标签TagInfoQuery对象
     * @param  condition 查询条件
     * @param  isSync 是否同步企微端
     * @return 是否更新成功
     */
    Integer updateSelective(TagInfoDTO record, TagInfoQuery condition, Boolean isSync);

    /**
     * 根据标签TagInfoQueryId更新标签TagInfoQuery信息
     *
     * @param ids 标签id列表
     * @param isSync 是否同步企微端
     * @return 是否更新成功
     */
    Integer deleteByIdList(List<Long> ids, Boolean isSync);

    /**
     * 分页查询
     *
     * @param query 查询条件
     * @return 查询结果
     */
    PageInfo<TagInfoDTO> pageQuery(TagInfoQuery query);

    /**
     * 分页查询虚拟标签的子标签
     *
     * @param keyword 查询条件
     * @return 查询结果
     */
    PageInfo<TagInfoDTO> searchDivisionCodeTag(String keyword);

    /**
     * 批量新增标签信息
     *
     * @param records 标签信息列表
     * @param isSync 是否同步企微端
     */
    Integer batchInsertSelective(List<TagInfoDTO> records, Boolean isSync);

    /**
     * 主键查询
     *
     * @param id 主键
     * @return 数据
     */
    TagInfoDTO queryByIdWithCache(String id);

    /**
     * 主键查询
     *
     * @param groupId 标签TagInfoQueryId
     * @param source  标签来源
     * @return 数据
     */
    TagInfoDTO queryByGroupIdAndSourceWithCache(Long groupId, String source);
}
