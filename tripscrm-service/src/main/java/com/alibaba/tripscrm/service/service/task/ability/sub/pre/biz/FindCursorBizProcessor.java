package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.INTERFACE;

/**
 * 单条任务数据处理_执行进度快速定位
 *
 * <AUTHOR>
 * @since 2024/4/22 15:01
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class FindCursorBizProcessor implements BizProcessor {
    @Override
    @TaskExecuteLog("单条任务数据处理_快速定位")
    public TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception {
        if (context.getRetry()) {
            return TripSCRMResult.success(null);
        }

        // 数据是否需要跳过处理逻辑
        if (skip(todoTaskVO, context)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.TASK_EXECUTE_SKIP_TARGET);
        }

        return TripSCRMResult.success(null);
    }

    /**
     * 判断当前任务实例(主or子)的targetId数据是否需要跳过
     * 重试情况下，已处理的数据无需再次处理
     */
    private boolean skip(TodoTaskVO todoTaskVO, TaskExecuteContext context) {
        // 接口或者单次触发，不会进行跳过逻辑
        TaskTriggerTypeEnum triggerType = context.getTriggerType();
        if (Lists.newArrayList(INTERFACE, EVENT).contains(triggerType)) {
            return false;
        }

        AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(TaskType.getByCode(context.getTaskInfoDOSnapshot().getType()));
        return taskExecutor.checkSkip(context, todoTaskVO);
    }
}
