package com.alibaba.tripscrm.service.service.strategy.websocket.param.response;

import lombok.Data;

import java.util.List;

/**
 * 聚合聊天的有权限的企微成员
 *
 * <AUTHOR>
 * @date 2023/10/12
 */
@Data
public class ManagerWechatUser {
    /**
     * 企微 userId
     */
    private String userId;

    /**
     * 企业Id
     */
    private String corpId;

    /**
     * 企业名称
     */
    private String corpName;

    /**
     * 名称
     */
    private String name;

    /**
     * 真名
     */
    private String realName;

    /**
     * 性别，1：男，2：女，0：未知
     */
    private Integer gender;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 别名
     */
    private String alias;

    /**
     * 在线状态
     */
    private Integer onlineStatus;

    /**
     * 新手任务完成状态，0：未完成，1：已完成
     */
    private Integer activateStatus;

    /**
     * 部门Id
     */
    private Integer departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 机器人类型：0=未激活，1=平台号，2=扫码号
     */
    private Integer robotType;

    /**
     * 是否置顶
     */
    private Integer topNo;

    /**
     * 未读消息数量
     */
    private Integer unreadCount;

    /**
     * 当前锁定的平台账号
     */
    private String lockUser;

    /**
     * 当前锁定的平台账号名称
     */
    private String lockUserName;

    /**
     * 使用者平台账号列表
     */
    private List<String> managerUserList;
}
