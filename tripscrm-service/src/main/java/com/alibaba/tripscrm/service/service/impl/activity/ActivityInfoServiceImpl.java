package com.alibaba.tripscrm.service.service.impl.activity;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.tddl.ActivityInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.activity.ActivityInfoQuery;
import com.alibaba.tripscrm.service.enums.activity.ActivityStatusEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 活动信息管理
 *
 * <AUTHOR>
 * @date 2023/4/14
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ActivityInfoServiceImpl implements ActivityInfoService {
    private final ActivityInfoMapper activityInfoMapper;
    private final AccountService accountService;

    @Override
    public Long add(ActivityInfoDO infoDO) {
        User user = accountService.getUserInWebThread();
        try {
            // 默认不是通过 SOP 创建
            infoDO.setSopType(NumberUtils.validInteger(infoDO.getSopType()) ? infoDO.getSopType() : -1);
            infoDO.setEnv(EnvUtils.getEnvironment());
            infoDO.setSpaceId(NumberUtils.validLong(infoDO.getSpaceId()) ? infoDO.getSpaceId() : SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
            int ret = activityInfoMapper.insert(infoDO);
            if (ret < 1) {
                PlatformLogUtil.logFail("insert fail", LogListUtil.newArrayList(infoDO));
                return null;
            }

            PlatformLogUtil.logInfo("success", LogListUtil.newArrayList(infoDO.getId(), user));
            return infoDO.getId();
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(infoDO));
            throw e;
        }
    }

    /**
     * 删除活动（绝对谨慎！！！）
     */
    public Integer deleteById(Long activityId) {
        try {
            int ret = activityInfoMapper.deleteByPrimaryKey(activityId);
            if (ret < 1) {
                PlatformLogUtil.logFail("delete fail", LogListUtil.newArrayList(activityId));
            }

            PlatformLogUtil.logInfo("删除活动成功", LogListUtil.newArrayList(activityId));
            return ret;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(activityId));
            throw e;
        }
    }

    @Override
    public Integer update(ActivityInfoDO infoDO) {
        User user = accountService.getUserInWebThread();
        try {
            int ret = activityInfoMapper.updateByPrimaryKey(infoDO);
            if (ret < 1) {
                PlatformLogUtil.logFail("update fail", LogListUtil.newArrayList(infoDO));
            }

            PlatformLogUtil.logInfo("更新活动成功", LogListUtil.newArrayList(infoDO.getId(), user));
            return ret;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(infoDO));
            throw e;
        }
    }

    @Override
    public ActivityInfoDO queryInfoById(Long activityId) {
        try {
            ActivityInfoDO activityInfoDO = activityInfoMapper.selectByPrimaryKey(activityId);
            if (Objects.isNull(activityInfoDO)) {
                PlatformLogUtil.logFail("select fail", LogListUtil.newArrayList(activityId));
            }
            return activityInfoDO;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(activityId));
            return null;
        }
    }

    @Override
    public List<ActivityInfoDO> queryInfoByIdList(List<Long> activityIdList) {
        if (CollectionUtils.isEmpty(activityIdList)) {
            return Lists.newArrayList();
        }
        try {
            ActivityInfoQuery query = new ActivityInfoQuery();
            query.setIdList(activityIdList);
            List<ActivityInfoDO> activityInfoDOList = activityInfoMapper.select(query);
            if (CollectionUtils.isEmpty(activityInfoDOList)) {
                PlatformLogUtil.logFail("queryInfoByIdList fail", LogListUtil.newArrayList(activityIdList));
            }
            return activityInfoDOList;
        } catch (Exception e) {
            PlatformLogUtil.logException("queryInfoByIdList exception", e.getMessage(), e, LogListUtil.newArrayList(activityIdList));
            return null;
        }
    }

    @Override
    public PageInfo<ActivityInfoDO> list(ActivityInfoQuery query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("param check fail");
                return null;
            }
            query.setEnv(EnvUtils.getEnvironment());
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
            List<ActivityInfoDO> list = activityInfoMapper.select(query);
            // 结果处理
            if (Objects.isNull(list)) {
                return null;
            }
            return new PageInfo<>(list);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public boolean checkDuplicateName(Long spaceId, String name, Long id) {
        try {
            if (!StringUtils.hasText(name)) {
                PlatformLogUtil.logFail("param check fail", LogListUtil.newArrayList(spaceId, name, id));
                return false;
            }
            if (Objects.equals(name, "公共活动")) {
                return true;
            }

            ActivityInfoDO taskInfoDO = activityInfoMapper.selectByNameAndSpaceIdAndPrimaryKey(name, spaceId, id);
            return Objects.nonNull(taskInfoDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(spaceId, name, id));
            return false;
        }
    }

    @Override
    public Integer compareAndUpdateStatus(Long id, ActivityStatusEnum oldStatus, ActivityStatusEnum newStatus) {
        return activityInfoMapper.updateStatus(id, oldStatus.getStatus(), newStatus.getStatus());
    }

    @Override
    public List<ActivityInfoDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }
        return activityInfoMapper.selectByIdList(idList);
    }

    @AteyeInvoker(description = "活动业务空间迁移", paraDesc = "oldSpaceId&newSpaceId")
    public int updateSpace(Long oldSpaceId, Long newSpaceId) {
        int res = 0;
        ActivityInfoQuery query = new ActivityInfoQuery();
        query.setSpaceId(oldSpaceId);
        List<ActivityInfoDO> oldDataList = activityInfoMapper.select(query);
        for (ActivityInfoDO data : oldDataList) {
            ActivityInfoDO activityInfoDO = new ActivityInfoDO();
            activityInfoDO.setId(data.getId());
            activityInfoDO.setSpaceId(newSpaceId);
            res += activityInfoMapper.updateByPrimaryKey(activityInfoDO);
        }

        return res;
    }
}
