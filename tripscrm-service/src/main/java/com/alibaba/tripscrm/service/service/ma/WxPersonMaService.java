package com.alibaba.tripscrm.service.service.ma;


import com.alibaba.tripscrm.service.model.vo.wechat.ContactMeVO;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.wechat.WxEnterpriseUserInfoVO;
import com.alibaba.tripscrm.service.model.domain.query.ContactMeQuery;
import com.alibaba.tripscrm.service.model.domain.query.WxEnterpriseUserQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29
 */
public interface WxPersonMaService {


    /**
     * 新增个码
     * @param contactMeVO
     * @return
     */
    PageResultVO<Long> addWxContactMeConfig(ContactMeVO contactMeVO);


    /**
     * 修改个码
     * @param contactMeVO
     * @return
     */
    PageResultVO<Integer> updateWxContactMeConfig(ContactMeVO contactMeVO, User user);

    /**
     * 修改个码分组
     * @param contactMeVO
     * @return
     */
    PageResultVO<Integer> updateWxContactMeGroupConfig(ContactMeVO contactMeVO, User user);


    /**
     * 根据id查询个码
     * @param id
     * @return
     */
    ContactMeVO queryContactMeById(Long id);

    /**
     * 删除个码
     * @param id
     * @param configId
     * @return
     */
    Integer delWxContactMeConfig(Long id, String configId);

    /**
     * 列表个码
     * @param query
     * @return
     */
    PageResultVO<ContactMeVO> listWxContactMeConfig(ContactMeQuery query);


    /**
     * 获取微信用户
     * @param wxEnterpriseUserQuery
     * @return
     */
    List<WxEnterpriseUserInfoVO> listWxEnterpriseUser(WxEnterpriseUserQuery wxEnterpriseUserQuery);

    /**
     * 根据分组id查询个码数量
     * @param bizGroup
     * @return
     */
    Integer countByBizGroup(Long bizGroup);

}
