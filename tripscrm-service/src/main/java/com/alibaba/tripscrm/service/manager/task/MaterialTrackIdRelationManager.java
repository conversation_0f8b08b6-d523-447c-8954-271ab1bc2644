package com.alibaba.tripscrm.service.manager.task;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.lindorm.MaterialTrackIdRelationDAO;
import com.alibaba.tripscrm.dal.model.lindorm.MaterialTrackIdRelationDO;
import com.alibaba.tripscrm.dal.utils.LindormUtil;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.tddl.client.sequence.Sequence;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @DECLARATION: THE AUTHORITY OF CODE BELONGS TO ENTERPRISE TECHNOLOGY GROUP OF FEIZHU
 * @NOTE: 素材关系和跟踪id管理类
 * @AUTHOR: benjamin.zhm
 * @DATE: 2024/1/24
 **/
@Component
public class MaterialTrackIdRelationManager {

    @Resource
    private MaterialTrackIdRelationDAO materialTrackIdRelationDAO;

    @Resource
    private Sequence materialTrackIdSequence;

    @AteyeInvoker(description = "根据trackId查询埋点数据")
    public MaterialTrackIdRelationDO relationDO(String trackId) {
        MaterialTrackIdRelationDO relationDO = materialTrackIdRelationDAO.queryByPrimaryKey(trackId);
        return relationDO;
    }

    /**
     * 传入发送素材的信息获取匹配的跟踪id
     *
     * @param relationDTO 素材关系
     * @return 全局跟踪trackId
     */
    public BaseResult<String> insertMaterialRelationAndGetTrackId(MaterialTrackRelationDTO relationDTO) {
        try {
            PlatformLogUtil.logInfo("MaterialTrackIdRelationManager插入素材跟踪关系入参", relationDTO);
            if (relationDTO == null || StringUtils.isBlank(relationDTO.getSceneType())) {
                PlatformLogUtil.logInfo("MaterialTrackIdRelationManager插入素材跟踪关系入参非法", relationDTO);
                return BaseResult.fail(ErrorCodeEnum.PARAM_INVALID.getCode(), ErrorCodeEnum.PARAM_INVALID.getDesc());
            }
            BaseResult<MaterialTrackIdRelationDO> result = checkParamRelationValidAndGetDo(relationDTO);
            if (result == null || !result.isSuccess() || result.getData() == null) {
                PlatformLogUtil.logInfo("MaterialTrackIdRelationManager插入素材跟踪关系获取DO失败", relationDTO, result);
                return BaseResult.fail(result.getErrorCode(), result.getErrorMsg());
            }
            String trackId = String.valueOf(materialTrackIdSequence.nextValue());
            if (StringUtils.isBlank(trackId)) {
                PlatformLogUtil.logInfo("MaterialTrackIdRelationManager插入素材跟踪关系获取trackId为空", relationDTO);
                return BaseResult.fail(ErrorCodeEnum.TRACK_ID_BLANK.getCode(), ErrorCodeEnum.TRACK_ID_BLANK.getDesc());
            }
            MaterialTrackIdRelationDO relationDO = result.getData();
            relationDO.setScrmTrackId(LindormUtil.addPrefixSalt(trackId));
            String time = DateUtil.format(new Date(System.currentTimeMillis()), DatePattern.NORM_DATETIME_MS_PATTERN);
            relationDO.setGmtCreate(time);
            relationDO.setGmtModified(time);
            int effect = materialTrackIdRelationDAO.upsert(relationDO);
            if (effect == 0) {
                PlatformLogUtil.logFail("MaterialTrackIdRelationManager插入素材跟踪关系落库失败", LogListUtil.newArrayList(relationDO));
                return BaseResult.fail(ErrorCodeEnum.TRACK_ID_RELATION_INSERT_DB_FAIL.getCode(), ErrorCodeEnum.TRACK_ID_RELATION_INSERT_DB_FAIL.getDesc());
            }
            PlatformLogUtil.logInfo("MaterialTrackIdRelationManager插入素材跟踪关系落库成功", relationDO);
            return BaseResult.success(relationDO.getScrmTrackId());
        } catch (Exception e) {
            PlatformLogUtil.logException("MaterialTrackIdRelationManager插入素材跟踪关系落库异常", e.getMessage(), e, LogListUtil.newArrayList(relationDTO));
            return BaseResult.fail(ErrorCodeEnum.TRACK_ID_RELATION_INSERT_DB_EXCEPTION.getCode(), ErrorCodeEnum.TRACK_ID_RELATION_INSERT_DB_EXCEPTION.getDesc());
        }
    }

    @Nullable
    private BaseResult<MaterialTrackIdRelationDO> checkParamRelationValidAndGetDo(MaterialTrackRelationDTO relationDTO) {
        switch (relationDTO.getSceneType()) {
            case MaterialSendSceneTypeConstant.GEMA_HYY:
            case MaterialSendSceneTypeConstant.SILIAO_RW:
            case MaterialSendSceneTypeConstant.SILIAO_RW_FOR_RULE:
            case MaterialSendSceneTypeConstant.AUTO_RESPONSE_SILIAO:
            case MaterialSendSceneTypeConstant.SMART_RESPONSE_SILIAO:
                if (relationDTO.getTaskInsId() == null || relationDTO.getMaterialId() == null
                        || relationDTO.getMsgIndexId() == null || relationDTO.getMsgParagraphId() == null || StringUtils.isBlank(relationDTO.getWxUserId())) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), relationDTO.getTaskInsId(), relationDTO.getAbBucketId(), relationDTO.getMaterialId(),
                        relationDTO.getMsgIndexId(), relationDTO.getSendUnionId(), null, relationDTO.getWxUserId(), null, null);
            case MaterialSendSceneTypeConstant.QUNLIAO_RW:
            case MaterialSendSceneTypeConstant.AUTO_RESPONSE_QUNLIAO:
                if (relationDTO.getTaskInsId() == null || relationDTO.getMaterialId() == null
                        || relationDTO.getMsgIndexId() == null || relationDTO.getMsgParagraphId() == null
                        || StringUtils.isBlank(relationDTO.getSendChatId()) || StringUtils.isBlank(relationDTO.getWxUserId())) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), relationDTO.getTaskInsId(), relationDTO.getAbBucketId(), relationDTO.getMaterialId(),
                        relationDTO.getMsgIndexId(), relationDTO.getSendUnionId(), relationDTO.getSendChatId(), relationDTO.getWxUserId(), null, null);
            case MaterialSendSceneTypeConstant.JUHE_SILIAO:
                if (relationDTO.getMaterialId() == null || relationDTO.getMsgIndexId() == null || relationDTO.getMsgParagraphId() == null || StringUtils.isBlank(relationDTO.getOperatorId())
                        || StringUtils.isBlank(relationDTO.getSendUnionId()) || StringUtils.isBlank(relationDTO.getWxUserId())) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), null, null, relationDTO.getMaterialId(),
                        relationDTO.getMsgIndexId(), relationDTO.getSendUnionId(), null, relationDTO.getWxUserId(), relationDTO.getOperatorId(), null);
            case MaterialSendSceneTypeConstant.QUNMA_HYY:
                if (relationDTO.getTaskInsId() == null || relationDTO.getMaterialId() == null
                        || relationDTO.getMsgIndexId() == null || relationDTO.getMsgParagraphId() == null
                        || StringUtils.isBlank(relationDTO.getSendChatId()) || StringUtils.isBlank(relationDTO.getWxUserId())) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), relationDTO.getTaskInsId(), relationDTO.getAbBucketId(), relationDTO.getMaterialId(),
                        relationDTO.getMsgIndexId(), null, relationDTO.getSendChatId(), relationDTO.getWxUserId(), null, null);
            case MaterialSendSceneTypeConstant.JUHE_QUNLIAO:
                if (relationDTO.getMaterialId() == null || relationDTO.getMsgIndexId() == null || relationDTO.getMsgParagraphId() == null || StringUtils.isBlank(relationDTO.getOperatorId())
                        || StringUtils.isBlank(relationDTO.getSendChatId()) || StringUtils.isBlank(relationDTO.getWxUserId())) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), null, null, relationDTO.getMaterialId(), relationDTO.getMsgIndexId(),
                        null, relationDTO.getSendChatId(), relationDTO.getWxUserId(), relationDTO.getOperatorId(), null);
            case MaterialSendSceneTypeConstant.QUNGG:
                if (relationDTO.getMaterialId() == null || relationDTO.getMsgIndexId() == null || relationDTO.getMsgParagraphId() == null
                        || StringUtils.isBlank(relationDTO.getSendChatId()) || StringUtils.isBlank(relationDTO.getWxUserId())
                        || (StringUtils.isBlank(relationDTO.getOperatorId()) && relationDTO.getTaskInsId() == null)) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), relationDTO.getTaskInsId(), relationDTO.getAbBucketId(), relationDTO.getMaterialId(),
                        relationDTO.getMsgIndexId(), null, relationDTO.getSendChatId(), relationDTO.getWxUserId(), relationDTO.getOperatorId(), null);
            case MaterialSendSceneTypeConstant.PENGYQ:
            case MaterialSendSceneTypeConstant.PENGYQ_PINGL:
            case MaterialSendSceneTypeConstant.SILIAO_GROUP_MSG_RW:
                if (relationDTO.getTaskInsId() == null || relationDTO.getMaterialId() == null
                        || relationDTO.getMsgIndexId() == null || relationDTO.getMsgParagraphId() == null) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), relationDTO.getTaskInsId(), relationDTO.getAbBucketId(), relationDTO.getMaterialId(),
                        relationDTO.getMsgIndexId(), null, null, relationDTO.getWxUserId(), null, null);
            case MaterialSendSceneTypeConstant.GOODS_PROMOTION:
                if (StringUtils.isBlank(relationDTO.getOperatorId()) || StringUtils.isBlank(relationDTO.getWxUserId())
                        || (StringUtils.isBlank(relationDTO.getSendUnionId()) && StringUtils.isBlank(relationDTO.getSendChatId()))) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), relationDTO.getTaskInsId(), relationDTO.getAbBucketId(), relationDTO.getMaterialId(), relationDTO.getMsgIndexId(),
                        relationDTO.getSendUnionId(), relationDTO.getSendChatId(), relationDTO.getWxUserId(), relationDTO.getOperatorId(), null);
            case MaterialSendSceneTypeConstant.ALIPAY_GROUP_MSG:
            case MaterialSendSceneTypeConstant.ALIPAY_DIRECT_MSG:
                if (relationDTO.getTaskInsId() == null || relationDTO.getMaterialId() == null
                        || relationDTO.getMsgIndexId() == null || relationDTO.getMsgParagraphId() == null) {
                    PlatformLogUtil.logInfo("落素材和trackId关系入参非法", relationDTO);
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), relationDTO.getTaskInsId(), relationDTO.getAbBucketId(), relationDTO.getMaterialId(),
                        relationDTO.getMsgIndexId(), null, relationDTO.getSendChatId(), null, null, null);
            case MaterialSendSceneTypeConstant.MINI_PROGRAM_SUBSCRIBE_MSG:
                if (relationDTO.getTaskInsId() == null || relationDTO.getMaterialId() == null
                        ||  relationDTO.getMsgParagraphId() == null) {
                    PlatformLogUtil.logFail("落素材和trackId关系入参非法", LogListUtil.newArrayList(relationDTO));
                    return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
                }
                return getMaterialTrackIdRelationDO(relationDTO.getMsgParagraphId(), relationDTO.getSceneType(), relationDTO.getTaskInsId(), relationDTO.getAbBucketId(), relationDTO.getMaterialId(),
                        relationDTO.getMsgIndexId(), null, relationDTO.getSendChatId(), null, null, relationDTO.getSendUserId());
            default:
                PlatformLogUtil.logInfo("落素材和trackId关系入参场景类型非法", relationDTO);
                return BaseResult.fail(ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getCode(), ErrorCodeEnum.MATERIAL_TRACK_ID_RELATION_PARAM_INVALID.getDesc());
        }
    }

    @NotNull
    private BaseResult<MaterialTrackIdRelationDO> getMaterialTrackIdRelationDO(Integer msgParagraphId, String sceneType, Long taskInsId, String abBucketId,
                                                                               Long materialId, Integer msgIndexId, String sendUnionId,
                                                                               String sendChatId, String wxUserId, String operatorId, String sendUserId) {
        MaterialTrackIdRelationDO relationDO = new MaterialTrackIdRelationDO();
        relationDO.setSceneType(sceneType);
        relationDO.setTaskInsId(taskInsId);
        relationDO.setAbBucketId(abBucketId);
        relationDO.setMaterialId(materialId);
        relationDO.setMsgParagraphId(msgParagraphId);
        relationDO.setMsgIndexId(msgIndexId);
        relationDO.setSendUnionId(sendUnionId);
        relationDO.setSendChatId(sendChatId);
        relationDO.setWxUserId(wxUserId);
        relationDO.setOperatorId(operatorId);
        relationDO.setSendUserId(sendUserId);
        return BaseResult.success(relationDO);
    }

    @AteyeInvoker(description = "测试插入素材跟踪关系信息", paraDesc = "sceneType&taskInsId&abBucketId&materialId&sendChatId&msgIndexId&wxUserId&operatorId&sendUnionId")
    public BaseResult<String> testInsertMaterialtrackIdRelation(String sceneType, Long taskInsId, String abBucketId, Long materialId, String sendChatId,
                                                                Integer msgIndexId, String wxUserId, String operatorId, String sendUnionId){
        MaterialTrackRelationDTO dto = new MaterialTrackRelationDTO();
        dto.setAbBucketId(abBucketId);
        dto.setMaterialId(materialId);
        dto.setMsgIndexId(msgIndexId);
        dto.setOperatorId(operatorId);
        dto.setSceneType(sceneType);
        dto.setSendUnionId(sendUnionId);
        dto.setSendChatId(sendChatId);
        dto.setTaskInsId(taskInsId);
        dto.setWxUserId(wxUserId);
        dto.setMsgParagraphId(0);
        return insertMaterialRelationAndGetTrackId(dto);
    }

    @AteyeInvoker(description = "删除素材跟踪关系信息", paraDesc = "scrmTrackId")
    public BaseResult<Boolean> testDeleteMaterialtrackIdRelation(String scrmTrackId){
        return BaseResult.success(materialTrackIdRelationDAO.deleteByPrimaryKey(scrmTrackId) != 0);
    }

}
