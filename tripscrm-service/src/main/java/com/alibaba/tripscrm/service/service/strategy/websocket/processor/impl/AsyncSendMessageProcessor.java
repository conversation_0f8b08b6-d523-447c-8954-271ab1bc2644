package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbLockManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.dto.MessageRelationInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.SendMessageRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.SendMessageResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingRobotUtils;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WechatGroupSendMsgRequest;
import com.alibaba.tripzoo.proxy.request.WxCustomerSendMsgRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.*;

import static com.alibaba.tripscrm.service.constant.TairConstant.SEND_MESSAGE_RECORD;
import static com.alibaba.tripscrm.service.constant.TairConstant.SEND_MESSAGE_RECORD_LOCK;

/**
 * 异步发消息 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j
public class AsyncSendMessageProcessor implements WsEventProcessor {
    /**
     * 异步操作传输数据的过期时间 30分钟
     */
    public static final Integer ASYNC_OPERATE_EXPIRE_SECOND = 30 * 60;
    public static final String ASYNC_CACHE_KEY = "sendMessage|";
    /**
     * 当前机器人自己发的消息，要在缓存中加个10秒过期记录，10秒内若是收到当前机器人发的消息，则消息不入库
     */
    public static final Integer ASYNC_FOR_SAVE_MSG_EXPIRE_SECOND = 10;
    public static final String ASYNC_FOR_SAVE_MSG_CACHE_KEY = "sendMessageForSaveMsg|";

    @Resource
    private GroupService groupService;
    @Resource
    private CustomerService customerService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private LdbLockManager ldbLockManager;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private MessageUtils messageUtils;


    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.SEND_MESSAGE;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        SendMessageRequest request = wsEvent.getData().toJavaObject(SendMessageRequest.class);
        // 参数校验
        if (wsEvent.getUserId() == null || request.getChatId() == null || request.getChatType() == null) {
            throw new TripscrmException("缺少必要的参数 userId or chatId or chatType");
        }
        if (request.getMsgContent().length() > 1012) {
            throw new TripscrmException("消息内容过长，请控制在1000字以内");
        }
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        if (!wechatUserService.checkHasLock(wsEvent.getUserId(), wsContextInfo.getAccount().getUserId(), wsContextInfo.getSpaceId())) {
            throw new TripscrmException("未锁定当前企微账号，无法执行该操作");
        }
        // 消息发送
        ResultDO<String> result = sendMessage(ChatTypeEnum.parse(request.getChatType()), wsEvent.getUserId(), request.getChatId(), request.build(), session);
        if (result == null || !result.getSuccess()) {
            throw new TripscrmException("发送接口报错");
        }
        // 后置处理
        postProcess(result, wsEvent);
    }

    /**
     * 消息发送后置处理
     * @param resultDO 结果集
     */
    public void postProcess(ResultDO<String> resultDO, WsEvent wsEvent) {
        // 保存消息上下文，以便回调时取到消息数据，key为requestId
        wsEvent.setTimestamp(System.currentTimeMillis());
        ldbTairManager.put(ASYNC_CACHE_KEY + resultDO.getModel(), JSONObject.toJSONString(wsEvent), ASYNC_OPERATE_EXPIRE_SECOND);
        // 当前机器人自己发的消息，要在缓存中加个10秒过期记录，10秒内若是收到当前机器人发的消息，则消息不入库
        ldbTairManager.put(ASYNC_FOR_SAVE_MSG_CACHE_KEY + wsEvent.getUserId(), wsEvent.getUserId(), ASYNC_FOR_SAVE_MSG_EXPIRE_SECOND);
        // 记录还未发送成功的消息sendMessageRecord
        this.addSendMessageRecord(resultDO.getModel());
    }

    /**
     * 发送消息
     * @param chatType 聊天类型
     * @param userId 发送用户ID
     * @param chatId 群聊ID
     * @param messageBody 消息体
     * @param session 发送session
     * @return 发送结果
     */
    public ResultDO<String> sendMessage(ChatTypeEnum chatType, String userId, String chatId, FusionChatMessageBody messageBody, WebSocketSession session) {
        ResultDO<String> resultDO = null;
        String operatorId = Optional.ofNullable(webSocketFactory.getWsContextInfo(session).getAccount()).orElse(new User()).getUserId();
        MessageRelationInfoDTO messageRelationInfoDTO = new MessageRelationInfoDTO(chatType, chatId, userId, operatorId);
        switch (chatType) {
            case GROUP:
                resultDO = sendGroupMessage(userId, chatId, messageBody, session, messageRelationInfoDTO);
                break;
            case SINGLE_FOR_CUSTOMER:
                resultDO = sendCustomerMessage(userId, chatId, messageBody, session, messageRelationInfoDTO);
                break;
            case SINGLE_FOR_USER:
                // rpc链路 未实现 todo-csn
                break;
            default:
                break;
        }

        if (Objects.nonNull(resultDO) && resultDO.getSuccess() && StringUtils.hasText(resultDO.getModel())) {
            // 用来记录是通过聚合聊天发送的消息，防止处理消息接收回调时前端重复渲染
            ldbTairManager.incr(TairConstant.FUSION_CHAT_SEND_MESSAGE_UNIQUE_KEY_PREFIX + resultDO.getModel(), 1, 0, 100);
        }
        return resultDO;
    }

    /**
     * 聚合聊天给客户发消息
     * @param userId 成员ID
     * @param externalUserId 客户ID
     * @param messageBody 发送消息体
     * @param session 发送session
     * @param messageRelationInfoDTO 消息关系
     * @return 发送结果
     */
    private ResultDO<String> sendCustomerMessage(String userId, String externalUserId, FusionChatMessageBody messageBody
            , WebSocketSession session, MessageRelationInfoDTO messageRelationInfoDTO) {
        // 请求构建
        WxCustomerSendMsgRequest wxCustomerSendMsgRequest = new WxCustomerSendMsgRequest();
        wxCustomerSendMsgRequest.setUserId(userId);
        wxCustomerSendMsgRequest.setExternalUserId(externalUserId);
        wxCustomerSendMsgRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        MessageBO messageBO = messageUtils.fusionChatMessage2SendMessage(messageBody, messageRelationInfoDTO);
        wxCustomerSendMsgRequest.setMessageList(Lists.newArrayList(messageBO));
        if (!StringUtils.hasLength(wxCustomerSendMsgRequest.getRequestId())) {
            wxCustomerSendMsgRequest.setRequestId(UUID.randomUUID().toString());
        }        // 发起请求
        ResultDO<String> resultDO = customerService.asyncSendMessage(wxCustomerSendMsgRequest);
        // 日志留存
        if (resultDO.getSuccess()) {
            PlatformLogUtil.logInfo("异步发送消息调用成功", LogListUtil.newArrayList(wxCustomerSendMsgRequest, resultDO, session.getId(), userId));
        } else {
            PlatformLogUtil.logFail("异步发送消息调用失败", LogListUtil.newArrayList(wxCustomerSendMsgRequest, resultDO, session.getId(), userId));
            throw new TripscrmException("消息发送失败：" + resultDO.getResultMessage());
        }
        return resultDO;
    }

    /**
     * 聚合聊天给群聊发消息
     * @param userId 发送人ID
     * @param chatId 群聊ID
     * @param messageBody 发送消息体
     * @param session 发送session
     * @return 发送结果
     */
    private ResultDO<String> sendGroupMessage(String userId, String chatId, FusionChatMessageBody messageBody
            , WebSocketSession session, MessageRelationInfoDTO messageRelationInfoDTO) {
        // 请求构建
        WechatGroupSendMsgRequest wechatGroupSendMsgRequest = new WechatGroupSendMsgRequest();
        wechatGroupSendMsgRequest.setUserId(userId);
        wechatGroupSendMsgRequest.setChatId(chatId);
        wechatGroupSendMsgRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        MessageBO messageBO = messageUtils.fusionChatMessage2SendMessage(messageBody, messageRelationInfoDTO);
        wechatGroupSendMsgRequest.setMessageList(Lists.newArrayList(messageBO));
        if (!StringUtils.hasLength(wechatGroupSendMsgRequest.getRequestId())) {
            wechatGroupSendMsgRequest.setRequestId(UUID.randomUUID().toString());
        }
        // 发起请求
        ResultDO<String> resultDO = groupService.asyncSendMessage(wechatGroupSendMsgRequest);
        // 结果处理
        if (resultDO.getSuccess()) {
            PlatformLogUtil.logInfo("异步发送消息调用成功",  LogListUtil.newArrayList(wechatGroupSendMsgRequest, resultDO, session.getId(), userId));
        } else {
            PlatformLogUtil.logFail("异步发送消息调用失败",  LogListUtil.newArrayList(wechatGroupSendMsgRequest, resultDO, session.getId(), userId));
            throw new TripscrmException("消息发送失败：" + resultDO.getResultMessage());
        }
        return resultDO;
    }

    /**
     * 成功发送消息后置动作
     */
    public WsEvent afterCreate(String requestId) {
        // 获取消息上下文
        String eventJson = (String) ldbTairManager.get(ASYNC_CACHE_KEY + requestId);
        if (eventJson != null) {
            WsEvent wsEvent = JSONObject.parseObject(eventJson, WsEvent.class);
            // 判断是否超过1分钟，若超过，则推送告警
            long costTime = System.currentTimeMillis() - wsEvent.getTimestamp();
            if (costTime > 1000 * 60) {
                DingRobotUtils.sendMessageForFusionChat(wsEvent.getUserId(), wsEvent.getType(), "发送消息耗时" + costTime / 1000 + "秒，wsEvent:" + JSONObject.toJSONString(wsEvent));
            }
            return wsEvent;
        }
        return null;
    }

    public void pushMessageByDistributed(WsEvent wsEvent, String chatId, ChatTypeEnum chatType, String messageId, String senderId, User lockAccount, Long timestamp, FusionChatUserBody fusionChatUserBody, FusionChatMessageBody fusionChatMessageBody) {
        SendMessageResponse sendMessageResponse = new SendMessageResponse();
        sendMessageResponse.setChatId(chatId);
        sendMessageResponse.setChatType(chatType.getValue());
        sendMessageResponse.setSenderId(senderId);
        sendMessageResponse.setSenderName(fusionChatUserBody.getUserName());
        if (Objects.nonNull(lockAccount)) {
            sendMessageResponse.setSenderName(fusionChatUserBody.getUserName() + "【" + lockAccount.getUserName() + "】");
        }
        sendMessageResponse.setSenderAvatar(fusionChatUserBody.getUserAvatar());
        sendMessageResponse.setMessageId(messageId);
        sendMessageResponse.setTimestamp(timestamp);
        sendMessageResponse.setMsgType(fusionChatMessageBody.getMsgType());
        sendMessageResponse.setMsgContent(fusionChatMessageBody.getMsgContent());
        sendMessageResponse.setText(MessageUtils.fusionChatMessage2Text(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
        wsEvent.setData((JSONObject) JSONObject.toJSON(sendMessageResponse));
        webSocketFactory.pushMessageByDistributed(wsEvent);
    }

    /**
     * 记录还未发送成功的消息
     *
     * @param msgRequestId msgRequestId
     */
    private void addSendMessageRecord(String msgRequestId) {
        try {
            ldbLockManager.lockWithRunnable(SEND_MESSAGE_RECORD_LOCK, 3, true, () -> {
                Map<String, Long> sendMessageRecordMap = this.getSendMessageRecordMap();
                sendMessageRecordMap.put(msgRequestId, System.currentTimeMillis());
                ldbTairManager.put(SEND_MESSAGE_RECORD, JSONObject.toJSONString(sendMessageRecordMap));
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("记录还未发送成功的消息失败", e.getMessage(), e, LogListUtil.newArrayList(msgRequestId));
        }
    }

    /**
     * 删除记录发送成功的消息
     *
     * @param msgRequestId msgRequestId
     */
    public void deleteSendMessageRecord(String msgRequestId) {
        try {
            ldbLockManager.lockWithRunnable(SEND_MESSAGE_RECORD_LOCK, 3, true, () -> {
                Map<String, Long> sendMessageRecordMap = this.getSendMessageRecordMap();
                sendMessageRecordMap.remove(msgRequestId);
                ldbTairManager.put(SEND_MESSAGE_RECORD, JSONObject.toJSONString(sendMessageRecordMap));
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("删除记录发送成功的消息失败", e.getMessage(), e, LogListUtil.newArrayList(msgRequestId));
        }
    }

    /**
     * 获取还未发送成功的消息列表
     *
     * @return return
     */
    public Map<String, Long> getSendMessageRecordMap() {
        Map<String, Long> sendMessageRecordMap = new HashMap<>();
        String sendMessageRecord = (String) ldbTairManager.get(SEND_MESSAGE_RECORD);
        if (sendMessageRecord != null) {
            sendMessageRecordMap = JSONObject.parseObject(sendMessageRecord, new TypeReference<Map<String, Long>>() {
            });
        }
        return sendMessageRecordMap;
    }
}
