package com.alibaba.tripscrm.service.service.material;

import com.alibaba.tripscrm.dal.model.domain.data.MaterialTemplateInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialTemplateInfoQuery;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/5/22
 */
public interface MaterialTemplateService {

    /**
     * 保存/更新
     * @param templateInfoDO
     * @return
     */
    Long upsert(MaterialTemplateInfoDO templateInfoDO);

    List<MaterialTemplateInfoDO> list(MaterialTemplateInfoQuery query);

    Integer delete(Long id);

}
