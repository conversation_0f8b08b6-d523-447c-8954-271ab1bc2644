package com.alibaba.tripscrm.service.service.strategy.message.factory;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.message.MessageInfoDTO;
import com.alibaba.tripscrm.service.service.strategy.message.AbstractMessageConverter;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 素材内容转换工具
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MessageConverterFactory<T extends MessageInfoDTO> {
    private final List<AbstractMessageConverter<T>> materialConverterList;

    private final Map<MessageTypeEnum, AbstractMessageConverter<T>> materialConverterMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        for (AbstractMessageConverter<T> materialConverter : materialConverterList) {
            materialConverterMap.put(materialConverter.getMsgType(), materialConverter);
        }
    }

    /**
     * 注册
     *
     * @param messageType       messageType
     * @param materialConverter materialConverter
     */
    public void registry(MessageTypeEnum messageType, AbstractMessageConverter<T> materialConverter) {
        materialConverterMap.put(messageType, materialConverter);
    }

    public AbstractMessageConverter<T> getMaterialConverter(MessageTypeEnum messageType) {
        return materialConverterMap.get(messageType);
    }

    /**
     * 转化为消息BO对象
     * @param messageResourceInfo 素材json信息（原始数据）
     * @param relationDTO 素材-trackId关系（数据统计使用）
     * @param context 素材上下文
     * @param taskType 任务类型 （某些任务场景需要定制化判断）
     * @param messageType 消息类型（不同消息不同处理）
     * @param msgIndex 消息下标
     * @return 消息BO对象
     */
    public List<MessageBO> convertMessageBO(JSONObject messageResourceInfo, MaterialTrackRelationDTO relationDTO,
                                            MaterialContentConvertContext context, TaskType taskType,
                                            MessageTypeEnum messageType, Integer msgIndex) {
        return getMaterialConverter(messageType).convertMessageBO(messageResourceInfo, relationDTO, context, taskType, messageType, msgIndex);
    }

    /**
     * 转化为微信消息BO对象
     * @param messageResourceInfo 素材json信息（原始数据）
     * @param relationDTO 素材-trackId关系（数据统计使用）
     * @param context 素材上下文
     * @param taskType 任务类型 （某些任务场景需要定制化判断）
     * @param messageType 消息类型（不同消息不同处理）
     * @param msgIndex 消息下标
     * @return 消息BO对象
     */
    public List<WxMessageBO> convertWxMessageBO(JSONObject messageResourceInfo, MaterialTrackRelationDTO relationDTO,
                                                MaterialContentConvertContext context, TaskType taskType,
                                                MessageTypeEnum messageType, Integer msgIndex) {
        return getMaterialConverter(messageType).convertWxMessageBO(messageResourceInfo, relationDTO, context, taskType, messageType, msgIndex);
    }
}
