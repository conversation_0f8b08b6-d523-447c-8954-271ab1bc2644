package com.alibaba.tripscrm.service.service.impl.task;

import com.alibaba.tripscrm.dal.mapper.tddl.ScrmTaskEffectDataMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmTaskEffectDataDO;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmTaskEffectDataParam;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.convert.ScrmTaskEffectDataConverter;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.model.domain.query.ScrmTaskEffectDataQuery;
import com.alibaba.tripscrm.service.model.dto.task.ScrmTaskEffectDataDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.effectdata.ScrmTaskEffectDataService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.FileUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description：任务详细效果数据
 * @Author：wangrui
 * @create：2025/6/24 16:50
 * @Filename：ScrmTaskEffectDataService
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ScrmTaskEffectDataServiceImpl implements ScrmTaskEffectDataService {
    private final ScrmTaskEffectDataMapper scrmTaskEffectDataMapper;
    private final ScrmTaskEffectDataConverter scrmTaskEffectDataConverter;
    private final FileUtils fileUtils;


    @Override
    public PageInfoDTO<ScrmTaskEffectDataDTO> pageQuery(ScrmTaskEffectDataQuery query) {
        ScrmTaskEffectDataParam param = buildParam(query);
        long count = scrmTaskEffectDataMapper.countByParam(param);
        List<ScrmTaskEffectDataDO> dataDOList = scrmTaskEffectDataMapper.selectByParam(param);
        if (count <= 0 || CollectionUtils.isEmpty(dataDOList)) {
            return new PageInfoDTO<ScrmTaskEffectDataDTO>();
        }
        PageInfoDTO<ScrmTaskEffectDataDTO> pageInfo = PageUtils.getPageInfoDTO( dataDOList, scrmTaskEffectDataConverter::convert2DTO);
        pageInfo.setPageNum(query.getPageNum());
        pageInfo.setPageSize(query.getPageSize());
        pageInfo.setTotal(count);
        return pageInfo;
    }

    /**
     * 查询任务效果数据
     *
     * @param query
     * @return
     */
    @Override
    public List<ScrmTaskEffectDataDTO> list(ScrmTaskEffectDataQuery query) {
        query.setPageNum(null);
        query.setPageSize(null);
        ScrmTaskEffectDataParam param = buildParam(query);

        List<ScrmTaskEffectDataDO> dataDOList = scrmTaskEffectDataMapper.selectByParam(param);
        if (CollectionUtils.isEmpty(dataDOList)) {
            return new ArrayList<>();
        }
        return dataDOList.stream().map(scrmTaskEffectDataConverter::convert2DTO).collect(Collectors.toList());
    }


    public ScrmTaskEffectDataParam buildParam(ScrmTaskEffectDataQuery query) {

        if (Objects.isNull(query) || !NumberUtils.biggerThanZero(query.getSpaceId())){
            throw new TripscrmException(ErrorCodeEnum.PARAM_INVALID);
        }
        ScrmTaskEffectDataParam param = new ScrmTaskEffectDataParam();
        ScrmTaskEffectDataParam.Criteria criteria = param.or();
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());
        criteria.andSpaceIdEqualTo(query.getSpaceId());
        if(Objects.nonNull(query.getLikeName())){
            criteria.andTaskNameLike("%" + query.getLikeName() + "%");
        }

        if(!CollectionUtils.isEmpty(query.getTaskTypeList())){
            criteria.andTaskTypeIn(query.getTaskTypeList());
        }
        if(Objects.nonNull(query.getTaskMessageType())){
            criteria.andTaskMessageTypeEqualTo(query.getTaskMessageType());
        }
        if(Objects.nonNull(query.getStartTime()) && Objects.nonNull(query.getEndTime())){
            criteria.andExecuteDateBetween(query.getStartTime(), query.getEndTime() );
        }
        if(Objects.nonNull(query.getPageNum()) && Objects.nonNull(query.getPageSize())){
            param.setPage(true);
            param.setPageSize(query.getPageSize());
            param.setPageStart(query.getPageNum());
        }
        param.appendOrderByClause( ScrmTaskEffectDataParam.OrderCondition.EXECUTEDATE, ScrmTaskEffectDataParam.SortType.DESC);
        param.appendOrderByClause( ScrmTaskEffectDataParam.OrderCondition.TASKID, ScrmTaskEffectDataParam.SortType.DESC);

        return param;
    }
}
