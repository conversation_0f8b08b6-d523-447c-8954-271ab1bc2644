package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.service.fusionchat.FusionChatService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetWechatUserLockListResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.ManagerWechatUser;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.List;

/**
 * 获取当前平台账号锁定的企微成员列表 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class GetWechatUserLockListProcessor implements WsEventProcessor {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private FusionChatService fusionChatService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.GET_WECHAT_USER_LOCK_LIST;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        // 没有接收
    }

    public void pushMessageBySession(WebSocketSession session, String accountId, Long spaceId) {
        List<ManagerWechatUser> lockUsers = fusionChatService.listLockUsers(accountId, spaceId);
        // 仅负责推送response
        GetWechatUserLockListResponse response = new GetWechatUserLockListResponse();
        response.setWechatUsers(lockUsers);
        // 推送到websocket
        WsEvent wsEvent = new WsEvent();
        wsEvent.setType(WsEventTypeEnum.GET_WECHAT_USER_LOCK_LIST.getValue());
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        webSocketFactory.pushMessageBySession(session, wsEvent, true);
    }
}
