package com.alibaba.tripscrm.service.service.impl.tag;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.TagRelationMapper;
import com.alibaba.tripscrm.dal.model.domain.data.TagRelationDO;
import com.alibaba.tripscrm.dal.model.domain.query.tag.TagRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationCoverRequest;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.tag.ItemTagRelationService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.mtop.api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 打标服务实现类
 * 循环依赖了，RequiredArgsConstructor 加个 @Lazy
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ItemTagRelationOldServiceImpl implements ItemTagRelationService {
    private final TagRelationMapper tagRelationMapper;
    private final TagInfoService tagInfoService;

    @Override
    public List<ItemTagRelationDTO> selectByCondition(ItemTagRelationQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getItemType()) || StringUtil.isEmpty(query.getItemId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TagRelationQuery tagRelationQuery = new TagRelationQuery();
        tagRelationQuery.setItemType(query.getItemType());
        tagRelationQuery.setItemId(query.getItemId());
        tagRelationQuery.setTagId(query.getTagId());
        tagRelationQuery.setSubCodeList(query.getSubCodeList());
        List<TagRelationDO> selectResult = tagRelationMapper.select(tagRelationQuery);
        return Optional.ofNullable(selectResult).orElse(new ArrayList<>()).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<TagInfoDTO> selectTagInfoByCondition(ItemTagRelationQuery query) {
        List<TagInfoDTO> tagInfoList = new ArrayList<>();
        List<ItemTagRelationDTO> itemTagRelationList = selectByCondition(query);
        if (CollectionUtils.isEmpty(itemTagRelationList)) {
            return tagInfoList;
        }

        for (ItemTagRelationDTO tagRelation : itemTagRelationList) {
            TagInfoDTO tagInfoDTO = tagInfoService.selectByTagId(String.valueOf(tagRelation.getTag()));
            if (Objects.isNull(tagInfoDTO)) {
                PlatformLogUtil.logFail("invalid data", LogListUtil.newArrayList(tagRelation));
                continue;
            }
            tagInfoList.add(tagInfoDTO);
        }
        return tagInfoList;
    }

    @Override
    public Integer upsertSelective(ItemTagRelationDTO record) {
        checkValid(record);
        return tagRelationMapper.insert(convert(record));
    }

    @Override
    public Integer updateSelective(ItemTagRelationDTO record, ItemTagRelationQuery condition) {
        if (Objects.isNull(condition) || Objects.isNull(condition.getItemType()) || StringUtil.isEmpty(condition.getItemId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (Objects.isNull(record) || Objects.isNull(record.getDeleted())) {
            return 0;
        }

        return tagRelationMapper.deleteByItem(condition.getItemType(), condition.getItemId());
    }

    @AteyeInvoker(description = "根据id删除", paraDesc = "id")
    public Integer deleteById(Long id) {
        return tagRelationMapper.deleteByPrimaryKey(id);
    }

    @Override
    public Integer batchUpsertSelective(List<ItemTagRelationDTO> list) {
        int result = 0;
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        for (ItemTagRelationDTO record : list) {
            if (upsertSelective(record) < 1) {
                PlatformLogUtil.logFail("写入物料与标签关系旧数据失败", LogListUtil.newArrayList(record));
            } else {
                result += 1;
            }
        }

        return result;
    }

    @Override
    public PageInfo<ItemTagRelationDTO> pageQuery(ItemTagRelationQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        TagRelationQuery tagRelationQuery = new TagRelationQuery();
        tagRelationQuery.setItemType(query.getItemType());
        tagRelationQuery.setItemId(query.getItemId());
        tagRelationQuery.setTagId(query.getTagId());
        tagRelationQuery.setSubCodeList(query.getSubCodeList());
        List<TagRelationDO> selectResult = tagRelationMapper.select(tagRelationQuery);
        PageInfo<TagRelationDO> pageInfo = new PageInfo<>(selectResult);
        return PageUtils.getPageInfo(pageInfo, this::convert);
    }

    @Override
    public void cover(ItemTagRelationCoverRequest request) {
        if (Objects.isNull(request) || !NumberUtils.validInteger(request.getItemType()) || StringUtil.isEmpty(request.getItemId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        // 查询数据库中关联关系
        TagRelationQuery query = new TagRelationQuery();
        query.setItemType(request.getItemType());
        query.setItemId(request.getItemId());
        List<TagRelationDO> dbRecords = tagRelationMapper.select(query);

        // 比对
        List<String> toDeleteTagIds = Lists.newArrayList();
        List<String> toAddTagIds = CollectionUtils.isNotEmpty(request.getTagIdList()) ? request.getTagIdList() : Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dbRecords)) {
            // 待删除
            toDeleteTagIds = dbRecords.stream()
                    .map(this::getTagId)
                    .filter(e -> !request.getTagIdList().contains(e))
                    .collect(Collectors.toList());
            // 待新增
            List<String> dbTagIds = dbRecords.stream().map(this::getTagId).collect(Collectors.toList());
            toAddTagIds.removeAll(dbTagIds);
        } else {
            toAddTagIds = request.getTagIdList();
        }

        if (CollectionUtils.isNotEmpty(toAddTagIds)) {
            List<ItemTagRelationDTO> toAddRecords = buildRecords(request);
            batchUpsertSelective(toAddRecords);
        }
        if (CollectionUtils.isNotEmpty(toDeleteTagIds)) {
            for (String deleteTagId : toDeleteTagIds) {
                ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                itemTagRelationDTO.setDeleted((byte) 1);
                ItemTagRelationQuery condition = new ItemTagRelationQuery();
                condition.setItemType(request.getItemType());
                condition.setItemId(request.getItemId());
                condition.setTag(String.valueOf(deleteTagId));
                updateSelective(itemTagRelationDTO, condition);
            }
        }
    }

    @Override
    public void addBasicTag(String externalUserId, String corpId, String uid) {
    }

    private String getTagId(TagRelationDO tagRelationDO) {
        return Objects.isNull(tagRelationDO.getSubCode()) ? String.valueOf(tagRelationDO.getTagId()) : (tagRelationDO.getTagId() + "_" + tagRelationDO.getSubCode());
    }

    /**
     * 批量构造record
     *
     * @return record列表
     */
    private List<ItemTagRelationDTO> buildRecords(ItemTagRelationCoverRequest request) {
        List<ItemTagRelationDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(request.getTagIdList())) {
            return result;
        }

        for (String tagId : request.getTagIdList()) {
            ItemTagRelationDTO record = new ItemTagRelationDTO();
            record.setItemType(request.getItemType());
            record.setItemId(request.getItemId());
            if (StringUtils.isNumeric(tagId)) {
                record.setTagId(Long.parseLong(tagId));
            } else {
                String[] splits = tagId.split("_");
                record.setTagId(Long.parseLong(splits[0]));
                record.setSubCode(splits[1]);
            }
            record.setCreatorId(request.getCreatorId());
            record.setCreatorName(request.getCreatorName());
            result.add(record);
        }

        return result;
    }


    private ItemTagRelationDTO convert(TagRelationDO d) {
        if (Objects.isNull(d)) {
            return null;
        }

        ItemTagRelationDTO dto = new ItemTagRelationDTO();
        BeanUtils.copyProperties(d, dto);
        return dto;
    }

    private TagRelationDO convert(ItemTagRelationDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        TagRelationDO d = new TagRelationDO();
        BeanUtils.copyProperties(dto, d);
        return d;
    }

    private void checkValid(ItemTagRelationDTO record) {
        if (Objects.isNull(record) || !org.springframework.util.StringUtils.hasText(record.getItemId())
                || !NumberUtils.validInteger(record.getItemType())
                || !NumberUtils.validLong(record.getTagId())
        ) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
    }
}
