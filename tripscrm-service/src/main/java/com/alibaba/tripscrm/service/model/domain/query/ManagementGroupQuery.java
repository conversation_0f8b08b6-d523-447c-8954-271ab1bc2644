package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/3/5 11:28
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ManagementGroupQuery extends BasePageRequest {

    /**
     * 分页
     */
    private boolean page;

    /**
     * 空间Id
     */
    private Long spaceId;

    /**
     * 管理组名称
     */
    private String name;

    /**
     * 管理组类型
     */
    private Byte type;

}
