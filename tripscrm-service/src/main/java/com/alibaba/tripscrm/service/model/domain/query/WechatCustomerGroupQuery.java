package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微信群内客户数量统计
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatCustomerGroupQuery extends BasePageRequest {
    /**
     * 客户Id
     */
    private String externalUserId;

    /**
     * 群主Id
     */
    private String ownerUserId;

    /**
     * 平台类型，微信：1，支付宝：4
     */
    private Integer platformType = 1;
}
