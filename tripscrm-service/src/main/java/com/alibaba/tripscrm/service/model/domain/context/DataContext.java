package com.alibaba.tripscrm.service.model.domain.context;

import com.alibaba.tripscrm.dal.model.domain.data.CustomerManageGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/25 10:07
 **/
@Getter
public class DataContext {

    /**
     * 组织ID
     */
    @Setter
    private String corpId;

    /**
     * 企微群聊信息
     */
    private List<WechatGroupVO> wechatGroupList;

    private Map<String, WechatGroupVO> wechatGroupMap;

    /**
     * 企微成员信息
     */
    private List<WechatUserDTO> wechatUserList;

    private Map<String, WechatUserDTO> wechatUserMap;

    /**
     * 成员针对客户的管理组
     */
    private List<CustomerManageGroupDO> customerManageGroupList;

    private Map<Long, CustomerManageGroupDO> customerManageGroupMap;

    /**
     * 企微客户信息
     */
    private List<WechatCustomerVO> wechatCustomerList;

    private Map<String, WechatCustomerVO> wechatCustomerMap;

    /**
     * 客户关系
     */
    private List<CustomerRelationDO> customerRelationList;

    private Map<String, CustomerRelationDO> customerRelationMap;

    /**
     * 用户关注的群聊ID列表
     */
    @Setter
    private List<String> workChatIdList;



    public void setWechatGroupList(List<WechatGroupVO> wechatGroupList) {
        this.wechatGroupList = wechatGroupList;
        this.wechatGroupMap = Optional.ofNullable(wechatGroupList)
                .orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(WechatGroupVO::getChatId, Function.identity()));
    }

    public void setWechatUserList(List<WechatUserDTO> wechatUserList) {
        this.wechatUserList = wechatUserList;
        this.wechatUserMap = Optional.ofNullable(wechatUserList)
                .orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(WechatUserDTO::getUserId, Function.identity()));
    }

    public void setWechatCustomerList(List<WechatCustomerVO> wechatCustomerList) {
        this.wechatCustomerList = wechatCustomerList;
        this.wechatCustomerMap = Optional.ofNullable(wechatCustomerList)
                .orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(WechatCustomerVO::getExternalUserId, Function.identity()));
    }

    public void setCustomerRelationList(List<CustomerRelationDO> customerRelationList) {
        this.customerRelationList = customerRelationList;
        this.customerRelationMap = Optional.ofNullable(customerRelationList)
                .orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(CustomerRelationDO::getExternalUserId, Function.identity()));
    }

    public void setCustomerManageGroupList(List<CustomerManageGroupDO> customerManageGroupList) {
        this.customerManageGroupList = customerManageGroupList;
        this.customerManageGroupMap = Optional.ofNullable(customerManageGroupList)
                .orElse(new ArrayList<>()).stream()
                .collect(Collectors.toMap(CustomerManageGroupDO::getId, Function.identity()));
    }
}
