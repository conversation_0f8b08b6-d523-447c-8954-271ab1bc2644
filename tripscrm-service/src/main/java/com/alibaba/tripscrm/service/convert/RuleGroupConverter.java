package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.RuleGroupDO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.query.RuleGroupQuery;
import com.alibaba.tripscrm.service.model.domain.request.RuleGroupQueryRequest;
import com.alibaba.tripscrm.service.model.vo.rule.RuleGroupVO;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RuleGroupConverter {
    private final AccountService accountService;

    public RuleGroupQuery queryRequest2Query(RuleGroupQueryRequest request) {
        RuleGroupQuery query = new RuleGroupQuery();
        BeanUtils.copyProperties(request, query);
        query.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        query.setEnv(EnvUtils.getEnvironment());
        if (Objects.nonNull(request.getCreateEndTime())) {
            // createEndTime的后一天
            query.setCreateEndTime(DateUtils.addDays(request.getCreateEndTime(), 1));
        }

        return query;
    }

    public RuleGroupVO do2vo(RuleGroupDO ruleGroupDO) {
        if (Objects.isNull(ruleGroupDO)) {
            return null;
        }

        RuleGroupVO ruleGroupVO = new RuleGroupVO();
        BeanUtils.copyProperties(ruleGroupDO, ruleGroupVO);

        if (StringUtils.hasText(ruleGroupVO.getMemberIds())) {
            String[] memberIdList = ruleGroupVO.getMemberIds().split(",");
            String memberNameList = Arrays.stream(memberIdList).map(accountService::getUserByAccountId).filter(Objects::nonNull).map(User::getUserName).collect(Collectors.joining(","));
            ruleGroupVO.setMemberNames(memberNameList);
        }

        if (StringUtils.hasText(ruleGroupVO.getCreatorId())) {
            User user = accountService.getUserByAccountId(ruleGroupVO.getCreatorId());
            if (Objects.nonNull(user)) {
                ruleGroupVO.setCreatorName(user.getUserName());
            }
        }

        return ruleGroupVO;
    }

    public RuleGroupDO vo2do(RuleGroupVO ruleGroupVO) {
        if (Objects.isNull(ruleGroupVO)) {
            return null;
        }

        RuleGroupDO ruleGroupDO = new RuleGroupDO();
        BeanUtils.copyProperties(ruleGroupVO, ruleGroupDO);
        ruleGroupDO.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        User user = accountService.getUserInWebThread();
        if (Objects.nonNull(user)) {
            ruleGroupDO.setLastOperatorId(user.getUserId());
        }

        return ruleGroupDO;
    }
}
