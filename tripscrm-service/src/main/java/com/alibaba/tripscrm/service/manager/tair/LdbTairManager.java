package com.alibaba.tripscrm.service.manager.tair;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.tair.DataEntry;
import com.taobao.tair.Result;
import com.taobao.tair.ResultCode;
import com.taobao.tair.TairManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 封装一个申请的LDB关于Tair的服务 该Tair 目前只有1GB存储，500QPS 的读写，请慎用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LdbTairManager {
    /**
     * 当前LDB 对应的NameSpace
     */
    private static final int LDB_NAME_SPACE = 1870;

    private final TairManager tairManager;

    public LdbTairManager(@Qualifier("ldbTair") TairManager tairManager) {
        this.tairManager = tairManager;
    }

    /**
     * 往LDB中写入数据
     *
     * @param key        Tair Key
     * @param value      Tair值
     * @param expireTime 超时时间 秒
     * @return 是否存放成功
     */
    public boolean put(String key, Serializable value, int expireTime) {
        try {
            // tair的put操作，返回的ResultCode包括操作是否成功的信息
            ResultCode result = tairManager.put(LDB_NAME_SPACE, key, value, 0, expireTime);
            if (result == null || !result.isSuccess()) {
                PlatformLogUtil.logFail("往LDB中写入数据失败", LogListUtil.newArrayList(key, value, expireTime, result));
            }
            return result != null && result.isSuccess();
        } catch (Exception e) {
            PlatformLogUtil.logException("往LDB中写入数据异常", e.getMessage(), e, LogListUtil.newArrayList(key, value, expireTime));
            throw e;
        }
    }

    /**
     * 往LDB中写入数据
     *
     * @param key   Tair Key
     * @param value Tair值
     * @return 是否存放成功
     */
    public boolean put(String key, Serializable value) {
        try {
            // tair的put操作，返回的ResultCode包括操作是否成功的信息
            ResultCode result = tairManager.put(LDB_NAME_SPACE, key, value, 0);
            if (result == null || !result.isSuccess()) {
                PlatformLogUtil.logFail("往LDB中写入数据失败", LogListUtil.newArrayList(key, value, result));
            }
            return result != null && result.isSuccess();
        } catch (Exception e) {
            PlatformLogUtil.logException("往LDB中写入异常", e.getMessage(), e, LogListUtil.newArrayList(key, value));
            throw e;
        }
    }

    /**
     * 从LDB中读取数据
     *
     * @param key Tair Key
     * @return 在Tair中保存的数据
     */
    public Object get(String key) {
        try {
            Result<DataEntry> result = tairManager.get(LDB_NAME_SPACE, key);
            if (result == null || !result.isSuccess()) {
                PlatformLogUtil.logFail("从LDB中读取数据失败", LogListUtil.newArrayList(key, result));
            }
            return Optional.ofNullable(result)
                    .filter(Result::isSuccess)
                    .map(Result::getValue)
                    .map(DataEntry::getValue)
                    .orElse(null);
        } catch (Exception e) {
            PlatformLogUtil.logException("从LDB中读取数据异常", e.getMessage(), e, LogListUtil.newArrayList(key));
            throw e;
        }
    }

    /**
     * 从LDB中读取数据
     *
     * @param keys Tair Key
     * @return 在Tair中保存的数据
     */
    public List<DataEntry> mGet(List<String> keys) {
        try {
            Result<List<DataEntry>> result = tairManager.mget(LDB_NAME_SPACE, keys);
            if (result == null || !result.isSuccess()) {
                PlatformLogUtil.logFail("从LDB中读取数据失败", LogListUtil.newArrayList(keys, result));
            }
            return new ArrayList<>(Optional.ofNullable(result)
                    .map(Result::getValue)
                    .orElse(Collections.emptyList()));
        } catch (Exception e) {
            PlatformLogUtil.logException("从LDB中读取数据异常", e.getMessage(), e, LogListUtil.newArrayList(keys));
            throw e;
        }
    }

    /**
     * 设置计数器的值
     *
     * @param key        指定的Tair Key
     * @param value      TairKey对应的Value初始值
     * @param expireTime 该Tair Key的超时时间
     */
    public void setCount(String key, int value, int expireTime) {
        try {
            ResultCode result = tairManager.setCount(LDB_NAME_SPACE, key, value, 0, expireTime);
            if (result == null || !result.isSuccess()) {
                PlatformLogUtil.logFail("设置计数器的值失败", LogListUtil.newArrayList(key, value, expireTime, result));
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("设置计数器的值异常", e.getMessage(), e, LogListUtil.newArrayList(key, value, expireTime));
            throw e;
        }
    }

    /**
     * @param key          指定的Tair Key
     * @param value        TairKey对应的Value需要累加的数字
     * @param defaultValue 当TairKey不存在时 默认为该值
     * @param expireTime   该Tair Key的超时时间
     * @return Tair Key累加后的结果
     */
    public int incr(String key, int value, int defaultValue, int expireTime) {
        try {
            Result<Integer> result = tairManager.incr(LDB_NAME_SPACE, key, value, defaultValue, expireTime);
            if (result == null || !result.isSuccess()) {
                PlatformLogUtil.logFail("Tair Key进行incr失败", LogListUtil.newArrayList(key, value, defaultValue, expireTime, result));
            }
            return Optional.ofNullable(result)
                    .filter(Result::isSuccess)
                    .map(Result::getValue)
                    .orElse(defaultValue);
        } catch (Exception e) {
            PlatformLogUtil.logException("Tair Key进行incr异常", e.getMessage(), e, LogListUtil.newArrayList(key, value, defaultValue, expireTime));
            throw e;
        }
    }

    /**
     * @param key          指定的Tair Key
     * @param value        TairKey对应的Value需要累加的数字
     * @param defaultValue 当TairKey不存在时 默认为该值
     * @param expireTime   该Tair Key的超时时间
     * @return Tair Key累加后的结果
     */
    public int incr(String key, int value, int defaultValue, int expireTime, int lowBound, int upperBound) {
        try {
            Result<Integer> result = tairManager.incr(LDB_NAME_SPACE, key, value, defaultValue, expireTime, lowBound, upperBound);
            if (result == null || !result.isSuccess()) {
                PlatformLogUtil.logFail("Tair Key进行incr失败", LogListUtil.newArrayList(key, value, defaultValue, expireTime, result));
            }
            return Optional.ofNullable(result)
                    .filter(Result::isSuccess)
                    .map(Result::getValue)
                    .orElse(defaultValue);
        } catch (Exception e) {
            PlatformLogUtil.logException("Tair Key进行incr异常", e.getMessage(), e, LogListUtil.newArrayList(key, value, defaultValue, expireTime));
            throw e;
        }
    }

    /**
     * @param key          指定的Tair Key
     * @param value        TairKey对应的Value需要累减的数字
     * @param defaultValue 当TairKey不存在时 默认为该值
     * @param expireTime   该Tair Key的超时时间
     * @param retryTime    重试次数
     * @return Tair Key累加后的结果
     */
    public int decr(String key, int value, int defaultValue, int retryTime, int expireTime) {
        try {
            for (int i = 0; i < retryTime; i++) {
                Result<Integer> result = tairManager.decr(LDB_NAME_SPACE, key, value, defaultValue, expireTime);
                if (result == null || !result.isSuccess()) {
                    PlatformLogUtil.logFail("Tair Key进行decr失败", LogListUtil.newArrayList(key, value, defaultValue, expireTime, result));
                } else {
                    return Optional.ofNullable(result)
                            .filter(Result::isSuccess)
                            .map(Result::getValue)
                            .orElse(defaultValue);
                }
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("Tair Key进行decr异常", e.getMessage(), e, LogListUtil.newArrayList(key, value, defaultValue, expireTime));
            throw e;
        }
        return defaultValue;
    }

    /**
     * 删除指定的TairKey
     *
     * @param key Tair Key
     * @return 是否删除成功
     */
    public boolean delete(String key) {
        if (StringUtils.isEmpty(key)) {
            PlatformLogUtil.logFail("删除指定的TairKey，入参key为空");
            return false;
        }
        try {
            ResultCode result = tairManager.delete(LDB_NAME_SPACE, key);
            if (result == null || !result.isSuccess()) {
                PlatformLogUtil.logFail("删除指定的TairKey失败", LogListUtil.newArrayList(key, result));
                return false;
            }
            return result.isSuccess();
        } catch (Exception e) {
            PlatformLogUtil.logException("删除指定的TairKey异常", e.getMessage(), e, LogListUtil.newArrayList(key));
            throw e;
        }
    }

}
