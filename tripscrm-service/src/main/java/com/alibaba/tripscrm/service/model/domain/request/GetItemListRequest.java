package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/6/28 16:20
 **/
@Data
public class GetItemListRequest {

    /**
     * 物料类型
     * @see ItemTypeEnum
     */
    private Integer itemType;

    /**
     * 商家ID
     */
    private String sellerId;

    /**
     * 是否分页
     */
    private Boolean isPage = true;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 搜索
     */
    private String searchKey;


}
