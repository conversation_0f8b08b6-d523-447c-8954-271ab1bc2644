package com.alibaba.tripscrm.service.service.task.ability.executor.impl;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.MonitorRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleGroupEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IndustryTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.system.SceneTypeEnum;
import com.alibaba.tripscrm.service.enums.system.SpaceEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.dto.task.MonitorRecordExtInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.MonitorRecordService;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.google.common.collect.Lists;
import com.ibm.icu.text.SimpleDateFormat;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MonitorRecordExecutor extends AbstractTaskExecutor {
    public static String dingTalkAccessToken = "2bf63eed4481414c6aefe85c2702649e0cc1951967f68702cf6a193ed87f2434";

    private final RuleService ruleService;
    private final MonitorRecordService monitorRecordService;
    private final DingTalkApi dingTalkApi;
    private final WechatGroupService wechatGroupService;
    private final WechatUserService wechatUserService;
    private final WechatCustomerService wechatCustomerService;
    private final GroupRelationRepository groupRelationRepository;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 从群聊消息接口中获得群聊消息
        List<TaskDataVO.DataBodyVO> dataBodyList = todoTaskVO.getData();

        if (CollectionUtils.isEmpty(dataBodyList)) {
            PlatformLogUtil.logFail("群聊消息为空", Lists.newArrayList(todoTaskVO.getTaskId(), todoTaskVO));
            return;
        }

        // 参数校验
        if (!paramVerify(dataBodyList.get(0), context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            return;
        }
        // 是否跳过
        if (skipProcess(dataBodyList.get(0), context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            todoTaskVO.setFailReason("不符合执行条件");
            return;
        }
        // 埋点日志
        PlatformLogUtil.logInfo("群聊消息接收成功，开始校验消息与用户身份", Lists.newArrayList(todoTaskVO.getTaskId(), todoTaskVO));
        // 业务处理
        businessProcess(context, todoTaskVO);

    }

    private boolean paramVerify(TaskDataVO.DataBodyVO dataBodyVO, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 目标信息需要存在（群/客户）
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(dataBodyVO.getTargetType());
        if (Objects.isNull(targetTypeEnum)) {
            PlatformLogUtil.logFail("群监控任务目标类型不存在", Lists.newArrayList(todoTaskVO.getTaskId(), dataBodyVO));
            return false;
        }
        if (!StringUtils.hasLength(dataBodyVO.getTargetId())) {
            PlatformLogUtil.logFail("群监控任务目标ID不存在", Lists.newArrayList(todoTaskVO.getTaskId(), dataBodyVO));
            return false;
        }
        // 任务空间校验
        if (Objects.isNull(getTaskBelongSpace(context, todoTaskVO))) {
            PlatformLogUtil.logFail("群监控任务参数校验不通过", Lists.newArrayList(todoTaskVO.getTaskId(), dataBodyVO));
            return false;
        }
        // 目标空间校验
        Long belongSpace = wechatUserService.getSpaceId(getSendUserId(context, todoTaskVO), SpaceInfoThreadLocalUtils.getCorpId());
        if (!NumberUtils.validLong(belongSpace)) {
            PlatformLogUtil.logFail("群监控任务找不到目标对应的空间", Lists.newArrayList(todoTaskVO.getTaskId(), dataBodyVO));
            return false;
        }
        // 发送人校验
        if (!StringUtils.hasLength(getSendUserId(context, todoTaskVO))) {
            PlatformLogUtil.logFail("群监控任务发送人为空", Lists.newArrayList(todoTaskVO.getTaskId(), dataBodyVO));
            return false;
        }
        // 消息内容
        if (MapUtils.isEmpty(context.getExtInfo()) || org.apache.commons.lang3.StringUtils.isBlank(MapUtils.getString(context.getExtInfo(), "message"))) {
            PlatformLogUtil.logFail("群监控任务消息为空", Lists.newArrayList(todoTaskVO.getTaskId(), dataBodyVO));
            return false;
        }
        return true;
    }

    /**
     * 获取任务的归属空间
     *
     * @param todoTaskVO 任务数据
     * @return 归属空间ID
     */
    private Long getTaskBelongSpace(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (Objects.isNull(context)|| Objects.isNull(todoTaskVO) || Objects.isNull(context.getTaskInfoDOSnapshot())) {
            return null;
        }
        return context.getTaskInfoDOSnapshot().getSpaceId();
    }

    private void businessProcess(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 消息获取
        MonitorRecordExtInfoDTO monitorRecordExtInfoDTO = getMonitorRecordMatchRule(context, todoTaskVO);
        String message = monitorRecordExtInfoDTO.getMessage();
        MessageTypeEnum messageTypeEnum = monitorRecordExtInfoDTO.getMsgSourceType();
        String name = "";
        if (messageTypeEnum == MessageTypeEnum.MINI_PROGRAM) {
            String oldMsg = message;
            message = oldMsg.substring(0, oldMsg.indexOf(","));
            name = "（小程序卡片-" + oldMsg.substring(oldMsg.indexOf(",") + 1) + ")";
        }
        Long spaceId = wechatUserService.getSpaceId(getSendUserId(context, todoTaskVO), SpaceInfoThreadLocalUtils.getCorpId());
        // 对群聊消息进行关键词匹配和发消息用户的身份检查（是否为黑名单中的企业微信、小程序卡片）
        Pair<String, Byte> checkResult = checkGroupMessage(message, messageTypeEnum, getGroupId(spaceId), monitorRecordExtInfoDTO);
        // 如果关键词匹配成功或发消息用户为黑名单中的用户
        if (StringUtils.hasLength(checkResult.getLeft())) {
            // 将该用户以识别加入的方式存入黑名单表
            PlatformLogUtil.logInfo("识别成功，将用户记录到规则记录列表", Lists.newArrayList(monitorRecordExtInfoDTO, checkResult, message, spaceId));
            if (Objects.equals(checkResult.getRight(), RuleTypeEnum.GROUP_MONITOR_MINI_PROGRAM_CARD.getCode().byteValue())){
                message = message + "  " + name;
            }
            MonitorRecordDO monitorRecordDO = getMonitorRecordDO(monitorRecordExtInfoDTO, checkResult, message, context, todoTaskVO, spaceId);
            try {
                monitorRecordService.insert(monitorRecordDO);
            } catch (Exception e) {
                PlatformLogUtil.logFail("监控记录插入失败", Lists.newArrayList(monitorRecordDO, e));
            }
            // 给钉钉群发消息
            sendMsg2DingTalk(message, checkResult.getLeft(), checkResult.getRight(),  monitorRecordExtInfoDTO);

        }
    }

    private Long getGroupId(Long spaceId) {
        switch(SpaceEnum.getByCode(spaceId)) {
            case TEST:
                if (EnvUtils.isPre()) {
                    return RuleGroupEnum.MONITOR_RECORD_TEST_PRE.getCode();
                }
                if (EnvUtils.isOnline()) {
                    return RuleGroupEnum.MONITOR_RECORD_TEST_ONLINE.getCode();
                }
            case USER_OPERATION:
                if (EnvUtils.isPre()) {
                    return RuleGroupEnum.MONITOR_RECORD_USER_OPERATION_PRE.getCode();
                }
                if (EnvUtils.isOnline()) {
                    return RuleGroupEnum.MONITOR_RECORD_USER_OPERATION_ONLINE.getCode();
                }
                break;
        }
        PlatformLogUtil.logFail("当前空间和环境下不存在群监控规则组", LogListUtil.newArrayList());
        throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
    }
    /**
     * 获取monitorRecordDO
     * @param monitorRecordExtInfoDTO
     * @param checkResult
     * @param message
     * @return
     */
    private @NotNull MonitorRecordDO getMonitorRecordDO(MonitorRecordExtInfoDTO monitorRecordExtInfoDTO, Pair<String, Byte> checkResult, String message, TaskExecuteContext context, TodoTaskVO todoTaskVO, Long spaceId) {
        MonitorRecordDO monitorRecordDO = new MonitorRecordDO();
        monitorRecordDO.setTargetId(monitorRecordExtInfoDTO.getExternalUserId());
        monitorRecordDO.setTargetType((byte) 1);
        monitorRecordDO.setSpaceId(spaceId);
        monitorRecordDO.setIndustry(IndustryTypeEnum.PLATFORM.getCode().byteValue());
        monitorRecordDO.setScene(SceneTypeEnum.GROUP_DEFENSE.getCode().byteValue());
        monitorRecordDO.setEntryMethod((byte) 0);
        monitorRecordDO.setDeleted((byte) 0);

        monitorRecordDO.setBizId(checkResult.getLeft());

        monitorRecordDO.setStatus((byte) 0);
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("message", message);
        extraInfo.put("entryMethodL2", checkResult.getRight());
        extraInfo.put("groupChatId", monitorRecordExtInfoDTO.getChatId());
        monitorRecordDO.setExtraInfo(extraInfo.toJSONString());
        return monitorRecordDO;
    }

    /**
     *
     * @param message 消息
     * @param monitorRecordExtInfoDTO 用户信息
     * @param bizId 命中的规则id
     * @param type 命中的规则类型
     */
    private void sendMsg2DingTalk(String message, String bizId, Byte type, MonitorRecordExtInfoDTO monitorRecordExtInfoDTO) {
        try {
            WechatGroupVO wechatGroupVO = wechatGroupService.getWechatGroupVOByChatId(monitorRecordExtInfoDTO.getChatId());
            String externalUserName = "";
            String corpName = "";
            // 如果用户为外部企业用户，从群关系表中获取用户名
            if (Objects.nonNull(monitorRecordExtInfoDTO.getGroupRelationDO())) {
                externalUserName = monitorRecordExtInfoDTO.getGroupRelationDO().getName();
            }
            // 如果为个人微信用户，从用户信息表中获取用户名和所属企业名
            if (Objects.nonNull(monitorRecordExtInfoDTO.getWechatCustomerVO())) {
                externalUserName = monitorRecordExtInfoDTO.getWechatCustomerVO().getName();
                corpName = monitorRecordExtInfoDTO.getWechatCustomerVO().getCorpName();
                // 如果type为企业微信
                if (Objects.equals(type, RuleTypeEnum.GROUP_MONITOR_OTHER_CORP_CUSTOMER.getCode().byteValue())) {
                    message = message + " " + "(企业微信-" + monitorRecordExtInfoDTO.getWechatCustomerVO().getCorpName() + ")";
                }
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String date = sdf.format(new Date());
            String monitorContent = "不在白名单中";
            // 如果type为关键词
            if (Objects.equals(type, RuleTypeEnum.GROUP_MONITOR_KEYWORD.getCode().byteValue())) {
                RuleDO ruleDO = ruleService.selectById(Long.valueOf(bizId));
                JSONObject config = JSONObject.parseObject(ruleDO.getConfig());
                monitorContent = config.getString("monitorContent");
            }
            dingTalkApi.sendMessageToDingTalkGroup(dingTalkAccessToken, "异常账号提醒（是否加入黑名单）"
                    + "\n微信名：" + externalUserName
                    + "\n入库方式：识别加入"
                    + "\n所属企业：" + corpName
                    + "\n微信群主：" + wechatGroupVO.getOwnerUser().getName()
                    + "\n微信群名：" + wechatGroupVO.getName()
                    + "\n消息：" + message
                    + "\n命中的监控内容：" + monitorContent
                    + "\n命中的规则类型：" + RuleTypeEnum.getByCode(Integer.valueOf(type)).getDesc()
                    + "\n发送时间：" + date
                    + "\n处理人：", SwitchConfig.MONITOR_RECORD_GROUP_MANAGER
            );

        } catch (Exception e) {
            PlatformLogUtil.logException("给钉钉群发送消息失败", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    /**
     * 对群聊消息进行关键词匹配和发消息用户的身份检查（是否为黑名单中的企业微信、小程序卡片）
     * @param message
     * @param monitorRecordExtInfoDTO
     * @param messageTypeEnum
     * @return
     */
    private Pair<String, Byte> checkGroupMessage(String message,  MessageTypeEnum messageTypeEnum, Long groupId, MonitorRecordExtInfoDTO monitorRecordExtInfoDTO) {
        if (Objects.isNull(monitorRecordExtInfoDTO)) {
            PlatformLogUtil.logFail("规则匹配失败，monitorRecordExtInfoDTO为空", Lists.newArrayList(message, monitorRecordExtInfoDTO, messageTypeEnum));
            return new ImmutablePair<>("", null);
        }
        PlatformLogUtil.logInfo("对群聊消息进行关键词匹配和发消息用户的身份检查", Lists.newArrayList(message, monitorRecordExtInfoDTO, messageTypeEnum));
        // 如果用户unionId为空，则为外部企业用户，直接添加到规则记录列表
        if (Objects.nonNull(monitorRecordExtInfoDTO.getGroupRelationDO())) {
            PlatformLogUtil.logInfo("外部企业用户，直接添加到规则记录列表", Lists.newArrayList(monitorRecordExtInfoDTO));
            String bizId = "";
            List<RuleDO> ruleDOList = ruleService.selectByTypeAndDeletedWithCache(RuleTypeEnum.GROUP_MONITOR_OTHER_CORP_CUSTOMER.getCode().byteValue(), IsDeleteEnum.NO.getCode(), groupId);
            if (!CollectionUtils.isEmpty(ruleDOList)) {
                bizId = ruleDOList.stream().map(RuleDO::getId).map(String::valueOf).collect(Collectors.joining(","));
            }
            return new ImmutablePair<>(bizId, RuleTypeEnum.GROUP_MONITOR_OTHER_CORP_CUSTOMER.getCode().byteValue());
        }
        String corpName = "";
        if (Objects.nonNull(monitorRecordExtInfoDTO.getWechatCustomerVO())) {
            corpName = monitorRecordExtInfoDTO.getWechatCustomerVO().getCorpName();
        }
        // 身份为企业微信
        if (Objects.nonNull(monitorRecordExtInfoDTO.getWechatCustomerVO()) && monitorRecordExtInfoDTO.getWechatCustomerVO().getType() == 2) {
            String bizId = checkMiniProgramOrOtherCorpCustomer(corpName, RuleTypeEnum.GROUP_MONITOR_OTHER_CORP_CUSTOMER.getCode().byteValue(), groupId);
            if (StringUtils.hasLength(bizId)) {
                return new ImmutablePair<>(bizId, RuleTypeEnum.GROUP_MONITOR_OTHER_CORP_CUSTOMER.getCode().byteValue());
            }
        }
        // 发送的消息为文本
        if (messageTypeEnum == MessageTypeEnum.TEXT){
            String bizId = checkKeyWord(message, RuleTypeEnum.GROUP_MONITOR_KEYWORD.getCode().byteValue(), groupId);
            if (StringUtils.hasLength(bizId)) {
                return new ImmutablePair<>(bizId, RuleTypeEnum.GROUP_MONITOR_KEYWORD.getCode().byteValue());
            }
        }
        // 发送的消息为小程序卡片
        if (messageTypeEnum == MessageTypeEnum.MINI_PROGRAM) {
            String bizId = checkMiniProgramOrOtherCorpCustomer(message, RuleTypeEnum.GROUP_MONITOR_MINI_PROGRAM_CARD.getCode().byteValue(), groupId);
            if (StringUtils.hasLength(bizId)) {
                return new ImmutablePair<>(bizId, RuleTypeEnum.GROUP_MONITOR_MINI_PROGRAM_CARD.getCode().byteValue());
            }
        }
        return new ImmutablePair<>("", null);
    }

    /**
     * 对群聊消息进行关键词匹配
     * @param content
     * @param type
     * @return
     */
    private String checkKeyWord(String content, Byte type, Long groupId) {
        List<RuleDO> ruleDOList = ruleService.selectByTypeAndDeletedWithCache(type, IsDeleteEnum.NO.getCode(), groupId);
        // 如果监控内容中包含关键词，则识别为黑名单
        for (RuleDO ruleDO : ruleDOList) {
            JSONObject config = JSONObject.parseObject(ruleDO.getConfig());
            String monitorContent = config.getString("monitorContent");
            if (monitorContent != null && content.contains(monitorContent)) {
                return String.valueOf(ruleDO.getId());
            }
        }
        return "";
    }

    /**
     * 对微信小程序/企微号进行匹配
     * @param content
     * @param type
     * @return
     */
    private String checkMiniProgramOrOtherCorpCustomer(String content, Byte type, Long groupId) {
        PlatformLogUtil.logFail("对微信小程序/企微号进行匹配", LogListUtil.newArrayList(content, type, groupId));
        List<RuleDO> ruleDOList = ruleService.selectByTypeAndDeletedWithCache(type, IsDeleteEnum.NO.getCode(), groupId);
        StringBuilder bizId = new StringBuilder();
        for (RuleDO ruleDO : ruleDOList) {
            JSONObject config = JSONObject.parseObject(ruleDO.getConfig());
            String monitorContent = config.getString("monitorContent");
            bizId.append(",").append(ruleDO.getId());
            if (Objects.equals(content, monitorContent)) {
                return "";
            }
        }
        return bizId.substring(1);
    }

    /**
     * 获取monitorRecordExtInfoDTO
     * @param context
     * @param todoTaskVO
     * @return
     */
    private MonitorRecordExtInfoDTO getMonitorRecordMatchRule(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        Map<String, Object> extInfo = context.getExtInfo();
        if (CollectionUtils.isEmpty(extInfo)) {
            PlatformLogUtil.logFail(TripSCRMErrorCode.INVALID_PARAMS.getDescCn(), TripSCRMErrorCode.INVALID_PARAMS.getCode(), Lists.newArrayList(todoTaskVO.getTaskId(), todoTaskVO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        MonitorRecordExtInfoDTO monitorRecordExtInfoDTO = new MonitorRecordExtInfoDTO();
        monitorRecordExtInfoDTO.setMessage(MapUtils.getString(extInfo, "message"));
        monitorRecordExtInfoDTO.setChatId(MapUtils.getString(extInfo, "chatId"));
        monitorRecordExtInfoDTO.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        monitorRecordExtInfoDTO.setUserId(getSendUserId(context, todoTaskVO));
        monitorRecordExtInfoDTO.setExternalUserId(getFinalTargetId(context, todoTaskVO.getData().get(0)));
        monitorRecordExtInfoDTO.setSenderType(MapUtils.getString(extInfo, "senderType"));
        monitorRecordExtInfoDTO.setMsgSourceType(MessageTypeEnum.valueOf(MapUtils.getInteger(extInfo, "msgType")));

        String errStr = monitorRecordExtInfoDTO.batchAddMustParamReError();
        if (StringUtils.hasLength(errStr)) {
            PlatformLogUtil.logFail("群聊消息接收失败" + errStr, Lists.newArrayList(context, todoTaskVO, errStr));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<WechatCustomerVO> wechatCustomerVOS = wechatCustomerService.listByExternalUserIdList(Collections.singletonList(monitorRecordExtInfoDTO.getExternalUserId()));
        if (!CollectionUtils.isEmpty(wechatCustomerVOS)) {
            monitorRecordExtInfoDTO.setWechatCustomerVO(wechatCustomerVOS.get(0));
            return monitorRecordExtInfoDTO;
        }

        // 如果用户信息表中没有该用户的信息，并且群关系表中用户unionId为空，则为外部企业用户
        List<GroupRelationDO> groupRelationDOS = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(monitorRecordExtInfoDTO.getChatId()), Lists.newArrayList(monitorRecordExtInfoDTO.getExternalUserId()), monitorRecordExtInfoDTO.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (!CollectionUtils.isEmpty(groupRelationDOS) && Objects.equals(GroupUserTypeEnum.of(Integer.valueOf(groupRelationDOS.get(0).getUserType())), GroupUserTypeEnum.CUSTOMER) && !StringUtils.hasLength(groupRelationDOS.get(0).getUnionId())) {
            monitorRecordExtInfoDTO.setGroupRelationDO(groupRelationDOS.get(0));
        }
        return monitorRecordExtInfoDTO;

    }


    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return MapUtils.getString(context.getExtInfo(), "userId");

    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_EXTERNAL_USERID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        return MapUtils.getString(context.getExtInfo(), "targetId");
    }


    private boolean skipProcess(TaskDataVO.DataBodyVO dataBodyVO, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 任务空间和成员空间统一性判断
        Long taskBelongSpace = getTaskBelongSpace(context, todoTaskVO);
        Long targetBelongSpace = wechatUserService.getSpaceId(getSendUserId(context, todoTaskVO), SpaceInfoThreadLocalUtils.getCorpId());
        if (!taskBelongSpace.equals(targetBelongSpace)) {
            PlatformLogUtil.logFail("群监控任务跳过执行，任务空间和成员空间不统一", Lists.newArrayList(todoTaskVO.getTaskId(), dataBodyVO, taskBelongSpace, targetBelongSpace));
            todoTaskVO.setSuccess(true);
            return true;
        }
        return false;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.MONITOR_RECORD;
    }
}
