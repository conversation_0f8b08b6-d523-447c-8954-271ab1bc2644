package com.alibaba.tripscrm.service.convert;

import com.alibaba.schedulerx.common.util.IpUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.SubTaskInstanceQuery;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceSaveVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * 子任务实例模型的防腐层
 */
public class SubTaskInstanceConverter {

    /**
     * 保存请求的参数 转化为 数据模型需要的DO
     *
     * @param request
     * @param supplier
     * @return
     */
    public static SubTaskInstanceDO saveVo2Do(SubTaskInstanceSaveVO request, Supplier<TaskInstanceDO> supplier) {
        SubTaskInstanceDO instanceDO = new SubTaskInstanceDO();
        BeanUtils.copyProperties(request, instanceDO);
        Date now = new Date();
        if (request.getId() == null) {
            instanceDO.setGmtCreate(now);
            instanceDO.setGmtModified(now);
            instanceDO.setDeleted((byte) 0);
        } else {
            instanceDO.setGmtModified(now);
        }
        instanceDO.setIpAddress(IpUtil.getIPV4Address());
        TaskInstanceDO taskInstanceDO = supplier.get();
        if (taskInstanceDO == null) {
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_SUB_TASK_INSTANCE_DATA);
        }
        instanceDO.setMainTaskId(taskInstanceDO.getTaskId());
        return instanceDO;
    }

    /**
     * 查询请求的参数 转化为 查询模型需要的Query对象
     *
     * @param request
     * @return
     */
    public static SubTaskInstanceQuery queryRequest2Query(SubTaskInstanceQueryRequest request) {
        SubTaskInstanceQuery query = new SubTaskInstanceQuery();
        query.setId(request.getId());
        query.setMainTaskId(request.getTaskId());
        query.setMainTaskInstanceId(request.getMainTaskInstanceId());
        query.setStatus(Optional.ofNullable(request.getStatus()).map(TaskInstanceStatusEnum::getStatus).orElse(null));
        query.setDeleted(request.getDeleted());
        return query;
    }
}
