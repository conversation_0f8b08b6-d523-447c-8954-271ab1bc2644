package com.alibaba.tripscrm.service.convert;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.model.domain.data.ManagementGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationWayEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.service.common.ManagementGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/3/6 21:27
 **/
@Component
public class ResourceRelationConverter {

    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private ManagementGroupService managementGroupService;

    /**
     * 创建任务-管理组关系
     * @param managementGroupId 管理组Id
     * @param taskId 任务Id
     * @param wayEnum 关联渠道
     * @return 资源关系
     */
    public ResourceRelationDO buildManagementGroupResourceRelation(String managementGroupId, String taskId, ResourceRelationWayEnum wayEnum) {
        ManagementGroupDO managementGroupDO = managementGroupService.queryById(Long.valueOf(managementGroupId));
        if (managementGroupDO == null){
            PlatformLogUtil.logFail("创建任务-管理组关系，管理组数据在表中为空", LogListUtil.newArrayList(managementGroupId, taskId, wayEnum));
            return null;
        }
        return buildResourceRelation(ResourceRelationTypeEnum.MANAGEMENT_GROUP_USAGE, wayEnum, ResourceTypeEnum.TASK, ResourceTypeEnum.MANAGEMENT_GROUP, taskId, managementGroupId, managementGroupDO.getName());
    }

    /**
     * 创建群活码-群聊关系
     * @param taskId 任务Id
     * @param chatId 群聊Id
     * @param wayEnum 创建方式
     */
    public ResourceRelationDO buildGroupManageTaskResourceRelation(String taskId, String chatId, ResourceRelationWayEnum wayEnum) {
        WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(chatId);
        if (Objects.isNull(wechatGroupDO)) {
            PlatformLogUtil.logFail("创建群活码-群聊关系，管理组数据在表中为空", LogListUtil.newArrayList(chatId, taskId, wayEnum));
            return null;
        }
        return buildResourceRelation(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP, wayEnum, ResourceTypeEnum.TASK, ResourceTypeEnum.CHAT_ID, taskId, chatId, wechatGroupDO.getName());
    }

    /**
     * 创建资源关系
     * @param resourceRelationTypeEnum 使用场景
     * @param wayEnum 关联渠道
     * @param sourceTypeEnum 资源类型
     * @param targetTypeEnum 目标类型
     * @param sourceId 资源Id
     * @param targetId 目标Id
     * @return 资源关系
     */
    private static ResourceRelationDO buildResourceRelation(ResourceRelationTypeEnum resourceRelationTypeEnum, ResourceRelationWayEnum wayEnum,
                                                            ResourceTypeEnum sourceTypeEnum, ResourceTypeEnum targetTypeEnum,
                                                            String sourceId, String targetId, String targetName) {
        ResourceRelationDO resourceRelationDO = new ResourceRelationDO();
        resourceRelationDO.setGmtCreate(new Date());
        resourceRelationDO.setGmtModified(new Date());
        resourceRelationDO.setWay(wayEnum.getCode());
        resourceRelationDO.setRelationType(resourceRelationTypeEnum.getCode());
        resourceRelationDO.setSourceType(sourceTypeEnum.getCode().byteValue());
        resourceRelationDO.setSourceId(sourceId);
        resourceRelationDO.setTargetType(targetTypeEnum.getCode().byteValue());
        resourceRelationDO.setTargetId(targetId);
        resourceRelationDO.setTargetName(targetName);
        return resourceRelationDO;
    }

}
