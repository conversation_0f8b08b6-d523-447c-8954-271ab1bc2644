package com.alibaba.tripscrm.service.middleware.metaq.consumer.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.dal.mapper.lindorm.TaskExecuteResultDAO;
import com.alibaba.tripscrm.dal.model.lindorm.TaskExecuteResultDO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务单次发送结果同步lindorm
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service(value = "taskResultSyncLindormConsumer")
public class TaskResultSyncLindormConsumer implements MessageListenerConcurrently {

    @Resource
    private TaskExecuteResultDAO taskExecuteResultDAO;
    @Switch(description = "任务发送结果同步lindorm开关", name = "OPEN_TASK_SYNC_LINDORM")
    public static Boolean OPEN_TASK_SYNC_LINDORM = Boolean.FALSE;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            if (!OPEN_TASK_SYNC_LINDORM) {
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
            PlatformLogUtil.logInfo("receive", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("receiveMsg", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            JSONObject content = JSONObject.parseObject(message);
            // 解析数据
            TodoTaskVO todoTaskVO = content.getObject("todoTaskVO", new TypeReference<TodoTaskVO>() {
            });

            String result = content.getString("result");

            if (todoTaskVO == null) {
                PlatformLogUtil.logFail("todoTaskVO is null", LogListUtil.newArrayList(message));
                return false;
            }

            Long taskId = todoTaskVO.getTaskId();
            Long instanceId = todoTaskVO.getInstanceId();

            // 原子能力执行结果
            PlatformLogUtil.logInfo("原子能力执行结果同步lindorm", LogListUtil.newArrayList(taskId, instanceId, todoTaskVO.getSuccess(), todoTaskVO.getData()));
            List<TaskDataVO.DataBodyVO> data = todoTaskVO.getData();
            try {
                if (data.size() == 1) {
                    TaskDataVO.DataBodyVO vo = data.get(0);
                    TaskExecuteResultDO record = buildRecord(vo.getTargetId(), vo.getTargetType(), result, todoTaskVO);
                    taskExecuteResultDAO.upsert(record);
                } else {
                    List<TaskExecuteResultDO> recordList = data.stream()
                            .map(t -> buildRecord(t.getTargetId(), t.getTargetType(), result, todoTaskVO))
                            .collect(Collectors.toList());
                    taskExecuteResultDAO.batchUpsert(recordList);
                }
            } catch (Exception ex) {
                PlatformLogUtil.logException("原子能力执行结果同步lindorm，执行异常", ex.getMessage(), ex, LogListUtil.newArrayList(taskId, instanceId, todoTaskVO.getSuccess(), todoTaskVO.getData()));
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
        return true;
    }


    private TaskExecuteResultDO buildRecord(String targetId, Integer targetType, String result, TodoTaskVO todoTaskVO) {
        long now = new Date().getTime();
        Long instanceId = todoTaskVO.getInstanceId();
        TaskExecuteResultDO record = new TaskExecuteResultDO();
        record.setGmtCreate(now);
        record.setGmtModified(now);
        record.setId(instanceId + targetId);
        record.setTaskInstanceId(String.valueOf(instanceId));
        record.setTargetId(targetId);
        record.setTargetType(String.valueOf(targetType));
        record.setStatus(todoTaskVO.getSuccess() ? "success" : "fail");
        record.setResult(result);
        record.setExtInfo(null);

        return record;
    }
}
