package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.CustomizerApprovalDO;
import com.alibaba.tripscrm.dal.model.domain.data.RiskObjectDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.enums.system.CustomizerApprovalStatusEnum;
import com.alibaba.tripscrm.service.model.vo.wechat.RobotVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatUserVO;
import com.alibaba.tripscrm.service.service.risk.RiskActionChecker;
import com.alibaba.tripscrm.service.service.wechat.CustomizerApprovalService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 企微成员相关数据防腐层
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatUserConverter {
    private final WechatCustomerService wechatCustomerService;
    private final WXCorpStorage wxCorpStorage;
    private final CustomizerApprovalService customizerApprovalService;
    private final RiskActionChecker riskActionChecker;

    /**
     * WechatUserDTO 转 WechatUserVO
     */
    public WechatUserVO convert2VO(WechatUserDTO wechatUserDTO) {
        return convert2VO(wechatUserDTO, null, false, false);
    }

    /**
     * WechatUserDTO 转 WechatUserVO
     */
    public WechatUserVO convert2VO(WechatUserDTO wechatUserDTO, String riskModuleCode) {
        return convert2VO(wechatUserDTO, riskModuleCode, false, false);
    }

    /**
     * WechatUserDTO 转 WechatUserVO
     */
    public WechatUserVO convert2VO(WechatUserDTO wechatUserDTO, String riskModuleCode, Boolean needCustomerCount, Boolean needCustomizerStatus) {
        if (Objects.isNull(wechatUserDTO)) {
            return null;
        }

        WechatUserVO wechatUserVO = new WechatUserVO();
        wechatUserVO.setId(wechatUserDTO.getId());
        wechatUserVO.setUserId(wechatUserDTO.getUserId());
        wechatUserVO.setCorpId(wechatUserDTO.getCorpId());
        wechatUserVO.setCorpName(wechatUserDTO.getCorpName());
        wechatUserVO.setName(wechatUserDTO.getName());
        wechatUserVO.setRealName(wechatUserDTO.getRealName());
        wechatUserVO.setGender(wechatUserDTO.getGender());
        wechatUserVO.setAvatarUrl(wechatUserDTO.getAvatarUrl());
        wechatUserVO.setAlias(wechatUserDTO.getAlias());
        wechatUserVO.setActivateStatus(wechatUserDTO.getActivateStatus());
        wechatUserVO.setDepartmentId(wechatUserDTO.getDepartmentId());
        wechatUserVO.setDepartmentName(wechatUserDTO.getDepartmentName());
        wechatUserVO.setOnlineStatus(wechatUserDTO.getOnlineStatus());
        wechatUserVO.setStatus(wechatUserDTO.getStatus());
        wechatUserVO.setRobotType(wechatUserDTO.getRobotType());
        wechatUserVO.setAdminList(wechatUserDTO.getAdminList());
        wechatUserVO.setAdminNameList(wechatUserDTO.getAdminNameList());
        wechatUserVO.setMemberList(wechatUserDTO.getMemberList());
        wechatUserVO.setMemberNameList(wechatUserDTO.getMemberNameList());
        wechatUserVO.setLockUser(wechatUserDTO.getLockUser());
        wechatUserVO.setLockUserName(wechatUserDTO.getLockUserName());
        if (!CollectionUtils.isEmpty(wechatUserDTO.getRobotList())) {
            wechatUserVO.setRobotList(wechatUserDTO.getRobotList().stream().map(robotDTO -> {
                RobotVO robotVO = new RobotVO();
                robotVO.setUserId(robotDTO.getUserId());
                robotVO.setIsvType(robotDTO.getIsvType());
                robotVO.setIsvName(IsvTypeEnum.valueOf(robotDTO.getIsvType()).getDesc());
                robotVO.setOnlineStatus(robotDTO.getRobotStatus());
                return robotVO;
            }).collect(Collectors.toList()));
        }

        if (needCustomerCount) {
            wechatUserVO.setCustomerCount(wechatCustomerService.getCustomerCount(wechatUserDTO.getUserId(), true));
        }

        if (needCustomizerStatus) {
            CustomizerApprovalDO customizerApprovalDO = customizerApprovalService.selectByWechatUserId(wechatUserDTO.getId());
            if (Objects.isNull(customizerApprovalDO)) {
                wechatUserVO.setCustomizerStatusDesc(CustomizerApprovalStatusEnum.INIT.getDesc());
            } else {
                CustomizerApprovalStatusEnum customizerApprovalStatusEnum = CustomizerApprovalStatusEnum.findByCode(customizerApprovalDO.getStatus());
                wechatUserVO.setCustomizerStatusDesc(Objects.isNull(customizerApprovalStatusEnum) ? CustomizerApprovalStatusEnum.INIT.getDesc() : customizerApprovalStatusEnum.getDesc());
            }
        }


        // 获取风控信息
        Map<String, RiskStatusEnum> riskStatusMap = riskActionChecker.showUserRiskStatus(Lists.newArrayList(wechatUserDTO.getUserId()), SpaceInfoThreadLocalUtils.getCorpId(), riskModuleCode);
        wechatUserVO.setRiskStatus(riskStatusMap.get(RiskObjectDO.buildRobotTargetId(wechatUserDTO.getCorpId(), wechatUserDTO.getUserId())).getCode());

        return wechatUserVO;
    }

    public WechatUserDTO convert(WechatUserDO wechatUserDO) {
        WechatUserDTO wechatUserDTO = new WechatUserDTO();
        wechatUserDTO.setId(wechatUserDO.getId());
        wechatUserDTO.setUserId(wechatUserDO.getUserId());
        wechatUserDTO.setName(wechatUserDO.getName());
        wechatUserDTO.setAlias(wechatUserDO.getAlias());
        wechatUserDTO.setDepartmentId(wechatUserDO.getMainDepartment());
        wechatUserDTO.setRealName(wechatUserDO.getRealName());
        wechatUserDTO.setGender(wechatUserDO.getGender().intValue());
        wechatUserDTO.setOnlineStatus(wechatUserDO.getRobotStatus().intValue());
        wechatUserDTO.setStatus(wechatUserDO.getStatus().intValue());
        wechatUserDTO.setAvatarUrl(wechatUserDO.getAvatar());
        wechatUserDTO.setCorpName(wxCorpStorage.getCorpName(wechatUserDO.getCorpId()));
        wechatUserDTO.setCorpId(wechatUserDO.getCorpId());
        wechatUserDTO.setGmtModified(wechatUserDO.getGmtModified());
        wechatUserDTO.setOfflineTime(wechatUserDO.getOfflineTime());
        wechatUserDTO.setOnlineTime(wechatUserDO.getOnlineTime());
        wechatUserDTO.setActivateStatus(wechatUserDO.getActivateStatus().intValue());
        if(Objects.nonNull(wechatUserDO.getRobotType())){
            wechatUserDTO.setRobotType(wechatUserDO.getRobotType().intValue());
        }
        return wechatUserDTO;
    }
}
