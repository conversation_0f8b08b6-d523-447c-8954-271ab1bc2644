package com.alibaba.tripscrm.service.service.impl.wechat.groupmsg;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatGroupMsgSendResultMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgSendResultDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgSendResultParam;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupMsgSendResultQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.wechat.groupmsg.WechatGroupMsgSendResultService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.tddl.client.sequence.Sequence;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class WechatGroupMsgSendResultServiceImpl implements WechatGroupMsgSendResultService {
    private final WechatGroupMsgSendResultMapper wechatGroupMsgSendResultMapper;
    private final Sequence wechatGroupMsgSendResultIdSequence;

    public WechatGroupMsgSendResultServiceImpl(WechatGroupMsgSendResultMapper wechatGroupMsgSendResultMapper, @Qualifier("wechatGroupMsgSendResultIdSequence") Sequence wechatGroupMsgSendResultIdSequence) {
        this.wechatGroupMsgSendResultMapper = wechatGroupMsgSendResultMapper;
        this.wechatGroupMsgSendResultIdSequence = wechatGroupMsgSendResultIdSequence;
    }

    /**
     * 根据参数查询
     *
     * @param query
     */
    @Override
    public WechatGroupMsgSendResultDO find(WechatGroupMsgSendResultQuery query) {
        List<WechatGroupMsgSendResultDO> list = list(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<WechatGroupMsgSendResultDO> list(WechatGroupMsgSendResultQuery param) {
        WechatGroupMsgSendResultParam wechatGroupMsgSendResultParam = buildParam(param);
        List<WechatGroupMsgSendResultDO> list = wechatGroupMsgSendResultMapper.selectByParam(wechatGroupMsgSendResultParam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list;
    }

    /**
     * 创建
     *
     * @param record
     */
    @Override
    public int insertSelective(WechatGroupMsgSendResultDO record) {
        record.setId(wechatGroupMsgSendResultIdSequence.nextValue());
        return wechatGroupMsgSendResultMapper.insertSelective(record);
    }

    /**
     * 修改
     *
     * @param record
     * @param condition
     */
    @Override
    public int updateSelective(WechatGroupMsgSendResultDO record, WechatGroupMsgSendResultQuery condition) {
        WechatGroupMsgSendResultParam wechatGroupMsgSendResultParam = buildParam(condition);
        return wechatGroupMsgSendResultMapper.updateByParamSelective(record, wechatGroupMsgSendResultParam);
    }

    @Override
    public int upsertSelective(WechatGroupMsgSendResultDO record) {
        record.setId(wechatGroupMsgSendResultIdSequence.nextValue());
        return wechatGroupMsgSendResultMapper.upsertSelective(record);
    }

    @AteyeInvoker(description = "删除群发成员执行结果", paraDesc = "msgId")
    public int deleteByMsgId(String msgId) {
        WechatGroupMsgSendResultParam wechatGroupMsgSendResultParam = new WechatGroupMsgSendResultParam();
        WechatGroupMsgSendResultParam.Criteria criteria = wechatGroupMsgSendResultParam.createCriteria();
        criteria.andMsgIdEqualTo(msgId);
        return wechatGroupMsgSendResultMapper.deleteByParam(wechatGroupMsgSendResultParam);
    }

    private WechatGroupMsgSendResultParam buildParam(WechatGroupMsgSendResultQuery query) {
        if (!StringUtils.hasText(query.getMsgId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WechatGroupMsgSendResultParam wechatGroupMsgSendResultParam = new WechatGroupMsgSendResultParam();
        WechatGroupMsgSendResultParam.Criteria criteria = wechatGroupMsgSendResultParam.createCriteria();
        if (NumberUtils.biggerThanZero(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (StringUtils.hasText(query.getMsgId())) {
            criteria.andMsgIdEqualTo(query.getMsgId());
        }
        if (StringUtils.hasText(query.getUserId())) {
            criteria.andUserIdEqualTo(query.getUserId());
        }
        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (StringUtils.hasText(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }
        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        if (Objects.nonNull(query.getTargetId())) {
            criteria.andTargetIdEqualTo(query.getTargetId());
        }
        if (Objects.nonNull(query.getTargetType())) {
            criteria.andTargetTypeEqualTo(query.getTargetType());
        }
        return wechatGroupMsgSendResultParam;
    }
}