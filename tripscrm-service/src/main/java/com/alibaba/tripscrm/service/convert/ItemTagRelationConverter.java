package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.ItemTagRelationDO;
import com.alibaba.tripscrm.service.model.vo.tag.ItemTagRelationVO;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Tag相关数据防腐层
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ItemTagRelationConverter {
    /**
     * ItemTagRelationVO 转 ItemTagRelationDTO
     */
    public ItemTagRelationDTO convert2DTO(ItemTagRelationVO itemTagRelationVO) {
        if (Objects.isNull(itemTagRelationVO)) {
            return null;
        }

        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setId(itemTagRelationVO.getId());
        itemTagRelationDTO.setDeleted(itemTagRelationVO.getDeleted());
        itemTagRelationDTO.setGmtCreate(itemTagRelationVO.getGmtCreate());
        itemTagRelationDTO.setGmtModified(itemTagRelationVO.getGmtModified());
        itemTagRelationDTO.setCreatorId(itemTagRelationVO.getCreatorId());
        itemTagRelationDTO.setCreatorName(itemTagRelationVO.getCreatorName());
        itemTagRelationDTO.setTagId(itemTagRelationVO.getTagId());
        itemTagRelationDTO.setItemId(itemTagRelationVO.getItemId());
        itemTagRelationDTO.setItemType(itemTagRelationVO.getItemType());
        itemTagRelationDTO.setSubCode(itemTagRelationVO.getSubCode());
        itemTagRelationDTO.setRelationId(itemTagRelationVO.getRelationId());
        return itemTagRelationDTO;
    }

    /**
     * ItemTagRelationDTO 转 ItemTagRelationVO
     */
    public ItemTagRelationVO convert2VO(ItemTagRelationDTO itemTagRelationDTO) {
        if (Objects.isNull(itemTagRelationDTO)) {
            return null;
        }

        ItemTagRelationVO itemTagRelationVO = new ItemTagRelationVO();
        itemTagRelationVO.setId(itemTagRelationDTO.getId());
        itemTagRelationVO.setDeleted(itemTagRelationDTO.getDeleted());
        itemTagRelationVO.setGmtCreate(itemTagRelationDTO.getGmtCreate());
        itemTagRelationVO.setGmtModified(itemTagRelationDTO.getGmtModified());
        itemTagRelationVO.setCreatorId(itemTagRelationDTO.getCreatorId());
        itemTagRelationVO.setCreatorName(itemTagRelationDTO.getCreatorName());
        itemTagRelationVO.setTagId(itemTagRelationDTO.getTagId());
        itemTagRelationVO.setItemId(itemTagRelationDTO.getItemId());
        itemTagRelationVO.setItemType(itemTagRelationDTO.getItemType());
        itemTagRelationVO.setSubCode(itemTagRelationDTO.getSubCode());
        itemTagRelationVO.setRelationId(itemTagRelationDTO.getRelationId());
        return itemTagRelationVO;
    }

    /**
     * ItemTagRelationDTO 转 ItemTagRelationDO
     */
    public ItemTagRelationDO convert2DO(ItemTagRelationDTO itemTagRelationDTO) {
        if (Objects.isNull(itemTagRelationDTO)) {
            return null;
        }

        ItemTagRelationDO itemTagRelationDO = new ItemTagRelationDO();
        itemTagRelationDO.setId(itemTagRelationDTO.getId());
        itemTagRelationDO.setDeleted(itemTagRelationDTO.getDeleted());
        itemTagRelationDO.setGmtCreate(itemTagRelationDTO.getGmtCreate());
        itemTagRelationDO.setGmtModified(itemTagRelationDTO.getGmtModified());
        itemTagRelationDO.setCreatorId(itemTagRelationDTO.getCreatorId());
        itemTagRelationDO.setCreatorName(itemTagRelationDTO.getCreatorName());
        itemTagRelationDO.setTagId(itemTagRelationDTO.getTagId());
        itemTagRelationDO.setItemId(itemTagRelationDTO.getItemId());
        itemTagRelationDO.setItemType(itemTagRelationDTO.getItemType());
        itemTagRelationDO.setSubCode(itemTagRelationDTO.getSubCode());
        itemTagRelationDO.setRelationId(itemTagRelationDTO.getRelationId());
        itemTagRelationDO.setIndexKey(itemTagRelationDTO.getItemId());
        if (StringUtils.isNotBlank(itemTagRelationDTO.getRelationId())) {
            itemTagRelationDO.setIndexKey(itemTagRelationDTO.getItemId() + "_" + itemTagRelationDTO.getRelationId());
        }

        return itemTagRelationDO;
    }

    /**
     * ItemTagRelationDO 转 ItemTagRelationDTO
     */
    public ItemTagRelationDTO convert2DTO(ItemTagRelationDO itemTagRelationDO) {
        if (Objects.isNull(itemTagRelationDO)) {
            return null;
        }

        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setId(itemTagRelationDO.getId());
        itemTagRelationDTO.setDeleted(itemTagRelationDO.getDeleted());
        itemTagRelationDTO.setGmtCreate(itemTagRelationDO.getGmtCreate());
        itemTagRelationDTO.setGmtModified(itemTagRelationDO.getGmtModified());
        itemTagRelationDTO.setCreatorId(itemTagRelationDO.getCreatorId());
        itemTagRelationDTO.setCreatorName(itemTagRelationDO.getCreatorName());
        itemTagRelationDTO.setTagId(itemTagRelationDO.getTagId());
        itemTagRelationDTO.setItemId(itemTagRelationDO.getItemId());
        itemTagRelationDTO.setItemType(itemTagRelationDO.getItemType());
        itemTagRelationDTO.setSubCode(itemTagRelationDO.getSubCode());
        itemTagRelationDTO.setRelationId(itemTagRelationDO.getRelationId());
        return itemTagRelationDTO;
    }
}
