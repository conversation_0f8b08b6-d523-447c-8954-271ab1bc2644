package com.alibaba.tripscrm.service.service.impl.task;

import com.alibaba.tripscrm.dal.mapper.tddl.EventSourceMapper;
import com.alibaba.tripscrm.dal.model.domain.data.EventSourceDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.EventSourceQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.EventSourceService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023-12-01 10:45:06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class EventSourceServiceImpl implements EventSourceService {
    private final EventSourceMapper eventSourceMapper;
    private final AccountService accountService;

    @Override
    public Long insert(EventSourceDO record) {
        if (eventSourceMapper.insert(record) < 1) {
            return null;
        }

        return record.getId();
    }

    @Override
    public EventSourceDO selectById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return null;
        }

        return eventSourceMapper.selectById(id);
    }

    @Override
    public PageInfo<EventSourceDO> pageQuery(EventSourceQuery query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("pageQuery参数校验失败");
                return null;
            }

            query.setIsDeleted(Byte.valueOf("0"));
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
            List<EventSourceDO> list = eventSourceMapper.selectByCondition(query);
            // 结果处理
            if (Objects.isNull(list)) {
                return null;
            }

            return new PageInfo<>(list);
        } catch (Exception e) {
            PlatformLogUtil.logException("pageQuery执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public List<EventSourceDO> selectByCondition(EventSourceQuery query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("selectByCondition参数校验失败");
                return new ArrayList<>();
            }

            query.setIsDeleted(Byte.valueOf("0"));
            List<EventSourceDO> list = eventSourceMapper.selectByCondition(query);
            return Optional.ofNullable(list).orElse(new ArrayList<>());
        } catch (Exception e) {
            PlatformLogUtil.logException("selectByCondition执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return new ArrayList<>();
        }
    }

    @Override
    public List<EventSourceDO> selectAll() {
        List<EventSourceDO> res = new ArrayList<>();
        for (long minId = 0; ; ) {
            List<EventSourceDO> selectResult = eventSourceMapper.selectByMinIdLimit(minId, 300);
            if (CollectionUtils.isEmpty(selectResult)) {
                break;
            }

            minId = selectResult.stream().map(EventSourceDO::getId).max(Long::compareTo).orElse(0L);
            res.addAll(selectResult);
        }

        return res;
    }

    @Override
    public Integer updateById(EventSourceDO record) {
        EventSourceDO eventSourceDO = eventSourceMapper.selectById(record.getId());
        if (Objects.isNull(eventSourceDO)) {
            return 0;
        }

        if (!Objects.equals((byte) 0, eventSourceDO.getStatus())) {
            throw new TripscrmException("只能修改编辑中的事件源");
        }

        return eventSourceMapper.updateById(record);
    }

    @Override
    public Integer online(Long id) {
        EventSourceDO eventSourceDO = new EventSourceDO();
        eventSourceDO.setId(id);
        eventSourceDO.setLastOperatorId(accountService.getUserInWebThread().getUserId());
        eventSourceDO.setStatus((byte) 1);
        return eventSourceMapper.updateById(eventSourceDO);
    }

    @Override
    public Integer offline(Long id) {
        EventSourceDO eventSourceDO = new EventSourceDO();
        eventSourceDO.setId(id);
        eventSourceDO.setLastOperatorId(accountService.getUserInWebThread().getUserId());
        eventSourceDO.setStatus((byte) 0);
        return eventSourceMapper.updateById(eventSourceDO);
    }
}
