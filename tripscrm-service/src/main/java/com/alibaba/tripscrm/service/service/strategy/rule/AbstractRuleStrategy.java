package com.alibaba.tripscrm.service.service.strategy.rule;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.RuleExecuteContext;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2024/6/6 20:15
 */
@Slf4j
@Component
public abstract class AbstractRuleStrategy implements RuleStrategy {
    public static final Map<RuleTypeEnum, AbstractRuleStrategy> RULE_STRATEGY_MAP = new ConcurrentHashMap<>();

    @Resource
    private RuleService ruleService;

    /**
     * 注册
     */
    public static void registry(RuleTypeEnum ruleTypeEnum, AbstractRuleStrategy ruleStrategy) {
        RULE_STRATEGY_MAP.put(ruleTypeEnum, ruleStrategy);
    }

    public static AbstractRuleStrategy getStrategyByType(Byte ruleType) {
        RuleTypeEnum ruleTypeEnum = RuleTypeEnum.getByCode(ruleType.intValue());
        return getStrategyByType(ruleTypeEnum);
    }

    public static AbstractRuleStrategy getStrategyByType(RuleTypeEnum ruleTypeEnum) {
        if (Objects.isNull(ruleTypeEnum)) {
            return null;
        }

        return RULE_STRATEGY_MAP.get(ruleTypeEnum);
    }

    /**
     * 执行规则
     */
    @Override
    public TripSCRMResult<Void> run(Long id, RuleExecuteContext context) {
        RuleDO ruleDO = ruleService.selectById(id);
        if (Objects.isNull(ruleDO)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.QUERY_RULE_INFO_FAIL);
        }

        AbstractRuleStrategy strategy = getStrategyByType(ruleDO.getType());
        if (Objects.isNull(strategy)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NOT_SUPPORT_RULE_TYPE);
        }

        return strategy.run(ruleDO, context);
    }

    @PostConstruct
    public void init() {
        registry(getRuleType(), this);
    }
}
