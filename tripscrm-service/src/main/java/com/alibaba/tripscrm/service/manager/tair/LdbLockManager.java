package com.alibaba.tripscrm.service.manager.tair;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 使用LDB实现的一个分布式锁工具类
 *
 * <AUTHOR>
 * @date 2023-09-24 21:40:52
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class LdbLockManager {
    private static final int DEFAULT_LOCK_EXPIRE_TIME = 10;

    private final LdbTairManager ldbTairManager;

    /**
     * 加分布式锁，默认5秒钟过期
     *
     * @param tairKey 分布式锁对应的TairKey
     * @return 是否加锁成功
     */
    public boolean lock(String tairKey) {
        return this.lock(tairKey, DEFAULT_LOCK_EXPIRE_TIME);
    }

    /**
     * 加分布式锁
     *
     * @param tairKey    分布式锁对应的TairKey
     * @param expireTime 超时时间（秒）
     * @return 是否加锁成功
     */
    public boolean lock(String tairKey, int expireTime) {
        return this.ldbTairManager.incr(tairKey, 1, 0, expireTime) == 1;
    }

    /**
     * 解除分布式锁数据
     *
     * @param tairKey
     * @return
     */
    public boolean unlock(String tairKey) {
        return this.ldbTairManager.delete(tairKey);
    }

    /**
     * 检查当前Tair Key是否已经加锁
     *
     * @param tairKey 分布式锁对应的Tair Key
     * @return 是否已被加锁
     */
    public boolean checkLocked(String tairKey) {
        return this.ldbTairManager.get(tairKey) != null;
    }

    /**
     * 分布式加锁，包含重试逻辑，参数传入方法
     *
     * @param lockKey        锁key
     * @param lockTimeout    锁超时时间
     * @param retry          是否重试
     * @param lockedRunnable 执行方法
     * @return 是否执行成功
     * @throws Exception Exception
     */
    public Boolean lockWithRunnable(String lockKey, int lockTimeout, Boolean retry, LockedRunnable lockedRunnable) throws Exception {
        // 获取锁
        boolean lockAcquired = this.ldbTairManager.incr(lockKey, 1, 0, lockTimeout) == 1;
        if (lockAcquired) {
            try {
                PlatformLogUtil.logFail("分布式加锁，上锁成功", LogListUtil.newArrayList(lockKey));
                lockedRunnable.run();
            } finally {
                PlatformLogUtil.logFail("分布式加锁，锁释放", LogListUtil.newArrayList(lockKey));
                this.ldbTairManager.delete(lockKey);
            }
            return true;
        }

        if (retry) {
            // 未得到锁，等待
            PlatformLogUtil.logFail("分布式加锁，上锁失败，等300ms重试", LogListUtil.newArrayList(lockKey));
            Thread.sleep(300);
            // 继续获取锁
            return lockWithRunnable(lockKey, lockTimeout, true, lockedRunnable);
        }

        PlatformLogUtil.logFail("分布式加锁，上锁失败", LogListUtil.newArrayList(lockKey));
        return false;
    }

    /**
     * 可重入锁，分布式加锁，包含重试逻辑，参数传入方法
     *
     * @param lockKey        锁key
     * @param lockTimeout    锁超时时间
     * @param lockedRunnable 执行方法
     * @return 是否执行成功
     * @throws Exception Exception
     */
    public void reentrantLockWithRunnable(Boolean reentrantLock, String lockKey, int lockTimeout, LockedRunnable lockedRunnable) throws Exception {
        if (reentrantLock) {
            // 当要重入锁时，则+1
            ldbTairManager.incr(lockKey, 1, 0, lockTimeout);
        }
        // 获取锁
        int inx = ldbTairManager.decr(lockKey, 1, 1, 1, lockTimeout);
        if (inx < 0) {
            // 当获取失败时，会减到负，负时要归正
            ldbTairManager.incr(lockKey, 1, 0, lockTimeout);
        }
        boolean lockAcquired = inx == 0;
        if (lockAcquired) {
            PlatformLogUtil.logFail("可重入锁，上锁成功", LogListUtil.newArrayList(lockKey, inx));
            lockedRunnable.run();
            return;
        }
        PlatformLogUtil.logFail("可重入锁，上锁失败", LogListUtil.newArrayList(lockKey, inx));
    }

    /**
     * 锁内方法
     */
    public interface LockedRunnable {
        void run() throws Exception;
    }
}

