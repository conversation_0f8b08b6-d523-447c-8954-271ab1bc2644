package com.alibaba.tripscrm.service.service.impl.material;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fliggypoi.client.domain.FliggyPoi;
import com.alibaba.fliggypoi.client.domain.TripPoiResult;
import com.alibaba.fliggypoi.client.service.TripPoiReadService;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.MaterailInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.WxCommunityContentDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialCheckDeleteEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSearchSourceEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.WxContentOpenSearchManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.query.WxCommunityContentQuery;
import com.alibaba.tripscrm.service.model.domain.request.MaterialToMessageRequest;
import com.alibaba.tripscrm.service.model.domain.request.SendMsgToCustomerRequest;
import com.alibaba.tripscrm.service.model.dto.ItemInfoDTO;
import com.alibaba.tripscrm.service.model.dto.material.FusionChatMaterialDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSelfTestDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.material.CheckDeleteVO;
import com.alibaba.tripscrm.service.model.vo.material.ItemVO;
import com.alibaba.tripscrm.service.model.vo.material.MaterialInfoVO;
import com.alibaba.tripscrm.service.model.vo.material.MaterialMessageVO;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskMaterialVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.fusionchat.FusionChatService;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.task.base.AbTestBucketService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.api.service.ForwardMessageService;
import com.alibaba.tripzoo.proxy.enums.ForwardMessageTargetTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.request.MergeMessageRelayRequest;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.travelsummary.domain.SummarySearchItem;
import com.fliggy.travelsummary.domain.query.TravelSummaryQuery;
import com.fliggy.travelsummary.service.SummarySearchService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.item.util.CollectionUtils;
import com.taobao.trip.hpc.client.domain.dataobject.SHotelDO;
import com.taobao.trip.hpc.client.result.HpcResult;
import com.taobao.trip.hpc.client.service.SHotelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 素材服务
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MaterialServiceImpl implements MaterialService {
    private final MaterailInfoMapper materailInfoMapper;
    private final TaskService taskService;
    private final AbTestBucketService abTestBucketService;
    private final SummarySearchService summarySearchService;
    private final SHotelService sHotelService;
    private final TripPoiReadService tripPoiReadService;
    private final com.aliyun.fc.open.api.hsf.HsfGatewayService gatewayService;
    private final EnterpriseWechatManager enterpriseWechatManager;
    private final LdbTairManager ldbTairManager;
    private final UicUtils uicUtils;
    private final MaterialTransferService materialTransferService;
    private final WechatCustomerService wechatCustomerService;
    private final AccountService accountService;
    private final WxContentOpenSearchManager wxContentOpenSearchManager;
    private final FusionChatService fusionChatService;
    private final ForwardMessageService forwardMessageService;
    private final IsvRouteService isvRouteService;

    private final Map<ItemTypeEnum, Function<ItemInfoDTO, ItemVO>> FUNCTION_MAP = new ConcurrentHashMap<>();

    public static final String MATERIAL_PRE = "MATERIAL_PRE";

    @PostConstruct
    public void init() {
        FUNCTION_MAP.put(ItemTypeEnum.GOODS, this::getGoods);
        FUNCTION_MAP.put(ItemTypeEnum.HOTEL, this::getHotel);
        FUNCTION_MAP.put(ItemTypeEnum.PAGE_MAKER, this::getPageMaker);
        FUNCTION_MAP.put(ItemTypeEnum.POI, this::getPOI);
        FUNCTION_MAP.put(ItemTypeEnum.COMMUNITY_CONTENT, this::getCommunityContent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long upsert(MaterailInfoDO materailInfoDO) {
        try {
            if (Objects.isNull(materailInfoDO)) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
            }

            checkMaterialValid(materailInfoDO);
            if (!NumberUtils.validLong(materailInfoDO.getId())) {
                // id无效，保存素材
                materailInfoDO.setDeleted((byte) WxConstants.not_deleted);
                return (long) materailInfoMapper.insert(materailInfoDO);
            }

            return (long) materailInfoMapper.updateByPrimaryKey(materailInfoDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(materailInfoDO));
            return null;
        }
    }

    @AteyeInvoker(description = "创建或修改素材")
    public Long ateyeUpsert(Long id, String name, String creator, Long materialTemplateId, String content, Byte deleted, Long spaceId, Byte used, Long sceneId) {
        MaterailInfoDO materailInfoDO = new MaterailInfoDO();
        materailInfoDO.setId(id);
        materailInfoDO.setName(name);
        materailInfoDO.setCreator(creator);
        materailInfoDO.setMaterialTemplateId(materialTemplateId);
        materailInfoDO.setContent(content);
        materailInfoDO.setDeleted(deleted);
        materailInfoDO.setSpaceId(spaceId);
        materailInfoDO.setUsed(used);
        materailInfoDO.setSceneId(sceneId);
        return upsert(materailInfoDO);
    }

    public boolean isWechatCashRedPacketMaterial(MaterailInfoDO materailInfoDO) {
        List<MaterialSupplyDTO> supplyDTOList = materialTransferService.extractMaterialSupply(materailInfoDO.getContent());
        return supplyDTOList.stream().anyMatch(
                supplyDTO -> Objects.equals(MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY.getType(), supplyDTO.getSupplyType())
        );
    }

    public void checkMaterialValid(MaterailInfoDO materailInfoDO) {
        if (!isWechatCashRedPacketMaterial(materailInfoDO)) {
            return;
        }

        // 现金红包只能在特定空间使用
        if (!SwitchConfig.wechatCashRedPacketSpaceIdList.contains(materailInfoDO.getSpaceId())) {
            return;
        }

        // 现金红包素材编辑时，不能修改供给
        if (!NumberUtils.validLong(materailInfoDO.getId())) {
            return;
        }

        MaterailInfoDO oldMaterialInfoDO = queryById(materailInfoDO.getId());
        List<MaterialSupplyDTO> supplyDTOList = materialTransferService.extractMaterialSupply(materailInfoDO.getContent());
        List<MaterialSupplyDTO> oldSupplyDTOList = materialTransferService.extractMaterialSupply(oldMaterialInfoDO.getContent());

        Set<String> supplyIdSet = supplyDTOList.stream()
                .filter(supplyDTO -> Objects.equals(MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY.getType(), supplyDTO.getSupplyType()))
                .map(MaterialSupplyDTO::getSupplyId)
                .collect(Collectors.toSet());

        Set<String> oldSupplyIdSet = oldSupplyDTOList.stream()
                .filter(supplyDTO -> Objects.equals(MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY.getType(), supplyDTO.getSupplyType()))
                .map(MaterialSupplyDTO::getSupplyId)
                .collect(Collectors.toSet());
        if (oldSupplyIdSet.containsAll(supplyIdSet) && supplyIdSet.containsAll(oldSupplyIdSet)) {
            return;
        }

        throw new TripscrmException(TripSCRMErrorCode.RED_PACKET_SUPPLY_CANNOT_BE_MODIFY);
    }

    @AteyeInvoker(description = "清理素材场景")
    public Boolean cleanMaterial(Long id) {
        MaterailInfoDO materailInfoDO = queryById(id);
        materailInfoDO.setSceneId(null);
        return materailInfoMapper.cleanSceneByKey(materailInfoDO) > 0;
    }

    @Override
    public PageInfo<MaterailInfoDO> list(MaterialInfoVO queryVO) {
        try {
            if (Objects.isNull(queryVO)) {
                PlatformLogUtil.logFail("param check fail");
                return null;
            }
            MaterialInfoQuery query = new MaterialInfoQuery();
            BeanUtils.copyProperties(queryVO, query);
            if (queryVO.getStartTime() != null) {
                query.setStartTime(new Date(queryVO.getStartTime()));
            }
            if (queryVO.getEndTime() != null) {
                query.setEndTime(new Date(queryVO.getEndTime()));
            }
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
            List<MaterailInfoDO> list = materailInfoMapper.selectByParam(query);
            if (MaterialSearchSourceEnum.FUSION_CHAT.getType().equals(queryVO.getSearchSource())) {
                list = list.stream().filter(t -> SwitchConfig.fusionChatMaterialWhiteList.contains(t.getMaterialTemplateId())).collect(Collectors.toList());
            }
            // 结果处理
            if (Objects.isNull(list)) {
                return null;
            }
            return new PageInfo<>(list);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(queryVO));
            return null;
        }
    }

    @Override
    public List<MaterailInfoDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return materailInfoMapper.selectByIds(ids);
    }

    @Override
    public MaterailInfoDO queryById(Long id) {
        try {
            if (!NumberUtils.validLong(id)) {
                PlatformLogUtil.logFail("param check fail", LogListUtil.newArrayList(id));
                return null;
            }
            return materailInfoMapper.selectByPrimaryKey(id);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(id));
            return null;
        }
    }

    @Override
    public MaterailInfoDO queryByIdAndSpaceId(Long id, Long spaceId) {
        try {
            MaterailInfoDO materailInfoDO = queryById(id);
            if (Objects.isNull(materailInfoDO) || !Objects.equals(materailInfoDO.getSpaceId(), spaceId)) {
                PlatformLogUtil.logFail("query fail");
                return null;
            }

            return materailInfoDO;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(id));
            return null;
        }
    }

    @Override
    public boolean checkDuplicateName(Long spaceId, String name, Long id) {
        try {
            if (!StringUtils.hasText(name)) {
                PlatformLogUtil.logFail("param check fail");
                return false;
            }
            MaterailInfoDO taskInfoDO = materailInfoMapper.selectByNameAndSpaceIdAndPrimaryKey(name, spaceId, id);

            return Objects.nonNull(taskInfoDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(spaceId, name, id));
            return false;
        }
    }

    @Override
    public List<MaterailInfoDO> listBySpaceIdAndType(Long spaceId, String type) {
        try {
            if (spaceId == null || type == null) {
                return new ArrayList<>();
            }
            
            List<MaterailInfoDO> list = materailInfoMapper.selectBySpaceIdAndType(spaceId, type);
            return list;
        } catch (Exception e) {
            log.error("根据spaceId和type查询素材列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<TaskInfoDO> getTaskListByMaterial(Long materialId, List<TaskStatusEnum> statusList) {
        if (!NumberUtils.validLong(materialId)) {
            return new ArrayList<>();
        }

        TaskQuery query = new TaskQuery();
        query.setMaterialId(materialId);
        query.setDeleted(0);
        query.setStatusList(Optional.ofNullable(statusList).orElse(new ArrayList<>()).stream().map(TaskStatusEnum::getStatus).collect(Collectors.toList()));
        List<TaskInfoDO> taskList = taskService.query(query);
        // 过滤掉没有开启AB实验的数据
        List<TaskInfoDO> result = taskList.stream().filter(x -> x.getAbType() == 0).collect(Collectors.toList());

        // 开启AB实验的任务
        List<AbTestBucketVO> abTestBucketVOList = abTestBucketService.listByMaterialId(materialId);
        List<Long> taskIdList = abTestBucketVOList.stream().map(AbTestBucketVO::getTaskId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskIdList)) {
            return result;
        }

        query = new TaskQuery();
        query.setDeleted(0);
        query.setStatusList(statusList.stream().map(TaskStatusEnum::getStatus).collect(Collectors.toList()));
        query.setIdList(taskIdList);
        List<TaskInfoDO> abTestTaskList = taskService.query(query);
        abTestTaskList.forEach(x -> {
            if (x.getAbType() == 0) {
                return;
            }
            result.add(x);
        });

        return result;
    }

    @Override
    @Cacheable(key = "'queryItemById_' + #itemInfoDTO.itemId + '_' + #itemInfoDTO.itemType", value = "sixHoursAnd512MaximumSizeCacheManager", unless = "#result == null")
    public ItemVO queryItemById(ItemInfoDTO itemInfoDTO) {
        ItemTypeEnum itemTypeEnum = ItemTypeEnum.findByType(itemInfoDTO.getItemType());
        if (Objects.isNull(itemTypeEnum)) {
            return null;
        }
        return FUNCTION_MAP.get(itemTypeEnum).apply(itemInfoDTO);
    }

    @Override
    public Boolean selfTest(MaterialSelfTestDTO materialSelfTestDTO) {
        // 参数校验
        if (materialSelfTestDTO == null || !StringUtils.hasText(materialSelfTestDTO.getContent())
                || !StringUtils.hasText(materialSelfTestDTO.getUid())) {
            throw new TripscrmException("自测素材必须传入素材和测试人员的淘系ID");
        }
        String content = Optional.ofNullable(materialSelfTestDTO.getContent()).orElse("{}");
        if (content.contains("momentContent") && !content.contains("messageParagraphList")) {
            throw new TripscrmException("朋友圈素材的视频/图片暂不支持自测");
        }
        String unionId = uicUtils.getUnionIdByUid(materialSelfTestDTO.getUid());
        if (!StringUtils.hasText(unionId)) {
            throw new TripscrmException("自测淘系ID对应的账号需要在飞猪微信小程序登陆过");
        }
        String externalUserId = wechatCustomerService.getExternalUserIdByUnionId(unionId);
        if (!StringUtils.hasText(externalUserId)) {
            throw new TripscrmException("请确保自测ID对应的微信账号添加过飞猪企业微信成员");
        }
        // 发送人选取
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setExternalUserId(externalUserId);
        isvRouteContext.setRiskActionEnum(RiskActionEnum.CUSTOMER_SEND_MESSAGE);
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        TripSCRMResult<WechatUserDTO> wechatUserResult = isvRouteService.matchWechatUser(isvRouteContext);
        if (Objects.isNull(wechatUserResult) || !wechatUserResult.isSuccess() || Objects.isNull(wechatUserResult.getData())) {
            throw new TripscrmException("请确保自测ID对应的微信账号存在可用的在线企微成员");
        }
        // 素材处理
        SendMsgToCustomerRequest sendMsgToCustomerRequest = getSendMsgToCustomerRequest(materialSelfTestDTO, unionId, wechatUserResult.getData().getUserId());
        // 消息发送
        TripSCRMResult<String> result = enterpriseWechatManager.asyncSendMessageToCustomer(sendMsgToCustomerRequest);
        if (!result.isSuccess()) {
            throw new TripscrmException(result.getMsg());
        }
        return true;
    }

    @NotNull
    private SendMsgToCustomerRequest getSendMsgToCustomerRequest(MaterialSelfTestDTO materialSelfTestDTO, String unionId, String sendUserId) {
        SendMsgToCustomerRequest sendMsgToCustomerRequest = new SendMsgToCustomerRequest();
        sendMsgToCustomerRequest.setUnionId(unionId);
        sendMsgToCustomerRequest.setUserId(sendUserId);
        MaterailInfoDO materailInfoDO = new MaterailInfoDO();
        materailInfoDO.setMaterialTemplateId(16L);
        materailInfoDO.setType(TaskType.ROBOT_CHAT_MESSAGE.getCode());
        materailInfoDO.setContent(materialSelfTestDTO.getContent());
        materailInfoDO.setSceneId(0L);
        if (materialSelfTestDTO.getId() != null && NumberUtils.validLong(materialSelfTestDTO.getId())) {
            MaterailInfoDO existMaterial = queryById(materialSelfTestDTO.getId());
            materailInfoDO.setSceneId(existMaterial.getSceneId());
            materailInfoDO.setContent(existMaterial.getContent());
            materailInfoDO.setId(materialSelfTestDTO.getId());
        }
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.SILIAO_RW);
        materialTrackRelationDTO.setSendUnionId(unionId);
        materialTrackRelationDTO.setTaskId(-1L);
        materialTrackRelationDTO.setTaskInsId(-1L);
        materialTrackRelationDTO.setSendUnionId(unionId);
        materialTrackRelationDTO.setMaterialId(materialSelfTestDTO.getId());
        materialTrackRelationDTO.setWxUserId(sendUserId);
        sendMsgToCustomerRequest.setMaterialContentConvertContext(new MaterialContentConvertContext());
        sendMsgToCustomerRequest.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        sendMsgToCustomerRequest.setMaterailInfoDO(materailInfoDO);
        sendMsgToCustomerRequest.setTaskType(TaskType.ROBOT_CHAT_MESSAGE);
        sendMsgToCustomerRequest.setIsTest(true);
        return sendMsgToCustomerRequest;
    }

    @Override
    public CheckDeleteVO checkDelete(Long id) {
        CheckDeleteVO checkDeleteVO = new CheckDeleteVO();
        Object exit = ldbTairManager.get(MATERIAL_PRE.concat(String.valueOf(id)));
        if (Objects.equals(1, exit)) {
            checkDeleteVO.setCanDelete(false);
            checkDeleteVO.setCode(MaterialCheckDeleteEnum.SEND_NOT_DELETE.getType());
            checkDeleteVO.setMessage(MaterialCheckDeleteEnum.SEND_NOT_DELETE.getDes());
            return checkDeleteVO;
        }
        MaterailInfoDO materailInfoDO = materailInfoMapper.selectByPrimaryKey(id);
        String operator = accountService.getUserNameAndEmpId();
        if (materailInfoDO.getUsed() != null && Objects.equals((byte) 1, materailInfoDO.getUsed())) {
            ldbTairManager.put(MATERIAL_PRE.concat(String.valueOf(id)), 1, 3600);
            checkDeleteVO.setCanDelete(false);
            checkDeleteVO.setCode(MaterialCheckDeleteEnum.SEND_NOT_DELETE.getType());
            checkDeleteVO.setMessage(MaterialCheckDeleteEnum.SEND_NOT_DELETE.getDes());
            return checkDeleteVO;
        }
        if (!materailInfoDO.getCreator().equals(operator)) {
            checkDeleteVO.setCanDelete(false);
            checkDeleteVO.setCode(MaterialCheckDeleteEnum.NOT_CREATE_DELETE.getType());
            checkDeleteVO.setMessage(MaterialCheckDeleteEnum.NOT_CREATE_DELETE.getDes());
            return checkDeleteVO;
        }
        checkDeleteVO.setCanDelete(true);
        checkDeleteVO.setCode(MaterialCheckDeleteEnum.CAN_DELETE.getType());
        checkDeleteVO.setMessage(MaterialCheckDeleteEnum.CAN_DELETE.getDes());
        return checkDeleteVO;
    }

    @Override
    public List<MaterialMessageVO> fusionChatMaterial(FusionChatMaterialDTO fusionChatMaterialDTO) {
        // 素材
        List<MessageBO> messageBOList;
        MaterailInfoDO materailInfoDO = this.queryById(fusionChatMaterialDTO.getMaterialId());
        User user = accountService.getUserInWebThread();
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setTaskId(-1L);
        materialTrackRelationDTO.setTaskInsId(-1L);
        materialTrackRelationDTO.setAbBucketId("-1");
        materialTrackRelationDTO.setMaterialId(fusionChatMaterialDTO.getMaterialId());
        materialTrackRelationDTO.setWxUserId(fusionChatMaterialDTO.getUserId());
        materialTrackRelationDTO.setOperatorId(user.getUserId());

        switch (ChatTypeEnum.parse(fusionChatMaterialDTO.getChatType())) {
            case GROUP:
                materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.JUHE_QUNLIAO);
                materialTrackRelationDTO.setSendChatId(fusionChatMaterialDTO.getChatId());
                messageBOList = materialTransferService.buildMessages(materailInfoDO, materialTrackRelationDTO, new MaterialContentConvertContext(), TaskType.GROUP_CHAT_MESSAGE.getCode());
                break;
            case SINGLE_FOR_CUSTOMER:
                List<WechatCustomerVO> wechatCustomerVOList = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(fusionChatMaterialDTO.getChatId()));
                materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.JUHE_SILIAO);
                materialTrackRelationDTO.setSendUnionId(wechatCustomerVOList.get(0).getUnionId());
                messageBOList = materialTransferService.buildMessages(materailInfoDO, materialTrackRelationDTO, new MaterialContentConvertContext(), TaskType.ROBOT_CHAT_MESSAGE.getCode());
                break;
            default:
                messageBOList = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(messageBOList)) {
            return new ArrayList<>();
        }
        List<MaterialMessageVO> materialMessageVOList = new ArrayList<>();
        for (MessageBO messageBO : messageBOList) {
            MaterialMessageVO messageVO = getMaterialMessageVO(messageBO);
            materialMessageVOList.add(messageVO);
        }
        return materialMessageVOList;
    }

    @Override
    public List<FusionChatMessageBody> fusionChatContent(FusionChatMaterialDTO fusionChatMaterialDTO) {
        // 请求体拼接
        MaterialToMessageRequest request = new MaterialToMessageRequest();
        request.setChatType(fusionChatMaterialDTO.getChatType());
        request.setOperatorId(Optional.ofNullable(accountService.getUserInWebThread()).orElse(new User()).getUserId());
        request.setSendUserId(fusionChatMaterialDTO.getUserId());
        request.setTargetId(fusionChatMaterialDTO.getChatId());
        // 社区数据查询
        WxCommunityContentQuery query = new WxCommunityContentQuery();
        query.setCppContentId(fusionChatMaterialDTO.getCppContentId());
        WxCommunityContentDTO wxCommunityContentDTO = wxContentOpenSearchManager.query(query);
        Assert.notNull(wxCommunityContentDTO, "发送素材已失效");
        // 素材数据校验
        MaterialInfoQuery materialInfoQuery = new MaterialInfoQuery();
        materialInfoQuery.setName(String.format("社区内容-%s", wxCommunityContentDTO.getCppContentId()));
        List<MaterailInfoDO> materialInfoList = materailInfoMapper.selectByParam(materialInfoQuery);
        if (CollectionUtils.isNotEmpty(materialInfoList)) {
            request.setMaterialId(materialInfoList.get(0).getId());
            return fusionChatService.toMaterialMessage(request);
        }
        // 创建素材
        TaskMaterialVO taskMaterialVO = new TaskMaterialVO();
        Long spaceId = SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId();
        taskMaterialVO.setSpaceId(spaceId);
        taskMaterialVO.setCreator(accountService.getUserNameAndEmpId());
        taskMaterialVO.setName(String.format("社区内容-%s", wxCommunityContentDTO.getCppContentId()));
        String content = String.format(SwitchConfig.CONTENT_SEND_TEMPLATE, fusionChatMaterialDTO.getCppContentId()
                , wxCommunityContentDTO.getTitle(), fusionChatMaterialDTO.getCppContentId(), wxCommunityContentDTO.getTitle()
                , CollectionUtils.isNotEmpty(wxCommunityContentDTO.getPics()) ? wxCommunityContentDTO.getPics().get(0).getUrl() : "");
        taskMaterialVO.setContent(content);
        taskMaterialVO.setType("robotChatMessage");
        taskMaterialVO.setMaterialTemplateId(18L);

        MaterailInfoDO materailInfoDO = new MaterailInfoDO();
        BeanUtils.copyProperties(taskMaterialVO, materailInfoDO);

        request.setMaterialId(upsert(materailInfoDO));
        return fusionChatService.toMaterialMessage(request);
    }

    @Override
    public List<MessageBO> getMaterialMessageList(Long materialId, MaterialTrackRelationDTO materialTrackRelationDTO, Map<String, Object> extraInfo) {
        if (!NumberUtils.validLong(materialId)) {
            return Lists.newArrayList();
        }
        // 原始素材
        MaterailInfoDO materailInfoDO = queryById(materialId);
        if (materailInfoDO == null) {
            return Lists.newArrayList();
        }
        // 参数替换
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        if (MapUtils.isNotEmpty(extraInfo)) {
            materialContentConvertContext.setWechatUserId(materialTrackRelationDTO.getWxUserId());
            materialContentConvertContext.setExtraInfo(extraInfo);
            materialContentConvertContext.setOriginContent(materailInfoDO.getContent());
        }
        // 素材转化
        return materialTransferService.buildMessages(materailInfoDO, materialTrackRelationDTO, materialContentConvertContext, null);
    }

    @NotNull
    private static MaterialMessageVO getMaterialMessageVO(MessageBO messageBO) {
        MaterialMessageVO messageVO = new MaterialMessageVO();
        messageVO.setMsgType(messageBO.getMsgType().getDesc());
        if (MessageTypeEnum.MINI_PROGRAM.equals(messageBO.getMsgType())) {
            messageVO.setMsgType(FusionChatMessageTypeEnum.TEXT.getValue());
            messageVO.setContent("[小程序消息:" + messageBO.getTitle() + " 请到客户端查看]");
        } else if (MessageTypeEnum.TEXT.equals(messageBO.getMsgType())) {
            messageVO.setMsgType(FusionChatMessageTypeEnum.TEXT.getValue());
            messageVO.setContent(messageBO.getMsgContent());
        }
        return messageVO;
    }

    @Override
    public Integer delete(Long id, Long spaceId) {
        try {
            if (!NumberUtils.validLong(id)) {
                PlatformLogUtil.logFail("param check fail");
                return null;
            }
            MaterailInfoDO materailInfoDO = materailInfoMapper.selectByPrimaryKey(id);
            if (Objects.isNull(materailInfoDO) || !Objects.equals(materailInfoDO.getSpaceId(), spaceId)) {
                PlatformLogUtil.logFail("materialInfoDO not found");
                return null;
            }

            return materailInfoMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(id, spaceId));
            return null;
        }
    }

    @AteyeInvoker(description = "删除指定素材")
    public Integer delete(Long id) {
        return materailInfoMapper.deleteByPrimaryKey(id);
    }

    @AteyeInvoker(description = "素材业务空间迁移", paraDesc = "oldSpaceId&newSpaceId")
    public int batchUpdateSpace(Long oldSpaceId, Long newSpaceId) {
        int res = 0;
        MaterialInfoQuery query = new MaterialInfoQuery();
        query.setSpaceId(oldSpaceId);
        List<MaterailInfoDO> oldDataList = materailInfoMapper.selectByParam(query);
        for (MaterailInfoDO data : oldDataList) {
            MaterailInfoDO materailInfoDO = new MaterailInfoDO();
            materailInfoDO.setId(data.getId());
            materailInfoDO.setSpaceId(newSpaceId);
            res += materailInfoMapper.updateByPrimaryKey(materailInfoDO);
        }

        return res;
    }


    @AteyeInvoker(description = "素材业务空间修改", paraDesc = "materialId&spaceId")
    public int updateSpace(Long materialId, Long spaceId) {
        int res = 0;
        MaterailInfoDO materailInfoDO = new MaterailInfoDO();
        materailInfoDO.setId(materialId);
        materailInfoDO.setSpaceId(spaceId);
        res += materailInfoMapper.updateByPrimaryKey(materailInfoDO);

        return res;
    }

    private ItemVO getGoods(ItemInfoDTO itemInfoDTO) {
        ItemVO itemVO = new ItemVO();
        TravelSummaryQuery query = new TravelSummaryQuery();
        query.setIds(Lists.newArrayList(itemInfoDTO.getItemId()));
        query.setAppName("tripscrm");
        Map<String, SummarySearchItem> result = summarySearchService.searchItemMap(query, null);
        if (!result.isEmpty()) {
            for (SummarySearchItem item : result.values()) {
                itemVO.setId(String.valueOf(item.getItemId()));
                itemVO.setName(item.getTitle());
                break;
            }
        }
        return itemVO;
    }

    private ItemVO getHotel(ItemInfoDTO itemInfoDTO) {
        ItemVO itemVO = new ItemVO();
        try {
            HpcResult<SHotelDO> result = sHotelService.getSHotelByShid(Long.parseLong(itemInfoDTO.getItemId()));
            if (result != null && result.isSuccess() && result.getModule() != null) {
                SHotelDO sHotelDO = result.getModule();
                itemVO.setId(String.valueOf(sHotelDO.getShid()));
                itemVO.setName(sHotelDO.getName());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return itemVO;
    }

    @AteyeInvoker(description = "查询页匠页信息（新）")
    public ItemVO getPageMakerNew(Long id) {
        ItemVO itemVO = new ItemVO();
        JSONObject json = new JSONObject();
        JSONObject userInfo = new JSONObject();
        userInfo.put("name", "云芃");
        userInfo.put("workid", "140036");
        userInfo.put("bucid", "939436");
        json.put("userInfo", userInfo);
        json.put("api", "page.queryByIds");
        JSONObject data = new JSONObject();
        data.put("idList", Lists.newArrayList(id));
        json.put("data", data);
        Object result = gatewayService.invoke("203285600190913223", "fl-xuanji", "hsf", json);
        if (Objects.isNull(result)) {
            return itemVO;
        }

        JSONObject jsonResult = JSONObject.parseObject(JSON.toJSONString(result));
        Boolean success = jsonResult.getBoolean("success");
        if (!Optional.ofNullable(success).orElse(false)) {
            return itemVO;
        }

        JSONObject pageMap = jsonResult.getJSONObject("value");
        if (CollectionUtils.isEmpty(pageMap) || !pageMap.containsKey(String.valueOf(id))) {
            return itemVO;
        }

        JSONObject pageInfo = pageMap.getJSONObject(String.valueOf(id));
        itemVO.setId(pageInfo.getString("id"));
        itemVO.setName(pageInfo.getString("name"));
        itemVO.setUrl(pageInfo.getString("mobileUrl"));
        return itemVO;
    }

    @AteyeInvoker(description = "查询页匠页信息（旧）")
    private ItemVO getPageMakerOld(Long id) {
        ItemVO itemVO = new ItemVO();
        JSONObject json = new JSONObject();
        JSONObject userInfo = new JSONObject();
        userInfo.put("name", "云芃");
        userInfo.put("workid", "140036");
        userInfo.put("bucid", "939436");
        json.put("userInfo", userInfo);
        json.put("api", "page.queryByIds");
        JSONObject data = new JSONObject();
        data.put("idList", Lists.newArrayList(id));
        json.put("data", data);
        Object result = gatewayService.invoke("282458359526638697", "fl-pageone-new", "hsf", json);
        if (Objects.isNull(result)) {
            return itemVO;
        }

        JSONObject jsonResult = JSONObject.parseObject(JSON.toJSONString(result));
        Boolean success = jsonResult.getBoolean("success");
        if (!Optional.ofNullable(success).orElse(false)) {
            return itemVO;
        }

        JSONObject pageMap = jsonResult.getJSONObject("value");
        if (CollectionUtils.isEmpty(pageMap) || !pageMap.containsKey(String.valueOf(id))) {
            return itemVO;
        }

        JSONObject pageInfo = pageMap.getJSONObject(String.valueOf(id));
        itemVO.setId(pageInfo.getString("id"));
        itemVO.setName(pageInfo.getString("name"));
        itemVO.setUrl(pageInfo.getString("mobileUrl"));
        return itemVO;
    }

    public ItemVO getPageMaker(ItemInfoDTO itemInfoDTO) {
        if (!StringUtils.hasText(itemInfoDTO.getItemId())) {
            return new ItemVO();
        }

        if (itemInfoDTO.getItemId().length() >= 6) {
            return getPageMakerOld(Long.parseLong(itemInfoDTO.getItemId()));
        }

        return getPageMakerNew(Long.parseLong(itemInfoDTO.getItemId()));
    }

    @Override
    public Boolean previewChat(Long materialId, String uid) {
        MaterailInfoDO materailInfoDO = materailInfoMapper.selectByPrimaryKey(materialId);
        if (Objects.isNull(materailInfoDO) || StringUtils.isEmpty(materailInfoDO.getContent())) {
            throw new TripscrmException(TripSCRMErrorCode.MATERIAL_NOT_EXIST);
        }

        JSONObject messageListJson = JSON.parseObject(materailInfoDO.getContent());
        JSONArray messageList = messageListJson.getJSONArray("messageList");
        if (messageList == null || messageList.isEmpty()) {
            throw new TripscrmException(TripSCRMErrorCode.MATERIAL_CONTENT_EMPTY);
        }
        JSONObject messageJson = messageList.getJSONObject(0);
        if (!messageJson.containsKey(TaskConstant.MSG_ID) || org.apache.commons.lang3.StringUtils.isBlank(messageJson.getString(TaskConstant.MSG_ID))) {
            throw new TripscrmException(TripSCRMErrorCode.CHAT_RECORD_MSG_ID_NOT_EXIST);
        }
        String msgId = messageJson.getString(TaskConstant.MSG_ID);

        String unionId = uicUtils.getUnionIdByUid(uid);
        if (!StringUtils.hasText(unionId)) {
            throw new TripscrmException("自测淘系ID对应的账号需要在飞猪微信小程序登陆过");
        }
        String externalUserId = wechatCustomerService.getExternalUserIdByUnionId(unionId);
        if (!StringUtils.hasText(externalUserId)) {
            throw new TripscrmException("请确保自测ID对应的微信账号添加过飞猪企业微信成员");
        }
        MergeMessageRelayRequest mergeMessageRelayRequest = new MergeMessageRelayRequest();
        mergeMessageRelayRequest.setType(ForwardMessageTargetTypeEnum.CUSTOMER);
        mergeMessageRelayRequest.setTargetId(externalUserId);
        mergeMessageRelayRequest.setMsgIdList(Lists.newArrayList(msgId));
        String sendUserId = SwitchConfig.MESSAGE_FORWARD_RECEIVE_GROUP_ID.keySet().stream()
                .findFirst()
                .orElse(null);
        mergeMessageRelayRequest.setUserId(sendUserId);
        ResultDO<String> resultDO = forwardMessageService.mergeMessageRelay(mergeMessageRelayRequest);
        return resultDO.getSuccess();
    }

    private ItemVO getPOI(ItemInfoDTO itemInfoDTO) {
        ItemVO itemVO = new ItemVO();
        try {
            TripPoiResult<FliggyPoi> result = tripPoiReadService.getPoi(Long.parseLong(itemInfoDTO.getItemId()));
            if (result != null && result.isSuccess() && result.getData() != null) {
                FliggyPoi fliggyPoi = result.getData();
                itemVO.setId(String.valueOf(fliggyPoi.getPoiBase().getPoiId()));
                itemVO.setName(fliggyPoi.getPoiBase().getName());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return itemVO;
    }

    private ItemVO getCommunityContent(ItemInfoDTO itemInfoDTO) {
        ItemVO itemVO = new ItemVO();
        try {
            WxCommunityContentQuery query = new WxCommunityContentQuery();
            query.setCppContentId(Long.parseLong(itemInfoDTO.getItemId()));
            WxCommunityContentDTO wxCommunityContentDTO = wxContentOpenSearchManager.query(query);
            if (wxCommunityContentDTO != null) {
                itemVO.setId(itemInfoDTO.getItemId());
                itemVO.setName(wxCommunityContentDTO.getTitle());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return itemVO;
    }

}
