package com.alibaba.tripscrm.service.model.response.crowd;

import lombok.Data;

import java.io.Serializable;

/**
 * 人群打标转换响应
 *
 * <AUTHOR>
 * @date 2024/10/13
 */
@Data
public class CrowdTagConvertResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 任务状态描述
     */
    private String statusDesc;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 预计处理用户数
     */
    private Long totalCount;

    /**
     * 实际转换成功用户数
     */
    private Long convertCount;

    /**
     * 任务描述
     */
    private String description;

    public static CrowdTagConvertResponse success(Long taskId, String taskName, String status, String statusDesc) {
        CrowdTagConvertResponse response = new CrowdTagConvertResponse();
        response.setTaskId(taskId);
        response.setTaskName(taskName);
        response.setStatus(status);
        response.setStatusDesc(statusDesc);
        return response;
    }

    public static CrowdTagConvertResponse fail(String description) {
        CrowdTagConvertResponse response = new CrowdTagConvertResponse();
        response.setDescription(description);
        return response;
    }
}