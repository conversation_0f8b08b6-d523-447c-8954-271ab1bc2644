package com.alibaba.tripscrm.service.service.log;

import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoDO;
import com.alibaba.tripscrm.service.model.vo.log.OperationLogInfoVO;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.query.OperationLogInfoQuery;

/**
 * <AUTHOR>
 * @Date 2024/6/5 15:48
 **/
public interface ActivityLogInfoService {

    /**
     * 新增日志信息
     * @param operationLogInfoDO 日志对象
     * @return 新增结果
     */
    Boolean insert(OperationLogInfoDO operationLogInfoDO);

    /**
     * 查询日志列表
     * @param query 查询条件
     * @return 日志列表
     */
    PageResultVO<OperationLogInfoVO> queryLogInfoVOList(OperationLogInfoQuery query);

}
