package com.alibaba.tripscrm.service.service.material;

import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;

/**
 * 素材内容获取服务
 * <AUTHOR>
 * @Date 2024/3/14 11:39
 **/
public interface MaterialContentGetService {

    /**
     * 获取群公告内容(包掉了埋点逻辑)
     * @param materialInfo 素材对象
     * @param sendUserId 发送人userId
     * @return 群公告内容
     */
    String getGroupNoticeContent(MaterailInfoDO materialInfo, String sendUserId);

    /**
     * 获取消息内容(包掉了埋点逻辑)
     * @param materialInfo 素材对象
     * @param sendUserId 发送人userId
     * @param sceneType 场景类型
     * @return 消息内容
     */
    String getMessageContent(MaterailInfoDO materialInfo, String sendUserId, String sceneType);

}
