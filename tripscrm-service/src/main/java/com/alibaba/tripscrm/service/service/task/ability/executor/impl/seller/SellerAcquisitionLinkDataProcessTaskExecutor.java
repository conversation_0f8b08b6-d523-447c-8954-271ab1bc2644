package com.alibaba.tripscrm.service.service.task.ability.executor.impl.seller;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.newmedia.client.model.common.ResultDO;
import com.alibaba.newmedia.client.model.response.category.CategoryQueryResponse;
import com.alibaba.newmedia.client.service.CategoryService;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.SellerAcquisitionLinkDataUpdateEventTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.second.DivisionService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.tmall.beehive.common.lang.Strings;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

/**
 * 商家获客链接数据处理任务
 *
 * <AUTHOR>
 * @since 2025/4/9 16:50
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerAcquisitionLinkDataProcessTaskExecutor extends AbstractTaskExecutor {
    private final ActivityContextService activityContextService;
    private final LdbTairManager ldbTairManager;
    private final MetaqProducer metaqProducer;
    private final WechatCustomerService wechatCustomerService;
    private final SellerAcquisitionLinkDataProcessTaskExecutor sellerAcquisitionLinkDataProcessTaskExecutor;
    private final TagInfoService tagInfoService;
    private final DivisionService divisionService;
    private final CategoryService categoryService;

    private final Map<SellerAcquisitionLinkDataUpdateEventTypeEnum, BiConsumer<TaskExecuteContext, TodoTaskVO>> PROCESS_FUNCTION_MAP = new ConcurrentHashMap<>();


    @PostConstruct
    public void init() {
        PROCESS_FUNCTION_MAP.put(SellerAcquisitionLinkDataUpdateEventTypeEnum.VISIT_PAGE, this::processVisitPage);
        PROCESS_FUNCTION_MAP.put(SellerAcquisitionLinkDataUpdateEventTypeEnum.ADD_CUSTOMER, this::processAddCustomer);
    }

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        SellerAcquisitionLinkDataUpdateEventTypeEnum sellerAcquisitionLinkDataUpdateEventTypeEnum = SellerAcquisitionLinkDataUpdateEventTypeEnum.of(context.getExtInfo().getOrDefault("eventType", "").toString());
        if (Objects.isNull(sellerAcquisitionLinkDataUpdateEventTypeEnum)) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，处理添加好友数据，eventType为空", LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        PROCESS_FUNCTION_MAP.get(sellerAcquisitionLinkDataUpdateEventTypeEnum).accept(context, todoTaskVO);
    }

    private void processAddCustomer(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        ActivityTaskInfoBO targetActivityContext = getTargetActivityContext(context, todoTaskVO.getData().get(0));
        if (Objects.isNull(targetActivityContext)) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，处理添加好友数据，活动上下文信息为空", LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.ACTIVITY_CONTEXT_EMPTY);
        }

        if (CollectionUtils.isEmpty(targetActivityContext.getExtraJson()) || !targetActivityContext.getExtraJson().containsKey("paramMap")) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，处理添加好友数据，paramMap为空", LogListUtil.newArrayList(context, targetActivityContext));
            throw new TripscrmException(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }

        String visitPageUserId = MapUtils.getString(targetActivityContext.getExtraJson(), "userId");
        if (StringUtils.isNotBlank(visitPageUserId)) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，处理添加好友数据，在访问页面的时候就已经是好友了", LogListUtil.newArrayList(context, targetActivityContext));
            return;
        }

        ldbTairManager.incr(TairConstant.SELLER_ACQUISITION_ACTIVITY_ADD_CUSTOMER_COUNT_PREFIX + targetActivityContext.getActivityId(), 1, 0, 86400 * 365 * 10);

        String userId = MapUtils.getString(context.getExtInfo(), "userId");
        String externalUserId = MapUtils.getString(context.getExtInfo(), "externalUserId");
        String welcomeCode = MapUtils.getString(context.getExtInfo(), "welcomeCode");
        Map<String, String> paramMap = targetActivityContext.getExtraJson().getObject("paramMap", new TypeReference<HashMap<String, String>>() {
        });
        if (CollectionUtils.isEmpty(paramMap)) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，处理添加好友数据，paramMap为空", LogListUtil.newArrayList(context, targetActivityContext));
            throw new TripscrmException(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }

        long sellerId = MapUtils.getLongValue(paramMap, "sellerId", 0L);
        int itemType = MapUtils.getIntValue(paramMap, "itemType", 0);
        String itemId = MapUtils.getString(paramMap, "itemId");
        String itemName = MapUtils.getString(paramMap, "itemName");
        String orderId = MapUtils.getString(paramMap, "orderId");
        String channelId = MapUtils.getString(paramMap, "channelId");

        if (!NumberUtils.biggerThanZero(sellerId) || (!StringUtils.isBlank(itemId) && !Lists.newArrayList(ItemTypeEnum.GOODS.getItemType(), ItemTypeEnum.HOTEL.getItemType()).contains(itemType))) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，处理添加好友数据，paramMap中缺失参数", LogListUtil.newArrayList(context, targetActivityContext));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        targetActivityContext.getExtraJson().put("addCustomerTime", System.currentTimeMillis());
        activityContextService.upsert(targetActivityContext);

        String unionId = getFinalTargetId(context, todoTaskVO.getData().get(0));
        ldbTairManager.delete(TairConstant.CUSTOMER_LAST_VISIT_SELLER_ACQUISITION_PAGE_ACTIVITY + unionId);

        // 1. 发送欢迎语
        JSONObject sellerAcquisitionLinkAddCustomerData = new JSONObject();
        sellerAcquisitionLinkAddCustomerData.put("welcomeCode", welcomeCode);
        sellerAcquisitionLinkAddCustomerData.put("unionId", unionId);
        sellerAcquisitionLinkAddCustomerData.put("externalUserId", externalUserId);
        sellerAcquisitionLinkAddCustomerData.put("sellerId", sellerId);
        sellerAcquisitionLinkAddCustomerData.put("itemType", itemType);
        sellerAcquisitionLinkAddCustomerData.put("itemId", itemId);
        sellerAcquisitionLinkAddCustomerData.put("itemName", itemName);
        sellerAcquisitionLinkAddCustomerData.put("orderId", orderId);
        sellerAcquisitionLinkAddCustomerData.put("channelId", channelId);
        sellerAcquisitionLinkAddCustomerData.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
        sellerAcquisitionLinkAddCustomerData.put("activityId", context.getTaskInfoDOSnapshot().getActivityId());
        sellerAcquisitionLinkAddCustomerData.put("contextId", MapUtils.getString(targetActivityContext.getExtraJson(), "contextId"));
        metaqProducer.send(MQEnum.SCRM_SELLER_ACQUISITION_LINK_ADD_CUSTOMER, null, null, sellerAcquisitionLinkAddCustomerData.toJSONString());
        // 2. 客户打标签&初始化群聊
        JSONObject notifyInviteSellerAndCustomerJoinGroupData = new JSONObject();
        notifyInviteSellerAndCustomerJoinGroupData.put("unionId", unionId);
        notifyInviteSellerAndCustomerJoinGroupData.put("sellerId", sellerId);
        notifyInviteSellerAndCustomerJoinGroupData.put("externalUserId", externalUserId);
        notifyInviteSellerAndCustomerJoinGroupData.put("userId", userId);
        notifyInviteSellerAndCustomerJoinGroupData.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
        notifyInviteSellerAndCustomerJoinGroupData.put("itemType", itemType);
        notifyInviteSellerAndCustomerJoinGroupData.put("itemId", itemId);
        notifyInviteSellerAndCustomerJoinGroupData.put("itemName", itemName);
        notifyInviteSellerAndCustomerJoinGroupData.put("activityId", context.getTaskInfoDOSnapshot().getActivityId());
        notifyInviteSellerAndCustomerJoinGroupData.put("contextId", MapUtils.getString(targetActivityContext.getExtraJson(), "contextId"));
        List<String> tagIdList = getTagIdList(paramMap);
        notifyInviteSellerAndCustomerJoinGroupData.put("tagIds", String.join(",", tagIdList));
        metaqProducer.send(MQEnum.SCRM_NOTIFY_INIT_SELLER_AND_CUSTOMER_GROUP, null, null, notifyInviteSellerAndCustomerJoinGroupData.toJSONString());
    }

    /**
     * lindorm 数据查询：select().from("trip_scrm_activity_task_info").where(and(compare("target_id", EQUAL, "ocrTX6aNFYphibmBnpuMHD5738xQ"), compare("activity_id", EQUAL, "1852")))
     */
    @AteyeInvoker(description = "手动重试商家客户自动拉群", paraDesc = "unionId&externalUserId&followUserId&activityId")
    public void retryInvite(String unionId, String externalUserId, String followUserId, Long activityId) {
        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(activityId);
        query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_UNION_ID);
        query.setTargetId(unionId);
        List<ActivityTaskInfoBO> targetActivityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
        if (CollectionUtils.isEmpty(targetActivityTaskInfoBOS)) {
            PlatformLogUtil.logFail("手动重试商家客户自动拉群，根据 targetId + targetType + activityId 查询上下文为空", LogListUtil.newArrayList(unionId, externalUserId, activityId));
            return;
        }

        ActivityTaskInfoBO activityTaskInfoBO = ActivityContextService.getNewestBo(targetActivityTaskInfoBOS);
        if (Objects.isNull(activityTaskInfoBO) || CollectionUtils.isEmpty(activityTaskInfoBO.getExtraJson()) || !activityTaskInfoBO.getExtraJson().containsKey("dataTime")) {
            PlatformLogUtil.logFail("手动重试商家客户自动拉群，根据 targetId + targetType + activityId 查询上下文为空", LogListUtil.newArrayList(unionId, externalUserId, activityId, activityTaskInfoBO));
        }

        // 活动上下文过期时间判断
        Long dataTime = MapUtils.getLong(activityTaskInfoBO.getExtraJson(), "dataTime", 0L);
        if (Math.abs(System.currentTimeMillis() - dataTime) > SwitchConfig.targetActivityContextExpireSeconds * 1000L) {
            PlatformLogUtil.logFail("手动重试商家客户自动拉群，上下文已过期", LogListUtil.newArrayList(unionId, externalUserId, activityId, activityTaskInfoBO));
            return;
        }

        Map<String, String> paramMap = activityTaskInfoBO.getExtraJson().getObject("paramMap", new TypeReference<HashMap<String, String>>() {
        });
        if (CollectionUtils.isEmpty(paramMap)) {
            PlatformLogUtil.logFail("手动重试商家客户自动拉群，paramMap为空", LogListUtil.newArrayList(unionId, externalUserId, activityId, activityTaskInfoBO));
            return;
        }

        long sellerId = MapUtils.getLongValue(paramMap, "sellerId", 0L);
        int itemType = MapUtils.getIntValue(paramMap, "itemType", 0);
        String itemId = MapUtils.getString(paramMap, "itemId");
        String itemName = MapUtils.getString(paramMap, "itemName");

        JSONObject notifyInviteSellerAndCustomerJoinGroupData = new JSONObject();
        notifyInviteSellerAndCustomerJoinGroupData.put("unionId", unionId);
        notifyInviteSellerAndCustomerJoinGroupData.put("sellerId", sellerId);
        notifyInviteSellerAndCustomerJoinGroupData.put("externalUserId", externalUserId);
        notifyInviteSellerAndCustomerJoinGroupData.put("userId", followUserId);
        notifyInviteSellerAndCustomerJoinGroupData.put("corpId", SwitchConfig.sellerCorpId);
        notifyInviteSellerAndCustomerJoinGroupData.put("itemType", itemType);
        notifyInviteSellerAndCustomerJoinGroupData.put("itemId", itemId);
        notifyInviteSellerAndCustomerJoinGroupData.put("itemName", itemName);
        notifyInviteSellerAndCustomerJoinGroupData.put("activityId", activityId);
        notifyInviteSellerAndCustomerJoinGroupData.put("contextId", MapUtils.getString(activityTaskInfoBO.getExtraJson(), "contextId"));
        metaqProducer.send(MQEnum.SCRM_NOTIFY_INIT_SELLER_AND_CUSTOMER_GROUP, null, null, notifyInviteSellerAndCustomerJoinGroupData.toJSONString());
    }

    private void processVisitPage(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        String unionId = getFinalTargetId(context, todoTaskVO.getData().get(0));
        String userId = MapUtils.getString(context.getExtInfo(), "userId");
        Map<String, String> paramMap = JSONObject.parseObject(MapUtils.getString(context.getExtInfo(), "paramMap", "{}"), new TypeReference<Map<String, String>>() {
        });
        if (CollectionUtils.isEmpty(paramMap)) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，处理访问获客页面数据，paramMap为空", LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        long sellerId = MapUtils.getLongValue(paramMap, "sellerId", 0L);
        int itemType = MapUtils.getIntValue(paramMap, "itemType", 0);
        String itemId = MapUtils.getString(paramMap, "itemId");
        String itemName = MapUtils.getString(paramMap, "itemName");

        if (!NumberUtils.biggerThanZero(sellerId) || (!StringUtils.isBlank(itemId) && !Lists.newArrayList(ItemTypeEnum.GOODS.getItemType(), ItemTypeEnum.HOTEL.getItemType()).contains(itemType))
        ) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，处理访问获客页面数据，paramMap中缺失参数", LogListUtil.newArrayList(context, paramMap));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        query.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_ACTIVITY_ID);
        query.setTargetId(context.getTaskInfoDOSnapshot().getActivityId().toString());
        List<ActivityTaskInfoBO> activityTaskInfoList = activityContextService.queryByActivityAndTarget(query);
        if (CollectionUtils.isEmpty(activityTaskInfoList)) {
            throw new TripscrmException(TripSCRMErrorCode.ACTIVITY_CONTEXT_EMPTY);
        }
        ActivityTaskInfoBO activityContext = activityTaskInfoList.get(0);

        // 活动上下文信息
        ActivityTaskInfoBO targetActivityContext = getTargetActivityContext(context, todoTaskVO.getData().get(0));
        if (Objects.isNull(targetActivityContext)) {
            targetActivityContext = new ActivityTaskInfoBO();
            targetActivityContext.setContextId(activityContextService.generateContextId());
            targetActivityContext.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
            targetActivityContext.setTargetTypeEnum(ActivityTargetTypeEnum.WX_UNION_ID);
            targetActivityContext.setTargetId(unionId);
            targetActivityContext.setTagIdList(new ArrayList<>());
        }

        JSONObject extraJson = CollectionUtils.isEmpty(targetActivityContext.getExtraJson()) ? new JSONObject() : targetActivityContext.getExtraJson();
        extraJson.put("dataTime", System.currentTimeMillis());
        extraJson.put("userId", userId);
        extraJson.put("paramMap", paramMap);
        extraJson.put(TaskConstant.CONTEXT_ID, activityContext.getContextId());
        targetActivityContext.setExtraJson(extraJson);
        activityContextService.upsert(targetActivityContext);
        ldbTairManager.put(TairConstant.CUSTOMER_LAST_VISIT_SELLER_ACQUISITION_PAGE_ACTIVITY + unionId, context.getTaskInfoDOSnapshot().getActivityId(), 60 * 60 * 12);

        if (StringUtils.isBlank(userId)) {
            return;
        }

        // 客户打标签&初始化群聊
        JSONObject notifyInviteSellerAndCustomerJoinGroupData = new JSONObject();
        notifyInviteSellerAndCustomerJoinGroupData.put("unionId", unionId);
        notifyInviteSellerAndCustomerJoinGroupData.put("sellerId", sellerId);
        notifyInviteSellerAndCustomerJoinGroupData.put("externalUserId", wechatCustomerService.getExternalUserIdByUnionId(unionId));
        notifyInviteSellerAndCustomerJoinGroupData.put("userId", userId);
        notifyInviteSellerAndCustomerJoinGroupData.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
        notifyInviteSellerAndCustomerJoinGroupData.put("itemType", itemType);
        notifyInviteSellerAndCustomerJoinGroupData.put("itemId", itemId);
        notifyInviteSellerAndCustomerJoinGroupData.put("itemName", itemName);
        notifyInviteSellerAndCustomerJoinGroupData.put("activityId", context.getTaskInfoDOSnapshot().getActivityId());
        notifyInviteSellerAndCustomerJoinGroupData.put("contextId", activityContext.getContextId());
        List<String> tagIdList = getTagIdList(paramMap);
        notifyInviteSellerAndCustomerJoinGroupData.put("tagIds", String.join(",", tagIdList));
        metaqProducer.send(MQEnum.SCRM_NOTIFY_INIT_SELLER_AND_CUSTOMER_GROUP, null, null, notifyInviteSellerAndCustomerJoinGroupData.toJSONString());
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return "";
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_UNION_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("目标Id为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        return taskDataBody.getTargetId();
    }

    @Override
    public boolean checkActivityContextValid(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        SellerAcquisitionLinkDataUpdateEventTypeEnum sellerAcquisitionLinkDataUpdateEventTypeEnum = SellerAcquisitionLinkDataUpdateEventTypeEnum.of(context.getExtInfo().getOrDefault("eventType", "").toString());
        if (Objects.isNull(sellerAcquisitionLinkDataUpdateEventTypeEnum)) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，eventType非法", LogListUtil.newArrayList(context));
            return false;
        }

        if (sellerAcquisitionLinkDataUpdateEventTypeEnum == SellerAcquisitionLinkDataUpdateEventTypeEnum.VISIT_PAGE) {
            return true;
        }

        return super.checkActivityContextValid(context, taskDataBody);
    }

    public List<String> getTagIdList(Map<String, String> paramMap) {
        List<String> tagIdList = new ArrayList<>();
        String channelId = MapUtils.getString(paramMap, "channelId");
        if (SwitchConfig.sellerAcquisitionChannelIdList.contains(channelId)) {
            tagIdList.add(channelId);
        }

        String categoryId = MapUtils.getString(paramMap, "categoryId");
        String arrCityDivisionId = MapUtils.getString(paramMap, "arrCityDivisionId");
        if (StringUtils.isNotBlank(categoryId) && Strings.isNumeric(categoryId)) {
            Long categoryTagId = createCategoryTagIfAbsent(SwitchConfig.sellerAcquisitionCategoryTagGroupId, categoryId);
            if (!NumberUtils.biggerThanZero(categoryTagId)) {
                PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，获取类目标签Id失败", LogListUtil.newArrayList(categoryId));
            } else {
                tagIdList.add(categoryTagId.toString());
            }
        }

        if (StringUtils.isBlank(arrCityDivisionId)) {
            return tagIdList;
        }

        try {
            Arrays.stream(arrCityDivisionId.split(","))
                    .filter(StringUtils::isNotBlank)
                    .filter(Strings::isNumeric)
                    .forEach(x -> {
                        Long arrCityDivisionTagId = createDivisionTagIfAbsent(SwitchConfig.sellerAcquisitionArrCityDivisionIdTagGroupId, x);
                        if (!NumberUtils.biggerThanZero(arrCityDivisionTagId)) {
                            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，获取目的地城市行政区划标签Id失败", LogListUtil.newArrayList(categoryId));
                            return;
                        }
                        tagIdList.add(arrCityDivisionTagId.toString());
                    });
        } catch (Exception e) {
            PlatformLogUtil.logException("商家获客链接数据处理任务执行器，获取目的地城市行政区划标签Id失败", e.getMessage(), e, LogListUtil.newArrayList(categoryId));
        }
        return tagIdList;
    }


    private Long createCategoryTagIfAbsent(Long groupId, String source) {
        TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(groupId, source);
        if (Objects.nonNull(tagInfoDTO)) {
            // 标签已经创建出来，无需处理
            return tagInfoDTO.getId();
        }

        ResultDO<CategoryQueryResponse> resultDO = categoryService.queryById(Long.parseLong(source));
        if (Objects.isNull(resultDO) || !resultDO.isSuccess() || Objects.isNull(resultDO.getModel()) || !StringUtils.isBlank(resultDO.getModel().getName())) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，获取类目标Id对应类目信息失败", LogListUtil.newArrayList(source));
            return null;
        }

        return sellerAcquisitionLinkDataProcessTaskExecutor.handleCreateTagIfAbsent(groupId, resultDO.getModel().getName(), source);
    }

    private Long createDivisionTagIfAbsent(Long groupId, String source) {
        TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(groupId, source);
        if (Objects.nonNull(tagInfoDTO)) {
            // 标签已经创建出来，无需处理
            return tagInfoDTO.getId();
        }

        List<TrdiDivisionDO> divisions = divisionService.getDivision(Lists.newArrayList(Long.parseLong(source)));
        if (CollectionUtils.isEmpty(divisions)) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，获取目的地城市行政区划信息失败", LogListUtil.newArrayList(source));
            return null;
        }

        String divisionName = divisions.get(0).getName();
        if (StringUtils.isBlank(divisionName)) {
            PlatformLogUtil.logFail("商家获客链接数据处理任务执行器，获取目的地城市行政区划名称失败", LogListUtil.newArrayList(source));
            return null;
        }

        return sellerAcquisitionLinkDataProcessTaskExecutor.handleCreateTagIfAbsent(groupId, divisionName, source);
    }

    /**
     * 加个锁，怕并发冲突
     */
    @TairLock(value = "'sellerAcquisitionActivityCheckCreateNewTag_' + #groupId + '_' + #name")
    public Long handleCreateTagIfAbsent(Long groupId, String name, String source) {
        TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(groupId, source);
        if (Objects.nonNull(tagInfoDTO)) {
            // 标签已经创建出来，无需处理
            return tagInfoDTO.getId();
        }

        PlatformLogUtil.logInfo("商家获客活动，打标，查询标签结果为，需要新建", LogListUtil.newArrayList(groupId, source));
        List<TagInfoDTO> records = new ArrayList<>();
        TagInfoDTO record = new TagInfoDTO();
        record.setName(name);
        record.setDescription("");
        record.setParentId(0L);
        record.setTagType(TagTypeEnum.CUSTOMER.getCode());
        record.setSpaceId(SwitchConfig.SCRM_BASE_SPACE_ID);
        record.setGroupId(groupId);
        record.setSource(source);
        records.add(record);
        tagInfoService.batchInsertSelective(records, false);
        return record.getId();
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.SELLER_ACQUISITION_LINK_DATA_PROCESS;
    }
}
