package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

/**
 * 会话消息 创建对象
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class ChatMessageUpdateParam {
    /**
     * 接收id（royKey）
     */
    private String receiveId;
    /**
     * 消息时间戳（royKey）
     */
    private Long timestamp;
    /**
     * 消息id（royKey）
     */
    private String messageId;
    /**
     * 状态 0 正常 1 撤回
     */
    private Integer status;
}