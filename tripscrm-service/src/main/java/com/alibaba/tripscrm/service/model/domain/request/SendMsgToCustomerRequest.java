package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/6/1
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SendMsgToCustomerRequest extends BaseSendWechatMsgRequest {
    private static final long serialVersionUID = -3134206355808610687L;
    private String userId;
    private String unionId;
    private String externalUserId;
    private String requestId;
    private MaterailInfoDO materailInfoDO;
    private MaterialTrackRelationDTO materialTrackRelationDTO;
    private MaterialContentConvertContext materialContentConvertContext;
    private TaskType taskType;
}
