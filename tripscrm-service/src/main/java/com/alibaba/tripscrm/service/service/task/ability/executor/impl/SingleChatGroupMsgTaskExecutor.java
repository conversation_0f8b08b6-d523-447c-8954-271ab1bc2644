package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.WechatMsgTemplateChatTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.WechatAddMsgTemplateRequest;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import com.alibaba.tripzoo.proxy.result.WechatAddMsgTemplateResponse;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import com.taobao.csp.courier.StringUtils;
import com.taobao.unifiedsession.core.commons.limiter.RateLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SingleChatGroupMsgTaskExecutor extends AbstractTaskExecutor {
    private final EnterpriseWechatManager enterpriseWechatManager;
    private final ResourceRelationService resourceRelationService;
    private final MaterialTransferService materialTransferService;
    private final LdbTairManager ldbTairManager;

    @Switch(description = "群发是否执行", name = "sendSwitch")
    public static Boolean sendSwitch = true;

    @Switch(description = "群聊消息发送缓存秒数", name = "cacheSeconds")
    public static Integer cacheSeconds = 60 * 2;

    @Switch(description = "每分钟最大群发消息下发数", name = "sendMessageCountPerMinute")
    public static Integer sendMessageCountPerMinute = 10;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> taskDataList = todoTaskVO.getData();
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = taskDataList.get(0);
        String userId = getFinalTargetId(context, taskDataBody);

        // 素材
        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("获取素材失败", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MATERIAL_IN_TASK);
        }

        TripSCRMResult<WechatAddMsgTemplateResponse> result = sendRequest(context, todoTaskVO, materailInfoDO, userId);

        JSONObject data = new JSONObject();
        data.put("extraInfo", context.getExtInfo());
        data.put("sendUserId", userId);
        data.put("materialId", materailInfoDO.getId());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    private void addTaskMsgIdRelation(TaskExecuteContext context, TripSCRMResult<WechatAddMsgTemplateResponse> result) {
        String msgId = result.getData().getMsgId();
        ResourceRelationDO resourceRelationDO = new ResourceRelationDO();
        resourceRelationDO.setGmtCreate(new Date());
        resourceRelationDO.setGmtModified(new Date());
        resourceRelationDO.setSourceId(String.valueOf(context.getMainTaskInstanceId()));
        resourceRelationDO.setSourceType(ResourceTypeEnum.TASK_INSTANCE_ID.getCode().byteValue());
        resourceRelationDO.setTargetId(msgId);
        resourceRelationDO.setTargetType(ResourceTypeEnum.WECHAT_GROUP_MSG_ID.getCode().byteValue());
        resourceRelationDO.setRelationType(ResourceRelationTypeEnum.SCRM_TASK_INSTANCE_WECHAT_GROUP_MSG_RELATION.getCode());
        resourceRelationService.add(resourceRelationDO);
    }

    private TripSCRMResult<WechatAddMsgTemplateResponse> sendRequest(TaskExecuteContext context, TodoTaskVO todoTaskVO, MaterailInfoDO materailInfoDO, String userId) {
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(context, todoTaskVO, userId);
        WechatAddMsgTemplateRequest wechatAddMsgTemplateRequest = new WechatAddMsgTemplateRequest();
        wechatAddMsgTemplateRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wechatAddMsgTemplateRequest.setChatType(WechatMsgTemplateChatTypeEnum.SINGLE);
        wechatAddMsgTemplateRequest.setSendUserId(userId);
        wechatAddMsgTemplateRequest.setTargetIdList(getExternalUserIdList(todoTaskVO, context));
        wechatAddMsgTemplateRequest.setAllowSelect(getAllowSelect(context));

        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        materialContentConvertContext.setWechatUserId(userId);
        List<WxMessageBO> wxMessageBOS = materialTransferService.buildWxMessages(materailInfoDO, materialTrackRelationDTO, materialContentConvertContext, getTaskType().getCode());
        List<WxMessageBO> textMessage = wxMessageBOS.stream().filter(wxMessageBO -> WxAttachmentTypeEnum.TEXT.equals(wxMessageBO.getMsgType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(textMessage)) {
            wechatAddMsgTemplateRequest.setText(textMessage.get(0).getMsgContent());
        }
        List<WxMessageBO> attachments = wxMessageBOS.stream().filter(wxMessageBO -> !WxAttachmentTypeEnum.TEXT.equals(wxMessageBO.getMsgType())).collect(Collectors.toList());
        wechatAddMsgTemplateRequest.setMessageBOList(attachments);

        if (!sendSwitch) {
            PlatformLogUtil.logFail("私聊群发开关关闭，暂不执行", LogListUtil.newArrayList());
            throw new TripscrmException(TripSCRMErrorCode.PRE_NOT_EXECUTE);
        }

        TripSCRMResult<WechatAddMsgTemplateResponse> result = enterpriseWechatManager.addSingleChatGroupMsgTemplate(wechatAddMsgTemplateRequest);
        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        addTaskMsgIdRelation(context, result);
        saveCache();
        return result;
    }

    private Boolean getAllowSelect(TaskExecuteContext context) {
        Map<String, Object> extInfo = context.getExtInfo();
        if (MapUtils.isNotEmpty(extInfo) && extInfo.containsKey(TaskConstant.ALLOW_SELECT)) {
            return MapUtils.getBoolean(extInfo, TaskConstant.ALLOW_SELECT, null);
        }
        return null;
    }

    private void saveCache() {
        ldbTairManager.put(TairConstant.WECHAT_GROUP_MSG_SEND_TIME, System.currentTimeMillis() / 1000, cacheSeconds);
    }

    /**
     * 通过类型过滤消息列表
     *
     * @param resourceList 原始消息列表
     * @param typeEnum     过滤条件
     * @return 过滤结果
     */
    private List<WxMessageBO> getMessageListByType(List<WxMessageBO> resourceList, WxAttachmentTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(resourceList)) {
            return Lists.newArrayList();
        }
        return Optional.of(resourceList
                        .stream()
                        .filter(wxMessageBO -> typeEnum.getCode().equals(wxMessageBO.getMsgType().getCode()))
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    private MaterialTrackRelationDTO buildMaterialTrackRelationDTO(TaskExecuteContext context, TodoTaskVO todoTaskVO, String userId) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(getScene());
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setWxUserId(userId);
        return materialTrackRelationDTO;
    }

    private List<String> getExternalUserIdList(TodoTaskVO todoTaskVO, TaskExecuteContext context) {
        Map<String, Object> extInfo = context.getExtInfo();
        if (MapUtils.isNotEmpty(extInfo) && extInfo.containsKey(TaskConstant.EXTERNAL_USERID_LIST)) {
            Object externalUserIdList = extInfo.get(TaskConstant.EXTERNAL_USERID_LIST);
            if (externalUserIdList instanceof String) {
                try {
                    return JSONObject.parseObject((String) externalUserIdList, new TypeReference<List<String>>() {});
                } catch (Exception e) {
                    PlatformLogUtil.logException("externalUserIdList 解析失败", e.getMessage(), e, LogListUtil.newArrayList(externalUserIdList));
                    return Collections.emptyList();
                }
            } else if (externalUserIdList instanceof List<?>) {
                return ((List<?>) externalUserIdList).stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .collect(Collectors.toList());
            }
        }
        JSONObject extraInfo = JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo());
        return extraInfo.getObject("externalUserIdList", new TypeReference<List<String>>() {
        });
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (!StringUtils.hasText(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("目标Id为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        if (!Objects.equals(taskDataBody.getTargetType(), ActivityTargetTypeEnum.WX_USER_ID.getCode())) {
            PlatformLogUtil.logFail("只能处理WX_USER_ID类型", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        return taskDataBody.getTargetId();
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return getFinalTargetId(context, todoTaskVO.getData().get(0));
    }

    @Override
    public RateLimiter getRateLimiter(TaskExecuteContext context) {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        if (isStreamTask) {
            PlatformLogUtil.logFail("isStreamTask");
            return null;
        }

        double qps = sendMessageCountPerMinute / 60d;
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(qps));
        return RateLimiter.create(sendMessageCountPerMinute / 60d);
    }

    protected String getScene() {
        return MaterialSendSceneTypeConstant.SILIAO_GROUP_MSG_RW;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.SINGLE_CHAT_GROUP_MSG;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_USER_ID;
    }

    @Override
    protected String getUniqueKey(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        JSONObject extraInfo = JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo());
        if (Objects.isNull(extraInfo) || !extraInfo.containsKey("uuid")) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        String uuid = extraInfo.getObject("uuid", new TypeReference<String>() {
        });

        return TairConstant.LDB_TASK_FINISH_TARGET_DATA_PREFIX + context.getInstanceId() + "_" + todoTaskVO.getData().get(0).getTargetId() + "_" + uuid;
    }
}
