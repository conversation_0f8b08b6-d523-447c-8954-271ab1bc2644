package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 个人活码查询
 *
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContactMeQuery extends BasePageRequest {
    /**
     * 分组
     */
    private String groupId = "jimei";
    /**
     * 个人活码名
     */
    private String remark;
    /**
     * 标签
     */
    private String tagId;
    /**
     * 创建开始时间
     */
    private String startTime;
    /**
     * 创建结束时间
     */
    private String endTime;
    /**
     * 业务分组id
     */
    private String bizGroupId;
    /**
     * 管理员
     */
    private String manager;

    /**
     * 业务空间 Id
     */
    private Long spaceId;
}
