package com.alibaba.tripscrm.service.model.domain.context;

import com.alibaba.tripzoo.proxy.enums.CrowdTypeEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-09-14 17:18:11
 */
@Data
public class MaterialContentConvertContext {
    /**
     * 企微成员 Id
     */
    private String wechatUserId;

    /**
     * 企微客户 Id
     */
    private String externalUserId;

    /**
     * 群聊Id
     */
    private String chatId;

    /**
     * 企微客户-群昵称
     */
    private String groupNickName;

    /**
     * 企微客户名称
     */
    private String customerName;

    /**
     * 原始素材内容
     */
    private String originContent;

    /**
     * 要发送的图片列表
     */
    private List<String> imageUrlList;

    /**
     * 动态参数值
     */
    private Map<String, Object> extraInfo = new HashMap<>();

    /**
     * 支付宝群组id列表
     */
    private List<String> alipayGroupTemplateIdList;

    /**
     * 支付宝人群id
     */
    private String alipayCrowdCode;

    /**
     * 支付人群类型
     */
    private String alipayCrowdType;
}
