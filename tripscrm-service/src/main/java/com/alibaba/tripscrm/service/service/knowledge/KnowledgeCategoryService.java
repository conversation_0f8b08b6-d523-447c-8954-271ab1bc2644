package com.alibaba.tripscrm.service.service.knowledge;

import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.CategoryReq;
import com.alibaba.tripscrm.service.model.dto.knowledge.KnowledgeCategoryDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 知识分类
 * @date 2025/9/15
 */
public interface KnowledgeCategoryService {

    /**
     * 根据条件查询（分页）
     */
    PageInfoDTO<KnowledgeCategoryDTO> pageQuery(CategoryReq query);

    /**
     * 根据主键ID查询
     */
    KnowledgeCategoryDTO selectById(Long id);

    /**
     * 根据条件查询知识分类
     */
    List<KnowledgeCategoryDTO> selectByParam(CategoryReq query);

    /**
     * 更新知识分类
     */
    Long update(KnowledgeCategoryDTO knowledgeCategory);

    /**
     * 根据名称查询知识分类
     */
    List<KnowledgeCategoryDTO> selectByName(String name, Long spaceId);

    /**
     * 删除知识分类
     */
    boolean deleteById(Long id, String userId, String userName);

    /**
     * 新增知识分类
     */
    Long insert(KnowledgeCategoryDTO knowledgeCategory);

}
