package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatReceiveTypeEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import lombok.Data;

/**
 * 会话消息 创建对象
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class ChatMessageCreateParam {
    /**
     * 发送人id，1:企微成员id 2:企微客户id
     */
    private String senderId;
    /**
     * 发送人类型，1:企微成员 2:企微客户
     *
     * @see GroupUserTypeEnum
     */
    private Integer senderType;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 接收方id，1:群聊会话id（wra6eoWgAAVz6MvTH604C3GfVfIpwgKg）2:单聊会话id（ChenShiNan#wma6eoWgAAmP8o2Ulsa_huRYq-MO24Rw）
     */
    private String receiveId;
    /**
     * 接收方类型，1:群聊 2:单聊
     *
     * @see FusionChatReceiveTypeEnum
     */
    private Integer receiveType;
    /**
     * 创建时间
     */
    private Long timestamp;
    /**
     * 组织id
     */
    private String corpId;
    /**
     * 当前占用该企微号的平台账号
     */
    private String lockUser;
    /**
     * 当前占用该企微号的平台账号名称
     */
    private String lockUserName;
    /**
     * 异步请求Id
     */
    private String requestId;
}