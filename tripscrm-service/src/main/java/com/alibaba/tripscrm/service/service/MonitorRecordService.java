package com.alibaba.tripscrm.service.service;


import com.alibaba.tripscrm.dal.model.domain.data.MonitorRecordDO;
import com.alibaba.tripscrm.service.model.domain.query.MonitorRecordQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 规则记录
 *
 * <AUTHOR>
 * @data 2024-10-18 17:42
 */
public interface MonitorRecordService {
    /**
     * 插入单条记录
     *
     * @param record
     * @return
     */
    Integer insert(MonitorRecordDO record);

    /**
     * 更新单条记录
     *
     * @param record
     * @return
     */
    Integer updateByPrimaryKey(MonitorRecordDO record);

    /**
     * 根据条件规则查询
     *
     * @param monitorRecordQuery
     * @return
     */
    PageInfo<MonitorRecordDO> pageQuery(MonitorRecordQuery monitorRecordQuery);

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    Integer delete(Long id);


    /**
     * 根据条件规则查询
     *
     * @param monitorRecordQuery
     * @return
     */
    List<MonitorRecordDO> selectByCondition(MonitorRecordQuery monitorRecordQuery);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    MonitorRecordDO selectByPrimaryKey(Long id);

    /**
     * 存在更新，不存在插入
     */
    Integer upsert(MonitorRecordDO record);
}
