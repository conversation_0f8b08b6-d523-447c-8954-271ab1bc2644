package com.alibaba.tripscrm.service.service.strategy.log.factory;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.model.domain.log.LogContentBO;
import com.alibaba.tripscrm.service.service.strategy.log.AbstractLogHandleService;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2024/3/17 14:11
 **/
@Service
public class AbstractLogFactory<T extends LogContentBO> {

    @Resource
    private List<AbstractLogHandleService> logHandleServiceList;

    private Map<LogShowTypeEnum, AbstractLogHandleService> logHandleServiceMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        for (AbstractLogHandleService abstractLogHandleService : logHandleServiceList) {
            logHandleServiceMap.put(abstractLogHandleService.handleType(), abstractLogHandleService);
        }
    }

    /**
     * 获取处理的服务
     * @param showTypeEnum 日志展示类型
     * @return 日志处理服务
     */
    private AbstractLogHandleService getService(LogShowTypeEnum showTypeEnum) {
        return logHandleServiceMap.get(showTypeEnum);
    }

    /**
     * 获取日志展示内容
     * @param logContentBO 日志内容对象
     * @return 展示内容
     */
    public String getLogShowStr(T logContentBO) {
        LogShowTypeEnum logShowTypeEnum = LogShowTypeEnum.codeOf(logContentBO.getShowType());
        if (logShowTypeEnum == null) {
            PlatformLogUtil.logFail("日志抽象工厂找不到对应的日志类型", LogListUtil.newArrayList(logContentBO));
            return null;
        }
        AbstractLogHandleService service = getService(logShowTypeEnum);
        if (service == null) {
            PlatformLogUtil.logFail("没有对应的日志处理服务", LogListUtil.newArrayList(logContentBO));
            return null;
        }
        return service.getLogContent(logContentBO);
    }

}
