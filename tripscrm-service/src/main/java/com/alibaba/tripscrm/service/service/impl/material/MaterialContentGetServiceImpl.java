package com.alibaba.tripscrm.service.service.impl.material;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.material.MaterialContentGetService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.util.material.MaterialTrackRelationUtils;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/14 11:43
 **/
@Service
public class MaterialContentGetServiceImpl implements MaterialContentGetService {
    @Resource
    private MaterialTransferService materialTransferService;

    @Override
    public String getGroupNoticeContent(MaterailInfoDO materialInfo, String sendUserId) {
        MaterialTrackRelationDTO materialTrackRelationDTO = MaterialTrackRelationUtils.buildMaterialTrackRelationDTO(MaterialSendSceneTypeConstant.QUNGG, materialInfo.getId(), sendUserId);
        MaterialContentConvertContext context = new MaterialContentConvertContext();
        context.setWechatUserId(sendUserId);
        context.setExtraInfo(new HashMap<>());
        List<MessageBO> messageBOS = materialTransferService.buildMessages(materialInfo, materialTrackRelationDTO, context, TaskType.PUBLISH_GROUP_NOTICE.getCode());
        if (CollectionUtils.isEmpty(messageBOS)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }
        return messageBOS.get(0).getMsgContent();
    }

    @Override
    public String getMessageContent(MaterailInfoDO materialInfo, String sendUserId, String sceneType) {
        MaterialTrackRelationDTO materialTrackRelationDTO = MaterialTrackRelationUtils.buildMaterialTrackRelationDTO(sceneType, materialInfo.getId(), sendUserId);
        MaterialContentConvertContext context = new MaterialContentConvertContext();
        context.setWechatUserId(sendUserId);
        context.setExtraInfo(new HashMap<>());
        List<MessageBO> messageBOS = materialTransferService.buildMessages(materialInfo, materialTrackRelationDTO, context, TaskType.WECHAT_MOMENT_COMMENT.getCode());
        if (CollectionUtils.isEmpty(messageBOS)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }
        return messageBOS.get(0).getMsgContent();
    }
}
