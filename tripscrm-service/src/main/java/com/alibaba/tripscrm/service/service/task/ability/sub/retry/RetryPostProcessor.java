package com.alibaba.tripscrm.service.service.task.ability.sub.retry;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 任务执行_单条记录重试-执行完成后更新子任务实例执行进度
 *
 * <AUTHOR>
 * @since 2024/4/22 16:51
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RetryPostProcessor implements ExecuteProcessor {
    private final SubTaskInstanceService subTaskInstanceService;

    @Override
    @TaskExecuteLog("任务执行_单条记录重试-执行完成后更新子任务实例执行进度")
    public TripSCRMResult<Void> process(TaskExecuteContext context) {
        if (!NumberUtils.validLong(context.getInstanceId())) {
            return TripSCRMResult.success(null);
        }

        TaskDataVO.DataBodyVO dataBodyVO = context.getTaskDataVO().getData().get(0);
        MetaQDeleyLevel retryDelayLevel = subTaskInstanceService.getRetryDelayLevel(context, dataBodyVO.getResultErrorCode());
        if (Objects.nonNull(retryDelayLevel)) {
            dataBodyVO.setNextRetryDelayLevel(retryDelayLevel);
            dataBodyVO.setResultErrorCode(TripSCRMErrorCode.FAIL_AND_RETRY);
            subTaskInstanceService.updateSubTaskInstanceExecuteResult(context, dataBodyVO);
            return TripSCRMResult.fail(TripSCRMErrorCode.FAIL_AND_RETRY);
        }

        subTaskInstanceService.updateSubTaskInstanceExecuteResult(context, context.getTaskDataVO().getData().get(0));
        return TripSCRMResult.success(null);
    }
}
