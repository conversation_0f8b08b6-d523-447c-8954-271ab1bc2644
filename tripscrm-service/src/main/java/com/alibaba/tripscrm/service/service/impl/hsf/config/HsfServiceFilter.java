package com.alibaba.tripscrm.service.service.impl.hsf.config;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.domain.request.base.CorpBaseRequest;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.taobao.hsf.invocation.Invocation;
import com.taobao.hsf.invocation.InvocationHandler;
import com.taobao.hsf.invocation.RPCResult;
import com.taobao.hsf.invocation.filter.ServerFilter;
import com.taobao.hsf.util.concurrent.ListenableFuture;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * HSF 拦截器
 * https://yuque.alibaba-inc.com/chgygl/xmp9di/xpufzh
 *
 * <AUTHOR>
 * @date 2023/12/26
 */
@Slf4j
public class HsfServiceFilter implements ServerFilter {
    @Override
    public ListenableFuture<RPCResult> invoke(InvocationHandler invocationHandler, Invocation invocation) throws Throwable {
        // 获取实际的入参
        Object[] arguments = invocation.getMethodArgs();
        // 初始化企微组织，默认组织
        SpaceInfoThreadLocalUtils.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
        for (int i = 0; i < arguments.length; i++) {
            if (arguments[i] instanceof CorpBaseRequest) {
                CorpBaseRequest corpBaseRequest = (CorpBaseRequest) arguments[i];
                if(Objects.isNull(corpBaseRequest.getCorpId())){
                    corpBaseRequest.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
                }
                // 初始化企微组织上下文
                SpaceInfoThreadLocalUtils.setCorpId(corpBaseRequest.getCorpId());
                break;
            }
        }
        try {
            return invocationHandler.invoke(invocation);
        } catch (Exception e) {
            PlatformLogUtil.logException("HsfFilter，执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            throw e;
        } finally {
            // 从线程上下文中移除
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    @Override
    public void onResponse(Invocation invocation, RPCResult rpcResult) {

    }
}