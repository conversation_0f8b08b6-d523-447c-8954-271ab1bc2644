package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;

/**
 * 在群内添加客户为好友任务配置处理器
 *
 * <AUTHOR>
 * @date 2023-12-30 15:26:28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AddWechatCustomerByGroupTaskConfigProcessor extends AbstractTaskConfigProcessor {
    private final TaskService taskService;
    private final ActivityContextService activityContextService;
    private final LdbTairManager ldbTairManager;
    private final WechatUserService wechatUserService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        JSONObject extraInfoJson = super.getExtraInfo(taskInfoDO);
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        if (!configJson.containsKey("contextId")) {
            return extraInfoJson;
        }

        int sendAddCustomerRequestCount = Integer.parseInt(Optional.ofNullable(ldbTairManager.get(TairConstant.ADD_WECHAT_CUSTOMER_BY_GROUP_REQUEST_COUNT_PREFIX + taskInfoDO.getId())).orElse(0).toString());
        int sendAddCustomerRequestSuccessCount = Integer.parseInt(Optional.ofNullable(ldbTairManager.get(TairConstant.ADD_WECHAT_CUSTOMER_BY_GROUP_REQUEST_SUCCESS_COUNT_PREFIX + taskInfoDO.getId())).orElse(0).toString());
        int addCustomerSuccessCount = Integer.parseInt(Optional.ofNullable(ldbTairManager.get(TairConstant.ADD_WECHAT_CUSTOMER_BY_GROUP_SUCCESS_COUNT_PREFIX + taskInfoDO.getId())).orElse(0).toString());

        // 申请添加总数
        extraInfoJson.put("sendAddCustomerRequestCount", sendAddCustomerRequestCount);
        // 申请添加成功总数
        extraInfoJson.put("sendAddCustomerRequestSuccessCount", sendAddCustomerRequestSuccessCount);
        // 申请添加失败总数
        extraInfoJson.put("sendAddCustomerRequestFailCount", sendAddCustomerRequestCount - sendAddCustomerRequestSuccessCount);
        // 添加成功总数
        extraInfoJson.put("addCustomerSuccessCount", addCustomerSuccessCount);
        // 有效申请率
        extraInfoJson.put("sendAddCustomerRequestSuccessPercent", sendAddCustomerRequestCount == 0 ? 0 : (sendAddCustomerRequestSuccessCount * 100 / sendAddCustomerRequestCount));
        // 有效添加率
        extraInfoJson.put("addCustomerSuccessPercent", sendAddCustomerRequestCount == 0 ? 0 : addCustomerSuccessCount * 100 / sendAddCustomerRequestCount);

        if (!configJson.containsKey("userIdList")) {
            return extraInfoJson;
        }

        List<String> userIdList = configJson.getObject("userIdList", new TypeReference<List<String>>() {
        });

        List<WechatUserDTO> wechatUserDTOS = wechatUserService.listById(userIdList);
        extraInfoJson.put("wechatUserList", wechatUserDTOS);
        return extraInfoJson;
    }

    @Override
    public TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName) {
        TaskInfoDO taskInfoDO = super.getInitConfig(activityId, spaceId, taskName);
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        Date effectEndTime = new Date(LocalDate.of(2040, 12, 30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        taskInfoDO.setEffectEndTime(effectEndTime);
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerByGroupEventSourceId);
        return taskInfoDO;
    }

    private Long processActivityContext(TaskInfoDO taskInfoDO, JSONObject extInfoJson, JSONObject configJson) {
        Long contextId = configJson.containsKey("contextId") ? configJson.getLong("contextId") : activityContextService.generateContextId();
        List<String> newChatIdList = extInfoJson.getObject("chatIdList", new TypeReference<List<String>>() {
        });

        // 查询上下文信息是否已存在
        ActivityTaskInfoBO oldActivityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        if (Objects.nonNull(oldActivityTaskInfoBO) && oldActivityTaskInfoBO.getExtraJson().containsKey("chatIdList")) {
            List<String> oldChatIdList = oldActivityTaskInfoBO.getExtraJson().getObject("chatIdList", new TypeReference<List<String>>() {
            });

            List<String> removeChatIdList = oldChatIdList.stream().filter(x -> !newChatIdList.contains(x)).collect(Collectors.toList());
            List<ActivityTaskInfoBO> queryList = new ArrayList<>();
            for (String chatId : removeChatIdList) {
                ActivityTaskInfoBO query = new ActivityTaskInfoBO();
                query.setActivityId(taskInfoDO.getActivityId());
                query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
                query.setTargetId(chatId);
                queryList.add(query);
            }
            List<ActivityTaskInfoBO> removeActivityTaskInfoList = activityContextService.batchQueryByActivityAndTarget(queryList);
            List<Long> contextIdList = removeActivityTaskInfoList.stream().map(ActivityTaskInfoBO::getContextId).collect(Collectors.toList());
            activityContextService.batchDeleteByTaskId(contextIdList);
            PlatformLogUtil.logFail("batchDeleteByTaskId", LogListUtil.newArrayList(removeActivityTaskInfoList));
        }

        // =====================================================================================
        // 插入/更新活动上下文信息（targetType = 任务id类型）
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(contextId);
        activityTaskInfoBO.setActivityId(taskInfoDO.getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_TASK_ID);
        activityTaskInfoBO.setTargetId(String.valueOf(taskInfoDO.getId()));
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("chatIdList", newChatIdList);
        extraJson.put("dataTime", System.currentTimeMillis());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);
        PlatformLogUtil.logFail("upsert", LogListUtil.newArrayList(activityTaskInfoBO));

        // =====================================================================================
        // 插入/更新活动上下文信息（targetType = 客户群类型）
        for (String chatId : newChatIdList) {
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setActivityId(taskInfoDO.getActivityId());
            query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            query.setTargetId(chatId);
            List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
            Long targetActivityContextId;
            if (!CollectionUtils.isEmpty(activityTaskInfoBOS)) {
                targetActivityContextId = ActivityContextService.getNewestBo(activityTaskInfoBOS).getContextId();
            } else {
                targetActivityContextId = activityContextService.generateContextId();
            }

            ActivityTaskInfoBO activityTaskInfoBO1 = new ActivityTaskInfoBO();
            activityTaskInfoBO1.setContextId(targetActivityContextId);
            activityTaskInfoBO1.setActivityId(taskInfoDO.getActivityId());
            activityTaskInfoBO1.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            activityTaskInfoBO1.setTargetId(chatId);
            activityTaskInfoBO1.setTagIdList(new ArrayList<>());
            JSONObject extraJson1 = new JSONObject();
            extraJson1.put("contextId", contextId);
            extraJson1.put("dataTime", System.currentTimeMillis());
            activityTaskInfoBO1.setExtraJson(extraJson1);
            activityContextService.upsert(activityTaskInfoBO1);
            PlatformLogUtil.logFail("upsert", LogListUtil.newArrayList(activityTaskInfoBO1));
        }

        return contextId;
    }

    private void deleteActivityContext(TaskInfoDO taskInfoDO, JSONObject extraInfoJson) {
        Long contextId = extraInfoJson.getLong("contextId");
        // 查询上下文信息
        ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        if (Objects.isNull(activityTaskInfoBO)) {
            return;
        }

        // 删除任务上下文
        activityContextService.deleteByTaskId(contextId);
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(activityTaskInfoBO));

        // ====================================================================
        // 删除群聊上下文
        if (!activityTaskInfoBO.getExtraJson().containsKey("chatIdList")) {
            return;
        }
        List<String> chatIdList = activityTaskInfoBO.getExtraJson().getObject("chatIdList", new TypeReference<List<String>>() {
        });
        List<ActivityTaskInfoBO> queryList = new ArrayList<>();
        for (String chatId : chatIdList) {
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setActivityId(taskInfoDO.getActivityId());
            query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            query.setTargetId(chatId);
            queryList.add(query);
        }

        List<ActivityTaskInfoBO> removeActivityTaskInfoList = activityContextService.batchQueryByActivityAndTarget(queryList);
        List<Long> contextIdList = removeActivityTaskInfoList.stream().map(ActivityTaskInfoBO::getContextId).collect(Collectors.toList());
        activityContextService.batchDeleteByTaskId(contextIdList);
        PlatformLogUtil.logFail("batchDeleteByTaskId", LogListUtil.newArrayList(removeActivityTaskInfoList));
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
        JSONObject extInfoJson = JSONObject.parseObject(taskInfoDO.getExtInfo());
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        extInfoJson.putIfAbsent("userIdList", new ArrayList<>());
        extInfoJson.putIfAbsent("chatIdList", new ArrayList<>());
        extInfoJson.putIfAbsent("addCustomerIfAt", true);
        extInfoJson.putIfAbsent("addCustomerSendMessageList", new ArrayList<>());

        // 处理活动上下文数据
        Long contextId = processActivityContext(taskInfoDO, extInfoJson, configJson);
        // 更新任务配置信息
        configJson.put("contextId", contextId);
        taskInfoDO.setExtInfo(extInfoJson.toJSONString());
        taskInfoDO.setConfig(configJson.toJSONString());
        taskService.updateTaskInfoWithoutPost(taskInfoDO);
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
        postCreate(taskInfoDO);
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        // 删除上下文信息
        deleteActivityContext(taskInfoDO, configJson);
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.ADD_WECHAT_CUSTOMER_BY_GROUP;
    }
}
