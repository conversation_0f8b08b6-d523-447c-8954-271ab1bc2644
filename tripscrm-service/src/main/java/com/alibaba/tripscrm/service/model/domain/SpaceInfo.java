package com.alibaba.tripscrm.service.model.domain;

import com.alibaba.tripscrm.domain.enums.RoleTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.SourceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-08-17 15:58:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpaceInfo {
    /**
     * 当前操作的企业id
     */
    private String corpId;
    /**
     * 当前操作的空间id
     */
    private Long spaceId;
    /**
     * 商家id
     */
    private String sellerId;
    /**
     * 当前用户的角色
     */
    private RoleTypeEnum roleType;
    /**
     * 当前操作的来源id
     */
    private String sourceId;
    /**
     * 当前操作的来源类型
     */
    private SourceTypeEnum sourceType;

    public SpaceInfo(RoleTypeEnum roleType, Long spaceId, String corpId) {
        this.roleType = roleType;
        this.spaceId = spaceId;
        this.corpId = corpId;
    }

    public SpaceInfo setSpaceId(Long spaceId) {
        this.spaceId = spaceId;
        return this;
    }
    public SpaceInfo setSellerId(String sellerId) {
        this.sellerId = sellerId;
        return this;
    }

    public SpaceInfo setRoleType(RoleTypeEnum roleType) {
        this.roleType = roleType;
        return this;
    }

    public SpaceInfo setSourceId(String sourceId) {
        this.sourceId = sourceId;
        return this;
    }

    public SpaceInfo setSourceType(SourceTypeEnum sourceType) {
        this.sourceType = sourceType;
        return this;
    }
}
