package com.alibaba.tripscrm.service.service.impl.hsf;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.domain.FollowUserCustomerRelationInfo;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.TripSCRMEnterpriseWechatCustomerResponse;
import com.alibaba.tripscrm.domain.WechatCustomerInfo;
import com.alibaba.tripscrm.domain.request.*;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.CustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserPageQuery;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.domain.query.TagGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerListQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.space.SpaceResourceService;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpecialCharEscape;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripscrm.service.wechat.EnterpriseWechatCustomerService;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.csp.courier.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-10 18:16:22
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = EnterpriseWechatCustomerService.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class EnterpriseWechatCustomerServiceImpl implements EnterpriseWechatCustomerService {
    private final WechatCustomerService wechatCustomerService;
    private final UicUtils uicUtils;
    private final Map<ActivityTargetTypeEnum, BiFunction<FollowUserQuery, String, Boolean>> ID_MAPPING_FUNCTION_MAP = new ConcurrentHashMap<>();
    private final SpaceResourceService spaceResourceService;
    private final TagGroupService tagGroupService;
    private final TagInfoService tagInfoService;
    private final CustomerRelationOpenSearchManager customerRelationOpenSearchManager;

    @PostConstruct
    public void init() {
        ID_MAPPING_FUNCTION_MAP.put(ActivityTargetTypeEnum.WX_UNION_ID, this::handleUnionId);
        ID_MAPPING_FUNCTION_MAP.put(ActivityTargetTypeEnum.WX_EXTERNAL_USERID, this::handleExternalUserId);
        ID_MAPPING_FUNCTION_MAP.put(ActivityTargetTypeEnum.TAOBAO_USER_ID, this::handleUid);
    }

    @Override
    @ServiceLog("查询企微成员-客户好友关系")
    public TripSCRMResult<FollowUserInfo> getFollowUserList(TripSCRMEnterpriseWechatCustomerRequest request) {
        TripSCRMEnterpriseWechatCustomerQueryRequest customersRequest = new TripSCRMEnterpriseWechatCustomerQueryRequest();
        customersRequest.setTargetId(request.getTargetId());
        customersRequest.setCorpId(request.getCorpId());
        customersRequest.setWechatUserIdList(Lists.newArrayList(request.getUserId()));
        customersRequest.setTargetType(request.getTargetType());
        return queryFollowUser(customersRequest);
    }

    @Override
    @ServiceLog("查询企微成员-判断是否具有好友关系")
    public TripSCRMResult<Boolean> hasEnterpriseWechatRelation(TripSCRMHasCustomerRelationRequest request) {
        if (Objects.isNull(request) || StringUtils.isBlank(request.getUserId())) {
            PlatformLogUtil.logFail("查询企微成员-判断是否具有好友关系，参数非法", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        String unionId = uicUtils.getUnionIdByUid(request.getUserId());
        if (StringUtils.isBlank(unionId)) {
            PlatformLogUtil.logFail("查询企微成员-判断是否具有好友关系，查询客户关系淘系userId未映射到unionId", LogListUtil.newArrayList(request));
            return TripSCRMResult.success(false);
        }

        FollowUserQuery query = new FollowUserQuery();
        query.setUnionId(unionId);
        query.setDepartmentIdList(SwitchConfig.CUSTOMER_RELATION_DEPARTMENT);
        if (StringUtils.isNotBlank(request.getWechatUserId())) {
            query.setUserIdList(Arrays.asList(request.getWechatUserId()));
        }
        FollowUserInfo followUserInfo = wechatCustomerService.listFollowUser(query);
        if (followUserInfo == null || CollectionUtils.isEmpty(followUserInfo.getRelationList())) {
            return TripSCRMResult.success(false);
        }
        List<FollowUserInfo.CustomerRelation> canUseRelationList = followUserInfo.getRelationList().stream()
                .filter(customerRelation -> CustomerRelationStatusEnum.FRIEND.getCode().equals(customerRelation.getStatus()))
                .collect(Collectors.toList());
        return TripSCRMResult.success(CollectionUtils.isNotEmpty(canUseRelationList));
    }

    @Override
    @ServiceLog("根据条件查询企微成员-客户好友关系")
    public TripSCRMResult<FollowUserInfo> queryFollowUser(TripSCRMEnterpriseWechatCustomerQueryRequest request) {
        if (!StringUtils.hasText(request.getTargetId())) {
            PlatformLogUtil.logFail("根据条件查询企微成员-客户好友关系，参数非法", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ActivityTargetTypeEnum targetType = ActivityTargetTypeEnum.codeOf(request.getTargetType());
        if (!ID_MAPPING_FUNCTION_MAP.containsKey(targetType)) {
            PlatformLogUtil.logFail("根据条件查询企微成员-客户好友关系，targetType非法", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        FollowUserQuery query = new FollowUserQuery();
        Boolean result = ID_MAPPING_FUNCTION_MAP.get(targetType).apply(query, request.getTargetId());
        if (!result) {
            return TripSCRMResult.fail(TripSCRMErrorCode.ID_MAPPING_FAIL);
        }

        // spaceId不为空
        if (NumberUtils.biggerThanZero(request.getSpaceId())) {
            // 查询指定空间资源列表
            List<SpaceResourceDO> spaceResourceDOS = spaceResourceService.listBySpaceIdAndResourceTypeWithCache(request.getSpaceId(), ResourceTypeEnum.DEPARTMENT);
            // 绑定部门Id列表
            List<Integer> depList = spaceResourceDOS.stream()
                    .map(SpaceResourceDO::getTargetId)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(depList)) {
                return TripSCRMResult.success(null);
            }
            query.setDepartmentIdList(depList);
        }

        // wechatUserIdList不为空
        if (CollectionUtils.isNotEmpty(request.getWechatUserIdList())){
            query.setUserIdList(request.getWechatUserIdList().stream().filter(StringUtils::hasText).collect(Collectors.toList()));
        }

        FollowUserInfo followUserInfo = wechatCustomerService.listFollowUser(query);
        return TripSCRMResult.success(followUserInfo);
    }

    @Override
    @ServiceLog("根据条件查询企微成员-客户好友关系支持模糊搜索")
    public TripSCRMResult<PageInfoDTO<TripSCRMEnterpriseWechatCustomerResponse>> queryCustomer(
            TripSCRMEnterpriseWechatCustomerPageRequest request) {
        PageInfoDTO<TripSCRMEnterpriseWechatCustomerResponse> pageInfoDTO = new PageInfoDTO<>();
        FollowUserPageQuery followUserPageQuery = new FollowUserPageQuery();
        followUserPageQuery.setUserIdList(Objects.isNull(request.getUserId())?null:Collections.singletonList(request.getUserId()));
        followUserPageQuery.setNameLike(SpecialCharEscape.doProcessSpecialWord(request.getNameLike()));
        followUserPageQuery.setRemarkLike(SpecialCharEscape.doProcessSpecialWord(request.getRemarkLike()));
        followUserPageQuery.setNameOrRemarkLike(SpecialCharEscape.doProcessSpecialWord(request.getNameOrRemarkLike()));
        followUserPageQuery.setExternalUserIdList(request.getExternalUserIdList());
        followUserPageQuery.setPageSize(request.getPageSize());
        followUserPageQuery.setPageNum(request.getPageNum());
        PageInfo<FollowUserCustomerRelationInfo> pageInfo =
                wechatCustomerService.pageFollowUserInfo(followUserPageQuery);

        List<FollowUserCustomerRelationInfo> followUserCustomerRelationInfos =
                Optional.ofNullable(pageInfo).map(PageInfo::getList).orElse(new ArrayList<>());
        List<TripSCRMEnterpriseWechatCustomerResponse> resultList = new ArrayList<>();
        for (FollowUserCustomerRelationInfo customerRelation : followUserCustomerRelationInfos) {
            TripSCRMEnterpriseWechatCustomerResponse tripSCRMEnterpriseWechatCustomerResponse =
                    new TripSCRMEnterpriseWechatCustomerResponse();
            tripSCRMEnterpriseWechatCustomerResponse.setExternalUserId(customerRelation.getExternalUserId());
            tripSCRMEnterpriseWechatCustomerResponse.setUserId(customerRelation.getUserId());
            tripSCRMEnterpriseWechatCustomerResponse.setName(customerRelation.getName());
            tripSCRMEnterpriseWechatCustomerResponse.setAvatarUrl(customerRelation.getAvatarUrl());
            tripSCRMEnterpriseWechatCustomerResponse.setCorpName(customerRelation.getCorpName());
            tripSCRMEnterpriseWechatCustomerResponse.setStatus(customerRelation.getStatus());
            tripSCRMEnterpriseWechatCustomerResponse.setRemark(customerRelation.getRemark());
            tripSCRMEnterpriseWechatCustomerResponse.setAddTime(customerRelation.getAddTime());
            tripSCRMEnterpriseWechatCustomerResponse.setUserName(customerRelation.getUserName());
            tripSCRMEnterpriseWechatCustomerResponse.setUserAvatarUrl(customerRelation.getUserAvatarUrl());
            resultList.add(tripSCRMEnterpriseWechatCustomerResponse);
        }
        pageInfoDTO.setList(resultList);
        pageInfoDTO.setTotal(pageInfo.getTotal());
        pageInfoDTO.setPageNum(request.getPageNum());
        pageInfoDTO.setPageSize(request.getPageSize());
        return TripSCRMResult.success(pageInfoDTO);
    }

    @Override
    public TripSCRMResult<List<WechatCustomerInfo>> wechatCustomerTagsQuery(WechatCustomerQueryRequest request) {
        List<WechatCustomerInfo> resultList = new ArrayList<>();
        WechatCustomerListQuery query = new WechatCustomerListQuery();
        BeanUtils.copyProperties(request, query);
        PageInfo<Set<String>> pageInfo = customerRelationOpenSearchManager.getCustomerTagIds(query);
        if (!pageInfo.hasContent()) {
            return TripSCRMResult.success(resultList);
        }
        List<Set<String>> tagSets = pageInfo.getList();
        Set<String> allTags = new HashSet<>();
        for (Set<String> itemSet : tagSets) {
            allTags.addAll(itemSet);
        }
        if (org.springframework.util.CollectionUtils.isEmpty(allTags)) {
            return TripSCRMResult.success(resultList);
        }
        WechatCustomerInfo wechatCustomerInfo = new WechatCustomerInfo();
        List<WechatCustomerInfo.InnerTagInfo> userTagList = new ArrayList<>();
        wechatCustomerInfo.setUserTagList(userTagList);
        wechatCustomerInfo.setUnionId(request.getUnionId());
        resultList.add(wechatCustomerInfo);
        List<TagInfoDTO> tagInfoDTOList = tagInfoService.selectByTagIdList(new ArrayList<>(allTags));
        if (org.springframework.util.CollectionUtils.isEmpty(tagInfoDTOList)) {
            return TripSCRMResult.success(resultList);
        }
        List<Long> groupIds = tagInfoDTOList.stream().map(TagInfoDTO::getGroupId).collect(Collectors.toList());
        TagGroupQuery tagGroupQuery = new TagGroupQuery();
        tagGroupQuery.setIds(groupIds);
        List<TagGroupDTO> groupDTOList = tagGroupService.selectByCondition(tagGroupQuery);
        if (org.springframework.util.CollectionUtils.isEmpty(groupDTOList)) {
            return TripSCRMResult.success(resultList);
        }
        for (TagInfoDTO tagInfoDTO : tagInfoDTOList) {
            // 要求限定空间 则 标签所属空间需要满足条件
            List<Long> spaceIdList = Optional.ofNullable(request.getSpaceIdList())
                    .orElse(Lists.newArrayList(SwitchConfig.userOperationSpaceId, SwitchConfig.SCRM_BASE_SPACE_ID, SwitchConfig.sellerCorpPlatformSpaceId));
            if (!spaceIdList.contains(tagInfoDTO.getSpaceId())) {
                continue;
            }
            TagGroupDTO tagGroupDTO = groupDTOList.stream()
                    .filter(item -> Objects.equals(item.getId(), tagInfoDTO.getGroupId()))
                    .findAny()
                    .orElse(null);
            if (tagGroupDTO == null) {
                continue;
            }
            WechatCustomerInfo.InnerTagInfo innerTagInfo = new WechatCustomerInfo.InnerTagInfo();
            innerTagInfo.setTagValue(tagInfoDTO.getName());
            innerTagInfo.setTagGroupName(tagGroupDTO.getName());
            userTagList.add(innerTagInfo);
        }
        return TripSCRMResult.success(resultList);
    }


    private boolean handleUnionId(FollowUserQuery query, String targetId) {
        query.setUnionId(targetId);
        return true;
    }

    private boolean handleExternalUserId(FollowUserQuery query, String targetId) {
        query.setExternalUserId(targetId);
        return true;
    }

    private boolean handleUid(FollowUserQuery query, String targetId) {
        String unionId = uicUtils.getUnionIdByUid(targetId);
        if (!StringUtils.hasText(unionId)) {
            return false;
        }

        query.setUnionId(unionId);
        return true;
    }
}
