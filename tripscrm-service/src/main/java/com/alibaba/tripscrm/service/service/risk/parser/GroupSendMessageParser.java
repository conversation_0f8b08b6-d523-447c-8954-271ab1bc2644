package com.alibaba.tripscrm.service.service.risk.parser;

import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionConfig;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionHitConfig;
import com.alibaba.tripscrm.service.service.risk.RiskActionContentParser;
import com.alibaba.tripscrm.service.util.system.BeanCopyUtils;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WechatGroupSendMsgRequest;
import com.taobao.hsf.invocation.Invocation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发送群聊消息 内容限流解析器
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class GroupSendMessageParser extends RiskActionContentParser {

    @Override
    protected RiskActionEnum riskAction() {
        return RiskActionEnum.GROUP_SEND_MESSAGE;
    }

    @Override
    public RiskActionConfig buildConfig() {
        // 父风控配置
        RiskActionConfig config = new RiskActionConfig();
        config.setActionCode(riskAction().getActionCode());
        config.setActionName(riskAction().getName());
        config.setEnable(false);
        // 构建子风控配置
        List<RiskActionConfig> subConfigs = new ArrayList<>();
        for (MessageTypeEnum messageType : MessageTypeEnum.values()) {
            if(StringUtils.isNotEmpty(messageType.getDescCn())) {
                RiskActionConfig subConfig = new RiskActionConfig();
                subConfig.setActionCode(RiskActionConfig.buildSubActionCode(riskAction().getActionCode(), messageType.name()));
                subConfig.setActionName(messageType.getDescCn());
                subConfig.setEnable(false);
                subConfigs.add(subConfig);
            }
        }
        config.setSubConfigs(subConfigs);
        return config;
    }

    @Override
    public RiskActionHitConfig hitRiskConfig(String corpId, String userId, RiskActionConfig configValue, Invocation invocation) {
        WechatGroupSendMsgRequest request = getRequest(invocation);
        if (request == null) {
            throw new RuntimeException("入参不合法");
        }
        // 设置父风控配置的命中次数
        RiskActionHitConfig hitConfig = BeanCopyUtils.copy(configValue, RiskActionConfig.class, RiskActionHitConfig.class);
        hitConfig.setHit(request.getMessageList().size());
        // 设置子风控配置的命中次数
        Map<String, RiskActionConfig> subConfigMap = configValue.getSubConfigs().stream().collect(Collectors.toMap(RiskActionConfig::getActionCode, Function.identity()));
        List<RiskActionHitConfig> hitSubConfigs = new ArrayList<>();
        Map<MessageTypeEnum, List<MessageBO>> messageGroup = request.getMessageList().stream().collect(Collectors.groupingBy(MessageBO::getMsgType));
        for (Map.Entry<MessageTypeEnum, List<MessageBO>> entry : messageGroup.entrySet()) {
            RiskActionConfig subConfig = subConfigMap.get(RiskActionConfig.buildSubActionCode(riskAction().getActionCode(), entry.getKey().getDesc()));
            RiskActionHitConfig hitSubConfig = BeanCopyUtils.copy(subConfig, RiskActionConfig.class, RiskActionHitConfig.class);
            hitSubConfig.setHit(entry.getValue().size());
            hitSubConfigs.add(hitSubConfig);
        }
        hitConfig.setHitSubConfigs(hitSubConfigs);
        return hitConfig;
    }

    /**
     * 获取请求入参
     *
     * @param invocation invocation
     * @return return
     */
    private WechatGroupSendMsgRequest getRequest(Invocation invocation) {
        // 获取实际的入参
        Object[] arguments = invocation.getMethodArgs();
        for (Object argument : arguments) {
            if (argument instanceof WechatGroupSendMsgRequest) {
                return (WechatGroupSendMsgRequest) argument;
            }
        }
        return null;
    }
}

