package com.alibaba.tripscrm.service.manager.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.risk.SourceTypeEnum;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.diamond.WechatGroupBlackListDiamond;
import com.alibaba.tripscrm.service.middleware.metaq.factory.DelayScheduleMethodFactory;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.DelayMetaqContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupInfoMemberVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.wechat.GroupRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.system.ErrorCodeConvertUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.api.service.RobotService;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import com.alibaba.tripzoo.proxy.model.RobotBO;
import com.alibaba.tripzoo.proxy.request.*;
import com.alibaba.tripzoo.proxy.result.GroupInviteJoinResponse;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/26 23:53
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatGroupAutoOpenWorkManager {
    private final GroupService groupService;
    private final GroupRelationService groupRelationService;
    private final WechatUserService wechatUserService;
    private final WechatGroupService wechatGroupService;
    private final MetaqProducer metaqProducer;
    private final LdbTairManager ldbTairManager;
    private final RobotService robotService;
    private final WechatGroupBlackListDiamond wechatGroupBlackListDiamond;

    @PostConstruct
    public void init() {
        DelayScheduleMethodFactory.register(DelayScheduleMethodEnum.WECHAT_USER_DO_OPEN_WORK_GROUP, this::doOpenWorkGroup);
    }

    /**
     * 每个账号关注群聊最大额度
     */
    @Switch(name = "groupOpenWorkMaxCount", description = "每个账号关注群聊最大额度")
    public static Integer groupOpenWorkMaxCount = 30;

    public void checkQuota(String corpId, Integer departmentId) {
        int size = 200;
        long wechatGroupMinId = 0L;
        int groupCount = 0;
        for (; ; ) {
            List<WechatGroupVO> wechatGroupList = wechatGroupService.listByMinIdLimit(wechatGroupMinId, size, true);
            if (CollectionUtils.isEmpty(wechatGroupList)) {
                PlatformLogUtil.logFail("【自动关注群聊】全量查询企微客户群，扫表结束", LogListUtil.newArrayList(wechatGroupMinId, departmentId, groupCount));
                break;
            }

            wechatGroupMinId = wechatGroupList.stream().map(WechatGroupVO::getId).max(Long::compareTo).orElse(0L);
            List<WechatGroupVO> needOpenWorkGroupList =
                    wechatGroupList.stream()
                            .filter(wechatGroupVO -> checkNeedCheckQuota(corpId, departmentId, wechatGroupVO))
                            .collect(Collectors.toList());
            groupCount += needOpenWorkGroupList.size();
        }

        WechatUserQuery query = new WechatUserQuery();
        query.setDepartmentIdList(Lists.newArrayList(departmentId));
        List<WechatUserDTO> wechatUserList = queryValidWechatUserList(corpId, departmentId, query);
        List<RobotBO> robotList = queryRobotInfo(wechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()), corpId);
        if (CollectionUtils.isEmpty(robotList)) {
            return;
        }

        List<String> bilinRobotUserIdList = robotList.stream()
                .filter(robotBO -> Objects.equals(robotBO.getIsvType(), IsvTypeEnum.BI_LIN))
                .map(RobotBO::getUserId)
                .collect(Collectors.toList());
        int wechatUserCount = bilinRobotUserIdList.size();
        PlatformLogUtil.logFail("【自动关注群聊】全量查询部门平台号，查询结束", LogListUtil.newArrayList(wechatUserCount));

        if (wechatUserCount * groupOpenWorkMaxCount < groupCount) {
            PlatformLogUtil.logFail("【比邻自动关注群聊】剩余额度不足", LogListUtil.newArrayList(corpId, departmentId, groupCount, wechatUserCount));
            DingTalkApi.sendTaskMessage(String.format("【比邻自动关注群聊】剩余额度不足，CorpId:%s，部门Id:%s，当前部门群聊数量:%s，可用平台号数量:%s", corpId, departmentId, groupCount, wechatUserCount));
            return;
        }

        PlatformLogUtil.logFail("【比邻自动关注群聊】剩余额度检查", LogListUtil.newArrayList(corpId, departmentId, groupCount, wechatUserCount));
        DingTalkApi.sendTaskMessage(String.format("【比邻自动关注群聊】剩余额度检查，CorpId:%s，部门Id:%s，当前部门群聊数量:%s，可用平台号数量:%s", corpId, departmentId, groupCount, wechatUserCount));
    }

    /**
     * 扫表，查找第一个需要执行关注操作的群聊，并执行关注操作
     * 如果关注成功，跳出扫表循环，等待关注操作异步执行结束后，再次进入该方法
     * 如果关注失败，则继续扫表，直到扫表结束
     */
    public void findFirstNeedOpenWorkGroupAndOpenWork(Long wechatGroupMinId) {
        int size = 10;
        for (; ; ) {
            List<WechatGroupVO> wechatGroupList = wechatGroupService.listByMinIdLimit(wechatGroupMinId, size, true);
            if (CollectionUtils.isEmpty(wechatGroupList)) {
                PlatformLogUtil.logFail("查询需要关注的企微客户群，扫表结束", LogListUtil.newArrayList(wechatGroupMinId));
                break;
            }

            List<WechatGroupVO> needOpenWorkGroupList = wechatGroupList.stream().filter(this::checkNeedOpenWork).collect(Collectors.toList());
            // 没查到
            if (CollectionUtils.isEmpty(needOpenWorkGroupList)) {
                wechatGroupMinId = wechatGroupList.stream().map(WechatGroupVO::getId).max(Long::compareTo).orElse(0L);
                continue;
            }

            // 取第一个
            WechatGroupVO wechatGroupVO = needOpenWorkGroupList.get(0);
            wechatGroupMinId = wechatGroupVO.getId();
            String tairRecordKey = TairConstant.WECHAT_GROUP_AUTO_OPEN_WORK_JOB_SCAN_PREFIX + wechatGroupVO.getOwnerUser().getCorpId() + "_" + wechatGroupVO.getChatId();
            PlatformLogUtil.logFail("查询需要关注的企微客户群，查询到了需要关注的群聊", LogListUtil.newArrayList(wechatGroupMinId, tairRecordKey, wechatGroupVO));

            Integer record = getFromTair(tairRecordKey);
            // 最近执行过了
            if (record > 0 || ldbTairManager.incr(tairRecordKey, 1, 0, 60 * 60 * 2) > 1) {
                PlatformLogUtil.logFail("查询需要关注的企微客户群，查询到了近期已经执行过的群聊", LogListUtil.newArrayList(wechatGroupMinId, tairRecordKey, wechatGroupVO));
                continue;
            }

            // 执行成功就跳出循环，这里是异步关注，关注成功后会继续执行
            if (openWorkGroup(wechatGroupVO, wechatGroupMinId)) {
                PlatformLogUtil.logFail("查询需要关注的企微客户群，查询到了需要关注的群聊，并成功执行关注操作，跳出循环，等待异步执行后继续执行", LogListUtil.newArrayList(wechatGroupVO));
                break;
            }

            PlatformLogUtil.logFail("查询需要关注的企微客户群，查询到了需要关注的群聊，执行关注操作失败，继续扫表", LogListUtil.newArrayList(wechatGroupVO));
        }
    }

    @AteyeInvoker(description = "关注群聊", paraDesc = "corpId&chatId&wechatGroupMinId")
    public void openWorkGroup(String corpId, String chatId, Long minId) {
        try {
            SpaceInfoThreadLocalUtils.setCorpId(corpId).setSourceType(SourceTypeEnum.SYSTEM_OPT);
            openWorkGroup(wechatGroupService.getWechatGroupVOByChatId(chatId), minId);
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    /**
     * @return 是否需要等待异步执行
     */
    @TairLock(value = "'openWorkGroup_' + #wechatGroupVO.chatId + '_' + #wechatGroupVO.ownerUser.corpId", expireSeconds = 120)
    public boolean openWorkGroup(WechatGroupVO wechatGroupVO, Long minId) {
        try {
            if (Objects.isNull(wechatGroupVO.getOwnerUser())) {
                PlatformLogUtil.logFail("执行关注操作，群主信息查询失败", LogListUtil.newArrayList(wechatGroupVO, minId));
                return false;
            }

            if (!checkNeedOpenWork(wechatGroupVO)) {
                PlatformLogUtil.logFail("执行关注操作，群聊无需关注", LogListUtil.newArrayList(wechatGroupVO, minId));
                return false;
            }

            PlatformLogUtil.logFail("执行关注操作，开始", LogListUtil.newArrayList(wechatGroupVO, minId));

            String corpId = wechatGroupVO.getOwnerUser().getCorpId();
            SpaceInfoThreadLocalUtils.setCorpId(corpId).setSourceType(SourceTypeEnum.SYSTEM_OPT);
            // 先判断群聊是否需要执行关注操作
            if (checkGroupHasOpenWork(wechatGroupVO)) {
                PlatformLogUtil.logFail("执行关注操作，群已满足关注条件，结束执行", LogListUtil.newArrayList(wechatGroupVO, minId));
                return false;
            }

            // 查询群内在线的企微成员
            List<WechatUserDTO> groupOnlineValidUserList = getGroupOnlineValidUserList(wechatGroupVO);
            List<RobotBO> robotList = queryRobotInfo(groupOnlineValidUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()), corpId);
            Set<String> bilinRobotUserIdSet = robotList.stream()
                    .filter(robotBO -> Objects.equals(robotBO.getIsvType(), IsvTypeEnum.BI_LIN))
                    .map(RobotBO::getUserId)
                    .collect(Collectors.toSet());
            PlatformLogUtil.logFail("执行关注操作，查询群内在线的企微成员", LogListUtil.newArrayList(wechatGroupVO, robotList));

            List<WechatUserDTO> groupOnlineValidBilinUserList = groupOnlineValidUserList.stream().filter(wechatUserDTO -> bilinRobotUserIdSet.contains(wechatUserDTO.getUserId())).collect(Collectors.toList());
            // 群内有关注剩余关注额度的企微成员
            List<WechatUserDTO> groupHasOpenWorkQuotaOnlineValidUserList = getHasOpenWorkQuotaUserList(groupOnlineValidBilinUserList);
            // 如果群内还有剩余关注额度的企微成员，直接执行关注操作
            if (!CollectionUtils.isEmpty(groupHasOpenWorkQuotaOnlineValidUserList)) {
                doOpenWorkGroup(groupHasOpenWorkQuotaOnlineValidUserList.get(0).getCorpId(), wechatGroupVO.getChatId(), groupHasOpenWorkQuotaOnlineValidUserList.get(0).getUserId(), null);
                return false;
            }

            // 群内在线的群主/管理员
            List<WechatUserDTO> groupOnlineOwnerOrAdmin = getGroupOnlineValidOwnerOrAdmin(wechatGroupVO);
            // 不在群内，在线，完成新人任务，有关注额度的平台号
            List<WechatUserDTO> hasOpenWorkQuotaNotInGroupWechatUserList = getHasOpenWorkQuotaNotInGroupOnlineValidWechatUserList(corpId, Collections.singletonList(wechatGroupVO.getOwnerUser().getDepartmentId()), wechatGroupVO);
            // 邀请剩余关注额度最多的账号入群
            asyncInviteJoinGroup(corpId, wechatGroupVO.getChatId(), groupOnlineOwnerOrAdmin, hasOpenWorkQuotaNotInGroupWechatUserList, minId);
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("【比邻自动关注群聊】策略执行失败", e.getMessage(), e, LogListUtil.newArrayList(wechatGroupVO, minId));

            if (e instanceof TripscrmException) {
                TripscrmException tripscrmException = (TripscrmException) e;
                DingTalkApi.sendTaskMessage(String.format("【比邻自动关注群聊】策略执行失败，群聊名称:%s，部门Id:%s，CorpId:%s，ChatId:%s，原因:%s", wechatGroupVO.getName(), wechatGroupVO.getOwnerUser().getDepartmentId(), wechatGroupVO.getOwnerUser().getCorpId(), wechatGroupVO.getChatId(), tripscrmException.getErrorMsg()));
            } else {
                DingTalkApi.sendTaskMessage(String.format("【比邻自动关注群聊】策略执行失败，群聊名称:%s，部门Id:%s，CorpId:%s，ChatId:%s，原因:%s", wechatGroupVO.getName(), wechatGroupVO.getOwnerUser().getDepartmentId(), wechatGroupVO.getOwnerUser().getCorpId(), wechatGroupVO.getChatId(), e.getMessage()));
            }
            return false;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    @TairLock(value = "'doOpenWorkGroup_' + #chatId + '_' + #corpId", expireSeconds = 120)
    public boolean doOpenWorkGroup(String corpId, String chatId, String userId, Long minId) {
        boolean openResult = false;
        try {
            SpaceInfoThreadLocalUtils.setCorpId(corpId).setSourceType(SourceTypeEnum.SYSTEM_OPT);
            WechatGroupOpenWorkRequest request = new WechatGroupOpenWorkRequest();
            request.setCorpId(corpId);
            request.setChatId(chatId);
            request.setUserId(userId);
            ResultDO<Void> result = groupService.openWorkGroup(request);
            if (!result.getSuccess()) {
                PlatformLogUtil.logFail("【比邻自动关注群聊】执行失败", LogListUtil.newArrayList(corpId, chatId, userId, minId));

                WechatGroupVO wechatGroupVO = wechatGroupService.getWechatGroupVOByChatId(chatId);
                TripSCRMErrorCode errorCode = ErrorCodeConvertUtils.convert(result.getResultCode());
                String reason = Objects.equals(errorCode, TripSCRMErrorCode.UNKNOWN) ? result.getResultMessage() : errorCode.getDescCn();
                DingTalkApi.sendTaskMessage(String.format("【比邻自动关注群聊】执行失败，群聊名称:%s，部门Id:%s，CorpId:%s，ChatId:%s，原因：%s", wechatGroupVO.getName(), wechatGroupVO.getOwnerUser().getCorpId(), wechatGroupVO.getChatId(), wechatGroupVO.getOwnerUser().getDepartmentId(), reason));
            } else {
                openResult = true;
                PlatformLogUtil.logFail("【比邻自动关注群聊】关注群聊成功", LogListUtil.newArrayList(corpId, chatId, userId, minId));
            }

            if (NumberUtils.validLong(minId)) {
                findFirstNeedOpenWorkGroupAndOpenWork(minId);
            }

            return openResult;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    public void doOpenWorkGroup(String param) {
        JSONObject jsonParma = JSON.parseObject(param);
        String corpId = jsonParma.getString("corpId");
        String chatId = jsonParma.getString("chatId");
        String userId = jsonParma.getString("userId");
        Long minId = jsonParma.getLong("wechatGroupMinId");
        doOpenWorkGroup(corpId, chatId, userId, minId);
    }

    private void asyncInviteJoinGroup(String corpId, String chatId, List<WechatUserDTO> groupOnlineOwnerOrAdmin, List<WechatUserDTO> hasOpenWorkQuotaNotInGroupWechatUserList, Long minId) {
        String userId = hasOpenWorkQuotaNotInGroupWechatUserList.get(0).getUserId();
        GroupInviteJoinRequest request = new GroupInviteJoinRequest();
        request.setCorpId(corpId);
        request.setUserId(groupOnlineOwnerOrAdmin.get(ThreadLocalRandom.current().nextInt(groupOnlineOwnerOrAdmin.size())).getUserId());
        request.setUserIdList(Lists.newArrayList(userId));
        request.setChatId(chatId);
        ResultDO<GroupInviteJoinResponse> result = groupService.asyncInviteJoinGroup(request);
        if (Objects.isNull(result) || !result.getSuccess()) {
            PlatformLogUtil.logFail("【比邻自动关注群聊】邀请企微成员入群失败", LogListUtil.newArrayList(request, result));
            TripSCRMErrorCode errorCode = ErrorCodeConvertUtils.convert(result.getResultCode());
            if (!Objects.equals(errorCode, TripSCRMErrorCode.UNKNOWN)) {
                throw new TripscrmException(errorCode);
            }
            throw new TripscrmException(TripSCRMErrorCode.INVITE_JOIN_GROUP_FAIL, result.getResultMessage());
        }

        // 根据间隔，计算每批执行时间
        long triggerTime = System.currentTimeMillis() + 70 * 1000L;
        DelayMetaqContext delayMetaqContext = buildDelayMetaqContext(corpId, chatId, userId, minId, triggerTime);
        boolean sendMqResult = metaqProducer.send(MQEnum.DELAY_MQ_SCHEDULING, "delay_wechat_user_open_work_group_" + chatId + "_" + userId, JSON.toJSONString(delayMetaqContext));
        if (!sendMqResult) {
            PlatformLogUtil.logFail("【比邻自动关注群聊】邀请企微成员入群，发送延迟一分钟后执行关注操作的消息失败", LogListUtil.newArrayList(corpId, chatId, userId, triggerTime, delayMetaqContext));
            throw new TripscrmException(TripSCRMErrorCode.SEND_OPEN_WORK_DELAY_MESSAGE_FAIL);
        }
        PlatformLogUtil.logFail("【比邻自动关注群聊】邀请企微成员入群，发送延迟一分钟后执行关注操作的消息成功", LogListUtil.newArrayList(corpId, chatId, userId, triggerTime, delayMetaqContext));
    }

    /**
     * 判断群聊是否满足关注条件
     */
    private boolean checkGroupHasOpenWork(WechatGroupVO wechatGroupVO) {
        String corpId = wechatGroupVO.getOwnerUser().getCorpId();
        String chatId = wechatGroupVO.getChatId();

        List<WechatGroupInfoMemberVO> wechatGroupInfoMemberList = groupRelationService.listByChatIdAndUserType(chatId, Lists.newArrayList(GroupUserTypeEnum.USER));
        if (CollectionUtils.isEmpty(wechatGroupInfoMemberList)) {
            return false;
        }

        List<String> userIdListInGroup = wechatGroupInfoMemberList.stream().map(WechatGroupInfoMemberVO::getUserId).collect(Collectors.toList());
        List<RobotBO> robotList = queryRobotInfo(userIdListInGroup, corpId);
        if (CollectionUtils.isEmpty(robotList)) {
            return false;
        }

        boolean hasValidRobot = robotList.stream().filter(robotBO ->
                (Objects.equals(robotBO.getStatus(), RobotStatusEnum.ONLINE) || Objects.equals(robotBO.getType(), RobotTypeEnum.ASSISTANT))
                        && Objects.equals(robotBO.getIsvType(), IsvTypeEnum.BAI_YE)
        ).map(RobotBO::getUserId).findAny().isPresent();

        if (hasValidRobot) {
            return true;
        }

        GetOpenWorkGroupUserList getOpenWorkGroupUserList = new GetOpenWorkGroupUserList();
        getOpenWorkGroupUserList.setChatId(chatId);
        getOpenWorkGroupUserList.setCorpId(corpId);
        // 查询已关注群聊的企微成员
        ResultDO<List<String>> result = groupService.getOpenWorkGroupUserList(getOpenWorkGroupUserList);
        if (Objects.isNull(result) || !result.getSuccess()) {
            PlatformLogUtil.logFail("【比邻自动关注群聊】判断群聊是否满足关注条件失败", LogListUtil.newArrayList(wechatGroupVO, result));

            throw new TripscrmException(TripSCRMErrorCode.CHECK_GROUP_HAS_OPEN_WORK_FAIL);
        }

        List<String> openWorkUserIdList = result.getModel();
        if (CollectionUtils.isEmpty(openWorkUserIdList)) {
            return false;
        }

        // 平台号不需要判断在线状态，扫码号需要在线
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(openWorkUserIdList);
        List<String> validWechatUserIdList = wechatUserList.stream().filter(wechatUserDTO ->
                        Objects.equals(RobotStatusEnum.of(wechatUserDTO.getOnlineStatus()), RobotStatusEnum.ONLINE)
                                || Objects.equals(RobotTypeEnum.of(wechatUserDTO.getRobotType()), RobotTypeEnum.ASSISTANT))
                .map(WechatUserDTO::getUserId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validWechatUserIdList)) {
            return false;
        }

        // proxy 接口返回的企微成员不一定在群内，需要再次判断
        return wechatGroupInfoMemberList.stream().anyMatch(wechatGroupInfoMemberVO -> validWechatUserIdList.contains(wechatGroupInfoMemberVO.getUserId()));
    }

    private List<RobotBO> queryRobotInfo(List<String> userIdListInGroup, String corpId) {
        BatchGetRobotInfoRequest request = new BatchGetRobotInfoRequest();
        request.setUserIdList(userIdListInGroup);
        request.setCorpId(corpId);
        ResultDO<List<RobotBO>> robotInfoResult = robotService.batchGetRobotInfo(request);
        if (Objects.isNull(robotInfoResult) || !robotInfoResult.getSuccess()) {
            PlatformLogUtil.logFail("【比邻自动关注群聊】获取机器人信息失败", LogListUtil.newArrayList(robotInfoResult));
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(robotInfoResult.getModel())) {
            return new ArrayList<>();
        }

        return robotInfoResult.getModel();
    }

    private List<WechatUserDTO> getGroupOnlineValidUserList(WechatGroupVO wechatGroupVO) {
        String corpId = wechatGroupVO.getOwnerUser().getCorpId();
        List<WechatGroupInfoMemberVO> wechatGroupInfoMemberList = groupRelationService.listByChatIdAndUserType(wechatGroupVO.getChatId(), Lists.newArrayList(GroupUserTypeEnum.USER));
        WechatUserQuery query = new WechatUserQuery();
        query.setUserIdList(Optional.ofNullable(wechatGroupInfoMemberList).orElse(new ArrayList<>()).stream().map(WechatGroupInfoMemberVO::getUserId).collect(Collectors.toList()));
        return queryOnlineValidWechatUserList(corpId, wechatGroupVO.getOwnerUser().getDepartmentId(), RobotTypeEnum.ASSISTANT, query);
    }

    private List<WechatUserDTO> getGroupOnlineValidOwnerOrAdmin(WechatGroupVO wechatGroupVO) {
        String corpId = wechatGroupVO.getOwnerUser().getCorpId();
        String ownerUserId = wechatGroupVO.getOwnerUserId();
        List<String> adminUserIdList = Optional.ofNullable(wechatGroupVO.getAdminUserIdList()).orElse(new ArrayList<>());
        List<String> userIdList = new ArrayList<>(adminUserIdList);
        userIdList.add(ownerUserId);
        WechatUserQuery query = new WechatUserQuery();
        query.setUserIdList(userIdList);
        List<WechatUserDTO> wechatUserList = queryOnlineValidWechatUserList(corpId, wechatGroupVO.getOwnerUser().getDepartmentId(), null, query);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            throw new TripscrmException(TripSCRMErrorCode.NO_ONLINE_OWNER_OR_ADMIN_IN_GROUP);
        }
        return wechatUserList;
    }

    /**
     * 获取群中还有关注额度的企微成员（按照剩余关注额度降序排序）
     */
    private List<WechatUserDTO> getHasOpenWorkQuotaUserList(List<WechatUserDTO> wechatUserDTOList) {
        if (CollectionUtils.isEmpty(wechatUserDTOList)) {
            return new ArrayList<>();
        }

        Map<String, WechatUserDTO> userId2WechatUserDTO = wechatUserDTOList.stream().collect(Collectors.toMap(WechatUserDTO::getUserId, wechatUserDTO -> wechatUserDTO, (v1, v2) -> v1));
        String corpId = wechatUserDTOList.get(0).getCorpId();
        List<UserWorkGroupCountBO> userWorkGroupCountList = getOpenWorkGroupCount(corpId, wechatUserDTOList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()));
        return userWorkGroupCountList
                .stream()
                .filter(userWorkGroupCountBO -> userWorkGroupCountBO.getCount() < groupOpenWorkMaxCount)
                .sorted(Comparator.comparingInt(UserWorkGroupCountBO::getCount))
                .map(userWorkGroupCountBO -> userId2WechatUserDTO.get(userWorkGroupCountBO.getUserId()))
                .collect(Collectors.toList());
    }

    private List<WechatUserDTO> getHasOpenWorkQuotaNotInGroupOnlineValidWechatUserList(String corpId, List<Integer> departmentIdList, WechatGroupVO wechatGroupVO) {
        List<WechatUserDTO> wechatUserList = new ArrayList<>();
        for (Integer departmentId : departmentIdList) {
            WechatUserQuery query = new WechatUserQuery();
            query.setDepartmentIdList(Collections.singletonList(departmentId));
            wechatUserList.addAll(queryOnlineValidWechatUserList(corpId, departmentId, RobotTypeEnum.ASSISTANT, query));
        }

        List<WechatUserDTO> hasOpenWorkQuotaUserList = getHasOpenWorkQuotaUserList(wechatUserList);
        List<WechatGroupInfoMemberVO> wechatGroupInfoMemberList = groupRelationService.listByChatIdAndUserType(wechatGroupVO.getChatId(), Lists.newArrayList(GroupUserTypeEnum.USER));
        Set<String> groupUserIdSet = wechatGroupInfoMemberList.stream().map(WechatGroupInfoMemberVO::getUserId).collect(Collectors.toSet());
        List<WechatUserDTO> hasOpenWorkQuotaNotInGroupWechatUserList = hasOpenWorkQuotaUserList.stream().filter(x -> !groupUserIdSet.contains(x.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasOpenWorkQuotaUserList)) {
            throw new TripscrmException(TripSCRMErrorCode.NO_ONLINE_VALID_HAS_OPEN_WORK_QUOTA_USER_IN_DEPARTMENT);
        }

        List<RobotBO> robotList = queryRobotInfo(hasOpenWorkQuotaNotInGroupWechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()), corpId);
        Set<String> bilinRobotUserIdSet = robotList.stream()
                .filter(robotBO -> Objects.equals(robotBO.getIsvType(), IsvTypeEnum.BI_LIN))
                .map(RobotBO::getUserId)
                .collect(Collectors.toSet());

        List<WechatUserDTO> result = hasOpenWorkQuotaNotInGroupWechatUserList.stream().filter(wechatUserDTO -> bilinRobotUserIdSet.contains(wechatUserDTO.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(robotList)) {
            throw new TripscrmException(TripSCRMErrorCode.NO_ONLINE_VALID_HAS_OPEN_WORK_QUOTA_USER_IN_DEPARTMENT);
        }

        return result;
    }

    private boolean checkNeedCheckQuota(String corpId, Integer departmentId, WechatGroupVO wechatGroupVO) {
        if (Objects.isNull(wechatGroupVO.getOwnerUser())) {
            PlatformLogUtil.logFail("【比邻自动关注群聊】检查关注额度，群主信息查询失败", LogListUtil.newArrayList(wechatGroupVO));
            return false;
        }

        if (!StringUtils.hasText(wechatGroupVO.getName())) {
            return false;
        }

        if (wechatGroupBlackListDiamond.checkInBlackList(corpId, wechatGroupVO.getChatId())) {
            return false;
        }

        // 不在指定部门
        if (!Objects.equals(corpId, wechatGroupVO.getOwnerUser().getCorpId()) || !Objects.equals(departmentId, wechatGroupVO.getOwnerUser().getDepartmentId())) {
            return false;
        }

        return true;
    }

    private boolean checkNeedOpenWork(WechatGroupVO wechatGroupVO) {
        if (Objects.isNull(wechatGroupVO.getOwnerUser())) {
            PlatformLogUtil.logFail("【比邻自动关注群聊】检查群聊是否需要关注，群主信息查询失败", LogListUtil.newArrayList(wechatGroupVO));
            return false;
        }

        List<String> corpDepartmentIdList = Optional.ofNullable(SwitchConfig.wechatUserClockInCorpDepartmentIdList).orElse(new ArrayList<>());
        // 不在指定部门
        if (!corpDepartmentIdList.contains(wechatGroupVO.getOwnerUser().getCorpId() + "_" + wechatGroupVO.getOwnerUser().getDepartmentId())) {
            return false;
        }

        if (!StringUtils.hasText(wechatGroupVO.getName())) {
            return false;
        }

        if (wechatGroupBlackListDiamond.checkInBlackList(wechatGroupVO.getOwnerUser().getCorpId(), wechatGroupVO.getChatId())) {
            return false;
        }

        // 只有未关注的群聊需要处理
        return !checkGroupHasOpenWork(wechatGroupVO);
    }

    private DelayMetaqContext buildDelayMetaqContext(String corpId, String chatId, String userId, Long minId, Long triggerTime) {
        DelayMetaqContext delayMetaqContext = new DelayMetaqContext();
        delayMetaqContext.setTriggerTime(triggerTime);
        delayMetaqContext.setTriggerType(TriggerTimeEnum.CUSTOM.getCode());
        delayMetaqContext.setSecondDelayLevel(true);
        delayMetaqContext.setFunctionType(DelayScheduleMethodEnum.WECHAT_USER_DO_OPEN_WORK_GROUP);
        delayMetaqContext.setDelayKey("delay_wechat_user_open_work_group_" + chatId + "_" + userId);

        JSONObject param = new JSONObject();
        param.put("corpId", corpId);
        param.put("userId", userId);
        param.put("chatId", chatId);
        param.put("wechatGroupMinId", minId);
        delayMetaqContext.setParam(JSON.toJSONString(param));
        return delayMetaqContext;
    }

    private List<WechatUserDTO> queryValidWechatUserList(String corpId, Integer departmentId, WechatUserQuery query) {
        query.setCorpId(corpId);
        query.setDepartmentIdList(Collections.singletonList(departmentId));
        query.setOnlineStatusList(Lists.newArrayList(RobotStatusEnum.ONLINE.getCode().byteValue(), RobotStatusEnum.OFFLINE.getCode().byteValue()));
        // 是否需要判断完成新手任务
        List<WechatUserDTO> wechatUserList = Optional.ofNullable(wechatUserService.listValidWechatUserByCondition(query)).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return wechatUserList;
        }

        Set<String> assistantRobotUserIdSet = wechatUserList.stream().filter(x -> Objects.equals(RobotTypeEnum.ASSISTANT.getCode(), x.getRobotType())).map(WechatUserDTO::getUserId).collect(Collectors.toSet());
        return wechatUserList.stream().filter(x -> assistantRobotUserIdSet.contains(x.getUserId())).collect(Collectors.toList());
    }

    private List<WechatUserDTO> queryOnlineValidWechatUserList(String corpId, Integer departmentId, RobotTypeEnum robotTypeEnum, WechatUserQuery query) {
        query.setCorpId(corpId);
        query.setDepartmentIdList(Lists.newArrayList(departmentId));
        query.setOnlineStatus(RobotStatusEnum.ONLINE.getCode().byteValue());
        List<WechatUserDTO> wechatUserList = Optional.ofNullable(wechatUserService.listValidWechatUserByCondition(query)).orElse(new ArrayList<>());
        if (Objects.isNull(robotTypeEnum)) {
            return wechatUserList;
        }

        if (CollectionUtils.isEmpty(wechatUserList)) {
            return wechatUserList;
        }

        Set<String> assistantRobotUserIdSet = wechatUserList.stream().filter(x -> Objects.equals(robotTypeEnum.getCode(), x.getRobotType())).map(WechatUserDTO::getUserId).collect(Collectors.toSet());
        return wechatUserList.stream().filter(x -> assistantRobotUserIdSet.contains(x.getUserId())).collect(Collectors.toList());
    }

    private List<UserWorkGroupCountBO> getOpenWorkGroupCount(String corpId, List<String> userIdList) {
        List<UserWorkGroupCountBO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        List<List<String>> partitions = Lists.partition(userIdList, 5);
        for (List<String> partition : partitions) {
            UserWorkGroupCountRequest request = new UserWorkGroupCountRequest();
            request.setCorpId(corpId);
            request.setUserIdList(partition);
            ResultDO<List<UserWorkGroupCountBO>> resultDO = groupService.getUserWorkGroupCount(request);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
                PlatformLogUtil.logFail("【比邻自动关注群聊】获取群中还有关注额度的企微成员失败", LogListUtil.newArrayList(partition, resultDO));
                throw new TripscrmException(TripSCRMErrorCode.GET_HAS_OPEN_WORK_QUOTA_USER_IN_GROUP_FAIL);
            }

            result.addAll(Optional.ofNullable(resultDO.getModel()).orElse(new ArrayList<>()));
        }

        return result;
    }

    private Integer getFromTair(String key) {
        Object o = ldbTairManager.get(key);
        return Integer.parseInt(String.valueOf(Optional.ofNullable(o).orElse(0)));
    }
}
