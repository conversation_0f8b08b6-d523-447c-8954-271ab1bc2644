package com.alibaba.tripscrm.service.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

@Configuration
@EnableCaching
public class CacheConfiguration {
    /**
     * 部门信息配置缓存管理器
     *
     * @return 部门信息缓存管理器
     */
    @Bean("departmentCacheManager")
    @Primary
    public CacheManager departmentCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                .initialCapacity(4)
                // 缓存的最大条数
                .maximumSize(10)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("taskMessageTypeCacheManager")
    public CacheManager taskMessageTypeCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(32)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("eventSourceBindTaskCacheManager")
    public CacheManager eventSourceBindTaskCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(1024)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("taskId2SpaceIdCacheManager")
    public CacheManager taskId2SpaceIdCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                // 缓存的最大条数
                .maximumSize(2048)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("taskInstanceCacheManager")
    public CacheManager taskInstanceCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(15, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(1024)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("taskInstanceListCacheManager")
    public CacheManager taskInstanceListCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(1024)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("wechatUserLockCacheManager")
    public CacheManager wechatUserLockCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(5, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(256)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("taskId2CorpIdCacheManager")
    public CacheManager taskId2CorpIdCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                // 缓存的最大条数
                .maximumSize(2048)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("taskInstanceId2TaskIdCacheManager")
    public CacheManager taskInstanceId2TaskIdCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                // 缓存的最大条数
                .maximumSize(1024)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("spaceId2CorpIdCacheManager")
    public CacheManager spaceId2CorpIdCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                // 缓存的最大条数
                .maximumSize(512)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("spaceId2SellerIdCacheManager")
    public CacheManager spaceId2SellerIdCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                // 缓存的最大条数
                .maximumSize(512)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("departmentId2SpaceIdCacheManager")
    public CacheManager departmentId2SpaceIdCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(512)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("tagGroupCacheManager")
    public CacheManager tagGroupCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(256)
        );
        return cacheManager;
    }

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("tagInfoCacheManager")
    public CacheManager tagInfoCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(512)
        );
        return cacheManager;
    }

    /**
     *  空间资源缓存
     *
     * @return 缓存管理器
     */

    @Bean("spaceResourceListManager")
    public CacheManager spaceResourceDoListManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(3, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(128)
        );
        return cacheManager;
    }

    /**
     * 过期时间：10秒，最大条数：64
     *
     * @return 缓存管理器
     */

    @Bean("common10SecondsAnd64SizeCacheManager")
    public CacheManager common10SecondsAnd64SizeCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(10, TimeUnit.SECONDS)
                // 缓存的最大条数
                .maximumSize(64)
        );
        return cacheManager;
    }

    /**
     * 过期时间：1分钟，最大条数：128
     *
     * @return 缓存管理器
     */

    @Bean("oneMinutesAnd128MaximumSizeCacheManager")
    public CacheManager oneMinutesAnd128MaximumSizeCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.MINUTES)
                // 缓存的最大条数
                .maximumSize(128)
        );
        return cacheManager;
    }

    /**
     * 过期时间：6小时，最大条数：512
     *
     * @return 缓存管理器
     */

    @Bean("sixHoursAnd512MaximumSizeCacheManager")
    public CacheManager sixHoursAnd512MaximumSizeCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(6, TimeUnit.HOURS)
                // 缓存的最大条数
                .maximumSize(512)
        );
        return cacheManager;
    }
}