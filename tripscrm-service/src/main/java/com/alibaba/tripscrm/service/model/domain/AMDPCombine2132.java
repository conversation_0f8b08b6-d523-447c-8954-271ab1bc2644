package com.alibaba.tripscrm.service.model.domain;

import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomain;
import com.alibaba.ihr.amdplatform.common.annotation.AmdpDomainField;

/**
 * 聚合单条数据返回值
 * 线上聚合-2132：https://amdp.alibaba-inc.com/amdp/portal.htm?id=2132&__id=jkai4
 */
public class AMDPCombine2132 {
        /**
         * 员工域
         */
        @AmdpDomain(
                code = "EMP_EMPLOYEE"
        )
        private EmpEmployee empEmployee;

        public EmpEmployee getEmpEmployee() {
            return this.empEmployee;
        }

        public void setEmpEmployee(EmpEmployee empEmployee) {
            this.empEmployee = empEmployee;
        }

        /**
         * 员工域
         */
        @AmdpDomain(
                code = "EMP_EMPLOYEE"
        )
        public static class EmpEmployee {
            /**
             * 员工工号
             */
            @AmdpDomainField(
                    code = "workNo",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String workNo;

            /**
             * 部门编号
             */
            @AmdpDomainField(
                    code = "deptNo",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String deptNo;

            /**
             * 花名
             */
            @AmdpDomainField(
                    code = "nickName",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String nickName;

            /**
             * 员工姓名
             */
            @AmdpDomainField(
                    code = "name",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String name;

            /**
             * 员工类型
             */
            @AmdpDomainField(
                    code = "empType",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String empType;

            /**
             * 工作状态
             */
            @AmdpDomainField(
                    code = "workStatus",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String workStatus;

            /**
             * 域账号
             */
            @AmdpDomainField(
                    code = "loginAccount",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String loginAccount;

            /**
             * 域账号前缀
             */
            @AmdpDomainField(
                    code = "adDomain",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String adDomain;

            /**
             * 域账号前缀/域账户
             */
            @AmdpDomainField(
                    code = "adAccount",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String adAccount;

            /**
             * 英文全名
             */
            @AmdpDomainField(
                    code = "englishName",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String englishName;

            /**
             * 英文姓
             */
            @AmdpDomainField(
                    code = "lastNameEn",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String lastNameEn;

            /**
             * 英文名
             */
            @AmdpDomainField(
                    code = "firstNameEn",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String firstNameEn;

            /**
             * 公司邮箱
             */
            @AmdpDomainField(
                    code = "buMail",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String buMail;

            /**
             * 头像信息
             */
            @AmdpDomainField(
                    code = "personalPhoto",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String personalPhoto;

            /**
             * 头像信息URL
             */
            @AmdpDomainField(
                    code = "personalPhotoUrl",
                    domainCode = "EMP_EMPLOYEE"
            )
            private String personalPhotoUrl;

            public String getWorkNo() {
                return this.workNo;
            }

            public void setWorkNo(String workNo) {
                this.workNo = workNo;
            }

            public String getDeptNo() {
                return this.deptNo;
            }

            public void setDeptNo(String deptNo) {
                this.deptNo = deptNo;
            }

            public String getNickName() {
                return this.nickName;
            }

            public void setNickName(String nickName) {
                this.nickName = nickName;
            }

            public String getName() {
                return this.name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getEmpType() {
                return this.empType;
            }

            public void setEmpType(String empType) {
                this.empType = empType;
            }

            public String getWorkStatus() {
                return this.workStatus;
            }

            public void setWorkStatus(String workStatus) {
                this.workStatus = workStatus;
            }

            public String getLoginAccount() {
                return this.loginAccount;
            }

            public void setLoginAccount(String loginAccount) {
                this.loginAccount = loginAccount;
            }

            public String getAdDomain() {
                return this.adDomain;
            }

            public void setAdDomain(String adDomain) {
                this.adDomain = adDomain;
            }

            public String getAdAccount() {
                return this.adAccount;
            }

            public void setAdAccount(String adAccount) {
                this.adAccount = adAccount;
            }

            public String getEnglishName() {
                return this.englishName;
            }

            public void setEnglishName(String englishName) {
                this.englishName = englishName;
            }

            public String getLastNameEn() {
                return this.lastNameEn;
            }

            public void setLastNameEn(String lastNameEn) {
                this.lastNameEn = lastNameEn;
            }

            public String getFirstNameEn() {
                return this.firstNameEn;
            }

            public void setFirstNameEn(String firstNameEn) {
                this.firstNameEn = firstNameEn;
            }

            public String getBuMail() {
                return this.buMail;
            }

            public void setBuMail(String buMail) {
                this.buMail = buMail;
            }

            public String getPersonalPhoto() {
                return this.personalPhoto;
            }

            public void setPersonalPhoto(String personalPhoto) {
                this.personalPhoto = personalPhoto;
            }

            public String getPersonalPhotoUrl() {
                return this.personalPhotoUrl;
            }

            public void setPersonalPhotoUrl(String personalPhotoUrl) {
                this.personalPhotoUrl = personalPhotoUrl;
            }
        }
    }

