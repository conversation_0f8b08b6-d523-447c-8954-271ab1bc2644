package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

import static com.alibaba.tripscrm.TripSCRMErrorCode.DATA_QUERY_PROCESSOR_NOT_FOUND;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:04:31
 */
@Component
public abstract class AbstractTaskDataProcessor {
    private static final Map<TaskType, AbstractTaskDataProcessor> TASK_DATA_PROCESSOR_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void init() {
        for(TaskType taskType : getTaskTypeList()){
            TASK_DATA_PROCESSOR_MAP.put(taskType, this);
        }

    }

    /**
     * 执行的任务类型
     *
     * @return
     */
    protected abstract List<TaskType> getTaskTypeList();

    public static TaskDataVO readAllData(TaskExecuteContext context) {
        TaskType taskType = TaskType.getByCode(context.getTaskInfoDOSnapshot().getType());
        if (!TASK_DATA_PROCESSOR_MAP.containsKey(taskType)) {
            PlatformLogUtil.logFail("【任务执行异常】获取数据查询处理器失败", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】获取数据查询处理器失败，任务ID:%s，实例ID:%s", context.getTaskId(), context.getInstanceId()));
            throw new TripscrmException(DATA_QUERY_PROCESSOR_NOT_FOUND);
        }

        return TASK_DATA_PROCESSOR_MAP.get(taskType).handleReadAllData(context);
    }

    /**
     * 获取任务执行数据
     *
     * @param context 任务执行上下文
     * @return
     */
    protected abstract TaskDataVO handleReadAllData(TaskExecuteContext context);
}
