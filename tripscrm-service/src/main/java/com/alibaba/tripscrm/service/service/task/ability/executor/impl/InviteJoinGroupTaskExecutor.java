package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.GroupUserOpenSearchManager;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatInfoQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.GroupRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.ErrorCodeEnum;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.request.GroupInviteJoinRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.OperatorResult;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * @description：
 * @Author：wangrui
 * @create：2024/8/15 17:30
 * @Filename：InviteJoinGroupTaskExecutor
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class InviteJoinGroupTaskExecutor extends AbstractTaskExecutor {
    /**
     * 群人数上限
     */
    private static final int MAX_MEMBER_COUNT = 495;
    /**
     * 每批随机取 chatId 数量
     */
    private static final int BATCH_SIZE = 3;

    private final GroupService groupService;
    private final TaskService taskService;
    private final ResourceRelationService resourceRelationService;
    private final WechatCustomerService wechatCustomerService;
    private final UicUtils uicUtils;
    private final GroupUserOpenSearchManager groupUserOpenSearchManager;
    private final GroupRelationService groupRelationService;
    private final WechatGroupService wechatGroupService;
    private final ActivityContextService activityContextService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> todoTaskList = todoTaskVO.getData();
        if (CollectionUtils.isEmpty(todoTaskList)) {
            return;
        }
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskList.get(0);
        String externalUserId = getFinalTargetId(context, taskDataBody);

        JSONObject extraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        if (!extraInfoJson.containsKey(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID)) {
            return;
        }

        Long wechatJoinGroupActivityId = extraInfoJson.getLong(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID);
        List<String> sendUserIdList = getSendUserIdList(context, externalUserId);
        if (CollectionUtils.isEmpty(sendUserIdList)) {
            throw new TripscrmException(TripSCRMErrorCode.ROBOT_USER_OF_TASK_OFFLINE_OR_WITHOUT_FRIENDSHIP_WITH_CUSTOMER);
        }

        //获取chatId
        String chatId = getChatId(wechatJoinGroupActivityId, sendUserIdList);
        if (StringUtils.isBlank(chatId)) {
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED, "暂无可用群聊");
        }

        WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(chatId);
        if (Objects.isNull(wechatGroupDO)) {
            return;
        }

        List<String> ownerAndAdminUserIdList = new ArrayList<>();
        List<String> adminUserIdList = Arrays.stream(Optional.ofNullable(wechatGroupDO.getAdminUser()).orElse("").split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        ownerAndAdminUserIdList.add(wechatGroupDO.getOwnerUser());
        ownerAndAdminUserIdList.addAll(adminUserIdList);
        List<String> sendUserIdListInGroup = ownerAndAdminUserIdList.stream().filter(sendUserIdList::contains).collect(Collectors.toList());
        String sendUserId = sendUserIdListInGroup.get(ThreadLocalRandom.current().nextInt(sendUserIdListInGroup.size()));

        GroupInviteJoinRequest groupInviteJoinRequest = new GroupInviteJoinRequest();
        groupInviteJoinRequest.setUserId(sendUserId);
        groupInviteJoinRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        groupInviteJoinRequest.setChatId(chatId);
        groupInviteJoinRequest.setExternalUserIdList(Collections.singletonList(externalUserId));

        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> asyncInviteCustomer(groupInviteJoinRequest));
        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        JSONObject data = new JSONObject();
        data.put("chatId", chatId);
        data.put("wechatUserId", sendUserId);
        data.put("extraInfo", context.getExtInfo());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        // 查询活动上下文
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        // 创建活动上下文
        createActivityContext(activityContext, context, chatId, taskDataBody);
    }

    private List<String> getSendUserIdList(TaskExecuteContext context, String externalUserId) {
        if (context.getExtInfo().containsKey("sendUserId")) {
            return Lists.newArrayList(context.getExtInfo().get("sendUserId").toString());
        }

        List<String> wechatUserIdList = Arrays.stream(Optional.ofNullable(context.getTaskInfoDOSnapshot().getSendUserId()).orElse("").split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        FollowUserQuery followUserQuery = new FollowUserQuery();
        followUserQuery.setExternalUserId(externalUserId);
        followUserQuery.setUserIdList(wechatUserIdList);
        followUserQuery.setRobotStatus(RobotStatusEnum.ONLINE.getCode());
        followUserQuery.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode()));
        FollowUserInfo followUserInfo = wechatCustomerService.listFollowUser(followUserQuery);

        if (Objects.isNull(followUserInfo) || CollectionUtils.isEmpty(followUserInfo.getRelationList())) {
            PlatformLogUtil.logFail("查询客户添加的企微成员信息，无好友关系", LogListUtil.newArrayList(externalUserId));
            return null;
        }

        return followUserInfo.getRelationList().stream().map(FollowUserInfo.CustomerRelation::getUserId).collect(Collectors.toList());
    }

    @Override
    protected ActivityTaskInfoBO getActivityContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        query.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_ACTIVITY_ID);
        query.setTargetId(String.valueOf(context.getTaskInfoDOSnapshot().getActivityId()));
        List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);

        if (CollectionUtils.isNotEmpty(activityTaskInfoBOS)) {
            return ActivityContextService.getNewestBo(activityTaskInfoBOS);
        }

        return super.getActivityContext(context, taskDataBody);
    }

    private void createActivityContext(ActivityTaskInfoBO activityContext, TaskExecuteContext context, String chatId, TaskDataVO.DataBodyVO taskDataBody) {
        List<WechatCustomerVO> wechatCustomerVOS = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(getFinalTargetId(context, taskDataBody)));
        if (CollectionUtils.isEmpty(wechatCustomerVOS)) {
            throw new TripscrmException(TripSCRMErrorCode.EXTERNAL_USERID_TO_UNION_ID_FAIL);
        }

        // 活动上下文信息
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(activityContextService.generateContextId());
        activityTaskInfoBO.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_UNION_ID);
        activityTaskInfoBO.setTargetId(wechatCustomerVOS.get(0).getUnionId());
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("dataTime", System.currentTimeMillis());
        extraJson.put("chatId", chatId);
        extraJson.put("contextId", activityContext.getContextId());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);
    }

    private String getChatId(Long wechatJoinGroupActivityId, List<String> sendUserIdList) {
        //获取任务id
        TaskQuery query = new TaskQuery();
        query.setActivityId(wechatJoinGroupActivityId);
        query.setType(TaskType.MANAGER_WECHAT_JOIN_GROUP.getCode());
        List<TaskInfoDO> taskInfoDOList = taskService.query(query);

        if (CollectionUtils.isEmpty(taskInfoDOList)) {
            PlatformLogUtil.logFail("empty taskIfo", LogListUtil.newArrayList(query));
            return null;
        }
        Long taskId = taskInfoDOList.get(0).getId();

        ResourceRelationQuery resourceRelationQuery = new ResourceRelationQuery();
        resourceRelationQuery.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
        resourceRelationQuery.setSourceType(ResourceTypeEnum.TASK.getCode().byteValue());
        resourceRelationQuery.setSourceId(taskId.toString());
        List<ResourceRelationDO> resourceRelationList = resourceRelationService.query(resourceRelationQuery);
        if (CollectionUtils.isEmpty(resourceRelationList)) {
            PlatformLogUtil.logFail("empty resourceRelationList", LogListUtil.newArrayList(resourceRelationQuery));
            return null;
        }
        List<String> chatIdList = resourceRelationList.stream().map(ResourceRelationDO::getTargetId).collect(Collectors.toList());

        WechatInfoQuery wechatInfoQuery = new WechatInfoQuery();
        wechatInfoQuery.setChatIdList(chatIdList);
        wechatInfoQuery.setUserIdList(sendUserIdList);
        wechatInfoQuery.setStatusEnum(GroupRelationStatusEnum.MEMBER);
        List<String> result = groupUserOpenSearchManager.getChatIds(wechatInfoQuery);
        if (CollectionUtils.isEmpty(result)) {
            PlatformLogUtil.logFail("empty newChatIdList", LogListUtil.newArrayList(wechatInfoQuery));
            return null;
        }
        return selectValidChatId(result);
    }

    private TripSCRMResult<String> asyncInviteCustomer(GroupInviteJoinRequest groupInviteJoinRequest) {
        ResultDO<OperatorResult> resultDO = groupService.asyncInvite(groupInviteJoinRequest);
        PlatformLogUtil.logFail("异步邀请群成员", LogListUtil.newArrayList(groupInviteJoinRequest, resultDO));
        if (Objects.isNull(resultDO)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED, resultDO.getResultMessage());
        }

        if (!resultDO.getSuccess()) {
            ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
            switch (errorCodeEnum) {
                case WECHAT_CUSTOMER_DATA_ERROR:
                case NO_FRIEND_RELATION:
                case WECHAT_USER_NOT_ONLINE:
                    return TripSCRMResult.fail(TripSCRMErrorCode.INVITE_JOIN_GROUP_FAIL);
                case RATE_LIMIT_FILTER:
                    return TripSCRMResult.fail(TripSCRMErrorCode.GATE_WAY_RATE_LIMIT);
                default:
                    return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }
        }
        if (Objects.isNull(resultDO.getModel()) || CollectionUtils.isEmpty(resultDO.getModel().getDetailList())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
        OperatorResult.Detail detail = resultDO.getModel().getDetailList().get(0);
        if (!detail.getResult()) {
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        return TripSCRMResult.success(detail.getRequestId(), resultDO.getResultMessage());
    }

    /**
     * 随机选择一个群聊
     *
     * @param chatIdList
     * @param userIdList
     * @return
     */
    private String selectValidChatId(List<String> chatIdList) {
        Collections.shuffle(chatIdList, new Random());

        for (int i = 0; i < chatIdList.size(); i += 3) {
            List<String> partition = chatIdList.stream()
                    .skip(i)
                    .limit(BATCH_SIZE)
                    .collect(Collectors.toList());

            Map<String, Integer> memberCounts = groupRelationService.batchGetGroupMemberCount(partition);
            for (Map.Entry<String, Integer> entry : memberCounts.entrySet()) {
                if (entry.getValue() < MAX_MEMBER_COUNT) {
                    return entry.getKey();
                }
            }
        }

        return null;
    }


    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return null;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_EXTERNAL_USERID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        // 只处理第一条
        if (taskDataBody.getContext().containsKey("externalUserId")) {
            return (String) taskDataBody.getContext().get("externalUserId");
        }

        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("empty targetId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!Lists.newArrayList(ActivityTargetTypeEnum.WX_UNION_ID, ActivityTargetTypeEnum.WX_EXTERNAL_USERID, ActivityTargetTypeEnum.TAOBAO_USER_ID).contains(activityTargetType)) {
            PlatformLogUtil.logFail("only accept unionId/externalUserId/userId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        String externalUserId = taskDataBody.getTargetId();
        // unionId 需要转 externalUserId
        if (ActivityTargetTypeEnum.WX_UNION_ID.equals(activityTargetType)) {
            externalUserId = wechatCustomerService.getExternalUserIdByUnionId(taskDataBody.getTargetId());
            if (StringUtils.isBlank(externalUserId)) {
                PlatformLogUtil.logFail("getExternalUserIdByUnionId empty", LogListUtil.newArrayList(taskDataBody));
                throw new TripscrmException(TripSCRMErrorCode.UNION_ID_TO_EXTERNAL_USER_ID_FAIL);
            }
        }

        // 淘宝uid -> unionId -> externalUserId
        if (ActivityTargetTypeEnum.TAOBAO_USER_ID.equals(activityTargetType)) {
            String unionId = uicUtils.getUnionIdByUid(taskDataBody.getTargetId());
            if (StringUtils.isBlank(unionId)) {
                PlatformLogUtil.logFail("getUnionIdByUserId empty", LogListUtil.newArrayList(taskDataBody));
                throw new TripscrmException(TripSCRMErrorCode.UID_TO_UNION_ID_FAIL);
            }

            externalUserId = wechatCustomerService.getExternalUserIdByUnionId(unionId);
            if (StringUtils.isBlank(externalUserId)) {
                PlatformLogUtil.logFail("getExternalUserIdByUnionId empty", LogListUtil.newArrayList(taskDataBody));
                throw new TripscrmException(TripSCRMErrorCode.UNION_ID_TO_EXTERNAL_USER_ID_FAIL);
            }
        }

        if (StringUtils.isBlank(externalUserId)) {
            PlatformLogUtil.logFail("externalUserId is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.EMPTY_EXTERNAL_USER_ID);
        }

        taskDataBody.getContext().put("externalUserId", externalUserId);
        return externalUserId;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.INVITE_JOIN_GROUP;
    }
}
