package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 微信客户关系信息查询（行程管家专用）
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerRelationForJourneyQuery extends BasePageRequest {
    /**
     * 淘宝uid
     */
    private String uid;
}
