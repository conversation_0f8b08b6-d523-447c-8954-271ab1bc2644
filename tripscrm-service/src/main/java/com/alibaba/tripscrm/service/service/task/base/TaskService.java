package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.model.domain.query.TaskInfoQuery;
import com.alibaba.tripscrm.service.model.domain.request.TaskTagRequest;
import com.alibaba.tripscrm.service.model.vo.task.TaskSelfVO;
import com.alibaba.tripscrm.service.model.dto.task.CrowdExpressionDTO;
import com.github.pagehelper.PageInfo;
import com.taobao.triptower.domain.PushCheckResultDO;

import java.util.List;

/**
 * 任务服务
 *
 * <AUTHOR>
 * @date 2023/5/25
 */
public interface TaskService {
    /**
     * 根据任务类型和活动ID查询任务
     *
     * @param query 任务类型
     * @return
     */
    List<TaskInfoDO> query(TaskQuery query);

    /**
     * 从 OpenSearch 分页查询
     *
     * @param query
     * @return
     */
    PageInfo<TaskInfoDO> pageQuery(TaskInfoQuery query);

    /**
     * 根据name查询任务
     *
     * @param name
     * @param spaceId
     * @return
     */
    TaskInfoDO queryTaskByNameAndSpaceId(String name, Long spaceId);

    /**
     * 重名校验
     *
     * @param spaceId
     * @param name
     * @param id
     * @return
     */
    boolean checkDuplicateName(Long spaceId, String name, Long id);

    /**
     * 修改活动Id为空的任务
     *
     * @param activityId 活动Id
     * @return 影响行数
     */
    Integer updateActivityIdWhereActivityIsNull(Long activityId);

    /**
     * 获取任务自定义参数 schema 模板
     *
     * @param taskType 任务类型
     * @return schema
     */
    String getCustomerConfigTemplate(TaskType taskType);

    /**
     * 根据ID查询任务
     *
     * @param id
     * @param spaceId
     * @return
     */
    TaskInfoDO queryTaskByIdAndSpaceId(Long id, Long spaceId);

    /**
     * 根据ID查询任务
     *
     * @param id
     * @return
     */
    TaskInfoDO queryTaskById(Long id);

    /**
     * 根据ID查询任务对应空间（走缓存）
     *
     * @param id
     * @return
     */
    Long querySpaceIdByTaskIdWithCache(Long id);

    /**
     * 根据条件查询任务列表-不带分页
     *
     * @param query 查询参数
     * @return
     */
    List<TaskInfoDO> list(TaskQuery query);

    /**
     * 根据事件源Id查询（读本地缓存）
     *
     * @param eventSourceId 事件源Id
     * @return List<TaskInfoDO>
     */
    List<TaskInfoDO> listByEventSourceIdAndStatus(Long eventSourceId, TaskStatusEnum status);

    /**
     * 查询待执行任务列表
     *
     * @param query
     * @return
     */
    List<TaskInfoDO> queryTodoTaskList(TaskQuery query) throws Exception;

    /**
     * 查询全部在线，并且还会执行的任务
     *
     * @param query
     * @return
     */
    List<TaskInfoDO> queryValidOnlineTaskList(TaskQuery query) throws Exception;

    /**
     * 任务保存/更新
     *
     * @return
     */
    Integer upsert(TaskInfoDO taskInfoDO);

    /**
     * 新建任务
     *
     * @param taskInfoDO
     * @return
     */
    Integer addTaskInfo(TaskInfoDO taskInfoDO);

    /**
     * 更新任务
     *
     * @param taskInfoDO
     * @param preCheck
     * @param doPost
     * @return
     */
    Integer updateTaskInfo(TaskInfoDO taskInfoDO, boolean preCheck, boolean doPost);

    /**
     * 更新任务
     *
     * @param taskInfoDO
     * @return
     */
    Integer updateTaskInfo(TaskInfoDO taskInfoDO, boolean preCheck);

    /**
     * 更新任务（不做更新后置处理）
     *
     * @param taskInfoDO
     * @return
     */
    Integer updateTaskInfoWithoutPost(TaskInfoDO taskInfoDO);

    /**
     * 删除任务
     *
     * @param id
     * @param spaceId
     * @return
     */
    Integer delete(Long id, Long spaceId,boolean preCheck);

    /**
     * 删除活动下任务
     *
     * @param activityId
     * @return
     */
    Integer deleteByActivityId(Long activityId);

    /**
     * 任务上线
     *
     * @param id
     * @param spaceId
     * @return
     */
    Boolean online(Long id, Long spaceId);

    /**
     * 任务下线
     *
     * @param id
     * @param spaceId
     * @return
     */
    Boolean offline(Long id, Long spaceId);

    /**
     * 活动上线
     *
     * @param activityId
     * @return
     */
    Boolean onlineActivity(Long activityId);

    /**
     * 活动下线
     *
     * @param activityId
     * @return
     */
    Boolean offlineActivity(Long activityId);

    /**
     * 校验任务是否合法，非法时会抛异常
     *
     * @param id
     * @return
     */
    TaskInfoDO checkTaskValid(Long id);


    /**
     * 校验任务是否合法，非法时会抛异常
     *
     * @param taskInfoDO
     * @param testFlag
     * @return
     */
    TaskInfoDO checkTaskValid(TaskInfoDO taskInfoDO, Boolean testFlag);

    /**
     * 乐观锁更新任务状态
     *
     * @param id        任务 Id
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     * @return 修改行数
     */
    Integer compareAndUpdateStatus(Long id, TaskStatusEnum oldStatus, TaskStatusEnum newStatus);

    /**
     * 任务是否在工作时间段
     * @param taskInfoDO
     * @return
     */
    boolean checkInWorkTimeWindow(TaskInfoDO taskInfoDO);

    /**
     * 解析圈人表达式
     */
    CrowdExpressionDTO evaluateCrowdExpression(String expression);

    /**
     * 获取任务工作时间段
     * @param taskInfoDO
     * @return
     */
    String[] getWorkTimeWindow(TaskInfoDO taskInfoDO);

    /**
     * 获取任务工作时间段，如果校验不通过，会抛出异常
     * @param taskInfoDO
     * @return
     */
    String[] checkAndGetWorkTimeWindow(TaskInfoDO taskInfoDO);

    /**
     * 任务自测链路
     *
     * @param taskSelfVO
     * @return
     */
    TripSCRMResult<String> selfTest(TaskSelfVO taskSelfVO);

    /**
     * 上线前测试检查
     *
     * @param taskId
     * @return
     */
    TripSCRMResult<List<PushCheckResultDO>> onlineCheck(Long taskId);

    /**
     * 给任务打标
     *
     * @param request 请求参数
     * @return 是否打标成功
     */
    Boolean addTags(TaskTagRequest request);

    /**
     * 删除任务标签
     *
     * @param request 请求参数
     * @return 是否打标成功
     */
    Boolean deleteTags(TaskTagRequest request);

    /**
     * 根据任务id获取corpId
     *
     * @return corpId
     */
    String getCorpIdByTaskId(Long taskId);
}
