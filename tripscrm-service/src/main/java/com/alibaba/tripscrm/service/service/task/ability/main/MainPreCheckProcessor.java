package com.alibaba.tripscrm.service.service.task.ability.main;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskInstanceQuery;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.TaskInstanceSaveRequest;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.base.AbTestBucketService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.pokemon.lock.client.api.TairLockManager;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.alibaba.tripscrm.TripSCRMErrorCode.*;

/**
 * 任务执行_主任务_前置校验
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MainPreCheckProcessor implements ExecuteProcessor {
    private final TaskService taskService;
    private final MaterialService materialService;
    private final AbTestBucketService abTestBucketService;
    private final TaskInstanceService taskInstanceService;
    private final TairLockManager tairLockManager;

    @Resource
    private MainPreCheckProcessor mainPreCheckProcessor;

    @Override
    @TaskExecuteLog("任务执行_主任务_前置校验")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        if (context.getTestFlag()) {
            return TripSCRMResult.success(null);
        }

        if (SwitchConfig.TASK_INSTANCE_CHECK_SWITCH) {
            Long instanceId = context.getInstanceId();
            boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
            if (!NumberUtils.validLong(instanceId) && isStreamTask) {
                // 如果实例已经创建，就不需要加锁
                if (Objects.nonNull(getTaskInstance(context))) {
                    processTaskInstance(context);
                    checkValid(context);
                    return TripSCRMResult.success(null);
                }
            }
        }


        // 这个地方执行时需要加锁的，因为涉及到主任务实例的创建
        tairLockManager.tryRunWithLock(
                "mainTaskPreCheck_" + context.getTaskId(),
                5000,
                () -> {
                    processTaskInstance(context);
                    checkValid(context);
                },
                () -> {
                    PlatformLogUtil.logFail("主任务执行-前置校验，获取锁失败！！", LogListUtil.newArrayList("mainTaskPreCheck_" + context.getTaskId()));
                    throw new TripscrmException(TripSCRMErrorCode.GET_TAIR_LOCK_FAIL);
                }
        );

        return TripSCRMResult.success(null);
    }

    private void checkValid(TaskExecuteContext context) {
        if (context.getTestFlag()) {
            return;
        }

        taskService.checkTaskValid(context.getTaskInfoDOSnapshot(), false);
        if (!taskService.checkInWorkTimeWindow(context.getTaskInfoDOSnapshot())) {
            throw new TripscrmException(TASK_EXECUTE_NOT_IN_WORK_TIME);
        }
    }

    public void processTaskInstance(TaskExecuteContext context) {
        Long instanceId = context.getInstanceId();
        if (!NumberUtils.validLong(instanceId)) {
            // 创建任务实例数据
            mainPreCheckProcessor.createTaskInstanceIfAbsent(context);
            return;
        }

        // 主任务重试场景，根据携带的任务实例ID查询出当时的任务数据快照
        // 指定了主任务实例Id，并且主任务实例当前正在运行
        TaskInstanceDO instanceDO = taskInstanceService.queryById(instanceId);
        if (Objects.isNull(instanceDO) || TaskInstanceStatusEnum.RUNNING.getStatus().equals(instanceDO.getStatus())) {
            throw new TripscrmException(TASK_INSTANCE_ALREADY_RUNNING);
        }

        // 将保存的任务快照设置到上下文
        context.setTaskInfoDOSnapshot(JSONObject.parseObject(instanceDO.getTaskConfig(), new TypeReference<TaskInfoDO>() {
        }));
        context.setMaterialSnapshot(JSONObject.parseObject(instanceDO.getMaterialInfo(), new TypeReference<MaterailInfoDO>() {
        }));
        context.setAbBucketListSnapshot(JSONObject.parseObject(instanceDO.getAbBucketList(), new TypeReference<List<AbTestBucketVO>>() {
        }));
    }

    /**
     * 创建主任务实例
     */
    @Transactional(rollbackFor = Exception.class)
    public void createTaskInstanceIfAbsent(TaskExecuteContext context) {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        if (isStreamTask) {
            createStreamTaskInstanceIfAbsent(context);
            return;
        }

        createBatchTaskInstanceIfAbsent(context);
    }

    private void createBatchTaskInstanceIfAbsent(TaskExecuteContext context) {
        TaskInstanceDO taskInstance = getTaskInstance(context);
        if (Objects.isNull(taskInstance)) {
            // 如果没有实例，则创建
            TaskInfoDO taskInfoDO = taskService.checkTaskValid(context.getTaskId());
            createTaskInstance(context, taskInfoDO);
            return;
        }

        if (Lists.newArrayList(TaskInstanceStatusEnum.RUN_SUCCESS, TaskInstanceStatusEnum.RUN_FAIL).contains(TaskInstanceStatusEnum.getByStatus(taskInstance.getStatus()))) {
            throw new TripscrmException(MAIN_TASK_INSTANCE_STATUS_RUN_FAIL_OR_RUN_SUCCESS);
        }

        context.setInstanceId(taskInstance.getId());
        context.setTaskInfoDOSnapshot(JSONObject.parseObject(taskInstance.getTaskConfig(), new TypeReference<TaskInfoDO>() {
        }));
        context.setMaterialSnapshot(JSONObject.parseObject(taskInstance.getMaterialInfo(), new TypeReference<MaterailInfoDO>() {
        }));
        context.setAbBucketListSnapshot(JSONObject.parseObject(taskInstance.getAbBucketList(), new TypeReference<List<AbTestBucketVO>>() {
        }));
    }

    private void createStreamTaskInstanceIfAbsent(TaskExecuteContext context) {
        TaskInstanceDO taskInstance = getTaskInstance(context);
        // 事件和接口触发，只需要在第一次执行时创建任务实例，所以需要先判断有没有实例已经创建出来了
        if (Objects.isNull(taskInstance)) {
            // 如果没有实例，则创建
            TaskInfoDO taskInfoDO = taskService.checkTaskValid(context.getTaskId());
            // 不支持接口&事件触发
            if (!Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(TaskTriggerTypeEnum.getByCode(taskInfoDO.getTriggerType()))) {
                throw new TripscrmException(TripSCRMErrorCode.NOT_SUPPORT_TRIGGER_BY_INTERFACE);
            }
            createTaskInstance(context, taskInfoDO);
            return;
        }

        TaskInfoDO taskInfoDOSnapShot = JSONObject.parseObject(taskInstance.getTaskConfig(), new TypeReference<TaskInfoDO>() {
        });
        // 不支持接口&事件触发
        if (!Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(TaskTriggerTypeEnum.getByCode(taskInfoDOSnapShot.getTriggerType()))) {
            throw new TripscrmException(TripSCRMErrorCode.NOT_SUPPORT_TRIGGER_BY_INTERFACE);
        }

        context.setInstanceId(taskInstance.getId());
        context.setTaskInfoDOSnapshot(taskInfoDOSnapShot);
        context.setMaterialSnapshot(JSONObject.parseObject(taskInstance.getMaterialInfo(), new TypeReference<MaterailInfoDO>() {
        }));
        context.setAbBucketListSnapshot(JSONObject.parseObject(taskInstance.getAbBucketList(), new TypeReference<List<AbTestBucketVO>>() {
        }));
    }

    private void createTaskInstance(TaskExecuteContext context, TaskInfoDO taskInfoDO) {
        TaskInstanceSaveRequest taskInstanceSaveRequest = new TaskInstanceSaveRequest();
        taskInstanceSaveRequest.setTaskId(context.getTaskId());
        taskInstanceSaveRequest.setTaskConfig(JSONObject.toJSONString(taskInfoDO));
        taskInstanceSaveRequest.setStatus(TaskInstanceStatusEnum.RUNNING.getStatus());
        taskInstanceSaveRequest.setStartTime(new Date());

        MaterailInfoDO materailInfoDO = materialService.queryById(taskInfoDO.getMaterialId());
        taskInstanceSaveRequest.setMaterialInfo(JSONObject.toJSONString(materailInfoDO));
        List<AbTestBucketVO> abTestBucketVOList = abTestBucketService.listByTaskId(taskInfoDO.getId());
        taskInstanceSaveRequest.setAbBucketList(JSONObject.toJSONString(abTestBucketVOList));

        Long taskInstanceId = taskInstanceService.add(taskInstanceSaveRequest);
        context.setInstanceId(taskInstanceId);
        context.setTaskInfoDOSnapshot(taskInfoDO);
        context.setMaterialSnapshot(materailInfoDO);
        context.setAbBucketListSnapshot(abTestBucketVOList);
    }

    private TaskInstanceDO getTaskInstance(TaskExecuteContext context) {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        TaskInstanceQuery query = new TaskInstanceQuery();
        query.setTaskId(context.getTaskId());
        List<TaskInstanceDO> taskInstanceList;
        if (isStreamTask) {
            // 流式触发，只需要查询正在运行中的任务实例
            // 流式主任务实例状态一般不会变，走缓存即可
            query.setStatus(TaskInstanceStatusEnum.RUNNING.getStatus());
            taskInstanceList = taskInstanceService.listWithCache(query);
        } else {
            // 批处理，需要查询调度时间之后是否有任务实例创建
            query.setGmtCreateStart(context.getScheduleTime());
            taskInstanceList = taskInstanceService.list(query);
        }

        if (CollectionUtils.isEmpty(taskInstanceList)) {
            return null;
        }

        if (taskInstanceList.size() == 1) {
            return taskInstanceList.get(0);
        }

        throw new TripscrmException(TripSCRMErrorCode.TASK_INSTANCE_DUPLICATE);
    }
}
