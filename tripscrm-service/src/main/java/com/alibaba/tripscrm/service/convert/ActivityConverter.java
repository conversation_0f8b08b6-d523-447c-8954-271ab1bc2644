package com.alibaba.tripscrm.service.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.activity.ActivityInfoQuery;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.model.vo.activity.ActivityVO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.enums.task.TaskOptionEnum;
import com.alibaba.tripscrm.service.model.domain.request.ActivityQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.ActivitySaveRequest;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.function.Function;

@Component
public class ActivityConverter {

    @Resource
    private AccountService accountService;

    public ActivityInfoDO saveRequest2Do(ActivitySaveRequest request) {
        ActivityInfoDO infoDO = new ActivityInfoDO();
        BeanUtils.copyProperties(request, infoDO);
        // 用户登录处理
        User user = accountService.getUserInWebThread();
        Date now = new Date();
        if (request.getId() == null) {
            infoDO.setGmtCreate(now);
            infoDO.setGmtModified(now);
            infoDO.setCreatorId(user.getUserId());
            infoDO.setCreatorName(user.getUserName());
            infoDO.setStatus(TaskOptionEnum.EDIT.getName());
            infoDO.setDeleted(0);
        } else {
            infoDO.setLastOperatorId(user.getUserId());
            infoDO.setLastOperatorName(user.getUserName());
        }
        infoDO.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        infoDO.setDescription(request.getDesc());
        List<Long> taskIdList = request.getTaskIdList();
        if (CollectionUtils.isNotEmpty(taskIdList)) {
            infoDO.setRelTaskIdList(JSON.toJSONString(taskIdList));
        }
        if (StringUtils.isNotBlank(request.getMemberIds())) {
            String[] memberIds = request.getMemberIds().split(",");
            infoDO.setMemberIds(String.join(",", memberIds));
        }
        return infoDO;
    }

    public static ActivityInfoQuery queryRequest2Do(ActivityQueryRequest request) {
        ActivityInfoQuery query = new ActivityInfoQuery();
        BeanUtils.copyProperties(request, query);
        query.setRelTaskIdList(Lists.newArrayList(request.getTaskId()));
        query.setId(request.getActivityId());
        query.setName(request.getActivityName());
        query.setCreatorId(request.getCreator());
        query.setTaskId(request.getTaskId());
        query.setTaskName(request.getTaskName());
        return query;
    }

    public static List<ActivityVO> doList2VoList(List<ActivityInfoDO> infoDOList, Function<ActivityInfoDO, Integer> getTaskCountFunction) {
        List<ActivityVO> result = Lists.newArrayList();

        if (CollectionUtils.isEmpty(infoDOList)) {
            return result;
        }

        for (ActivityInfoDO activityInfoDO : infoDOList) {
            result.add(do2Vo(activityInfoDO, getTaskCountFunction));
        }

        return result;
    }

    public static ActivityVO do2Vo(ActivityInfoDO infoDO, Function<ActivityInfoDO, Integer> getTaskCountFunction) {
        ActivityVO vo = new ActivityVO();
        BeanUtils.copyProperties(infoDO, vo);
        vo.setTaskCount(getTaskCountFunction.apply(infoDO));
        vo.setDesc(infoDO.getDescription());
        if (StringUtils.isNotBlank(infoDO.getStatus())) {
            vo.setStatus(TaskOptionEnum.getByName(infoDO.getStatus()));
        }
        return vo;
    }
}
