package com.alibaba.tripscrm.service.synchronizer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.domain.query.GroupTemplateQuery;
import com.alibaba.tripscrm.service.model.dto.group.GroupTemplateInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.group.GroupTemplateInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 群组信息同步器
 * 继承BaseLockedSynchronizer，实现群组信息的同步
 *
 * <AUTHOR>
 * @since 2025/1/13
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupTemplateSynchronizer extends BaseLockedSynchronizer<GroupTemplateInfoDTO> {
    private final GroupTemplateInfoService groupTemplateInfoService;

    @Override
    public void process(BaseSynchronizerContext<GroupTemplateInfoDTO> context) {
        try {
            // 参数校验
            if (!paramVerify(context)) {
                PlatformLogUtil.logFail("群组信息同步器参数校验失败", LogListUtil.newArrayList(context));
                return;
            }
            GroupTemplateInfoDTO data = context.getData();
            // 同步群组信息
            syncGroupInfo(data);

        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.SYNCHRONIZER_ERROR.getDescCn(),
                    e.getMessage(), e, LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.SYNCHRONIZER_ERROR);
        }
    }

    /**
     * 同步群组信息
     */
    private void syncGroupInfo(GroupTemplateInfoDTO data) {
        if (!StringUtils.hasText(data.getGroupTemplateId())) {
            PlatformLogUtil.logFail("群组ID为空，无法同步群组信息", LogListUtil.newArrayList(data));
            return;
        }
        // 查询群组信息 如果不存在就新增 存在就对比是否需要更新
        // 查询现有群组信息
        GroupTemplateQuery query = new GroupTemplateQuery();
        query.setGroupTemplateId(data.getGroupTemplateId());
        query.setCorpId(data.getCorpId());
        query.setPlatformType(data.getPlatformType());

        GroupTemplateInfoDTO oldValue = Optional.ofNullable(groupTemplateInfoService.select(query))
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0)).orElse(null);
        if (Objects.isNull(oldValue)) {
            PlatformLogUtil.logInfo("开始同步群组信息-新增", LogListUtil.newArrayList(data));

            if (groupTemplateInfoService.insertSelective(data) <= 0) {
                PlatformLogUtil.logFail("群组信息新增失败", LogListUtil.newArrayList(data));
            }
            return;
        }
        Boolean isChange = data.isDiff(oldValue);
        if (!isChange) {
            return;
        }
        data.setId(oldValue.getId());
        PlatformLogUtil.logInfo("开始同步群组信息-更新", LogListUtil.newArrayList(data));

        if (groupTemplateInfoService.updateSelective(data) <= 0) {
            PlatformLogUtil.logFail("群组信息更新失败", LogListUtil.newArrayList(data));
        }

    }


    /**
     * 参数校验
     */
    private boolean paramVerify(BaseSynchronizerContext<GroupTemplateInfoDTO> context) {
        if (Objects.isNull(context) || Objects.isNull(context.getData())) {
            PlatformLogUtil.logFail("群组信息同步器上下文为空", LogListUtil.newArrayList(context));
            return false;
        }
        GroupTemplateInfoDTO data = context.getData();

        if (!StringUtils.hasText(data.getCorpId()) || !StringUtils.hasText(data.getGroupTemplateId())) {
            PlatformLogUtil.logFail("群组信息同步器参数校验失败", LogListUtil.newArrayList(context));
            return false;
        }
        return true;
    }

    @Override
    public String getLockKey(BaseSynchronizerContext<GroupTemplateInfoDTO> context) {
        return TairConstant.GROUP_TEMPLATE_SYNCHRONIZER_LOCK_PREFIX + context.getData().getPlatformType().getCode() + "_" + context.getData().getGroupTemplateId();

    }

}

