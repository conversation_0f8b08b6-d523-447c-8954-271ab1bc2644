package com.alibaba.tripscrm.service.service.risk;

import com.alibaba.tripscrm.service.enums.risk.RateLimitTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionLimitTargetEnum;
import com.alibaba.tripscrm.service.model.domain.risk.RateLimitResult;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionConfig;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionHitConfig;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionLimitProcess;
import com.taobao.hsf.invocation.Invocation;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 行动项限流目标处理器抽象类，处理不同风控目标的风控配置、命中风控配置、限流控制、重置限流
 *
 * <AUTHOR>
 * @date 2024/07/04
 */
public abstract class RiskActionLimitTargetHandler {
    public static final Map<String, String> REDIS_SCRIPT = new ConcurrentHashMap<>();
    private static final Map<String, RiskActionLimitTargetHandler> HANDLER_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    public void init0() {
        HANDLER_MAP.put(riskTarget().getValue(), this);
    }

    public static RiskActionLimitTargetHandler get(RiskActionLimitTargetEnum target) {
        return HANDLER_MAP.get(target.getValue());
    }

    protected abstract RiskActionLimitTargetEnum riskTarget();

    /**
     * 获取风控配置
     *
     * @return 风控配置
     */
    public abstract RiskActionConfig fillConfig(RiskActionEnum riskAction, RiskActionConfig actionConfig);

    /**
     * 命中风控配置
     *
     * @return 风控配置
     */
    public abstract RiskActionHitConfig hitRiskConfig(String corpId, String userId, RiskActionConfig configValue, Invocation invocation);

    /**
     * 风控限流控制
     *
     * @return 风控结果
     */
    public abstract RateLimitResult limitControl(String corpId, String userId, RiskActionHitConfig hitConfig);

    /**
     * 获取限流进度
     */
    public abstract RiskActionLimitProcess getLimitProcess(String corpId, String userId, RiskActionConfig config);

    /**
     * 重置限流
     */
    public abstract void resetLimit(String corpId, String userId, RiskActionConfig config);

    /**
     * 根据文件名称读取工程中的文件内容
     *
     * @param fileName fileName
     * @return return
     */
    protected String readFileAsString(String fileName) {
        try {
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            try (InputStream inputStream = classLoader.getResourceAsStream(fileName)) {
                if (inputStream == null) {
                    throw new IllegalArgumentException("File not found in resources: " + fileName);
                }
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
                    return reader.lines().collect(Collectors.joining(System.lineSeparator()));
                }
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 构建限流结果
     *
     * @param result result
     * @return return
     */
    protected RateLimitResult buildResult(String result) {
        // 结果解析
        String[] results = result.split("\\|");
        if (results[0].equals("1")) {
            return new RateLimitResult(false);
        } else {
            String limitTypeCode = results[1];
            String key = results[2];
            String hitActionCode = key.split("_")[2];
            return new RateLimitResult(true, hitActionCode, RateLimitTypeEnum.fromCode(limitTypeCode));
        }
    }

    /**
     * 构建限流key
     *
     * @return LimitKeys
     */
    protected LimitKeys buildLimitKeys(String corpId, String userId, String actionCode) {
        String limitTag = "{" + corpId + "_" + userId + "}_" + actionCode + "_";
        long currentTimeMillis = System.currentTimeMillis();
        String minuteKey = limitTag + (currentTimeMillis / 1000 / 60) % 60;
        String hourKey = limitTag + (currentTimeMillis / 1000 / 60 / 60 + 8) % 24;
        String dayKey = limitTag + (currentTimeMillis / 1000 / 60 / 60 / 24 + 4) % 7;
        return new LimitKeys(minuteKey, hourKey, dayKey);
    }

    /**
     * 获取限流值
     *
     * @param rateLimitValue rateLimitValue
     * @return return
     */
    protected String getRateLimitValue(Integer rateLimitValue) {
        if (rateLimitValue != null) {
            return rateLimitValue.toString();
        } else {
            return "";
        }
    }

    @Data
    @AllArgsConstructor
    public static class LimitKeys {
        private String minuteKey;
        private String hourKey;
        private String dayKey;
    }
}