package com.alibaba.tripscrm.service.service.impl.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.mapper.tddl.CustomizerApprovalMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CustomizerApprovalDO;
import com.alibaba.tripscrm.dal.model.domain.data.CustomizerApprovalParam;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatContactMeDO;
import com.alibaba.tripscrm.service.enums.system.CustomizerApprovalStatusEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.opensearch.WxCustomizerOpenSearchManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.vo.customizer.CustomizerApprovalVO;
import com.alibaba.tripscrm.service.model.domain.query.CustomizerApplyQuery;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.wechat.CustomizerApprovalService;
import com.alibaba.tripscrm.service.service.wechat.WechatContactMeService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/21
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomizerApprovalServiceImpl implements CustomizerApprovalService {

    private final ActivityContextService activityContextService;
    private final CustomizerApprovalMapper customizerApprovalMapper;
    private final SpaceService spaceService;
    private final AccountService accountService;
    private final WechatContactMeService wechatContactMeService;
    private final MetaqProducer metaqProducer;
    private final WxCustomizerOpenSearchManager wxCustomizerOpenSearchManager;

    private static final String CUSTOMIZER_APPROVAL_KEY_PREFIX = "customizer_approval";
    private static final String CUSTOMIZER_MESSAGE_PREFIX = "customizer_message";
    private final LdbTairManager ldbTairManager;
    private final UicUtils uicUtils;

    @Override
    public Long apply(CustomizerApplyQuery query) {
        String user = accountService.getUserNameAndEmpId();
        CustomizerApprovalDO insertDO = new CustomizerApprovalDO();

        this.buildDO(query, insertDO);
        Date now = new Date();
        insertDO.setApplyTime(now);
        insertDO.setGmtCreate(now);
        insertDO.setGmtModified(now);
        insertDO.setEnv(EnvUtils.getEnvironment());
        insertDO.setStatus(CustomizerApprovalStatusEnum.PENDING_APPROVAL.getCode());
        if (Objects.nonNull(user)) {
            insertDO.setCreator(user);
            insertDO.setUpdater(user);
        }
        List<String> userIds = new ArrayList<>();
        userIds.add(query.getUserId());
        Long spaceId = SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId();
        WechatContactMeDO wechatContactMeDO = wechatContactMeService.create(userIds, true, activityContextService.generateContextId().toString(), spaceId);
        if (Objects.isNull(wechatContactMeDO)) {
            throw new RuntimeException("申请失败，个人活码生成失败");
        }
        insertDO.setWechatContactMeId(wechatContactMeDO.getId());
        insertDO.setState(wechatContactMeDO.getState());

        int insert = customizerApprovalMapper.insert(insertDO);
        this.updateCustomizerTair();
        return insert > 0 ? insertDO.getId() : null;
    }


    @Override
    public Long reApply(CustomizerApplyQuery query, CustomizerApprovalDO exist) {
        String user = accountService.getUserNameAndEmpId();
        CustomizerApprovalDO updateDO = new CustomizerApprovalDO();
        this.buildDO(query, updateDO);

        if (Objects.nonNull(user)) {
            updateDO.setUpdater(user);
        }
        updateDO.setApplyTime(new Date());
        updateDO.setApprovalTime(null);
        updateDO.setGmtModified(new Date());
        updateDO.setDes(null);
        updateDO.setEnv(EnvUtils.getEnvironment());
        updateDO.setStatus(CustomizerApprovalStatusEnum.PENDING_APPROVAL.getCode());

        updateDO.setId(exist.getId());
        int update = customizerApprovalMapper.updateByPrimaryKeySelective(updateDO);
        this.updateCustomizerTair();
        return update > 0 ? updateDO.getId() : null;
    }

    @Override
    public Map<Long, CustomizerApprovalDO> queryByWechatUserId(List<Long> wechatUserIds) {
        CustomizerApprovalParam param = new CustomizerApprovalParam();
        CustomizerApprovalParam.Criteria criteria = param.createCriteria();
        criteria.andWechatUserIdIn(wechatUserIds);
        List<CustomizerApprovalDO> customizerApprovalDOList = customizerApprovalMapper.selectByParam(param);
        return customizerApprovalDOList.stream()
                .collect(Collectors.toMap(CustomizerApprovalDO::getWechatUserId, Function.identity(), (a, b) -> a));
    }

    @Override
    public Boolean updateCustomizerApprovalStatus(Long id, CustomizerApprovalStatusEnum statusEnum) {
        String user = accountService.getUserNameAndEmpId();
        CustomizerApprovalDO updateDO = new CustomizerApprovalDO();
        updateDO.setId(id);
        updateDO.setStatus(statusEnum.getCode());
        if (Objects.nonNull(user)) {
            updateDO.setUpdater(user);
        }
        updateDO.setDes("");
        updateDO.setGmtModified(new Date());
        boolean update = customizerApprovalMapper.updateByPrimaryKeySelective(updateDO) > 0;
        this.updateCustomizerTair();
        return update;
    }

    @Override
    public CustomizerApprovalDO selectByWechatUserId(Long userId) {
        CustomizerApprovalParam param = new CustomizerApprovalParam();
        CustomizerApprovalParam.Criteria criteria = param.createCriteria();
        criteria.andWechatUserIdEqualTo(userId);
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());
        criteria.andSpaceIdEqualTo(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        List<CustomizerApprovalDO> exist = customizerApprovalMapper.selectByParam(param);
        if (CollectionUtils.isEmpty(exist)) {
            return null;
        }
        return exist.get(0);
    }

    @Override
    public CustomizerApprovalVO do2Vo(CustomizerApprovalDO customizerApprovalDO) {
        if (Objects.isNull(customizerApprovalDO)) {
            return null;
        }
        CustomizerApprovalVO vo = new CustomizerApprovalVO();
        BeanUtils.copyProperties(customizerApprovalDO, vo);

        CustomizerApprovalStatusEnum statusEnum = CustomizerApprovalStatusEnum.findByCode(customizerApprovalDO.getStatus());
        vo.setStatusDesc(Objects.isNull(statusEnum) ? null : statusEnum.getDetailDesc());

        String destCityCode = customizerApprovalDO.getDestCityCode();
        String destCityName = customizerApprovalDO.getDestCityName();
        if (StringUtils.isBlank(destCityCode) || StringUtils.isBlank(destCityName)) {
            return null;
        }
        String[] splitCityCode = destCityCode.split(",");
        String[] splitCityName = destCityName.split(",");
        List<CustomizerApprovalVO.DestInfo> destInfos = new ArrayList<>(splitCityCode.length);
        for (int i = 0; i < splitCityCode.length; i++) {
            CustomizerApprovalVO.DestInfo destInfo = new CustomizerApprovalVO.DestInfo();
            String cityCode = splitCityCode[i];
            String cityName = splitCityName[i];
            destInfo.setCityCode(Objects.equals("-", cityCode) ? null : cityCode);
            destInfo.setCityName(Objects.equals("-", cityName) ? null : cityName);
            destInfos.add(destInfo);
        }
        vo.setDestInfos(destInfos);
        return vo;
    }

    @Override
    public void sendSingleChatMessage(String userId, String uid, String state, String cropId) {
        String unionId = uicUtils.getUnionIdByUid(uid);
        if (StringUtils.isBlank(unionId)) {
            PlatformLogUtil.logFail("定制师发送私聊消息，unionId为空", LogListUtil.newArrayList(userId, uid, state, cropId));
            return;
        }

        JSONObject message = new JSONObject();
        message.put("userId", userId);
        message.put("unionId", unionId);
        message.put("corpId", cropId);
        String sendMsg = getSendMessage(uid, state);
        if (Objects.isNull(sendMsg)) {
            PlatformLogUtil.logFail("定制师发送私聊消息，消息内容为空", LogListUtil.newArrayList(userId, uid, state, cropId));
            return;
        }

        message.put("message", sendMsg);
        metaqProducer.send(MQEnum.CUSTOMIZER_MSG, "customizer_" + userId + "_" + unionId, "", message.toJSONString());
        PlatformLogUtil.logFail("定制师发送私聊消息", LogListUtil.newArrayList(userId, uid, unionId, state, cropId, message));
    }

    private String getSendMessage(String taoId, String state) {
        String extInfo = (String) ldbTairManager.get(String.format("%s_%s_%s", CUSTOMIZER_MESSAGE_PREFIX, taoId, state));
        // 定制师发送消息
        if (StringUtils.isBlank(extInfo)) {
            return null;
        }

        JSONObject extInfoMap = JSONObject.parseObject(extInfo);
        if (MapUtils.isEmpty(extInfoMap)) {
            return null;
        }

        return MapUtils.getString(extInfoMap, "defaultMessage");
    }

    @Override
    public void sendWelcomeMessage(String userId, String externalUserId, String taoId, String state, String cropId, String welcomeCode) {
        // 任意参数为空不展示
        if (StringUtils.isBlank(welcomeCode) || StringUtils.isBlank(state) || StringUtils.isBlank(taoId)) {
            return;
        }

        JSONObject message = new JSONObject();
        message.put("userId", userId);
        message.put("welcomeCode", welcomeCode);
        message.put("externalUserId", externalUserId);
        message.put("corpId", cropId);
        String sendMsg = getSendMessage(taoId, state);
        if (Objects.isNull(sendMsg)) {
            PlatformLogUtil.logFail("定制师发送欢迎语消息，消息内容为空", LogListUtil.newArrayList(userId, externalUserId, taoId, state, cropId, welcomeCode));
            return;
        }

        message.put("message", sendMsg);
        metaqProducer.send(MQEnum.CUSTOMIZER_WELCOME_MSG, "customizer_" + userId + "_" + externalUserId, "", message.toJSONString());
        PlatformLogUtil.logInfo("定制师发送欢迎语消息", LogListUtil.newArrayList(userId, externalUserId, taoId, state, cropId, welcomeCode, message));
    }

    @Override
    public void updateCustomizerTair() {
        CustomizerApprovalParam param = new CustomizerApprovalParam();
        CustomizerApprovalParam.Criteria criteria = param.createCriteria();
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());
        List<CustomizerApprovalDO> customizerApprovalDOList = customizerApprovalMapper.selectByParam(param);
        ldbTairManager.put(String.format("%s_%s", EnvUtils.getEnvironment(), CUSTOMIZER_APPROVAL_KEY_PREFIX), Objects.nonNull(customizerApprovalDOList) ? JSON.toJSONString(customizerApprovalDOList) : StringUtils.EMPTY);
    }

    private void buildDO(CustomizerApplyQuery query, CustomizerApprovalDO customizerApprovalDO) {
        customizerApprovalDO.setWechatUserId(query.getId());
        customizerApprovalDO.setName(query.getName());
        customizerApprovalDO.setWxUserId(query.getUserId());
        List<CustomizerApplyQuery.DestinationQuery> destinationQueryList = query.getDestinationQueryList();
        if (CollectionUtils.isNotEmpty(destinationQueryList)) {
            StringBuilder cityCodeBuilder = new StringBuilder();
            StringBuilder cityNameBuilder = new StringBuilder();
            for (CustomizerApplyQuery.DestinationQuery destinationQuery : destinationQueryList) {
                cityCodeBuilder.append(Objects.isNull(destinationQuery.getId()) ? "-" : destinationQuery.getId()).append(",");
                cityNameBuilder.append(StringUtils.isBlank(destinationQuery.getName()) ? "-" : destinationQuery.getName()).append(",");
            }
            customizerApprovalDO.setDestCityCode(cityCodeBuilder.toString());
            customizerApprovalDO.setDestCityName(cityNameBuilder.toString());
        }

        Long spaceId = SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId();
        customizerApprovalDO.setSpaceId(spaceId);
        SpaceDO spaceDO = spaceService.getById(spaceId);
        if (Objects.nonNull(spaceDO)) {
            customizerApprovalDO.setSellerName(spaceDO.getDescription());
            String extInfo = spaceDO.getExtInfo();
            if (StringUtils.isBlank(extInfo)) {
                return;
            }
            JSONObject jsonObject = JSONObject.parseObject(extInfo);
            if (Objects.isNull(jsonObject)) {
                return;
            }
            customizerApprovalDO.setSellerId(jsonObject.getString("sellerId"));
        }
    }
}
