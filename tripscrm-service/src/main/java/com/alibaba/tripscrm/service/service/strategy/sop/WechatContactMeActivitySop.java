package com.alibaba.tripscrm.service.service.strategy.sop;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterialTemplateInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialTemplateInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatUserVO;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatContactMeActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTemplateService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 个人活码SOP
 *
 * <AUTHOR>
 * @date 2023-12-30 14:50:08
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatContactMeActivitySop extends AbstractActivitySop<WechatContactMeActivitySopConfigVO> {
    private final TaskService taskService;
    private final MaterialService materialService;
    private final MaterialTemplateService materialTemplateService;
    private final TagInfoService tagInfoService;

    @Override
    protected void checkConfigValid(WechatContactMeActivitySopConfigVO config, Boolean isUpdate) {
        // 默认免验证
        if (Objects.isNull(config.getSkipVerify())) {
            config.setSkipVerify(true);
        }

        // 客户自动打标标签列表
        if (Objects.isNull(config.getCustomerAutoAddTagList())) {
            config.setCustomerAutoAddTagList(new ArrayList<>());
        }

        // 客户自动打标标签列表长度不可超过20
        if (config.getCustomerAutoAddTagList().size() > 20) {
            throw new TripscrmException("标签列表长度不可超过20");
        }

        // 企微号不可为空
        if (CollectionUtils.isEmpty(config.getUserIdList())) {
            throw new TripscrmException("企微号不可为空");
        }

        // 企微号列表长度不可超过100
        if (config.getUserIdList().size() > 100L) {
            throw new TripscrmException("企微号列表长度不可超过100");
        }

        // 没有配置欢迎语
        if (Optional.ofNullable(config.getWelcomeTaskMaterialId()).orElse(-1L) == -1L) {
            config.setWelcomeTaskMaterialId(-1L);
            return;
        }

        MaterailInfoDO materailInfoDO = materialService.queryById(config.getWelcomeTaskMaterialId());
        // 欢迎语素材不存在
        if (Objects.isNull(materailInfoDO)) {
            throw new TripscrmException("欢迎语素材不存在");
        }

        MaterialTemplateInfoQuery query = new MaterialTemplateInfoQuery();
        query.setId(materailInfoDO.getMaterialTemplateId());
        List<MaterialTemplateInfoDO> templateInfoDOList = materialTemplateService.list(query);
        if (CollectionUtils.isEmpty(templateInfoDOList)) {
            throw new TripscrmException("素材类型非欢迎语");
        }

        // 素材类型非欢迎语
        if (!Objects.equals(TaskType.PERSONAL_WELCOME, TaskType.getByCode(templateInfoDOList.get(0).getType()))) {
            throw new TripscrmException("素材类型非欢迎语");
        }
    }

    @Override
    protected void createTaskList(WechatContactMeActivitySopConfigVO config, Long activityId) {
        // 1.创建获取个人活码任务
        createGetWechatContactMeTask(config, activityId);
        // 2.创建欢迎语任务
        createPersonWelcomeTask(config, activityId);
        // 3.创建客户打标任务
        createWechatCustomerAddTagTask(config, activityId);
        // 4.创建邀请入群任务
        createInviteJoinGroupTask(config, activityId);
    }

    @Override
    protected void updateTaskList(WechatContactMeActivitySopConfigVO config) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        // 1.更新获取个人活码任务
        if (taskType2TaskInfoDO.containsKey(TaskType.GET_WECHAT_CONTACT_ME.getCode())) {
            TaskInfoDO getWechatContactMeTask = taskType2TaskInfoDO.get(TaskType.GET_WECHAT_CONTACT_ME.getCode());
            updateGetWechatContactMeTask(config, getWechatContactMeTask);
        } else {
            createGetWechatContactMeTask(config, config.getActivityId());
        }

        // 2.更新欢迎语任务
        if (taskType2TaskInfoDO.containsKey(TaskType.PERSONAL_WELCOME.getCode())) {
            TaskInfoDO welcomeTask = taskType2TaskInfoDO.get(TaskType.PERSONAL_WELCOME.getCode());
            updatePersonWelcomeTask(config, welcomeTask);
        } else {
            createPersonWelcomeTask(config, config.getActivityId());
        }

        // 3.更新客户打标任务
        if (taskType2TaskInfoDO.containsKey(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode())) {
            TaskInfoDO wechatCustomerAddTagTask = taskType2TaskInfoDO.get(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode());
            updateWechatCustomerAddTagTask(config, wechatCustomerAddTagTask);
        } else {
            createWechatCustomerAddTagTask(config, config.getActivityId());
        }

        // 4.创建邀请入群任务
        if (taskType2TaskInfoDO.containsKey(TaskType.INVITE_JOIN_GROUP.getCode())) {
            TaskInfoDO inviteJoinGroupTask = taskType2TaskInfoDO.get(TaskType.INVITE_JOIN_GROUP.getCode());
            updateInviteJoinGroupTask(config, inviteJoinGroupTask);
        } else {
            createInviteJoinGroupTask(config, config.getActivityId());
        }
    }

    @Override
    public SopTypeEnum getSopType() {
        return SopTypeEnum.WECHAT_CONTACT_ME;
    }

    @Override
    public void fillInfo(ActivitySopVO<WechatContactMeActivitySopConfigVO> activitySopVO) {
        WechatContactMeActivitySopConfigVO config = activitySopVO.getConfig();
        // 标签信息
        if (!CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(config.getCustomerAutoAddTagList());
            config.setCustomerAutoAddTagList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
            config.setCustomerAutoAddTagNameList(tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        }

        // 素材信息
        Long materialId = Optional.ofNullable(config.getWelcomeTaskMaterialId()).orElse(-1L);
        if (materialId > 0L) {
            MaterailInfoDO materailInfoDO = materialService.queryById(materialId);
            config.setWelcomeTaskMaterialName(materailInfoDO.getName());
        }

        // 任务数据统计
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        query.setType(TaskType.GET_WECHAT_CONTACT_ME.getCode());
        List<TaskInfoDO> taskList = taskService.query(query);
        TaskInfoDO getWechatContactMeTask = taskList.stream().max(Comparator.comparing(TaskInfoDO::getGmtCreate)).orElse(null);
        if (Objects.isNull(getWechatContactMeTask)) {
            return;
        }

        JSONObject extraInfo = TaskConfigProcessorFactory.getExtraInfo(getWechatContactMeTask);
        activitySopVO.getExtraInfo().put("qrCodeUrl", extraInfo.getString("qrCodeUrl"));
        activitySopVO.getExtraInfo().put("addCount", extraInfo.getInteger("addCount"));
        activitySopVO.getExtraInfo().put("state", extraInfo.getString("state"));
        activitySopVO.getExtraInfo().put("link", extraInfo.getString("link"));
        if (extraInfo.containsKey("wechatUserList")) {
            activitySopVO.getExtraInfo().put("wechatUserList", extraInfo.getObject("wechatUserList", new TypeReference<List<WechatUserVO>>() {
            }));
        }
    }

    private void createGetWechatContactMeTask(WechatContactMeActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.GET_WECHAT_CONTACT_ME).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("userIdList", config.getUserIdList());
        extraInfo.put("skipVerify", config.getSkipVerify());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建获取个人活码任务失败");
        }
    }

    private void createPersonWelcomeTask(WechatContactMeActivitySopConfigVO config, Long activityId) {
        // 没有配置欢迎语打标
        if (Objects.isNull(config.getWelcomeTaskMaterialId()) || Objects.equals(-1L, config.getWelcomeTaskMaterialId())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.PERSONAL_WELCOME).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setMaterialId(config.getWelcomeTaskMaterialId());
        taskInfoDO.setTriggerType(Integer.parseInt(TaskTriggerTypeEnum.EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerEventSourceId);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建欢迎语任务失败");
        }
    }

    private void createWechatCustomerAddTagTask(WechatContactMeActivitySopConfigVO config, Long activityId) {
        // 没有配置自动打标
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.WECHAT_CUSTOMER_ADD_TAG).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));

        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(TaskTriggerTypeEnum.EVENT.getCode()));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setEventSourceId(SwitchConfig.wechatCustomerAddTagEventSourceId);

        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("tagList", config.getCustomerAutoAddTagList());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建客户打标任务失败");
        }
    }

    private void createInviteJoinGroupTask(WechatContactMeActivitySopConfigVO config, Long activityId) {
        if (!NumberUtils.biggerThanZero(config.getWechatJoinGroupActivityId())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.INVITE_JOIN_GROUP).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setTriggerType(Integer.parseInt(TaskTriggerTypeEnum.EVENT.getCode()));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerEventSourceId);

        JSONObject extJs = StringUtils.hasText(taskInfoDO.getExtInfo()) ? JSONObject.parseObject(taskInfoDO.getExtInfo()) : new JSONObject();
        extJs.put(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID, config.getWechatJoinGroupActivityId());
        extJs.put(TaskConstant.WECHAT_JOIN_GROUP_STATE, getWechatContactMeState(config.getWechatJoinGroupActivityId()));
        taskInfoDO.setExtInfo(extJs.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建邀请入群任务失败");
        }
    }

    private void updateInviteJoinGroupTask(WechatContactMeActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了邀请入群配置
        if (Objects.isNull(config.getWechatJoinGroupActivityId()) || Objects.equals(-1L, config.getWechatJoinGroupActivityId())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        taskInfoDO.setTriggerType(Integer.parseInt(TaskTriggerTypeEnum.EVENT.getCode()));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerEventSourceId);

        JSONObject extJs = StringUtils.hasText(taskInfoDO.getExtInfo()) ? JSONObject.parseObject(taskInfoDO.getExtInfo()) : new JSONObject();
        extJs.put(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID, config.getWechatJoinGroupActivityId());
        extJs.put(TaskConstant.WECHAT_JOIN_GROUP_STATE, getWechatContactMeState(config.getWechatJoinGroupActivityId()));
        taskInfoDO.setExtInfo(extJs.toJSONString());

        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void updateGetWechatContactMeTask(WechatContactMeActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());

        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("userIdList", config.getUserIdList());
        extraInfo.put("skipVerify", config.getSkipVerify());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        taskInfoDO.setTriggerType(Integer.parseInt(TaskTriggerTypeEnum.INTERFACE.getCode()));
        taskInfoDO.setEventSourceId(-1L);
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void updatePersonWelcomeTask(WechatContactMeActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了欢迎语配置
        if (Objects.isNull(config.getWelcomeTaskMaterialId()) || Objects.equals(-1L, config.getWelcomeTaskMaterialId())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setMaterialId(config.getWelcomeTaskMaterialId());
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerEventSourceId);
        taskInfoDO.setTriggerType(Integer.parseInt(TaskTriggerTypeEnum.EVENT.getCode()));

        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void updateWechatCustomerAddTagTask(WechatContactMeActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了自动打标配置
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());

        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extInfo.put("tagList", config.getCustomerAutoAddTagList());
        taskInfoDO.setExtInfo(extInfo.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private String getWechatContactMeState(Long wechatJoinGroupActivityId) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(wechatJoinGroupActivityId);
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        TaskInfoDO taskInfoDO = taskType2TaskInfoDO.get(TaskType.GET_WECHAT_JOIN_GROUP.getCode());
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        return configJson.getString("contextId");
    }
}
