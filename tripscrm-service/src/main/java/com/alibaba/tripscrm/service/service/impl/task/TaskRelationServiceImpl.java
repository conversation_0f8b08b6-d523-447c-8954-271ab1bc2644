package com.alibaba.tripscrm.service.service.impl.task;

import com.alibaba.tripscrm.dal.mapper.tddl.TaskRelationMapper;
import com.alibaba.tripscrm.dal.model.domain.data.TaskRelationDO;
import com.alibaba.tripscrm.service.service.task.base.TaskRelationService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-11 15:57:32
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskRelationServiceImpl implements TaskRelationService {
    private final TaskRelationMapper taskRelationMapper;

    @Override
    public List<TaskRelationDO> listByTaskId(Long taskId) {
        if (!NumberUtils.validLong(taskId)) {
            return new ArrayList<>();
        }

        List<TaskRelationDO> list = taskRelationMapper.listByTaskId(taskId);
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : list;
    }

    @Override
    public List<TaskRelationDO> listByTargetIdAndTargetType(String targetId, Integer targetType) {
        if (!StringUtils.hasText(targetId) || !NumberUtils.validInteger(targetType)) {
            return new ArrayList<>();
        }

        List<TaskRelationDO> list = taskRelationMapper.listByTargetIdAndTargetType(targetId, targetType);
        return CollectionUtils.isEmpty(list) ? new ArrayList<>() : list;
    }

    @Override
    public Integer upsert(TaskRelationDO record) {
        if (!StringUtils.hasText(record.getTargetId()) || !NumberUtils.validLong(record.getTaskId()) || !NumberUtils.validInteger(record.getTargetType())) {
            return 0;
        }

        return taskRelationMapper.upsert(record);
    }

    @Override
    public Integer deleteByTaskIdAndTargetIdAndTargetType(TaskRelationDO record) {
        if (!StringUtils.hasText(record.getTargetId()) || !NumberUtils.validLong(record.getTaskId()) || !NumberUtils.validInteger(record.getTargetType())) {
            return 0;
        }

        return taskRelationMapper.deleteByTaskIdAndTargetIdAndTargetType(record.getTaskId(), record.getTargetId(), record.getTargetType());
    }
}
