package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.trip.trippc2.api.SmsBlacklistService;
import com.alibaba.trip.trippc2.request.SmsBlacklistRequest;
import com.alibaba.trip.trippc2.response.PushResult;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.CallPushActivityRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmBlackListDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.ScrmConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.system.BlackListTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.callpush.CallStageEnum;
import com.alibaba.tripscrm.service.enums.callpush.PushStageEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.task.CallPushManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.ScrmBlackListQuery;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.ScrmBlackListService;
import com.alibaba.tripscrm.service.service.fusionchat.ProfileService;
import com.alibaba.tripscrm.service.service.second.ShortLinkService;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.callpush.CallPushActivityRecordService;
import com.alibaba.tripscrm.service.service.factory.ThreadPoolExecutorFactory;
import com.alibaba.tripscrm.service.service.strategy.account.TbSessionManager;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.BaiYingService;
import com.alibaba.tripzoo.proxy.request.baiying.BaiYingUserInfo;
import com.alibaba.tripzoo.proxy.request.baiying.BatchAddCustomersParam;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.baiying.BaiYingAddUserRes;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.platform.protocol.TripCommonPlatformResult;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 外呼推送任务执行器
 *
 * <AUTHOR>
 * @since 2024/8/2 18:51
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CallPushTaskExecutor extends AbstractTaskExecutor {
    private final CallPushManager callPushManager;
    private final ScrmBlackListService scrmBlackListService;
    private final CallPushActivityRecordService callPushActivityRecordService;
    private final BaiYingService baiYingService;
    private final ActivityContextService activityContextService;
    private final ShortLinkService shortLinkService;
    private final LdbTairManager ldbTairManager;
    private final TbSessionManager tbSessionManager;
    private final TaskService taskService;
    private final ProfileService profileService;
    private final SmsBlacklistService smsBlacklistService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 前置校验
        preCheck(context);
        TaskDataVO.DataBodyVO dataBodyVO = todoTaskVO.getData().get(0);
        if (checkInBlackList(dataBodyVO)) {
            PlatformLogUtil.logFail("外呼推送任务执行器，命中黑名单", LogListUtil.newArrayList(context, dataBodyVO));
            throw new TripscrmException(TripSCRMErrorCode.HIT_BLACK_LIST);
        }

        // 匹配诸葛人群
        if (Objects.equals(TaskTriggerTypeEnum.EVENT, context.getTriggerType())){
            if (!isPushByMatchResult(context, dataBodyVO)) {
                return;
            }
        }

        CallPushActivityRecordDO callPushActivityRecordDO = buildCallPushActivityRecordDO(context, dataBodyVO);
        boolean fatigueResult = callPushManager.fatigueAcquire(callPushActivityRecordDO);
        // 疲劳度控制
        if (!fatigueResult) {
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_OVER_FATIGUE_LIMIT);
        }

        if (Objects.equals(CallStageEnum.of(callPushActivityRecordDO.getCallStage()), CallStageEnum.SAMPLING)) {
            PlatformLogUtil.logFail("外呼推送任务执行器，不推送，留样", LogListUtil.newArrayList(getFinalTargetId(context, dataBodyVO)));
            return;
        }

        // 调用外呼推送接口
        BatchAddCustomersParam param = new BatchAddCustomersParam();
        BaiYingUserInfo baiYingUserInfo = new BaiYingUserInfo();
        BeanUtils.copyProperties(callPushActivityRecordDO, baiYingUserInfo);
        param.setUserInfos(Lists.newArrayList(baiYingUserInfo));
        ResultDO<List<BaiYingAddUserRes>> resultDO = baiYingService.batchAddCustomers(param);
        if (Objects.isNull(resultDO) || !Optional.ofNullable(resultDO.getSuccess()).orElse(false) || CollectionUtils.isEmpty(resultDO.getModel())) {
            PlatformLogUtil.logFail("百应批量新增客户信息失败", LogListUtil.newArrayList(param, resultDO));
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED);
        }

        BaiYingAddUserRes addUserResult = resultDO.getModel().get(0);
        PushStageEnum pushStageEnum = Optional.ofNullable(addUserResult.getAddSuccess()).orElse(false) ? PushStageEnum.PUSH_SUCCESS : PushStageEnum.PUSH_FAIL;
        // 计算执行成功次数
        if (Objects.equals(pushStageEnum, PushStageEnum.PUSH_SUCCESS)) {
            incTaskExecuteTime(context);
        }
        // 更新外呼结果
        updateRecord(pushStageEnum, callPushActivityRecordDO);
        // 查询活动上下文
        ActivityTaskInfoBO activityContext = getActivityContext(context, dataBodyVO);
        // 创建活动上下文
        createActivityContext(activityContext, callPushActivityRecordDO, context, dataBodyVO);
    }

    private void createActivityContext(ActivityTaskInfoBO activityContext, CallPushActivityRecordDO callPushActivityRecordDO, TaskExecuteContext context, TaskDataVO.DataBodyVO dataBodyVO) {
        // 活动上下文信息
        ActivityTaskInfoBO uidActivityTaskInfoBO = new ActivityTaskInfoBO();
        uidActivityTaskInfoBO.setContextId(activityContextService.generateContextId());
        uidActivityTaskInfoBO.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        uidActivityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.TAOBAO_USER_ID);
        uidActivityTaskInfoBO.setTargetId(String.valueOf(callPushActivityRecordDO.getUid()));
        uidActivityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("dataTime", System.currentTimeMillis());
        extraJson.put("contextId", activityContext.getContextId());
        extraJson.put("callPushActivityRecordId", callPushActivityRecordDO.getId());
        uidActivityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(uidActivityTaskInfoBO);

        // 活动上下文信息
        ActivityTaskInfoBO cellPhoneActivityTaskInfoBO = new ActivityTaskInfoBO();
        cellPhoneActivityTaskInfoBO.setContextId(activityContextService.generateContextId());
        cellPhoneActivityTaskInfoBO.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        cellPhoneActivityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.PHONE_MOBILE);
        cellPhoneActivityTaskInfoBO.setTargetId(callPushActivityRecordDO.getCellPhone());
        cellPhoneActivityTaskInfoBO.setTagIdList(new ArrayList<>());
        cellPhoneActivityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(cellPhoneActivityTaskInfoBO);
        PlatformLogUtil.logFail("外呼推送任务执行，保存活动上下文", LogListUtil.newArrayList(uidActivityTaskInfoBO, cellPhoneActivityTaskInfoBO));
    }

    @Override
    protected ActivityTaskInfoBO getActivityContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        query.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_ACTIVITY_ID);
        query.setTargetId(String.valueOf(context.getTaskInfoDOSnapshot().getActivityId()));
        List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
        return ActivityContextService.getNewestBo(activityTaskInfoBOS);
    }

    private boolean checkInBlackList(TaskDataVO.DataBodyVO taskDataBody) {
        // 先判断淘宝Uid是否在黑名单
        ScrmBlackListQuery query = ScrmBlackListQuery.builder()
                .type(BlackListTypeEnum.CALL_PUSH)
                .targetType(ActivityTargetTypeEnum.TAOBAO_USER_ID)
                .targetIdList(Lists.newArrayList(taskDataBody.getTargetId()))
                .build();
        List<ScrmBlackListDO> scrmBlackListDOS = scrmBlackListService.selectByCondition(query);
        if (!CollectionUtils.isEmpty(scrmBlackListDOS)) {
            return true;
        }

        // 还需要判断手机号是否在黑名单
        String extInfo = taskDataBody.getExtInfo();
        JSONObject extInfoJson = JSONObject.parseObject(extInfo);
        String cellPhone = extInfoJson.getString(ScrmConstant.PHONE);

        // 手机号为空，则查询手机号
        if (!org.springframework.util.StringUtils.hasText(cellPhone)){
            cellPhone = getCellPhone(Long.parseLong(taskDataBody.getTargetId()));
        }
        //飞猪短信退订用户黑名单
        Boolean isSMSBlackUser = checkSMSBlackUser(taskDataBody.getTargetId(), cellPhone);
        if (isSMSBlackUser) {
            return true;
        }

        query = ScrmBlackListQuery.builder()
                .type(BlackListTypeEnum.CALL_PUSH)
                .targetType(ActivityTargetTypeEnum.PHONE_MOBILE)
                .targetIdList(Lists.newArrayList(cellPhone))
                .build();
        return !CollectionUtils.isEmpty(scrmBlackListService.selectByCondition(query));
    }

    private Boolean checkSMSBlackUser(String targetId, String cellPhone) {
        //淘id 诸葛
        TripSCRMResult<String> profileValue = profileService.getProfileValue(targetId, SwitchConfig.ZHUGE_REALTIME_SMS_BLACK_TAG_CODE);
        if(profileValue.isSuccess() && StringUtils.isNotBlank(profileValue.getData())) {
            return true;
        }
        //淘宝id
        SmsBlacklistRequest smsBlacklistRequest = new SmsBlacklistRequest();
        smsBlacklistRequest.setPhone(cellPhone);
        PushResult<Boolean> inBlacklist = smsBlacklistService.isInBlacklist(smsBlacklistRequest);
        if (inBlacklist.isSuccess() && inBlacklist.getData()) {
            return true;
        }
        return false;
    }

    private void updateRecord(PushStageEnum pushStageEnum, CallPushActivityRecordDO callPushActivityRecordDO) {
        CallPushActivityRecordDO updateCallPushActivityRecordDO = new CallPushActivityRecordDO();
        updateCallPushActivityRecordDO.setUid(callPushActivityRecordDO.getUid());
        updateCallPushActivityRecordDO.setActivityId(callPushActivityRecordDO.getActivityId());
        updateCallPushActivityRecordDO.setDayVersion(callPushActivityRecordDO.getDayVersion());
        updateCallPushActivityRecordDO.setPushStage(pushStageEnum.getCode());

        if (SwitchConfig.BAIYING_CALL_PUSH_VIP_CRAWD_ASYNC_UPDATE) {
            ThreadPoolExecutorFactory.VIP_PUSH_UPDATE_THREAD_POOL.submit(() -> {
                callPushActivityRecordService.updateByUidAndActivityIdAndDayVersion(updateCallPushActivityRecordDO);
            });
        } else {
            Integer result = callPushActivityRecordService.updateByUidAndActivityIdAndDayVersion(updateCallPushActivityRecordDO);
            PlatformLogUtil.logInfo("外呼推送任务执行，更新外呼结果", LogListUtil.newArrayList(updateCallPushActivityRecordDO, result));
        }
    }

    private CallPushActivityRecordDO buildCallPushActivityRecordDO(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        String extInfo = taskDataBody.getExtInfo();
        JSONObject extInfoJson = JSONObject.parseObject(extInfo);
        Long uid = Long.parseLong(getFinalTargetId(context, taskDataBody));
        String uidEncrypt = callPushManager.encrypt(String.valueOf(uid));
        String cellPhone = extInfoJson.getString(ScrmConstant.PHONE);
        if (!org.springframework.util.StringUtils.hasText(cellPhone)) {
            cellPhone = getCellPhone(uid);
        }
        String cellPhoneEncrypt = callPushManager.encrypt(cellPhone);

        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        callPushActivityRecordDO.setUid(uid);
        callPushActivityRecordDO.setUidEncrypt(uidEncrypt);
        callPushActivityRecordDO.setCellPhone(cellPhone);
        callPushActivityRecordDO.setCellPhoneEncrypt(cellPhoneEncrypt);
        callPushActivityRecordDO.setMsgUrl(buildMsgUrl(context.getTaskInfoDOSnapshot(), uidEncrypt));
        callPushActivityRecordDO.setDayVersion(DateUtils.getDayVersion(new Date()).intValue());

        double randomSamplingPercent = ThreadLocalRandom.current().nextDouble() * 100;
        double sample = MapUtils.getDouble(JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo()), TaskConstant.SAMPLING_PERCENT, 0D);

        if (NumberUtils.biggerThanZero(sample - randomSamplingPercent)) {
            callPushActivityRecordDO.setCallStage(CallStageEnum.SAMPLING.getCode());
        }

        return callPushActivityRecordDO;
    }

    private String buildMsgUrl(TaskInfoDO taskInfoDO, String userIdEncrypt) {
        String msgUrl;
        try {
            JSONObject extraInfoJson = JSONObject.parseObject(taskInfoDO.getExtInfo());
            String originLink = extraInfoJson.getString(TaskConstant.LANDING_PAGE_URL);
            if (StringUtils.isBlank(originLink)) {
                return originLink;
            }

            originLink = originLink.trim();
            // 个码
            if (extraInfoJson.containsKey(TaskConstant.WECHAT_CONTACT_ME_STATE)) {
                String state = extraInfoJson.getString(TaskConstant.WECHAT_CONTACT_ME_STATE);
                UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(originLink);
                uriComponentsBuilder.replaceQueryParam("state", state);
                uriComponentsBuilder.replaceQueryParam("userIdEncrypt", "encode_" + URLEncoder.encode(userIdEncrypt, StandardCharsets.UTF_8.name()));
                originLink = uriComponentsBuilder.build(true).toUriString();
                String miniProgramUrl = shortLinkService.convertToShortUrl(MaterialUtils.convert2MiniProgramMiddlePath(originLink));
                msgUrl = (StringUtils.isNotBlank(miniProgramUrl) && miniProgramUrl.startsWith("https://"))
                        ? miniProgramUrl.replace("https://", "")
                        : miniProgramUrl;
            } else {
                msgUrl = shortLinkService.convertToShortUrl(originLink);
            }

            PlatformLogUtil.logFail("外呼推送任务拼接落地页链接", LogListUtil.newArrayList(userIdEncrypt, originLink, msgUrl));
        } catch (Exception e) {
            PlatformLogUtil.logException("外呼推送任务拼接落地页链接，执行异常", e.getMessage(), e, LogListUtil.newArrayList(userIdEncrypt));
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED, e.getMessage());
        }

        if (StringUtils.isBlank(msgUrl)) {
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED, "落地页链接处理失败");
        }
        return msgUrl;
    }

    @AteyeInvoker(description = "外呼推送记录mock", paraDesc = "activityId&uid&timeStamp&callStage")
    public Integer mock(Long activityId, Long uid, Long timeStamp, Byte callStage) {
        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setActivityId(activityId);
        callPushActivityRecordDO.setUid(uid);
        callPushActivityRecordDO.setUidEncrypt(callPushManager.encrypt(String.valueOf(uid)));
        String cellPhone = tbSessionManager.getPhoneByUserId(uid);
        callPushActivityRecordDO.setCellPhone(cellPhone);
        callPushActivityRecordDO.setCellPhoneEncrypt(callPushManager.encrypt(cellPhone));

        TaskQuery query = new TaskQuery();
        query.setActivityId(activityId);
        query.setDeleted(0);
        List<TaskInfoDO> taskInfoList = taskService.query(query);
        TaskInfoDO taskInfoDO = taskInfoList.stream()
                .filter(a -> a.getType().equals(TaskType.CALL_PUSH.getCode()))
                .findFirst().orElse(null);


        callPushActivityRecordDO.setMsgUrl(buildMsgUrl(taskInfoDO, callPushActivityRecordDO.getUidEncrypt()));
        callPushActivityRecordDO.setDayVersion(DateUtils.getDayVersion(new Date(timeStamp)).intValue());
        callPushActivityRecordDO.setCallStage(callStage);
        return callPushActivityRecordService.insert(callPushActivityRecordDO);
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return null;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.TAOBAO_USER_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        return taskDataBody.getTargetId();
    }

    public void incTaskExecuteTime(TaskExecuteContext context) {
        ldbTairManager.incr(TairConstant.TASK_INSTANCE_BIZ_EXECUTE_SUCCESS_COUNT_PREFIX + context.getTaskId() + DateUtils.getDayVersion(new Date()), 1, 0, 24 * 60 * 60);
    }

    public void preCheck(TaskExecuteContext context) {
        Integer callPushLimit = getCallPushLimit(context);
        Long executeSuccessTime = Optional.ofNullable(ldbTairManager.get(TairConstant.TASK_INSTANCE_BIZ_EXECUTE_SUCCESS_COUNT_PREFIX + context.getTaskId() + DateUtils.getDayVersion(new Date())))
                .map(String::valueOf)
                .map(Long::parseLong)
                .orElse(0L);

        // 执行次数到达上限
        if (executeSuccessTime >= callPushLimit) {
            throw new TripscrmException(TripSCRMErrorCode.TASK_INSTANCE_BIZ_EXECUTE_MAX_COUNT_LIMIT);
        }
    }

    private Integer getCallPushLimit(TaskExecuteContext context) {
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        return MapUtils.getInteger(extInfo, TaskConstant.CALL_PUSH_LIMIT, SwitchConfig.CALL_PUSH_ACTIVITY_DEFAULT_PUSH_LIMIT);
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.CALL_PUSH;
    }

    /**
     * 事件触发场景，获取手机号
     * @param uid 用户id
     * @return 手机号
     */
    private String getCellPhone(Long uid){
        if (NumberUtils.biggerThanZero(uid)){
            return tbSessionManager.getPhoneByUserId(uid);
        }
        return null;
    }

    /**
     * 事件触发场景下，判断是否推送
     * @param context 上下文
     * @param dataBodyVO 任务数据
     * @return 是否推送
     */
    private Boolean isPushByMatchResult(TaskExecuteContext context, TaskDataVO.DataBodyVO dataBodyVO) {
        JSONObject taskConfig = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        // 诸葛人群Id为空，则不判断
        if (Objects.isNull(taskConfig.getJSONArray(TaskConstant.CALL_PUSH_CROWD_IDS)) || Objects.isNull(taskConfig.getJSONArray(TaskConstant.CALL_PUSH_CROWD_IDS).get(0))){
            PlatformLogUtil.logFail("外呼推送任务执行，未配置人群", LogListUtil.newArrayList(getFinalTargetId(context, dataBodyVO), taskConfig, taskConfig.getJSONArray(TaskConstant.CALL_PUSH_CROWD_IDS)));
            return true;
        }

        List<Long> crowdIds = taskConfig.getObject(TaskConstant.CALL_PUSH_CROWD_IDS, new TypeReference<List<Long>>() {});
        TripCommonPlatformResult<Map<Long, Boolean>> uidInCrowds = callPushManager.isUidInCrowds(Long.parseLong(getFinalTargetId(context, dataBodyVO)), crowdIds);
        // 查询客户所在人群结果失败
        if (Objects.isNull(uidInCrowds) || !uidInCrowds.isSuccess() || Objects.isNull(uidInCrowds.getData())){
            PlatformLogUtil.logFail("外呼推送任务执行，查询人群失败", LogListUtil.newArrayList(getFinalTargetId(context, dataBodyVO), uidInCrowds, crowdIds));
            return false;
        }

        // 查询客户所在人群结果全匹配为不确定状态
        if (uidInCrowds.getData().values().stream().allMatch(Objects::isNull)){
            PlatformLogUtil.logFail("外呼推送任务执行器，无法确定用户是否命中人群", LogListUtil.newArrayList(getFinalTargetId(context, dataBodyVO), uidInCrowds, crowdIds));
            return true;

        }
        // 查询客户所在人群结果

        return taskConfig.getBoolean(TaskConstant.CALL_PUSH_IN_POPULATION) ?
                uidInCrowds.getData().values().stream().filter(Objects::nonNull).anyMatch(Boolean.TRUE::equals) :
                uidInCrowds.getData().values().stream().filter(Objects::nonNull).allMatch(Boolean.FALSE::equals);
    }
}
