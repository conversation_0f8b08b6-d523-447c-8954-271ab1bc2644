package com.alibaba.tripscrm.service.service.fusionchat;

import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.vo.ItemSearchVO;
import com.alibaba.tripscrm.service.model.domain.fusionchat.vo.ItemVO;
import com.alibaba.tripscrm.service.model.domain.request.GetItemListRequest;
import com.alibaba.tripscrm.service.model.domain.request.MaterialToMessageRequest;
import com.alibaba.tripscrm.service.model.vo.fusionchat.FusionChatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.fusionchat.FusionChatGroupVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupInfoVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupWithWorkVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatSingleInfoVO;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.ManagerWechatUser;

import java.util.List;

/**
 * 聚合聊天 Manager
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
public interface FusionChatService {
    /**
     * 获取当前账号拥有权限的企微成员列表
     *
     * @param accountId accountId
     * @param spaceId   spaceId
     * @return return
     */
    List<WechatUserDTO> listManagerWechatUserDTOList(String accountId, Long spaceId);

    /**
     * 获取当前账号拥有权限的企微成员列表
     *
     * @param accountId accountId
     * @param spaceId   spaceId
     * @return return
     */
    ManagerWechatUser getManagerUser(String userId, String accountId, Long spaceId);

    /**
     * 获取当前账号拥有权限的企微成员列表
     *
     * @param accountId accountId
     * @param spaceId   spaceId
     * @return return
     */
    List<ManagerWechatUser> listManagerUsers(String accountId, Long spaceId);

    /**
     * 获取当前账号锁定的企微成员列表
     *
     * @param accountId accountId
     * @param spaceId   spaceId
     * @return return
     */
    List<ManagerWechatUser> listLockUsers(String accountId, Long spaceId);

    /**
     * 校验群会话名称（聚合聊天的群名称不能重复）
     *
     * @param groupName 群名称
     */
    void checkGroupName(String userId, String groupName);

    /**
     * 锁定/解锁企微成员账号
     *
     * @param account account
     * @param spaceId spaceId
     * @param userId  userId
     * @param lock    lock
     */
    void lockUser(User account, Long spaceId, String userId, Boolean lock);

    /**
     * 获取某个群聊基本信息
     *
     * @param chatId chatId
     * @param userId userId
     * @return return
     */
    WechatGroupInfoVO getGroupChatInfo(String chatId, String searchKey, String userId);

    /**
     * 获取某个单聊基本信息
     *
     * @param chatId   chatId
     * @param chatType chatType
     * @return return
     */
    WechatSingleInfoVO getSingleChatInfo(String chatId, Integer chatType);

    List<WechatGroupWithWorkVO> listGroupByUserId(String userId, String searchKey, String externalUserId);

    /**
     * 关注群聊（关注群聊后，将会收到该群聊消息的回调）
     *
     * @param userId userId
     * @param chatId chatId
     */
    void openWorkGroup(String userId, String chatId);

    /**
     * 取消关注群聊（取消关注群聊后，将不会收到该群聊消息的回调）
     *
     * @param userId userId
     * @param chatId chatId
     */
    void cancelWorkGroup(String userId, String chatId);

    /**
     * 根据客户的unionId获取客户信息
     *
     * @param userId         用户id
     * @param externalUserId 客户id
     * @return 客户信息
     */
    FusionChatCustomerVO getCustomerInfo(String userId, String externalUserId);

    /**
     * 根据群聊id获取群聊信息
     *
     * @param chatId 群聊id
     * @param userId 用户id
     * @return 群聊信息
     */
    FusionChatGroupVO getGroupInfo(String chatId, String userId);

    /**
     * @param title
     * @param pageSize
     * @param pageNum
     * @return
     */
    Object getWxCommunityContentList(String title, Integer pageNum, Integer pageSize);

    /**
     * 转化素材为消息
     *
     * @param request 请求体
     * @return 消息列表
     */
    List<FusionChatMessageBody> toMaterialMessage(MaterialToMessageRequest request);

    /**
     * 获取物料列表
     *
     * @param request 请求体
     * @return 结果列表
     */
    ItemSearchVO<ItemVO> searchItem(GetItemListRequest request);
}