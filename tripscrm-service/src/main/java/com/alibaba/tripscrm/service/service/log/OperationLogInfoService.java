package com.alibaba.tripscrm.service.service.log;


import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoDO;
import com.alibaba.tripscrm.service.enums.system.OperationLogStatusEnum;
import com.alibaba.tripscrm.service.model.domain.query.OperationLogInfoQuery;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface OperationLogInfoService {

    /**
     * 主键查询
     * @param id 主键
     * @return 日志对象
     */
    OperationLogInfoDO queryById(Long id);

    /**
     * 新增日志信息
     * @param operationLogInfoDO 日志对象
     * @return 新增结果
     */
    Boolean insert(OperationLogInfoDO operationLogInfoDO);

    /**
     * 删除日志
     * @param id 日志id
     * @return 删除结果
     */
    Boolean deleteById(Long id);

    /**
     * 更新日志信息
     * @param operationLogInfoDO 日志对象
     * @return 更新结果
     */
    Boolean updateById(OperationLogInfoDO operationLogInfoDO);

    /**
     * 根据条件查询数量
     * @param query 查询条件
     * @return 查询数量
     */
    Long countByQuery(OperationLogInfoQuery query);

    /**
     * 指定日志操作状态修改
     * @param id 日志id
     * @param statusEnum 修改的状态
     * @return 更新结果
     */
    Boolean updateStatusById(Long id, OperationLogStatusEnum statusEnum);

    /**
     * 查询日志列表
     * @param query 条件
     * @return 操作日志信息
     */
    List<OperationLogInfoDO> queryLogInfoDOList(OperationLogInfoQuery query);

    /**
     * 根据操作组id查询组内所有操作日志
     * @param groupId 操作组id
     * @return 操作日志信息
     */
    List<OperationLogInfoDO> queryLogInfoListByGroupId(Long groupId);

}
