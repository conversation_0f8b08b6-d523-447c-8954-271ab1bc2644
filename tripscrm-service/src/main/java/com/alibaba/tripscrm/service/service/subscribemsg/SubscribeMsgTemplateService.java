package com.alibaba.tripscrm.service.service.subscribemsg;

import com.alibaba.tripscrm.service.model.dto.subscribemsg.SubscribeMsgTemplateDTO;

import java.util.List;

/**
 * 小程序订阅消息模板服务接口
 *
 * <AUTHOR>
 * @date 2025/10/20
 */
public interface SubscribeMsgTemplateService {

    /**
     * 根据素材id列表查询小程序订阅消息模板（为空则返回所有订阅消息模版）
     */
    List<SubscribeMsgTemplateDTO> list(List<Long> materialIdList);
}
