package com.alibaba.tripscrm.service.service.impl.material;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.material.VariableInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripscrm.service.service.material.MaterialVariableInfoService;
import com.alibaba.tripscrm.service.service.material.VariableInfoService;
import com.alibaba.tripscrm.service.service.strategy.variable.AbstractVariableDataService;
import com.alibaba.tripscrm.service.service.strategy.variable.factory.VariableFactory;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripscrm.service.util.system.RegexUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class MaterialVariableInfoServiceImpl implements MaterialVariableInfoService {

    private final VariableInfoService variableInfoService;
    private final VariableFactory variableFactory;
    private final MaterialLinkService materialLinkService;
    /**
     * 变量正则匹配表达式（#{key}）
     */
    private static final String VARIABLE_REGEX = "#\\{([^}]*)}";
    /**
     * 变量正则表达式编译对象
     */
    private static final Pattern VARIABLE_PATTERN = Pattern.compile(VARIABLE_REGEX, Pattern.CASE_INSENSITIVE);

    @Override
    public void replaceMaterialVariable(MaterailInfoDO materailInfoDO, Map<String, Object> carryMap) {
        if (materailInfoDO == null || StringUtils.isBlank(materailInfoDO.getContent())) {
            return;
        }
        materailInfoDO.setContent(replaceVariable(materailInfoDO.getContent(), carryMap));
    }

    @Override
    public String replaceVariable(String content, Map<String, Object> carryMap) {
        return replaceVariable(content, carryMap, null);
    }

    @Override
    public String replaceVariable(String content, Map<String, Object> carryMap, MaterialTrackRelationDTO materialTrackRelationDTO) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        List<Object> matchFailList = new ArrayList<>();
        // 匹配变量
        List<String> variableList = RegexUtils.getMatchList(content, VARIABLE_PATTERN, 0);
        if (CollectionUtils.isEmpty(variableList)) {
            return content;
        }
        Map<String, String> variableIdMap = Sets.newHashSet(variableList).stream()
                .collect(Collectors.toMap(Function.identity(), variable -> RegexUtils.getMatchList(variable, VARIABLE_PATTERN, 1).get(0)));
        Map<String, String> variableValueMap = new HashMap<>();
        // 变量处理
        for (Map.Entry<String, String> entry : variableIdMap.entrySet()) {
            if (StringUtils.isBlank(entry.getValue()) || !StringUtils.isNumeric(entry.getValue())) {
                matchFailList.add(entry);
                continue;
            }
            VariableInfoDTO variableInfoDTO = variableInfoService.queryById(Long.valueOf(entry.getValue()));
            if (variableInfoDTO == null) {
                matchFailList.add(entry);
                continue;
            }
            // 变量值获取
            String variableValue = getVariableValue(variableInfoDTO, carryMap);
            if (Objects.isNull(variableValue)) {
                matchFailList.add(entry);
                continue;
            }
            variableValueMap.put(entry.getKey(), specialProcess(variableValue, materialTrackRelationDTO));
        }
        PlatformLogUtil.logFail("内容中未匹配到的变量列表", LogListUtil.newArrayList(content, matchFailList));
        if (SwitchConfig.MATERIAL_VARIABLE_STUCK_POINT && CollectionUtils.isNotEmpty(matchFailList)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        // 变量替换
        return RegexUtils.valReplace(content, variableValueMap, VARIABLE_PATTERN, 0);
    }

    /**
     * 特殊处理
     *
     * @param variableValue            变量值
     * @param materialTrackRelationDTO 埋点信息
     * @return 处理后结果
     */
    private String specialProcess(String variableValue, MaterialTrackRelationDTO materialTrackRelationDTO) {
        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(variableValue);
        request.setConvertShortLink(true);
        request.setTitle(UUID.randomUUID().toString());
        request.setUseInWechat(true);
        request.setScrmTrackId(materialLinkService.getBuriedPointId(materialTrackRelationDTO));
        request.setMaterialId(Optional.ofNullable(materialTrackRelationDTO).map(MaterialTrackRelationDTO::getMaterialId).orElse(null));

        if (MaterialUtils.isValidURL(variableValue)) {
            request.setTargetLinkType(LinkTypeEnum.H5_LINK);
            return materialLinkService.convertLink(request) + " ";
        }
        if (inWhiteList(variableValue)) {
            request.setTargetLinkType(LinkTypeEnum.MINI_PROGRAM_LIKE);
            return materialLinkService.convertLink(request) + " ";
        }
        return variableValue;
    }

    /**
     * 命中白名单中
     *
     * @param url 原始链接
     * @return 命中结果
     */
    private Boolean inWhiteList(String url) {
        for (String urlPre : SwitchConfig.SHORT_LINK_PRE) {
            if (url.startsWith(urlPre)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取变量值
     *
     * @param variableInfoDTO 变量信息
     * @param carryMap        携带参数
     * @return 变量值
     */
    public String getVariableValue(VariableInfoDTO variableInfoDTO, Map<String, Object> carryMap) {
        // 获取数据服务
        AbstractVariableDataService dataService = variableFactory.getDataService(variableInfoDTO);
        if (dataService == null) {
            PlatformLogUtil.logFail("没有找到变量对应的服务", LogListUtil.newArrayList(variableInfoDTO));
            return null;
        }
        return dataService.getValue(variableInfoDTO, carryMap);
    }

}
