package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.trip.channel.ai.client.enums.TaskBizSceneType;
import com.alibaba.trip.channel.ai.client.model.request.task.TaskSubmitRequest;
import com.alibaba.trip.channel.ai.client.model.response.task.TaskSubmitResponse;
import com.alibaba.trip.channel.ai.client.service.task.AiTaskService;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.material.AiMaterialAsynTaskParamEnum;
import com.alibaba.tripscrm.service.enums.material.AiMaterialAsynTaskStatusEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.common.ResultDO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.dto.material.AiMaterialAsynTaskDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.material.AiMaterialAsynTaskService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 单条任务数据处理_AI异步处理判断
 *
 * <AUTHOR>
 * @since 2024/4/22 15:22
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CheckAiAsyncBizProcessor implements BizProcessor {
    private final MaterialTransferService materialTransferService;
    private final AiMaterialAsynTaskService aiMaterialAsynTaskService;
    private final AiTaskService aiTaskService;
    private final UicUtils uicUtils;

    @Override
    @TaskExecuteLog("单条任务数据处理_AI异步处理判断")
    public TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception {
        // 只有时间接口可触发
        TaskTriggerTypeEnum triggerType = context.getTriggerType();
        if (!Lists.newArrayList(TaskTriggerTypeEnum.EVENT).contains(triggerType)) {
            return TripSCRMResult.success(null);
        }

        if (aiMaterialCheckContinue(context, todoTaskVO)) {
            PlatformLogUtil.logInfo("任务包含AI素材，本次执行跳过", LogListUtil.newArrayList(context.getTaskId(), todoTaskVO));
            return TripSCRMResult.fail(TripSCRMErrorCode.TASK_CONTINUE_BY_AI_ASYNC);
        }
        return TripSCRMResult.success(null);
    }

    private boolean aiMaterialCheckContinue(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskInfoDO taskInfo = context.getTaskInfoDOSnapshot();
        MaterailInfoDO materialInfo = AbstractTaskExecutor.getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materialInfo)) {
            return false;
        }

        List<MaterialSupplyDTO> materialSupplyList = materialTransferService.extractMaterialSupply(materialInfo.getContent());
        Map<String, Object> extraInfo = new HashMap<>();
        extraInfo.putAll(context.getExtInfo());
        extraInfo.putAll(JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo()));
        return materialSupplyList.stream()
                .anyMatch(materialSupplyDTO -> checkAndSettleSingleSupply(taskInfo, materialInfo, todoTaskVO.getData().get(0), materialSupplyDTO, extraInfo));
    }

    private boolean checkAndSettleSingleSupply(TaskInfoDO taskInfoDO, MaterailInfoDO materialInfo, TaskDataVO.DataBodyVO dataBodyVO, MaterialSupplyDTO materialSupplyDTO, Map<String, Object> extraInfo) {
        boolean hasAI = false;
        MaterialSupplyTypeEnum materialSupplyTypeEnum = MaterialSupplyTypeEnum.getSupplyType(materialSupplyDTO.getSupplyType());
        if (materialSupplyTypeEnum == null) {
            PlatformLogUtil.logFail("供给类型匹配失败，跳过执行", LogListUtil.newArrayList(taskInfoDO, dataBodyVO));
            return hasAI;
        }

        switch (materialSupplyTypeEnum) {
            case DOMESTIC_HOTEL_TOP_AI:
            case DOMESTIC_FLIGHT_TOP_AI:
            case INTERNATIONAL_FLIGHT_TOP_AI:
                hasAI = true;
                submitAiMaterialAsyncTask(taskInfoDO, materialInfo, dataBodyVO, extraInfo, materialSupplyTypeEnum);
                break;
            default:
                break;
        }
        return hasAI;
    }

    private void submitAiMaterialAsyncTask(TaskInfoDO taskInfoDO, MaterailInfoDO materialInfo, TaskDataVO.DataBodyVO dataBodyVO, Map<String, Object> extraInfo, MaterialSupplyTypeEnum materialSupplyTypeEnum) {
        String requestId = UUID.randomUUID().toString();
        Object uIdObject = extraInfo.get(AiMaterialAsynTaskParamEnum.COMMON_USER_ID.getEventCode());
        if (uIdObject == null) {
            PlatformLogUtil.logFail("用户userId为空", LogListUtil.newArrayList(dataBodyVO, extraInfo, taskInfoDO));
            return;
        }
        AiMaterialAsynTaskDTO taskDTO = getInitAiMaterialAsynTaskDTO(taskInfoDO, materialInfo, dataBodyVO, extraInfo, requestId);
        ResultDO<AiMaterialAsynTaskDTO> resultDO = aiMaterialAsynTaskService.upsertAiMaterialAsynTask(taskDTO);
        if (resultDO == null || !resultDO.isSuccess() || resultDO.getModel() == null) {
            PlatformLogUtil.logFail("插入AI素材异步任务失败", LogListUtil.newArrayList(taskInfoDO, taskDTO, resultDO));
            throw new TripscrmException(resultDO.getErrorCode(), resultDO.getErrorMessage());
        }
        Long userId = Long.valueOf(uIdObject.toString());
        TaskSubmitRequest submitRequest = new TaskSubmitRequest();
        submitRequest.setBizId(userId.toString());
        submitRequest.setRequestId(requestId);
        submitRequest.setBizScene(TaskBizSceneType.SCRM_SEARCH_NOT_BUY.getCode());
        Map<String, String> param = new HashMap<>();
        if (!buildParamMap(param, taskInfoDO, userId, dataBodyVO, extraInfo, materialSupplyTypeEnum, taskDTO, resultDO)) {
            return;
        }
        submitRequest.setParam(param);
        com.alibaba.trip.channel.ai.client.model.common.ResultDO<TaskSubmitResponse> result = aiTaskService.submitTask(submitRequest);

        PlatformLogUtil.logInfo("AI素材异步任务提交", LogListUtil.newArrayList(taskInfoDO, taskDTO, result));
        AiMaterialAsynTaskDTO asynTaskDTO = resultDO.getModel();
        if (result == null || !result.isSuccess() || result.getModel() == null) {
            asynTaskDTO.setStatus(AiMaterialAsynTaskStatusEnum.SUBMIT_FAIL.getCode());
        } else {
            asynTaskDTO.setOutBizId(result.getModel().getInstanceId());
            asynTaskDTO.setStatus(AiMaterialAsynTaskStatusEnum.SUBMIT_SUCCESS.getCode());
        }
        ResultDO<AiMaterialAsynTaskDTO> submitResult = aiMaterialAsynTaskService.upsertAiMaterialAsynTask(asynTaskDTO);
        if (submitResult == null || !submitResult.isSuccess() || submitResult.getModel() == null) {
            PlatformLogUtil.logFail("本地更新AI素材任务状态失败", LogListUtil.newArrayList(taskInfoDO, asynTaskDTO, submitResult));
            throw new TripscrmException(submitResult.getErrorCode(), submitResult.getErrorMessage());
        }
    }

    private boolean buildParamMap(Map<String, String> param, TaskInfoDO taskInfoDO, Long userId, TaskDataVO.DataBodyVO dataBodyVO, Map<String, Object> extraInfo, MaterialSupplyTypeEnum materialSupplyTypeEnum, AiMaterialAsynTaskDTO taskDTO, ResultDO<AiMaterialAsynTaskDTO> resultDO) {
        String unionId = uicUtils.getUnionIdByUid(userId.toString());
        if (!StringUtils.hasText(unionId)) {
            PlatformLogUtil.logFail("用户未绑定unionId", LogListUtil.newArrayList(dataBodyVO, extraInfo, taskInfoDO, taskDTO, resultDO));
            return false;
        }
        param.put(AiMaterialAsynTaskParamEnum.COMMON_USER_ID.getAiCode(), String.valueOf(userId));
        param.put(AiMaterialAsynTaskParamEnum.COMMON_UNION_ID.getAiCode(), unionId);
        param.put(AiMaterialAsynTaskParamEnum.COMMON_REQUEST_TYPE.getAiCode(), materialSupplyTypeEnum.getType());
        switch (materialSupplyTypeEnum) {
            case DOMESTIC_HOTEL_TOP_AI:
                if (!buildHotelParam(taskInfoDO, extraInfo, taskDTO, resultDO, param)) {
                    return false;
                }
                break;
            case DOMESTIC_FLIGHT_TOP_AI:
            case INTERNATIONAL_FLIGHT_TOP_AI:
                if (!buildFlightParam(taskInfoDO, extraInfo, taskDTO, resultDO, param)) {
                    return false;
                }
                break;
            default:
                break;
        }
        return true;
    }

    private static boolean buildHotelParam(TaskInfoDO taskInfoDO, Map<String, Object> extraInfo, AiMaterialAsynTaskDTO taskDTO, ResultDO<AiMaterialAsynTaskDTO> resultDO, Map<String, String> param) {
        String cityCode = extraInfo.get(AiMaterialAsynTaskParamEnum.DOMESTIC_HOTEL_CITY_CODE.getEventCode()).toString();
        String checkInDate = extraInfo.get(AiMaterialAsynTaskParamEnum.DOMESTIC_HOTEL_CHECK_IN.getEventCode()).toString();
        String checkOutDate = extraInfo.get(AiMaterialAsynTaskParamEnum.DOMESTIC_HOTEL_CHECK_OUT.getEventCode()).toString();
        if (!StringUtils.hasText(cityCode) || !StringUtils.hasText(checkInDate) || !StringUtils.hasText(checkOutDate)) {
            PlatformLogUtil.logFail("城市编码、入住时间、离店时间不能为空", LogListUtil.newArrayList(taskInfoDO, taskDTO, resultDO));
            return false;
        }
        param.put(AiMaterialAsynTaskParamEnum.DOMESTIC_HOTEL_CITY_CODE.getAiCode(), cityCode);
        param.put(AiMaterialAsynTaskParamEnum.DOMESTIC_HOTEL_CHECK_IN.getAiCode(), checkInDate.replace("-", ""));
        param.put(AiMaterialAsynTaskParamEnum.DOMESTIC_HOTEL_CHECK_OUT.getAiCode(), checkOutDate.replace("-", ""));
        return true;
    }

    private static boolean buildFlightParam(TaskInfoDO taskInfoDO, Map<String, Object> extraInfo, AiMaterialAsynTaskDTO taskDTO, ResultDO<AiMaterialAsynTaskDTO> resultDO, Map<String, String> param) {
        String depCityCode = extraInfo.get(AiMaterialAsynTaskParamEnum.FLIGHT_DEP_CITY_CODE.getEventCode()).toString();
        String arrCityCode = extraInfo.get(AiMaterialAsynTaskParamEnum.FLIGHT_ARR_CITY_CODE.getEventCode()).toString();
        String depDateStart = extraInfo.get(AiMaterialAsynTaskParamEnum.FLIGHT_DEP_DATE_START.getEventCode()).toString();
        if (!StringUtils.hasText(depCityCode) || !StringUtils.hasText(arrCityCode) || !StringUtils.hasText(depDateStart)) {
            PlatformLogUtil.logFail("城市编码、入住时间、离店时间不能为空", LogListUtil.newArrayList(taskInfoDO, taskDTO, resultDO));
            return false;
        }
        // 出发日期加3天，产品逍彦给的规则
        Date depDateStartDate = DateUtils.parseToDateOfDay(depDateStart);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(depDateStartDate);
        calendar.add(Calendar.DATE, 3);
        String depDateEnd = DateUtils.getOtherSimpleDayDateString(calendar.getTime());
        param.put(AiMaterialAsynTaskParamEnum.FLIGHT_DEP_CITY_CODE.getAiCode(), depCityCode);
        param.put(AiMaterialAsynTaskParamEnum.FLIGHT_ARR_CITY_CODE.getAiCode(), arrCityCode);
        param.put(AiMaterialAsynTaskParamEnum.FLIGHT_DEP_DATE_START.getAiCode(), depDateStart.replace("-", ""));
        param.put(AiMaterialAsynTaskParamEnum.FLIGHT_DEP_DATE_END.getAiCode(), depDateEnd.replace("-", ""));
        return true;
    }

    private static AiMaterialAsynTaskDTO getInitAiMaterialAsynTaskDTO(TaskInfoDO taskInfoDO, MaterailInfoDO materialInfo, TaskDataVO.DataBodyVO dataBodyVO, Map<String, Object> extraInfo, String requestId) {
        AiMaterialAsynTaskDTO taskDTO = new AiMaterialAsynTaskDTO();
        taskDTO.setTaskId(taskInfoDO.getId());
        taskDTO.setTargetId(dataBodyVO.getTargetId());
        taskDTO.setTargetType(dataBodyVO.getTargetType());
        taskDTO.setMaterialId(materialInfo.getId());
        taskDTO.setRequestId(requestId);
        AiMaterialAsynTaskDTO.AiMaterialAsynTaskExtraInfo taskExtraInfo = new AiMaterialAsynTaskDTO.AiMaterialAsynTaskExtraInfo();
        taskExtraInfo.setExtraInfo(extraInfo);
        taskDTO.setExtInfo(JSONObject.toJSONString(taskExtraInfo));
        return taskDTO;
    }

}