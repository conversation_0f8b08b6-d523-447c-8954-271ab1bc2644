package com.alibaba.tripscrm.service.service.task.ability.executor.impl.seller;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.manager.opensearch.GroupCustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupPoolQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatGroupPoolDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.seller.SellerWorkScheduleInfoService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupPoolService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.fliggy.pokemon.common.utils.JsonUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 初始化客户-商家沟通群聊任务
 *
 * <AUTHOR>
 * @since 2025/4/9 16:50
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class InitialCustomerSellerGroupTaskExecutor extends AbstractTaskExecutor {
    private final GroupCustomerRelationOpenSearchManager groupCustomerRelationOpenSearchManager;
    private final WechatGroupPoolService wechatGroupPoolService;
    private final TagRelationService tagRelationService;
    private final WechatGroupService wechatGroupService;
    private final SellerWorkScheduleInfoService sellerWorkScheduleInfoService;
    private final TaskExecuteRecordService taskExecuteRecordService;
    private final GroupRelationRepository groupRelationRepository;
    private final GroupService groupService;
    private final MetaqProducer metaqProducer;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        String externalUserId = MapUtils.getString(context.getExtInfo(), "externalUserId");
        String userId = getSendUserId(context, todoTaskVO);
        String sellerId = MapUtils.getString(context.getExtInfo(), "sellerId");
        String unionId = getFinalTargetId(context, todoTaskVO.getData().get(0));
        Integer itemType = MapUtils.getInteger(context.getExtInfo(), "itemType");
        String itemId = MapUtils.getString(context.getExtInfo(), "itemId");
        String itemName = MapUtils.getString(context.getExtInfo(), "itemName");

        if (StringUtils.isBlank(externalUserId) || StringUtils.isBlank(sellerId) || StringUtils.isBlank(userId)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ActivityTaskInfoBO targetActivityContext = getTargetActivityContext(context, todoTaskVO.getData().get(0));
        String contextId = MapUtils.getString(targetActivityContext.getExtraJson(), "contextId");
        Map<String, String> paramMap = targetActivityContext.getExtraJson().getObject("paramMap", new TypeReference<HashMap<String, String>>() {
        });
        String channelId = MapUtils.getString(paramMap, "channelId");

        // 现有群
        WechatGroupPoolDTO wechatGroupPoolDTO = queryGroup(externalUserId, userId, sellerId);
        String sellerFollowUserId = null;
        if (Objects.nonNull(wechatGroupPoolDTO)) {
            List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKey(groupRelationRepository.chatId2ShardingKey(wechatGroupPoolDTO.getChatId()), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
            List<String> sellerUserIdList = groupRelationList.stream().filter(groupRelationDO -> Objects.equals(groupRelationDO.getUserType(), GroupUserTypeEnum.USER.getCode().byteValue())).map(GroupRelationDO::getUserId).collect(Collectors.toList());
            List<String> wechatUserIdList = Arrays.stream(context.getTaskInfoDOSnapshot().getSendUserId().split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            TripSCRMResult<List<String>> wechatUserIdListResult = sellerWorkScheduleInfoService.queryScheduleWechatUserIdList(sellerId, wechatUserIdList, new Date());
            if (Objects.isNull(wechatUserIdListResult) || !wechatUserIdListResult.isSuccess() || CollectionUtils.isEmpty(wechatUserIdListResult.getData())) {
                PlatformLogUtil.logFail("初始化客户-商家沟通群聊成员任务执行，商家当前没有值班账号", TripSCRMErrorCode.SELLER_NO_WORK_WECHAT_USER.getCode(), LogListUtil.newArrayList(wechatUserIdListResult));
                DingTalkApi.sendTaskMessage(String.format("【初始化客户-商家沟通群聊成员】商家当前没有值班账号！！，企微成员userId：%s，客户externalUserId：%s，商家Id：%s", userId, externalUserId, sellerId));
                throw new TripscrmException(TripSCRMErrorCode.SELLER_NO_WORK_WECHAT_USER);
            }
            sellerFollowUserId = wechatUserIdListResult.getData().stream().filter(sellerUserIdList::contains).findFirst().orElse(null);
        }

        if (StringUtils.isNotBlank(sellerFollowUserId)) {
            WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(wechatGroupPoolDTO.getChatId());
            addTagId(wechatGroupPoolDTO.getChatId(), wechatGroupPoolDTO.getCorpId(), paramMap);
            JSONObject message = new JSONObject();
            message.put("sellerId", sellerId);
            message.put("unionId", unionId);
            message.put("userId", userId);
            message.put("sellerFollowUserId", sellerFollowUserId);
            message.put("externalUserId", externalUserId);
            message.put("chatId", wechatGroupPoolDTO.getChatId());
            message.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
            message.put("itemType", itemType);
            message.put("itemId", itemId);
            message.put("itemName", itemName);
            message.put("activityId", context.getTaskInfoDOSnapshot().getActivityId());
            message.put("contextId", contextId);
            message.put("channelId", channelId);
            message.put("messageContent", String.format("您好，经查询已有商家咨询群「%s」，有关商品相关咨询，请移步群内沟通，感谢您的理解", wechatGroupDO.getName()));
            metaqProducer.send(MQEnum.SCRM_INVITE_SELLER_AND_CUSTOMER_JOIN_GROUP_SUCCESS, null, null, message.toJSONString(), MetaQDeleyLevel.LEVEL_2.getLevel());
            metaqProducer.send(MQEnum.SCRM_INVITE_SELLER_CUSTOMER_JOIN_GROUP_SUCCESS_FOR_SINGLE_CHAT, null, null, message.toJSONString(), MetaQDeleyLevel.LEVEL_2.getLevel());
            return;
        }

        TripSCRMResult<WechatGroupPoolDTO> result = wechatGroupPoolService.getOneValidGroupAndMarkUsed(userId, SpaceInfoThreadLocalUtils.getCorpId(), sellerId);
        if (Objects.isNull(result) || !result.isSuccess()) {
            DingTalkApi.sendTaskMessage(String.format("【初始化客户-商家沟通群聊】没有可用群聊！！，企微成员userId：%s，客户externalUserId：%s，商家Id：%s", userId, externalUserId, sellerId));
            throw new TripscrmException(TripSCRMErrorCode.NO_VALID_WECHAT_GROUP);
        }
        wechatGroupPoolDTO = result.getData();
        todoTaskVO.getData().get(0).getContext().put("chatId", wechatGroupPoolDTO.getChatId());

        String chatId = wechatGroupPoolDTO.getChatId();
        addTagId(wechatGroupPoolDTO.getChatId(), wechatGroupPoolDTO.getCorpId(), paramMap);
        JSONObject message = new JSONObject();
        message.put("sellerId", sellerId);
        message.put("userId", userId);
        message.put("unionId", unionId);
        message.put("externalUserId", externalUserId);
        message.put("chatId", chatId);
        message.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
        message.put("itemType", itemType);
        message.put("itemId", itemId);
        message.put("itemName", itemName);
        message.put("contextId", contextId);
        message.put("channelId", channelId);
        message.put("activityId", context.getTaskInfoDOSnapshot().getActivityId());
        metaqProducer.send(MQEnum.SCRM_NOTIFY_INVITE_SELLER_AND_CUSTOMER_JOIN_GROUP, null, null, message.toJSONString(), MetaQDeleyLevel.LEVEL_2.getLevel());
    }

    public void addTagId(String chatId, String corpId, Map<String, String> paramMap) {
        String channelId = MapUtils.getString(paramMap, "channelId");
        if (!SwitchConfig.sellerAcquisitionChannelIdList.contains(channelId)) {
            return;
        }

        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_GROUP.getCode());
        itemTagRelationDTO.setItemId(chatId + "_" + corpId);
        itemTagRelationDTO.setTag(channelId);
        itemTagRelationDTO.setDeleted((byte) 0);
        tagRelationService.upsertSelective(itemTagRelationDTO);
    }

    private WechatGroupPoolDTO queryGroup(String externalUserId, String userId, String sellerId) {
        WechatCustomerGroupQuery wechatCustomerGroupQuery = new WechatCustomerGroupQuery();
        wechatCustomerGroupQuery.setExternalUserId(externalUserId);
        wechatCustomerGroupQuery.setOwnerUserId(userId);
        wechatCustomerGroupQuery.setPageSize(1);
        PageInfo<String> pageInfo = groupCustomerRelationOpenSearchManager.pageQuery(wechatCustomerGroupQuery);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return null;
        }

        WechatGroupPoolQuery wechatGroupPoolQuery = new WechatGroupPoolQuery();
        wechatGroupPoolQuery.setDeleted((byte) 0);
        wechatGroupPoolQuery.setStatus((byte) 1);
        wechatGroupPoolQuery.setOwner(userId);
        wechatGroupPoolQuery.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wechatGroupPoolQuery.setChatIdList(pageInfo.getList());
        List<WechatGroupPoolDTO> wechatGroupPoolList = wechatGroupPoolService.listByCondition(wechatGroupPoolQuery).stream()
                .filter(x -> Objects.equals(sellerId, MapUtils.getString(JsonUtils.parseOrNewJson(x.getExtraInfo()), "sellerId"))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wechatGroupPoolList)) {
            return null;
        }

        String chatId = pageInfo.getList().get(0);
        WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(chatId);
        if (Objects.isNull(wechatGroupDO) || !Objects.equals((byte) 0, wechatGroupDO.getIsDeleted())) {
            return null;
        }

        List<GroupRelationDO> groupRelationDOS = groupRelationRepository.listByShardingKey(groupRelationRepository.chatId2ShardingKey(chatId), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isEmpty(groupRelationDOS) || groupRelationDOS.size() <= 2) {
            return null;
        }

        if (groupRelationDOS.stream().noneMatch(x -> Objects.equals(x.getUserId(), externalUserId))) {
            return null;
        }

        if (groupRelationDOS.stream().noneMatch(x -> Objects.equals(x.getUserId(), userId))) {
            return null;
        }

        return wechatGroupPoolList.get(0);
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        Map<String, Object> extInfo = context.getExtInfo();
        String sendUserId = MapUtils.getString(extInfo, "userId");
        if (StringUtils.isBlank(sendUserId)) {
            PlatformLogUtil.logFail("初始化客户-商家沟通群聊任务，企微成员id为空", LogListUtil.newArrayList(extInfo));
            throw new TripscrmException(TripSCRMErrorCode.WECHAT_USER_ID_EMPTY);
        }

        return sendUserId;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_UNION_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("目标Id为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        return taskDataBody.getTargetId();
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.INITIAL_CUSTOMER_SELLER_GROUP;
    }

    @Override
    public void updateAsyncExecuteResult(Long recordId, ScrmCallbackMsg scrmCallbackMsg) {
        TaskExecuteRecordBO taskExecuteRecordBO = taskExecuteRecordService.queryByRecordId(recordId);
        if (Objects.isNull(taskExecuteRecordBO)) {
            PlatformLogUtil.logFail("初始化客户-商家沟通群聊任务，更新任务异步执行结果，查询任务执行明细为空", LogListUtil.newArrayList(recordId));
            return;
        }

        boolean firstCallback = Lists.newArrayList(TaskSingleDataExecuteResultEnum.SUCCESS).contains(taskExecuteRecordBO.getStatusEnum());

        // 第一个回调，作为异步执行结果
        if (firstCallback) {
            taskExecuteRecordBO.setStatusEnum(scrmCallbackMsg.getResult() ? TaskSingleDataExecuteResultEnum.ASYNC_SUCCESS : TaskSingleDataExecuteResultEnum.ASYNC_FAIL);
            PlatformLogUtil.logInfo("初始化客户-商家沟通群聊任务，更新任务异步执行结果，首次更新异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        }

        JSONObject extraInfo = taskExecuteRecordBO.getExtraInfo();
        List<String> asyncExecuteResultList = extraInfo.containsKey("asyncExecuteResult") ? extraInfo.getObject("asyncExecuteResult", new TypeReference<List<String>>() {
        }) : new ArrayList<>();
        if (StringUtils.isNotBlank(scrmCallbackMsg.getContent())) {
            asyncExecuteResultList.add(scrmCallbackMsg.getContent());
            extraInfo.put("asyncExecuteResultList", asyncExecuteResultList);
        }
        if (StringUtils.isNotBlank(scrmCallbackMsg.getMessage())) {
            extraInfo.put("asyncExecuteResultMessage", scrmCallbackMsg.getMessage());
        }
        taskExecuteRecordBO.setExtraInfo(extraInfo);

        PlatformLogUtil.logInfo("初始化客户-商家沟通群聊任务，更新任务异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        taskExecuteRecordService.upsert(taskExecuteRecordBO);
    }

    @Override
    protected ActivityTaskInfoBO getActivityContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        return getTargetActivityContext(context, taskDataBody);
    }
}
