package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.tripscrm.dal.model.domain.data.TaskRelationDO;

import java.util.List;

/**
 * 任务实体绑定关系服务
 *
 * <AUTHOR>
 * @date 2023/7/11
 */
public interface TaskRelationService {
    /**
     * 根据任务 Id 查询
     *
     * @param taskId 任务Id
     * @return List<TaskRelationDO>
     */
    List<TaskRelationDO> listByTaskId(Long taskId);

    /**
     * 根据绑定实体 Id 和实体类型查询
     *
     * @param targetId   实体 Id
     * @param targetType 实体类型
     * @return List<TaskInfoDO>
     */
    List<TaskRelationDO> listByTargetIdAndTargetType(String targetId, Integer targetType);

    /**
     * 更新
     *
     * @param record record
     * @return update lines
     */
    Integer upsert(TaskRelationDO record);

    /**
     * 删除
     *
     * @param record record
     * @return
     */
    Integer deleteByTaskIdAndTargetIdAndTargetType(TaskRelationDO record);
}
