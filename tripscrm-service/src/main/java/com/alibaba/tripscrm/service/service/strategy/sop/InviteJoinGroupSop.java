package com.alibaba.tripscrm.service.service.strategy.sop;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterialTemplateInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialTemplateInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.convert.TaskConverter;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.activity.InviteJoinGroupActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTemplateService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;

/**
 * <AUTHOR>
 * @since 2024/8/19 10:05
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class InviteJoinGroupSop extends AbstractActivitySop<InviteJoinGroupActivitySopConfigVO> {
    private final MaterialService materialService;
    private final TaskService taskService;
    private final TagInfoService tagInfoService;
    private final ActivityContextService activityContextService;
    private final MaterialTemplateService materialTemplateService;

    @Override
    protected void checkConfigValid(InviteJoinGroupActivitySopConfigVO config, Boolean isUpdate) {
        TaskTriggerTypeEnum triggerTypeEnum = TaskTriggerTypeEnum.getByCode(config.getTriggerType());
        if (Objects.isNull(triggerTypeEnum)) {
            throw new TripscrmException("触发类型不可为空！");
        }

        // 企微号不可为空
        if (CollectionUtils.isEmpty(config.getUserIdList())) {
            throw new RuntimeException("企微号不可为空");
        }

        // 私聊
        checkSingleChatTaskMaterial(config);
    }

    private void checkSingleChatTaskMaterial(InviteJoinGroupActivitySopConfigVO config) {
        // 没有配置私聊素材
        if (Optional.ofNullable(config.getSingleChatTaskMaterialId()).orElse(-1L) == -1L) {
            config.setSingleChatTaskMaterialId(-1L);
            return;
        }

        MaterailInfoDO materailInfoDO = materialService.queryById(config.getSingleChatTaskMaterialId());
        // 私聊素材不存在
        if (Objects.isNull(materailInfoDO)) {
            throw new TripscrmException("私聊素材不存在");
        }

        MaterialTemplateInfoQuery query = new MaterialTemplateInfoQuery();
        query.setId(materailInfoDO.getMaterialTemplateId());
        List<MaterialTemplateInfoDO> templateInfoDOList = materialTemplateService.list(query);
        if (CollectionUtils.isEmpty(templateInfoDOList)) {
            throw new RuntimeException("素材类型非法");
        }

        // 素材类型非法
        if (!Objects.equals(TaskType.ROBOT_CHAT_MESSAGE, TaskType.getByCode(templateInfoDOList.get(0).getType()))) {
            throw new RuntimeException("素材类型非法");
        }
    }

    @Override
    protected void createTaskList(InviteJoinGroupActivitySopConfigVO config, Long activityId) {
        // 1.创建邀请入群任务
        createInviteJoinGroupTask(config, activityId);
        // 2.创建私聊任务
        createSingleChatTask(config, activityId);
    }

    @Override
    protected void updateTaskList(InviteJoinGroupActivitySopConfigVO config) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        // 1.邀请入群任务
        if (taskType2TaskInfoDO.containsKey(TaskType.INVITE_JOIN_GROUP.getCode())) {
            TaskInfoDO inviteJoinGroupTask = taskType2TaskInfoDO.get(TaskType.INVITE_JOIN_GROUP.getCode());
            updateInviteJoinGroupTask(config, inviteJoinGroupTask);
        } else {
            createInviteJoinGroupTask(config, config.getActivityId());
        }

        // 2.私聊任务
        if (taskType2TaskInfoDO.containsKey(TaskType.ROBOT_CHAT_MESSAGE.getCode())) {
            TaskInfoDO singleChatTask = taskType2TaskInfoDO.get(TaskType.ROBOT_CHAT_MESSAGE.getCode());
            updateSingleChatTask(config, singleChatTask);
        } else {
            createSingleChatTask(config, config.getActivityId());
        }
    }

    private Long processActivityContext(TaskInfoDO taskInfoDO) {
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        Long contextId = configJson.containsKey("contextId") ? configJson.getLong("contextId") : activityContextService.generateContextId();
        // 插入/更新活动上下文信息（targetType = 活动id类型）
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(contextId);
        activityTaskInfoBO.setActivityId(taskInfoDO.getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_ACTIVITY_ID);
        activityTaskInfoBO.setTargetId(String.valueOf(taskInfoDO.getActivityId()));
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("dataTime", System.currentTimeMillis());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);
        PlatformLogUtil.logFail("邀请入群活动SOP，活动上下文处理", LogListUtil.newArrayList(activityTaskInfoBO));
        return contextId;
    }

    private void createInviteJoinGroupTask(InviteJoinGroupActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.INVITE_JOIN_GROUP).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        fillInviteJoinGroupTaskConfig(config, taskInfoDO);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建邀请入群任务失败");
        }

        processActivityContext(taskInfoDO);
    }

    private void updateInviteJoinGroupTask(InviteJoinGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        fillInviteJoinGroupTaskConfig(config, taskInfoDO);
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void createSingleChatTask(InviteJoinGroupActivitySopConfigVO config, Long activityId) {
        if (!NumberUtils.biggerThanZero(config.getSingleChatTaskMaterialId())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.ROBOT_CHAT_MESSAGE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(DateUtils.addOneWeek(config.getEffectEndTime()));
        taskInfoDO.setMaterialId(config.getSingleChatTaskMaterialId());
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.inviteCustomerJoinGroupRequestSendSuccessEventSourceId);
        JSONObject extJs = StringUtils.hasText(taskInfoDO.getExtInfo()) ? JSONObject.parseObject(taskInfoDO.getExtInfo()) : new JSONObject();
        // 写死 营销类-日常营销
        extJs.put(TaskConstant.TASK_MESSAGE_TYPE_ID, 144L);
        extJs.put(TaskConstant.TASK_MESSAGE_SCENE_ID, 281L);

        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建私聊任务失败");
        }
    }

    private void updateSingleChatTask(InviteJoinGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了私聊消息配置
        if (!NumberUtils.biggerThanZero(config.getSingleChatTaskMaterialId())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }


        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(DateUtils.addOneWeek(config.getEffectEndTime()));
        taskInfoDO.setMaterialId(config.getSingleChatTaskMaterialId());
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.inviteCustomerJoinGroupRequestSendSuccessEventSourceId);
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void fillInviteJoinGroupTaskConfig(InviteJoinGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        TaskVO taskVO = new TaskVO();
        taskVO.setTriggerType(config.getTriggerType());
        taskVO.setTriggerTimeType(config.getTriggerTimeType());
        taskVO.setTriggerTimeValue(config.getTriggerTimeValue());
        // 任务开始时间段、结束时间段
        String executeStartTime = config.getExecuteStartTime();
        String executeEndTime = config.getExecuteEndTime();
        taskVO.setExecuteStartTime(executeStartTime);
        taskVO.setExecuteEndTime(executeEndTime);
        taskVO.setNotDisturbStartTime(config.getNotDisturbStartTime());
        taskVO.setNotDisturbEndTime(config.getNotDisturbEndTime());

        // 时间表达式
        taskInfoDO.setTriggerTimeCron(TaskConverter.generateCron(taskVO));
        taskInfoDO.setTriggerType(config.getTriggerType());
        // 生效时间
        if (Objects.equals(TaskTriggerTypeEnum.SINGLE_EXECUTE, TaskTriggerTypeEnum.getByCode(config.getTriggerType()))) {
            long triggerTime = Long.parseLong(taskVO.getTriggerTimeValue());
            Date currDateStart = org.apache.commons.lang.time.DateUtils.truncate(new Date(triggerTime), Calendar.DATE);
            Date currDateEnd = DateUtils.addDays(currDateStart, 1);
            taskInfoDO.setEffectStartTime(currDateStart);
            taskInfoDO.setEffectEndTime(currDateEnd);
        } else {
            taskInfoDO.setEffectStartTime(Optional.ofNullable(config.getEffectStartTime()).orElse(new Date()));
            taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
            taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);
        }

        taskInfoDO.setSendUserId(String.join(",", config.getUserIdList()));

        JSONObject extJs = StringUtils.hasText(taskInfoDO.getExtInfo()) ? JSONObject.parseObject(taskInfoDO.getExtInfo()) : new JSONObject();
        extJs.put(TaskConstant.TRIGGER_TIME_TYPE, taskVO.getTriggerTimeType());
        extJs.put(TaskConstant.TRIGGER_TIME_VALUE, taskVO.getTriggerTimeValue());
        if (!CollectionUtils.isEmpty(config.getScrmIncludeTagIdList())) {
            extJs.put(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST, config.getScrmIncludeTagIdList());
        }
        if (!CollectionUtils.isEmpty(config.getScrmExcludeTagIdList())) {
            extJs.put(TaskConstant.SCRM_EXCLUDE_TAG_ID_LIST, config.getScrmExcludeTagIdList());
        }

        if (StringUtils.hasText(config.getIncludeCrowdExpression())) {
            extJs.put(TaskConstant.INCLUDE_CROWD_EXPRESSION, config.getIncludeCrowdExpression());
        }
        if (StringUtils.hasText(config.getExcludeCrowdExpression())) {
            extJs.put(TaskConstant.EXCLUDE_CROWD_EXPRESSION, config.getExcludeCrowdExpression());
        }
        if (NumberUtils.biggerThanZero(config.getEventSourceId())) {
            taskInfoDO.setEventSourceId(config.getEventSourceId());
        }

        extJs.put(TaskConstant.CROWD_ID_FIELD_KEY, config.getCrowdId());
        extJs.put(TaskConstant.ODPS_PROJECT_NAME_FIELD_KEY, config.getOdpsProjectName());
        extJs.put(TaskConstant.ODPS_TABLE_NAME_FIELD_KEY, config.getOdpsTableName());
        extJs.put(TaskConstant.ALL_MATCH, config.getAllMatch());
        extJs.put(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID, config.getWechatJoinGroupActivityId());
        extJs.put(TaskConstant.WECHAT_JOIN_GROUP_STATE, getWechatContactMeState(config.getWechatJoinGroupActivityId()));
        taskInfoDO.setExtInfo(extJs.toJSONString());
    }

    private String getWechatContactMeState(Long wechatJoinGroupActivityId) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(wechatJoinGroupActivityId);
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        TaskInfoDO taskInfoDO = taskType2TaskInfoDO.get(TaskType.GET_WECHAT_JOIN_GROUP.getCode());
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        return configJson.getString("contextId");
    }

    @Override
    public SopTypeEnum getSopType() {
        return SopTypeEnum.INVITE_JOIN_GROUP;
    }

    @Override
    public void fillInfo(ActivitySopVO<InviteJoinGroupActivitySopConfigVO> activitySopVO) {
        InviteJoinGroupActivitySopConfigVO config = activitySopVO.getConfig();

        // 包含标签信息
        if (!CollectionUtils.isEmpty(config.getScrmIncludeTagIdList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(config.getScrmIncludeTagIdList());
            config.setScrmIncludeTagIdList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
            config.setScrmIncludeTagNameList(tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        }

        // 排除标签信息
        if (!CollectionUtils.isEmpty(config.getScrmExcludeTagIdList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(config.getScrmExcludeTagIdList());
            config.setScrmExcludeTagIdList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
            config.setScrmExcludeTagNameList(tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        }

        // 素材信息
        Long materialId = Optional.ofNullable(config.getSingleChatTaskMaterialId()).orElse(-1L);
        if (materialId > 0L) {
            MaterailInfoDO materailInfoDO = materialService.queryById(materialId);
            config.setSingleChatTaskMaterialName(materailInfoDO.getName());
        }
    }
}
