package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 微信群内客户数量统计
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatGroupCustomerCountQuery extends BasePageRequest {
    /**
     * 入群参数
     */
    private String state;

    /**
     * 群聊Id列表
     */
    private List<String> chatIdList;

    /**
     * 是否有添加过企微成员
     */
    private Boolean hasFollowUser;

    /**
     * 是否已经退群
     */
    private Boolean notInGroup;

    /**
     * 平台类型，微信：1，支付宝：4
     */
    private Integer platformType = 1;
}
