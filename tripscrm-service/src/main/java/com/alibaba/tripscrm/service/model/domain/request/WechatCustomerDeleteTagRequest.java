package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.model.vo.tag.TagInfoVO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class WechatCustomerDeleteTagRequest implements Serializable {
    private static final long serialVersionUID = 6459439970531336822L;

    /**
     * 客户 externalUserId
     */
    @NotEmpty(message = "客户Id不可为空")
    private List<String> externalUserIdList;

    /**
     * 标签 Id
     */

    private List<String> tagIdList;

    /**
     * 标签列表
     */
    @NotEmpty(message = "标签列表不可为空")
    private List<TagInfoVO> tagList;

    /**
     * 业务空间Id
     */
    private Long spaceId;

}
