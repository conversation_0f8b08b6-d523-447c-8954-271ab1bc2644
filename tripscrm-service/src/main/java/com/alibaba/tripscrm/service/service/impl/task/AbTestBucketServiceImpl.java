package com.alibaba.tripscrm.service.service.impl.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.AbTestBucketMapper;
import com.alibaba.tripscrm.dal.model.domain.data.AbTestBucketDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.model.dto.subscribemsg.SubscribeMsgTemplateDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.subscribemsg.SubscribeMsgTemplateService;
import com.alibaba.tripscrm.service.service.task.base.AbTestBucketService;
import com.alibaba.tripscrm.service.util.system.HashUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-19 17:51:53
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AbTestBucketServiceImpl implements AbTestBucketService {
    private final AbTestBucketMapper abTestBucketMapper;
    private final MaterialService materialService;
    private final SubscribeMsgTemplateService subscribeMsgTemplateService;

    @Override
    public AbTestBucketVO getById(Long id) {
        if (!NumberUtils.validLong(id)) {
            PlatformLogUtil.logFail("invalid id");
        }

        return convert(abTestBucketMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<AbTestBucketVO> listByTaskId(Long taskId) {
        if (!NumberUtils.validLong(taskId)) {
            PlatformLogUtil.logFail("invalid taskId");
            throw new IllegalArgumentException("invalid taskId");
        }

        List<AbTestBucketDO> abTestBucketDOList = abTestBucketMapper.listByTaskId(taskId);
        if (CollectionUtils.isEmpty(abTestBucketDOList)) {
            return new ArrayList<>();
        }
        return abTestBucketDOList.stream().map(this::convert).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<AbTestBucketVO> listByMaterialId(Long materialId) {
        if (!NumberUtils.validLong(materialId)) {
            PlatformLogUtil.logFail("invalid materialId");
            throw new IllegalArgumentException("invalid materialId");
        }

        List<AbTestBucketDO> abTestBucketDOList = abTestBucketMapper.listByMaterialId(materialId);
        if (CollectionUtils.isEmpty(abTestBucketDOList)) {
            return new ArrayList<>();
        }

        return abTestBucketDOList.stream().map(this::convert).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public Integer insert(AbTestBucketVO abTestBucketVO) {
        if (Objects.isNull(abTestBucketVO)) {
            PlatformLogUtil.logFail("invalid abTestBucketVO");
            return 0;
        }

        if (!NumberUtils.validLong(abTestBucketVO.getMaterialId()) && CollectionUtils.isEmpty(abTestBucketVO.getMaterialIdList())) {
            PlatformLogUtil.logFail("写入分桶数据失败，素材id或素材id列表都为空");
            return 0;
        }

        AbTestBucketDO abTestBucketDO = convert(abTestBucketVO);
        int res = abTestBucketMapper.insert(abTestBucketDO);
        if (res == 0) {
            PlatformLogUtil.logFail("insert failed");
            return 0;
        }

        abTestBucketVO.setId(abTestBucketDO.getId());
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer update(Long taskId, List<AbTestBucketVO> list) {
        int res = 0;
        list.forEach(abTestBucketVO -> {
            abTestBucketVO.setTaskId(taskId);
            abTestBucketVO.setMaterialId(NumberUtils.validLong(abTestBucketVO.getMaterialId()) ? abTestBucketVO.getMaterialId() : -1L);
        });
        List<AbTestBucketVO> oldList = listByTaskId(taskId);
        Map<String, AbTestBucketVO> name2OldAbTestBucketVO = oldList.stream().collect(Collectors.toMap(AbTestBucketVO::getBucketName, a -> a));
        Map<String, AbTestBucketVO> name2NewAbTestBucketVO = list.stream().collect(Collectors.toMap(AbTestBucketVO::getBucketName, a -> a));

        List<AbTestBucketVO> addList = list.stream().filter(x -> !name2OldAbTestBucketVO.containsKey(x.getBucketName())).collect(Collectors.toList());
        List<AbTestBucketVO> updateList = list.stream().filter(x -> name2OldAbTestBucketVO.containsKey(x.getBucketName())).collect(Collectors.toList());
        List<AbTestBucketVO> deleteList = oldList.stream().filter(x -> !name2NewAbTestBucketVO.containsKey(x.getBucketName())).collect(Collectors.toList());
        for (AbTestBucketVO abTestBucketVO : addList) {
            res += insert(abTestBucketVO);
        }
        for (AbTestBucketVO abTestBucketVO : updateList) {
            res += updateById(abTestBucketVO);
        }
        for (AbTestBucketVO abTestBucketVO : deleteList) {
            res += deleteById(abTestBucketVO.getId());
        }

        return res;
    }

    @AteyeInvoker(description = "更新ab分桶素材信息", paraDesc = "id&materialId")
    public Integer updateById(Long id, Long materialId) {
        if (Objects.isNull(id) || Objects.isNull(materialId)) {
            return 0;
        }

        AbTestBucketDO abTestBucketDO = new AbTestBucketDO();
        abTestBucketDO.setId(id);
        abTestBucketDO.setMaterialId(materialId);
        return abTestBucketMapper.updateByPrimaryKey(abTestBucketDO);
    }

    @Override
    public Integer updateById(AbTestBucketVO abTestBucketVO) {
        if (Objects.isNull(abTestBucketVO)) {
            PlatformLogUtil.logFail("invalid abTestBucketVO");
            return 0;
        }

        return abTestBucketMapper.updateByPrimaryKey(convert(abTestBucketVO));
    }

    @Override
    public Integer deleteById(Long id) {
        if (!NumberUtils.validLong(id)) {
            PlatformLogUtil.logFail("invalid id");
            return 0;
        }

        return abTestBucketMapper.deleteByPrimaryKey(id);
    }

    @Override
    public AbTestBucketVO getBucket(Long taskId, String userId) {
        return getBucket(listByTaskId(taskId), userId);
    }

    @Override
    public AbTestBucketVO getBucket(List<AbTestBucketVO> bucketList, String userId) {
        if (CollectionUtils.isEmpty(bucketList)) {
            return null;
        }

        bucketList.sort(Comparator.comparingLong(AbTestBucketVO::getId));
        long hash = HashUtils.md5Hash(userId);
        long mod = hash % 100;
        int totalPercent = 0;
        for (AbTestBucketVO abTestBucketVO : bucketList) {
            totalPercent += abTestBucketVO.getBucketPercent();
            if (totalPercent >= mod) {
                return abTestBucketVO;
            }
        }

        return null;
    }

    @Override
    public void fillTemplateInfo(List<AbTestBucketVO> bucketList) {
        if (CollectionUtils.isEmpty(bucketList)) {
            return;
        }
        List<Long> materialIdList = bucketList.stream()
                .map(AbTestBucketVO::getMaterialIdList)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<SubscribeMsgTemplateDTO> templateMaterialInfoList = subscribeMsgTemplateService.list(materialIdList);
        Map<Long, SubscribeMsgTemplateDTO> materialId2TemplateDTOMap = templateMaterialInfoList.stream().collect(Collectors.toMap(SubscribeMsgTemplateDTO::getMaterialId, a -> a));
        for (AbTestBucketVO abTestBucketVO : bucketList) {
            if (CollectionUtils.isEmpty(abTestBucketVO.getMaterialIdList())) {
                continue;
            }
            List<SubscribeMsgTemplateDTO> templateList = new ArrayList<>();
            for (Long materialId : abTestBucketVO.getMaterialIdList()) {
                SubscribeMsgTemplateDTO templateDTO = materialId2TemplateDTOMap.get(materialId);
                if (Objects.isNull(templateDTO)) {
                    continue;
                }
                templateList.add(templateDTO);
            }
            abTestBucketVO.setTemplateMaterialInfoList(templateList);
        }
    }

    private AbTestBucketVO convert(AbTestBucketDO abTestBucketDO) {
        if (Objects.isNull(abTestBucketDO)) {
            return null;
        }

        AbTestBucketVO abTestBucketVO = new AbTestBucketVO();
        BeanUtils.copyProperties(abTestBucketDO, abTestBucketVO);
        if (Objects.equals(abTestBucketVO.getMaterialId(), -1L)) {
            processMaterialList(abTestBucketDO, abTestBucketVO);
            return abTestBucketVO;
        }

        MaterailInfoDO materailInfoDO = materialService.queryById(abTestBucketVO.getMaterialId());
        abTestBucketVO.setMaterialName(materailInfoDO.getName());
        abTestBucketVO.setMaterialInfo(materailInfoDO);

        return abTestBucketVO;
    }

    private AbTestBucketDO convert(AbTestBucketVO abTestBucketVO) {
        if (Objects.isNull(abTestBucketVO)) {
            return null;
        }

        AbTestBucketDO abTestBucketDO = new AbTestBucketDO();
        BeanUtils.copyProperties(abTestBucketVO, abTestBucketDO);
        JSONObject jsonExtraInfo = new JSONObject();
        if (!CollectionUtils.isEmpty(abTestBucketVO.getMaterialIdList())) {
            jsonExtraInfo.put(MaterialConstant.MATERIAL_ID_LIST, abTestBucketVO.getMaterialIdList());
        }
        abTestBucketDO.setExtraInfo(jsonExtraInfo.toJSONString());
        return abTestBucketDO;
    }

    /**
     * 处理素材列表
     *
     * @param abTestBucketDO ab分桶DO
     * @param abTestBucketVO ab分桶VO
     */
    private void processMaterialList(AbTestBucketDO abTestBucketDO, AbTestBucketVO abTestBucketVO) {
        try {
            JSONObject jsonExtraInfo = JSONObject.parseObject(abTestBucketDO.getExtraInfo());
            List<Long> materialIdList = jsonExtraInfo.getJSONArray(MaterialConstant.MATERIAL_ID_LIST).toJavaList(Long.class);
            abTestBucketVO.setMaterialIdList(materialIdList);
            List<MaterailInfoDO> materialList = materialService.listByIds(materialIdList);
            if (CollectionUtils.isEmpty(materialList)) {
                return;
            }
            List<SubscribeMsgTemplateDTO> templateMaterialInfoList = new ArrayList<>();
            for (MaterailInfoDO materailInfoDO : materialList) {
                TaskType taskType = TaskType.getByCode(materailInfoDO.getType());
                switch (taskType) {
                    case MINI_PROGRAM_SUBSCRIBE_MSG:
                        try {
                            SubscribeMsgTemplateDTO subscribeMsgTemplateDTO = JSONObject.parseObject(materailInfoDO.getContent(), new TypeReference<SubscribeMsgTemplateDTO>() {});
                            templateMaterialInfoList.add(subscribeMsgTemplateDTO);
                        } catch (Exception e) {
                            PlatformLogUtil.logException("解析素材content字段失败", e.getMessage(), e, LogListUtil.newArrayList(materailInfoDO.getId(), materailInfoDO.getContent()));
                            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
                        }
                        break;
                    default:
                        break;
                }
            }
            abTestBucketVO.setTemplateMaterialInfoList(templateMaterialInfoList);
        } catch (Exception e) {
            PlatformLogUtil.logException("处理素材列表出错", e.getMessage(), e, LogListUtil.newArrayList(abTestBucketDO, abTestBucketVO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }
    }
}
