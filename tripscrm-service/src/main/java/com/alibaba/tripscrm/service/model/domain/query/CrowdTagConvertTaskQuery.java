package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 人群标签转换任务查询参数
 *
 * <AUTHOR>
 * @create 2025/10/11 17:39
 */
@Data
public class CrowdTagConvertTaskQuery implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 主键列表
     */
    private List<Long> idList;

    /**
     * 源平台：5-诸葛、6-图灵
     */
    private Byte sourcePlatform;

    /**
     * 源平台列表
     */
    private List<Byte> sourcePlatformList;

    /**
     * 目标平台：5-诸葛、6-图灵
     */
    private Byte targetPlatform;

    /**
     * 目标平台列表
     */
    private List<Byte> targetPlatformList;

    /**
     * 目标平台人群ID
     */
    private String targetCrowdId;

    /**
     * 目标平台人群ID列表
     */
    private List<String> targetCrowdIdList;

    /**
     * 目标平台人群名称模糊查询
     */
    private String targetCrowdNameLike;

    /**
     * 源人群ID
     */
    private String sourceCrowdId;

    /**
     * 源人群ID列表
     */
    private List<String> sourceCrowdIdList;

    /**
     * 源人群名称模糊查询
     */
    private String sourceCrowdNameLike;

    /**
     * 一级标签
     */
    private String firstLevelTag;

    /**
     * 一级标签列表
     */
    private List<String> firstLevelTagList;

    /**
     * 二级标签
     */
    private String secondLevelTag;

    /**
     * 二级标签列表
     */
    private List<String> secondLevelTagList;

    /**
     * 任务状态(0-待处理 1-处理中 2-转换成功 3-转换失败)
     */
    private Byte status;

    /**
     * 任务状态列表
     */
    private List<Byte> statusList;

    /**
     * 执行类型(0-单次 1-周期 2-源人群有效时间)
     */
    private Byte executeType;

    /**
     * 执行类型列表
     */
    private List<Byte> executeTypeList;

    /**
     * 操作人id
     */
    private String operatorId;

    /**
     * 操作人id列表
     */
    private List<String> operatorIdList;

    /**
     * 创建时间开始
     */
    private Date gmtCreateStart;

    /**
     * 创建时间结束
     */
    private Date gmtCreateEnd;

    /**
     * 修改时间开始
     */
    private Date gmtModifiedStart;

    /**
     * 修改时间结束
     */
    private Date gmtModifiedEnd;

    /**
     * 最近执行时间开始
     */
    private Date lastExecuteTimeStart;

    /**
     * 最近执行时间结束
     */
    private Date lastExecuteTimeEnd;

    /**
     * 同步开始日期开始
     */
    private Date startDateStart;

    /**
     * 同步开始日期结束
     */
    private Date startDateEnd;

    /**
     * 同步结束日期开始
     */
    private Date endDateStart;

    /**
     * 同步结束日期结束
     */
    private Date endDateEnd;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    private Integer isDelete;
}