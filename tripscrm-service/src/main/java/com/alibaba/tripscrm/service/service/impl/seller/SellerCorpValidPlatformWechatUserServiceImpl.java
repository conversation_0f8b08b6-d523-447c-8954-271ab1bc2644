package com.alibaba.tripscrm.service.service.impl.seller;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.SellerCorpValidPlatformWechatUserMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SellerCorpValidPlatformWechatUserDO;
import com.alibaba.tripscrm.dal.model.domain.data.SellerCorpValidPlatformWechatUserParam;
import com.alibaba.tripscrm.dal.model.domain.data.WechatContactMeDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.convert.SellerCorpValidPlatformWechatUserConverter;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupPoolQuery;
import com.alibaba.tripscrm.service.model.domain.query.seller.SellerCorpValidPlatformWechatUserQuery;
import com.alibaba.tripscrm.service.model.domain.risk.ActionCheckResult;
import com.alibaba.tripscrm.service.model.dto.seller.SellerCorpValidPlatformWechatUserDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.risk.RiskActionChecker;
import com.alibaba.tripscrm.service.service.seller.SellerCorpValidPlatformWechatUserService;
import com.alibaba.tripscrm.service.service.wechat.WechatContactMeService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupPoolService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerCorpValidPlatformWechatUserServiceImpl implements SellerCorpValidPlatformWechatUserService {
    private final SellerCorpValidPlatformWechatUserMapper sellerCorpValidPlatformWechatUserMapper;
    private final SellerCorpValidPlatformWechatUserConverter sellerCorpValidPlatformWechatUserConverter;
    private final WechatUserService wechatUserService;
    private final ActivityContextService activityContextService;
    private final WechatGroupPoolService wechatGroupPoolService;
    private final WechatContactMeService wechatContactMeService;
    private final RiskActionChecker riskActionChecker;

    @Switch(description = "扫码号是否可以使用", name = "employeeRobotTypeValid")
    private static Boolean employeeRobotTypeValid = false;

    @Override
    public List<SellerCorpValidPlatformWechatUserDTO> listByCondition(SellerCorpValidPlatformWechatUserQuery query) {
        SellerCorpValidPlatformWechatUserParam param = buildParam(query);
        List<SellerCorpValidPlatformWechatUserDO> list = sellerCorpValidPlatformWechatUserMapper.selectByParam(param);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list.stream().map(sellerCorpValidPlatformWechatUserConverter::convertFromDO).collect(Collectors.toList());
    }

    @Override
    public TripSCRMResult<Void> upsertSelective(SellerCorpValidPlatformWechatUserDTO record) {
        if (Objects.isNull(record)) {
            PlatformLogUtil.logFail("新增商家企业用户运营组可用企微号信息，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        try {
            record.setEnv(EnvUtils.getEnvironment());
            sellerCorpValidPlatformWechatUserMapper.upsertSelective(sellerCorpValidPlatformWechatUserConverter.convertFromDTO(record));
            return TripSCRMResult.success(null);
        } catch (Exception e) {
            PlatformLogUtil.logException("更新商家企业用户运营组可用企微号信息，写入db出错", e.getMessage(), e, LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }
    }

    @Override
    public void updateSelectiveByCondition(SellerCorpValidPlatformWechatUserDTO record, SellerCorpValidPlatformWechatUserQuery query) {
        if (Objects.isNull(record)) {
            PlatformLogUtil.logFail("更新商家企业用户运营组可用企微号信息，参数非法", LogListUtil.newArrayList());
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        record.setEnv(EnvUtils.getEnvironment());
        SellerCorpValidPlatformWechatUserParam param = buildParam(query);
        SellerCorpValidPlatformWechatUserDO sellerCorpValidPlatformWechatUserDO = sellerCorpValidPlatformWechatUserConverter.convertFromDTO(record);
        sellerCorpValidPlatformWechatUserMapper.updateByParamSelective(sellerCorpValidPlatformWechatUserDO, param);
    }

    @Override
    @TairLock(value = "'SellerCorpValidPlatformWechatUserService.sync_' + #userId", waitMilliseconds = 3000)
    public int sync(String userId) {
        if (!StringUtils.hasText(userId)) {
            PlatformLogUtil.logFail("同步商家企业用户运营组可用企微号信息，参数非法", LogListUtil.newArrayList());
            return 0;
        }

        WechatUserQuery query = new WechatUserQuery();
        query.setQueryRobotInfo(true);
        query.setCorpId(SwitchConfig.sellerCorpId);
        query.setSpaceId(SwitchConfig.sellerCorpPlatformSpaceId);
        query.setUserIdList(Lists.newArrayList(userId));
        List<WechatUserDTO> wechatUserList = wechatUserService.listValidWechatUserByCondition(query).stream()
                .filter(wechatUserDTO ->
                        // 限制必须百业
                        wechatUserDTO.getRobotList().stream().anyMatch(robotDTO -> Objects.equals(robotDTO.getRobotStatus(), RobotStatusEnum.ONLINE.getCode()) && Objects.equals(robotDTO.getIsvType(), IsvTypeEnum.BAI_YE.getCode()))
                                && (employeeRobotTypeValid || Objects.equals(RobotTypeEnum.ASSISTANT.getCode(), wechatUserDTO.getRobotType()))
                )
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(wechatUserList)) {
            PlatformLogUtil.logFail("同步商家企业用户运营组可用企微号信息，企微成员不可用", LogListUtil.newArrayList());
            updateDeleted(userId);
            return 0;
        }

        WechatUserDTO wechatUserDTO = wechatUserList.get(0);
        // 风控检查
        ActionCheckResult actionCheckResult = riskActionChecker.riskCheck(wechatUserDTO.getCorpId(), wechatUserDTO.getUserId(), RiskActionEnum.GROUP_INVITE);
        if (Lists.newArrayList(RiskStatusEnum.HOLD, RiskStatusEnum.ABNORMAL, RiskStatusEnum.PROTECT).contains(actionCheckResult.getRiskStatus())) {
            // 处于异常或挂起状态下，无法使用
            PlatformLogUtil.logFail("同步商家企业用户运营组可用企微号信息，企微账号风控状态异常", LogListUtil.newArrayList(wechatUserDTO.getUserId(), actionCheckResult.getRiskStatus()));
            updateDeleted(userId);
            return 0;
        }

        WechatGroupPoolQuery wechatGroupPoolQuery = new WechatGroupPoolQuery();
        wechatGroupPoolQuery.setOwner(wechatUserDTO.getUserId());
        wechatGroupPoolQuery.setCorpId(wechatUserDTO.getCorpId());
        wechatGroupPoolQuery.setStatus((byte) 0);
        wechatGroupPoolQuery.setDeleted((byte) 0);
        long groupCount = wechatGroupPoolService.countByCondition(wechatGroupPoolQuery);
        if (groupCount < SwitchConfig.sellerCorpPlatformUserMinValidGroupCountForAcquisition) {
            PlatformLogUtil.logFail("同步商家企业用户运营组可用企微号信息，企微账号下可用群聊数量不足", LogListUtil.newArrayList(wechatUserDTO.getUserId(), groupCount));
            updateDeleted(userId);
            return 0;
        }

        SellerCorpValidPlatformWechatUserDTO sellerCorpValidPlatformWechatUserDTO = new SellerCorpValidPlatformWechatUserDTO();
        sellerCorpValidPlatformWechatUserDTO.setUserId(wechatUserDTO.getUserId());
        sellerCorpValidPlatformWechatUserDTO.setCorpId(wechatUserDTO.getCorpId());
        sellerCorpValidPlatformWechatUserDTO.setDeleted((byte) 0);

        SellerCorpValidPlatformWechatUserQuery sellerCorpValidPlatformWechatUserQuery = new SellerCorpValidPlatformWechatUserQuery();
        sellerCorpValidPlatformWechatUserQuery.setCorpId(wechatUserDTO.getCorpId());
        sellerCorpValidPlatformWechatUserQuery.setUserId(wechatUserDTO.getUserId());
        List<SellerCorpValidPlatformWechatUserDTO> sellerCorpValidPlatformWechatUserList = listByCondition(sellerCorpValidPlatformWechatUserQuery);

        // 新建时需要创建个人活码
        if (CollectionUtils.isEmpty(sellerCorpValidPlatformWechatUserList)) {
            WechatContactMeDO wechatContactMeDO = createWechatContactMe(wechatUserDTO);
            if (wechatContactMeDO == null) {
                PlatformLogUtil.logFail("同步商家企业用户运营组可用企微号信息，个人活码为空", LogListUtil.newArrayList(wechatUserDTO.getUserId()));
                updateDeleted(userId);
                return 0;
            }

            sellerCorpValidPlatformWechatUserDTO.setExtraInfo(SellerCorpValidPlatformWechatUserDTO.ExtraInfo.builder().wechatContactMeQrCode(wechatContactMeDO.getQrCode()).build());
        }

        TripSCRMResult<Void> result = upsertSelective(sellerCorpValidPlatformWechatUserDTO);
        return Objects.nonNull(result) && result.isSuccess() ? 1 : 0;
    }

    private WechatContactMeDO createWechatContactMe(WechatUserDTO wechatUserDTO) {
        WechatContactMeDO wechatContactMeDO = wechatContactMeService.create(Lists.newArrayList(wechatUserDTO.getUserId()), true, String.valueOf(activityContextService.generateContextId()), SwitchConfig.sellerCorpPlatformSpaceId);
        if (Objects.isNull(wechatContactMeDO)) {
            PlatformLogUtil.logFail("同步商家企业用户运营组可用企微号信息，企微账号创建个人活码失败", LogListUtil.newArrayList(wechatUserDTO.getUserId()));
            return null;
        }
        return wechatContactMeDO;
    }

    private void updateDeleted(String userId) {
        SellerCorpValidPlatformWechatUserQuery condition = new SellerCorpValidPlatformWechatUserQuery();
        condition.setUserId(userId);
        condition.setCorpId(SwitchConfig.sellerCorpId);
        SellerCorpValidPlatformWechatUserDTO record = new SellerCorpValidPlatformWechatUserDTO();
        record.setDeleted((byte) 1);
        updateSelectiveByCondition(record, condition);
    }

    private SellerCorpValidPlatformWechatUserParam buildParam(SellerCorpValidPlatformWechatUserQuery query) {
        if (Objects.isNull(query) || (!NumberUtils.biggerThanZero(query.getId()) && Objects.isNull(query.getCorpId()))) {
            throw new TripscrmException(ErrorCodeEnum.PARAM_INVALID);
        }

        SellerCorpValidPlatformWechatUserParam param = new SellerCorpValidPlatformWechatUserParam();
        SellerCorpValidPlatformWechatUserParam.Criteria criteria = param.createCriteria();
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());

        if (StringUtils.hasText(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }
        if (Objects.nonNull(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (StringUtils.hasText(query.getUserId())) {
            criteria.andUserIdEqualTo(query.getUserId());
        }
        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        return param;
    }
}