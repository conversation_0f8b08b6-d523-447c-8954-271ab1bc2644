package com.alibaba.tripscrm.service.service.common;

import com.alibaba.tripscrm.dal.model.domain.data.ManagementGroupDO;
import com.alibaba.tripscrm.service.model.domain.query.GroupQuery;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ManagementGroupService {

    /**
     * 增加管理组
     * @param managementGroupDO 管理组对象
     * @return 增加结果
     */
    boolean add(ManagementGroupDO managementGroupDO);

    /**
     * 根据主键删除
     * @param id 主键
     * @return 删除结果
     */
    boolean deleteById(Long id);

    /**
     * 更新管理组信息
     * @param managementGroupDO 管理组
     * @return 更新结果
     */
    boolean updateById(ManagementGroupDO managementGroupDO);

    /**
     * 根据id查询管理组
     * @param id 管理组id
     * @return 查询结果
     */
    ManagementGroupDO queryById(Long id);

    /**
     * 条件查询管理组
     * @param query 查询条件
     * @return 查询结果
     */
    List<ManagementGroupDO> queryByPage(GroupQuery query);

    /**
     * 名称查询
     * @param name 名称
     * @par spaceId 空间id
     * @return 管理组
     */
    List<ManagementGroupDO> queryByName(String name, Long spaceId);

    /**
     * 条件查询数量
     * @param query 查询条件
     * @return 查询结果
     */
    Long count(GroupQuery query);
}
