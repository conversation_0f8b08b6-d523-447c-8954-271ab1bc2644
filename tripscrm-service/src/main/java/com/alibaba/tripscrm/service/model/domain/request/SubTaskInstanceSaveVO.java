package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SubTaskInstanceSaveVO implements Serializable {

    private static final long serialVersionUID = -4535749078388092426L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 主任务id
     */
    private Long mainTaskId;

    /**
     * 主任务执行的实例id
     */
    private Long mainTaskInstanceId;

    /**
     * 执行开始时间
     */
    private Date startTime;

    /**
     * 执行结束时间
     */
    private Date endTime;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 扩展字段
     */
    private String extInfo;

    /**
     * 任务快照，执行时只取这里面的配置数据
     */
    private String taskConfig;

    /**
     * 素材快照，执行时只取这里面的配置数据
     */
    private String materialInfo;

    /**
     * ab分桶快照，执行时只取这里面的配置数据
     */
    private String abBucketList;

    /**
     * 子任务实例分片数据量
     */
    private Integer dataCount;
}
