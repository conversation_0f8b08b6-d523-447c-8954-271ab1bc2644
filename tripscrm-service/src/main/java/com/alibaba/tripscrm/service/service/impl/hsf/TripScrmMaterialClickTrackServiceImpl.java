package com.alibaba.tripscrm.service.service.impl.hsf;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.dal.mapper.lindorm.MaterialClickTrackDAO;
import com.alibaba.tripscrm.dal.mapper.lindorm.MaterialTrackIdRelationDAO;
import com.alibaba.tripscrm.dal.model.lindorm.MaterialClickTrackDO;
import com.alibaba.tripscrm.dal.model.lindorm.MaterialTrackIdRelationDO;
import com.alibaba.tripscrm.dal.utils.LindormUtil;
import com.alibaba.tripscrm.domain.request.TripSCRMMaterialTrackRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripScrmMaterialClickTrackService;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.tddl.client.sequence.Sequence;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @DECLARATION: THE AUTHORITY OF CODE BELONGS TO ENTERPRISE TECHNOLOGY GROUP OF FEIZHU
 * @NOTE:
 * @AUTHOR: benjamin.zhm
 * @DATE: 2024/1/23
 **/
@Slf4j
@HSFProvider(serviceInterface = TripScrmMaterialClickTrackService.class)
public class TripScrmMaterialClickTrackServiceImpl implements TripScrmMaterialClickTrackService {

    @Resource
    private MaterialTrackIdRelationDAO materialTrackIdRelationDAO;

    @Resource
    private MaterialClickTrackDAO materialClickTrackDAO;

    @Resource
    private Sequence materialClickIdSequence;

    @AteyeInvoker(description = "根据trackId查询埋点数据")
    public List<MaterialClickTrackDO> getMaterialClickTrack(String trackId) {
        List<MaterialClickTrackDO> clickTrackDOList = materialClickTrackDAO.queryByTrackId(trackId);
        return clickTrackDOList;
    }

    @Override
    @ServiceLog("Scrm素材点击上报")
    public TripSCRMResult<Boolean> materialClickTrack(TripSCRMMaterialTrackRequest request) {
        try {
            if (request == null || StringUtils.isBlank(request.getScrmTrackId()) ||
                    (StringUtils.isBlank(request.getClickUnionId()) && StringUtils.isBlank(request.getClickOpenId()) && !NumberUtils.validLong(request.getClickUserId()))) {
                PlatformLogUtil.logInfo("TripScrmMaterialClickTrackServiceImpl素材点击上报入参非法", request);
                return TripSCRMResult.fail(ErrorCodeEnum.PARAM_INVALID.getCode(), ErrorCodeEnum.PARAM_INVALID.getDesc());
            }
            PlatformLogUtil.logInfo("TripScrmMaterialClickTrackServiceImpl素材点击上报入参", request);
            MaterialTrackIdRelationDO relation = materialTrackIdRelationDAO.queryByPrimaryKey(request.getScrmTrackId());
            if (relation == null) {
                PlatformLogUtil.logInfo("TripScrmMaterialClickTrackServiceImpl素材点击上报跟踪id无效", request);
                return TripSCRMResult.fail(ErrorCodeEnum.TRACK_ID_INVALID.getCode(), ErrorCodeEnum.TRACK_ID_INVALID.getDesc());
            }
            String clickId = String.valueOf(materialClickIdSequence.nextValue());
            if (StringUtils.isBlank(clickId)) {
                PlatformLogUtil.logInfo("TripScrmMaterialClickTrackServiceImpl素材点击上报生成点击id无效", request);
                return TripSCRMResult.fail(ErrorCodeEnum.CLICK_ID_BLANK.getCode(), ErrorCodeEnum.CLICK_ID_BLANK.getDesc());
            }
            MaterialClickTrackDO trackDO = new MaterialClickTrackDO();
            trackDO.setScrmTrackId(request.getScrmTrackId());
            trackDO.setClickUnionId(request.getClickUnionId());
            trackDO.setClickOpenId(request.getClickOpenId());
            trackDO.setClickId(LindormUtil.addPrefixSalt(clickId));
            trackDO.setClickUserId(String.valueOf(request.getClickUserId()));
            String time = DateUtil.format(new Date(System.currentTimeMillis()), DatePattern.NORM_DATETIME_MS_PATTERN);
            trackDO.setGmtCreate(time);
            trackDO.setGmtModified(time);
            int effect = materialClickTrackDAO.upsert(trackDO);
            if (effect == 0) {
                PlatformLogUtil.logInfo("TripScrmMaterialClickTrackServiceImpl素材点击上报落库失败", trackDO);
                return TripSCRMResult.fail(ErrorCodeEnum.CLICK_INSERT_DB_FAIL.getCode(), ErrorCodeEnum.CLICK_INSERT_DB_FAIL.getDesc());
            }
            return TripSCRMResult.success(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("MaterialTrackIdRelationManager插入素材跟踪关系落库异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(ErrorCodeEnum.CLICK_INSERT_DB_EXCEPTION.getCode(), ErrorCodeEnum.CLICK_INSERT_DB_EXCEPTION.getDesc());
        }
    }

    @AteyeInvoker(description = "删除素材点击上报信息", paraDesc = "clickId")
    public BaseResult<Boolean> testDeleteMaterialClickId(String clickId) {
        return BaseResult.success(materialClickTrackDAO.deleteByPrimaryKey(clickId) != 0);
    }
}
