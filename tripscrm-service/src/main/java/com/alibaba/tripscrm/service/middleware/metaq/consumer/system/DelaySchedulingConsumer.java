package com.alibaba.tripscrm.service.middleware.metaq.consumer.system;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.factory.DelayScheduleMethodFactory;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.DelayMetaqContext;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.MetaQDelayLevelUtil;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 延时消息消费
 */
@Slf4j
@Service(value = "delaySchedulingConsumer")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class DelaySchedulingConsumer implements MessageListenerConcurrently {
    private final MetaqProducer metaqProducer;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("receive", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("receiveMsg", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("receive message",  LogListUtil.newArrayList(message));
            DelayMetaqContext context = JSONObject.parseObject(message, DelayMetaqContext.class);
            // 立即执行
            if (TriggerTimeEnum.REAL_TIME.getCode().equals(context.getTriggerType())) {
                callback(context);
                return true;
            }
            // 自定义执行时间
            context.setDelayNum(context.getDelayNum() + 1);
            // 方便追溯metaq消息以及接力次数
            String key = context.getDelayKey() + "_" + context.getDelayNum();
            // 超过最大时间,要接力,按照最大时间来投递
            if (context.getTriggerTime() - DateUtils.addSeconds(new Date(), MetaQDeleyLevel.LEVEL_18.getSeconds()).getTime() > 0) {
                metaqProducer.send(MQEnum.DELAY_MQ_SCHEDULING, key, "", message, MetaQDeleyLevel.LEVEL_18.getLevel());
                PlatformLogUtil.logFail("sendDelayMq", LogListUtil.newArrayList(key, 18, message));
                return true;
            }
            // 超过1分钟
            if (context.getTriggerTime() - DateUtils.addSeconds(new Date(), 60).getTime() > 0) {
                int remainingTime = (int) (context.getTriggerTime() - System.currentTimeMillis()) / 1000;
                Integer bestDelayLevel = MetaQDelayLevelUtil.getBestDelayLevel(remainingTime);
                metaqProducer.send(MQEnum.DELAY_MQ_SCHEDULING, key, "", message, bestDelayLevel);
                PlatformLogUtil.logFail("sendDelayMq moreOneMinite", LogListUtil.newArrayList(key, bestDelayLevel, message));
                return true;
            }
            // 需要精确到秒级延迟
            if (context.getSecondDelayLevel() && context.getTriggerTime() - DateUtils.addSeconds(new Date(), 1).getTime() > 0) {
                int remainingTime = (int) (context.getTriggerTime() - System.currentTimeMillis()) / 1000;
                Integer bestDelayLevel = MetaQDelayLevelUtil.getBestDelayLevel(remainingTime);
                metaqProducer.send(MQEnum.DELAY_MQ_SCHEDULING, key, "", message, bestDelayLevel);
                return true;
            }

            // 当前时间一分钟之内，执行任务
            callback(context);
            PlatformLogUtil.logFail("completeDelayMq alreadyRun", LogListUtil.newArrayList(key, message));

        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
        }
        return true;
    }

    private void callback(DelayMetaqContext context) {
        PlatformLogUtil.logFail("start", LogListUtil.newArrayList(context));
        DelayScheduleMethodFactory.schedule(context.getFunctionType(), context.getParam());
        PlatformLogUtil.logFail("end", LogListUtil.newArrayList(context));
    }
}
