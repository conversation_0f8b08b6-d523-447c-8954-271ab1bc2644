package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.request.WxCustomizerMsgRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.service.wechat.CustomizerApprovalService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.wechat.WxCustomizerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2024/7/3
 */
@Slf4j
@HSFProvider(serviceInterface = WxCustomizerService.class)
public class WxCustomizerServiceImpl implements WxCustomizerService {

    @Resource
    private CustomizerApprovalService customizerApprovalService;

    @Override
    @ServiceLog("企微定制师-发送私聊消息")
    public TripSCRMResult<Boolean> customizerMsg(WxCustomizerMsgRequest request) {
        if (StringUtils.isBlank(request.getWechatUserId())
                || StringUtils.isBlank(request.getSendUserId())
                || StringUtils.isBlank(request.getCorpId())
                || StringUtils.isBlank(request.getTaoId())
                || StringUtils.isBlank(request.getState())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        PlatformLogUtil.logFail("企微定制师-发送私聊消息", LogListUtil.newArrayList(request));

        try {
            customizerApprovalService.sendSingleChatMessage(request.getWechatUserId(), request.getTaoId(), request.getState(), request.getCorpId());
            return TripSCRMResult.success(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("企微定制师-发送私聊消息，执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
    }
}
