package com.alibaba.tripscrm.service.middleware.diamond;

import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @desc 定制师目的地diamond监控
 * @date 2024/6/28
 */
@Slf4j
@DiamondListener(dataId = "customtrip_dest", groupId = "travelbc", executeAfterInit = true)
public class CustomizerDestinationDiamond implements DiamondDataCallback {

    private String destJson;

    @Override
    public void received(String s) {
        destJson = s;
    }

    /**
     *
     * 获取目的地配置json
     * @return {@link String}
     */
    public String getDestJson() {
        return destJson;
    }
}
