package com.alibaba.tripscrm.service.middleware.metaq.consumer.system;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.service.strategy.callback.ScrmProxyCallbackRouter;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-08 17:26:58
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service(value = "scrmProxyCallbackConsumer")
public class ScrmProxyCallbackConsumer implements MessageListenerConcurrently {
    @Resource
    private ScrmProxyCallbackRouter scrmProxyCallbackRouter;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logFail("receive", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("dealWithMessage fail", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logFail("receive message", LogListUtil.newArrayList(message));
            ScrmCallbackMsg scrmCallbackMsg = JSONObject.parseObject(message, new TypeReference<ScrmCallbackMsg>() {
            });
            if (scrmCallbackMsg == null) {
                PlatformLogUtil.logFail("scrmCallbackMsg is null");
                return false;
            }
            // 统一处理proxy回调
            return scrmProxyCallbackRouter.routeProcessor(scrmCallbackMsg);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
    }
}
