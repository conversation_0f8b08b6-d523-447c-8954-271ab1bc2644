package com.alibaba.tripscrm.service.service.task.ability.old.second;

import com.alibaba.fastjson.JSON;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.pushcenter.client.UnifiedPushTaskService;
import com.taobao.pushcenter.domain.PushTaskDO;
import com.taobao.pushcenter.result.PushResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 触达服务
 * <AUTHOR>
 * @date 2023/4/17
 */
@Slf4j
@Component
public class TouchService {

    @Autowired
    private UnifiedPushTaskService unifiedPushTaskService;

    public boolean send(String userId, String actionCode, String bizCode, Map<String, String> extInfo){
        PushTaskDO pushTaskDO = new PushTaskDO();
        pushTaskDO.setUserId(userId);
        pushTaskDO.setActionCode(actionCode);
        pushTaskDO.setBizCode(bizCode);
        pushTaskDO.setExtInfo(JSON.toJSONString(extInfo));
        PushResult pushResult = unifiedPushTaskService.buildPushTask(pushTaskDO);
        if (Objects.isNull(pushResult) || !pushResult.isSuccess()){
            PlatformLogUtil.logFail("buildPushTask fail", LogListUtil.newArrayList(pushTaskDO));
            return false;
        }
        return true;
    }

}
