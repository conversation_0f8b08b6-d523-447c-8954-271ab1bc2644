package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateParam;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.AsyncUpdateUserConversationConfigRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.AsyncUpdateUserConversationConfigProcessor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 标记会话已读/未读【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class MarkAsReadCallbackProcessor implements ProxyCallbackProcessor {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private AsyncUpdateUserConversationConfigProcessor asyncUpdateUserConversationConfigProcessor;
    @Resource
    private ChatConversationService chatConversationService;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.MARK_AS_READ_MESSAGE_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        // 执行主动发送消息的后置动作
        WsEvent wsEvent = asyncUpdateUserConversationConfigProcessor.afterCreate(scrmCallbackMsg.getRequestId());
        if (wsEvent == null) {
            PlatformLogUtil.logFail("标记会话已读/未读回调处理，wsEvent为空", LogListUtil.newArrayList(scrmCallbackMsg));
            return true;
        }

        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("标记会话已读/未读回调处理，回调结果为失败", LogListUtil.newArrayList(wsEvent.getUserId(), scrmCallbackMsg));

            wsEvent.setSuccess(false);
            wsEvent.setErrorMessage(scrmCallbackMsg.getMessage() + "|" + scrmCallbackMsg.getContent());
            webSocketFactory.pushMessageByDistributed(wsEvent);
            return true;
        }
        PlatformLogUtil.logInfo("标记会话已读/未读回调结果为成功", LogListUtil.newArrayList(wsEvent.getUserId(), scrmCallbackMsg));

        AsyncUpdateUserConversationConfigRequest updateUserConversationConfigRequest = wsEvent.getData().toJavaObject(AsyncUpdateUserConversationConfigRequest.class);
        String userId = wsEvent.getUserId();
        String chatId = updateUserConversationConfigRequest.getChatId();
        Integer chatType = updateUserConversationConfigRequest.getChatType();
        ChatConversationUpdateParam chatConversationUpdateParam = new ChatConversationUpdateParam();
        chatConversationUpdateParam.setUserId(userId);
        chatConversationUpdateParam.setChatId(chatId);
        chatConversationUpdateParam.setChatType(chatType);
        chatConversationUpdateParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
        chatConversationUpdateParam.setMarkRead(updateUserConversationConfigRequest.getMarkRead());
        ChatConversationDO chatConversation = chatConversationService.updateChatConversationConfig(chatConversationUpdateParam);
        // 推送ws
        asyncUpdateUserConversationConfigProcessor.pushMessageByDistributed(wsEvent, updateUserConversationConfigRequest.getMarkRead(), chatConversation);
        return true;
    }
}
