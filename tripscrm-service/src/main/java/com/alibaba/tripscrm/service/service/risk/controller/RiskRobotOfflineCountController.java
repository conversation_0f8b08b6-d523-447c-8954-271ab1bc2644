package com.alibaba.tripscrm.service.service.risk.controller;

import com.alibaba.tripscrm.service.enums.risk.RiskItemEnum;
import com.alibaba.tripscrm.service.model.domain.request.RiskOffLineCountRequest;
import com.alibaba.tripscrm.service.service.risk.RiskController;
import org.springframework.stereotype.Component;

/**
 * 机器人 掉线次数 风控控制器
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class RiskRobotOfflineCountController extends RiskController<RiskOffLineCountRequest> {

    @Override
    protected RiskItemEnum riskItem() {
        return RiskItemEnum.OFFLINE_COUNT;
    }

    @Override
    public Boolean judgeIntoProtect(RiskOffLineCountRequest param) {
        return true;
    }

    @Override
    public Boolean judgeOutProtect(RiskOffLineCountRequest param) {
        return true;
    }
}

