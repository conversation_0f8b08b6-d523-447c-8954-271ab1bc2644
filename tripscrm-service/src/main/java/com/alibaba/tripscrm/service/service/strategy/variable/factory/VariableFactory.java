package com.alibaba.tripscrm.service.service.strategy.variable.factory;

import com.alibaba.tripscrm.service.enums.material.DataSourceTypeEnum;
import com.alibaba.tripscrm.service.model.dto.material.VariableInfoDTO;
import com.alibaba.tripscrm.service.service.strategy.variable.AbstractVariableDataService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@AllArgsConstructor
public class VariableFactory {

    private final List<AbstractVariableDataService> variableDataServiceList;

    private final Map<DataSourceTypeEnum, AbstractVariableDataService> variableDataServiceMap = new ConcurrentHashMap<>();

    @PostConstruct
    void init() {
        for (AbstractVariableDataService abstractVariableDataService : variableDataServiceList) {
            variableDataServiceMap.put(abstractVariableDataService.type(), abstractVariableDataService);
        }
    }

    /**
     * 获取变量对应的数据服务
     * @param variableInfoDTO 变量信息
     * @return 数据处理服务
     */
    public AbstractVariableDataService getDataService(VariableInfoDTO variableInfoDTO) {
        if (variableInfoDTO == null || variableInfoDTO.getDataSourceTypeEnum() == null) {
            PlatformLogUtil.logFail("变量信息缺失", LogListUtil.newArrayList(variableInfoDTO));
            return null;
        }
        return variableDataServiceMap.get(variableInfoDTO.getDataSourceTypeEnum());
    }

}
