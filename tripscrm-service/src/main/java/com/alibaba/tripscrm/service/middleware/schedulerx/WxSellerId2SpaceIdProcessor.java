package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.mapper.tddl.SpaceMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceDO;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 定时任务：查询space表中sellerId和spaceId的对应关系，并刷新到缓存中进行使用
 * @date 2024/8/5
 */
@Slf4j
@Component
public class WxSellerId2SpaceIdProcessor extends JavaProcessor {

    @Resource
    private LdbTairManager ldbTairManager;

    @Resource
    private SpaceMapper spaceMapper;

    private static final String WX_SELLER_ID_2_SPACE_ID_PREFIX = "wx_seller_id_2_space_id";


    @Override
    public ProcessResult process(JobContext context) throws Exception {
        List<SpaceDO> list = spaceMapper.list();
        Map<String, Long> sellerId2SpaceId = list.stream().filter(e -> Objects.equals(Byte.valueOf("0"), e.getIsDeleted()) && Objects.nonNull(e.getSellerId()))
                .collect(Collectors.toMap(SpaceDO::getSellerId, SpaceDO::getId, (a, b) -> a));
        // 过期时间为24小时
        ldbTairManager.put(WX_SELLER_ID_2_SPACE_ID_PREFIX, JSONObject.toJSONString(sellerId2SpaceId), 24 * 60 * 60);
        return new ProcessResult(true);
    }


    @AteyeInvoker(description = "绑定商家id", paraDesc = "")
    public String bindSellerId(Long spaceId, String sellerId) {
        SpaceDO spaceDO = spaceMapper.selectByPrimaryKey(spaceId);
        if (Objects.isNull(spaceDO)) {
            return null;
        }

        String extInfo = spaceDO.getExtInfo();
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isNotBlank(extInfo)) {
            jsonObject = JSONObject.parseObject(extInfo);
        }
        jsonObject.put("sellerId", sellerId);
        String extInfoJson = jsonObject.toJSONString();
        Ateye.out.println("extInfoJson:"+extInfoJson);
        SpaceDO updateDO = new SpaceDO();
        updateDO.setId(spaceId);
        updateDO.setExtInfo(extInfoJson);
        Integer integer = spaceMapper.updateByPrimaryKey(updateDO);
        return String.valueOf(integer);

    }

    @AteyeInvoker(description = "查询所有空间信息")
    public String listSpace() {
        List<SpaceDO> list = spaceMapper.list().stream().sorted(Comparator.comparingLong(SpaceDO::getId)).collect(Collectors.toList());
        return JSONObject.toJSONString(list);
    }

}
