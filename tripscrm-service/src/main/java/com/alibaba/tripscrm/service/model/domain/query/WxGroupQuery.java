package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/4/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WxGroupQuery extends BasePageRequest implements Serializable {
    private static final long serialVersionUID = -9113755388946088876L;
    /**
     * 新增联系方式的配置id
     */
    private String configId;

    /**
     * 场景 1-插件 2-二维码
     */
    private Byte scene;

    /**
     * 联系方式的备注信息，用于助记
     */
    private String remark;
    /**
     * 企业自定义的state参数，用于区分不同的入群渠道
     */
    private String state;

    /**
     * 标签
     */
    private String tagId;

    /**
     * 业务空间 Id
     */
    private Long spaceId;

}
