package com.alibaba.tripscrm.service.service.seller;

import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.query.seller.SellerCorpValidPlatformWechatUserQuery;
import com.alibaba.tripscrm.service.model.dto.seller.SellerCorpValidPlatformWechatUserDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SellerCorpValidPlatformWechatUserService {
    /**
     * 列表查询
     *
     * @param query
     */
    List<SellerCorpValidPlatformWechatUserDTO> listByCondition(SellerCorpValidPlatformWechatUserQuery query);

    /**
     * upsert
     *
     * @param record
     */
    TripSCRMResult<Void> upsertSelective(SellerCorpValidPlatformWechatUserDTO record);

    /**
     * 选择性修改
     *
     * @param record
     * @param query
     */
    void updateSelectiveByCondition(SellerCorpValidPlatformWechatUserDTO record, SellerCorpValidPlatformWechatUserQuery query);

    /**
     * 同步单个企微账号的可用状态数据
     *
     * @param userId
     */
    int sync(String userId);
}