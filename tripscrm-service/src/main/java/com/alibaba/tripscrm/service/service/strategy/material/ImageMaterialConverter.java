package com.alibaba.tripscrm.service.service.strategy.material;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.material.ImageMaterialDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.WxMediaUtils;
import com.alibaba.tripzoo.proxy.enums.AttachmentSceneTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@AllArgsConstructor
public class ImageMaterialConverter extends AbstractMaterialConverter<ImageMaterialDTO> {

    private final WxMediaUtils wxMediaUtils;

    @Override
    public ImageMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        return JSONObject.parseObject(content, ImageMaterialDTO.class);
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        return new ArrayList<>();
    }

    @Override
    public List<MessageBO> buildMessageBO(ImageMaterialDTO materialDTO, MaterialContentConvertContext context) {
        ArrayList<MessageBO> messageBOList = new ArrayList<>();
        List<String> imageUrlList = Optional.ofNullable(materialDTO.getIsDynamic()).orElse(false) ? context.getImageUrlList() : materialDTO.getUrlList();
        if (CollectionUtils.isEmpty(imageUrlList)) {
            return messageBOList;
        }

        int msgIndex = materialDTO.getIndex();
        for (String imageUrl : imageUrlList) {
            MessageBO messageBO = new MessageBO();
            messageBO.setMsgNum(msgIndex++);
            messageBO.setMsgType(MessageTypeEnum.IMAGE);
            messageBO.setMsgContent(imageUrl);
            messageBOList.add(messageBO);
        }
        return messageBOList;
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(ImageMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        ArrayList<WxMessageBO> wxMessageList = new ArrayList<>();
        List<String> imageUrlList = Optional.ofNullable(materialDTO.getIsDynamic()).orElse(false) ? context.getImageUrlList() : materialDTO.getUrlList();
        if (CollectionUtils.isEmpty(imageUrlList)) {
            return wxMessageList;
        }
        for (String url : imageUrlList) {
            WxMessageBO wxMessageBO2 = new WxMessageBO();
            wxMessageBO2.setMsgType(WxAttachmentTypeEnum.IMAGE);
            wxMessageBO2.setMediaId(wxMediaUtils.getMediaId(WxAttachmentTypeEnum.IMAGE, sendMessage ? null : AttachmentSceneTypeEnum.MOMENT, url, SpaceInfoThreadLocalUtils.getCorpId(), false));
            wxMessageList.add(wxMessageBO2);
        }
        return wxMessageList;
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.PICTURE;
    }
}
