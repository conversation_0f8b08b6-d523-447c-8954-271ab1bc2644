package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.service.model.domain.query.WechatUserApplyRecordQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatUserApplyRecordDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 企业微信上号记录
 *
 * <AUTHOR>
 * @data 2025-01-21 14:28
 */
public interface WechatUserApplyRecordService {

    /**
     * 插入单条记录
     *
     * @param record
     * @return
     */
    Integer insertSelective(WechatUserApplyRecordDTO record);

    /**
     * 根据主键更新单条记录
     *
     * @param record
     * @return
     */
    Integer updateByIdSelective(WechatUserApplyRecordDTO record);

    /**
     * 根据主键id查询
     *
     * @param id
     * @return
     */
    WechatUserApplyRecordDTO selectById(Long id);

    /**
     * 根据主键Id删除
     *
     * @param id
     * @return
     */
    Integer deleteById(Long id);

    /**
     * 根据条件查询
     *
     * @param query
     * @return 标签组对象列表
     */
    List<WechatUserApplyRecordDTO> selectByCondition(WechatUserApplyRecordQuery query);

    /**
     * 唯一键冲突时更新，不冲突时写入
     *
     * @param record
     * @return
     */
    Integer upsertSelective(WechatUserApplyRecordDTO record);

    /**
     * 根据条件规则分页查询
     *
     * @param query
     * @return
     */
    PageInfo<WechatUserApplyRecordDTO> pageQuery(WechatUserApplyRecordQuery query);
}
