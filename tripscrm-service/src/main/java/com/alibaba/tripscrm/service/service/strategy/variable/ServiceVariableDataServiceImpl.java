package com.alibaba.tripscrm.service.service.strategy.variable;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.material.DataSourceTypeEnum;
import com.alibaba.tripscrm.service.model.dto.material.VariableInfoDTO;
import com.alibaba.tripscrm.service.model.dto.material.service.HSFServiceRuleDTO;
import com.alibaba.tripscrm.service.model.dto.material.service.InnerServiceRuleDTO;
import com.alibaba.tripscrm.service.model.dto.material.service.ServiceInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.material.ServiceInfoService;
import com.alibaba.tripscrm.service.service.strategy.variable.inner.AbstractVariableDataInnerService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.fliggy.pokemon.client.hsf.GenericLocator;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class ServiceVariableDataServiceImpl extends AbstractVariableDataService {
    private final ServiceInfoService serviceInfoService;

    @Override
    public DataSourceTypeEnum type() {
        return DataSourceTypeEnum.SERVICE;
    }

    @Override
    public String getValue(VariableInfoDTO variableInfoDTO, Map<String, Object> carryMap) {
        // 服务信息获取
        if (variableInfoDTO == null || !DataSourceTypeEnum.SERVICE.equals(variableInfoDTO.getDataSourceTypeEnum())
                || variableInfoDTO.getServiceId() == null || StringUtils.isBlank(variableInfoDTO.getRule())) {
            PlatformLogUtil.logFail("变量和处理器不匹配或者变量数据存在问题", LogListUtil.newArrayList(variableInfoDTO));
            throw new TripscrmException(TripSCRMErrorCode.EXISTS_NOT_REPLACE_VARIABLE);
        }

        ServiceInfoDTO serviceInfoDTO = serviceInfoService.queryById(variableInfoDTO.getServiceId());
        if (serviceInfoDTO == null) {
            PlatformLogUtil.logFail("变量对应的服务不存在", LogListUtil.newArrayList(variableInfoDTO));
            throw new TripscrmException(TripSCRMErrorCode.EXISTS_NOT_REPLACE_VARIABLE);
        }

        switch (serviceInfoDTO.getServiceTypeEnum()) {
            case HSF:
                HSFServiceRuleDTO hsfServiceRuleDTO = JSONObject.parseObject(serviceInfoDTO.getServiceRuleStr(), new TypeReference<HSFServiceRuleDTO>() {
                });
                String output = genericCall(hsfServiceRuleDTO, carryMap);
                if (StringUtils.isBlank(output)) {
                    PlatformLogUtil.logFail("泛化调用结果为空或者调用失败", LogListUtil.newArrayList(variableInfoDTO));
                    throw new TripscrmException(TripSCRMErrorCode.EXISTS_NOT_REPLACE_VARIABLE);
                }
                // 值获取
                return JSONPath.read(output, variableInfoDTO.getRule(), String.class);
            case INNER:
                InnerServiceRuleDTO innerServiceRuleDTO = JSONObject.parseObject(serviceInfoDTO.getServiceRuleStr(), new TypeReference<InnerServiceRuleDTO>() {
                });
                return AbstractVariableDataInnerService.getValue(innerServiceRuleDTO, carryMap);
            default:
                throw new TripscrmException(TripSCRMErrorCode.EXISTS_NOT_REPLACE_VARIABLE);
        }
    }

    /**
     * 泛化调用
     *
     * @param rule     调用规则
     * @param carryMap 携带数据
     * @return 泛化调用结果
     */
    private String genericCall(HSFServiceRuleDTO rule, Map<String, Object> carryMap) {
        // 参数校验
        if (rule == null || StringUtils.isBlank(rule.getServiceName()) || StringUtils.isBlank(rule.getServiceVersion())
                || StringUtils.isBlank(rule.getMethodName()) || rule.getParam() == null) {
            PlatformLogUtil.logFail("服务信息缺失", LogListUtil.newArrayList(rule));
            return null;
        }
        // 请求参数填充
        JSONObject param = new JSONObject();
        Map<String, String> paramFieldMap = Optional.ofNullable(rule.getParam().getParamFieldMap()).orElse(new HashMap<>());
        for (Map.Entry<String, String> entry : paramFieldMap.entrySet()) {
            param.put(entry.getValue(), carryMap.get(entry.getKey()));
        }
        // 请求规则校验
        if (CollectionUtils.isNotEmpty(rule.getParam().getVerifyFields())) {
            List<String> notExistFields = rule.getParam().getVerifyFields().stream()
                    .filter(field -> !param.containsKey(field)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notExistFields)) {
                PlatformLogUtil.logFail("外调参数校验不通过", LogListUtil.newArrayList(rule, carryMap, notExistFields));
                return null;
            }
        }
        // 实际调用
        return GenericLocator.execHsfGeneric(rule.getServiceName(), rule.getServiceVersion(), rule.getMethodName()
                , 3000, 5000, new String[]{rule.getParam().getParamType()}, new Object[]{param});
    }

    /**
     * 通过变量ID获取调用结果
     *
     * @param id    变量主键
     * @param param 参数
     * @return 结果值
     */
    @AteyeInvoker(description = "服务调用结果", paraDesc = "id&param")
    public String getOutput(Long id, String param) {
        if (!NumberUtils.validLong(id) || StringUtils.isBlank(param)) {
            PlatformLogUtil.logFail("参数缺失", LogListUtil.newArrayList(id, param));
            return "";
        }
        ServiceInfoDTO serviceInfoDTO = serviceInfoService.queryById(id);
        if (serviceInfoDTO == null) {
            PlatformLogUtil.logFail("变量对应的服务不存在", LogListUtil.newArrayList(id));
            return "";
        }
        HSFServiceRuleDTO hsfServiceRuleDTO = JSONObject.parseObject(serviceInfoDTO.getServiceRuleStr(), new TypeReference<HSFServiceRuleDTO>() {
        });
        // 服务调用
        return genericCall(hsfServiceRuleDTO, JSONObject.parseObject(param, new TypeReference<Map<String, Object>>() {
        }));
    }

    /**
     * {"methodName":"getOpenWorkGroupUserList","param":{"paramFieldMap":{"corpId":"corpId","id":"chatId"},"paramType":"com.alibaba.tripzoo.proxy.request.GetOpenWorkGroupUserList"},"serviceName":"com.alibaba.tripzoo.proxy.api.service.GroupService","serviceVersion":"1.0.0"}
     */
    @AteyeInvoker(description = "泛化调用", paraDesc = "服务信息&参数")
    public String genericCall(String hsfInfo, String carryData) {
        HSFServiceRuleDTO rule = JSONObject.parseObject(hsfInfo, HSFServiceRuleDTO.class);
        Map map = JSONObject.parseObject(carryData, Map.class);
        return genericCall(rule, map);
    }

}
