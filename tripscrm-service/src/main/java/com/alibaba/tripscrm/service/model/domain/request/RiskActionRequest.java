package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.dal.model.domain.data.RiskObjectDO;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.model.domain.risk.*;
import com.taobao.hsf.invocation.Invocation;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/07/07
 */
@Data
public class RiskActionRequest {
    private String corpId;
    private String userId;
    private RiskActionEnum riskAction;
    private Invocation invocation;
    private RateLimitResult result;

    private RiskObjectDO riskObject;

    public RiskActionRequest(String corpId, String userId, RiskActionEnum riskAction, Invocation invocation, RateLimitResult result) {
        this.corpId = corpId;
        this.userId = userId;
        this.riskAction = riskAction;
        this.invocation = invocation;
        this.result = result;
    }

    public RiskActionRequest(String corpId, String userId, RiskActionEnum riskAction, RiskObjectDO riskObject) {
        this.corpId = corpId;
        this.userId = userId;
        this.riskAction = riskAction;
        this.riskObject = riskObject;
    }
}