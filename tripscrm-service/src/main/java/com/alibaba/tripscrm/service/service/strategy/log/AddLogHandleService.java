package com.alibaba.tripscrm.service.service.strategy.log;

import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.model.domain.log.SopLogContentBO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/3/17 13:51
 **/
@Service
public class AddLogHandleService extends SopLogHandleService {

    @Override
    public LogShowTypeEnum handleType() {
        return LogShowTypeEnum.ADD;
    }

    @Override
    public String getLogContentStr(SopLogContentBO sopLogContentBO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(sopLogContentBO.getNameDesc())
                .append("增加【")
                .append(sopLogContentBO.getPresentValue())
                .append("】");
        return stringBuilder.toString();
    }
}
