package com.alibaba.tripscrm.service.model.domain.request.miniprogram.subscribemsg;


import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 发送小程序订阅消息请求类
 *
 * <AUTHOR>
 * @since 2025/10/22
 */
@Data
public class SCRMSubscribeMsgSendRequest implements Serializable {

    /**
     * 目标用户id，微信的 opendi/支付宝的 userId
     */
    private String targetId;

    /**
     * 素材id
     */
    private Long materialId;

    /**
     * 素材埋点
     */
    private MaterialTrackRelationDTO materialTrackRelationDTO;

    /**
     * 素材内容转换上下文
     */
    private MaterialContentConvertContext materialContentConvertContext;
}
