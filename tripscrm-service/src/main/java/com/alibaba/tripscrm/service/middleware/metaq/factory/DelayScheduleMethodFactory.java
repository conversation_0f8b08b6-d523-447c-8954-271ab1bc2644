package com.alibaba.tripscrm.service.middleware.metaq.factory;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @since 2024/6/25 16:12
 */
public class DelayScheduleMethodFactory {
    private static final Map<DelayScheduleMethodEnum, Consumer<String>> FUNCTION_MAP = Maps.newConcurrentMap();

    public static void register(DelayScheduleMethodEnum delayScheduleMethodEnum, Consumer<String> function) {
        FUNCTION_MAP.put(delayScheduleMethodEnum, function);
    }

    public static void schedule(DelayScheduleMethodEnum delayScheduleMethodEnum, String param) {
        Consumer<String> consumer = Optional.ofNullable(FUNCTION_MAP.get(delayScheduleMethodEnum))
                .orElseThrow(() -> new TripscrmException(TripSCRMErrorCode.DELAY_SCHEDULE_METHOD_NOT_FOUND));
        consumer.accept(param);
    }
}
