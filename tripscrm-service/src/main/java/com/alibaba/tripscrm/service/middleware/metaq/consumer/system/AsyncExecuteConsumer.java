package com.alibaba.tripscrm.service.middleware.metaq.consumer.system;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.dto.AsyncExecuteContext;
import com.alibaba.tripscrm.service.util.system.AsyncFunctionUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service(value = "asyncExecuteConsumer")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AsyncExecuteConsumer implements MessageListenerConcurrently {

    private static final int MAX_RETRY_COUNT = 3;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            try {
                String message = new String(msg.getBody(), StandardCharsets.UTF_8);
                if (!dealWithMessage(message)) {
                    PlatformLogUtil.logFail("异步执行消息处理，执行失败", LogListUtil.newArrayList(message));
                }
            } catch (Throwable e) {
                PlatformLogUtil.logException("异步执行消息处理，执行异常", e.getMessage(), e, LogListUtil.newArrayList(msg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        // 解析数据
        AsyncExecuteContext context = JSONObject.parseObject(message, new TypeReference<AsyncExecuteContext>() {
        });
        if (Objects.isNull(context)) {
            PlatformLogUtil.logFail("异步执行消息处理，消息体非法", LogListUtil.newArrayList(message));
            return false;
        }

        TripSCRMResult result = AsyncFunctionUtils.execute(context);
        PlatformLogUtil.logInfo("异步执行消息处理，执行完毕", context);
        if (result.isSuccess()) {
            PlatformLogUtil.logInfo("异步执行消息处理，执行成功", context);
            return true;
        }
        PlatformLogUtil.logFail("异步执行消息处理，执行失败", LogListUtil.newArrayList(context, result.getMsg()));

        // 重试逻辑
        int retryCount = context.getRetryCount() == null ? 0 : context.getRetryCount();
        if (!context.getRetry()) {
            return false;
        }
        if (retryCount < MAX_RETRY_COUNT) {
            // 重新提交到异步执行队列
            context.setRetryCount(retryCount + 1);
            AsyncFunctionUtils.submitWithRetry(context);
            return true;
        }
        PlatformLogUtil.logFail("异步执行消息处理，重试次数超限或不支持重试", LogListUtil.newArrayList(context, retryCount));
        return false;

    }

}
