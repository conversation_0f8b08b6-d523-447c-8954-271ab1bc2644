package com.alibaba.tripscrm.service.service.subscribemsg;

import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.dto.subscribemsg.MiniProgramSubscribeMsgTaskStatDTO;
import com.alibaba.tripscrm.service.model.query.subscribemsg.MiniProgramSubscribeMsgTaskStatQuery;

import java.util.List;

/**
 * 小程序订阅消息任务统计服务接口
 *
 * <AUTHOR>
 */
public interface SubscribeMsgTaskStatService {

    /**
     * 分页查询小程序订阅消息任务统计数据
     *
     * @param query 查询参数
     * @return 分页结果
     */
    PageInfoDTO<MiniProgramSubscribeMsgTaskStatDTO> pageQuery(MiniProgramSubscribeMsgTaskStatQuery query);

    /**
     * 列表查询小程序订阅消息任务统计数据
     *
     * @param query 查询参数
     * @return 查询结果列表
     */
    List<MiniProgramSubscribeMsgTaskStatDTO> list(MiniProgramSubscribeMsgTaskStatQuery query);
}