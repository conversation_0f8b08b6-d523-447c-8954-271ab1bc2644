package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/10 16:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TagInfoQuery extends BasePageRequest {
    /**
     * 主键
     */
    private List<Long> idList;

    /**
     * 名称
     */
    private String name;

    /**
     * 名称，模糊匹配
     */
    private String nameLike;

    /**
     * 业务空间id
     */
    private List<Long> spaceIdList;

    /**
     * 标签组id
     */
    private Long groupId;

    /**
     * 标签类型
     */
    private List<Integer> tagType;

    /**
     * 标签来源
     */
    private List<String> sourceList;

    /**
     * 是否删除
     */
    private Byte deleted = 0;
}
