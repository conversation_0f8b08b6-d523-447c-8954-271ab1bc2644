package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.mapper.tddl.SubTaskInstanceMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceExecuteStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.ateye.annotation.Switch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;

/**
 * 扫描执行超时的任务实例
 *
 * <AUTHOR>
 * @date 2023/8/30
 */
@Slf4j
@Component
public class UpdateTaskStatusProcessor extends JavaProcessor {

    @Resource
    private SubTaskInstanceMapper subTaskInstanceMapper;
    @Resource
    private TaskInstanceService taskInstanceService;
    @Resource
    private SubTaskInstanceService subTaskInstanceService;
    @Resource
    private TaskService taskService;

    @Resource
    private LdbTairManager ldbTairManager;

    @Switch(description = "任务执行超时秒数", name = "timeOutSeconds")
    private static Long timeOutSeconds = 60 * 60L * 3;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        List<SubTaskInstanceDO> list = subTaskInstanceMapper.selectOvertimeList(timeOutSeconds);
        if (CollectionUtils.isEmpty(list)) {
            PlatformLogUtil.logFail("扫描执行超时的任务实例，结果为空", LogListUtil.newArrayList());
            return new ProcessResult(true);
        }

        PlatformLogUtil.logFail("扫描执行超时的任务实例", LogListUtil.newArrayList(list));

        Map<Long, List<SubTaskInstanceDO>> mainTaskInstanceId2SubTaskInstanceList = list.stream().collect(Collectors.groupingBy(SubTaskInstanceDO::getMainTaskInstanceId));
        for (Long mainTaskInstanceId : mainTaskInstanceId2SubTaskInstanceList.keySet()) {
            Long taskId = mainTaskInstanceId2SubTaskInstanceList.get(mainTaskInstanceId).get(0).getMainTaskId();
            if (!checkNeedProcess(taskId)) {
                continue;
            }

            try {
                for (SubTaskInstanceDO subTaskInstanceDO : mainTaskInstanceId2SubTaskInstanceList.get(mainTaskInstanceId)) {
                    subTaskInstanceService.updateSubTaskStatus(subTaskInstanceDO.getId(), false, TaskInstanceExecuteStatusEnum.TIMEOUT);
                }

                taskInstanceService.updateStatus(mainTaskInstanceId, TaskInstanceStatusEnum.TIMEOUT);
                // 乐观锁更新任务状态
                Integer ret = taskService.compareAndUpdateStatus(mainTaskInstanceId, TaskStatusEnum.OFFLINE_IN_PROGRESS, TaskStatusEnum.EDITING);
                if (ret <= 0) {
                    continue;
                }

                ldbTairManager.put(TairConstant.LDB_TASK_STATUS_PREFIX + taskId, TaskStatusEnum.EDITING.getStatus(), EXPIRE_TIME_DAY_UNIT);
            } catch (Exception e) {
                PlatformLogUtil.logException("扫描执行超时的任务实例，更新任务状态失败", e.getMessage(), e, LogListUtil.newArrayList(taskId));
            }
        }
        return new ProcessResult(true);
    }

    private boolean checkNeedProcess(Long taskId) {
        try {
            TaskInfoDO taskInfoDO = taskService.checkTaskValid(taskId);
            if (!Objects.equals(EnvUtils.getEnvironment(), taskInfoDO.getEnv())) {
                return false;
            }
            return !taskService.checkInWorkTimeWindow(taskInfoDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("扫描执行超时的任务实例，校验任务配置未通过", e.getMessage(), e, LogListUtil.newArrayList(taskId));
            return true;
        }
    }
}
