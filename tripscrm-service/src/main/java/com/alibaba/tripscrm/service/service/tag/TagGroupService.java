package com.alibaba.tripscrm.service.service.tag;


import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.model.domain.query.TagGroupQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;

import java.util.List;

/**
 * SCRM标签组服务
 * <AUTHOR>
 */
public interface TagGroupService {

    /**
     * 新增一个标签组
     * @param record 标签组对象实体
     * @return 影响行数
     */
    Integer insertSelective(TagGroupDTO record);

    /**
     * 根据id查询标签组
     * @param id 标签组id
     * @return 标签组对象
     */
    TagGroupDTO selectById(Long id);

    /**
     * 根据id删除
     * @param id 标签组id
     * @param isSync 是否同步企微端
     * @return
     */
    Integer deleteById(Long id, Boolean isSync);

    /**
     * 根据条件查询
     * @param query
     * @return 标签组对象列表
     */
    List<TagGroupDTO> selectByCondition(TagGroupQuery query);

    /**
     * 根据标签组Id更新标签组信息
     * @param record 标签组对象
     * @param condition 查询条件
     * @param isSync 是否同步企微端
     * @return 是否更新成功
     */
    Integer updateSelective(TagGroupDTO record, TagGroupQuery condition, Boolean isSync);

    /**
     * 根据id查询标签组
     * @param id 标签组id
     * @return 标签组对象
     */
    TagGroupDTO selectByIdWithCache(Long id);

    /**
     * 根据标签类型和来源查询
     * @param tagTypeEnum
     * @param source
     * @return
     */
    TagGroupDTO selectBySpaceIdAndTagTypeAndSourceWithCache(Long spaceId, TagTypeEnum tagTypeEnum, String source);
}
