package com.alibaba.tripscrm.service.service.impl.wechat;

import com.ali.com.google.common.collect.Lists;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatUserApplyRecordMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserApplyRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserApplyRecordParam;
import com.alibaba.tripscrm.service.convert.WechatUserApplyRecordConverter;
import com.alibaba.tripscrm.service.enums.wechat.WechatUserApplyStatusEnum;
import com.alibaba.tripscrm.service.model.domain.query.WechatUserApplyRecordQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatUserApplyRecordDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.wechat.WechatUserApplyRecordService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 企业微信上号记录
 *
 * <AUTHOR>
 * @data 2025-01-21 17:19
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatUserApplyRecordServiceImpl implements WechatUserApplyRecordService {

    private final WechatUserApplyRecordMapper wechatUserApplyRecordMapper;
    private final WechatUserApplyRecordConverter wechatUserApplyRecordConverter;

    @Override
    public Integer insertSelective(WechatUserApplyRecordDTO record) {
        if (Objects.isNull(record) || !Lists.newArrayList(IsvTypeEnum.BI_LIN, IsvTypeEnum.BAI_YE).contains(IsvTypeEnum.valueOf(record.getIsvType()))
                || !StringUtils.hasLength(record.getIsvId()) || Objects.isNull(WechatUserApplyStatusEnum.Of(record.getStatus()))) {
            PlatformLogUtil.logFail("企业微信上号记录写入失败，参数非法", LogListUtil.newArrayList(record));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return wechatUserApplyRecordMapper.insertSelective(wechatUserApplyRecordConverter.convert2DO(record));
    }

    @Override
    public Integer updateByIdSelective(WechatUserApplyRecordDTO record) {
        if (Objects.isNull(record) || Objects.isNull(record.getId())) {
            PlatformLogUtil.logFail("企业微信上号记录更新失败，参数非法", LogListUtil.newArrayList(record));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatUserApplyRecordDTO oldRecord = selectById(record.getId());
        if (Objects.isNull(oldRecord)) {
            PlatformLogUtil.logFail("企业微信上号记录更新失败，记录不存在", LogListUtil.newArrayList(record));
            return 0;
        }
        return wechatUserApplyRecordMapper.updateByPrimaryKeySelective(wechatUserApplyRecordConverter.convert2DO(record));
    }

    @Override
    public WechatUserApplyRecordDTO selectById(Long id) {
        if (!NumberUtils.validLong(id)) {
            PlatformLogUtil.logFail("企业微信上号记录查询失败，参数非法", LogListUtil.newArrayList(id));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return Optional.ofNullable(wechatUserApplyRecordMapper.selectByPrimaryKey(id)).map(wechatUserApplyRecordConverter::convert2DTO).orElse(null);
    }

    @Override
    public Integer deleteById(Long id) {
        if (!NumberUtils.validLong(id)) {
            PlatformLogUtil.logFail("企业微信上号记录删除失败，参数非法", LogListUtil.newArrayList(id));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return wechatUserApplyRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<WechatUserApplyRecordDTO> selectByCondition(WechatUserApplyRecordQuery query) {
        if (Objects.isNull(query)) {
            PlatformLogUtil.logFail("企业微信上号记录查询失败，参数非法", LogListUtil.newArrayList(query));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatUserApplyRecordParam param = buildParam(query);
        return Optional.ofNullable(wechatUserApplyRecordMapper.selectByParam(param)).orElse(new ArrayList<>()).stream().map(wechatUserApplyRecordConverter::convert2DTO).collect(Collectors.toList());
    }

    @Override
    public Integer upsertSelective(WechatUserApplyRecordDTO record) {
        if (Objects.isNull(record) || !Lists.newArrayList(IsvTypeEnum.BI_LIN, IsvTypeEnum.BAI_YE).contains(IsvTypeEnum.valueOf(record.getIsvType()))
                || !StringUtils.hasLength(record.getIsvId()) || Objects.isNull(WechatUserApplyStatusEnum.Of(record.getStatus()))) {
            PlatformLogUtil.logFail("企业微信上号记录写入失败，参数非法", LogListUtil.newArrayList(record));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return wechatUserApplyRecordMapper.upsertSelective(wechatUserApplyRecordConverter.convert2DO(record));
    }

    @Override
    public PageInfo<WechatUserApplyRecordDTO> pageQuery(WechatUserApplyRecordQuery query) {
        try {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
            WechatUserApplyRecordParam param = buildParam(query);
            param.appendOrderByClause(WechatUserApplyRecordParam.OrderCondition.GMTMODIFIED, WechatUserApplyRecordParam.SortType.DESC);
            List<WechatUserApplyRecordDO> list = wechatUserApplyRecordMapper.selectByParam(param);
            if (CollectionUtils.isEmpty(list)) {
                return new PageInfo<>();
            }
            PageInfo<WechatUserApplyRecordDO> pageInfo = new PageInfo<>(list);
            return PageUtils.getPageInfo(pageInfo, wechatUserApplyRecordConverter::convert2DTO);
        } catch (Exception e) {
            PlatformLogUtil.logException("企业微信上号记录查询出错", e.getMessage(), e, LogListUtil.newArrayList(query));
            throw e;
        }
    }

    private static WechatUserApplyRecordParam buildParam(WechatUserApplyRecordQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WechatUserApplyRecordParam param = new WechatUserApplyRecordParam();
        WechatUserApplyRecordParam.Criteria criteria = param.or();
        if (Objects.nonNull(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (NumberUtils.validLong(query.getCreateWeComRecordId())) {
            criteria.andCreateWeComRecordIdEqualTo(query.getCreateWeComRecordId());
        }
        if (Objects.nonNull(query.getIsvType()) && NumberUtils.validInteger(Integer.valueOf(query.getIsvType()))) {
            criteria.andIsvTypeEqualTo(query.getIsvType());
        }
        if (Objects.nonNull(WechatUserApplyStatusEnum.Of(query.getStatus()))) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (Objects.nonNull(query.getCreateStartTime())) {
            criteria.andGmtCreateGreaterThanOrEqualTo(query.getCreateStartTime());
        }
        if (Objects.nonNull(query.getCreateEndTime())) {
            criteria.andGmtCreateLessThanOrEqualTo(query.getCreateEndTime());
        }
        if (StringUtils.hasLength(query.getIsvId())) {
            criteria.andIsvIdEqualTo(query.getIsvId());
        }
        return param;
    }
}
