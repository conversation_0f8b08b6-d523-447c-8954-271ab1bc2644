package com.alibaba.tripscrm.service.manager.alipay;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.request.alipay.CouponQuery;
import com.alibaba.tripzoo.proxy.api.service.alipay.activity.AlipayMarketingActivityService;
import com.alibaba.tripzoo.proxy.model.alipay.activity.AlipayMarketingActivityQueryResponseDTO;
import com.alibaba.tripzoo.proxy.request.alipay.AlipayMarketingActivityInfoQueryRequest;
import com.alibaba.tripzoo.proxy.request.alipay.AlipayMarketingActivityPageRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 支付宝优惠券管理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@Component
public class AliPayCounponManager {

    @Resource
    private AlipayMarketingActivityService alipayMarketingActivityService;


    /**
     * 分页查询支付宝优惠券
     *
     * @param request 查询请求
     * @return 优惠券分页结果
     */
    public PageInfoDTO<AlipayMarketingActivityQueryResponseDTO> pageQueryCoupons(CouponQuery request) {
        PageInfoDTO pageInfo = new PageInfoDTO();
        pageInfo.setPageNum(request.getPageNum());
        pageInfo.setPageSize(request.getPageSize());

        if (StringUtils.hasText(request.getActivityId())) {
            AlipayMarketingActivityInfoQueryRequest queryRequest = new AlipayMarketingActivityInfoQueryRequest();
            queryRequest.setActivityId(request.getActivityId());
            ResultDO<AlipayMarketingActivityQueryResponseDTO> byIdResult =
                    alipayMarketingActivityService.queryMarketingActivityInfo(queryRequest);
            if (!byIdResult.getSuccess() || Objects.isNull(byIdResult.getModel())) {
                PlatformLogUtil.logFail("查询支付宝优惠券失败", LogListUtil.newArrayList(request, byIdResult));
                return null;
            }
            pageInfo.setTotal(1L);
            pageInfo.setList(Lists.newArrayList(byIdResult.getModel()));
            return pageInfo;
        }

        // 构建查询请求
        AlipayMarketingActivityPageRequest pageRequest = new AlipayMarketingActivityPageRequest();
        pageRequest.setActivityStatus(request.getActivityStatus());
        pageRequest.setPageNum(Long.valueOf(request.getPageNum()));
        pageRequest.setPageSize(Long.valueOf(request.getPageSize()));

        // 调用支付宝营销活动服务
        ResultDO<com.alibaba.tripzoo.proxy.result.PageInfoDTO<AlipayMarketingActivityQueryResponseDTO>> result =
                alipayMarketingActivityService.pageQuery(pageRequest);

        if (!result.getSuccess() || Objects.isNull(result.getModel())) {
            PlatformLogUtil.logFail("查询支付宝优惠券失败", LogListUtil.newArrayList(request, result));
            return null;
        }
        com.alibaba.tripzoo.proxy.result.PageInfoDTO<AlipayMarketingActivityQueryResponseDTO> page = result.getModel();
        pageInfo.setTotal(Long.valueOf(page.getTotal()));
        pageInfo.setList(page.getList());
        return pageInfo;
    }
}