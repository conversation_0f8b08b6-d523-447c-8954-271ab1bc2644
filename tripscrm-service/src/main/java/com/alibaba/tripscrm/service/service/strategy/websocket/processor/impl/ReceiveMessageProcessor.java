package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.SendMessageResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 收消息 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class ReceiveMessageProcessor implements WsEventProcessor {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private ChatConversationService chatConversationService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.RECEIVE_MESSAGE;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        // 没有接收
    }

    public void pushMessageByDistributed(String userId, String chatId, ChatTypeEnum chatType, String senderId, User lockAccount, String messageId, Long timestamp, String corpId, FusionChatMessageBody fusionChatMessageBody, FusionChatUserBody fusionChatUserBody) {
        SendMessageResponse sendMessageResponse = new SendMessageResponse();
        sendMessageResponse.setChatId(chatId);
        sendMessageResponse.setChatType(chatType.getValue());
        sendMessageResponse.setSenderId(senderId);
        sendMessageResponse.setSenderId(senderId);
        sendMessageResponse.setSenderName(fusionChatUserBody.getUserName());
        if (Objects.equals(userId, senderId) && Objects.nonNull(lockAccount)) {
            sendMessageResponse.setSenderName(fusionChatUserBody.getUserName() + "【" + lockAccount.getUserName() + "】");
        }
        sendMessageResponse.setSenderAvatar(fusionChatUserBody.getUserAvatar());
//        sendMessageResponse.setMessageId(messageId.get());
        sendMessageResponse.setMessageId(messageId);
        sendMessageResponse.setTimestamp(timestamp);
        sendMessageResponse.setMsgType(fusionChatMessageBody.getMsgType());
        sendMessageResponse.setMsgContent(fusionChatMessageBody.getMsgContent());
        sendMessageResponse.setText(MessageUtils.fusionChatMessage2Text(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
        // 设置未读数
        sendMessageResponse.setAllUnreadCount(chatConversationService.getWechatUserUnreadCount(corpId, userId));
        sendMessageResponse.setUnreadCount(chatConversationService.getWechatUserConversationUnreadCount(corpId, userId, chatId));
        WsEvent wsEvent = new WsEvent();
        wsEvent.setUserId(userId);
        wsEvent.setType(WsEventTypeEnum.RECEIVE_MESSAGE.getValue());
        wsEvent.setData((JSONObject) JSONObject.toJSON(sendMessageResponse));
        webSocketFactory.pushMessageByDistributed(wsEvent);
    }
}
