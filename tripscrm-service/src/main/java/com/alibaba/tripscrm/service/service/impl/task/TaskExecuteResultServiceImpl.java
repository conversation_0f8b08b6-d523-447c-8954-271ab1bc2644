package com.alibaba.tripscrm.service.service.impl.task;

import com.alibaba.dataworks.dataservice.model.hsf.DataParam;
import com.alibaba.dataworks.dataservice.service.HsfDataApiService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.service.enums.task.TaskAbTypeEnum;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteResultBO;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteResultVO;
import com.alibaba.tripscrm.service.service.task.base.AbTestBucketService;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteResultService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-05 23:09:22
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskExecuteResultServiceImpl implements TaskExecuteResultService {
    private final TaskService taskService;
    private final AbTestBucketService abTestBucketService;
    private final HsfDataApiService hsfDataApiService;

    private static final Long APP_ID = 411222946568887L;
    private static final String APP_CODE = "BFBB80AB06F24B0A8187320CAB9C6C0A";

    private List<TaskExecuteResultBO> getTaskExecuteResultList(Long taskId, Date beginDate, Date endDate) {
        List<TaskExecuteResultBO> result = new ArrayList<>();
        DataParam dataParam = new DataParam();
        // 可以在接口属性里获得
        dataParam.setApiId(APP_ID);
        // 生成API的那个页面可以得到 服务管理-->API调用
        dataParam.setAppCode(APP_CODE);
        // API需要的入参
        dataParam.getParams().put("taskId", taskId);
        dataParam.getParams().put("beginDate", DateUtils.getSimpleDayDateString(beginDate));
        dataParam.getParams().put("endDate", DateUtils.getSimpleDayDateString(endDate));
        // API的出参
        Map<String, Object> data = hsfDataApiService.getData(dataParam);
        if (Objects.isNull(data) || !data.containsKey("errCode") || !data.containsKey("data") || !data.get("errCode").equals(0)) {
            return new ArrayList<>();
        }

        JSONArray list = (JSONArray) data.get("data");
        for (Object o : list) {
            JSONObject jsonObject = (JSONObject) o;
            try {
                TaskExecuteResultBO taskExecuteResultBO = new TaskExecuteResultBO();
                taskExecuteResultBO.setTaskId(taskId);
                taskExecuteResultBO.setDate(DateUtils.parseToSimpleDayDate(jsonObject.getString("date")));
                taskExecuteResultBO.setTaskInstanceId(jsonObject.getLong("taskinstanceid"));
                taskExecuteResultBO.setAbBucketId(jsonObject.getLong("abbucketid"));
                taskExecuteResultBO.setMaterialId(jsonObject.getLong("materialid"));
                taskExecuteResultBO.setMessageIndex(jsonObject.getInteger("messageindex"));
                taskExecuteResultBO.setAllCount(jsonObject.getLong("allcount"));
                taskExecuteResultBO.setSuccessCount(jsonObject.getLong("successcount"));
                taskExecuteResultBO.setFailCount(jsonObject.getLong("failcount"));
                taskExecuteResultBO.getAttributeMap().putAll(JSONObject.parseObject(jsonObject.getString("attributionmap"), new TypeReference<HashMap<String, Long>>() {
                }));
                result.add(taskExecuteResultBO);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return result;
    }

    @Override
    public TaskExecuteResultVO getTaskExecuteResult(Long taskId, Date beginDate, Date endDate) {
        TaskInfoDO taskInfoDO = taskService.queryTaskById(taskId);
        if (Objects.isNull(taskInfoDO)) {
            throw new RuntimeException("任务不存在");
        }

        TaskExecuteResultVO taskExecuteResultVO = new TaskExecuteResultVO();
        taskExecuteResultVO.setTaskId(taskId);
        if (Objects.isNull(TaskAbTypeEnum.of(Optional.ofNullable(taskInfoDO.getAbType()).orElse((byte) 0).intValue()))) {
            taskExecuteResultVO.setAbTestBucketVOList(new ArrayList<>());
        } else {
            taskExecuteResultVO.setAbTestBucketVOList(abTestBucketService.listByTaskId(taskId));
        }

        List<TaskExecuteResultBO> taskExecuteResultList = getTaskExecuteResultList(taskId, beginDate, endDate);
        // 按照ab实验分桶统计
        Map<Long, List<TaskExecuteResultBO>> abBucketId2TaskExecuteResultList = taskExecuteResultList.stream().collect(Collectors.groupingBy(TaskExecuteResultBO::getAbBucketId));

        for (Long abBucketId : abBucketId2TaskExecuteResultList.keySet()) {
            TaskExecuteResultVO.ReachData reachData = new TaskExecuteResultVO.ReachData();
            taskExecuteResultVO.getReachDataMap().put(abBucketId, reachData);

            // 当前分桶的数据
            List<TaskExecuteResultBO> taskExecuteResultBOList = abBucketId2TaskExecuteResultList.get(abBucketId);
            // 分桶内按照消息编号分桶
            Map<Integer, List<TaskExecuteResultBO>> messageIndex2TaskExecuteResultList = taskExecuteResultBOList.stream().filter(x -> !Objects.equals(x.getMessageIndex(), -1)).collect(Collectors.groupingBy(TaskExecuteResultBO::getMessageIndex));

            // 预期触达人数
            List<Long> expectReachCountList = new ArrayList<>(Collections.nCopies(messageIndex2TaskExecuteResultList.size(), 0L));
            // 实际触达人数
            List<Long> reachCountList = new ArrayList<>(Collections.nCopies(messageIndex2TaskExecuteResultList.size(), 0L));
            // 归因
            List<Map<String, Long>> attributeMapList = new ArrayList<>();
            for (int i = 0; i < messageIndex2TaskExecuteResultList.size(); i++) {
                attributeMapList.add(new HashMap<>());
            }

            // 每个消息分别处理
            for (Integer messageIndex : messageIndex2TaskExecuteResultList.keySet()) {
                // 当前消息的数据
                List<TaskExecuteResultBO> taskExecuteResults = messageIndex2TaskExecuteResultList.get(messageIndex);
                long expectReachCount = 0L;
                long reachCount = 0L;
                Map<String, Long> attributeMap = new HashMap<>();

                for (TaskExecuteResultBO taskExecuteResultBO : taskExecuteResults) {
                    expectReachCount += taskExecuteResultBO.getSuccessCount() + taskExecuteResultBO.getFailCount();
                    reachCount += taskExecuteResultBO.getSuccessCount();
                    Map<String, Long> currentAttributeMap = taskExecuteResultBO.getAttributeMap();
                    for (String attribute : currentAttributeMap.keySet()) {
                        attributeMap.putIfAbsent(attribute, 0L);
                        attributeMap.put(attribute, currentAttributeMap.get(attribute) + attributeMap.get(attribute));
                    }
                }

                expectReachCountList.set(messageIndex, expectReachCount);
                reachCountList.set(messageIndex, reachCount);
                attributeMapList.set(messageIndex, attributeMap);
            }

            reachData.setExpectReachCountList(expectReachCountList);
            reachData.setReachCountList(reachCountList);
            reachData.setAttributeMap(attributeMapList);
        }

        return taskExecuteResultVO;
    }
}
