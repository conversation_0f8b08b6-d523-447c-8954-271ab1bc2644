package com.alibaba.tripscrm.service.service.task.ability.sub.pre;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceExecuteStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.base.AbTestBucketService;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.alibaba.tripscrm.TripSCRMErrorCode.*;

/**
 * 任务执行_子任务_任务有效性检查
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CheckValidProcessor implements ExecuteProcessor {
    private final TaskService taskService;
    private final MaterialService materialService;
    private final AbTestBucketService abTestBucketService;
    private final TaskInstanceService taskInstanceService;
    private final SubTaskInstanceService subTaskInstanceService;

    @Switch(name = "allowedSubTaskRunningReentrant", description = "允许子任务运行中实例可重入执行")
    public static Boolean allowedSubTaskRunningReentrant = Boolean.TRUE;

    @Override
    @TaskExecuteLog("任务执行_子任务_子任务执行有效性检查")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        // 检查任务是否有效
        checkValid(context);
        return TripSCRMResult.success(null);
    }

    private void checkValid(TaskExecuteContext context) {
        // 非自测&没有主任务实例&没有子任务实例，直接结束吧
        if (!context.getTestFlag() && Objects.isNull(context.getMainTaskInstanceId()) && Objects.isNull(context.getInstanceId())) {
            throw new TripscrmException(NOT_EXIST_TASK_INSTANCE_DATA);
        }

        // 没有子任务实例Id，则为流式处理
        if (Objects.isNull(context.getInstanceId())) {
            executeStreamTask(context);
            return;
        }

        executeBatchTask(context);
    }

    private void executeBatchTask(TaskExecuteContext context) {
        // 任务ID
        Long taskId = context.getTaskId();
        // 动态校验任务的合法性
        taskService.checkTaskValid(taskId);

        // 校验实例数据
        SubTaskInstanceDO subTaskInstanceDO = checkAndGetSubTaskInstance(context.getInstanceId());
        context.setMainTaskInstanceId(subTaskInstanceDO.getMainTaskInstanceId());

        // 将保存的任务快照设置到上下文
        TaskInfoDO taskSnapshot = JSONObject.parseObject(subTaskInstanceDO.getTaskConfig(), new TypeReference<TaskInfoDO>() {
        });
        context.setTaskInfoDOSnapshot(taskSnapshot);

        checkTaskExecuteTimeValid(context);

        // 素材快照
        MaterailInfoDO materialSnapshot = JSONObject.parseObject(subTaskInstanceDO.getMaterialInfo(), new TypeReference<MaterailInfoDO>() {
        });
        context.setMaterialSnapshot(materialSnapshot);

        // AB分桶快照
        List<AbTestBucketVO> abBucketListSnapshot = JSONObject.parseObject(subTaskInstanceDO.getAbBucketList(), new TypeReference<List<AbTestBucketVO>>() {
        });
        context.setAbBucketListSnapshot(abBucketListSnapshot);

        // 子任务执行状态设置为RUNNING
        if (!subTaskInstanceService.updateSubTaskStatus(context.getInstanceId(), false, TaskInstanceExecuteStatusEnum.RUNNING)) {
            throw new TripscrmException(UPDATE_SUB_TASK_INSTANCE_STATUS_ERROR);
        }
        PlatformLogUtil.logInfo("任务执行-子任务执行有效性检查，更新子任务执行状态运行中", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
    }

    /**
     * 流式触发任务（接口+事件）
     */
    private void executeStreamTask(TaskExecuteContext context) {
        // 任务ID
        Long taskId = context.getTaskId();
        // 任务快照
        TaskInfoDO taskSnapshot;
        // 素材快照
        MaterailInfoDO materialSnapshot;
        // AB分桶快照
        List<AbTestBucketVO> abBucketListSnapshot;
        if (context.getTestFlag()) {
            taskSnapshot = taskService.queryTaskById(taskId);
            materialSnapshot = materialService.queryById(taskSnapshot.getMaterialId());
            abBucketListSnapshot = abTestBucketService.listByTaskId(taskId);
            context.setTaskInfoDOSnapshot(taskSnapshot);
            context.setMaterialSnapshot(materialSnapshot);
            context.setAbBucketListSnapshot(abBucketListSnapshot);
        }
        // 动态校验任务的合法性
        taskService.checkTaskValid(context.getTaskInfoDOSnapshot(), context.getTestFlag());
        checkTaskExecuteTimeValid(context);
        PlatformLogUtil.logInfo("任务执行-子任务执行有效性检查，任务配置检查通过", LogListUtil.newArrayList(context));
    }

    private void checkTaskExecuteTimeValid(TaskExecuteContext context) {
        if (context.getTestFlag()) {
            return;
        }

        if (!taskService.checkInWorkTimeWindow(context.getTaskInfoDOSnapshot())) {
            throw new TripscrmException(TASK_EXECUTE_NOT_IN_WORK_TIME);
        }
    }

    /**
     * 子任务实例合法校验
     * 对应的主任务实例数据是否存在，以及状态是否失败
     */
    private SubTaskInstanceDO checkAndGetSubTaskInstance(Long subTaskInstanceId) {
        SubTaskInstanceDO subTaskInstanceDO = subTaskInstanceService.queryById(subTaskInstanceId);
        // 没有子任务实例数据
        if (Objects.isNull(subTaskInstanceDO)) {
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_SUB_TASK_INSTANCE_DATA);
        }

        // 子任务实例数据未配置主任务实例
        if (subTaskInstanceDO.getMainTaskId() == null) {
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MAIN_TASK_INSTANCE_OF_SUB_TASK_INSTANCE);
        }

        // 主任务实例数据不存在
        TaskInstanceDO mainTaskInstanceDO = taskInstanceService.queryById(subTaskInstanceDO.getMainTaskInstanceId());
        if (Objects.isNull(mainTaskInstanceDO)) {
            throw new TripscrmException(NOT_EXIST_TASK_INSTANCE_DATA);
        }

        // 主任务已经执行失败/执行完成
        if (Lists.newArrayList(TaskInstanceStatusEnum.RUN_FAIL, TaskInstanceStatusEnum.RUN_SUCCESS).contains(TaskInstanceStatusEnum.getByStatus(mainTaskInstanceDO.getStatus()))) {
            throw new TripscrmException(MAIN_TASK_INSTANCE_STATUS_RUN_FAIL_OR_RUN_SUCCESS);
        }

        // 运行中的任务不允许重入
        if (!allowedSubTaskRunningReentrant && Objects.equals(TaskInstanceStatusEnum.RUNNING.getStatus(), subTaskInstanceDO.getStatus())) {
            throw new TripscrmException(SUB_TASK_INSTANCE_ALREADY_RUNNING);
        }
        return subTaskInstanceDO;
    }
}
