package com.alibaba.tripscrm.service.service.task.effectdata;

import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.domain.query.ScrmTaskEffectDataQuery;
import com.alibaba.tripscrm.service.model.dto.task.ScrmTaskEffectDataDTO;

import java.util.List;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/6/24 16:50
 * @Filename：ScrmTaskEffectDataService
 */
public interface ScrmTaskEffectDataService {

    /**
     * 分页查询任务效果数据
     * @param query
     * @return
     */
    PageInfoDTO<ScrmTaskEffectDataDTO> pageQuery(ScrmTaskEffectDataQuery query);

    /**
     * 查询任务效果数据
     * @param query
     * @return
     */
    List<ScrmTaskEffectDataDTO> list(ScrmTaskEffectDataQuery query);
}
