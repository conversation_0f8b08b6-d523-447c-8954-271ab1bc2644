package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatContactMeDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.CallPushDataUpdateEventTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.request.AccountRiskConfigUpdateRequest;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionStatus;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.risk.RiskPanelService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatContactMeService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.request.WechatSendAddFriendRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.util.Ateye;
import com.taobao.util.CollectionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 手机号添加好友任务执行器
 *
 * <AUTHOR>
 * @since 2024/8/2 18:51
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AddCustomerByPhoneTaskExecutor extends AbstractTaskExecutor {
    private final CustomerService customerService;
    private final WechatContactMeService wechatContactMeService;
    private final WechatUserService wechatUserService;
    private final LdbTairManager ldbTairManager;
    private final MetaqProducer producer;
    private final TaskService taskService;
    private final IsvRouteService isvRouteService;

    @Resource
    private RiskPanelService riskPanelService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO dataBodyVO = todoTaskVO.getData().get(0);
        String cellPhone = getFinalTargetId(context, dataBodyVO);
        String sendUserId = getSendUserId(context, todoTaskVO);
        WechatSendAddFriendRequest wechatSendAddFriendRequest = new WechatSendAddFriendRequest();
        wechatSendAddFriendRequest.setUserId(sendUserId);
        wechatSendAddFriendRequest.setMobile(getFinalTargetId(context, dataBodyVO));
        wechatSendAddFriendRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());


        ActivityTaskInfoBO targetActivityContext = getTargetActivityContext(context, dataBodyVO);
        if (Objects.nonNull(targetActivityContext)) {
            wechatSendAddFriendRequest.setState(String.valueOf(getTargetActivityContext(context, dataBodyVO).getContextId()));
        }

        ResultDO<Boolean> resultDO = customerService.sendAddFriendRequestByMobile(wechatSendAddFriendRequest);
        if (!Optional.ofNullable(resultDO).map(ResultDO::getModel).orElse(false)) {
            PlatformLogUtil.logFail("发送加企微的请求失败", LogListUtil.newArrayList(wechatSendAddFriendRequest, resultDO, context.getTaskId()));
            JSONObject data = new JSONObject();
            data.put("targetId", cellPhone);
            data.put("targetType", ActivityTargetTypeEnum.PHONE_MOBILE.getCode());
            data.put("eventType", CallPushDataUpdateEventTypeEnum.ADD_CUSTOMER_BY_PHONE.getCode());
            data.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
            data.put("result", false);
            data.put(TaskConstant.ACTIVITY_ID, context.getTaskInfoDOSnapshot().getActivityId());
            producer.send(MQEnum.SCRM_CALL_PUSH_DATA_PROCESS, cellPhone, CallPushDataUpdateEventTypeEnum.ADD_CUSTOMER_BY_PHONE.getCode(), data.toJSONString());
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED);
        }

        calc(new Date().getTime(), sendUserId);
        PlatformLogUtil.logInfo("发送加企微的请求完成", LogListUtil.newArrayList(wechatSendAddFriendRequest, resultDO, context.getTaskId()));

        JSONObject data = new JSONObject();
        data.put("targetId", cellPhone);
        data.put("targetType", ActivityTargetTypeEnum.PHONE_MOBILE.getCode());
        data.put("eventType", CallPushDataUpdateEventTypeEnum.ADD_CUSTOMER_BY_PHONE.getCode());
        data.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
        data.put("result", true);
        data.put(TaskConstant.ACTIVITY_ID, context.getTaskInfoDOSnapshot().getActivityId());
        producer.send(MQEnum.SCRM_CALL_PUSH_DATA_PROCESS, cellPhone, CallPushDataUpdateEventTypeEnum.ADD_CUSTOMER_BY_PHONE.getCode(), data.toJSONString());
    }

    @AteyeInvoker(description = "更新主加发起统计信息", paraDesc = "timestamp&sendUserId")
    public void calc(Long timestamp, String sendUserId) {
        Date date = new Date(timestamp);
        Long dayVersion = DateUtils.getDayVersion(date);
        Long minuteVersion = DateUtils.getMinuteVersion(date);
        Long windowVersion = minuteVersion / 10;
        String dayKey = TairConstant.WECHAT_USER_ADD_CUSTOMER_COUNT_IN_WINDOW_PREFIX + dayVersion + "_d_" + SpaceInfoThreadLocalUtils.getCorpId() + "_" + sendUserId;
        String windowKey = TairConstant.WECHAT_USER_ADD_CUSTOMER_COUNT_IN_WINDOW_PREFIX + windowVersion + "_w_" + SpaceInfoThreadLocalUtils.getCorpId() + "_" + sendUserId;
        ldbTairManager.incr(dayKey, 1, 0, 60 * 60 * 25);
        ldbTairManager.incr(windowKey, 1, 0, 60 * 60);
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        // 上下文缓存取
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            return (String) taskDataBody.getContext().get("sendUserId");
        }

        // 获取指定的发送者id
        String specifiedSendUserId = getSpecifiedSendUserId(context, taskDataBody);
        if (specifiedSendUserId != null) {
            taskDataBody.getContext().put("sendUserId", specifiedSendUserId);
            return specifiedSendUserId;
        }

        String sendUserId = getAddCustomerWechatUserId(context);
        if (!StringUtils.hasText(sendUserId)) {
            PlatformLogUtil.logFail("获取可主加的机器人失败", LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }
        return sendUserId;
    }

    @AteyeInvoker(description = "获取主加好友的企微账号", paraDesc = "taskId")
    public String getAddCustomerWechatUserId(Long taskId) {
        TaskExecuteContext context = new TaskExecuteContext();
        TaskInfoDO taskInfoDO = taskService.queryTaskById(taskId);
        context.setTaskInfoDOSnapshot(taskInfoDO);
        return getAddCustomerWechatUserId(context);
    }

    private String getAddCustomerWechatUserId(TaskExecuteContext context) {
        // 根据个人活码配置匹配企微号
        String userId = getAddCustomerWechatUserIdFromWechatContactMeConfig(context);
        if (StringUtils.hasText(userId)) {
            return userId;
        }
        // 空间兜底匹配企微号
        return getAddCustomerWechatUserIdFromSpace(context.getTaskInfoDOSnapshot().getSpaceId());
    }

    private String getAddCustomerWechatUserIdFromWechatContactMeConfig(TaskExecuteContext context) {
        JSONObject taskExtInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        String state = taskExtInfoJson.getString(TaskConstant.WECHAT_CONTACT_ME_STATE);
        if (!StringUtils.hasText(state)) {
            PlatformLogUtil.logFail("获取执行主加好友行动项对应的企微号，state非法", LogListUtil.newArrayList(context.getTaskId()));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WechatContactMeDO wechatContactMeDO = wechatContactMeService.getByState(state);
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setRiskActionEnum(RiskActionEnum.CUSTOMER_SEND_ADD_FRIEND_REQUEST_BY_PHONE);
        isvRouteContext.setUserIdList(Arrays.stream(wechatContactMeDO.getUser().split(",")).filter(StringUtils::hasText).collect(Collectors.toList()));
        isvRouteContext.setSpaceId(context.getTaskInfoDOSnapshot().getSpaceId());
        TripSCRMResult<WechatUserDTO> result = isvRouteService.matchWechatUser(isvRouteContext);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            PlatformLogUtil.logFail("获取主加好友的企微成员，查询活码中绑定的可用企微成员结果为空", LogListUtil.newArrayList(state, isvRouteContext));
            return null;
        }
        return result.getData().getUserId();
    }

    @AteyeInvoker(description = "获取空间下可用于主加好友的企微号（随机返回一个）", paraDesc = "spaceId")
    public String getAddCustomerWechatUserIdFromSpace(Long spaceId) {
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setRiskActionEnum(RiskActionEnum.CUSTOMER_SEND_ADD_FRIEND_REQUEST_BY_PHONE);
        isvRouteContext.setSpaceId(spaceId);
        TripSCRMResult<WechatUserDTO> result = isvRouteService.matchWechatUser(isvRouteContext);
        if (!result.isSuccess() || Objects.isNull(result.getData())) {
            PlatformLogUtil.logFail("获取主加好友的企微成员，获取空间下可用于主加好友的企微号结果为空", LogListUtil.newArrayList(spaceId, isvRouteContext));
            return null;
        }
        return result.getData().getUserId();
    }

    @AteyeInvoker(description = "将机器人外呼后通过手机号加好友的接口权限挂起", paraDesc = "机器人账号列表&员工账号&员工昵称或者姓名")
    public int updateUserRiskSchemaAddFriendByMobile(String userIds, String empId, String empNick){
        if(org.apache.commons.lang.StringUtils.isBlank(userIds)){
            Ateye.out.println("机器人账号列表不能为空");
            return 0;
        }
        List<String> userIdList = Arrays.asList(userIds.split(","));
        List<WechatUserDTO> wechatUserDTOS = wechatUserService.listById(userIdList);
        if (CollectionUtil.isEmpty(wechatUserDTOS)) {
            Ateye.out.println("根据机器人账号列表获取机器人信息为空");
            return 0;
        }
        Ateye.out.println("入参传入账号数" + userIdList.size());
        Ateye.out.println("查询信息成功的账号数" + wechatUserDTOS.size());

        List<String> updateConfigSuccessUserIds = new ArrayList<>();
        List<String> updateConfigFailUserIds = new ArrayList<>();
        for(WechatUserDTO userVO: wechatUserDTOS) {
            AccountRiskConfigUpdateRequest param = new AccountRiskConfigUpdateRequest();
            param.setRiskId("robot|" + userVO.getCorpId() + "|" + userVO.getUserId() + "|" + userVO.getRobotType());

            RiskActionStatus status = new RiskActionStatus();
            status.setActionCode(RiskActionEnum.CUSTOMER_SEND_ADD_FRIEND_REQUEST_BY_PHONE.getActionCode());
            status.setActionName(RiskActionEnum.CUSTOMER_SEND_ADD_FRIEND_REQUEST_BY_PHONE.getName());
            status.setStatusCode(RiskStatusEnum.HOLD.getCode());
            param.setActionStatuses(status);
            try {
                boolean result = riskPanelService.setRiskConfig(param);
                if(result){
                    updateConfigSuccessUserIds.add(userVO.getUserId());
                }else {
                    updateConfigFailUserIds.add(userVO.getUserId());
                }
            }catch (Exception e){
                PlatformLogUtil.logException("将机器人外呼后通过手机号加好友的接口权限挂起出现异常", e.getMessage(), e, LogListUtil.newArrayList(param));
                Ateye.out.println("将机器人外呼后通过手机号加好友的接口权限挂起出现异常" + JSONObject.toJSONString(param));
            }
        }
        Ateye.out.println("更新规则配置成功的账号数目" + updateConfigSuccessUserIds.size());
        Ateye.out.println("更新规则配置失败的账号数目" + updateConfigFailUserIds.size());

        Ateye.out.println("更新规则配置成功的账号" + updateConfigSuccessUserIds);
        Ateye.out.println("更新规则配置失败的账号" + updateConfigFailUserIds);
        return updateConfigSuccessUserIds.size();
    }

    private String getSpecifiedSendUserId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        String sendUserId;
        // 先取事件源或接口指定的发送者id
        Map<String, Object> extInfo = context.getExtInfo();
        if (extInfo != null && extInfo.containsKey("sendUserId")) {
            sendUserId = (String) extInfo.get("sendUserId");
            PlatformLogUtil.logFail("从上下文扩展获取sendUserId", LogListUtil.newArrayList(taskDataBody, sendUserId));
            checkSendUserOnline(taskDataBody, sendUserId);
            return sendUserId;
        }

        // 再取离线表指定的发送者id
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        if (extInfoJson.containsKey("sendUserId")) {
            sendUserId = extInfoJson.getString("sendUserId");
            PlatformLogUtil.logFail("从taskDataBody取sendUserId", LogListUtil.newArrayList(taskDataBody, sendUserId));
            checkSendUserOnline(taskDataBody, sendUserId);
            return sendUserId;
        }

        return null;
    }

    private void checkSendUserOnline(TaskDataVO.DataBodyVO taskDataBody, String sendUserId) {
        // 判断指定的发送者id是否在线
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(sendUserId));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wechatUserList) && Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
            return;
        }

        PlatformLogUtil.logFail("私聊消息发送过程中指定的发送者id不在线", LogListUtil.newArrayList(taskDataBody, sendUserId));
        throw new TripscrmException(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE);
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.PHONE_MOBILE;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        return taskDataBody.getTargetId();
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.ADD_WECHAT_CUSTOMER_BY_PHONE;
    }
}
