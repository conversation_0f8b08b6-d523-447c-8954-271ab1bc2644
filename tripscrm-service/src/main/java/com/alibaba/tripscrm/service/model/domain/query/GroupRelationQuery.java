package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 群组关系信息查询条件
 */
@Data
public class GroupRelationQuery {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID列表
     */
    private List<Long> idList;

    /**
     * chat_id
     */
    private String chatId;

    /**
     * chat_id列表
     */
    private List<String> chatIdList;

    /**
     * 分表使用，每份关系保存2条记录，格式：1_user_id和2_chat_id
     */
    private String shardingKey;

    /**
     * 分表使用，每份关系保存2条记录，格式：1_user_id和2_chat_id
     */
    private List<String> shardingKeyList;


    /**
     * user_id
     */
    private String userId;

    /**
     * user_id列表
     */
    private List<String> userIdList;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 企业ID列表
     */
    private List<String> corpIdList;
    /**
     * 平台类型
     */
    private PlatformTypeEnum platformType;


    /**
     * 用户类型 1企微成员，2客户
     */
    private Byte userType;

    /**
     * 用户类型列表
     */
    private List<Byte> userTypeList;

    /**
     * 群聊名称
     */
    private String groupName;

    /**
     * 群聊名称模糊查询
     */
    private String groupNameLike;

    /**
     * 成员名称
     */
    private String name;

    /**
     * 成员名称模糊查询
     */
    private String nameLike;

    /**
     * 状态 1未退群，2已退群
     */
    private Byte status;

    /**
     * 状态列表
     */
    private List<Byte> statusList;

    /**
     * 是否删除 0未删除，1删除
     */
    private Byte isDeleted;

    /**
     * 入群时间开始
     */
    private Date joinTimeStart;

    /**
     * 入群时间结束
     */
    private Date joinTimeEnd;

    /**
     * 成员类型 0普通成员，1管理员，2群主
     */
    private Byte memberType;

    /**
     * 成员类型列表
     */
    private List<Byte> memberTypeList;
}
