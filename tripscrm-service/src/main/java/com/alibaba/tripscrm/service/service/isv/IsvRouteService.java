package com.alibaba.tripscrm.service.service.isv;

import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.model.domain.query.IsvRouteStrategyPageQuery;
import com.alibaba.tripscrm.service.model.dto.isv.GroupMemberAllocationDTO;
import com.alibaba.tripscrm.service.model.dto.isv.GroupMemberAllocationStrategyDTO;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.dto.isv.IsvRouteStrategyDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 服务商能力路由策略
 *
 * <AUTHOR>
 * @since 2025/2/21 18:06
 */
public interface IsvRouteService {
    /**
     * 获取指定服务商分流策略
     *
     * @return IsvRouteStrategyDTO
     */
    IsvRouteStrategyDTO getIsvRouteStrategyByAction(RiskActionEnum riskActionEnum);
    /**
     * 获取指定服务商分流策略
     *
     * @return IsvRouteStrategyDTO
     */
    IsvRouteStrategyDTO getIsvRouteStrategyByActionWithCache(RiskActionEnum riskActionEnum, String env);

    /**
     * 分页获取服务商分流策略
     *
     * @return IsvRouteStrategyDTO
     */
    PageInfo<IsvRouteStrategyDTO> pageQueryIsvRouteStrategy(IsvRouteStrategyPageQuery query);

    /**
     * 增加服务商分流策略配置
     *
     * @return 主键Id
     */
    TripSCRMResult<Boolean> addIsvRouteStrategy(IsvRouteStrategyDTO isvRouteStrategyDTO);

    /**
     * 更新服务商分流策略配置
     *
     * @return 影响行数
     */
    TripSCRMResult<Boolean> updateIsvRouteStrategy(IsvRouteStrategyDTO isvRouteStrategyDTO);

    /**
     * 服务商群成员分配策略配置
     *
     * @return GroupMemberAllocationStrategyDTO
     */
    TripSCRMResult<GroupMemberAllocationStrategyDTO> getGroupMemberAllocationStrategy();

    /**
     * 更新服务商群成员分配策略配置
     *
     * @return return
     */
    TripSCRMResult<Boolean> updateGroupMemberAllocationStrategy(GroupMemberAllocationStrategyDTO groupMemberAllocationStrategyDTO);

    /**
     * 获取执行行动项对应的企微号
     *
     * @param isvRouteContext 上下文
     * @return WechatUserDTO
     */
    TripSCRMResult<WechatUserDTO> matchWechatUser(IsvRouteContext isvRouteContext);

    /**
     * 判断是否匹配群成员分配策略
     *
     * @return 判断是否匹配群成员分配策略
     */
    TripSCRMResult<Boolean> isMatchGroupMemberAllocationStrategy(GroupMemberAllocationDTO groupMemberAllocationDTO, Long spaceId);

    /**
     * 根据传入的企微号UserId，对群成员账号进行分配
     *
     * @param userIdList 企微成员UserId列表
     * @param spaceId    企微空间Id
     * @param count      需要分配账号数量
     * @return List<WechatUserDTO>
     */
    TripSCRMResult<GroupMemberAllocationDTO> getGroupAllocationWechatUserList(List<String> userIdList, Long spaceId, Integer count);
}
