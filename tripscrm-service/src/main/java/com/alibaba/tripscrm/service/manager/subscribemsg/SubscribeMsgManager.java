package com.alibaba.tripscrm.service.manager.subscribemsg;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.request.miniprogram.subscribemsg.SCRMSubscribeMsgSendRequest;
import com.alibaba.tripscrm.service.model.dto.subscribemsg.SubscribeMsgTemplateDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripzoo.proxy.api.service.multiminiprogram.MiniProgramSubscribeMsgService;
import com.alibaba.tripzoo.proxy.enums.miniprogram.MiniProgramPlatformEnum;
import com.alibaba.tripzoo.proxy.enums.miniprogram.MsgTemplateSubStatusEnum;
import com.alibaba.tripzoo.proxy.model.multiminiprogram.SubscribeRelationDTO;
import com.alibaba.tripzoo.proxy.request.multiminiprogram.SubscribeMsgSendRequest;
import com.alibaba.tripzoo.proxy.request.multiminiprogram.SubscribeRelationQueryRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.miniprogram.SubscribeRelationQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 小程序订阅消息管理
 *
 * <AUTHOR>
 * @since 2025-09-16
 */
@Slf4j
@Component
public class SubscribeMsgManager {

    @Resource
    private MiniProgramSubscribeMsgService miniProgramSubscribeMsgService;

    @Resource
    private MaterialTransferService materialTransferService;

    @Resource
    private UicUtils uicUtils;

    public TripSCRMResult<Boolean> sendSubscribeMsg(SCRMSubscribeMsgSendRequest scrmSubscribeMsgSendRequest) {
        if (Objects.isNull(scrmSubscribeMsgSendRequest) || !StringUtils.hasText(scrmSubscribeMsgSendRequest.getTargetId())
                || Objects.isNull(scrmSubscribeMsgSendRequest.getMaterialId()) || Objects.isNull(scrmSubscribeMsgSendRequest.getMaterialTrackRelationDTO())
                || Objects.isNull(scrmSubscribeMsgSendRequest.getMaterialContentConvertContext())) {
            PlatformLogUtil.logFail("小程序订阅消息管理，发送订阅消息，参数非法", LogListUtil.newArrayList(scrmSubscribeMsgSendRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        // 构建订阅消息模板
        SubscribeMsgTemplateDTO subscribeMsgTemplateDTO = materialTransferService.buildSubscribeMsg(
                scrmSubscribeMsgSendRequest.getMaterialId(),
                scrmSubscribeMsgSendRequest.getMaterialTrackRelationDTO(),
                scrmSubscribeMsgSendRequest.getMaterialContentConvertContext());

        if (Objects.isNull(subscribeMsgTemplateDTO)) {
            PlatformLogUtil.logFail("小程序订阅消息管理，发送订阅消息，构建订阅消息失败", LogListUtil.newArrayList(scrmSubscribeMsgSendRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }

        // 根据模版平台类型查询用户id
        String targetId = getSendSubscribeMsgTargetId(scrmSubscribeMsgSendRequest.getTargetId(), MiniProgramPlatformEnum.valueOf(subscribeMsgTemplateDTO.getPlatformType()));

        // 校验用户是否订阅该模版
        if (!checkUserSubscribeMsg(targetId, subscribeMsgTemplateDTO.getTemplateId(), MiniProgramPlatformEnum.valueOf(subscribeMsgTemplateDTO.getPlatformType()))) {
            PlatformLogUtil.logFail("小程序订阅消息管理，发送订阅消息，用户未订阅该模版", LogListUtil.newArrayList(targetId, subscribeMsgTemplateDTO.getTemplateId()));
            return TripSCRMResult.fail(TripSCRMErrorCode.USER_NOT_SUBSCRIBE_MSG);
        }

        // 构建proxy服务请求
        SubscribeMsgSendRequest subscribeMsgSendRequest = buildSubscribeMsgSendRequest(subscribeMsgTemplateDTO, targetId);

        // 发送订阅消息
        if (!SwitchConfig.SEND_SUBSCRIBE_MSG_SWITCH) {
            PlatformLogUtil.logFail("小程序订阅消息管理，发送订阅消息，发送订阅消息开关关闭", LogListUtil.newArrayList(subscribeMsgSendRequest));
            return TripSCRMResult.success(true);
        }
        try {
            ResultDO<Boolean> resultDO = miniProgramSubscribeMsgService.sendSubscribeMsg(subscribeMsgSendRequest);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
                PlatformLogUtil.logFail("小程序订阅消息管理，发送订阅消息，发送订阅消息失败", LogListUtil.newArrayList(subscribeMsgSendRequest, resultDO));
                return TripSCRMResult.fail(TripSCRMErrorCode.SEND_SUBSCRIBE_MSG_FAIL);
            }
            return TripSCRMResult.success(resultDO.getModel());
        } catch (Exception e) {
            PlatformLogUtil.logException("小程序订阅消息管理，发送订阅消息，发送订阅消息异常", e.getMessage(), e, LogListUtil.newArrayList(subscribeMsgSendRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.SEND_SUBSCRIBE_MSG_FAIL);
        }
    }

    private SubscribeMsgSendRequest buildSubscribeMsgSendRequest(SubscribeMsgTemplateDTO templateDTO, String targetId) {
        SubscribeMsgSendRequest subscribeMsgSendRequest = new SubscribeMsgSendRequest();
        subscribeMsgSendRequest.setTargetId(targetId);
        subscribeMsgSendRequest.setPlatformType(MiniProgramPlatformEnum.valueOf(templateDTO.getPlatformType()));
        subscribeMsgSendRequest.setPageUrl(templateDTO.getPageUrl());
        subscribeMsgSendRequest.setMsgTemplateParams(buildTemplateParams(templateDTO.getParamList()));
        subscribeMsgSendRequest.setTemplateId(templateDTO.getTemplateId());
        return subscribeMsgSendRequest;
    }

    /**
     * 校验用户是否订阅该模版
     *
     * @param targetId     目标用户id
     * @param templateId   模版id
     * @param platformType 平台类型
     * @return 是否订阅
     */
    private boolean checkUserSubscribeMsg(String targetId, String templateId, MiniProgramPlatformEnum platformType) {
        if (!StringUtils.hasLength(targetId) || !StringUtils.hasLength(templateId) || Objects.isNull(platformType)) {
            PlatformLogUtil.logFail("校验用户是否订阅该模版失败，targetId或templateId或platformType非法", LogListUtil.newArrayList(targetId, templateId, platformType));
            return false;
        }
        switch (platformType) {
            case ALIPAY:
                SubscribeRelationQueryRequest request = new SubscribeRelationQueryRequest();
                request.setTargetId(targetId);
                request.setPlatformType(platformType);
                request.setTemplateIdList(Collections.singletonList(templateId));
                ResultDO<SubscribeRelationQueryResponse> resultDO = miniProgramSubscribeMsgService.querySubscribeRelation(request);
                if (Objects.isNull(resultDO) || !resultDO.getSuccess() || Objects.isNull(resultDO.getModel())) {
                    PlatformLogUtil.logFail("校验用户是否订阅该模版失败，查询用户订阅关系失败", LogListUtil.newArrayList(targetId, templateId, platformType, resultDO));
                    return false;
                }
                if (CollectionUtils.isEmpty(resultDO.getModel().getRelationList())) {
                    PlatformLogUtil.logFail("校验用户是否订阅该模版失败，订阅关系列表为空，用户未订阅该模版", LogListUtil.newArrayList(targetId, templateId, platformType));
                    return false;
                }
                SubscribeRelationDTO subscribeRelationDTO = resultDO.getModel().getRelationList().stream().filter(relation -> Objects.equals(relation.getTemplateId(), templateId)).findFirst().orElse(null);
                if (Objects.isNull(subscribeRelationDTO) || !Objects.equals(subscribeRelationDTO.getStatus(), MsgTemplateSubStatusEnum.BOOK)) {
                    PlatformLogUtil.logFail("校验用户是否订阅该模版失败，用户未订阅该模版", LogListUtil.newArrayList(targetId, templateId, platformType));
                    return false;
                }
                return true;
            case WEIXIN:
                // 微信暂时无法校验
                return true;
            default:
                PlatformLogUtil.logFail("校验用户是否订阅该模版失败，平台类型不支持", LogListUtil.newArrayList(targetId, templateId, platformType));
                return false;
        }
    }

    /**
     * 根据平台类型获取发送用户id
     *
     * @param targetId     目标用户id
     * @param platformType 平台类型
     * @return 用户id
     */
    private String getSendSubscribeMsgTargetId(String targetId, MiniProgramPlatformEnum platformType) {
        if (!StringUtils.hasLength(targetId) || Objects.isNull(platformType)) {
            PlatformLogUtil.logFail("获取发送订阅消息用户id失败，targetId或platformType非法", LogListUtil.newArrayList(targetId, platformType));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        switch (platformType) {
            case WEIXIN:
                return uicUtils.getMiniAppOpenIdByUid(targetId);
            case ALIPAY:
                return uicUtils.getAlipayIdByUid(targetId);
            default:
                PlatformLogUtil.logFail("获取发送订阅消息用户id失败，平台类型不支持", LogListUtil.newArrayList(targetId, platformType));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
    }

    /**
     * 构建模板参数
     *
     * @param paramList 模板参数列表
     * @return 模板参数映射
     */
    private HashMap<String, String> buildTemplateParams(List<SubscribeMsgTemplateDTO.TemplateParam> paramList) {
        HashMap<String, String> msgTemplateParams = new HashMap<>();
        if (paramList != null) {
            // 按索引排序
            paramList.sort(Comparator.comparing(SubscribeMsgTemplateDTO.TemplateParam::getIndex));
            // 构建参数映射
            for (SubscribeMsgTemplateDTO.TemplateParam templateParam : paramList) {
                msgTemplateParams.put(templateParam.getParamCode(), templateParam.getParamContent());
            }
        }
        return msgTemplateParams;
    }
}
