package com.alibaba.tripscrm.service.model.domain;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 企业微信元数据信息
 * <AUTHOR>
 * @date 2023/4/18
 */
@Data
public class EventMetaInfo {
    private String toUserName;

    private String fromUserName;

    private Long createTime;

    private String msgType;

    private String event;

    private String changeType;

    private String userID;

    /**
     * 企业微信id
     */
    private String externalUserID;

    /**
     * 渠道标识码
     */
    private String state;

    /**
     *用于新增用户时发送欢迎语
     */
    private String welcomeCode;

    /**
     * 删除客户的操作来源，DELETE_BY_TRANSFER表示此客户是因在职继承自动被转接成员删除
     */
    private String source;

    /**
     * 附加信息JSON
     */
    JSONObject extraJson;
}
