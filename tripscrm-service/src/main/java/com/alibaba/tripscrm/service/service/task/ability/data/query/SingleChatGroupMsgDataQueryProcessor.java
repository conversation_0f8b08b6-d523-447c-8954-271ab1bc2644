package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.dto.task.TargetDataDTO;
import com.alibaba.tripscrm.service.service.task.ability.data.query.common.CommonCustomerDataQueryProcessor;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:04:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SingleChatGroupMsgDataQueryProcessor extends AbstractTaskDataProcessor {
    private final CommonCustomerDataQueryProcessor commonCustomerDataQueryProcessor;
    private final TaskService taskService;

    @Switch(description = "私聊群发单次发送条数", name = "batchSendSingleChatGroupMsgDataSize")
    public static Integer batchSendSingleChatGroupMsgDataSize = 10000;

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.SINGLE_CHAT_GROUP_MSG);
    }

    @AteyeInvoker(description = "企微群发数据查询器", paraDesc = "taskId")
    public TaskDataVO handleReadAllData(Long taskId) {
        TaskExecuteContext context = new TaskExecuteContext();
        context.setTaskInfoDOSnapshot(taskService.queryTaskById(taskId));
        context.setInstanceId(-1L);
        return handleReadAllData(context);
    }

    /**
     * 从离线表中从获取发送数据
     *
     * @param context 任务执行上下文
     * @return TaskDataVO
     */
    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        // 查询出所有客户信息
        List<TargetDataDTO> targetDataList = commonCustomerDataQueryProcessor.queryFromTask(context.getTaskInfoDOSnapshot(), context.getInstanceId(), false, ActivityTargetTypeEnum.WX_EXTERNAL_USERID);

        // 按照发送者进行分组
        Map<String, List<TargetDataDTO>> userId2DataList = targetDataList.stream().collect(Collectors.groupingBy(dataBody -> dataBody.getExtInfo().getString("sendUserId")));
        for (String userId : userId2DataList.keySet()) {
            List<TargetDataDTO> dataList = userId2DataList.get(userId);
            // 每10000个作为一个群发任务
            List<List<TargetDataDTO>> partitions = Lists.partition(dataList, batchSendSingleChatGroupMsgDataSize);
            for (List<TargetDataDTO> partition : partitions) {
                TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
                dataBodyVO.setTargetId(userId);
                dataBodyVO.setTargetType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
                List<String> externalUserIdList = partition.stream().map(TargetDataDTO::getTargetId).collect(Collectors.toList());
                JSONObject extInfo = new JSONObject();
                extInfo.put("externalUserIdList", externalUserIdList);
                extInfo.put("sendUserId", userId);
                extInfo.put("uuid", UUID.randomUUID().toString());
                dataBodyVO.setExtInfo(extInfo.toJSONString());
                dataVO.getData().add(dataBodyVO);
            }
        }
        dataVO.setTotalCount((long) dataVO.getData().size());
        return dataVO;
    }
}

