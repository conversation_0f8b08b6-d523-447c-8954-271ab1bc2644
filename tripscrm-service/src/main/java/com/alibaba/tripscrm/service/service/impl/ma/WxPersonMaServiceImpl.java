package com.alibaba.tripscrm.service.service.impl.ma;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.dal.model.domain.data.BizGroupInfoDO;
import com.alibaba.tripscrm.domain.enums.TagBizTypeEnum;
import com.alibaba.tripscrm.service.constant.WxAccountConstant;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.model.vo.wechat.ContactMeVO;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.wechat.WxEnterpriseUserInfoVO;
import com.alibaba.tripscrm.service.model.domain.query.ContactMeQuery;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationCoverRequest;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.WxEnterpriseUserQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.BizGroupService;
import com.alibaba.tripscrm.service.service.ma.WxPersonMaService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.mtop.api.util.StringUtil;
import com.taobao.trip.wireless.mc.client.hsf.EnterpriseWeChatService;
import com.taobao.trip.wireless.mc.client.hsf.domain.common.PagingQuery;
import com.taobao.trip.wireless.mc.client.hsf.domain.common.PagingQueryResult;
import com.taobao.trip.wireless.mc.client.hsf.domain.weChat.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Slf4j
@HSFProvider(serviceInterface = WxPersonMaService.class)
public class WxPersonMaServiceImpl implements WxPersonMaService {

    @Autowired
    private EnterpriseWeChatService enterpriseWeChatService;

    @Autowired
    private TagRelationService tagRelationService;

    @Autowired
    private BizGroupService bizGroupService;

    @Override
    public PageResultVO<Long> addWxContactMeConfig(ContactMeVO contactMeVO) {
        try {
            // state字段由服务端生成, uuid+随机数,无需检查state是否重复了
            Random random = new Random();
            String state = System.currentTimeMillis() + "_" + random.nextInt(100);
            contactMeVO.setState(state);
            WxContactMe wxContactMe = new WxContactMe();
            BeanUtils.copyProperties(contactMeVO, wxContactMe);
            wxContactMe.setTagIds(null);
            //保存个码信息
            WxContactMeResponse wxResponse = enterpriseWeChatService.saveContactMeConfigScrm(wxContactMe);
            if (wxResponse == null || !wxResponse.getSuccess() || wxResponse.getId() == null) {
                PlatformLogUtil.logFail("save fail", LogListUtil.newArrayList(contactMeVO));
                return PageResultVO.failResult(wxResponse.getErrorMsg());
            }
            if (StringUtils.isNotBlank(contactMeVO.getTagIds())) {
                for (String tagId : contactMeVO.getTagIds().split(",")) {
                    //保存标签关联关系
                    ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                    if (StringUtils.isNumeric(tagId)) {
                        itemTagRelationDTO.setTagId(Long.parseLong(tagId));
                    } else {
                        String[] split = tagId.split("_");
                        itemTagRelationDTO.setTagId(Long.parseLong(split[0]));
                        itemTagRelationDTO.setSubCode(split[1]);
                    }
                    itemTagRelationDTO.setItemType(BizTypeEnum.PERSON_MA_OLD.getCode());
                    itemTagRelationDTO.setItemId(String.valueOf(wxResponse.getId()));
                    tagRelationService.upsertSelective(itemTagRelationDTO);
                }
            }
            return PageResultVO.successResult(wxResponse.getId());
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(contactMeVO));
            return PageResultVO.failResult("系统错误，请重试");
        }
    }

    @Override
    public PageResultVO<Integer> updateWxContactMeConfig(ContactMeVO contactMeVO, User user) {
        try {
            WxContactMe wxContactMe = new WxContactMe();
            BeanUtils.copyProperties(contactMeVO, wxContactMe);
            wxContactMe.setTagIds(null);
            WxResponse wxResponse = enterpriseWeChatService.updateContactMeConfig(wxContactMe);
            if (wxResponse == null || !wxResponse.getSuccess()) {
                PlatformLogUtil.logFail("update fail", LogListUtil.newArrayList(contactMeVO));
                return PageResultVO.failResult("更新失败");
            }
            //更新标签内容
            if (StringUtils.isNotBlank(contactMeVO.getTagIds())) {
                List<String> toCoverTagIds = Arrays.stream(contactMeVO.getTagIds().split(",")).collect(Collectors.toList());
                ItemTagRelationCoverRequest itemTagRelationCoverRequest = new ItemTagRelationCoverRequest();
                itemTagRelationCoverRequest.setItemId(String.valueOf(contactMeVO.getId()));
                itemTagRelationCoverRequest.setItemType(BizTypeEnum.PERSON_MA_OLD.getCode());
                itemTagRelationCoverRequest.setTagIdList(toCoverTagIds);
                itemTagRelationCoverRequest.setCreatorId(user.getUserId());
                itemTagRelationCoverRequest.setCreatorName(user.getUserName());
                tagRelationService.cover(itemTagRelationCoverRequest);
            } else {
                // 删除个码标签
                ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                itemTagRelationDTO.setDeleted((byte)1);
                ItemTagRelationQuery condition = new ItemTagRelationQuery();
                condition.setItemType(BizTypeEnum.PERSON_MA_OLD.getCode());
                condition.setItemId(String.valueOf(contactMeVO.getId()));
                tagRelationService.updateSelective(itemTagRelationDTO, condition);
            }
            return PageResultVO.successResult(1);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(contactMeVO));
            return PageResultVO.failResult("系统错误，请重试");
        }
    }

    @Override
    public PageResultVO<Integer> updateWxContactMeGroupConfig(ContactMeVO contactMeVO, User user) {
        try {
            WxContactMe wxContactMe = new WxContactMe();
            BeanUtils.copyProperties(contactMeVO, wxContactMe);
            wxContactMe.setTagIds(null);
            WxResponse wxResponse = enterpriseWeChatService.updateContactMeConfig(wxContactMe);
            if (wxResponse == null || !wxResponse.getSuccess()) {
                PlatformLogUtil.logFail("update fail", LogListUtil.newArrayList(contactMeVO));
                return PageResultVO.failResult("更新失败");
            }
            return PageResultVO.successResult(1);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(contactMeVO));
            return PageResultVO.failResult("系统错误，请重试");
        }
    }

    @Override
    public ContactMeVO queryContactMeById(Long id) {
        try {
            WxContactMeVO wxContactMeVO = enterpriseWeChatService.queryContactMeById(id);
            if (wxContactMeVO == null) {
                PlatformLogUtil.logFail("query fai", LogListUtil.newArrayList(id));
                return null;
            }
            ContactMeVO contactMeVO = new ContactMeVO();
            BeanUtils.copyProperties(wxContactMeVO, contactMeVO);
            return contactMeVO;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(id));
            return null;
        }
    }

    @Override
    public Integer delWxContactMeConfig(Long id, String configId) {
        try {
            WxResponse wxResponse = enterpriseWeChatService.delContactMeConfig(id, WxAccountConstant.groupId_jimei, configId);
            if (wxResponse == null || !wxResponse.getSuccess()) {
                PlatformLogUtil.logFail("update fail", LogListUtil.newArrayList(id, configId));
                return null;
            }
            //删除打标关系
            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            itemTagRelationDTO.setDeleted((byte) 1);
            ItemTagRelationQuery condition = new ItemTagRelationQuery();
            condition.setItemType(BizTypeEnum.PERSON_MA_OLD.getCode());
            condition.setItemId(String.valueOf(id));
            return tagRelationService.updateSelective(itemTagRelationDTO, condition);
        } catch (Exception e) {
            PlatformLogUtil.logException("删除旧个人活码配置，执行异常", e.getMessage(), e, LogListUtil.newArrayList(id, configId));
            return null;
        }
    }

    @Override
    public PageResultVO<ContactMeVO> listWxContactMeConfig(ContactMeQuery query) {
        try {
            List<ContactMeVO> contactMeVOS = new ArrayList<>();
            PagingQuery pagingQuery = new PagingQuery();
            pagingQuery.setPageNum(query.getPageNum());
            pagingQuery.setPageSize(query.getPageSize());
            WxContactMePageRequest request = new WxContactMePageRequest();
            BeanUtils.copyProperties(query, request);
            if (StringUtils.isNotBlank(query.getManager()) && query.getManager().contains("-")) {
                request.setManager(query.getManager().split("-")[1]);
            }
            PagingQueryResult<WxContactMeVO> queryResult = enterpriseWeChatService.queryContactMeConfigList(pagingQuery, request);
            if (Objects.isNull(queryResult) || queryResult.getCount() == 0 || CollectionUtils.isEmpty(queryResult.getCurPageData())) {
                PlatformLogUtil.logFail("list is empty", LogListUtil.newArrayList(query));
                return PageResultVO.successResult(new ArrayList<>(), 0, 0, 0);
            }
            for (WxContactMeVO wxContactMeVO : queryResult.getCurPageData()) {
                ContactMeVO contactMeVO = new ContactMeVO();
                BeanUtils.copyProperties(wxContactMeVO, contactMeVO);

                // 从关联表标签ID和标签名
                ItemTagRelationQuery itemTagRelationQuery = new ItemTagRelationQuery();
                itemTagRelationQuery.setItemId(String.valueOf(contactMeVO.getId()));
                itemTagRelationQuery.setItemType(BizTypeEnum.PERSON_MA_OLD.getCode());
                List<TagInfoDTO> tagInfoList = tagRelationService.selectTagInfoByCondition(itemTagRelationQuery);
                tagInfoList = tagInfoList.stream()
                        .filter(tagInfo -> Objects.equals(tagInfo.getSpaceId(), query.getSpaceId()) || Objects.equals(TagBizTypeEnum.SYSTEM, tagInfo.getTagBizTypeEnum()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tagInfoList)) {
                    contactMeVO.setTagIds(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.joining(",")));
                    contactMeVO.setTagNames(tagInfoList.stream().map(p -> String.valueOf(p.getName())).collect(Collectors.joining(",")));
                }
                if (StringUtil.isNotBlank(contactMeVO.getBizGroupId())) {
                    BizGroupInfoDO bizGroupInfoDO = bizGroupService.queryBizGroupById(Long.parseLong(contactMeVO.getBizGroupId()));
                    contactMeVO.setBizGroupName(Objects.isNull(bizGroupInfoDO) ? null : bizGroupInfoDO.getName());
                }
                contactMeVO.setUserName(getWxUserName(contactMeVO.getUser()));
                contactMeVO.setCreateTime(DateUtils.getDetailDateString(wxContactMeVO.getGmtCreate()));
                if (StringUtils.isNotBlank(query.getTagId())
                        && (StringUtils.isBlank(contactMeVO.getTagIds()) || !contactMeVO.getTagIds().contains(query.getTagId()))) {
                    continue;
                }
                contactMeVOS.add(contactMeVO);
            }
            return PageResultVO.successResult(contactMeVOS, queryResult.getCount(), queryResult.getPageNo(), query.getPageSize());
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return PageResultVO.failResult("系统异常");
        }
    }

    /**
     * 获取微信员工用户名
     *
     * @param userIds
     * @return
     */
    private String getWxUserName(String userIds) {
        if (StringUtils.isBlank(userIds)) {
            return null;
        }
        WxEnterpriseUserQuery query = new WxEnterpriseUserQuery();
        List<WxEnterpriseUserInfoVO> userInfoVOS = listWxEnterpriseUser(query);
        if (CollectionUtils.isEmpty(userInfoVOS)) {
            PlatformLogUtil.logFail("listWxEnterpriseUser fail", LogListUtil.newArrayList(query));
            return null;
        }
        Map<String, String> userInfoMap = new HashMap<>();
        userInfoVOS.forEach(p -> userInfoMap.put(p.getUserId(), p.getName()));
        List<String> userNameList = new ArrayList<>();
        for (String userId : userIds.split(",")) {
            userNameList.add(userInfoMap.get(userId));
        }
        return String.join(",", userNameList);
    }

    @Override
    public List<WxEnterpriseUserInfoVO> listWxEnterpriseUser(WxEnterpriseUserQuery wxEnterpriseUserQuery) {
        try {
            List<WxUserVO> wxUserVOS = enterpriseWeChatService.getFollowersList(wxEnterpriseUserQuery.getGroupId(), wxEnterpriseUserQuery.getName());
            List<WxEnterpriseUserInfoVO> wxEnterpriseUserInfoVOS = new ArrayList<>();
            for (WxUserVO wxUserVO : wxUserVOS) {
                WxEnterpriseUserInfoVO wxEnterpriseUserInfoVO = new WxEnterpriseUserInfoVO();
                BeanUtils.copyProperties(wxUserVO, wxEnterpriseUserInfoVO);
                wxEnterpriseUserInfoVOS.add(wxEnterpriseUserInfoVO);
            }
            return wxEnterpriseUserInfoVOS;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(wxEnterpriseUserQuery));
            return null;
        }
    }

    @Override
    public Integer countByBizGroup(Long bizGroup) {
        try {
            WxContactMePageRequest wxContactMePageRequest = new WxContactMePageRequest();
            wxContactMePageRequest.setBizGroupId(String.valueOf(bizGroup));
            wxContactMePageRequest.setGroupId(WxAccountConstant.groupId_jimei);
            return enterpriseWeChatService.countContactMeConfig(wxContactMePageRequest);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(bizGroup));
            return null;
        }
    }
}
