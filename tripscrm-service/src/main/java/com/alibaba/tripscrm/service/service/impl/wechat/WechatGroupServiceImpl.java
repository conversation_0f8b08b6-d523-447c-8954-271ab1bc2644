package com.alibaba.tripscrm.service.service.impl.wechat;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.GroupRelationMapper;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatGroupMapper;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.model.domain.result.GroupMemberCountResult;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.dal.repository.WechatGroupRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TagBizTypeEnum;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.convert.TagConverter;
import com.alibaba.tripscrm.service.convert.WechatUserConverter;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.task.UserRelationStatusEnum;
import com.alibaba.tripscrm.service.enums.wechat.MemberTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.GroupCustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.manager.opensearch.GroupUserOpenSearchManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupCustomerCountQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupListQuery;
import com.alibaba.tripscrm.service.model.domain.request.*;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.vo.tag.TagInfoVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupRelationVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.common.JoinGroupRelationService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.request.*;
import com.alibaba.tripzoo.proxy.result.GroupInviteJoinResponse;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.service.impl.fusionchat.ChatConversationServiceImpl.DEFAULT_GROUP_AVATAR;


/**
 * <AUTHOR>
 * @date 2023-07-04 21:30:18
 */
@Slf4j
@Service
public class WechatGroupServiceImpl implements WechatGroupService {
    @Resource
    private GroupUserOpenSearchManager groupUserOpenSearchManager;
    @Resource
    private GroupCustomerRelationOpenSearchManager groupCustomerRelationOpenSearchManager;
    @Resource
    private TagInfoService tagInfoService;
    @Resource
    private TagRelationService tagRelationService;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private WechatGroupMapper wechatGroupMapper;
    @Resource
    private TagConverter tagConverter;
    @Resource
    private GroupRelationMapper groupRelationMapper;
    @Resource
    private WechatGroupRepository wechatGroupRepository;
    @Resource
    private GroupRelationRepository groupRelationRepository;
    @Resource
    private GroupService groupService;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private JoinGroupRelationService groupCodeRelationService;
    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private WechatUserConverter wechatUserConverter;

    public final static int EXPIRE_TIME = 60 * 60 * 60 * 24;

    @Override
    public PageInfoDTO<WechatGroupVO> listPageInfo(WechatGroupListQuery query) {
        PageInfo<WechatGroupVO> pageInfo = groupUserOpenSearchManager.getGroupList(query);
        List<WechatUserDTO> wechatUserList = wechatUserService.listAll();
        List<WechatUserDTO> spaceWechatUserList = wechatUserService.listBySpaceId(query.getSpaceId());
        Map<String, WechatGroupVO> wechatGroupAllInfoMap = wechatGroupService.listByChatIdList(pageInfo.getList().stream().map(WechatGroupVO::getChatId).collect(Collectors.toList()), false).stream().collect(Collectors.toMap(WechatGroupVO::getChatId, wechatGroupVO -> wechatGroupVO));
        wechatUserList.forEach(wechatUserDTO -> wechatUserDTO.setAvatarUrl(StringUtils.hasText(wechatUserDTO.getAvatarUrl()) ? wechatUserDTO.getAvatarUrl() : WxConstants.DEFAULT_AVATAR));
        Map<String, WechatUserDTO> wechatUserMap = wechatUserList.stream().collect(Collectors.toMap(WechatUserDTO::getUserId, Function.identity()));
        Map<String, WechatUserDTO> spaceWechatUserMap = spaceWechatUserList.stream().collect(Collectors.toMap(WechatUserDTO::getUserId, Function.identity()));
        pageInfo.getList().forEach(wechatGroup -> {
            wechatGroup.setAdminUserList(wechatGroup.getAdminUserIdList().stream().map(userId -> wechatUserConverter.convert2VO(wechatUserMap.get(userId))).collect(Collectors.toList()));
            List<TagInfoVO> tagList = tagInfoService.selectByTagIdList(new ArrayList<>(wechatGroup.getTagIdList()))
                    .stream()
                    .filter(tagInfo -> Objects.equals(tagInfo.getSpaceId(), query.getSpaceId()) || Objects.equals(TagBizTypeEnum.SYSTEM, tagInfo.getTagBizTypeEnum()))
                    .map(tagConverter::convert2VO)
                    .collect(Collectors.toList());
            wechatGroup.setTagList(tagList);
            wechatGroup.setNotice(Optional.ofNullable(wechatGroupAllInfoMap.get(wechatGroup.getChatId())).orElse(new WechatGroupVO()).getNotice());
            if (wechatUserMap.containsKey(wechatGroup.getOwnerUserId())) {
                wechatGroup.setOwnerUser(wechatUserConverter.convert2VO(wechatUserMap.get(wechatGroup.getOwnerUserId())));
                wechatGroup.setHasOwnerPermission(spaceWechatUserMap.containsKey(wechatGroup.getOwnerUserId()));
            }

            wechatGroup.setHasAdminPermission(wechatGroup.getAdminUserIdList().stream().anyMatch(spaceWechatUserMap::containsKey));
        });

        return PageUtils.getPageInfoDTO(pageInfo, a -> a);
    }

    private boolean hasOwnerPermission(List<WechatUserDTO> spaceWechatUserList, WechatGroupVO wechatGroupVO) {
        return spaceWechatUserList.stream().map(WechatUserDTO::getUserId).anyMatch(y -> Objects.equals(wechatGroupVO.getOwnerUserId(), y));
    }

    private boolean hasAdminPermission(List<WechatUserDTO> spaceWechatUserList, WechatGroupVO wechatGroupVO) {
        return wechatGroupVO.getAdminUserIdList().stream().anyMatch(x -> spaceWechatUserList.stream().map(WechatUserDTO::getUserId).anyMatch(y -> Objects.equals(x, y)));
    }

    @Override
    public boolean hasAdminPermission(Long spaceId, String chatId) {
        List<WechatUserDTO> spaceWechatUserList = wechatUserService.listBySpaceId(spaceId);
        List<WechatGroupVO> wechatGroupList = listByChatIdList(Lists.newArrayList(chatId), false);

        if (CollectionUtils.isEmpty(spaceWechatUserList) || CollectionUtils.isEmpty(wechatGroupList)) {
            return false;
        }

        return hasAdminPermission(spaceWechatUserList, wechatGroupList.get(0));
    }

    @Override
    public int inviteJoinGroup(InviteJoinGroupRequest request) {
        // 发送邀请请求
        GroupInviteJoinRequest groupInviteJoinRequest = new GroupInviteJoinRequest();
        groupInviteJoinRequest.setUserId(request.getUserId());
        groupInviteJoinRequest.setChatId(request.getChatId());
        groupInviteJoinRequest.setUserIdList(request.getUserIdList());
        groupInviteJoinRequest.setExternalUserIdList(request.getExternalUserIdList());
        groupInviteJoinRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<GroupInviteJoinResponse> groupInviteJoinResponseResultDO = groupService.asyncInviteJoinGroup(groupInviteJoinRequest);
        if (!groupInviteJoinResponseResultDO.getSuccess()) {
            return UserRelationStatusEnum.NOT_ADDED.getCode();
        }
        // 缓存数据
        ArrayList<String> result = new ArrayList<>();
        List<String> userIdList = request.getUserIdList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userIdList)) {
            result.addAll(userIdList);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(request.getExternalUserIdList())) {
            result.addAll(request.getExternalUserIdList());
        }

        if (SwitchConfig.CACHE_IS_OPEN) {
            for (String invitedUserId : result) {
                String key = TairConstant.APPLY_TO_JOIN_GROUP_PREFIX + request.getChatId() + "_" + invitedUserId;
                ldbTairManager.put(key, invitedUserId, EXPIRE_TIME);
            }
        }
        return UserRelationStatusEnum.TO_BE_ACCEPTED.getCode();
    }

    @Override
    public boolean addCustomerByGroup(AddCustomerByGroupRequest request) {
        // 发送邀请请求
        WechatGroupAddCustomerRequest wechatGroupAddCustomerRequest = new WechatGroupAddCustomerRequest();
        wechatGroupAddCustomerRequest.setUserId(request.getUserId());
        wechatGroupAddCustomerRequest.setExternalUserId(request.getExternalUserId());
        wechatGroupAddCustomerRequest.setChatId(request.getChatId());
        wechatGroupAddCustomerRequest.setMessage(request.getMessage());
        wechatGroupAddCustomerRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<String> resultDO = groupService.asyncAddCustomerByGroup(wechatGroupAddCustomerRequest);
        if (!resultDO.getSuccess()) {
            return false;
        }
        // 缓存数据
        if (SwitchConfig.CACHE_IS_OPEN) {
            String key = TairConstant.APPLY_TO_BE_FRIEND_PREFIX + request.getUserId() + "_" + request.getExternalUserId();
            ldbTairManager.put(key, request.getUserId(), EXPIRE_TIME);
        }
        return true;
    }

    @Override
    public TripSCRMResult<String> createGroup(CreateGroupRequest request) {
        GroupCreateRequest groupCreateRequest = new GroupCreateRequest();
        groupCreateRequest.setGroupName(request.getGroupName());
        groupCreateRequest.setUserId(request.getUserId());
        groupCreateRequest.setNotice(request.getNotice());
        groupCreateRequest.setInitUserIdList(request.getInitUserIdList());
        groupCreateRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<String> resultDO = groupService.asyncCreate(groupCreateRequest);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
            PlatformLogUtil.logFail("创建群聊失败", LogListUtil.newArrayList(resultDO));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
        return TripSCRMResult.success(resultDO.getModel());
    }

    @Override
    public boolean updateGroupRemark(String chatId, String userId, String remark) {
        List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(chatId), Lists.newArrayList(userId), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isEmpty(groupRelationList)) {
            return false;
        }
        GroupRelationDO groupRelationDO = groupRelationList.get(0);
        groupRelationDO.setRemark(remark);
        return groupRelationRepository.updateByShardingKeyAndUserid(groupRelationDO);
    }

    @Override
    public List<WechatGroupVO> filterManageTaskGroupList(List<WechatGroupVO> wechatGroupVOList, Long manageGroupTaskId) {
        if (CollectionUtils.isEmpty(wechatGroupVOList)) {
            return wechatGroupVOList;
        }
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
        query.setSourceType(ResourceTypeEnum.TASK.getCode().byteValue());
        query.setSourceId(String.valueOf(manageGroupTaskId));
        PageResultVO<WechatGroupRelationVO> pageResultVO = groupCodeRelationService.list(query);
        if (!pageResultVO.isSuccess() || CollectionUtils.isEmpty(pageResultVO.getData())) {
            return wechatGroupVOList;
        }
        List<String> mangeGroupIdList = pageResultVO.getData().stream().map(WechatGroupRelationVO::getChatId).collect(Collectors.toList());
        return wechatGroupVOList.stream().filter(wechatGroupVO -> !mangeGroupIdList.contains(wechatGroupVO.getChatId())).collect(Collectors.toList());
    }

    @Override
    public List<WechatGroupVO> listByMinIdLimit(Long minId, Integer size, boolean onlyQueryOwner) {
        List<WechatGroupDO> wechatGroupList = wechatGroupMapper.listByMinIdLimit(minId, size);
        if (CollectionUtils.isEmpty(wechatGroupList)) {
            return new ArrayList<>();
        }

        return wechatGroupList.stream().map(x -> convert(x, onlyQueryOwner)).collect(Collectors.toList());
    }

    @Override
    public List<String> getUserWorkChatIdList(String userId, String corpId) {
        WechatGroupWorkListRequest wechatGroupWorkListRequest = new WechatGroupWorkListRequest();
        wechatGroupWorkListRequest.setUserId(userId);
        wechatGroupWorkListRequest.setCorpId(Optional.ofNullable(corpId).orElse(SpaceInfoThreadLocalUtils.getCorpId()));
        try {
            ResultDO<List<String>> workResult = groupService.listWorkGroupId(wechatGroupWorkListRequest);
            if (workResult == null || !workResult.getSuccess()) {
                PlatformLogUtil.logFail("获取关注群列表失败", LogListUtil.newArrayList(wechatGroupWorkListRequest, workResult, userId));
                return Lists.newArrayList();
            }
            PlatformLogUtil.logFail("获取关注群列表成功", LogListUtil.newArrayList(wechatGroupWorkListRequest, workResult, userId));
            return Optional.ofNullable(workResult.getModel()).orElse(new ArrayList<>());
        } catch (Exception e) {
            PlatformLogUtil.logException("获取关注群列表异常", e.getMessage(), e, LogListUtil.newArrayList(wechatGroupWorkListRequest, userId));
            return Lists.newArrayList();
        }
    }

    @Override
    public List<String> groupWorkUserIdList(String chatId, String corpId) {
        GetOpenWorkGroupUserList request = new GetOpenWorkGroupUserList();
        request.setChatId(chatId);
        request.setCorpId(corpId);
        ResultDO<List<String>> resultDO = groupService.getOpenWorkGroupUserList(request);
        if (resultDO == null || !resultDO.getSuccess()) {
            PlatformLogUtil.logFail("查询群聊内关注的客户列表失败", LogListUtil.newArrayList(request, resultDO));
            return Lists.newArrayList();
        }
        return resultDO.getModel();
    }

    @Override
    public ResultDO<Void> operateGroupWork(String userId, String corpId, String chatId, Boolean status) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(chatId) || status == null) {
            return ResultDO.falseInstance(ErrorCodeEnum.PARAM_INVALID.getCode(), ErrorCodeEnum.PARAM_INVALID.getDesc());
        }
        // 关注
        if (status) {
            WechatGroupOpenWorkRequest wechatGroupOpenWorkRequest = new WechatGroupOpenWorkRequest();
            wechatGroupOpenWorkRequest.setUserId(userId);
            wechatGroupOpenWorkRequest.setCorpId(corpId);
            wechatGroupOpenWorkRequest.setChatId(chatId);
            return groupService.openWorkGroup(wechatGroupOpenWorkRequest);
        } else {
            WechatGroupCancelWorkRequest wechatGroupCancelWorkRequest = new WechatGroupCancelWorkRequest();
            wechatGroupCancelWorkRequest.setUserId(userId);
            wechatGroupCancelWorkRequest.setCorpId(corpId);
            wechatGroupCancelWorkRequest.setChatId(chatId);
            return groupService.cancelWorkGroup(wechatGroupCancelWorkRequest);
        }
    }

    @Override
    public boolean hasOwnerPermission(Long spaceId, String chatId) {
        List<WechatUserDTO> spaceWechatUserList = wechatUserService.listBySpaceId(spaceId);
        List<WechatGroupVO> wechatGroupList = listByChatIdList(Lists.newArrayList(chatId), false);

        if (CollectionUtils.isEmpty(spaceWechatUserList) || CollectionUtils.isEmpty(wechatGroupList)) {
            return false;
        }

        return hasOwnerPermission(spaceWechatUserList, wechatGroupList.get(0));
    }

    @Override
    public Integer addTag(WechatGroupAddTagRequest request) {
        List<WechatGroupVO> wechatGroupList = listByChatIdList(request.getChatIdList(), false);
        for (WechatGroupVO wechatGroupVO : wechatGroupList) {
            List<String> newTagIdList = Optional.ofNullable(request.getTagIdList()).orElse(new ArrayList<>()).stream().filter(tagId -> !wechatGroupVO.getTagIdList().contains(tagId)).collect(Collectors.toList());
            wechatGroupVO.getTagIdList().addAll(newTagIdList);
            wechatGroupMapper.updateByChatIdAndCorpId(convert(wechatGroupVO));
            return newTagIdList.size();
        }

        return 0;
    }

    @Override
    public void deleteTag(WechatGroupDeleteTagRequest request) {
        List<WechatGroupVO> wechatGroupList = listByChatIdList(request.getChatIdList(), false);
        for (WechatGroupVO wechatGroupVO : wechatGroupList) {
            List<String> tagIdList = wechatGroupVO.getTagIdList().stream().filter(x -> !request.getTagIdList().contains(x)).collect(Collectors.toList());
            wechatGroupVO.setTagIdList(tagIdList);
            wechatGroupMapper.updateByChatIdAndCorpId(convert(wechatGroupVO));
        }
    }

    @Override
    public WechatGroupVO getWechatGroupVOByChatId(String chatId) {
        WechatGroupDO wechatGroupDO = getByChatId(chatId);
        return Objects.isNull(wechatGroupDO) ? null : convert(wechatGroupDO, false);
    }

    @Override
    public WechatGroupDO getByChatId(String chatId) {
        return wechatGroupRepository.getByChatIdAndCorpId(chatId, SpaceInfoThreadLocalUtils.getCorpId());
    }

    @Override
    public List<WechatGroupDO> listByOwnerUser(String userId, String corpId) {
        List<WechatGroupDO> wechatGroupList = new ArrayList<>();

        if (!StringUtils.hasText(userId) || !StringUtils.hasText(corpId)) {
            return new ArrayList<>();
        }

        for (long minId = 0; ; ) {
            List<WechatGroupDO> list = wechatGroupMapper.listByOwnerUserAndCorpIdAndMinIdLimit(userId, corpId, minId, 300);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            minId = list.stream().map(WechatGroupDO::getId).max(Long::compareTo).orElse(0L);
            wechatGroupList.addAll(list);
        }

        return wechatGroupList;
    }

    @Override
    public PageInfoDTO<WechatGroupDO> listByOwnerUserPage(String userId, String corpId, Integer pageNo, Integer pageSize) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(corpId)) {
            return new PageInfoDTO<>();
        }
        Integer pageIndex =  pageNo < 1 ? 0 : (pageNo - 1) * pageSize;
        List<WechatGroupDO> dos =  wechatGroupMapper.listByOwnerUserAndCorpIdAndPageLimit(userId, corpId, pageIndex, pageSize);
        PageInfoDTO pageInfoDTO = new PageInfoDTO();
        pageInfoDTO.setList(dos);
        pageInfoDTO.setTotal(wechatGroupMapper.countByOwnerUserAndCorpId(userId, corpId));
        return pageInfoDTO;
    }

    @Override
    public List<WechatGroupVO> listByChatIdList(List<String> chatIdList, Boolean onlyQueryOwner) {
        if (CollectionUtils.isEmpty(chatIdList)) {
            return new ArrayList<>();
        }

        List<WechatGroupDO> wechatGroupList = wechatGroupMapper.listByChatIdAndCorpId(chatIdList, SpaceInfoThreadLocalUtils.getCorpId());
        if (CollectionUtils.isEmpty(wechatGroupList)) {
            return new ArrayList<>();
        }

        return wechatGroupList.stream().map(x -> convert(x, onlyQueryOwner)).collect(Collectors.toList());
    }

    @Override
    public List<WechatGroupVO> listByUserId(String userId) {
        List<GroupRelationDO> groupRelationList = groupRelationMapper.listByShardingKeyAndCorpIdAndUserIdAndStatus(groupRelationRepository.userId2ShardingKey(userId), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isEmpty(groupRelationList)) {
            return new ArrayList<>();
        }

        List<String> chatIdList = groupRelationList.stream().map(GroupRelationDO::getChatId).collect(Collectors.toList());
        return listByChatIdList(chatIdList, false);
    }

    @Override
    public List<WechatGroupVO> listByUserIdList(List<String> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        List<GroupRelationDO> groupRelationList = groupRelationMapper.listByShardingKeyListAndCorpIdAndUserTypeAndStatus(userIdList.stream().map(groupRelationRepository::userId2ShardingKey).collect(Collectors.toList()), SpaceInfoThreadLocalUtils.getCorpId(), GroupUserTypeEnum.USER.getCode().byteValue(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isEmpty(groupRelationList)) {
            return new ArrayList<>();
        }

        List<String> chatIdList = groupRelationList.stream().map(GroupRelationDO::getChatId).collect(Collectors.toList());
        return listByChatIdList(chatIdList, false);
    }

    @Override
    public List<String> listUserIdByChatIdAndUserTypeAndCorpId(String chatId, GroupUserTypeEnum groupUserTypeEnum, String corpId) {
        List<GroupRelationDO> groupRelationList = groupRelationMapper.listByShardingKeyAndCorpIdAndUserTypeAndStatus(groupRelationRepository.chatId2ShardingKey(chatId), corpId, groupUserTypeEnum.getCode().byteValue(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        return groupRelationList.stream().map(GroupRelationDO::getUserId).collect(Collectors.toList());
    }

    @Override
    public Integer getGroupCustomerCount(List<String> chatIdList, String state) {
        int count = 0;
        if (CollectionUtils.isEmpty(chatIdList)) {
            return count;
        }

        WechatGroupCustomerCountQuery query = new WechatGroupCustomerCountQuery();
        query.setState(state);
        List<List<String>> partition = Lists.partition(chatIdList, 10);
        for (List<String> list : partition) {
            query.setChatIdList(list);
            count += groupCustomerRelationOpenSearchManager.getGroupCustomerCount(query).intValue();
        }

        return count;
    }

    @Override
    public Integer getGroupCount(List<String> tagIdList) {
        int count = 0;
        if (CollectionUtils.isEmpty(tagIdList)) {
            return count;
        }

        return groupUserOpenSearchManager.getGroupCount(tagIdList).intValue();
    }


    @Override
    public Integer getGroupCustomerWithFollowUserCount(List<String> chatIdList, String state) {
        int count = 0;
        if (CollectionUtils.isEmpty(chatIdList)) {
            return count;
        }

        WechatGroupCustomerCountQuery query = new WechatGroupCustomerCountQuery();
        query.setState(state);
        query.setHasFollowUser(true);
        List<List<String>> partition = Lists.partition(chatIdList, 10);
        for (List<String> list : partition) {
            query.setChatIdList(list);
            count += groupCustomerRelationOpenSearchManager.getGroupCustomerCount(query).intValue();
        }

        return count;
    }

    @Override
    public Integer getGroupLeaveCustomerCount(List<String> chatIdList, String state) {
        int count = 0;
        if (CollectionUtils.isEmpty(chatIdList)) {
            return count;
        }

        WechatGroupCustomerCountQuery query = new WechatGroupCustomerCountQuery();
        query.setState(state);
        List<List<String>> partition = Lists.partition(chatIdList, 10);
        for (List<String> list : partition) {
            query.setChatIdList(list);
            query.setNotInGroup(true);
            count += groupCustomerRelationOpenSearchManager.getGroupCustomerCount(query).intValue();
        }

        return count;
    }

    @Override
    public Integer getGroupLeaveCustomerWithFollowUserCount(List<String> chatIdList, String state) {
        int count = 0;
        if (CollectionUtils.isEmpty(chatIdList)) {
            return count;
        }

        WechatGroupCustomerCountQuery query = new WechatGroupCustomerCountQuery();
        query.setState(state);
        query.setHasFollowUser(true);
        List<List<String>> partition = Lists.partition(chatIdList, 10);
        for (List<String> list : partition) {
            query.setChatIdList(list);
            query.setNotInGroup(true);
            count += groupCustomerRelationOpenSearchManager.getGroupCustomerCount(query).intValue();
        }

        return count;
    }

    private WechatGroupVO convert(WechatGroupDO wechatGroupDO, boolean onlyQueryOwner) {
        WechatGroupVO wechatGroupVO = new WechatGroupVO();
        BeanUtils.copyProperties(wechatGroupDO, wechatGroupVO);
        List<String> adminUserIds = Arrays.stream(Optional.ofNullable(wechatGroupDO.getAdminUser()).orElse("").split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());
        wechatGroupVO.setAdminUserIdList(adminUserIds);
        String ownerUserId = wechatGroupDO.getOwnerUser();
        wechatGroupVO.setOwnerUserId(ownerUserId);
        wechatGroupVO.setId(wechatGroupDO.getId());
        wechatGroupVO.setCreateTime(wechatGroupDO.getCreateTime());
        wechatGroupVO.setQrCode(wechatGroupDO.getQrcode());
        wechatGroupVO.setAvatarUrl(DEFAULT_GROUP_AVATAR);

        if (StringUtils.hasText(ownerUserId)) {
            wechatGroupVO.setOwnerUser(CollectionUtils.isEmpty(wechatUserService.listById(Lists.newArrayList(ownerUserId))) ? null : wechatUserConverter.convert2VO(wechatUserService.listById(Lists.newArrayList(ownerUserId)).get(0)));
        }

        if (onlyQueryOwner) {
            return wechatGroupVO;
        }

        if (!CollectionUtils.isEmpty(adminUserIds)) {
            wechatGroupVO.setAdminUserList(wechatUserService.listById(adminUserIds).stream().map(wechatUserConverter::convert2VO).collect(Collectors.toList()));
        }

        ItemTagRelationQuery query = new ItemTagRelationQuery();
        query.setItemId(wechatGroupDO.getChatId() + "_" + wechatGroupDO.getCorpId());
        query.setItemType(BizTypeEnum.WECHAT_GROUP.getCode());
        query.setDeleted(IsDeleteEnum.NO.getCode());
        List<ItemTagRelationDTO> tagRelationList = tagRelationService.selectByCondition(query);
        wechatGroupVO.setTagIdList(tagRelationList.stream().map(ItemTagRelationDTO::getTag).collect(Collectors.toList()));
        List<TagInfoDTO> tagInfoDTOS = tagInfoService.selectByTagIdList(wechatGroupVO.getTagIdList());
        List<TagInfoVO> tagInfoVOS = tagInfoDTOS.stream().map(tagInfoDTO -> tagConverter.convert2VO(tagInfoDTO)).collect(Collectors.toList());
        wechatGroupVO.setTagList(tagInfoVOS);
        List<GroupMemberCountResult> groupMemberCountResults = groupRelationRepository.countByShardingKeyList(
                Lists.newArrayList(groupRelationRepository.chatId2ShardingKey(wechatGroupVO.getChatId())),
                Lists.newArrayList(MemberTypeEnum.EnterpriseMember.getType(), MemberTypeEnum.ExternalContact.getType()),
                SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (!CollectionUtils.isEmpty(groupMemberCountResults)) {
            wechatGroupVO.setUserCount(groupMemberCountResults.get(0).getCount());
        }

        return wechatGroupVO;
    }

    private WechatGroupDO convert(WechatGroupVO wechatGroupVO) {
        WechatGroupDO wechatGroupDO = new WechatGroupDO();
        BeanUtils.copyProperties(wechatGroupVO, wechatGroupDO);
        if (Objects.nonNull(wechatGroupVO.getTagIdList())) {
            wechatGroupDO.setTags(wechatGroupVO.getTagIdList().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        wechatGroupDO.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        return wechatGroupDO;
    }
}
