package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.RecallMessageRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.RecallMessageResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;

import static com.alibaba.tripscrm.service.constant.TairConstant.MESSAGE_ID_MAPPING_PREFIX;

/**
 * 消息撤回 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class AsyncRecallMessageProcessor implements WsEventProcessor {
    /**
     * 异步操作传输数据的过期时间 30分钟
     */
    public static final Integer ASYNC_OPERATE_EXPIRE_SECOND = 30 * 60;
    public static final String ASYNC_CACHE_KEY = "recallMessage|";
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private GroupService groupService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private WechatUserService wechatUserService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.RECALL_MESSAGE;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        RecallMessageRequest request = wsEvent.getData().toJavaObject(RecallMessageRequest.class);
        if (wsEvent.getUserId() == null || request.getChatId() == null || request.getChatType() == null) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        // 获取msgId
        String msgId = (String) ldbTairManager.get(MESSAGE_ID_MAPPING_PREFIX + request.getMessageId());
        if (msgId == null) {
            throw new TripscrmException(TripSCRMErrorCode.RECALL_MESSAGE_TIMEOUT);
        }
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        if (!wechatUserService.checkHasLock(wsEvent.getUserId(), wsContextInfo.getAccount().getUserId(), wsContextInfo.getSpaceId())) {
            throw new TripscrmException("未锁定当前企微账号，无法执行该操作");
        }
        // 调用proxy撤回
        com.alibaba.tripzoo.proxy.request.RecallMessageRequest recallMessageRequest = new com.alibaba.tripzoo.proxy.request.RecallMessageRequest();
        recallMessageRequest.setUserId(wsEvent.getUserId());
        recallMessageRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        recallMessageRequest.setMsgId(msgId);
        ResultDO<String> result = groupService.asyncRecallMessage(recallMessageRequest);
        if (result != null && result.getSuccess()) {
            // 保存消息上下文，以便回调时取到消息数据，key为requestId
            wsEvent.setTimestamp(System.currentTimeMillis());
            ldbTairManager.put(ASYNC_CACHE_KEY + result.getModel(), JSONObject.toJSONString(wsEvent), ASYNC_OPERATE_EXPIRE_SECOND);
            PlatformLogUtil.logInfo("消息撤回成功", LogListUtil.newArrayList(request, result, session.getId(), wsEvent.getUserId(), wsEvent));
            return;
        }
        PlatformLogUtil.logFail("消息撤回失败", LogListUtil.newArrayList(request, result, session.getId(), wsEvent.getUserId(), wsEvent));
        throw new TripscrmException(TripSCRMErrorCode.RECALL_MESSAGE_FAIL);
    }

    /**
     * 成功发送消息后置动作
     */
    public WsEvent afterCreate(String requestId) {
        // 获取消息上下文
        String eventJson = (String) ldbTairManager.get(ASYNC_CACHE_KEY + requestId);
        if (eventJson != null) {
            return JSONObject.parseObject(eventJson, WsEvent.class);
        }
        return null;
    }

    public void pushMessageByDistributed(WsEvent wsEvent, String chatId, Integer chatType, String messageId) {
        RecallMessageResponse recallMessageResponse = new RecallMessageResponse();
        recallMessageResponse.setChatId(chatId);
        recallMessageResponse.setChatType(chatType);
        recallMessageResponse.setMessageId(messageId);
        wsEvent.setData((JSONObject) JSONObject.toJSON(recallMessageResponse));
        webSocketFactory.pushMessageByDistributed(wsEvent);
    }
}
