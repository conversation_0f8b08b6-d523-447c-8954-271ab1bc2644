package com.alibaba.tripscrm.service.middleware.metaq.consumer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.trip.channel.ai.client.model.common.ResultDO;
import com.alibaba.trip.channel.ai.client.enums.TaskInstanceStatus;
import com.alibaba.trip.channel.ai.client.model.metaq.task.TaskInstanceResultEvent;
import com.alibaba.trip.channel.ai.client.model.request.task.TaskInstanceQueryRequest;
import com.alibaba.trip.channel.ai.client.model.response.task.TaskInstanceQueryResponse;
import com.alibaba.trip.channel.ai.client.service.task.AiTaskService;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.material.AiMaterialAsynTaskStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.domain.query.AiMaterialAsynTaskQuery;
import com.alibaba.tripscrm.service.model.dto.material.AiMaterialAsynTaskDTO;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.service.material.AiMaterialAsynTaskService;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * AI异步素材任务完成消息
 */
@Slf4j
@Service(value = "aiTaskFinishMetaqConsumer")
public class AiTaskFinishMetaqConsumer implements MessageListenerConcurrently {

    @Resource
    private AiTaskService aiTaskService;
    @Resource
    private AiMaterialAsynTaskService aiMaterialAsynTaskService;
    @Resource
    private TaskExecuteStrategy taskExecuteStrategy;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("AI异步素材任务完成消息处理失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            TaskInstanceResultEvent event = JSONObject.parseObject(message, TaskInstanceResultEvent.class);
            PlatformLogUtil.logInfo("AI异步素材任务完成消息", event);
            String instanceId = event.getInstanceId();
            Integer status = event.getStatus();
            String requestId = event.getRequestId();
            if (StringUtils.isEmpty(instanceId) || status == null || StringUtils.isEmpty(requestId)) {
                PlatformLogUtil.logFail("AI异步素材任务完成消息入参非法", LogListUtil.newArrayList(message));
                return false;
            }
            // 查询本地任务数据
            AiMaterialAsynTaskDTO taskDTO = getAiMaterialAsynTaskDTOFromDB(message, requestId);
            if (taskDTO == null){
                return false;
            }
            // 未成功结果判断
            if (resultNotSuccesscheck(status, taskDTO)){
                return false;
            }
            // 成功查结果处理
            if (!resultSuccessSettle(message, instanceId, taskDTO)){
                return false;
            }
            if (AiMaterialAsynTaskStatusEnum.EXECUTE_SUCCESS.getCode() != taskDTO.getStatus()){
                PlatformLogUtil.logFail("AI异步素材任务完成失败，不执行消息发送", LogListUtil.newArrayList(message, taskDTO));
                return false;
            }
            // 基于事件触发任务执行
            triggerEventTaskRun(message, taskDTO);
        } catch (Exception e) {
            PlatformLogUtil.logException("AI异步素材任务完成消息处理异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
        return true;
    }

    private void triggerEventTaskRun(String message, AiMaterialAsynTaskDTO taskDTO) {
        AiMaterialAsynTaskDTO.AiMaterialAsynTaskExtraInfo taskExtraInfo = JSONObject.parseObject(taskDTO.getExtInfo(), AiMaterialAsynTaskDTO.AiMaterialAsynTaskExtraInfo.class);
        TaskExecuteParam param = TaskExecuteParam.buildParam(new Date(), taskDTO.getTaskId(), TaskTriggerTypeEnum.INTERFACE, taskDTO.getTargetType(), taskDTO.getTargetId(), true, taskExtraInfo.getExtraInfo(), new ArrayList<>());
        TripSCRMResult<String> result = taskExecuteStrategy.runMain(param);
        PlatformLogUtil.logInfo("事件源监听，AI素材消息触发任务执行，单个任务执行结束", LogListUtil.newArrayList(message, result));
    }

    private boolean resultSuccessSettle(String message, String instanceId, AiMaterialAsynTaskDTO taskDTO) {
        TaskInstanceQueryRequest request = new TaskInstanceQueryRequest();
        request.setInstanceId(instanceId);
        ResultDO<TaskInstanceQueryResponse> result = aiTaskService.queryTaskInstance(request);
        if (result == null || !result.isSuccess() || result.getModel() == null) {
            PlatformLogUtil.logFail("查询AI任务实例失败", LogListUtil.newArrayList(message, request));
            return false;
        }
        TaskInstanceQueryResponse response = result.getModel();
        Map<String, Object> aiResult = response.getResult();
        taskDTO.setResult(JSONObject.toJSONString(aiResult));
        byte finalStaus = TaskInstanceStatus.SUCCESS.getCode().equals(response.getStatus())
                ? AiMaterialAsynTaskStatusEnum.EXECUTE_SUCCESS.getCode()
                : AiMaterialAsynTaskStatusEnum.EXECUTE_FAIL.getCode();
        taskDTO.setStatus(finalStaus);
        com.alibaba.tripscrm.service.model.common.ResultDO<AiMaterialAsynTaskDTO> taskDTOResultDO = aiMaterialAsynTaskService.upsertAiMaterialAsynTask(taskDTO);
        if (taskDTOResultDO == null || !taskDTOResultDO.isSuccess()) {
            PlatformLogUtil.logFail("更新AI素材异步任务状态失败", LogListUtil.newArrayList(taskDTO));
            return false;
        }
        return true;
    }

    private boolean resultNotSuccesscheck(Integer status, AiMaterialAsynTaskDTO taskDTO) {
        if (TaskInstanceStatus.FAIL.getCode().equals(status) || TaskInstanceStatus.CANCEL.getCode().equals(status)) {
            byte finalStaus = TaskInstanceStatus.FAIL.getCode().equals(status)
                    ? AiMaterialAsynTaskStatusEnum.EXECUTE_FAIL.getCode()
                    : AiMaterialAsynTaskStatusEnum.EXECUTE_CANCEL.getCode();
            taskDTO.setStatus(finalStaus);
            com.alibaba.tripscrm.service.model.common.ResultDO<AiMaterialAsynTaskDTO> taskDTOResultDO = aiMaterialAsynTaskService.upsertAiMaterialAsynTask(taskDTO);
            if (taskDTOResultDO == null || !taskDTOResultDO.isSuccess()) {
                PlatformLogUtil.logFail("更新AI素材异步任务状态失败", LogListUtil.newArrayList(taskDTO));
            }
            return true;
        }
        return false;
    }

    private @Nullable AiMaterialAsynTaskDTO getAiMaterialAsynTaskDTOFromDB(String message, String requestId) {
        AiMaterialAsynTaskQuery query = new AiMaterialAsynTaskQuery();
        query.setRequestId(requestId);
        com.alibaba.tripscrm.service.model.common.ResultDO<List<AiMaterialAsynTaskDTO>> resultDO = aiMaterialAsynTaskService.getAiMaterialAsynTask(query);
        if (resultDO == null || !resultDO.isSuccess() || resultDO.getModel() == null || resultDO.getModel().isEmpty()) {
            PlatformLogUtil.logFail("AI异步素材任务完成消息入参非法，没有匹配上任务", LogListUtil.newArrayList(message, resultDO));
            return null;
        }
        AiMaterialAsynTaskDTO taskDTO = resultDO.getModel().get(0);
        return taskDTO;
    }

}
