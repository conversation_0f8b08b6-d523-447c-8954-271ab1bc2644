package com.alibaba.tripscrm.service.service.risk.handler;

import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionLimitTargetEnum;
import com.alibaba.tripscrm.service.manager.middleware.RedisManager;
import com.alibaba.tripscrm.service.model.domain.risk.*;
import com.alibaba.tripscrm.service.service.risk.RiskActionLimitTargetHandler;
import com.alibaba.tripscrm.service.service.risk.RiskActionContentParser;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import com.taobao.hsf.invocation.Invocation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 行动项内容调用次数 限流处理器
 *
 * <AUTHOR>
 * @date 2024/07/04
 */
@Component
public class RiskActionLimitTargetContentHandler extends RiskActionLimitTargetHandler {
    @Autowired(required = false)
    private RedisManager redisManager;

    @Override
    protected RiskActionLimitTargetEnum riskTarget() {
        return RiskActionLimitTargetEnum.CONTENT;
    }

    @Override
    public RiskActionConfig fillConfig(RiskActionEnum riskAction, RiskActionConfig configValue) {
        RiskActionContentParser riskActionContentParser = RiskActionContentParser.get(riskAction);
        if (riskActionContentParser == null) {
            throw new RuntimeException("内容风控需要实现解析");
        }
        RiskActionConfig configDefine = riskActionContentParser.buildConfig();
        if (configValue != null) {
            configDefine.setMinuteRateLimit(configValue.getMinuteRateLimit());
            configDefine.setHourRateLimit(configValue.getHourRateLimit());
            configDefine.setDayRateLimit(configValue.getDayRateLimit());
            configDefine.setEnable(configValue.getEnable());
            Map<String, RiskActionConfig> subConfigMap = new HashMap<>();
            if (configValue.getSubConfigs() != null) {
                subConfigMap = configValue.getSubConfigs().stream().collect(Collectors.toMap(RiskActionConfig::getActionCode, Function.identity()));
            }
            if (configDefine.getSubConfigs() != null) {
                for (RiskActionConfig subConfig : configDefine.getSubConfigs()) {
                    RiskActionConfig subConfigValue = subConfigMap.get(subConfig.getActionCode());
                    if (subConfigValue != null) {
                        subConfig.setMinuteRateLimit(subConfigValue.getMinuteRateLimit());
                        subConfig.setHourRateLimit(subConfigValue.getHourRateLimit());
                        subConfig.setDayRateLimit(subConfigValue.getDayRateLimit());
                        subConfig.setEnable(subConfigValue.getEnable());
                    }
                }
            }
        }
        return configDefine;
    }

    @Override
    public RiskActionHitConfig hitRiskConfig(String corpId, String userId, RiskActionConfig configValue, Invocation invocation) {
        RiskActionContentParser riskActionContentParser = RiskActionContentParser.get(RiskActionEnum.parse(configValue.getActionCode()));
        if (riskActionContentParser == null) {
            throw new RuntimeException("内容风控需要实现解析");
        }
        return riskActionContentParser.hitRiskConfig(corpId, userId, configValue, invocation);
    }

    @Override
    public RateLimitResult limitControl(String corpId, String userId, RiskActionHitConfig hitConfig) {
        RateLimitResult limitResult;
        if (hitConfig.getEnable()) {
            // 构建限流业务标记和参数
            List<String> keys = new ArrayList<>();
            List<String> args = new ArrayList<>();
            List<RiskActionHitConfig> hitSubConfigs = hitConfig.getHitSubConfigs() != null ?
                    hitConfig.getHitSubConfigs().stream().filter(RiskActionHitConfig::getEnable).collect(Collectors.toList()) : Collections.emptyList();
            int subSize = hitSubConfigs.size();
            args.add(Integer.toString(subSize));
            LimitKeys limitKeys = buildLimitKeys(corpId, userId, hitConfig.getActionCode());
            keys.add(limitKeys.getDayKey());
            keys.add(limitKeys.getHourKey());
            keys.add(limitKeys.getMinuteKey());
            args.add(getRateLimitValue(hitConfig.getDayRateLimit()));
            args.add(getRateLimitValue(hitConfig.getHourRateLimit()));
            args.add(getRateLimitValue(hitConfig.getMinuteRateLimit()));
            args.add(hitConfig.getHit().toString());
            for (RiskActionHitConfig hitSubConfig : hitSubConfigs) {
                LimitKeys subLimitKeys = buildLimitKeys(corpId, userId, hitSubConfig.getActionCode());
                keys.add(subLimitKeys.getDayKey());
                keys.add(subLimitKeys.getHourKey());
                keys.add(subLimitKeys.getMinuteKey());
                args.add(getRateLimitValue(hitSubConfig.getDayRateLimit()));
                args.add(getRateLimitValue(hitSubConfig.getHourRateLimit()));
                args.add(getRateLimitValue(hitSubConfig.getMinuteRateLimit()));
                args.add(hitSubConfig.getHit().toString());
            }
            AtomicReference<String> result = new AtomicReference<>();
            // 执行redis
            redisManager.execute(jedis -> {
                // Lua 脚本，限流控制-丢弃式限流-固定窗口
                String fileName = "lua/riskContentLimit.lua";
                String script = REDIS_SCRIPT.get(fileName);
                if (StringUtils.isBlank(script)) {
                    script = readFileAsString(fileName);
                    REDIS_SCRIPT.put(fileName, script);
                }
                // 执行 Lua 脚本
                result.set((String) jedis.eval(script, keys, args));
                PlatformLogUtil.logFail("风控控制-风控内容限流判断", LogListUtil.newArrayList(keys,  args, result, corpId, userId, hitConfig.getActionCode(), hitConfig));
            });
            limitResult = buildResult(result.get());
        } else {
            limitResult = new RateLimitResult(false);
        }
        if (limitResult.getLimit()) {
            PlatformLogUtil.logFail("风控控制-行动项Content触发限流", LogListUtil.newArrayList(corpId, userId, hitConfig.getActionCode(), limitResult.getLimitType()));
        } else {
            PlatformLogUtil.logInfo("风控控制-行动项Content未触发限流", LogListUtil.newArrayList(corpId, userId, hitConfig.getActionCode()));
        }
        return limitResult;
    }

    @Override
    public RiskActionLimitProcess getLimitProcess(String corpId, String userId, RiskActionConfig config) {
        // 构建限流业务标记和参数
        LimitKeys limitKeys = buildLimitKeys(corpId, userId, config.getActionCode());
        RiskActionLimitProcess limitProcess = new RiskActionLimitProcess();
        limitProcess.setActionName(config.getActionName());
        limitProcess.setActionCode(config.getActionCode());
        List<String> values = redisManager.mget(Lists.newArrayList(limitKeys.getMinuteKey(), limitKeys.getHourKey(), limitKeys.getDayKey()));
        if (values.get(0) != null) {
            limitProcess.setMinuteRemainCount(Integer.parseInt(values.get(0)));
        } else {
            limitProcess.setMinuteRemainCount(config.getMinuteRateLimit());
        }
        if (values.get(1) != null) {
            limitProcess.setHourRemainCount(Integer.parseInt(values.get(1)));
        } else {
            limitProcess.setHourRemainCount(config.getHourRateLimit());
        }
        if (values.get(2) != null) {
            limitProcess.setDayRemainCount(Integer.parseInt(values.get(2)));
        } else {
            limitProcess.setDayRemainCount(config.getDayRateLimit());
        }
        if (config.getSubConfigs() != null) {
            List<RiskActionLimitProcess> subLimitProcesses = new ArrayList<>(config.getSubConfigs().size());
            for (RiskActionConfig subConfig : config.getSubConfigs()) {
                RiskActionLimitProcess subLimitProcess = new RiskActionLimitProcess();
                LimitKeys subLimitKeys = buildLimitKeys(corpId, userId, subConfig.getActionCode());
                subLimitProcess.setActionName(subConfig.getActionName());
                subLimitProcess.setActionCode(subConfig.getActionCode());
                List<String> subValues = redisManager.mget(Lists.newArrayList(subLimitKeys.getMinuteKey(), subLimitKeys.getHourKey(), subLimitKeys.getDayKey()));
                if (subValues.get(0) != null) {
                    subLimitProcess.setMinuteRemainCount(Integer.parseInt(subValues.get(0)));
                } else {
                    subLimitProcess.setMinuteRemainCount(subConfig.getMinuteRateLimit());
                }
                if (subValues.get(1) != null) {
                    subLimitProcess.setHourRemainCount(Integer.parseInt(subValues.get(1)));
                } else {
                    subLimitProcess.setHourRemainCount(subConfig.getHourRateLimit());
                }
                if (subValues.get(2) != null) {
                    subLimitProcess.setDayRemainCount(Integer.parseInt(subValues.get(2)));
                } else {
                    subLimitProcess.setDayRemainCount(subConfig.getDayRateLimit());
                }
                subLimitProcesses.add(subLimitProcess);
            }
            limitProcess.setSubProcess(subLimitProcesses);
        }
        return limitProcess;
    }

    @Override
    public void resetLimit(String corpId, String userId, RiskActionConfig config) {
        // 构建限流业务标记和参数
        List<String> keys = new ArrayList<>();
        List<String> args = new ArrayList<>();
        int subSize = config.getSubConfigs() != null ? config.getSubConfigs().size() : 0;
        args.add(Integer.toString(subSize));
        LimitKeys limitKeys = buildLimitKeys(corpId, userId, config.getActionCode());
        keys.add(limitKeys.getDayKey());
        keys.add(limitKeys.getHourKey());
        keys.add(limitKeys.getMinuteKey());
        if (config.getSubConfigs() != null) {
            for (RiskActionConfig subConfig : config.getSubConfigs()) {
                LimitKeys subLimitKeys = buildLimitKeys(corpId, userId, subConfig.getActionCode());
                keys.add(subLimitKeys.getDayKey());
                keys.add(subLimitKeys.getHourKey());
                keys.add(subLimitKeys.getMinuteKey());
            }
        }
        // 执行redis
        redisManager.execute(jedis -> {
            // Lua 脚本，限流控制-丢弃式限流-固定窗口
            String fileName = "lua/deleteContentLimit.lua";
            String script = REDIS_SCRIPT.get(fileName);
            if (StringUtils.isBlank(script)) {
                script = readFileAsString(fileName);
                REDIS_SCRIPT.put(fileName, script);
            }
            // 执行 Lua 脚本
            jedis.eval(script, keys, args);
        });
    }
}
