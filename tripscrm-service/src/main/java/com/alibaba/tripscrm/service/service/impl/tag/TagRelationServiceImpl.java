package com.alibaba.tripscrm.service.service.impl.tag;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatCustomerMapper;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatGroupMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatCustomerDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationCoverRequest;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.domain.request.WechatCustomerAddTagRequest;
import com.alibaba.tripscrm.service.model.domain.request.WechatCustomerDeleteTagRequest;
import com.alibaba.tripscrm.service.model.domain.request.WechatGroupAddTagRequest;
import com.alibaba.tripscrm.service.model.domain.request.WechatGroupDeleteTagRequest;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 此表根据 item_id 进行分表，所有db操作必须包含 item_id 字段！！！
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TagRelationServiceImpl implements TagRelationService {
    private final ItemTagRelationServiceImpl itemTagRelationService;
    private final ItemTagRelationOldServiceImpl itemTagRelationOldService;
    private final TagRelationService tagRelationService;
    private final WechatCustomerService wechatCustomerService;
    private final WechatGroupService wechatGroupService;
    private final TagInfoService tagInfoService;
    private final WechatCustomerMapper wechatCustomerMapper;
    private final WechatGroupMapper wechatGroupMapper;
    private final MetaqProducer metaqProducer;
    private final LdbTairManager ldbTairManager;

    @Override
    public List<ItemTagRelationDTO> selectByCondition(ItemTagRelationQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getItemType()) || !StringUtils.hasText(query.getItemId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        BizTypeEnum bizTypeEnum = BizTypeEnum.findByCode(query.getItemType());
        if (Objects.isNull(bizTypeEnum)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP, BizTypeEnum.GROUP_MA_OLD, BizTypeEnum.PERSON_MA_OLD, BizTypeEnum.CUSTOMER_ACQUISITION_LINK).contains(bizTypeEnum)) {
            return itemTagRelationService.selectByCondition(query);
        }

        if (!SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
            return itemTagRelationService.selectByCondition(query);
        }

        switch (bizTypeEnum) {
            case WECHAT_CUSTOMER:
                return getWechatCustomerTagRelationOldList(query);
            case WECHAT_GROUP:
                return getWechatGroupTagRelationOldList(query);
            default:
                break;
        }
        return itemTagRelationOldService.selectByCondition(query);
    }

    @Override
    public List<TagInfoDTO> selectTagInfoByCondition(ItemTagRelationQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getItemType()) || !StringUtils.hasText(query.getItemId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        BizTypeEnum bizTypeEnum = BizTypeEnum.findByCode(query.getItemType());
        if (Objects.isNull(bizTypeEnum)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP, BizTypeEnum.GROUP_MA_OLD, BizTypeEnum.PERSON_MA_OLD, BizTypeEnum.CUSTOMER_ACQUISITION_LINK).contains(bizTypeEnum)) {
            return itemTagRelationService.selectTagInfoByCondition(query);
        }

        if (!SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
            return itemTagRelationService.selectTagInfoByCondition(query);
        }

        switch (bizTypeEnum) {
            case WECHAT_CUSTOMER:
                return getWechatCustomerTagOldList(query);
            case WECHAT_GROUP:
                return getWechatGroupTagOldList(query);
            default:
                break;
        }
        return itemTagRelationOldService.selectTagInfoByCondition(query);
    }

    @Override
    public PageInfo<ItemTagRelationDTO> pageQuery(ItemTagRelationQuery query) {
        BizTypeEnum bizTypeEnum = BizTypeEnum.findByCode(query.getItemType());
        if (Objects.isNull(bizTypeEnum) || Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP).contains(bizTypeEnum)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP, BizTypeEnum.GROUP_MA_OLD, BizTypeEnum.PERSON_MA_OLD, BizTypeEnum.CUSTOMER_ACQUISITION_LINK).contains(bizTypeEnum)) {
            return itemTagRelationService.pageQuery(query);
        }

        if (!SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
            return itemTagRelationService.pageQuery(query);
        }

        return itemTagRelationOldService.pageQuery(query);
    }

    @Override
    public Integer batchUpsertSelective(List<ItemTagRelationDTO> list) {
        PlatformLogUtil.logInfo("批量写入物料与标签关系数据", LogListUtil.newArrayList(list));
        int result = 0;
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        for (ItemTagRelationDTO record : list) {
            PlatformLogUtil.logInfo("写入单个物料与标签关系数据", LogListUtil.newArrayList(record));
            if (tagRelationService.upsertSelective(record) < 1) {
                PlatformLogUtil.logFail("写入物料与标签关系数据失败", LogListUtil.newArrayList(record));
            } else {
                result++;
            }
        }

        return result;
    }

    @AteyeInvoker(description = "新增标签关系数据", paraDesc = "itemId&itemType&tagId")
    public Integer upsertSelective(String itemId, Integer itemType, String tagId) {
        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setItemId(itemId);
        itemTagRelationDTO.setItemType(Objects.requireNonNull(BizTypeEnum.findByCode(itemType)).getCode());
        itemTagRelationDTO.setTag(tagId);
        itemTagRelationDTO.setDeleted((byte) 0);
        return tagRelationService.upsertSelective(itemTagRelationDTO);
    }

    @Override
    @TairLock(value = "'updateItemTagRelation_' + #record.itemId + '_' + #record.itemType", waitMilliseconds = 2000L)
    public Integer upsertSelective(ItemTagRelationDTO record) {
        if (Objects.isNull(record) || Objects.isNull(record.getItemType()) || !StringUtils.hasText(record.getItemId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        BizTypeEnum bizTypeEnum = BizTypeEnum.findByCode(record.getItemType());
        if (Objects.isNull(bizTypeEnum)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        Integer result = 1;
        if (itemTagRelationService.upsertSelective(record) < 1) {
            PlatformLogUtil.logFail("新增物料与标签关系新数据，已存在", LogListUtil.newArrayList(record));
            result = 0;
//            throw new TripscrmException(TripSCRMErrorCode.DB_FAILED);
        }

        if (Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER_RELATION).contains(bizTypeEnum)) {
            String externalUserId = record.getItemId().substring(0, record.getItemId().lastIndexOf('_'));
            String corpId = record.getItemId().substring(record.getItemId().lastIndexOf('_') + 1);
            if (ldbTairManager.incr(TairConstant.WECHAT_CUSTOMER_TAG_DIFF_COUNT_PREFIX + record.getItemId() + "_" + record.getTag(), 1, 0, 60) == 1) {
                metaqProducer.send(MQEnum.SCRM_WECHAT_CUSTOMER_SCRM_TAG_DIFF, record.getItemId(), "add_tag", String.format("%s\t%s\t%s", corpId, externalUserId, record.getTag()));
            }
            return result;
        }

        if (!Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP, BizTypeEnum.GROUP_MA_OLD, BizTypeEnum.PERSON_MA_OLD, BizTypeEnum.CUSTOMER_ACQUISITION_LINK).contains(bizTypeEnum)) {
            return result;
        }

        if (!SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_WRITE_SWITCH) {
            if (result > 0 && Objects.equals(bizTypeEnum, BizTypeEnum.WECHAT_CUSTOMER)) {
                String externalUserId = record.getItemId().substring(0, record.getItemId().lastIndexOf('_'));
                String corpId = record.getItemId().substring(record.getItemId().lastIndexOf('_') + 1);
                metaqProducer.send(MQEnum.SCRM_WECHAT_CUSTOMER_SCRM_TAG_DIFF, externalUserId, "add_tag", String.format("%s\t%s\t%s", corpId, externalUserId, record.getTag()));
            }
            return result;
        }


        switch (bizTypeEnum) {
            case WECHAT_CUSTOMER:
                if (Objects.equals(record.getDeleted(), (byte) 1)) {
                    return 0;
                }
                WechatCustomerAddTagRequest wechatCustomerAddTagRequest = new WechatCustomerAddTagRequest();
                wechatCustomerAddTagRequest.setExternalUserIdList(Lists.newArrayList(record.getItemId().substring(0, record.getItemId().lastIndexOf('_'))));
                wechatCustomerAddTagRequest.setTagIdList(Lists.newArrayList(record.getTag()));
                result = wechatCustomerService.addTag(wechatCustomerAddTagRequest);
                if (result > 0) {
                    String externalUserId = record.getItemId().substring(0, record.getItemId().lastIndexOf('_'));
                    String corpId = record.getItemId().substring(record.getItemId().lastIndexOf('_') + 1);
                    metaqProducer.send(MQEnum.SCRM_WECHAT_CUSTOMER_SCRM_TAG_DIFF, externalUserId, "add_tag", String.format("%s\t%s\t%s", corpId, externalUserId, record.getTag()));
                }
            case WECHAT_GROUP:
                if (Objects.equals(record.getDeleted(), (byte) 1)) {
                    return 0;
                }
                WechatGroupAddTagRequest wechatGroupAddTagRequest = new WechatGroupAddTagRequest();
                wechatGroupAddTagRequest.setChatIdList(Lists.newArrayList(record.getItemId().substring(0, record.getItemId().lastIndexOf('_'))));
                wechatGroupAddTagRequest.setTagIdList(Lists.newArrayList(record.getTag()));
                return wechatGroupService.addTag(wechatGroupAddTagRequest);
            default:
                break;
        }

        if (itemTagRelationOldService.upsertSelective(record) < 1) {
            PlatformLogUtil.logFail("新增物料与标签关系旧数据失败", LogListUtil.newArrayList(record));
            throw new TripscrmException(TripSCRMErrorCode.DB_FAILED);
        }
        return 1;
    }

    @Override
    @TairLock(value = "'updateItemTagRelation_' + #condition.itemId + '_' + #condition.itemType", waitMilliseconds = 2000L)
    public Integer updateSelective(ItemTagRelationDTO record, ItemTagRelationQuery condition) {
        if (Objects.isNull(record) || Objects.isNull(condition.getItemType()) || !StringUtils.hasText(condition.getItemId())) {
            PlatformLogUtil.logFail("更新物料与标签关系新数据失败，参数错误", LogListUtil.newArrayList(record, condition));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        BizTypeEnum bizTypeEnum = BizTypeEnum.findByCode(condition.getItemType());
        if (Objects.isNull(bizTypeEnum)) {
            PlatformLogUtil.logFail("更新物料与标签关系新数据失败，业务类型错误", LogListUtil.newArrayList(record, condition));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (itemTagRelationService.updateSelective(record, condition) < 1) {
            PlatformLogUtil.logFail("更新物料与标签关系新数据失败", LogListUtil.newArrayList(record, condition));
//            throw new TripscrmException(TripSCRMErrorCode.DB_FAILED);
        }

        if (!Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP, BizTypeEnum.GROUP_MA_OLD, BizTypeEnum.PERSON_MA_OLD, BizTypeEnum.CUSTOMER_ACQUISITION_LINK, BizTypeEnum.WECHAT_CUSTOMER_RELATION).contains(bizTypeEnum)) {
            return 1;
        }

        if (!SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_WRITE_SWITCH) {
            return 1;
        }

        switch (bizTypeEnum) {
            case WECHAT_CUSTOMER:
            case WECHAT_CUSTOMER_RELATION:
                if (!Objects.equals(record.getDeleted(), (byte) 1)) {
                    return 0;
                }
                WechatCustomerDeleteTagRequest wechatCustomerDeleteTagRequest = new WechatCustomerDeleteTagRequest();
                wechatCustomerDeleteTagRequest.setExternalUserIdList(Lists.newArrayList(condition.getItemId().substring(0, condition.getItemId().lastIndexOf('_'))));
                wechatCustomerDeleteTagRequest.setTagIdList(Lists.newArrayList(String.valueOf(condition.getTagId())));
                wechatCustomerService.deleteTag(wechatCustomerDeleteTagRequest);
                return 1;
            case WECHAT_GROUP:
                if (!Objects.equals(record.getDeleted(), (byte) 1)) {
                    return 0;
                }
                WechatGroupDeleteTagRequest wechatGroupDeleteTagRequest = new WechatGroupDeleteTagRequest();
                wechatGroupDeleteTagRequest.setChatIdList(Lists.newArrayList(Lists.newArrayList(condition.getItemId().substring(0, condition.getItemId().lastIndexOf('_')))));
                wechatGroupDeleteTagRequest.setTagIdList(Lists.newArrayList(String.valueOf(condition.getTagId())));
                wechatGroupService.deleteTag(wechatGroupDeleteTagRequest);
                return 1;
            default:
                break;
        }

        if (itemTagRelationOldService.updateSelective(record, condition) < 1) {
            PlatformLogUtil.logFail("更新物料与标签关系旧数据失败", LogListUtil.newArrayList(record, condition));
            throw new TripscrmException(TripSCRMErrorCode.DB_FAILED);
        }
        return 1;
    }

    @Override
    @TairLock(value = "'updateItemTagRelation_' + #request.itemId + '_' + #request.itemType", waitMilliseconds = 2000L)
    public void cover(ItemTagRelationCoverRequest request) {
        BizTypeEnum bizTypeEnum = BizTypeEnum.findByCode(request.getItemType());
        if (Objects.isNull(bizTypeEnum) || Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP).contains(bizTypeEnum)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        itemTagRelationService.cover(request);

        if (!Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP, BizTypeEnum.GROUP_MA_OLD, BizTypeEnum.PERSON_MA_OLD, BizTypeEnum.CUSTOMER_ACQUISITION_LINK).contains(bizTypeEnum)) {
            return;
        }

        if (!SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_WRITE_SWITCH) {
            return;
        }

        itemTagRelationOldService.cover(request);
    }

    @Override
    @AteyeInvoker(description = "给客户打基本标签", paraDesc = "externalUserId&corpId&uid")
    public void addBasicTag(String externalUserId, String corpId, String uid) {
        addTag(externalUserId + "_" + corpId, SwitchConfig.SCRM_DEFAULT_TAG_ID, BizTypeEnum.WECHAT_CUSTOMER.getCode());
        if (!StringUtils.hasLength(uid)) {
            addTag(externalUserId + "_" + corpId, SwitchConfig.WITHOUT_UID_TAG_ID, BizTypeEnum.WECHAT_CUSTOMER.getCode());
            deleteTag(externalUserId + "_" + corpId, SwitchConfig.WITH_UID_TAG_ID, BizTypeEnum.WECHAT_CUSTOMER.getCode());
            return;
        }
        addTag(externalUserId + "_" + corpId, SwitchConfig.WITH_UID_TAG_ID, BizTypeEnum.WECHAT_CUSTOMER.getCode());
        deleteTag(externalUserId + "_" + corpId, SwitchConfig.WITHOUT_UID_TAG_ID, BizTypeEnum.WECHAT_CUSTOMER.getCode());

    }

    private boolean addTag(String itemId, Long tagId, Integer itemType) {
        if (!StringUtils.hasLength(itemId) || !NumberUtils.validLong(tagId) || Objects.isNull(BizTypeEnum.findByCode(itemType))) {
            PlatformLogUtil.logFail("打标失败，参数非法");
            return false;
        }
        ItemTagRelationDTO tagRelationDTO = new ItemTagRelationDTO();
        tagRelationDTO.setItemId(itemId);
        tagRelationDTO.setTagId(tagId);
        tagRelationDTO.setItemType(itemType);
        tagRelationDTO.setDeleted(IsDeleteEnum.NO.getCode());
        try {
            Integer result = upsertSelective(tagRelationDTO);
            if (!NumberUtils.validInteger(result)) {
                PlatformLogUtil.logFail("打标失败", LogListUtil.newArrayList(itemId, tagId, itemType));
                return false;
            }
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("打标出错", e.getMessage(), e, LogListUtil.newArrayList(itemId, tagId, itemType));
            return false;
        }
    }

    private boolean deleteTag(String itemId, Long tagId, Integer itemType) {
        if (!StringUtils.hasLength(itemId) || !NumberUtils.validLong(tagId) || Objects.isNull(BizTypeEnum.findByCode(itemType))) {
            PlatformLogUtil.logFail("删除标签失败，参数非法");
            return false;
        }
        ItemTagRelationQuery condition = new ItemTagRelationQuery();
        condition.setItemId(itemId);
        condition.setItemType(itemType);
        condition.setTagId(tagId);
        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setDeleted(IsDeleteEnum.YES.getCode());
        try {
            Integer result = tagRelationService.updateSelective(itemTagRelationDTO, condition);
            if (!NumberUtils.validInteger(result)) {
                PlatformLogUtil.logFail("删除标签失败", LogListUtil.newArrayList(itemId, tagId, itemType));
                return false;
            }
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("删除标签出错", e.getMessage(), e, LogListUtil.newArrayList(itemId, tagId, itemType));
            return false;
        }
    }

    public List<ItemTagRelationDTO> getWechatCustomerTagRelationOldList(ItemTagRelationQuery query) {
        List<TagInfoDTO> wechatCustomerTagOldList = getWechatCustomerTagOldList(query);
        return wechatCustomerTagOldList.stream()
                .map(tagInfoDTO -> {
                    ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                    itemTagRelationDTO.setTag(tagInfoDTO.getTag());
                    itemTagRelationDTO.setItemId(query.getItemId());
                    itemTagRelationDTO.setItemType(query.getItemType());
                    return itemTagRelationDTO;
                })
                .collect(Collectors.toList());
    }

    private List<TagInfoDTO> getWechatCustomerTagOldList(ItemTagRelationQuery query) {
        String externalUserId = query.getItemId().substring(0, query.getItemId().lastIndexOf('_'));
        String corpId = query.getItemId().substring(query.getItemId().lastIndexOf('_') + 1);
        WechatCustomerDO wechatCustomerDO = wechatCustomerMapper.selectByExternalUserIdAndCorpId(externalUserId, corpId);
        if (Objects.isNull(wechatCustomerDO)) {
            return new ArrayList<>();
        }

        List<Long> tagIdList = Arrays.stream(Optional.ofNullable(wechatCustomerDO.getTags()).orElse("").split(","))
                .filter(StringUtils::hasText)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        return tagInfoService.selectByIdList(tagIdList);
    }

    public List<ItemTagRelationDTO> getWechatGroupTagRelationOldList(ItemTagRelationQuery query) {
        List<TagInfoDTO> wechatGroupTagOldList = getWechatGroupTagOldList(query);
        return wechatGroupTagOldList.stream()
                .map(tagInfoDTO -> {
                    ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                    itemTagRelationDTO.setTag(tagInfoDTO.getTag());
                    itemTagRelationDTO.setItemId(query.getItemId());
                    itemTagRelationDTO.setItemType(query.getItemType());
                    return itemTagRelationDTO;
                })
                .collect(Collectors.toList());
    }

    private List<TagInfoDTO> getWechatGroupTagOldList(ItemTagRelationQuery query) {
        String chatId = query.getItemId().substring(0, query.getItemId().lastIndexOf('_'));
        String corpId = query.getItemId().substring(query.getItemId().lastIndexOf('_') + 1);
        WechatGroupDO wechatGroupDO = wechatGroupMapper.selectByChatIdAndCorpId(chatId, corpId);
        if (Objects.isNull(wechatGroupDO)) {
            return new ArrayList<>();
        }

        List<Long> tagIdList = Arrays.stream(Optional.ofNullable(wechatGroupDO.getTags()).orElse("").split(","))
                .filter(StringUtils::hasText)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        return tagInfoService.selectByIdList(tagIdList);
    }
}