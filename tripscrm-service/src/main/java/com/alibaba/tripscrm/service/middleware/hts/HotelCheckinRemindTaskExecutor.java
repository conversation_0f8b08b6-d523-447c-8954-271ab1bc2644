package com.alibaba.tripscrm.service.middleware.hts;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.MaterialVariableConstants;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.second.TccpManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.fliggy.tccp.client.api.model.OrderDTO;
import com.fliggy.tccp.client.enumerate.HotelSubBizTypeEnum;
import com.fliggy.tccp.client.enumerate.PayStatusEnum;
import com.fliggy.tccp.client.notify.OrderNotify;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.hts.client.domain.result.HTSTaskResult;
import com.taobao.trip.hts.client.domain.task.Task;
import com.taobao.trip.hts.client.executor.TaskCallback;
import com.taobao.trip.hts.client.executor.TaskExecutor;
import com.taobao.trip.hts.client.service.TaskScheduleClient;
import com.taobao.trip.hts.schema.spring.context.annotation.Executor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/10 16:42
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Executor("hotelCheckinRemindTaskExecutor")
public class HotelCheckinRemindTaskExecutor implements TaskExecutor {
    private final TccpManager tccpManager;
    private final MetaqProducer metaqProducer;
    private final LdbTairManager ldbTairManager;
    private final UicUtils uicUtils;
    private final TaskScheduleClient taskScheduleClient;

    private static final String HOTEL_CHECK_REMIND = "hotelCheckRemind";

    @Override
    public TaskCallback execute(Task task) {
        Map<String, Serializable> param = task.getParam();
        try {
            PlatformLogUtil.logInfo("酒店入住提醒hts任务执行，开始执行", LogListUtil.newArrayList(task));
            Long orderId = (Long) param.get("orderId");
            Long userId = (Long) param.get("userId");
            Integer hotelSubBizType = (Integer) param.get("hotelSubBizType");
            HotelSubBizTypeEnum hotelSubBizTypeEnum = HotelSubBizTypeEnum.codeToEnum(hotelSubBizType);

            String unionId = uicUtils.getUnionIdByUid(String.valueOf(userId));
            if (!StringUtils.hasText(unionId)) {
                PlatformLogUtil.logFail("酒店入住提醒hts任务执行，根据uid查询unionId为空", TripSCRMErrorCode.INVALID_PARAMS.getCode(), LogListUtil.newArrayList(param));
                return new TaskCallback();
            }

            OrderDTO orderDTO = tccpManager.queryOrderDTO(orderId, userId, false);
            // 预付和面付，判断支付状态
            boolean valid;
            if (Lists.newArrayList(HotelSubBizTypeEnum.PREPAY, HotelSubBizTypeEnum.FACEPAY).contains(hotelSubBizTypeEnum)) {
                valid = Objects.equals(PayStatusEnum.PAID.getCode(), orderDTO.getOrderDO().getPayStatus());
            } else {
                valid = !Objects.equals(PayStatusEnum.CLOSE.getCode(), orderDTO.getOrderDO().getPayStatus());
            }

            if (!valid) {
                PlatformLogUtil.logFail("酒店入住提醒hts任务执行，订单状态非法", LogListUtil.newArrayList(param, orderDTO));
                return new TaskCallback();
            }

            // 入住时间
            Date serviceStartTime = orderDTO.getResourcesDTOs().get(0).getResourceDO().getServiceStartTime();
            // 需要做入住提醒的日期
            LocalDate remindLocalDate = serviceStartTime
                    .toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
                    .minusDays(1);

            // 如果时间变了，就不发了
            if (!LocalDate.now().equals(remindLocalDate)) {
                PlatformLogUtil.logFail("酒店入住提醒hts任务执行，不是第二天入住，不发了", LogListUtil.newArrayList(serviceStartTime));
                return new TaskCallback();
            }

            if (ldbTairManager.incr(TairConstant.TCCP_CONSUME_DISTINCT_KEY + HOTEL_CHECK_REMIND + "_" + orderId, 1, 0, 120) > 1) {
                PlatformLogUtil.logFail("酒店入住提醒hts任务执行，重复消费，不做处理", LogListUtil.newArrayList(param, orderDTO));
                return new TaskCallback();
            }

            // 入住时间
            String checkinDateStr = serviceStartTime.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate()
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            // 入住酒店名称
            String hotelName = orderDTO.getOrderDO().getAttribute("common_tp_info_hotel_name");
            // 酒店地址
            String address = orderDTO.getResourcesDTOs().get(0).getResourceDO().getAddress();
            // 入住人姓名
            String passengerNameList = orderDTO.getOrderPassengerDTOS().stream()
                    .map(orderPassengerDTO -> orderPassengerDTO.getOrderPassengerDO().getName())
                    .collect(Collectors.joining(","));
            // 入住目的地
            String cityName = orderDTO.getResourcesDTOs().get(0).getResourceDO().getCity();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("unionId", unionId);
            jsonObject.put(MaterialVariableConstants.HOTEL_NAME, hotelName);
            jsonObject.put(MaterialVariableConstants.HOTEL_ADDRESS, address);
            jsonObject.put(MaterialVariableConstants.CHECK_IN_DATE, checkinDateStr);
            jsonObject.put(MaterialVariableConstants.ARR_CITY_NAME, cityName);
            jsonObject.put(MaterialVariableConstants.PASSENGER_NAME, passengerNameList);

            metaqProducer.send(MQEnum.WECHAT_CUSTOMER_HOTEL_CHECK_IN_REMIND, "", "", jsonObject.toJSONString());
            PlatformLogUtil.logInfo("酒店入住提醒hts任务执行，酒店入住提醒事件", LogListUtil.newArrayList(param, orderDTO, jsonObject));
        } catch (Exception e) {
            PlatformLogUtil.logException("酒店入住提醒hts任务执行，执行异常", e.getMessage(), e, LogListUtil.newArrayList(task));
        }
        return new TaskCallback();
    }

    @AteyeInvoker(description = "测试酒店入住提醒事件发送", paraDesc = "orderId&userId&unionId")
    public void testRemind(long orderId, long userId, String unionId) {
        OrderDTO orderDTO = tccpManager.queryOrderDTO(orderId, userId, false);
        // 入住时间
        Date serviceStartTime = orderDTO.getResourcesDTOs().get(0).getResourceDO().getServiceStartTime();
        // 入住时间
        String checkinDateStr = serviceStartTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 入住酒店名称
        String hotelName = orderDTO.getOrderDO().getAttribute("common_tp_info_hotel_name");
        // 酒店地址
        String address = orderDTO.getResourcesDTOs().get(0).getResourceDO().getAddress();
        // 入住人姓名
        String passengerNameList = orderDTO.getOrderPassengerDTOS().stream()
                .map(orderPassengerDTO -> orderPassengerDTO.getOrderPassengerDO().getName())
                .collect(Collectors.joining(","));
        // 入住目的地
        String cityName = orderDTO.getResourcesDTOs().get(0).getResourceDO().getCity();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("unionId", unionId);
        jsonObject.put(MaterialVariableConstants.HOTEL_NAME, hotelName);
        jsonObject.put(MaterialVariableConstants.HOTEL_ADDRESS, address);
        jsonObject.put(MaterialVariableConstants.CHECK_IN_DATE, checkinDateStr);
        jsonObject.put(MaterialVariableConstants.ARR_CITY_NAME, cityName);
        jsonObject.put(MaterialVariableConstants.PASSENGER_NAME, passengerNameList);
        metaqProducer.send(MQEnum.WECHAT_CUSTOMER_HOTEL_CHECK_IN_REMIND, "", "", jsonObject.toJSONString());
        PlatformLogUtil.logInfo("酒店入住提醒测试，酒店入住提醒事件", LogListUtil.newArrayList(orderId, userId, orderDTO, jsonObject));
    }

    public void register(OrderNotify orderNotify, Date executeTime) {
        Task task = new Task();
        task.setTaskExecutor(this);
        task.setExecuteTime(executeTime);
        task.setTimeout(3000);
        Map<String, Serializable> param = new HashMap<>();
        param.put("orderId", orderNotify.getOrderId());
        param.put("userId", orderNotify.getBuyerId());
        param.put("hotelSubBizType", orderNotify.getSubBizType());
        task.setParam(param);
        HTSTaskResult<Boolean> result = taskScheduleClient.registerTask(task);
        if (Objects.isNull(result) || !result.isSuccess()) {
            PlatformLogUtil.logFail("酒店入住提醒hts任务注册失败", LogListUtil.newArrayList(task, result));
            return;
        }
        PlatformLogUtil.logInfo("酒店入住提醒hts任务注册成功", LogListUtil.newArrayList(task, result));
    }
}
