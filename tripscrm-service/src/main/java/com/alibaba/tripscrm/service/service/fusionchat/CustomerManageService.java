package com.alibaba.tripscrm.service.service.fusionchat;

import com.alibaba.tripscrm.dal.model.domain.data.CustomerManageGroupDO;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.ChatGroupRequest;

/**
 * 客户管理服务
 */
public interface CustomerManageService {

    /**
     * 根据客户关系ID查询客户管理组
     * @param customerRelationId 客户关系ID
     * @return 管理组信息
     */
    CustomerManageGroupDO queryGroupByRelationId(String customerRelationId, ResourceTypeEnum relationType);

    /**
     * 查询客户归属成员的管理组
     * @param userId 成员ID
     * @param externalUserId 拓展客户ID
     * @param corpId 组织ID
     * @return 管理组信息
     */
    CustomerManageGroupDO queryGroupByUser(String userId, String externalUserId, String corpId);

    /**
     * 查询客户群归属成员的的管理组
     * @param userId 成员ID
     * @param chatId 群聊ID
     * @param corpId 组织ID
     * @return 管理组信息
     */
    CustomerManageGroupDO queryGroupByGroup(String userId, String chatId, String corpId);

    /**
     * 给用户分组
     * @param request 请求参数
     * @return 分组结果
     */
    Boolean chatGroup(ChatGroupRequest request);

    /**
     * 管理组是否存在可用关联关系
     * @param id 主键
     * @return 存在结果
     */
    Boolean existGroupRelation(Long id);

}
