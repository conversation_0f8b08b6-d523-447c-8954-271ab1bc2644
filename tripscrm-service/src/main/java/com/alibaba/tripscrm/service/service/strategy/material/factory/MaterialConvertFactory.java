package com.alibaba.tripscrm.service.service.strategy.material.factory;

import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.model.dto.material.MaterialContentDTO;
import com.alibaba.tripscrm.service.service.strategy.material.AbstractMaterialConverter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Component
@AllArgsConstructor
public class MaterialConvertFactory<T extends MaterialContentDTO> {

    private final List<AbstractMaterialConverter<T>> materialConverterList;

    private final Map<MaterialContentTypeEnum, AbstractMaterialConverter<T>> materialConverterMap = new ConcurrentHashMap<>();

    @PostConstruct
    void init() {
        for (AbstractMaterialConverter<T> tAbstractMaterialConverter : materialConverterList) {
            materialConverterMap.put(tAbstractMaterialConverter.contentType(), tAbstractMaterialConverter);
        }
    }

    /**
     * 获取素材内容转化器
     * @param typeEnum 素材内容类型
     * @return 素材转化器
     */
    public AbstractMaterialConverter<T> getMaterialContentConverter(MaterialContentTypeEnum typeEnum) {
        return materialConverterMap.get(typeEnum);
    }

}
