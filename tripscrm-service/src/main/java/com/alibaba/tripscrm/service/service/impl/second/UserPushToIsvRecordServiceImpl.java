package com.alibaba.tripscrm.service.service.impl.second;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.tddl.UserPushToIsvRecordMapper;
import com.alibaba.tripscrm.dal.model.domain.data.UserPushToIsvRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.UserPushToIsvRecordParam;
import com.alibaba.tripscrm.dal.model.domain.data.UserPushToIsvRecordParam.Criteria;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.enums.callpush.AddWechatCustomerByMsgStageEnum;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.model.domain.request.UserPushToIsvRecordRequest;
import com.alibaba.tripscrm.service.model.dto.vippush.UserPushToIsvRecordDTO;
import com.alibaba.tripscrm.service.service.second.UserPushToIsvRecordService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Service
public class UserPushToIsvRecordServiceImpl implements UserPushToIsvRecordService {

    @Resource
    private UserPushToIsvRecordMapper userPushToIsvRecordMapper;

    /**
     * 根据参数统计总数
     * @param param
     */
    @Override
    public long count(UserPushToIsvRecordRequest param) {
        UserPushToIsvRecordParam userPushToIsvRecordParam = new UserPushToIsvRecordParam();
        Criteria criteria = userPushToIsvRecordParam.createCriteria();
        setCriteria(param, criteria);
        return userPushToIsvRecordMapper.countByParam(userPushToIsvRecordParam);
    }

    /**
     * 根据参数查询
     * @param param
     */
    @Override
    public UserPushToIsvRecordDTO find(UserPushToIsvRecordRequest param) {
        UserPushToIsvRecordParam userPushToIsvRecordParam = new UserPushToIsvRecordParam();
        Criteria criteria = userPushToIsvRecordParam.createCriteria();
        if(param.getId() != null){
            UserPushToIsvRecordDO recordDO = userPushToIsvRecordMapper.selectByPrimaryKey(param.getId());
            return convertFromDO(recordDO);
        }
        setCriteria(param, criteria);
        List<UserPushToIsvRecordDO> list = userPushToIsvRecordMapper.selectByParam(userPushToIsvRecordParam);
        if (null == list || list.isEmpty()) {
                return null;
        }
        return convertFromDO(list.get(0));
    }

    /**
     * 列表查询
     * @param param
     */
    @Override
    public List<UserPushToIsvRecordDTO> list(UserPushToIsvRecordRequest param) {
        UserPushToIsvRecordParam userPushToIsvRecordParam = new UserPushToIsvRecordParam();
        Criteria criteria = userPushToIsvRecordParam.createCriteria();
        setCriteria(param, criteria);
        List<UserPushToIsvRecordDO> list = userPushToIsvRecordMapper.selectByParam(userPushToIsvRecordParam);
        if (null == list || list.isEmpty()) {
                return null;
        }
        List<UserPushToIsvRecordDTO> result = new ArrayList<>();
        for (UserPushToIsvRecordDO record : list) {
            UserPushToIsvRecordDTO userPushToIsvRecordDTO = convertFromDO(record);
                result.add(userPushToIsvRecordDTO);
        }
        return result;
    }

    /**
     * 创建
     * @param param
     */
    @Override
    public void create(UserPushToIsvRecordDTO param) {
        UserPushToIsvRecordDO record = new UserPushToIsvRecordDO();
        record.setId(param.getId());
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setIsvCode(param.getIsvCode());
        record.setUserId(param.getUserId());
        record.setUserIdEncrypt(param.getUserIdEncrypt());
        record.setCellPhone(param.getCellPhone());
        record.setCellPhoneEncrypt(param.getCellPhoneEncrypt());
        record.setCrowdType(param.getCrowdType());
        record.setGuideType(param.getGuideType());
        record.setPushStage(param.getPushStage());
        record.setCallStage(param.getCallStage());
        record.setMsgStage(param.getMsgStage());
        record.setJoinWxStage(param.getJoinWxStage());
        record.setStatus(param.getStatus());
        record.setMsgUrl(param.getMsgUrl());
        record.setUnionId(param.getUnionId());
        record.setCallBackContent(param.getCallBackContent());
        record.setCrowdId(param.getCrowdId());
        int effect = userPushToIsvRecordMapper.insert(record);
        if(effect <= 0){
            PlatformLogUtil.logFail("用户外推记录落库失败", LogListUtil.newArrayList(param, record));
        }
        // id赋值回去
        param.setId(record.getId());
    }

    /**
     * 修改
     * @param dto
     * @param param
     */
    @Override
    public void updateSelective(UserPushToIsvRecordDTO dto, UserPushToIsvRecordRequest param) {
        UserPushToIsvRecordDO record = convertFromDTO(dto);
        record.setGmtModified(new Date());
        if(record.getId() != null){
            int effect = userPushToIsvRecordMapper.updateByPrimaryKey(record);
            if(effect <= 0){
                PlatformLogUtil.logFail("根据主键更新用户外推记录失败", LogListUtil.newArrayList(param, dto, record));
            }
            return;
        }
        UserPushToIsvRecordParam userPushToIsvRecordParam = new UserPushToIsvRecordParam();
        Criteria criteria = userPushToIsvRecordParam.createCriteria();
        setCriteria(param, criteria);
        int effect = userPushToIsvRecordMapper.updateByParamSelective(record, userPushToIsvRecordParam);
        if(effect <= 0){
            PlatformLogUtil.logFail("根据条件更新用户外推记录失败", LogListUtil.newArrayList(param, dto, record));
        }
    }

    @Override
    public BaseResult<Boolean> updateUnionIdByUserIdEncrypt(UserPushToIsvRecordDTO dto) {
        if(dto == null || StringUtils.isBlank(dto.getUserIdEncrypt())){
            PlatformLogUtil.logFail("根据加密的userId更新推送记录的unionId入参非法", LogListUtil.newArrayList(dto));
            return BaseResult.fail(ErrorCodeEnum.PARAM_INVALID.getCode(), ErrorCodeEnum.PARAM_INVALID.getDesc());
        }

        UserPushToIsvRecordParam param = new UserPushToIsvRecordParam();
        Criteria criteria = param.createCriteria();
        criteria.andUserIdEncryptEqualTo(dto.getUserIdEncrypt());

        UserPushToIsvRecordDO record = new UserPushToIsvRecordDO();
        record.setJoinWxStage(AddWechatCustomerByMsgStageEnum.ENTER_JOIN_PAGE.getCode());
        if(StringUtils.isNotBlank(dto.getUnionId())){
            record.setUnionId(dto.getUnionId());
        }

        int effect = userPushToIsvRecordMapper.updateByParamSelective(record, param);
        if(effect <= 0){
            PlatformLogUtil.logFail("根据条件更新用户外推记录失败", LogListUtil.newArrayList(param, dto, record));
            return BaseResult.fail(ErrorCodeEnum.UPDATE_UNION_ID_BY_USER_ID_ENCRYPT_FAIL.getCode(), ErrorCodeEnum.UPDATE_UNION_ID_BY_USER_ID_ENCRYPT_FAIL.getDesc());
        }
        return BaseResult.success(true);
    }

    private void setCriteria(UserPushToIsvRecordRequest param, Criteria criteria) {
        if (param.getId() != null) {
            criteria.andIdEqualTo(param.getId());
        }
        if(param.getIsvCode() != null){
            criteria.andIsvCodeEqualTo(param.getIsvCode());
        }
        if(param.getUserId() != null){
            criteria.andUserIdEqualTo(param.getUserId());
        }
        if(param.getUserIdEncrypt() != null){
            criteria.andUserIdEncryptEqualTo(param.getUserIdEncrypt());
        }
        if(param.getCellPhone() != null){
            criteria.andCellPhoneEqualTo(param.getCellPhone());
        }
        if(param.getCellPhoneEncrypt() != null){
            criteria.andCellPhoneEncryptEqualTo(param.getCellPhoneEncrypt());
        }
        if(param.getCrowdType() != null){
            criteria.andCrowdTypeEqualTo(param.getCrowdType());
        }
        if(param.getGuideType() != null){
            criteria.andGuideTypeEqualTo(param.getGuideType());
        }
        if(param.getPushStage() != null){
            criteria.andPushStageEqualTo(param.getPushStage());
        }
        if(param.getCallStage() != null){
            criteria.andCallStageEqualTo(param.getCallStage());
        }
        if(param.getMsgStage() != null){
            criteria.andMsgStageEqualTo(param.getMsgStage());
        }
        if(param.getJoinWxStage() != null){
            criteria.andJoinWxStageEqualTo(param.getJoinWxStage());
        }
        if(param.getStatus() != null){
            criteria.andStatusEqualTo(param.getStatus());
        }
    }

    /**
     * DTO模型转换成DO模型
     * @param userPushToIsvRecordDTO
     */
    public UserPushToIsvRecordDO convertFromDTO(UserPushToIsvRecordDTO userPushToIsvRecordDTO) {
        UserPushToIsvRecordDO userPushToIsvRecordDO = new UserPushToIsvRecordDO();
        BeanUtils.copyProperties(userPushToIsvRecordDTO,userPushToIsvRecordDO);
        return userPushToIsvRecordDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param userPushToIsvRecordDO
     */
    public UserPushToIsvRecordDTO convertFromDO(UserPushToIsvRecordDO userPushToIsvRecordDO) {
        UserPushToIsvRecordDTO userPushToIsvRecordDTO = new UserPushToIsvRecordDTO();
        BeanUtils.copyProperties(userPushToIsvRecordDO,userPushToIsvRecordDTO);
        return userPushToIsvRecordDTO;
    }
}