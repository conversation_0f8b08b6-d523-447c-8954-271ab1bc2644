package com.alibaba.tripscrm.service.manager.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.model.domain.data.CallPushActivityRecordDO;
import com.alibaba.tripscrm.service.constant.ScrmConstant;
import com.alibaba.tripscrm.service.enums.callpush.CallPushFatigueEnum;
import com.alibaba.tripscrm.service.enums.callpush.CallStageEnum;
import com.alibaba.tripscrm.service.enums.task.CycleEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.CallPushActivityRecordQuery;
import com.alibaba.tripscrm.service.service.callpush.CallPushActivityRecordService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.fliggy.crowd.service.TripCrowdCommonService;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.common.keycenter.security.Cryptograph;
import com.taobao.trip.platform.protocol.TripCommonPlatformResult;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 外呼推送管理器
 *
 * <AUTHOR>
 * @since 2024/8/2 18:57
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CallPushManager {
    /**
     * 外呼推送疲劳度管控白名单
     */
    @Switch(description = "外呼推送疲劳度管控白名单", name = "callPushFatigueWhiteList")
    public static String callPushFatigueWhiteList = "";

    private final CallPushActivityRecordService callPushActivityRecordService;
    private final Cryptograph cryptograph;
    private final TripCrowdCommonService tripCrowdCommonService;

    /**
     * 诸葛人群列表匹配
     * @param uid 用户ID
     * @param crowdIds 诸葛人群Id列表
     * @return 是否命中
     */
    public TripCommonPlatformResult<Map<Long, Boolean>> isUidInCrowds(Long uid, List<Long> crowdIds) {
        return tripCrowdCommonService.isUidInCrowd(uid, crowdIds);
    }


    /**
     * 加密
     */
    public String encrypt(String value) {
        if (!StringUtils.hasText(value)) {
            return value;
        }

        return cryptograph.encrypt(value, ScrmConstant.KEY_CENTER_KEY);
    }

    /**
     * 解密
     */
    public String decrypt(String encryptValue) {
        if (!StringUtils.hasText(encryptValue)) {
            return encryptValue;
        }

        return cryptograph.decrypt(encryptValue, ScrmConstant.KEY_CENTER_KEY);
    }

    @TairLock(value = "'callPushFatigueManager' + #record.uid", waitMilliseconds = 2000)
    public boolean fatigueAcquire(CallPushActivityRecordDO record) {
        // 命中白名单
        if (!EnvUtils.isOnline() && callPushFatigueWhiteList.contains(String.valueOf(record.getUid()))) {
            PlatformLogUtil.logFail("外呼推送疲劳度管理，命中客户白名单", LogListUtil.newArrayList(record.getUid()));
            return true;
        }

        // callPushFatigueEnum -> {cycleEnum -> fatigueCount}
        for (String key : SwitchConfig.CALL_PUSH_FATIGUE_CONFIG.keySet()) {
            CallPushFatigueEnum callPushFatigueEnum = CallPushFatigueEnum.of(key);
            Map<Integer, Integer> fatigueConfig = SwitchConfig.CALL_PUSH_FATIGUE_CONFIG.get(key);
            for (Integer cycle : fatigueConfig.keySet()) {
                CycleEnum cycleEnum = CycleEnum.of(cycle);
                int lastPushDayVersion = getRecordCount(record.getUid(), cycleEnum, callPushFatigueEnum);
                if (lastPushDayVersion >= fatigueConfig.get(cycle)) {
                    PlatformLogUtil.logFail("外呼推送疲劳度管理，超出疲劳度限制", LogListUtil.newArrayList(record.getUid(), callPushFatigueEnum, cycleEnum));
                    return false;
                }
            }
        }

        Integer lines = callPushActivityRecordService.insert(record);
        if (Optional.ofNullable(lines).orElse(0) <= 0) {
            PlatformLogUtil.logFail("外呼推送疲劳度管理，新增外呼推送记录失败", LogListUtil.newArrayList(record));
            return false;
        }
        return true;
    }

    private Integer getRecordCount(Long uid, CycleEnum cycleEnum, CallPushFatigueEnum callPushFatigueEnum) {
        LocalDateTime beginLocalDateTime = LocalDate.now().atStartOfDay();
        switch (cycleEnum) {
            case DAY:
                beginLocalDateTime = beginLocalDateTime.minusDays(1);
                break;
            case WEEK:
                beginLocalDateTime = beginLocalDateTime.minusDays(7);
            case HALF_MONTH:
                beginLocalDateTime = beginLocalDateTime.minusDays(15);
                break;
            case MONTH:
                beginLocalDateTime = beginLocalDateTime.minusDays(30);
                break;
            case TWO_MONTH:
                beginLocalDateTime = beginLocalDateTime.minusDays(60);
                break;
        }

        Date beginDate = Date.from(beginLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());
        switch (callPushFatigueEnum) {
            case PUSH:
                return getCallPushList(uid, beginDate).size();
            case CALL_FAIL:
                return getCallFailList(uid, beginDate).size();
            case CALL_SUCCESS:
                return getCallSuccessList(uid, beginDate).size();
            case SAMPLING:
                return getCallSamplingList(uid, beginDate).size();
            default:
                return 0;
        }
    }

    private List<CallPushActivityRecordDO> getCallPushList(Long uid, Date startCreateTime) {
        List<CallPushActivityRecordDO> callPushActivityRecordDOS = callPushActivityRecordService.selectByCondition(
                CallPushActivityRecordQuery.builder()
                        .uid(uid)
                        .startCreateTime(startCreateTime)
                        .build()
        );
        return Optional.ofNullable(callPushActivityRecordDOS).orElse(new ArrayList<>());
    }

    private List<CallPushActivityRecordDO> getCallFailList(Long uid, Date startCreateTime) {
        List<CallPushActivityRecordDO> callPushActivityRecordDOS = callPushActivityRecordService.selectByCondition(
                CallPushActivityRecordQuery.builder()
                        .uid(uid)
                        .startCreateTime(startCreateTime)
                        .callStage(Lists.newArrayList(CallStageEnum.CALL_FAIL.getCode()))
                        .build()
        );
        return Optional.ofNullable(callPushActivityRecordDOS).orElse(new ArrayList<>());
    }

    private List<CallPushActivityRecordDO> getCallSuccessList(Long uid, Date startCreateTime) {
        List<CallPushActivityRecordDO> callPushActivityRecordDOS = callPushActivityRecordService.selectByCondition(
                CallPushActivityRecordQuery.builder()
                        .uid(uid)
                        .startCreateTime(startCreateTime)
                        .callStage(Lists.newArrayList(CallStageEnum.CALL_SUCCESS_A.getCode(), CallStageEnum.CALL_SUCCESS_B.getCode(), CallStageEnum.CALL_SUCCESS_C.getCode(), CallStageEnum.CALL_SUCCESS_D.getCode()))
                        .build()
        );
        return Optional.ofNullable(callPushActivityRecordDOS).orElse(new ArrayList<>());
    }

    private List<CallPushActivityRecordDO> getCallSamplingList(Long uid, Date startCreateTime) {
        List<CallPushActivityRecordDO> callPushActivityRecordDOS = callPushActivityRecordService.selectByCondition(
                CallPushActivityRecordQuery.builder()
                        .uid(uid)
                        .startCreateTime(startCreateTime)
                        .callStage(Lists.newArrayList(CallStageEnum.SAMPLING.getCode()))
                        .build()
        );
        return Optional.ofNullable(callPushActivityRecordDOS).orElse(new ArrayList<>());
    }

    @AteyeInvoker(description = "外呼疲劳度卡控测试", paraDesc = "uid")
    public boolean checkFatigue(Long uid) {
        // 命中白名单
        if (!EnvUtils.isOnline() && callPushFatigueWhiteList.contains(String.valueOf(uid))) {
            PlatformLogUtil.logFail("外呼推送疲劳度管理，命中客户白名单", LogListUtil.newArrayList(uid));
            return true;
        }

        // callPushFatigueEnum -> {cycleEnum -> fatigueCount}
        for (String key : SwitchConfig.CALL_PUSH_FATIGUE_CONFIG.keySet()) {
            CallPushFatigueEnum callPushFatigueEnum = CallPushFatigueEnum.of(key);
            Map<Integer, Integer> fatigueConfig = SwitchConfig.CALL_PUSH_FATIGUE_CONFIG.get(key);
            for (Integer cycle : fatigueConfig.keySet()) {
                CycleEnum cycleEnum = CycleEnum.of(cycle);
                int lastPushDayVersion = getRecordCount(uid, cycleEnum, callPushFatigueEnum);
                if (lastPushDayVersion >= fatigueConfig.get(cycle)) {
                    PlatformLogUtil.logFail("外呼推送疲劳度管理，超出疲劳度限制", LogListUtil.newArrayList(uid, callPushFatigueEnum, cycleEnum));
                    return false;
                }
            }
        }

        return true;
    }
}
