package com.alibaba.tripscrm.service.service.impl.callpush;

import com.alibaba.tripscrm.dal.mapper.tddl.CallPushActivityRecordMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CallPushActivityRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.CallPushActivityRecordParam;
import com.alibaba.tripscrm.service.model.domain.query.CallPushActivityRecordCondition;
import com.alibaba.tripscrm.service.model.domain.query.CallPushActivityRecordQuery;
import com.alibaba.tripscrm.service.service.callpush.CallPushActivityRecordService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/8/3 12:05
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CallPushActivityRecordServiceImpl implements CallPushActivityRecordService {
    private final CallPushActivityRecordMapper callPushActivityRecordMapper;

    @Override
    public List<CallPushActivityRecordDO> selectByCondition(CallPushActivityRecordQuery query) {
        CallPushActivityRecordParam param = new CallPushActivityRecordParam();
        CallPushActivityRecordParam.Criteria criteria = param.or();
        if (NumberUtils.biggerThanZero(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (NumberUtils.biggerThanZero(query.getUid())) {
            criteria.andUidEqualTo(query.getUid());
        }
        if (NumberUtils.biggerThanZero(query.getActivityId())) {
            criteria.andActivityIdEqualTo(query.getActivityId());
        }
        if (StringUtils.hasText(query.getUnionId())) {
            criteria.andUnionIdEqualTo(query.getUnionId());
        }
        if (StringUtils.hasText(query.getUidEncrypt())) {
            criteria.andUidEncryptEqualTo(query.getUidEncrypt());
        }
        if (StringUtils.hasText(query.getCellPhoneEncrypt())) {
            criteria.andCellPhoneEncryptEqualTo(query.getCellPhoneEncrypt());
        }
        if (!CollectionUtils.isEmpty(query.getPushStage())) {
            criteria.andPushStageIn(query.getPushStage());
        }
        if (!CollectionUtils.isEmpty(query.getCallStage())) {
            criteria.andCallStageIn(query.getCallStage());
        }
        if (Objects.nonNull(query.getStartCreateTime())) {
            criteria.andGmtCreateGreaterThanOrEqualTo(query.getStartCreateTime());
        }
        criteria.andDeletedEqualTo((byte) 0);
        param.appendOrderByClause(CallPushActivityRecordParam.OrderCondition.GMTCREATE, CallPushActivityRecordParam.SortType.DESC);
        return Optional.ofNullable(callPushActivityRecordMapper.selectByParam(param)).orElse(new ArrayList<>());
    }

    @Override
    public Integer insert(CallPushActivityRecordDO record) {
        return callPushActivityRecordMapper.insertSelective(record);
    }

    @Override
    @AteyeInvoker(description = "外呼推送记录删除", paraDesc = "id")
    public Integer delete(Long id) {
        return callPushActivityRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public Integer updateByCondition(CallPushActivityRecordDO record, CallPushActivityRecordCondition condition) {
        CallPushActivityRecordParam param = new CallPushActivityRecordParam();
        CallPushActivityRecordParam.Criteria criteria = param.or();
        if (NumberUtils.biggerThanZero(condition.getId())) {
            criteria.andIdEqualTo(condition.getId());
        }
        if (!CollectionUtils.isEmpty(condition.getCallStage())) {
            criteria.andCallStageIn(condition.getCallStage());
        }
        if (!CollectionUtils.isEmpty(condition.getPushStage())) {
            criteria.andPushStageIn(condition.getPushStage());
        }
        if (!CollectionUtils.isEmpty(condition.getAddByMsgStage())) {
            criteria.andAddByMsgStageIn(condition.getAddByMsgStage());
        }
        if (!CollectionUtils.isEmpty(condition.getAddByPhoneStage())) {
            criteria.andAddByPhoneStageIn(condition.getAddByPhoneStage());
        }

        return callPushActivityRecordMapper.updateByParamSelective(record, param);
    }

    @Override
    public Integer updateById(CallPushActivityRecordDO record) {
        return callPushActivityRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public Integer updateByUidAndActivityIdAndDayVersion(CallPushActivityRecordDO record) {
        CallPushActivityRecordParam param = new CallPushActivityRecordParam();
        param.or()
                .andUidEqualTo(record.getUid())
                .andActivityIdEqualTo(record.getActivityId())
                .andDayVersionEqualTo(record.getDayVersion());
        return callPushActivityRecordMapper.updateByParamSelective(record, param);
    }
}
