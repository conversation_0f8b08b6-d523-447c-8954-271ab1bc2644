package com.alibaba.tripscrm.service.service.second;

import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.model.domain.request.UserPushToIsvRecordRequest;
import com.alibaba.tripscrm.service.model.dto.vippush.UserPushToIsvRecordDTO;

import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public interface UserPushToIsvRecordService {
    /**
     * 根据参数统计总数
     * @param param
     */
    long count(UserPushToIsvRecordRequest param);

    /**
     * 根据参数查询
     * @param param
     */
    UserPushToIsvRecordDTO find(UserPushToIsvRecordRequest param);

    /**
     * 列表查询
     * @param param
     */
    List<UserPushToIsvRecordDTO> list(UserPushToIsvRecordRequest param);

    /**
     * 创建
     * @param param
     */
    void create(UserPushToIsvRecordDTO param);

    /**
     * 选择性修改
     * @param dto
     * @param param
     */
    void updateSelective(UserPushToIsvRecordDTO dto, UserPushToIsvRecordRequest param);

    /**
     * 根据加密的userId更新推送记录的unionId
     *  @param dto
     */
    BaseResult<Boolean> updateUnionIdByUserIdEncrypt(UserPushToIsvRecordDTO dto);
}