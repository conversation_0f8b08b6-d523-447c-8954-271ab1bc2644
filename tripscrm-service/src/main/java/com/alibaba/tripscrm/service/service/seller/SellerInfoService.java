package com.alibaba.tripscrm.service.service.seller;

import com.alibaba.tripscrm.service.model.domain.request.CreateNewSellerInfoParam;
import com.alibaba.tripscrm.service.model.domain.query.seller.SellerInfoQuery;
import com.alibaba.tripscrm.service.model.dto.seller.SellerInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SellerInfoService {
    /**
     * 根据参数统计总数
     *
     * @param query
     */
    long count(SellerInfoQuery query);

    /**
     * 根据参数查询
     *
     * @param query
     */
    SellerInfoDTO find(SellerInfoQuery query);

    /**
     * 列表查询
     *
     * @param query
     */
    List<SellerInfoDTO> listByCondition(SellerInfoQuery query);

    /**
     * 扫全表，查询启用商家
     *
     */
    List<SellerInfoDTO> listAllValid();

    /**
     * 创建
     *
     * @param record
     */
    int createSelective(SellerInfoDTO record);

    /**
     * 选择性修改
     *
     * @param dto
     * @param query
     */
    int updateSelective(SellerInfoDTO dto, SellerInfoQuery query);


    /**
     * 用作消息接收后的状态变更
     *
     * @param sellerId 招商协议通知消息传回的sellerId
     * @param status   招商协议通知消息告诉协议status
     */
    boolean updateBizAgtStatus(String sellerId, Integer status);


    /**
     * 用作商家第一次的创建(有特殊性)
     *
     * @return spaceId
     */
    int newSellerCreate(CreateNewSellerInfoParam param);
}