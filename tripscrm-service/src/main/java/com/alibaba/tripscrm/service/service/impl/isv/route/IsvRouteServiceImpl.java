package com.alibaba.tripscrm.service.service.impl.isv.route;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.RobotDTO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.RuleConstant;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.query.IsvRouteStrategyPageQuery;
import com.alibaba.tripscrm.service.model.domain.query.RuleQuery;
import com.alibaba.tripscrm.service.model.dto.isv.GroupMemberAllocationDTO;
import com.alibaba.tripscrm.service.model.dto.isv.GroupMemberAllocationStrategyDTO;
import com.alibaba.tripscrm.service.model.dto.isv.IsvRouteStrategyDTO;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.isv.route.AbstractActionRouteStrategy;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2025/2/21 18:08
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class IsvRouteServiceImpl implements IsvRouteService {
    private final RuleService ruleService;
    private final WechatUserService wechatUserService;
    private final SpaceService spaceService;

    private static final List<RobotTypeEnum> ROBOT_TYPE_LIST = Arrays.asList(
            RobotTypeEnum.EMPLOYEE,
            RobotTypeEnum.ASSISTANT
    );

    @Override
    public IsvRouteStrategyDTO getIsvRouteStrategyByAction(RiskActionEnum riskActionEnum) {
        if (Objects.isNull(riskActionEnum)) {
            PlatformLogUtil.logFail("获取服务商路由策略，参数非法", LogListUtil.newArrayList());
            return null;
        }

        RuleQuery ruleQuery = new RuleQuery();
        ruleQuery.setEnv(EnvUtils.getEnvironment());
        ruleQuery.setType(RuleTypeEnum.ISV_ACTION_ROUTE_STRATEGY.getCode().byteValue());
        ruleQuery.setTargetId(riskActionEnum.getActionCode());
        RuleDO ruleDO = ruleService.selectByTargetId(RuleTypeEnum.ISV_ACTION_ROUTE_STRATEGY.getCode().byteValue(), ActivityTargetTypeEnum.ACTION_CODE.getCode().byteValue(), riskActionEnum.getActionCode());
        return convert2IsvRouteStrategyDTO(ruleDO);
    }

    @Override
    @Cacheable(key = "'getIsvRouteStrategyByAction_actionCode:' + #riskActionEnum.actionCode + '_' + #env", value = "common10SecondsAnd64SizeCacheManager", unless = "#result == null")
    public IsvRouteStrategyDTO getIsvRouteStrategyByActionWithCache(RiskActionEnum riskActionEnum, String env) {
        return getIsvRouteStrategyByAction(riskActionEnum);
    }

    @Override
    public PageInfo<IsvRouteStrategyDTO> pageQueryIsvRouteStrategy(IsvRouteStrategyPageQuery query) {
        if (Objects.isNull(query)) {
            PlatformLogUtil.logFail("获取服务商路由策略，参数非法", LogListUtil.newArrayList(query));
            return null;
        }

        RuleQuery ruleQuery = new RuleQuery();
        if (NumberUtils.validInteger(query.getPageNum())) {
            ruleQuery.setPageNum(query.getPageNum());
        }
        if (NumberUtils.validInteger(query.getPageSize())) {
            ruleQuery.setPageSize(query.getPageSize());
        }
        ruleQuery.setEnv(EnvUtils.getEnvironment());
        ruleQuery.setType(RuleTypeEnum.ISV_ACTION_ROUTE_STRATEGY.getCode().byteValue());
        PageInfo<RuleDO> pageInfo = ruleService.pageQuery(ruleQuery);
        return PageUtils.getPageInfo(pageInfo, this::convert2IsvRouteStrategyDTO);
    }

    @Override
    public TripSCRMResult<Boolean> addIsvRouteStrategy(IsvRouteStrategyDTO isvRouteStrategyDTO) {
        if (Objects.isNull(isvRouteStrategyDTO)
                || Objects.isNull(isvRouteStrategyDTO.getActionEnum())
                || CollectionUtils.isEmpty(isvRouteStrategyDTO.getIsvRoutePercentMap())
                || CollectionUtils.isEmpty(isvRouteStrategyDTO.getRobotTypeRoutePercentMap())
        ) {
            PlatformLogUtil.logFail("创建服务商路由策略，参数非法", LogListUtil.newArrayList(isvRouteStrategyDTO));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        try {
            RuleQuery ruleQuery = new RuleQuery();
            ruleQuery.setTargetId(isvRouteStrategyDTO.getActionEnum().getActionCode());
            ruleQuery.setTargetType(ActivityTargetTypeEnum.ACTION_CODE.getCode().byteValue());
            ruleQuery.setType(RuleTypeEnum.ISV_ACTION_ROUTE_STRATEGY.getCode().byteValue());
            ruleQuery.setEnv(EnvUtils.getEnvironment());
            List<RuleDO> ruleList = ruleService.selectByCondition(ruleQuery);
            if (!CollectionUtils.isEmpty(ruleList)) {
                PlatformLogUtil.logFail("创建服务商路由策略，策略已存在", LogListUtil.newArrayList(isvRouteStrategyDTO));
                return TripSCRMResult.fail(TripSCRMErrorCode.ISV_ROUTE_STRATEGY_IS_EXIST.getCode(), TripSCRMErrorCode.ISV_ROUTE_STRATEGY_IS_EXIST.getDescCn());
            }
            RuleDO ruleDO = convert2RuleDO(isvRouteStrategyDTO);
            return TripSCRMResult.success(ruleService.create(ruleDO) > 0);
        } catch (Exception e) {
            PlatformLogUtil.logException("创建服务商路由策略，执行异常", e.getMessage(), e, LogListUtil.newArrayList(isvRouteStrategyDTO));
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }
    }

    @Override
    public TripSCRMResult<Boolean> updateIsvRouteStrategy(IsvRouteStrategyDTO isvRouteStrategyDTO) {
        if (Objects.isNull(isvRouteStrategyDTO)
                || Objects.isNull(isvRouteStrategyDTO.getActionEnum())
                || CollectionUtils.isEmpty(isvRouteStrategyDTO.getIsvRoutePercentMap())
                || CollectionUtils.isEmpty(isvRouteStrategyDTO.getRobotTypeRoutePercentMap())
        ) {
            PlatformLogUtil.logFail("更新服务商路由策略，参数非法", LogListUtil.newArrayList(isvRouteStrategyDTO));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        try {
            RuleQuery ruleQuery = new RuleQuery();
            ruleQuery.setTargetId(isvRouteStrategyDTO.getActionEnum().getActionCode());
            ruleQuery.setTargetType(ActivityTargetTypeEnum.ACTION_CODE.getCode().byteValue());
            ruleQuery.setType(RuleTypeEnum.ISV_ACTION_ROUTE_STRATEGY.getCode().byteValue());
            ruleQuery.setEnv(EnvUtils.getEnvironment());
            List<RuleDO> ruleList = ruleService.selectByCondition(ruleQuery);
            if (CollectionUtils.isEmpty(ruleList)) {
                PlatformLogUtil.logFail("更新服务商路由策略，策略不存在", LogListUtil.newArrayList(isvRouteStrategyDTO));
                return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
            }

            RuleDO ruleDO = convert2RuleDO(isvRouteStrategyDTO);
            ruleDO.setId(ruleList.get(0).getId());
            return TripSCRMResult.success(ruleService.update(ruleDO, false) > 0);
        } catch (Exception e) {
            PlatformLogUtil.logException("更新服务商路由策略，执行异常", e.getMessage(), e, LogListUtil.newArrayList(isvRouteStrategyDTO));
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }
    }

    @Override
    public TripSCRMResult<GroupMemberAllocationStrategyDTO> getGroupMemberAllocationStrategy() {
        RuleQuery ruleQuery = new RuleQuery();
        ruleQuery.setType(RuleTypeEnum.GROUP_MEMBER_ALLOCATION_STRATEGY.getCode().byteValue());
        ruleQuery.setEnv(EnvUtils.getEnvironment());
        List<RuleDO> ruleList = ruleService.selectByCondition(ruleQuery);
        if (CollectionUtils.isEmpty(ruleList)) {
            PlatformLogUtil.logFail("查询群成员分配策略，策略不存在", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }

        GroupMemberAllocationStrategyDTO groupMemberAllocationStrategyDTO = convert2GroupMemberAllocationStrategyDTO(ruleList.get(0));
        return TripSCRMResult.success(groupMemberAllocationStrategyDTO);
    }

    @Override
    public TripSCRMResult<Boolean> updateGroupMemberAllocationStrategy(GroupMemberAllocationStrategyDTO groupMemberAllocationStrategyDTO) {
        if (Objects.isNull(groupMemberAllocationStrategyDTO)
                || CollectionUtils.isEmpty(groupMemberAllocationStrategyDTO.getIsvRoutePercentMap())
                || CollectionUtils.isEmpty(groupMemberAllocationStrategyDTO.getRobotTypeRoutePercentMap())
        ) {
            PlatformLogUtil.logFail("更新群成员分配策略，参数非法", LogListUtil.newArrayList(groupMemberAllocationStrategyDTO));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        try {
            RuleQuery ruleQuery = new RuleQuery();
            ruleQuery.setType(RuleTypeEnum.GROUP_MEMBER_ALLOCATION_STRATEGY.getCode().byteValue());
            ruleQuery.setEnv(EnvUtils.getEnvironment());
            List<RuleDO> ruleList = ruleService.selectByCondition(ruleQuery);
            if (CollectionUtils.isEmpty(ruleList)) {
                PlatformLogUtil.logFail("更新群成员分配策略，策略不存在", LogListUtil.newArrayList(groupMemberAllocationStrategyDTO));
                return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
            }

            RuleDO ruleDO = convert2RuleDO(groupMemberAllocationStrategyDTO);
            ruleDO.setId(ruleList.get(0).getId());
            return TripSCRMResult.success(ruleService.update(ruleDO, false) > 0);
        } catch (Exception e) {
            PlatformLogUtil.logException("更新群成员分配策略，执行异常", e.getMessage(), e, LogListUtil.newArrayList(groupMemberAllocationStrategyDTO));
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }
    }

    @AteyeInvoker(description = "初始化群成员分配策略")
    public TripSCRMResult<Boolean> initGroupMemberAllocationStrategy() {
        try {
            Map<IsvTypeEnum, Integer> isvRoutePercentMap = new HashMap<>();
            isvRoutePercentMap.put(IsvTypeEnum.BI_LIN, 50);
            isvRoutePercentMap.put(IsvTypeEnum.BAI_YE, 50);
            Map<RobotTypeEnum, Integer> robotTypeRoutePercentMap = new HashMap<>();
            robotTypeRoutePercentMap.put(RobotTypeEnum.ASSISTANT, 50);
            robotTypeRoutePercentMap.put(RobotTypeEnum.EMPLOYEE, 50);
            List<IsvTypeEnum> isvBlackList = new ArrayList<>();
            List<RobotTypeEnum> robotTypeBlackList = new ArrayList<>();

            GroupMemberAllocationStrategyDTO groupMemberAllocationStrategyDTO = new GroupMemberAllocationStrategyDTO();
            groupMemberAllocationStrategyDTO.setIsvRoutePercentMap(isvRoutePercentMap);
            groupMemberAllocationStrategyDTO.setRobotTypeRoutePercentMap(robotTypeRoutePercentMap);
            groupMemberAllocationStrategyDTO.setIsvBlackList(isvBlackList);
            groupMemberAllocationStrategyDTO.setRobotTypeBlackList(robotTypeBlackList);
            RuleDO ruleDO = convert2RuleDO(groupMemberAllocationStrategyDTO);
            return TripSCRMResult.success(ruleService.create(ruleDO) > 0);
        } catch (Exception e) {
            PlatformLogUtil.logException("初始化群成员分配策略，执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }
    }

    @AteyeInvoker(description = "根据行动项进行路由", paraDesc = "riskActionCode&externalUserId&chatId&userIdListStr&spaceId")
    public TripSCRMResult<WechatUserDTO> matchWechatUser(String riskActionCode, String externalUserId, String chatId, String userIdListStr, Long spaceId) {
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setRiskActionEnum(RiskActionEnum.parse(riskActionCode));
        isvRouteContext.setExternalUserId(externalUserId);
        isvRouteContext.setChatId(chatId);
        if (Objects.nonNull(userIdListStr)) {
            isvRouteContext.setUserIdList(Arrays.stream(userIdListStr.split(",")).filter(StringUtils::hasText).collect(Collectors.toList()));
        }
        isvRouteContext.setSpaceId(spaceId);
        return matchWechatUser(isvRouteContext);
    }

    @Override
    public TripSCRMResult<WechatUserDTO> matchWechatUser(IsvRouteContext isvRouteContext) {
        return AbstractActionRouteStrategy.route(isvRouteContext);
    }

    @AteyeInvoker(description = "群成员是否符合分配策略", paraDesc = "ownerUserId&adminUserIdListStr&spaceId")
    public TripSCRMResult<Boolean> isMatchGroupMemberAllocationStrategy(String ownerUserId, String adminUserIdListStr, Long spaceId) {
        GroupMemberAllocationDTO groupMemberAllocationDTO = new GroupMemberAllocationDTO();
        List<String> adminUserIdList = Arrays.stream(adminUserIdListStr.split(",")).collect(Collectors.toList());
        groupMemberAllocationDTO.setOwner(wechatUserService.listById(Lists.newArrayList(ownerUserId)).get(0));
        groupMemberAllocationDTO.setAdminList(wechatUserService.listById(adminUserIdList));
        return isMatchGroupMemberAllocationStrategy(groupMemberAllocationDTO, spaceId);
    }

    @Override
    public TripSCRMResult<Boolean> isMatchGroupMemberAllocationStrategy(GroupMemberAllocationDTO groupMemberAllocationDTO, Long spaceId) {
        if (Objects.isNull(groupMemberAllocationDTO)
                || CollectionUtils.isEmpty(groupMemberAllocationDTO.getAdminList())
                || Objects.isNull(groupMemberAllocationDTO.getOwner())
                || !NumberUtils.validLong(spaceId)
        ) {
            PlatformLogUtil.logFail("判断是否匹配群成员分配策略，参数非法", LogListUtil.newArrayList(groupMemberAllocationDTO, spaceId));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TripSCRMResult<GroupMemberAllocationStrategyDTO> groupMemberAllocationStrategy = getGroupMemberAllocationStrategy();
        if (Objects.isNull(groupMemberAllocationStrategy) || !groupMemberAllocationStrategy.isSuccess() || Objects.isNull(groupMemberAllocationStrategy.getData())) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，查询群成员分配策略失败", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }

        GroupMemberAllocationStrategyDTO groupMemberAllocationStrategyDTO = groupMemberAllocationStrategy.getData();
        String corpId = spaceService.getCorpIdBySpaceId(spaceId);
        String ownerUserId = groupMemberAllocationDTO.getOwner().getUserId();
        List<String> adminUserIdList = groupMemberAllocationDTO.getAdminList().stream().map(WechatUserDTO::getUserId).collect(Collectors.toList());
        List<String> userIdList = Stream.concat(adminUserIdList.stream(), Stream.of(ownerUserId)).collect(Collectors.toList());

        Map<Integer, Map<Integer, List<WechatUserDTO>>> isvType2RobotType2OnlineUserList = getIsvType2RobotType2ValidOnlineUserList(userIdList, corpId);
        if (CollectionUtils.isEmpty(isvType2RobotType2OnlineUserList)) {
            PlatformLogUtil.logFail("判断是否匹配群成员分配策略，群主和管理员中无在线可用机器人", LogListUtil.newArrayList(userIdList, corpId));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_ONLINE_OWNER_OR_ADMIN_IN_GROUP);
        }

        // 过滤ISV黑名单
        boolean matchIsvBlack = Optional.ofNullable(groupMemberAllocationStrategyDTO.getIsvBlackList()).orElse(new ArrayList<>())
                .stream().anyMatch(isvType -> isvType2RobotType2OnlineUserList.containsKey(isvType.getCode()));
        if (matchIsvBlack) {
            PlatformLogUtil.logFail("判断是否匹配群成员分配策略，命中服务商黑名单", LogListUtil.newArrayList(userIdList, corpId));
            return TripSCRMResult.fail(TripSCRMErrorCode.HIT_ISV_BLACK_LIST);
        }

        // 过滤账号类型黑名单
        Set<Integer> robotTypeSet = isvType2RobotType2OnlineUserList.values().stream().flatMap(map -> map.keySet().stream()).collect(Collectors.toSet());
        boolean matchRobotTypeBlack = Optional.ofNullable(groupMemberAllocationStrategyDTO.getRobotTypeBlackList()).orElse(new ArrayList<>())
                .stream().anyMatch(robotType -> robotTypeSet.contains(robotType.getCode()));
        if (matchRobotTypeBlack) {
            PlatformLogUtil.logFail("判断是否匹配群成员分配策略，命中账号类型黑名单", LogListUtil.newArrayList(userIdList, corpId));
            return TripSCRMResult.fail(TripSCRMErrorCode.HIT_ROBOT_TYPE_BLACK_LIST);
        }

        List<Integer> isvTypeBlackList = Optional.ofNullable(groupMemberAllocationStrategyDTO.getIsvBlackList()).orElse(new ArrayList<>()).stream().map(IsvTypeEnum::getCode).collect(Collectors.toList());
        return TripSCRMResult.success(SwitchConfig.ROBOT_ISV_LIST.stream().allMatch(x -> isvTypeBlackList.contains(x) || isvType2RobotType2OnlineUserList.containsKey(x)));
    }

    @AteyeInvoker(description = "对群成员进行分配", paraDesc = "userIdListStr&spaceId&count")
    public TripSCRMResult<GroupMemberAllocationDTO> getGroupAllocationWechatUserList(String userIdListStr, Long spaceId, Integer count) {
        List<String> userIdList = Arrays.stream(userIdListStr.split(",")).collect(Collectors.toList());
        return getGroupAllocationWechatUserList(userIdList, spaceId, count);
    }

    @Override
    public TripSCRMResult<GroupMemberAllocationDTO> getGroupAllocationWechatUserList(List<String> userIdList, Long spaceId, Integer count) {
        if (CollectionUtils.isEmpty(userIdList) || !NumberUtils.validLong(spaceId) || Optional.ofNullable(count).orElse(0) < 1) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，参数非法", LogListUtil.newArrayList(userIdList, spaceId, count));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        // 查询群成员分配策略
        TripSCRMResult<GroupMemberAllocationStrategyDTO> groupMemberAllocationStrategy = getGroupMemberAllocationStrategy();
        if (Objects.isNull(groupMemberAllocationStrategy) || !groupMemberAllocationStrategy.isSuccess() || Objects.isNull(groupMemberAllocationStrategy.getData())) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，查询群成员分配策略失败", LogListUtil.newArrayList(userIdList, spaceId));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
        String corpId = spaceService.getCorpIdBySpaceId(spaceId);
        // 查询机器人信息
        Map<Integer, Map<Integer, List<WechatUserDTO>>> isvType2RobotType2OnlineUserList = getIsvType2RobotType2ValidOnlineUserList(userIdList, corpId);
        if (CollectionUtils.isEmpty(isvType2RobotType2OnlineUserList)) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，无在线可用机器人", LogListUtil.newArrayList(userIdList, spaceId));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }
        GroupMemberAllocationStrategyDTO groupMemberAllocationStrategyDTO = groupMemberAllocationStrategy.getData();
        // 过滤ISV黑名单
        List<IsvTypeEnum> isvTypeBlackList = Optional.ofNullable(groupMemberAllocationStrategyDTO.getIsvBlackList()).orElse(new ArrayList<>());
        isvTypeBlackList.forEach(isvType -> isvType2RobotType2OnlineUserList.remove(isvType.getCode()));
        if (CollectionUtils.isEmpty(isvType2RobotType2OnlineUserList)) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，过滤服务商黑名单后无在线企微成员", LogListUtil.newArrayList(userIdList, spaceId));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }
        // 过滤账号类型黑名单
        List<RobotTypeEnum> robotTypeBlackList = Optional.ofNullable(groupMemberAllocationStrategyDTO.getRobotTypeBlackList()).orElse(new ArrayList<>());
        // 过滤账号类型黑名单后空的ISV类型
        List<Integer> emptyIsvType = new ArrayList<>();
        for (Integer isvType : isvType2RobotType2OnlineUserList.keySet()) {
            robotTypeBlackList.forEach(robotType -> isvType2RobotType2OnlineUserList.get(isvType).remove(robotType.getCode()));
            if (CollectionUtils.isEmpty(isvType2RobotType2OnlineUserList.get(isvType))) {
                emptyIsvType.add(isvType);
            }
        }
        // 移除空ISV类型
        emptyIsvType.forEach(isvType2RobotType2OnlineUserList::remove);
        if (CollectionUtils.isEmpty(isvType2RobotType2OnlineUserList)) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，过滤账号类型黑名单后无在线企微成员", LogListUtil.newArrayList(userIdList, spaceId));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        // 获取服务商类型分流的比例（过滤黑名单）
        Map<Integer, Integer> isvTypeRoutePercentMap = getIsvTypeRoutePercentMap(groupMemberAllocationStrategyDTO.getIsvRoutePercentMap(), groupMemberAllocationStrategyDTO.getIsvBlackList());
        if (CollectionUtils.isEmpty(isvTypeRoutePercentMap)) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，当前配置无法进行服务商类型分流", LogListUtil.newArrayList(userIdList, spaceId, groupMemberAllocationStrategyDTO));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
        // 获取账号类型分流的比例（过滤黑名单）
        Map<Integer, Integer> robotType2RoutePercent = getRobotTypeRoutePercentMap(groupMemberAllocationStrategyDTO.getRobotTypeRoutePercentMap(), robotTypeBlackList);
        if (CollectionUtils.isEmpty(robotType2RoutePercent)) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，当前配置无法进行账号类型分流", LogListUtil.newArrayList(userIdList, spaceId, groupMemberAllocationStrategyDTO));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }

        List<WechatUserDTO> ownerOrAdminList = new ArrayList<>();
        List<WechatUserDTO> memberList = new ArrayList<>();

        // 先对每个服务商随机选取一个账号，并作为群主或管理员，如果某一服务商可用账号不足，则不选取
        for (int i = 0; i < SwitchConfig.ROBOT_ISV_LIST.size() && ownerOrAdminList.size() < Math.min(SwitchConfig.MAX_GROUP_ADMIN_COUNT + 1, count); i++) {
            if (isvTypeBlackList.contains(IsvTypeEnum.valueOf(SwitchConfig.ROBOT_ISV_LIST.get(i)))) {
                continue;
            }
            WechatUserDTO wechatUserDTO = routeAndRemove(isvType2RobotType2OnlineUserList, IsvTypeEnum.valueOf(SwitchConfig.ROBOT_ISV_LIST.get(i)), robotType2RoutePercent);
            if (Objects.isNull(wechatUserDTO)) {
                PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，当前服务商无可用账号", LogListUtil.newArrayList(userIdList, spaceId, groupMemberAllocationStrategyDTO, IsvTypeEnum.valueOf(SwitchConfig.ROBOT_ISV_LIST.get(i))));
                continue;
            }
            ownerOrAdminList.add(wechatUserDTO);
        }

        // 剩余的账号根据服务商分流策略分配
        while (ownerOrAdminList.size() + memberList.size() < count) {
            IsvTypeEnum isvTypeEnum = isvTypeRoute(isvTypeRoutePercentMap, isvType2RobotType2OnlineUserList);
            if (Objects.isNull(isvTypeEnum)) {
                PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，随机分配服务商类型失败", LogListUtil.newArrayList(userIdList, spaceId, groupMemberAllocationStrategyDTO));
                return TripSCRMResult.fail(TripSCRMErrorCode.INSUFFICIENT_WECHAT_USER);
            }
            WechatUserDTO wechatUserDTO = routeAndRemove(isvType2RobotType2OnlineUserList, isvTypeEnum, robotType2RoutePercent);
            if (Objects.isNull(wechatUserDTO)) {
                PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，当前服务商无可用账号", LogListUtil.newArrayList(userIdList, spaceId, groupMemberAllocationStrategyDTO, isvTypeEnum));
                return TripSCRMResult.fail(TripSCRMErrorCode.INSUFFICIENT_WECHAT_USER);
            }
            if (ownerOrAdminList.size() < SwitchConfig.MAX_GROUP_ADMIN_COUNT + 1) {
                ownerOrAdminList.add(wechatUserDTO);
            } else {
                memberList.add(wechatUserDTO);
            }
        }

        GroupMemberAllocationDTO groupMemberAllocationDTO = new GroupMemberAllocationDTO();
        groupMemberAllocationDTO.setOwner(ownerOrAdminList.remove(ThreadLocalRandom.current().nextInt(ownerOrAdminList.size())));
        groupMemberAllocationDTO.setAdminList(ownerOrAdminList);
        groupMemberAllocationDTO.setMemberList(memberList);
        PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，结果", LogListUtil.newArrayList(userIdList, corpId, groupMemberAllocationStrategyDTO, groupMemberAllocationDTO));
        return TripSCRMResult.success(groupMemberAllocationDTO);
    }

    private Map<Integer, Map<Integer, List<WechatUserDTO>>> getIsvType2RobotType2ValidOnlineUserList(List<String> userIdList, String corpId) {
        WechatUserQuery query = new WechatUserQuery();
        query.setUserIdList(userIdList);
        query.setCorpId(corpId);
        query.setQueryRobotInfo(true);
        List<WechatUserDTO> wechatUserList = wechatUserService.listValidWechatUserByCondition(query);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return new HashMap<>();
        }

        Map<Integer, Map<Integer, List<WechatUserDTO>>> isvType2RobotType2OnlineUserList = new HashMap<>();
        for (WechatUserDTO wechatUserDTO : wechatUserList) {
            if (CollectionUtils.isEmpty(wechatUserDTO.getRobotList())) {
                continue;
            }

            RobotDTO onlineRobotInfo = wechatUserDTO.getRobotList().stream().filter(robot -> Objects.equals(robot.getRobotStatus(), RobotStatusEnum.ONLINE.getCode())).findFirst().orElse(null);
            if (Objects.isNull(onlineRobotInfo)) {
                continue;
            }
            isvType2RobotType2OnlineUserList.putIfAbsent(onlineRobotInfo.getIsvType(), new HashMap<>());
            isvType2RobotType2OnlineUserList.get(onlineRobotInfo.getIsvType()).putIfAbsent(wechatUserDTO.getRobotType(), new ArrayList<>());
            isvType2RobotType2OnlineUserList.get(onlineRobotInfo.getIsvType()).get(wechatUserDTO.getRobotType()).add(wechatUserDTO);
        }

        return isvType2RobotType2OnlineUserList;
    }

    private WechatUserDTO routeAndRemove(Map<Integer, Map<Integer, List<WechatUserDTO>>> isvType2RobotType2OnlineUserList, IsvTypeEnum isvTypeEnum, Map<Integer, Integer> robotType2RoutePercent) {
        Map<Integer, List<WechatUserDTO>> robotType2OnlineUserList = isvType2RobotType2OnlineUserList.get(isvTypeEnum.getCode());
        if (CollectionUtils.isEmpty(robotType2OnlineUserList)) {
            return null;
        }

        // 根据分流策略获取选取的账号类型
        RobotTypeEnum robotTypeEnum = robotTypeRoute(robotType2RoutePercent, robotType2OnlineUserList);
        if (Objects.isNull(robotTypeEnum)) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，随机分配账号类型失败", LogListUtil.newArrayList(robotType2RoutePercent));
            return null;
        }

        // 如果当前选取的账号类型无可用账号，选取其他类型账号作为兜底
        if (CollectionUtils.isEmpty(robotType2OnlineUserList.get(robotTypeEnum.getCode()))) {
            PlatformLogUtil.logInfo("根据传入的企微号UserId，对群成员账号进行分配，随机分配的账号类型下无可用账号，选取其他类型账号作为兜底", LogListUtil.newArrayList(isvType2RobotType2OnlineUserList, isvTypeEnum, robotTypeEnum));
            robotTypeEnum = Objects.equals(robotTypeEnum, RobotTypeEnum.ASSISTANT) ? RobotTypeEnum.EMPLOYEE : RobotTypeEnum.ASSISTANT;
        }

        // 其他类型账号列表也为空
        List<WechatUserDTO> onlineWechatUserList = robotType2OnlineUserList.get(robotTypeEnum.getCode());
        if (CollectionUtils.isEmpty(onlineWechatUserList)) {
            return null;
        }

        // 从可用的账号中随机选取一个，并移除
        return onlineWechatUserList.remove(ThreadLocalRandom.current().nextInt(onlineWechatUserList.size()));
    }

    private IsvTypeEnum isvTypeRoute(Map<Integer, Integer> isvTypeRoutePercentMap, Map<Integer, Map<Integer, List<WechatUserDTO>>> isvType2RobotType2OnlineUserList) {
        List<IsvTypeEnum> validIsvTypeList = new ArrayList<>();
        // 计算所有有可用账号的服务商的总权重
        int totalCount = 0;
        for (Integer isvType : SwitchConfig.ROBOT_ISV_LIST) {
            IsvTypeEnum isvTypeEnum = IsvTypeEnum.valueOf(isvType);
            if (CollectionUtils.isEmpty(isvType2RobotType2OnlineUserList.get(isvTypeEnum.getCode()))) {
                continue;
            }
            if (isvType2RobotType2OnlineUserList.get(isvTypeEnum.getCode()).values().stream().mapToLong(List::size).sum() == 0) {
                continue;
            }
            validIsvTypeList.add(isvTypeEnum);
            totalCount += isvTypeRoutePercentMap.get(isvTypeEnum.getCode());
        }

        if (CollectionUtils.isEmpty(validIsvTypeList)) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，没有可用企微号", LogListUtil.newArrayList(isvTypeRoutePercentMap, isvType2RobotType2OnlineUserList));
            return null;
        }

        if (totalCount == 0) {
            // 可用账号所属的ISV分流比例都为0，那就从没加黑的里面兜底选
            PlatformLogUtil.logInfo("根据传入的企微号UserId，对群成员账号进行分配，有效企微号所属服务商分流比例都为0，那就只能从这里面兜底选择了", LogListUtil.newArrayList(isvTypeRoutePercentMap, isvType2RobotType2OnlineUserList));
            return validIsvTypeList.get(ThreadLocalRandom.current().nextInt(validIsvTypeList.size()));
        }

        int randomValue = ThreadLocalRandom.current().nextInt(totalCount);
        int currentSum = 0;

        for (IsvTypeEnum isvType : validIsvTypeList) {
            currentSum += isvTypeRoutePercentMap.getOrDefault(isvType.getCode(), 0);
            if (currentSum > randomValue) {
                return isvType;
            }
        }
        return null;
    }

    private RobotTypeEnum robotTypeRoute(Map<Integer, Integer> robotTypeRoutePercentMap, Map<Integer, List<WechatUserDTO>> robotType2OnlineUserList) {
        int totalCount = robotTypeRoutePercentMap.values().stream().mapToInt(x -> x).sum();
        int randomValue = ThreadLocalRandom.current().nextInt(totalCount);
        int currentSum = 0;

        RobotTypeEnum result = null;

        for (RobotTypeEnum robotTypeEnum : ROBOT_TYPE_LIST) {
            currentSum += robotTypeRoutePercentMap.getOrDefault(robotTypeEnum.getCode(), 0);
            if (currentSum > randomValue) {
                result = robotTypeEnum;
                break;
            }
        }
        if (Objects.isNull(result)) {
            return result;
        }

        // 如果当前选取的账号类型无可用账号，选取其他类型账号作为兜底
        if (CollectionUtils.isEmpty(robotType2OnlineUserList.get(result.getCode()))) {
            PlatformLogUtil.logInfo("根据传入的企微号UserId，对群成员账号进行分配，随机分配的账号类型下无可用账号，选取其他类型账号作为兜底", LogListUtil.newArrayList(robotTypeRoutePercentMap, robotType2OnlineUserList, result));
            result = Objects.equals(result, RobotTypeEnum.ASSISTANT) ? RobotTypeEnum.EMPLOYEE : RobotTypeEnum.ASSISTANT;
        }

        if (CollectionUtils.isEmpty(robotType2OnlineUserList.get(result.getCode()))) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，随机分配的账号类型下无可用账号", LogListUtil.newArrayList(robotTypeRoutePercentMap, robotType2OnlineUserList));
            return null;
        }
        return result;
    }

    /**
     * 获取服务商类型分流的比例（过滤黑名单）
     */
    private Map<Integer, Integer> getIsvTypeRoutePercentMap(Map<IsvTypeEnum, Integer> isvTypeRoutePercentMap, List<IsvTypeEnum> isvTypeBlackList) {
        Map<Integer, Integer> isvType2RoutePercent = new HashMap<>();
        for (IsvTypeEnum isvType : Optional.ofNullable(isvTypeRoutePercentMap).orElse(new HashMap<>()).keySet()) {
            if (isvTypeBlackList.contains(isvType)) {
                continue;
            }

            isvType2RoutePercent.put(isvType.getCode(), isvTypeRoutePercentMap.get(isvType));
        }

        return isvType2RoutePercent;
    }

    /**
     * 获取账号类型分流的比例（过滤黑名单）
     */
    private Map<Integer, Integer> getRobotTypeRoutePercentMap(Map<RobotTypeEnum, Integer> robotTypeRoutePercentMap, List<RobotTypeEnum> robotTypeBlackList) {
        Map<Integer, Integer> robotType2RoutePercent = new HashMap<>();
        for (RobotTypeEnum robotType : Optional.ofNullable(robotTypeRoutePercentMap).orElse(new HashMap<>()).keySet()) {
            if (robotTypeBlackList.contains(robotType)) {
                continue;
            }

            robotType2RoutePercent.put(robotType.getCode(), robotTypeRoutePercentMap.get(robotType));
        }
        return robotType2RoutePercent;
    }

    private RuleDO convert2RuleDO(IsvRouteStrategyDTO isvRouteStrategyDTO) {
        if (Objects.isNull(isvRouteStrategyDTO)) {
            return null;
        }

        RuleDO ruleDO = new RuleDO();
        ruleDO.setType(RuleTypeEnum.ISV_ACTION_ROUTE_STRATEGY.getCode().byteValue());
        ruleDO.setTargetId(isvRouteStrategyDTO.getActionEnum().getActionCode());
        ruleDO.setTargetType(ActivityTargetTypeEnum.ACTION_CODE.getCode().byteValue());
        ruleDO.setEnv(EnvUtils.getEnvironment());
        ruleDO.setName("【" + EnvUtils.getEnvDesc() + "】" + isvRouteStrategyDTO.getActionEnum().getName());

        JSONObject jsonConfig = new JSONObject();
        Map<IsvTypeEnum, Integer> isvTypeRoutePercentMap = Optional.ofNullable(isvRouteStrategyDTO.getIsvRoutePercentMap()).orElse(new HashMap<>());
        Map<Integer, Integer> isvRoutePercentMap = isvTypeRoutePercentMap.keySet().stream().collect(Collectors.toMap(IsvTypeEnum::getCode, isvTypeRoutePercentMap::get));
        jsonConfig.put(RuleConstant.ISV_ROUTE_PERCENT_MAP, isvRoutePercentMap);


        List<IsvTypeEnum> isvTypeBlackList = Optional.ofNullable(isvRouteStrategyDTO.getIsvBlackList()).orElse(new ArrayList<>());
        List<Integer> isvBlackList = isvTypeBlackList.stream().map(IsvTypeEnum::getCode).collect(Collectors.toList());
        jsonConfig.put(RuleConstant.ISV_BLACK_LIST, isvBlackList);

        Map<RobotTypeEnum, Integer> robotTypeEnumRoutePercentMap = Optional.ofNullable(isvRouteStrategyDTO.getRobotTypeRoutePercentMap()).orElse(new HashMap<>());
        Map<Integer, Integer> robotTypeRoutePercentMap = robotTypeEnumRoutePercentMap.keySet().stream().collect(Collectors.toMap(RobotTypeEnum::getCode, robotTypeEnumRoutePercentMap::get));
        jsonConfig.put(RuleConstant.ROBOT_TYPE_ROUTE_PERCENT_MAP, robotTypeRoutePercentMap);

        ruleDO.setConfig(jsonConfig.toJSONString());
        ruleDO.setPriority(-1);
        ruleDO.setGroupId(-1L);
        ruleDO.setDynamicConfig("{}");
        return ruleDO;
    }

    private RuleDO convert2RuleDO(GroupMemberAllocationStrategyDTO groupMemberAllocationStrategyDTO) {
        if (Objects.isNull(groupMemberAllocationStrategyDTO)) {
            return null;
        }

        RuleDO ruleDO = new RuleDO();
        ruleDO.setType(RuleTypeEnum.GROUP_MEMBER_ALLOCATION_STRATEGY.getCode().byteValue());
        ruleDO.setName("【" + EnvUtils.getEnvDesc() + "】" + RuleTypeEnum.GROUP_MEMBER_ALLOCATION_STRATEGY.getDesc());
        ruleDO.setEnv(EnvUtils.getEnvironment());

        JSONObject jsonConfig = new JSONObject();
        Map<IsvTypeEnum, Integer> isvTypeRoutePercentMap = Optional.ofNullable(groupMemberAllocationStrategyDTO.getIsvRoutePercentMap()).orElse(new HashMap<>());
        Map<Integer, Integer> isvRoutePercentMap = isvTypeRoutePercentMap.keySet().stream().collect(Collectors.toMap(IsvTypeEnum::getCode, isvTypeRoutePercentMap::get));
        jsonConfig.put(RuleConstant.ISV_ROUTE_PERCENT_MAP, isvRoutePercentMap);


        List<IsvTypeEnum> isvTypeBlackList = Optional.ofNullable(groupMemberAllocationStrategyDTO.getIsvBlackList()).orElse(new ArrayList<>());
        List<Integer> isvBlackList = isvTypeBlackList.stream().map(IsvTypeEnum::getCode).collect(Collectors.toList());
        jsonConfig.put(RuleConstant.ISV_BLACK_LIST, isvBlackList);

        List<RobotTypeEnum> robotTypeEnumBlackList = Optional.ofNullable(groupMemberAllocationStrategyDTO.getRobotTypeBlackList()).orElse(new ArrayList<>());
        List<Integer> robotTypeBlackList = robotTypeEnumBlackList.stream().map(RobotTypeEnum::getCode).collect(Collectors.toList());
        jsonConfig.put(RuleConstant.ROBOT_TYPE_BLACK_LIST, robotTypeBlackList);

        Map<RobotTypeEnum, Integer> robotTypeEnumRoutePercentMap = Optional.ofNullable(groupMemberAllocationStrategyDTO.getRobotTypeRoutePercentMap()).orElse(new HashMap<>());
        Map<Integer, Integer> robotTypeRoutePercentMap = robotTypeEnumRoutePercentMap.keySet().stream().collect(Collectors.toMap(RobotTypeEnum::getCode, robotTypeEnumRoutePercentMap::get));
        jsonConfig.put(RuleConstant.ROBOT_TYPE_ROUTE_PERCENT_MAP, robotTypeRoutePercentMap);
        ruleDO.setConfig(jsonConfig.toJSONString());
        ruleDO.setPriority(-1);
        ruleDO.setGroupId(-1L);
        ruleDO.setDynamicConfig("{}");
        return ruleDO;
    }

    private IsvRouteStrategyDTO convert2IsvRouteStrategyDTO(RuleDO ruleDO) {
        if (Objects.isNull(ruleDO)) {
            return null;
        }

        IsvRouteStrategyDTO isvRouteStrategyDTO = new IsvRouteStrategyDTO();
        isvRouteStrategyDTO.setActionEnum(RiskActionEnum.parse(ruleDO.getTargetId()));
        isvRouteStrategyDTO.setName(ruleDO.getName());
        JSONObject jsonConfig = JSONObject.parseObject(ruleDO.getConfig());

        Map<Integer, Integer> isvRoutePercentMap = jsonConfig.getObject(RuleConstant.ISV_ROUTE_PERCENT_MAP, new TypeReference<HashMap<Integer, Integer>>() {
        });
        Map<IsvTypeEnum, Integer> isvType2RoutePercentMap = isvRoutePercentMap.keySet().stream().collect(Collectors.toMap(IsvTypeEnum::valueOf, isvRoutePercentMap::get));
        isvRouteStrategyDTO.setIsvRoutePercentMap(isvType2RoutePercentMap);

        if (jsonConfig.containsKey(RuleConstant.ISV_BLACK_LIST)) {
            List<Integer> isvBlackList = jsonConfig.getObject(RuleConstant.ISV_BLACK_LIST, new TypeReference<ArrayList<Integer>>() {
            });
            List<IsvTypeEnum> isvTypeBlackList = isvBlackList.stream().map(IsvTypeEnum::valueOf).collect(Collectors.toList());
            isvRouteStrategyDTO.setIsvBlackList(isvTypeBlackList);
        }

        Map<Integer, Integer> robotTypeRoutePercentMap = jsonConfig.getObject(RuleConstant.ROBOT_TYPE_ROUTE_PERCENT_MAP, new TypeReference<HashMap<Integer, Integer>>() {
        });
        Map<RobotTypeEnum, Integer> robotType2RoutePercentMap = robotTypeRoutePercentMap.keySet().stream().collect(Collectors.toMap(RobotTypeEnum::of, robotTypeRoutePercentMap::get));
        isvRouteStrategyDTO.setRobotTypeRoutePercentMap(robotType2RoutePercentMap);
        return isvRouteStrategyDTO;
    }

    private GroupMemberAllocationStrategyDTO convert2GroupMemberAllocationStrategyDTO(RuleDO ruleDO) {
        if (Objects.isNull(ruleDO)) {
            return null;
        }

        GroupMemberAllocationStrategyDTO groupMemberAllocationStrategyDTO = new GroupMemberAllocationStrategyDTO();
        JSONObject jsonConfig = JSONObject.parseObject(ruleDO.getConfig());
        Map<Integer, Integer> isvRoutePercentMap = jsonConfig.getObject(RuleConstant.ISV_ROUTE_PERCENT_MAP, new TypeReference<HashMap<Integer, Integer>>() {
        });
        Map<IsvTypeEnum, Integer> isvType2RoutePercentMap = isvRoutePercentMap.keySet().stream().collect(Collectors.toMap(IsvTypeEnum::valueOf, isvRoutePercentMap::get));
        groupMemberAllocationStrategyDTO.setIsvRoutePercentMap(isvType2RoutePercentMap);

        Map<Integer, Integer> robotTypeRoutePercentMap = jsonConfig.getObject(RuleConstant.ROBOT_TYPE_ROUTE_PERCENT_MAP, new TypeReference<HashMap<Integer, Integer>>() {
        });
        Map<RobotTypeEnum, Integer> robotType2RoutePercentMap = robotTypeRoutePercentMap.keySet().stream().collect(Collectors.toMap(RobotTypeEnum::of, robotTypeRoutePercentMap::get));
        groupMemberAllocationStrategyDTO.setRobotTypeRoutePercentMap(robotType2RoutePercentMap);

        if (jsonConfig.containsKey(RuleConstant.ISV_BLACK_LIST)) {
            List<Integer> isvBlackList = jsonConfig.getObject(RuleConstant.ISV_BLACK_LIST, new TypeReference<ArrayList<Integer>>() {
            });
            List<IsvTypeEnum> isvTypeBlackList = isvBlackList.stream().map(IsvTypeEnum::valueOf).collect(Collectors.toList());
            groupMemberAllocationStrategyDTO.setIsvBlackList(isvTypeBlackList);
        }

        if (jsonConfig.containsKey(RuleConstant.ROBOT_TYPE_BLACK_LIST)) {
            List<Integer> robotTypeBlackList = jsonConfig.getObject(RuleConstant.ROBOT_TYPE_BLACK_LIST, new TypeReference<ArrayList<Integer>>() {
            });
            List<RobotTypeEnum> robotTypeEnumBlackList = robotTypeBlackList.stream().map(RobotTypeEnum::of).collect(Collectors.toList());
            groupMemberAllocationStrategyDTO.setRobotTypeBlackList(robotTypeEnumBlackList);
        }

        groupMemberAllocationStrategyDTO.setName(ruleDO.getName());
        return groupMemberAllocationStrategyDTO;
    }
}
