package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/5 17:09
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class GroupQuery extends BasePageRequest {

    /**
     * 分页
     */
    private boolean page;

    /**
     * 空间Id
     */
    private Long spaceId;

    /**
     * 管理组名称
     */
    private String name;

    /**
     * 管理组类型
     */
    private List<Byte> typeList;

}
