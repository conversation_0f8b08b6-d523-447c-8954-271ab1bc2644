package com.alibaba.tripscrm.service.middleware.notify.consumer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.service.constant.MaterialVariableConstants;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.second.TccpManager;
import com.alibaba.tripscrm.service.middleware.hts.HotelCheckinRemindTaskExecutor;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.service.second.DivisionService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.fliggy.tccp.client.api.model.OrderDTO;
import com.fliggy.tccp.client.api.model.VerticalAttributesDTO;
import com.fliggy.tccp.client.enumerate.BizTypeEnum;
import com.fliggy.tccp.client.enumerate.HotelSubBizTypeEnum;
import com.fliggy.tccp.client.enumerate.ItemGoodsSubBizTypeEnum;
import com.fliggy.tccp.client.enumerate.VerticalAttributesTypeEnum;
import com.fliggy.tccp.client.notify.OrderNotify;
import com.fliggy.tccp.dal.dataobject.VerticalAttributesDO;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import com.taobao.notify.message.Message;
import com.taobao.notify.message.ObjectMessage;
import com.taobao.notify.remotingclient.MessageListener;
import com.taobao.notify.remotingclient.MessageStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/10 16:38
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class OrderNotifyConsumer implements MessageListener {
    private final HotelCheckinRemindTaskExecutor hotelCheckinRemindTaskExecutor;
    private final TccpManager tccpManager;
    private final LdbTairManager ldbTairManager;
    private final DivisionService divisionService;
    private final MetaqProducer metaqProducer;
    private final UicUtils uicUtils;
    private final ResourceRelationService resourceRelationService;

    @Switch(description = "酒店入住提醒开始时间", name = "hotelRemindStartHour")
    private static String hotelRemindBeginTime = "11:00";

    @Switch(description = "酒店入住提醒结束时间", name = "hotelRemindEndHour")
    private static String hotelRemindEndTime = "20:00";

    /**
     * TCCP Notify 统一对外的消息
     */
    public static final String TCCP_NOTIFY_TOPIC_TCCP_TRADE_RESULT = "Tccp-Trade-Result";

    /**
     * TCCP Notify 内部消息
     */
    public static final String TCCP_NOTIFY_TOPIC_TCCP_TRADE = "Tccp-Trade";

    /**
     * 垂直标变更消息
     */
    public static final String TCCP_TRADE_UPDATE_VERTICAL_ATTRIBUTE = "Tccp-Trade_UPDATE_VERTICAL_ATTRIBUTE";

    /**
     * 酒店关单消息
     */
    public static final String TCCP_TRADE_RESULT_HOTEL_CLOSE_ORDER = "Tccp-Trade-Result_HOTEL_CLOSE_ORDER";

    /**
     * 酒店订单创建担保交易消息
     */
    public static final String TCCP_TRADE_RESULT_HOTEL_ENABLE_ORDER = "Tccp-Trade-Result_HOTEL_ENABLE_ORDER";

    /**
     * 交易成功消息
     */
    public static final String TCCP_TRADE_MULTI_RESOURCE_ORDER_CONFIRM_SUCC = "Tccp-Trade_MULTI_RESOURCE_ORDER_CONFIRM_SUCC";

    /**
     * 方法引用map
     */
    public static final Map<String, BiConsumer<OrderNotify, String>> FUNCTION_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        FUNCTION_MAP.put(TCCP_TRADE_UPDATE_VERTICAL_ATTRIBUTE, this::processUpdateVerticalAttribute);
        FUNCTION_MAP.put(TCCP_TRADE_RESULT_HOTEL_CLOSE_ORDER, this::processHotelCloseOrder);
        FUNCTION_MAP.put(TCCP_TRADE_RESULT_HOTEL_ENABLE_ORDER, this::processHotelEnableOrder);
        FUNCTION_MAP.put(TCCP_TRADE_MULTI_RESOURCE_ORDER_CONFIRM_SUCC, this::processMultiResourceOrderConfirmSucc);
    }

    @Override
    public void receiveMessage(Message message, MessageStatus messageStatus) {
        try {
            if (Objects.isNull(message) || !StringUtils.hasText(message.getTopic()) || !StringUtils.hasText(message.getMessageType())) {
                PlatformLogUtil.logFail("Tccp消息消费, 消息内容非法", LogListUtil.newArrayList(message));
                return;
            }

            if (!Lists.newArrayList(TCCP_NOTIFY_TOPIC_TCCP_TRADE_RESULT, TCCP_NOTIFY_TOPIC_TCCP_TRADE).contains(message.getTopic())) {
                PlatformLogUtil.logFail("Tccp消息消费，无法处理的topic", LogListUtil.newArrayList(message));
                return;
            }

            Object notify = Optional.of(message).filter(x -> x instanceof ObjectMessage).map(x -> (ObjectMessage) x).map(ObjectMessage::getObject).orElse(null);
            if (Objects.isNull(notify)) {
                PlatformLogUtil.logFail("Tccp消息消费，消息内容非法", LogListUtil.newArrayList(message));
                return;
            }

            // 只处理正向消息
            if (!(notify instanceof OrderNotify)) {
                PlatformLogUtil.logFail("Tccp消息消费，只处理正向消息", LogListUtil.newArrayList(notify));
                return;
            }

            OrderNotify orderNotify = (OrderNotify) notify;
            if (!NumberUtils.biggerThanZero(orderNotify.getBuyerId()) || !NumberUtils.biggerThanZero(orderNotify.getOrderId())) {
                PlatformLogUtil.logFail("Tccp消息消费，orderId或userId非法", TripSCRMErrorCode.INVALID_PARAMS.getCode(), LogListUtil.newArrayList(orderNotify));
                return;
            }

            String unionId = uicUtils.getUnionIdByUid(String.valueOf(orderNotify.getBuyerId()));
            if (!StringUtils.hasText(unionId)) {
                PlatformLogUtil.logFail("Tccp消息消费，根据uid查询unionId为空", TripSCRMErrorCode.INVALID_PARAMS.getCode(), LogListUtil.newArrayList(orderNotify));
                return;
            }

            String functionKey = String.format("%s_%s", message.getTopic(), message.getMessageType());

            if (!FUNCTION_MAP.containsKey(functionKey)) {
                PlatformLogUtil.logFail("Tccp消息消费，无法处理的消息类型", LogListUtil.newArrayList(message));
                return;
            }

            PlatformLogUtil.logInfo("Tccp消息消费，开始处理消息", LogListUtil.newArrayList(message));
            FUNCTION_MAP.get(functionKey).accept(orderNotify, unionId);
        } catch (Exception e) {
            PlatformLogUtil.logException("Tccp消息消费，出现异常", e.getMessage(), e, LogListUtil.newArrayList(message));
        }
    }

    /**
     * 垂直标变更消息，可以用来生成 酒店预定成功 事件
     * 过滤条件：orderNotify.getBizType为com.fliggy.tccp.client.enumerate.BizTypeEnum#HOTEL
     * 判断确认有房：tccpQueryService.queryOrderByOrderId(orderId, orderDO.getBuyerId(), QueryInfoType.NEW_QUERY_VERTICAL)，取参数时需判断：hotel_confirmed_successed=1
     *
     * @param orderNotify tccp消息
     */
    private void processUpdateVerticalAttribute(OrderNotify orderNotify, String unionId) {
        if (!Objects.equals(BizTypeEnum.HOTEL.getCode(), orderNotify.getBizType())) {
            PlatformLogUtil.logInfo("Tccp垂直标变更消息消费，非酒店行业，不做处理", LogListUtil.newArrayList(orderNotify));
            return;
        }

        OrderDTO orderDTO = tccpManager.queryOrderDTO(orderNotify.getOrderId(), orderNotify.getBuyerId(), true);
        if (Objects.isNull(orderDTO)) {
            PlatformLogUtil.logFail("Tccp垂直标变更消息消费，查询订单失败", LogListUtil.newArrayList(orderNotify));
            return;
        }

        // 这里要用 订单中心同步业务垂直标 判断确认有房状态
        VerticalAttributesDO orderCenterSyncBusinessVerticalAttributesDO = orderDTO.getVerticalAttributesDTOS()
                .stream()
                .map(VerticalAttributesDTO::getVerticalAttributesDO)
                .filter(verticalAttributesDO -> Objects.equals(VerticalAttributesTypeEnum.ORDER_CENTER_SYNC_BUSINESS.getCode(), verticalAttributesDO.getVerticalAttributesType()))
                .findFirst().orElse(null);

        if (Objects.isNull(orderCenterSyncBusinessVerticalAttributesDO) || !Objects.equals(orderCenterSyncBusinessVerticalAttributesDO.getAttribute("hotel_confirmed_successed"), "1")) {
            PlatformLogUtil.logFail("Tccp垂直标变更消息消费，不是确认有房状态", LogListUtil.newArrayList(orderNotify, orderDTO));
            return;
        }

        Integer city = com.alibaba.tripscrm.service.util.system.NumberUtils.toInteger(orderDTO.getOrderDO().getAttribute("common_tp_info_city"), 0);
        Integer hotelArea = com.alibaba.tripscrm.service.util.system.NumberUtils.toInteger(orderCenterSyncBusinessVerticalAttributesDO.getAttribute("hotelArea"), 0);
        if (city > 700000 || hotelArea > 0) {
            PlatformLogUtil.logFail("Tccp垂直标变更消息消费，不是国内酒店的订单", LogListUtil.newArrayList(orderNotify, orderDTO));
            return;
        }

        Date serviceStartTime = orderDTO.getResourcesDTOs().get(0).getResourceDO().getServiceStartTime();
        // 入住时间
        String checkinDateStr = serviceStartTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 入住酒店名称
        String hotelName = orderDTO.getOrderDO().getAttribute("common_tp_info_hotel_name");
        // 酒店地址
        String address = orderDTO.getResourcesDTOs().get(0).getResourceDO().getAddress();
        // 入住人姓名
        String passengerNameList = orderDTO.getOrderPassengerDTOS().stream()
                .map(orderPassengerDTO -> orderPassengerDTO.getOrderPassengerDO().getName())
                .collect(Collectors.joining(","));
        // 入住目的地
        String cityName = orderDTO.getResourcesDTOs().get(0).getResourceDO().getCity();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("unionId", unionId);
        jsonObject.put(MaterialVariableConstants.HOTEL_NAME, hotelName);
        jsonObject.put(MaterialVariableConstants.HOTEL_ADDRESS, address);
        jsonObject.put(MaterialVariableConstants.CHECK_IN_DATE, checkinDateStr);
        jsonObject.put(MaterialVariableConstants.ARR_CITY_NAME, cityName);
        jsonObject.put(MaterialVariableConstants.PASSENGER_NAME, passengerNameList);

        if (ldbTairManager.incr(TairConstant.TCCP_CONSUME_DISTINCT_KEY + TCCP_TRADE_UPDATE_VERTICAL_ATTRIBUTE + "_" + orderNotify.getOrderId(), 1, 0, 120) > 1) {
            PlatformLogUtil.logFail("Tccp垂直标变更消息消费，重复消费，不做处理", LogListUtil.newArrayList(orderNotify));
            return;
        }

        if (queryHotelBookSuccessRecord(unionId, orderNotify.getOrderId())) {
            PlatformLogUtil.logFail("Tccp垂直标变更消息消费，酒店预定成功事件已触发过，不做处理", LogListUtil.newArrayList(orderNotify));
            return;
        }

        recordHotelBookSuccessRecord(unionId, orderNotify.getOrderId());

        metaqProducer.send(MQEnum.WECHAT_CUSTOMER_HOTEL_BOOK_SUCCESS, "", "", jsonObject.toJSONString());
        PlatformLogUtil.logInfo("Tccp垂直标变更消息消费，酒店预定成功事件", LogListUtil.newArrayList(orderNotify, orderDTO, jsonObject));
    }

    /**
     * 酒店关单消息，可以用来生成 酒店未预定成功 事件
     * 判断酒店未预定成功：orderDo.payTime!=空，订单关单，且没有确认有房标（hotel_confirmed_successed!=1）
     *
     * @param orderNotify tccp消息
     */
    private void processHotelCloseOrder(OrderNotify orderNotify, String unionId) {
        OrderDTO orderDTO = tccpManager.queryOrderDTO(orderNotify.getOrderId(), orderNotify.getBuyerId(), true);
        if (Objects.isNull(orderDTO)) {
            PlatformLogUtil.logFail("Tccp酒店关单消息消费，查询订单失败", LogListUtil.newArrayList(orderNotify));
            return;
        }

        // 这里要用 订单中心同步业务垂直标 判断确认有房状态
        VerticalAttributesDO orderCenterSyncBusinessVerticalAttributesDO = orderDTO.getVerticalAttributesDTOS()
                .stream()
                .map(VerticalAttributesDTO::getVerticalAttributesDO)
                .filter(verticalAttributesDO -> Objects.equals(VerticalAttributesTypeEnum.ORDER_CENTER_SYNC_BUSINESS.getCode(), verticalAttributesDO.getVerticalAttributesType()))
                .findFirst().orElse(null);

        if (Objects.isNull(orderCenterSyncBusinessVerticalAttributesDO) || Objects.equals(orderCenterSyncBusinessVerticalAttributesDO.getAttribute("hotel_confirmed_successed"), "1")) {
            PlatformLogUtil.logFail("Tccp酒店关单消息消费，垂直标信息为空或曾确认有房", LogListUtil.newArrayList(orderNotify, orderDTO));
            return;
        }

        if (Objects.isNull(orderDTO.getOrderDO().getPayTime())) {
            PlatformLogUtil.logFail("Tccp酒店关单消息消费，订单未支付", LogListUtil.newArrayList(orderNotify, orderDTO));
            return;
        }

        Integer city = com.alibaba.tripscrm.service.util.system.NumberUtils.toInteger(orderDTO.getOrderDO().getAttribute("common_tp_info_city"), 0);
        Integer hotelArea = com.alibaba.tripscrm.service.util.system.NumberUtils.toInteger(orderCenterSyncBusinessVerticalAttributesDO.getAttribute("hotelArea"), 0);
        if (city > 700000 || hotelArea > 0) {
            PlatformLogUtil.logFail("Tccp酒店关单消息消费，不是国内酒店的订单", LogListUtil.newArrayList(orderNotify, orderDTO));
            return;
        }

        // 入住时间
        Date serviceStartTime = orderDTO.getResourcesDTOs().get(0).getResourceDO().getServiceStartTime();
        // 入住时间
        String checkinDateStr = serviceStartTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 离店时间
        String checkOutDateStr = serviceStartTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .plusDays(1)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 入住酒店名称
        String hotelName = orderDTO.getOrderDO().getAttribute("common_tp_info_hotel_name");
        // 酒店地址
        String address = orderDTO.getResourcesDTOs().get(0).getResourceDO().getAddress();
        // 入住人姓名
        String passengerNameList = orderDTO.getOrderPassengerDTOS().stream()
                .map(orderPassengerDTO -> orderPassengerDTO.getOrderPassengerDO().getName())
                .collect(Collectors.joining(","));
        // 入住目的地
        String cityName = orderDTO.getResourcesDTOs().get(0).getResourceDO().getCity();
        Long divisionId = null;
        try {
            divisionId = Long.parseLong(orderDTO.getOrderDO().getAttribute("common_tp_info_city"));
        } catch (Exception e) {
            PlatformLogUtil.logException("Tccp酒店关单消息消费，从订单信息中解析divisionId失败，尝试根据城市名查询", e.getMessage(), e, LogListUtil.newArrayList(orderNotify, orderDTO));
            divisionId = divisionService.getDivisionIdByCityName(cityName);
        }

        if (ldbTairManager.incr(TairConstant.TCCP_CONSUME_DISTINCT_KEY + TCCP_TRADE_RESULT_HOTEL_CLOSE_ORDER + "_" + orderNotify.getOrderId(), 1, 0, 120) > 1) {
            PlatformLogUtil.logFail("Tccp酒店关单消息消费，重复消费，不做处理", LogListUtil.newArrayList(orderNotify));
            return;
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("unionId", unionId);
        jsonObject.put(MaterialVariableConstants.HOTEL_NAME, hotelName);
        jsonObject.put(MaterialVariableConstants.HOTEL_ADDRESS, address);
        jsonObject.put(MaterialVariableConstants.CHECK_IN_DATE, checkinDateStr);
        jsonObject.put(MaterialVariableConstants.ARR_CITY_NAME, cityName);
        jsonObject.put(MaterialVariableConstants.PASSENGER_NAME, passengerNameList);

        jsonObject.put(MaterialVariableConstants.NON_PACKAGE_HOTEL_DIVISION_ID, divisionId);
        jsonObject.put(MaterialVariableConstants.NON_PACKAGE_HOTEL_CHECK_IN, checkinDateStr);
        jsonObject.put(MaterialVariableConstants.NON_PACKAGE_HOTEL_CHECK_OUT, checkOutDateStr);

        metaqProducer.send(MQEnum.WECHAT_CUSTOMER_HOTEL_BOOK_FAIL, "", "", jsonObject.toJSONString());
        PlatformLogUtil.logInfo("Tccp酒店关单消息消费，酒店未预定成功事件", LogListUtil.newArrayList(orderNotify, orderDTO, jsonObject));
    }

    /**
     * 酒店 订单创建担保交易消息，可以用来生成 入住提醒 事件
     * 查询到入住时间后，在hts上注册事件，事件执行时需判断：payStatus是否合法，预付&面付订单payStatus = 20，信用住&线下信用住订单payStatus != 40
     *
     * @param orderNotify tccp消息
     */
    private void processHotelEnableOrder(OrderNotify orderNotify, String unionId) {
        HotelSubBizTypeEnum hotelSubBizTypeEnum = HotelSubBizTypeEnum.codeToEnum(orderNotify.getSubBizType());
        if (Objects.isNull(hotelSubBizTypeEnum)) {
            PlatformLogUtil.logFail("Tccp酒店订单创建担保交易消息消费，酒店子业务类型非法", LogListUtil.newArrayList(orderNotify));
            return;
        }

        OrderDTO orderDTO = tccpManager.queryOrderDTO(orderNotify.getOrderId(), orderNotify.getBuyerId(), true);
        // 这里要用 订单中心同步业务垂直标 判断是否境外酒店
        VerticalAttributesDO orderCenterSyncBusinessVerticalAttributesDO = orderDTO.getVerticalAttributesDTOS()
                .stream()
                .map(VerticalAttributesDTO::getVerticalAttributesDO)
                .filter(verticalAttributesDO -> Objects.equals(VerticalAttributesTypeEnum.ORDER_CENTER_SYNC_BUSINESS.getCode(), verticalAttributesDO.getVerticalAttributesType()))
                .findFirst().orElse(null);

        if (Objects.isNull(orderCenterSyncBusinessVerticalAttributesDO)) {
            PlatformLogUtil.logFail("Tccp酒店订单创建担保交易消息消费，垂直标信息为空", LogListUtil.newArrayList(orderNotify, orderDTO));
            return;
        }

        Integer city = com.alibaba.tripscrm.service.util.system.NumberUtils.toInteger(orderDTO.getOrderDO().getAttribute("common_tp_info_city"), 0);
        Integer hotelArea = com.alibaba.tripscrm.service.util.system.NumberUtils.toInteger(orderCenterSyncBusinessVerticalAttributesDO.getAttribute("hotelArea"), 0);
        if (city > 700000 || hotelArea > 0) {
            PlatformLogUtil.logFail("Tccp酒店订单创建担保交易消息消费，不是国内酒店的订单", LogListUtil.newArrayList(orderNotify, orderDTO));
            return;
        }

        // 入住时间
        Date serviceStartTime = orderDTO.getResourcesDTOs().get(0).getResourceDO().getServiceStartTime();
        Date remindDate = calcRemindDate(serviceStartTime.getTime());
        if (remindDate == null) {
            PlatformLogUtil.logInfo("Tccp酒店订单创建担保交易消息消费，不做入住提醒", LogListUtil.newArrayList(orderNotify, orderDTO));
            return;
        }

        if (ldbTairManager.incr(TairConstant.TCCP_CONSUME_DISTINCT_KEY + TCCP_TRADE_RESULT_HOTEL_ENABLE_ORDER + "_" + orderNotify.getOrderId(), 1, 0, 120) > 1) {
            PlatformLogUtil.logFail("Tccp酒店订单创建担保交易消息消费，重复消费，不做处理", LogListUtil.newArrayList(orderNotify));
            return;
        }

        hotelCheckinRemindTaskExecutor.register(orderNotify, remindDate);
        PlatformLogUtil.logInfo("Tccp酒店订单创建担保交易消息消费，注册入住提醒hts任务", LogListUtil.newArrayList(orderNotify, orderDTO, serviceStartTime, remindDate));
    }

    private Date calcRemindDate(long serviceStartTimeStamp) {
        Date serviceStartTime = new Date(serviceStartTimeStamp);
        // 需要做入住提醒的日期
        LocalDate remindLocalDate = serviceStartTime
                .toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .minusDays(1);

        // 当天入住，不做提醒
        if (LocalDate.now().isAfter(remindLocalDate)) {
            PlatformLogUtil.logInfo("Tccp酒店订单创建担保交易消息消费，当天入住，不做提醒", LogListUtil.newArrayList(serviceStartTime));
            return null;
        }

        String[] split = hotelRemindBeginTime.split(":");
        int hotelRemindBeginHour = Integer.parseInt(split[0]);
        int hotelRemindBeginMinute = Integer.parseInt(split[1]);

        String[] split1 = hotelRemindEndTime.split(":");
        int hotelRemindEndHour = Integer.parseInt(split1[0]);
        int hotelRemindEndMinute = Integer.parseInt(split1[1]);

        // 前一天下的单
        if (LocalDate.now().isEqual(remindLocalDate)) {
            // 判断是否已经过了提醒时间
            if (DateUtils.getCurrentHour() >= hotelRemindBeginHour) {
                PlatformLogUtil.logInfo("Tccp酒店订单创建担保交易消息消费，前一天入住，并且已经过了11点，不做提醒", LogListUtil.newArrayList(serviceStartTime));
                return null;
            }
        }

        // 只在 11点 - 15点 之间做入住提醒
        LocalDateTime remindStartLocalDateTime = remindLocalDate.atTime(LocalTime.of(hotelRemindBeginHour, hotelRemindBeginMinute, 0));
        LocalDateTime remindEndLocalDateTime = remindLocalDate.atTime(LocalTime.of(hotelRemindEndHour, hotelRemindEndMinute, 0));

        // 获取 remindStartLocalDateTime 和 remindEndLocalDateTime 之间随机一个时间
        long randomTime = ThreadLocalRandom.current().nextLong(
                remindStartLocalDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(),
                remindEndLocalDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
        );
        Date remindDate = new Date(randomTime);
        PlatformLogUtil.logInfo("Tccp酒店订单创建担保交易消息消费，计算入住提醒时间", LogListUtil.newArrayList(serviceStartTime, remindDate));
        return remindDate;
    }

    /**
     * 交易成功消息，可以用来生成 度假门票预定成功 事件
     * 过滤条件：orderNotify.getBizType为com.fliggy.tccp.client.enumerate.BizTypeEnum#ITEM_GOODS + com.fliggy.tccp.client.enumerate.ItemGoodsSubBizTypeEnum#FLIGGY_GOODS_TICKET
     *
     * @param orderNotify tccp消息
     */
    private void processMultiResourceOrderConfirmSucc(OrderNotify orderNotify, String unionId) {
        if (!Objects.equals(BizTypeEnum.ITEM_GOODS.getCode(), orderNotify.getBizType())) {
            PlatformLogUtil.logInfo("Tccp交易成功消息消费，非度假宝贝类目，不做处理", LogListUtil.newArrayList(orderNotify));
            return;
        }

        if (!Objects.equals(ItemGoodsSubBizTypeEnum.FLIGGY_GOODS_TICKET.getCode(), orderNotify.getSubBizType())) {
            PlatformLogUtil.logInfo("Tccp交易成功消息消费，非度假门票类目，不做处理", LogListUtil.newArrayList(orderNotify));
            return;
        }

        OrderDTO orderDTO = tccpManager.queryOrderDTO(orderNotify.getOrderId(), orderNotify.getBuyerId(), false);
        if (Objects.isNull(orderDTO)) {
            PlatformLogUtil.logFail("Tccp交易成功消息消费，查询订单失败", LogListUtil.newArrayList(orderNotify));
            return;
        }

        if (ldbTairManager.incr(TairConstant.TCCP_CONSUME_DISTINCT_KEY + TCCP_TRADE_MULTI_RESOURCE_ORDER_CONFIRM_SUCC + "_" + orderNotify.getOrderId(), 1, 0, 120) > 1) {
            PlatformLogUtil.logFail("Tccp交易成功消息消费，重复消费，不做处理", LogListUtil.newArrayList(orderNotify));
            return;
        }

        // 出游日期
        Date serviceStartTime = orderDTO.getResourcesDTOs().get(0).getResourceDO().getServiceStartTime();
        // 出游日期
        String startDateStr = serviceStartTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 订购商品名称
        String name = orderDTO.getOrderItemDTOs().get(0).getOrderItemDO().getName();
        // 订购商品份数
        Integer buyAmount = orderDTO.getOrderItemDTOs().get(0).getOrderItemDO().getBuyAmount();
        // 出游目的地
        String itemGoodsTicketScenicCityName = orderDTO.getResourcesDTOs().get(0).getResourceDO().getAttribute("item_goods_ticket_scenic_city_name");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("unionId", unionId);
        jsonObject.put(MaterialVariableConstants.ITEM_NAME, name);
        jsonObject.put(MaterialVariableConstants.AMOUNT, buyAmount);
        jsonObject.put(MaterialVariableConstants.ARR_CITY_NAME, itemGoodsTicketScenicCityName);
        jsonObject.put(MaterialVariableConstants.SERVICE_START_TIME, startDateStr);
        metaqProducer.send(MQEnum.WECHAT_CUSTOMER_GOODS_TICKET_RESERVATION_SUCCESS, "", "", jsonObject.toJSONString());
        PlatformLogUtil.logInfo("Tccp交易成功消息消费，度假门票预定成功事件", LogListUtil.newArrayList(orderNotify, orderDTO, jsonObject));
    }

    private boolean queryHotelBookSuccessRecord(String unionId, long orderId) {
        ResourceRelationDO resourceRelationDO = new ResourceRelationDO();
        resourceRelationDO.setGmtCreate(new Date());
        resourceRelationDO.setGmtModified(new Date());
        resourceRelationDO.setSourceId(unionId);
        resourceRelationDO.setSourceType(ResourceTypeEnum.UNION_ID.getCode().byteValue());
        resourceRelationDO.setTargetId(String.valueOf(orderId));
        resourceRelationDO.setTargetType(ResourceTypeEnum.TCCP_ORDER_ID.getCode().byteValue());
        resourceRelationDO.setRelationType(ResourceRelationTypeEnum.HOTEL_BOOK_SUCCESS_RECORD.getCode());

        ResourceRelationQuery resourceRelationQuery = new ResourceRelationQuery();
        resourceRelationQuery.setSourceType(ResourceTypeEnum.UNION_ID.getCode().byteValue());
        resourceRelationQuery.setSourceId(unionId);
        resourceRelationQuery.setTargetType(ResourceTypeEnum.TCCP_ORDER_ID.getCode().byteValue());
        resourceRelationQuery.setTargetId(String.valueOf(orderId));
        resourceRelationQuery.setType(ResourceRelationTypeEnum.HOTEL_BOOK_SUCCESS_RECORD.getCode());
        return !CollectionUtils.isEmpty(resourceRelationService.query(resourceRelationQuery));
    }

    private void recordHotelBookSuccessRecord(String unionId, long orderId) {
        ResourceRelationDO resourceRelationDO = new ResourceRelationDO();
        resourceRelationDO.setGmtCreate(new Date());
        resourceRelationDO.setGmtModified(new Date());
        resourceRelationDO.setSourceId(unionId);
        resourceRelationDO.setSourceType(ResourceTypeEnum.UNION_ID.getCode().byteValue());
        resourceRelationDO.setTargetId(String.valueOf(orderId));
        resourceRelationDO.setTargetType(ResourceTypeEnum.TCCP_ORDER_ID.getCode().byteValue());
        resourceRelationDO.setRelationType(ResourceRelationTypeEnum.HOTEL_BOOK_SUCCESS_RECORD.getCode());
        resourceRelationService.add(resourceRelationDO);
    }
}
