package com.alibaba.tripscrm.service.model.domain.context.isv;

import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/1/16 10:33
 */
@Data
public class IsvRouteWechatUserContext implements Serializable {
    /**
     * 企微成员信息
     */
    private WechatUserDTO wechatUserDTO;

    /**
     * 风控状态
     */
    private RiskStatusEnum riskStatusEnum;
}
