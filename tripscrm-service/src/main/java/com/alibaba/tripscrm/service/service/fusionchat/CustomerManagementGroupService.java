package com.alibaba.tripscrm.service.service.fusionchat;

import com.alibaba.tripscrm.dal.model.domain.data.CustomerManageGroupDO;
import com.alibaba.tripscrm.service.model.domain.query.CustomerManagementGroupQuery;

import java.util.List;

/**
 * 客户管理组服务
 */
public interface CustomerManagementGroupService {

    /**
     * 增加客户管理组
     * @param customerManageGroupDO 管理组信息
     * @return 新增结果
     */
    Boolean addManagementGroup(CustomerManageGroupDO customerManageGroupDO);

    /**
     * 主键删除客户管理组
     * @param id 主键
     * @return 删除结果
     */
    Boolean deleteManagementGroup(Long id);

    /**
     * 更新客户管理组信息
     * @param customerManageGroupDO 管理组信息
     * @return 更新结果
     */
    Boolean updateManagementGroupById(CustomerManageGroupDO customerManageGroupDO);

    /**
     * 主键查询客户管理组信息
     * @param id 主键
     * @return 客户管理组信息
     */
    CustomerManageGroupDO selectById(Long id);

    /**
     * 成员下客户管理组是否存在同名
     * @param userId 企微成员
     * @param corpId 组织ID
     * @return 存在标识
     */
    Boolean customerGroupExist(String userId, String corpId, String name);

    /**
     * 条件查询
     * @param query 查询条件
     * @return 管理组列表
     */
    List<CustomerManageGroupDO> list(CustomerManagementGroupQuery query);

}
