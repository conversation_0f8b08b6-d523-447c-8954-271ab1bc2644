package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.service.model.domain.request.CreateGroupRequest;
import com.alibaba.tripscrm.service.model.vo.log.UpdateDetailVO;

import java.util.List;

/**
 * 微信群管理接口（偏向于外调的服务）
 * <AUTHOR>
 * @Date 2024/3/14 10:55
 **/
public interface WechatGroupManagementService {

    /**
     * 创建群聊
     * @param request 请求体
     * @return 创建结果
     */
    String createGroup(CreateGroupRequest request);

    /**
     * 批量修改群名称
     * @param chatIdList 群聊列表
     * @param groupNamePre 群名称前缀
     * @param groupNameIndex 群名称后缀下标
     * @return 创建结果
     */
    List<UpdateDetailVO> batchUpdateGroupName(List<String> chatIdList, String groupNamePre, Long groupNameIndex);

    /**
     * 批量发布群公告
     * @param chatIdList 群聊列表
     * @param noticeMaterialId 素材id
     * @return 创建结果
     */
    List<UpdateDetailVO> batchPushGroupNotice(List<String> chatIdList, Long noticeMaterialId);

    /**
     * 批量修改群主
     * @param chatIdList 群聊列表
     * @param ownerUserId 群主userId
     * @return 创建结果
     */
    List<UpdateDetailVO> batchUpdateGroupOwner(List<String> chatIdList, String ownerUserId);

    /**
     * 批量修改群管理员
     * @param chatIdList 群聊列表
     * @param adminList 管理员列表
     * @return 创建结果
     */
    List<UpdateDetailVO> batchUpdateGroupAdmin(List<String> chatIdList, List<String> adminList);
}
