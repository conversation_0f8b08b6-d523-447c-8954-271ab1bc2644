package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.request.DeleteUserRequest;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.query.WechatUserListQuery;
import com.alibaba.tripscrm.service.model.domain.request.WechatUserCreateRequest;
import com.alibaba.tripscrm.service.model.domain.request.WechatUserSetPermissionConfigRequest;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-07-04 11:37:28
 */
public interface WechatUserService {
    /**
     * 批量创建企微成员
     *
     * @param request
     * @return
     */
    TripSCRMResult<Integer> batchCreate(WechatUserCreateRequest request);

    /**
     * 分页获取企微成员列表
     *
     * @param query 查询条件
     * @return 企微成员信息
     */
    PageInfoDTO<WechatUserDTO> listPageInfo(WechatUserListQuery query);

    /**
     * 批量获取企微成员列表
     *
     * @param userIdList 企微成员 Id
     * @return 企微成员信息
     */
    List<WechatUserDTO> listById(List<String> userIdList);

    /**
     * 批量获取企微成员列表
     *
     * @param userIdList 企微成员 Id
     * @return 企微成员信息
     */
    List<WechatUserDTO> listWithRobotInfoById(List<String> userIdList);

    /**
     * 查询企微成员列表
     *
     * @return List<WechatUserBO>
     */
    List<WechatUserDTO> listAll();

    /**
     * 通过机器人在线状态获取
     *
     * @param robotStatusEnumList 机器人在线状态
     * @return 企微成员列表
     */
    List<WechatUserDTO> listByRobotStatus(List<RobotStatusEnum> robotStatusEnumList);

    /**
     * 查询企微成员列表
     *
     * @param spaceId 业务空间 Id
     * @return List<WechatUserBO>
     */
    List<WechatUserDTO> listBySpaceId(Long spaceId);

    /**
     * 批量获取企微成员列表
     *
     * @param onlineStatus 在线状态
     * @return 企微成员信息
     */
    List<WechatUserDTO> listByOnlineStatus(Byte onlineStatus);

    /**
     * 根据条件查询企微成员
     *
     * @param query 查询条件
     * @return 企微成员信息
     */
    List<WechatUserDTO> listByCondition(WechatUserQuery query);

    /**
     * 查询当前锁定企微账号的平台账号
     *
     * @param userId  企微账号id
     * @param spaceId 业务空间
     * @return 平台账号
     */
    String queryLockUser(String userId, Long spaceId);

    /**
     * 查询当前锁定企微账号的平台账号
     *
     * @param userId 企微账号id
     * @param corpId 企业Id
     * @return 平台账号
     */
    String queryLockUserWithCache(String userId, String corpId);

    /**
     * 根据条件查询企微成员（完成新人任务）
     *
     * @param query 查询条件
     * @return 企微成员信息
     */
    List<WechatUserDTO> listValidWechatUserByCondition(WechatUserQuery query);

    /**
     * 批量获取企微成员列表
     *
     * @param userIdList           企微成员 Id
     * @param wechatUserStatusEnum 成员状态
     * @return 企微成员信息
     */
    List<WechatUserDTO> listByIdAndStatus(List<String> userIdList, WechatUserStatusEnum wechatUserStatusEnum);

    /**
     * 查询拥有使用权限的企微账号列表
     *
     * @param accountId 员工号
     * @param spaceId   业务空间 Id
     * @return 拥有使用权限的企微账号列表
     */
    List<String> listUserIdWithPermission(String accountId, Long spaceId);

    /**
     * 是否拥有该企微账号使用权限
     *
     * @param userId  企微账号id
     * @param spaceId 业务空间 Id
     * @return 拥有该企微账号的平台账号列表
     */
    Boolean checkHasManagerPermission(String userId, String accountId, Long spaceId);

    /**
     * 是否锁定该企微账号
     *
     * @param userId  企微账号id
     * @param spaceId 业务空间 Id
     * @return 拥有该企微账号的平台账号列表
     */
    Boolean checkHasLock(String userId, String accountId, Long spaceId);

    /**
     * 查询拥有该企微账号的平台账号列表
     *
     * @param userId  企微账号id
     * @param spaceId 业务空间 Id
     * @return 拥有该企微账号的平台账号列表
     */
    List<String> listAccountIdWithPermission(String userId, Long spaceId);

    /**
     * 锁定账号（聚合聊天独占使用）
     *
     * @param userId  企微成员 userId
     * @param empId   员工号
     * @param spaceId 业务空间 id
     */
    void lock(User account, String userId, String empId, Long spaceId, Boolean force);

    /**
     * 解锁账号（聚合聊天独占使用）
     *
     * @param userId  企微成员 userId
     * @param empId   员工号
     * @param spaceId 业务空间 id
     */
    void unlock(User account, String userId, String empId, Long spaceId, Boolean force);

    /**
     * 配置企微账号的权限（管理员和使用者）
     *
     * @param request 请求
     * @return 影响行数
     */
    Integer setPermissionConfig(WechatUserSetPermissionConfigRequest request);

    /**
     * 退出登录
     *
     * @param userId userId
     * @return requestId
     */
    String logout(String userId);

    /**
     * 获取业务空间 id
     *
     * @param userId userId
     * @return spaceId
     */
    Long getSpaceId(String userId, String corpId);

    /**
     * 企微成员打卡（异步）
     *
     * @param corpId corpId
     * @param userId userId
     * @return TripSCRMResult
     */
    TripSCRMResult<String> asyncClock(String corpId, String userId);

    /**
     * 企微成员打卡（异步）
     *
     * @param jsonRequest jsonRequest
     * @return TripSCRMResult
     */
    TripSCRMResult<String> asyncClock(String jsonRequest);

    /**
     * 更新企微成员新手任务完成状态
     */
    Integer updateActivateStatus(String corpId, String userId, Integer activateStatus);

    /**
     * 获取所有可用的企微成员
     *
     * @param taskInfoDO 任务
     * @return spaceId
     */
    List<String> getAutoMatchUserIdList(TaskInfoDO taskInfoDO);

    /**
     * 是否需要判断保存通讯录
     *
     * @param userId 企微成员Id
     * @param corpId 企业Id
     */
    Boolean needCheckSaveInContact(String userId, String corpId);

    /**
     * 是否需要判断或者关注群聊
     *
     * @param userId 企微成员Id
     * @return corpId 企业Id
     */
    Boolean needCheckOpenWorkGroup(String userId, String corpId);

    /**
     * 从缓存中根据userId查询企微成员
     *
     * @param userId 企微成员id
     * @return 企微成员信息
     */
    List<WechatUserDTO> listByUserIdWithCache(String userId);

    /**
     * 删除用户
     * @param request
     * @return
     */
    TripSCRMResult<Boolean> deleteUser(DeleteUserRequest request);
}
