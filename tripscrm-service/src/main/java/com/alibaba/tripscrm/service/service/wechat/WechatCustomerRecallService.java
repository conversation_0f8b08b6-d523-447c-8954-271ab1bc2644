package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.ScrmWechatCustomerRecallDO;
import com.alibaba.tripscrm.service.enums.wechat.RescallStatusTypeEnum;
import com.alibaba.tripscrm.service.model.dto.wechat.ScrmWechatCustomerRecallDTO;

import java.util.List;

/**
 * @description：好友召回服务
 * @Author：wangrui
 * @create：2025/1/7 下午8:26
 * @Filename：WechatCustomerRecallService
 */
public interface WechatCustomerRecallService {
    /**
     *  根据企微号查询
     * @param
     * @return recall customer response
     */
    List<ScrmWechatCustomerRecallDTO> selectByUserIdAndDeletTime(String userId, int days, int limit);
    /**
     * 根据召回状态查询
     * @param status
     * @return recall customer response
     */
    List<ScrmWechatCustomerRecallDTO> selectByStatus(RescallStatusTypeEnum status, int pageNum, int pageSize,Long minId);

    /**
     * 根据外部用户id更新
     * @param userId
     * @param record
     * @return recall customer response
     */
    int updateByExternalUserId(String userId, ScrmWechatCustomerRecallDO record);

    /**
     * 根据id更新
     * @param record
     * @return recall customer response
     */
    int updateByPrimaryKey(ScrmWechatCustomerRecallDO record);

}
