package com.alibaba.tripscrm.service.service.impl.common;

import com.alibaba.tripscrm.dal.mapper.tddl.ManagementGroupMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ManagementGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.ManagementGroupParam;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.model.domain.query.GroupQuery;
import com.alibaba.tripscrm.service.service.common.ManagementGroupService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/5 11:46
 **/
@Service
public class ManagementGroupServiceImpl implements ManagementGroupService {

    @Resource
    private ManagementGroupMapper managementGroupMapper;
    @Resource
    private ResourceRelationService resourceRelationService;

    @Override
    public boolean add(ManagementGroupDO managementGroupDO) {
        if (managementGroupDO == null) {
            return false;
        }
        return managementGroupMapper.insert(managementGroupDO) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return false;
        }
        return managementGroupMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public boolean updateById(ManagementGroupDO managementGroupDO) {
        if (managementGroupDO == null) {
            return false;
        }
        return managementGroupMapper.updateByPrimaryKeySelective(managementGroupDO) > 0;
    }

    @Override
    public ManagementGroupDO queryById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return null;
        }
        return managementGroupMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ManagementGroupDO> queryByPage(GroupQuery query) {
        ManagementGroupParam param = new ManagementGroupParam();
        // 分页配置
        param.setPage(query.isPage());
        param.setPageSize(query.getPageSize());
        param.setPageStart(query.getPageNum());
        // 查询条件
        ManagementGroupParam.Criteria criteria = param.createCriteria();
        criteria.andIsDeleteEqualTo(IsDeleteEnum.NO.getCode());
        if (query.getSpaceId() != null) {
            criteria.andSpaceIdEqualTo(query.getSpaceId());
        }
        if (CollectionUtils.isNotEmpty(query.getTypeList())) {
            criteria.andTypeIn(query.getTypeList());
        }
        if (!StringUtils.isBlank(query.getName())) {
            criteria.andNameLike(String.format("%%%s%%", query.getName()));
        }
        param.appendOrderByClause(ManagementGroupParam.OrderCondition.GMTMODIFIED, ManagementGroupParam.SortType.DESC);
        return managementGroupMapper.selectByParam(param);
    }

    @Override
    public List<ManagementGroupDO> queryByName(String name, Long spaceId) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        ManagementGroupParam param = new ManagementGroupParam();
        ManagementGroupParam.Criteria criteria = param.createCriteria();
        criteria.andNameEqualTo(name);
        criteria.andSpaceIdEqualTo(spaceId);
        return managementGroupMapper.selectByParam(param);
    }

    @Override
    public Long count(GroupQuery query) {
        ManagementGroupParam param = new ManagementGroupParam();
        ManagementGroupParam.Criteria criteria = param.createCriteria();
        criteria.andIsDeleteEqualTo(IsDeleteEnum.NO.getCode());
        if (query.getSpaceId() != null) {
            criteria.andSpaceIdEqualTo(query.getSpaceId());
        }
        if (!CollectionUtils.isEmpty(query.getTypeList())) {
            criteria.andTypeIn(query.getTypeList());
        }
        if (!StringUtils.isBlank(query.getName())) {
            criteria.andNameLike(String.format("%%%s%%", query.getName()));
        }
        return managementGroupMapper.countByParam(param);
    }
}
