package com.alibaba.tripscrm.service.service.task.ability.sub.pre;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz.*;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.eagleeye.EagleEye;
import com.taobao.eagleeye.RpcContext_inner;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 任务执行_子任务，对每条数据执行业务逻辑
 *
 * <AUTHOR>
 * @since 2024/4/22 11:56
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskBizExecuteProcessor implements ExecuteProcessor {
    private static final List<BizProcessor> BIZ_PROCESSOR_LIST = new ArrayList<>();

    /**
     * Spring上下文
     */
    private final ApplicationContext applicationContext;
    private final TaskExecuteRecordService taskExecuteRecordService;
    private final SubTaskInstanceService subTaskInstanceService;

    @PostConstruct
    public void init() {
        // 单条任务数据处理_前置处理
        BIZ_PROCESSOR_LIST.add(applicationContext.getBean(PreBizProcessor.class));
        // 单条任务数据处理_快速定位
        BIZ_PROCESSOR_LIST.add(applicationContext.getBean(FindCursorBizProcessor.class));
        // 单条任务数据处理_AB分桶
        BIZ_PROCESSOR_LIST.add(applicationContext.getBean(AbTestBizProcessor.class));
        // 单条任务数据处理_规则校验
        BIZ_PROCESSOR_LIST.add(applicationContext.getBean(RuleCheckBizProcessor.class));
        // 单条任务数据处理_AI异步处理判断
        BIZ_PROCESSOR_LIST.add(applicationContext.getBean(CheckAiAsyncBizProcessor.class));
        // 单条任务数据处理_限流
        BIZ_PROCESSOR_LIST.add(applicationContext.getBean(RateLimitBizProcessor.class));
        // 单条任务数据处理_原子能力
        BIZ_PROCESSOR_LIST.add(applicationContext.getBean(ExecuteBizProcessor.class));
        // 单条任务数据处理_后置处理
        BIZ_PROCESSOR_LIST.add(applicationContext.getBean(PostBizProcessor.class));
    }

    @Override
    @TaskExecuteLog("任务执行_子任务，对每条数据执行业务逻辑")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(TaskTriggerTypeEnum.getByCode(context.getTaskInfoDOSnapshot().getTriggerType()));
        TodoTaskVO todoTaskVO = context.getTodoTaskVOList().get(0);
        List<TaskDataVO.DataBodyVO> data = Optional.ofNullable(todoTaskVO.getData()).orElse(new ArrayList<>());

        boolean needReplaceTraceId = data.size() > 1;
        // 针对每个targetId进行业务方法调用
        for (TaskDataVO.DataBodyVO d : data) {
            TodoTaskVO todo = todoTaskVO.clone();
            todo.setData(Lists.newArrayList(d));
            todo.setStartTime(System.currentTimeMillis());
            if (!NumberUtils.validLong(todo.getData().get(0).getRecordId())) {
                todo.getData().get(0).setRecordId(taskExecuteRecordService.generateRecordId());
            }

            TripSCRMResult<Void> result = executeBizProcess(context, todo, needReplaceTraceId);
            if (result.isSuccess()) {
                continue;
            }

            // 单条直接返回执行结果
            if (context.getTestFlag() || context.getRetry() || isStreamTask) {
                throw new TripscrmException(TripSCRMErrorCode.codeOf(result.getCode()));
            }
        }

        // 结果标志设置为true
        context.setRunTaskResult(true);
        return TripSCRMResult.success(null);
    }

    private TripSCRMResult<Void> executeBizProcess(TaskExecuteContext context, TodoTaskVO todoTaskVO, boolean needReplaceTraceId) {
        RpcContext_inner rpcContext = EagleEye.getRpcContext();
        try {
            if (needReplaceTraceId) {
                EagleEye.startTrace(null, "scrmTaskBizExecute" + todoTaskVO.getData().get(0).getRecordId(), EagleEye.TYPE_CUSTOM_TRACE);
            }
            for (BizProcessor bizProcessor : BIZ_PROCESSOR_LIST) {
                TripSCRMResult<Void> result = bizProcessor.process(context, todoTaskVO);
                if (!result.isSuccess()) {
                    todoTaskVO.getData().get(0).setResultErrorCode(TripSCRMErrorCode.codeOf(result.getCode()));
                    subTaskInstanceService.updateSubTaskInstanceExecuteResult(context, todoTaskVO.getData().get(0));
                    return result;
                }
            }

            // 重试场景不走这里更新子任务实例执行结果
            if (context.getRetry()) {
                return TripSCRMResult.success(null);
            }

            subTaskInstanceService.updateSubTaskInstanceExecuteResult(context, todoTaskVO.getData().get(0));
            return TripSCRMResult.success(null);
        } catch (TripscrmException e) {
            PlatformLogUtil.logException("子任务执行，对每条数据执行业务逻辑，执行异常", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getMainTaskInstanceId(), context.getInstanceId(), todoTaskVO.getData().get(0)));

            // 重试场景/重复执行不走这里更新子任务实例执行结果
            if (context.getRetry() || Lists.newArrayList(TripSCRMErrorCode.TASK_EXECUTE_SKIP_TARGET).contains(e.getErrorCodeEnum())) {
                return TripSCRMResult.fail(e.getErrorCode(), e.getErrorMsg());
            }

            MetaQDeleyLevel retryDelayLevel = subTaskInstanceService.getRetryDelayLevel(context, e.getErrorCodeEnum());
            if (Objects.nonNull(retryDelayLevel)) {
                todoTaskVO.getData().get(0).setNextRetryDelayLevel(retryDelayLevel);
                todoTaskVO.getData().get(0).setResultErrorCode(TripSCRMErrorCode.FAIL_AND_RETRY);
                subTaskInstanceService.updateSubTaskInstanceExecuteResult(context, todoTaskVO.getData().get(0));
                return TripSCRMResult.fail(TripSCRMErrorCode.FAIL_AND_RETRY);
            }

            todoTaskVO.getData().get(0).setResultErrorCode(e.getErrorCodeEnum());
            subTaskInstanceService.updateSubTaskInstanceExecuteResult(context, todoTaskVO.getData().get(0));
            return TripSCRMResult.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Exception e) {
            PlatformLogUtil.logException("子任务执行，对每条数据执行业务逻辑，处理失败", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getMainTaskInstanceId(), context.getInstanceId(), todoTaskVO.getData().get(0)));

            // 重试场景不走这里更新子任务实例执行结果
            if (context.getRetry()) {
                return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
            }

            todoTaskVO.getData().get(0).setResultErrorCode(TripSCRMErrorCode.SYSTEM_EXCEPTION);
            subTaskInstanceService.updateSubTaskInstanceExecuteResult(context, todoTaskVO.getData().get(0));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        } finally {
            if (needReplaceTraceId) {
                //设置RPC类型名
                EagleEye.attribute("rpcName", "scrmTask");
                //标识一次trace调用结束，设置状态码(00)和RpcType(90)
                EagleEye.endTrace("00");
            }
            EagleEye.setRpcContext(rpcContext);
        }
    }
}