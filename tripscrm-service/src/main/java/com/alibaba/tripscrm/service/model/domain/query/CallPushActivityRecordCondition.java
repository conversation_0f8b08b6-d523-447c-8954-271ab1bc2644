package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:43
 */
@Data
@Builder
public class CallPushActivityRecordCondition {
    /**
     * Id
     */
    private Long id;

    /**
     * 外呼推送状态，0：初始化，1：外推成功，2：外推失败
     */
    private List<Byte> pushStage;

    /**
     * 外呼状态，0：初始化，1：外呼解密成功，2：外呼成功，意向等级为A，3：外呼失败，4：外呼成功，意向等级为B，5：外呼成功，意向等级不为A和B，6：外呼成功，意向等级为G
     */
    private List<Byte> callStage;

    /**
     * 短信发送状态，0：初始化，1：短信解密成功，2：短信发送成功，3：短信发送失败
     */
    private List<Byte> msgStage;

    /**
     * 手机号主加好友状态，0：初始化，1：发起主加成功，2：发起主加失败，3：主加成功
     */
    private List<Byte> addByPhoneStage;

    /**
     * 短信添加好友状态，0：初始化，1：进入加企微承接页，2：加企微成功
     */
    private List<Byte> addByMsgStage;
}
