package com.alibaba.tripscrm.service.middleware.metaq.consumer.ai;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.trip.channel.ai.client.enums.TaskInstanceStatus;
import com.alibaba.trip.channel.ai.client.model.common.ResultDO;
import com.alibaba.trip.channel.ai.client.model.metaq.task.TaskInstanceResultEvent;
import com.alibaba.trip.channel.ai.client.model.request.task.TaskInstanceQueryRequest;
import com.alibaba.trip.channel.ai.client.model.request.task.TaskSubmitRequest;
import com.alibaba.trip.channel.ai.client.model.response.task.TaskInstanceQueryResponse;
import com.alibaba.trip.channel.ai.client.service.task.AiTaskService;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.SmartResponseTaskParamKey;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import com.alibaba.tripscrm.service.service.task.base.TaskService;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Ai智能回复mq消费者
 *
 * <AUTHOR>
 */
@Service(value = "aiSmartResponseMetaqConsumer")
public class AiSmartResponseMetaqConsumer implements MessageListenerConcurrently {

    @Resource
    private AiTaskService aiTaskService;
    @Resource
    private TaskService taskService;
    @Resource
    private TaskExecuteStrategy taskExecuteStrategy;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("AI智能回复消息处理失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     *
     * @param message 消息对象
     * @return 处理结果
     */
    private boolean dealWithMessage(String message) {
        try {
            // 1. 消息参数转换
            TaskInstanceResultEvent taskResult = JSONObject.parseObject(message, TaskInstanceResultEvent.class);
            PlatformLogUtil.logInfo("AI智能回复消息结果", taskResult);

            // 判断是否跳过智能消息处理
            if (skipSmartResponse(taskResult)) {
                return false;
            }
            // 2. 获取外部Ai任务实例数据
            TaskInstanceQueryResponse taskInstance = queryAiTaskInstance(taskResult);
            if (Objects.isNull(taskInstance)) {
                PlatformLogUtil.logFail("AI智能回复查询AI任务实例失败", LogListUtil.newArrayList(taskResult));
                return false;
            }

            // 3. 获取任务请求实例
            TaskSubmitRequest taskSubmitRequest = JSONObject.parseObject(taskInstance.getRequestParam(), TaskSubmitRequest.class);
            // 任务参数有效校验
            if (Objects.isNull(taskSubmitRequest)) {
                PlatformLogUtil.logFail("获取Ai任务实例数据为空", LogListUtil.newArrayList(taskInstance));
                return false;
            }

            // 4. 获取Ai智能回复任务
            TaskInfoDO smartResponseTask = querySmartResponseTask(taskSubmitRequest, taskInstance);
            if (Objects.isNull(smartResponseTask)) {
                PlatformLogUtil.logFail("AI智能回复查询智能回复任务失败", LogListUtil.newArrayList(taskInstance));
                return false;
            }

            // 5. 进行智能回复处理
            processSmartResponseTask(smartResponseTask, taskSubmitRequest, taskInstance);
        } catch (Exception e) {
            PlatformLogUtil.logException("AI智能回复消息处理异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
        return true;
    }

    /**
     * 是否跳过智能回复处理
     *
     * @param taskResult ai任务结果
     * @return
     */
    private boolean skipSmartResponse(TaskInstanceResultEvent taskResult) {
        // 任务结果数据为空，则直接跳过
        if (Objects.isNull(taskResult)) {
            PlatformLogUtil.logFail("AI智能回复消息任务结果数据为空，跳过消息处理", LogListUtil.newArrayList(taskResult));
            return true;
        }
        // 如果任务状态不是成功，则直接跳过
        if (!TaskInstanceStatus.SUCCESS.getCode().equals(taskResult.getStatus())) {
            PlatformLogUtil.logFail("AI智能回复消息任务状态不是成功，跳过消息处理", LogListUtil.newArrayList(taskResult));
            return true;
        }
        return false;
    }

    /**
     * 查询AI任务实例数据
     *
     * @param taskResult ai任务结果
     * @return ai任务实例数据
     */
    private TaskInstanceQueryResponse queryAiTaskInstance(TaskInstanceResultEvent taskResult) {
        // 1. 构建任务查询请求
        TaskInstanceQueryRequest queryRequest = new TaskInstanceQueryRequest();
        queryRequest.setInstanceId(taskResult.getInstanceId());
        queryRequest.setRequestId(taskResult.getRequestId());
        queryRequest.setBizScene(taskResult.getBizScene());
        // 2. 查询任务实例数据
        ResultDO<TaskInstanceQueryResponse> resultDO = aiTaskService.queryTaskInstance(queryRequest);
        if (Objects.isNull(resultDO) || !resultDO.isSuccess() || Objects.isNull(resultDO.getModel())) {
            PlatformLogUtil.logFail("查询AI任务实例失败", LogListUtil.newArrayList(taskResult, queryRequest));
            return null;
        }
        return resultDO.getModel();
    }

    /**
     * 查询智能回复任务
     *
     * @param taskSubmitRequest Ai任务请求参数
     * @param taskInstance      外部Ai任务数据
     * @return 智能回复任务
     */
    private TaskInfoDO querySmartResponseTask(TaskSubmitRequest taskSubmitRequest, TaskInstanceQueryResponse taskInstance) {
        // 任务参数获取
        Map<String, String> taskParam = taskSubmitRequest.getParam();
        // 获取到智能回复任务的id
        String smartResponseTaskId = Optional.ofNullable(taskParam).orElse(Collections.emptyMap()).get(SmartResponseTaskParamKey.SMART_RESPONSE_TASK_ID.getCode());
        // 智能回复任务id有效校验
        if (StringUtils.isBlank(smartResponseTaskId)) {
            PlatformLogUtil.logFail("获取到智能回复任务的id为空", LogListUtil.newArrayList(taskInstance));
            return null;
        }

        // 获取到智能回复任务
        TaskInfoDO smartResponseTask = taskService.queryTaskById(Long.valueOf(smartResponseTaskId));
        if (Objects.isNull(smartResponseTask)) {
            PlatformLogUtil.logFail("获取到智能回复任务，查询为空", LogListUtil.newArrayList(taskInstance, smartResponseTaskId));
            return null;
        }
        // 如果任务非上线，则直接跳过
        if (!TaskStatusEnum.ONLINE.getStatus().equals(smartResponseTask.getStatus())) {
            PlatformLogUtil.logFail("获取到智能回复任务，任务状态不是上线", LogListUtil.newArrayList(taskInstance, smartResponseTaskId));
            return null;
        }
        return smartResponseTask;
    }

    /**
     * 处理智能回复任务
     *
     * @param smartResponseTask 智能回复任务
     * @param taskSubmitRequest 任务请求参数
     * @param taskInstance      任务实例数据
     */
    private void processSmartResponseTask(TaskInfoDO smartResponseTask, TaskSubmitRequest taskSubmitRequest, TaskInstanceQueryResponse taskInstance) {
        // 获取任务请求参数
        Map<String, String> taskRequestParam = Optional.ofNullable(taskSubmitRequest.getParam()).orElse(Collections.emptyMap());
        // 获取任务结果参数
        Map<String, Object> taskResultParam = Optional.ofNullable(taskInstance.getResult()).orElse(Collections.emptyMap());

        // 获取到消息的企微客户id
        String externalUserId = taskRequestParam.get(SmartResponseTaskParamKey.EXTERNAL_USER_ID.getCode());
        // 企微客户id有效校验
        if (StringUtils.isBlank(externalUserId)) {
            PlatformLogUtil.logFail("智能任务获取到企微客户id为空", LogListUtil.newArrayList(taskInstance));
            return;
        }

        // 获取任务参数
        Map<String, Object> taskParam = Maps.newHashMap();
        taskParam.put(SmartResponseTaskParamKey.REQUEST_TASK_PARAM.getCode(), taskRequestParam);
        taskParam.put(SmartResponseTaskParamKey.RESULT_TASK_PARAM.getCode(), getSmartResponseTaskResultParam(taskResultParam));
        // 业务参数
        taskParam.put(SmartResponseTaskParamKey.EXTERNAL_USER_ID.getCode(), taskRequestParam.get(SmartResponseTaskParamKey.EXTERNAL_USER_ID.getCode()));
        taskParam.put(SmartResponseTaskParamKey.SEND_USER_ID.getCode(), taskRequestParam.get(SmartResponseTaskParamKey.SEND_USER_ID.getCode()));
        taskParam.put(SmartResponseTaskParamKey.CORP_ID.getCode(), taskRequestParam.get(SmartResponseTaskParamKey.CORP_ID.getCode()));
        taskParam.put(SmartResponseTaskParamKey.TARGET_TYPE.getCode(), taskRequestParam.get(SmartResponseTaskParamKey.TARGET_TYPE.getCode()));
        taskParam.put(SmartResponseTaskParamKey.TARGET_ID.getCode(), taskRequestParam.get(SmartResponseTaskParamKey.TARGET_ID.getCode()));

        // 构造任务执行参数
        TaskExecuteParam param = TaskExecuteParam.buildParam(new Date(), smartResponseTask.getId(), TaskTriggerTypeEnum.INTERFACE, ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode(), externalUserId,
            true, taskParam, Collections.emptyList());

        // 执行任务
        TripSCRMResult<String> result = taskExecuteStrategy.runMain(param);
        PlatformLogUtil.logInfo("智能任务触发任务执行，单个任务执行结束", LogListUtil.newArrayList(smartResponseTask, param, result));
    }

    /**
     * 获取智能回复任务结果参数
     *
     * @param taskResultParam 任务结果参数
     * @return 智能回复任务结果参数
     */
    private Map<String, Object> getSmartResponseTaskResultParam(Map<String, Object> taskResultParam) {
        // 获取指定结果数据
        String output = MapUtils.getString(taskResultParam, "output");
        if (StringUtils.isBlank(output)) {
            return Collections.emptyMap();
        }
        return JSONObject.parseObject(output);
    }
}
