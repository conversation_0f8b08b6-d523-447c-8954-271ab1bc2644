package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmWechatCustomerRecallDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.wechat.RescallStatusTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerRecallService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.enums.ErrorCodeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.request.ChattingRetryAddCustomerRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/1/8 下午5:26
 * @Filename：CustomerRecallTaskProcessor
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerRecallTaskExecutor extends AbstractTaskExecutor {
    private final CustomerService customerService;
    private final WechatUserService wechatUserService;
    private final LdbTairManager ldbTairManager;
    private final WechatCustomerRecallService wechatCustomerRecallService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String targetId = getFinalTargetId(context, taskDataBody);
        String sendUserId = getSendUserId(context, todoTaskVO);
        //校验参数
        paramVerify(taskDataBody, context, todoTaskVO);

        //构建请求
        ChattingRetryAddCustomerRequest request = new ChattingRetryAddCustomerRequest();
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        request.setUserId(sendUserId);
        request.setExternalUserId(targetId);
        request.setMessage(extInfoJson.getString("message"));
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> asyncReacallCustomer(request));

        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }
        Date date = new Date();
        Long dayVersion = DateUtils.getDayVersion(date);
        Long minuteVersion = DateUtils.getMinuteVersion(date);
        Long windowVersion = minuteVersion / 10;
        String dayKey = TairConstant.WECHAT_USER_ADD_CUSTOMER_COUNT_IN_WINDOW_PREFIX + dayVersion + "_d_" + SpaceInfoThreadLocalUtils.getCorpId() + "_" + sendUserId;
        String windowKey = TairConstant.WECHAT_USER_ADD_CUSTOMER_COUNT_IN_WINDOW_PREFIX + windowVersion + "_w_" + SpaceInfoThreadLocalUtils.getCorpId() + "_" + sendUserId;
        ldbTairManager.incr(dayKey, 1, 0, 60 * 60 * 25);
        ldbTairManager.incr(windowKey, 1, 0, 60 * 60);
        PlatformLogUtil.logInfo("发送召回请求完成", LogListUtil.newArrayList(request, result));

        //TODO 成功后更新表中的状态为召回中
        ScrmWechatCustomerRecallDO recallDO = new ScrmWechatCustomerRecallDO();
        recallDO.setStatus(RescallStatusTypeEnum.IN_PROGRESS_RECALL.getType());
        recallDO.setRecallTime(new Date());
        if (wechatCustomerRecallService.updateByExternalUserId(targetId, recallDO) < 0) {
            throw new TripscrmException("更新失败");
        }

        // 查询活动上下文
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        JSONObject data = new JSONObject();
        data.put(TaskConstant.TARGET_ID, targetId);
        data.put("wechatUserId", sendUserId);
        data.put("extraInfo", context.getExtInfo());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    private TripSCRMResult<String> asyncReacallCustomer(ChattingRetryAddCustomerRequest chattingRetryAddCustomerRequest) {
        ResultDO<String> resultDO = customerService.chattingRetryAddCustomer(chattingRetryAddCustomerRequest);
        PlatformLogUtil.logInfo("召回好友消息请求发送", chattingRetryAddCustomerRequest, resultDO);
        if (Objects.isNull(resultDO)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED, resultDO.getResultMessage());
        }

        if (!resultDO.getSuccess()) {
            ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
            switch (errorCodeEnum) {
                case WECHAT_CUSTOMER_DATA_ERROR:
                case NO_FRIEND_RELATION:
                case WECHAT_USER_NOT_ONLINE:
                    return TripSCRMResult.fail(TripSCRMErrorCode.FORWARD_MESSAGE_FAIL);
                case RATE_LIMIT_FILTER:
                    return TripSCRMResult.fail(TripSCRMErrorCode.RPC_RATE_LIMIT_FILTER);
                default:
                    return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }
        }
        if (Objects.isNull(resultDO.getModel())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
        return TripSCRMResult.success(resultDO.getModel(), resultDO.getResultMessage());
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey(TaskConstant.SEND_USER_ID)) {
            return (String) taskDataBody.getContext().get(TaskConstant.SEND_USER_ID);
        }
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        if (extInfoJson.containsKey(TaskConstant.SEND_USER_ID)) {
            String sendUserId = extInfoJson.getString(TaskConstant.SEND_USER_ID);
            List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(sendUserId));
            if (CollectionUtils.isNotEmpty(wechatUserList) && Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
                taskDataBody.getContext().put(TaskConstant.SEND_USER_ID, sendUserId);
                return getSendUserId(context, todoTaskVO);
            }
        }
        PlatformLogUtil.logFail("getWechatUserId empty", LogListUtil.newArrayList(taskDataBody));
        throw new TripscrmException(TripSCRMErrorCode.TASK_QUERY_TASK_SEND_USER_FAIL);
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_EXTERNAL_USERID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("目标Id为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        return taskDataBody.getTargetId();
    }

    private boolean paramVerify(TaskDataVO.DataBodyVO dataBodyVO, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        JSONObject extInfoJson = JSONObject.parseObject(dataBodyVO.getExtInfo());
        if (!extInfoJson.containsKey(TaskConstant.SEND_USER_ID) || !extInfoJson.containsKey("message")) {
            PlatformLogUtil.logFail("好友召回发送过程中参数缺失", LogListUtil.newArrayList(dataBodyVO));

            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (StringUtils.isBlank(extInfoJson.getString("message"))) {
            PlatformLogUtil.logFail("好友召回发送过程中message为空", LogListUtil.newArrayList(dataBodyVO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (StringUtils.isBlank(extInfoJson.getString(TaskConstant.SEND_USER_ID))) {
            PlatformLogUtil.logFail("好友召回发送过程中sendUserId为空", LogListUtil.newArrayList(dataBodyVO));
        }
        String sendUserId = extInfoJson.getString(TaskConstant.SEND_USER_ID);
        checkSendUserOnline(dataBodyVO, sendUserId);
        return true;
    }

    private void checkSendUserOnline(TaskDataVO.DataBodyVO taskDataBody, String sendUserId) {
        // 判断指定的发送者id是否在线
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(sendUserId));
        if (CollectionUtils.isNotEmpty(wechatUserList) && Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
            return;
        }
        PlatformLogUtil.logFail("好友召回发送过程中指定的发送者id不在线", LogListUtil.newArrayList(taskDataBody, sendUserId));

        throw new TripscrmException(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE);
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.CUSTOMER_RECALL;
    }
}
