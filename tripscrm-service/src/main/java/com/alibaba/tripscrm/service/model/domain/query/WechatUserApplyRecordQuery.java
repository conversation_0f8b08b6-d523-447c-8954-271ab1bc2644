package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/01/21 16:23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatUserApplyRecordQuery extends BasePageRequest {
    /**
     *   主键
     */
    private Long id;

    /**
     *   上号申请记录Id
     */
    private Long createWeComRecordId;

    /**
     *   服务商类型，1:比邻,10:百业
     */
    private Byte isvType;

    /**
     *   服务商Id
     */
    private String isvId;

    /**
     *   状态，0:初始化,1:上号成功,2:上号失败
     */
    private Byte status;

    /**
     * 创建时间（开始时间）
     */
    private Date createStartTime;

    /**
     * 创建时间（结束时间）
     */
    private Date createEndTime;
}
