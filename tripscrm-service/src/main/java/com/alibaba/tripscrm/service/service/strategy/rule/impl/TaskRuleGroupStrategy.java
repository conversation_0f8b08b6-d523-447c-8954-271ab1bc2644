package com.alibaba.tripscrm.service.service.strategy.rule.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.model.domain.data.RuleGroupDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.rule.RuleStatusEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.RuleExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.RuleQuery;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.alibaba.tripscrm.service.service.strategy.rule.AbstractRuleGroupStrategy;
import com.alibaba.tripscrm.service.service.strategy.rule.AbstractRuleStrategy;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/6 21:00
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskRuleGroupStrategy extends AbstractRuleGroupStrategy {
    private final RuleService ruleService;

    @Override
    protected TripSCRMResult<Void> run(RuleGroupDO ruleGroupDO, RuleExecuteContext context) {
        if (Objects.isNull(ruleGroupDO) || !NumberUtils.validLong(ruleGroupDO.getId())) {
            PlatformLogUtil.logFail("任务规则组执行，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        RuleQuery query = new RuleQuery();
        query.setGroupId(ruleGroupDO.getId());
        query.setStatus(RuleStatusEnum.ONLINE.getStatus());
        List<RuleDO> ruleList = ruleService.selectByCondition(query);
        if (CollectionUtils.isEmpty(ruleList)) {
            PlatformLogUtil.logFail("任务规则组执行，查询规则组下已上线规则数据为空", LogListUtil.newArrayList(ruleGroupDO.getId()));
            return TripSCRMResult.success(null);
        }

        ruleList.sort(Comparator.comparingInt(RuleDO::getPriority));
        for (RuleDO ruleDO : ruleList) {
            RuleExecuteContext ruleExecuteContext = new RuleExecuteContext();
            ruleExecuteContext.setParams(context.getParams());
            TripSCRMResult<Void> result = AbstractRuleStrategy.getStrategyByType(ruleDO.getType()).run(ruleDO, ruleExecuteContext);
            if (result.isSuccess()) {
                return TripSCRMResult.success(null);
            }

            // 规则不满足执行条件，跳过执行
            if (Objects.equals(TripSCRMErrorCode.RULE_NOT_MATCH_EXECUTE_CONDITION, TripSCRMErrorCode.codeOf(result.getCode()))) {
                PlatformLogUtil.logFail("任务规则组执行，规则不满足执行条件，跳过执行", LogListUtil.newArrayList(ruleGroupDO.getId(), ruleDO.getId()));
                continue;
            }

            PlatformLogUtil.logFail("任务规则组执行，规则执行失败", LogListUtil.newArrayList(ruleGroupDO.getId(), ruleDO.getId()));
            return TripSCRMResult.fail(result.getCode(), result.getMsg());
        }

        return TripSCRMResult.success(null);
    }

    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.TASK;
    }
}
