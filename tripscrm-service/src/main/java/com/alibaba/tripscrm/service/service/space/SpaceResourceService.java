package com.alibaba.tripscrm.service.service.space;

import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-15 16:50:27
 */
public interface SpaceResourceService {
    /**
     * 新增
     *
     * @param spaceResourceDO spaceResourceDO
     * @return 影响行数
     */
    Integer insert(SpaceResourceDO spaceResourceDO);

    /**
     * 更新
     *
     * @param spaceResourceDO spaceResourceDO
     * @return 影响行数
     */
    Integer update(SpaceResourceDO spaceResourceDO);

    /**
     * 全量查询
     *
     * @param spaceId          业务空间Id
     * @param resourceTypeEnum 资源类型
     * @return 业务空间下某一类资源的全量数据
     */
    List<SpaceResourceDO> listBySpaceIdAndResourceType(Long spaceId, ResourceTypeEnum resourceTypeEnum);

    /**
     * 全量查询
     * @param spaceId           业务空间Id
     * @param resourceTypeEnum  资源类型
     * @return List<SpaceResourceDO>  业务空间下某一类资源的全量数据(带缓存)
     */
    List<SpaceResourceDO> listBySpaceIdAndResourceTypeWithCache(Long spaceId, ResourceTypeEnum resourceTypeEnum);

    /**
     * 根据类型全量查询
     *
     * @param resourceTypeEnum 资源类型
     * @return 查询拥有某一类资源的所有数据
     */
    List<SpaceResourceDO> listByResourceType(ResourceTypeEnum resourceTypeEnum);

    /**
     * 全量查询
     *
     * @param resourceTypeEnum 资源类型
     * @param resourceId       资源 Id
     * @return 拥有某一资源的所有数据
     */
    List<SpaceResourceDO> listByResourceTypeAndResourceId(ResourceTypeEnum resourceTypeEnum, String resourceId);

    /**
     * 全量查询
     * @param resourceTypeEnums 资源类型
     * @param resourceId 资源id
     * @return 拥有资源的所有数据
     */
    List<SpaceResourceDO> listByResourceTypesAndResourceId(List<ResourceTypeEnum> resourceTypeEnums, String resourceId);

    /**
     * 全量查询
     *
     * @param resourceTypeEnum 资源类型
     * @param resourceIdList   资源 Id
     * @return 拥有某一资源的所有数据
     */
    List<SpaceResourceDO> listByResourceTypeAndResourceIdList(ResourceTypeEnum resourceTypeEnum, List<String> resourceIdList);

    /**
     * 全量查询
     *
     * @param resourceTypeEnum 资源类型
     * @param resourceIdList   资源 Id
     * @param spaceId          业务空间 Id
     * @return 拥有某一资源的所有数据
     */
    List<SpaceResourceDO> listByResourceTypeAndSpaceIdAndResourceIdList(ResourceTypeEnum resourceTypeEnum, Long spaceId, List<String> resourceIdList);

    /**
     * 查询单条记录
     *
     * @param spaceId          业务空间Id
     * @param resourceTypeEnum 资源类型
     * @param resourceId       资源Id
     * @return 业务空间下某一具体资源
     */
    SpaceResourceDO getBySpaceIdAndResourceTypeAndResourceId(Long spaceId, ResourceTypeEnum resourceTypeEnum, String resourceId);

    /**
     * 删除单条记录
     *
     * @param spaceId          业务空间 Id
     * @param resourceId       资源Id
     * @param resourceTypeEnum 资源类型
     * @return 修改行数
     */
    Integer deleteBySpaceIdAndResourceIdAndResourceType(Long spaceId, String resourceId, ResourceTypeEnum resourceTypeEnum);

    /**
     * 删除业务空间下所有资源（谨慎调用）
     *
     * @param spaceId
     * @return
     */
    Integer deleteBySpaceId(Long spaceId);

}
