package com.alibaba.tripscrm.service.service.impl;

import com.alibaba.tripscrm.dal.model.domain.data.BizGroupInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.BizGroupInfoParam;
import com.alibaba.tripscrm.dal.mapper.tddl.BizGroupInfoMapper;
import com.alibaba.tripscrm.service.model.vo.BizGroupVO;
import com.alibaba.tripscrm.service.service.BizGroupService;
import com.alibaba.tripscrm.service.service.ma.WxPersonMaService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/31
 */
@Service
public class BizGroupServiceImpl implements BizGroupService {

    private final BizGroupInfoMapper bizGroupInfoMapper;

    @Autowired
    public BizGroupServiceImpl(BizGroupInfoMapper bizGroupInfoMapper) {
        this.bizGroupInfoMapper = bizGroupInfoMapper;
    }

    @Autowired
    private WxPersonMaService wxPersonMaService;

    @Override
    public List<BizGroupVO> listBizGroup() {
        BizGroupInfoParam bizGroupInfoParam = new BizGroupInfoParam();
        bizGroupInfoParam.appendOrderByClause(BizGroupInfoParam.OrderCondition.ID, BizGroupInfoParam.SortType.DESC);
        List<BizGroupInfoDO> bizGroupInfoDOS = bizGroupInfoMapper.selectByParam(bizGroupInfoParam);
        List<BizGroupVO> bizGroupVOS = new ArrayList<>();
        for (BizGroupInfoDO bizGroupInfoDO:bizGroupInfoDOS){
            BizGroupVO bizGroupVO = convert2VO(bizGroupInfoDO);
            bizGroupVO.setCount(wxPersonMaService.countByBizGroup(bizGroupVO.getId()));
            bizGroupVOS.add(bizGroupVO);
        }
        return bizGroupVOS;
    }

    @Override
    public int addBizGroup(BizGroupInfoDO bizGroupInfoDO) {
        BizGroupInfoParam bizGroupInfoParam = new BizGroupInfoParam();
        bizGroupInfoParam.createCriteria().andNameEqualTo(bizGroupInfoDO.getName());
        Long numExist = bizGroupInfoMapper.countByParam(bizGroupInfoParam);
        if (numExist > 0){
            //分组名已存在，返回错误码
            return -99;
        }
        return bizGroupInfoMapper.insert(bizGroupInfoDO);
    }

    @Override
    public int updateBizGroup(BizGroupInfoDO bizGroupInfoDO) {
        BizGroupInfoParam checkParam = new BizGroupInfoParam();
        checkParam.createCriteria().andNameEqualTo(bizGroupInfoDO.getName());
        List<BizGroupInfoDO> bizGroupInfoDOS = bizGroupInfoMapper.selectByParam(checkParam);
        if (CollectionUtils.isNotEmpty(bizGroupInfoDOS) && !bizGroupInfoDOS.get(0).getId().equals(bizGroupInfoDO.getId())){
            //分组名已存在，返回错误码
            return -99;
        }
        return bizGroupInfoMapper.updateByPrimaryKey(bizGroupInfoDO);
    }

    @Override
    public int delBizGroup(Long id) {
        BizGroupInfoParam bizGroupInfoParam = new BizGroupInfoParam();
        bizGroupInfoParam.createCriteria().andIdEqualTo(id);
        return bizGroupInfoMapper.deleteByParam(bizGroupInfoParam);
    }

    @Override
    public BizGroupInfoDO queryBizGroupById(Long id) {
        return bizGroupInfoMapper.selectByPrimaryKey(id);
    }


    @Override
    public BizGroupInfoDO queryBizGroupByName(String name) {
        BizGroupInfoParam bizGroupInfoParam = new BizGroupInfoParam();
        bizGroupInfoParam.createCriteria().andNameEqualTo(name);
        List<BizGroupInfoDO> bizGroupInfoDOS = bizGroupInfoMapper.selectByParam(bizGroupInfoParam);
        if (CollectionUtils.isEmpty(bizGroupInfoDOS)){
            return null;
        }
        return bizGroupInfoDOS.get(0);
    }

    private BizGroupVO convert2VO(BizGroupInfoDO bizGroupInfoDO){
        BizGroupVO bizGroupVO = new BizGroupVO();
        bizGroupVO.setId(bizGroupInfoDO.getId());
        bizGroupVO.setName(bizGroupInfoDO.getName());
        bizGroupVO.setCreatorId(bizGroupInfoDO.getCreatorId());
        bizGroupVO.setCreatorName(bizGroupInfoDO.getCreatorName());
        bizGroupVO.setLastOperatorId(bizGroupInfoDO.getLastOperatorId());
        bizGroupVO.setGmtCreate(bizGroupInfoDO.getGmtCreate());
        return bizGroupVO;
    }

    private BizGroupInfoDO convert2DO(BizGroupVO bizGroupVO){
        BizGroupInfoDO bizGroupInfoDO = new BizGroupInfoDO();
        bizGroupInfoDO.setId(bizGroupVO.getId());
        bizGroupInfoDO.setName(bizGroupVO.getName());
        bizGroupInfoDO.setCreatorId(bizGroupVO.getCreatorId());
        bizGroupInfoDO.setCreatorName(bizGroupVO.getCreatorName());
        bizGroupInfoDO.setLastOperatorId(bizGroupVO.getLastOperatorId());
        bizGroupInfoDO.setLastOpeartorName(bizGroupVO.getLastOperatorName());
        return bizGroupInfoDO;
    }

}
