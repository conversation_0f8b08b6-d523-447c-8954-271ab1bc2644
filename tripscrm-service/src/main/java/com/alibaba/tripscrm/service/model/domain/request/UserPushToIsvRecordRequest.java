package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.io.Serializable;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Data
public class UserPushToIsvRecordRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    private Long id;

    /**
     *   ISV的code，区分是给谁了，第一次是baiying
     */
    private String isvCode;

    /**
     *   用户淘宝账号id
     */
    private Long userId;

    /**
     *   加密后的用户淘宝账号id
     */
    private String userIdEncrypt;

    /**
     *   用户淘宝账号id关联的手机号
     */
    private String cellPhone;

    /**
     *   加密后的用户淘宝账号id关联的手机号
     */
    private String cellPhoneEncrypt;

    /**
     *   com.alibaba.tripscrm.service.enums.baiying.PushStageEnum
     */
    private Byte crowdType;

    /**
     *   引导加企微的方式，0 实物引导 1 券权益引导
     */
    private Byte guideType;

    /**
     * Database Column Remarks:
     *   0 初始化，1 外推成功,  2 外推失败
     *
     *
     * @mbg.generated
     */
    private Byte pushStage;

    /**
     * Database Column Remarks:
     *   0 初始化，1 外呼解密成功，2 外呼成功,  3 外呼失败
     *
     *
     * @mbg.generated
     */
    private Byte callStage;

    /**
     * Database Column Remarks:
     *   0 初始化， 1 短信解密成功，2 短信发送成功， 3 短信发送失败
     *
     *
     * @mbg.generated
     */
    private Byte msgStage;

    /**
     * Database Column Remarks:
     *   0 初始化， 1 进入加企微承接页，2 加企微成功
     *
     *
     * @mbg.generated
     */
    private Byte joinWxStage;

    /**
     *   删除状态，0正常 1删除
     */
    private Byte status;
}