package com.alibaba.tripscrm.service.service.task.ability.executor.impl.seller;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskAsyncRequestDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.seller.SellerWorkScheduleInfoService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.request.GroupInviteJoinRequest;
import com.alibaba.tripzoo.proxy.result.GroupInviteJoinResponse;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 初始化客户-商家沟通群聊成员任务
 *
 * <AUTHOR>
 * @since 2025/4/9 16:50
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class InitialCustomerSellerGroupMemberTaskExecutor extends AbstractTaskExecutor {
    private final SellerWorkScheduleInfoService sellerWorkScheduleInfoService;
    private final TaskExecuteRecordService taskExecuteRecordService;
    private final LdbTairManager ldbTairManager;
    private final GroupService groupService;
    private final WechatGroupService wechatGroupService;
    private final MetaqProducer metaqProducer;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        String userId = MapUtils.getString(context.getExtInfo(), "userId");
        String chatId = MapUtils.getString(context.getExtInfo(), "chatId");
        String externalUserId = MapUtils.getString(context.getExtInfo(), "externalUserId");
        String sellerId = MapUtils.getString(context.getExtInfo(), "sellerId");

        if (StringUtils.isBlank(externalUserId) || StringUtils.isBlank(sellerId) || StringUtils.isBlank(userId) || StringUtils.isBlank(chatId)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<String> sellerFollowUserIdList = getSellerFollowUserIdList(context, todoTaskVO);
        GroupInviteJoinRequest groupInviteJoinRequest = new GroupInviteJoinRequest();
        groupInviteJoinRequest.setUserId(userId);
        groupInviteJoinRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        groupInviteJoinRequest.setChatId(chatId);
        groupInviteJoinRequest.setExternalUserIdList(Collections.singletonList(externalUserId));
        groupInviteJoinRequest.setUserIdList(sellerFollowUserIdList);

        TripSCRMResult<String> tripSCRMResult = sendAsyncRequest(context, todoTaskVO, () -> {
            ResultDO<GroupInviteJoinResponse> resultDO = groupService.asyncInviteJoinGroup(groupInviteJoinRequest);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
                PlatformLogUtil.logFail("初始化客户-商家沟通群聊成员任务执行，邀请入群群聊失败", TripSCRMErrorCode.INVITE_JOIN_GROUP_FAIL.getCode(), LogListUtil.newArrayList(resultDO));
                DingTalkApi.sendTaskMessage(String.format("【初始化客户-商家沟通群聊成员】邀请入群群聊失败！！，企微成员userId：%s，客户externalUserId：%s，商家Id：%s", userId, externalUserId, sellerId));
                return TripSCRMResult.fail(TripSCRMErrorCode.INVITE_JOIN_GROUP_FAIL);
            }

            if (CollectionUtils.isNotEmpty(resultDO.getModel().getFailUserIdList()) || CollectionUtils.isNotEmpty(resultDO.getModel().getFailExternalUserIdList())) {
                PlatformLogUtil.logFail("初始化客户-商家沟通群聊成员任务执行，部分成员邀请入群群聊失败", TripSCRMErrorCode.INVITE_JOIN_GROUP_FAIL.getCode(), LogListUtil.newArrayList(resultDO));
                DingTalkApi.sendTaskMessage(String.format("【初始化客户-商家沟通群聊成员】部分成员邀请入群群聊失败！！，企微成员userId：%s，客户externalUserId：%s，商家Id：%s", userId, externalUserId, sellerId));
                return TripSCRMResult.fail(TripSCRMErrorCode.INVITE_JOIN_GROUP_FAIL);
            }
            return TripSCRMResult.success(resultDO.getModel().getRequestId());
        });

        if (!tripSCRMResult.isSuccess()) {
            throw new TripscrmException(tripSCRMResult.getCode());
        }
    }

    private List<String> getSellerFollowUserIdList(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        // 上下文缓存取
        if (taskDataBody.getContext().containsKey("sellerFollowUserIdList")) {
            return (List<String>) taskDataBody.getContext().get("sellerFollowUserIdList");
        }

        String userId = MapUtils.getString(context.getExtInfo(), "userId");
        String sellerId = MapUtils.getString(context.getExtInfo(), "sellerId");
        String externalUserId = MapUtils.getString(context.getExtInfo(), "externalUserId");
        List<String> wechatUserIdList = Arrays.stream(context.getTaskInfoDOSnapshot().getSendUserId().split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        TripSCRMResult<List<String>> wechatUserIdListResult = sellerWorkScheduleInfoService.queryScheduleWechatUserIdList(sellerId, wechatUserIdList, new Date());
        if (Objects.isNull(wechatUserIdListResult) || !wechatUserIdListResult.isSuccess() || CollectionUtils.isEmpty(wechatUserIdListResult.getData())) {
            PlatformLogUtil.logFail("初始化客户-商家沟通群聊成员任务执行，商家当前没有值班账号", TripSCRMErrorCode.SELLER_NO_WORK_WECHAT_USER.getCode(), LogListUtil.newArrayList(wechatUserIdListResult));
            DingTalkApi.sendTaskMessage(String.format("【初始化客户-商家沟通群聊成员】商家当前没有值班账号！！，企微成员userId：%s，客户externalUserId：%s，商家Id：%s", userId, externalUserId, sellerId));
            throw new TripscrmException(TripSCRMErrorCode.SELLER_NO_WORK_WECHAT_USER);
        }

        taskDataBody.getContext().put("sellerFollowUserIdList", wechatUserIdListResult.getData());
        return wechatUserIdListResult.getData();
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return context.getTaskInfoDOSnapshot().getSendUserId();
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_UNION_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("目标Id为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        return taskDataBody.getTargetId();
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.INITIAL_CUSTOMER_SELLER_GROUP_MEMBER;
    }

    @Override
    protected Map<String, String> getTaskAsyncExecuteExtraInfo(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);

        ActivityTaskInfoBO targetActivityContext = getTargetActivityContext(context, todoTaskVO.getData().get(0));
        String contextId = MapUtils.getString(targetActivityContext.getExtraJson(), "contextId");
        Map<String, String> paramMap = targetActivityContext.getExtraJson().getObject("paramMap", new TypeReference<HashMap<String, String>>() {
        });
        String channelId = MapUtils.getString(paramMap, "channelId");
        String unionId = getFinalTargetId(context, taskDataBody);
        String externalUserId = MapUtils.getString(context.getExtInfo(), "externalUserId");
        String userId = MapUtils.getString(context.getExtInfo(), "userId");
        String sellerId = MapUtils.getString(context.getExtInfo(), "sellerId");
        Integer itemType = MapUtils.getInteger(context.getExtInfo(), "itemType");
        String itemId = MapUtils.getString(context.getExtInfo(), "itemId");
        String itemName = MapUtils.getString(context.getExtInfo(), "itemName");
        String chatId = MapUtils.getString(context.getExtInfo(), "chatId");

        Map<String, String> result = Maps.newHashMap();
        result.put("sellerId", sellerId);
        result.put("userId", userId);
        result.put("channelId", channelId);
        result.put("unionId", unionId);
        result.put("externalUserId", externalUserId);
        result.put("chatId", chatId);
        result.put("itemType", String.valueOf(itemType));
        result.put("itemId", itemId);
        result.put("itemName", itemName);
        result.put("sellerFollowUserId", getSellerFollowUserIdList(context, todoTaskVO).get(0));
        result.put("activityId", String.valueOf(context.getTaskInfoDOSnapshot().getActivityId()));
        result.put("contextId", String.valueOf(contextId));
        return result;
    }

    @Override
    public void updateAsyncExecuteResult(Long recordId, ScrmCallbackMsg scrmCallbackMsg) {
        TaskExecuteRecordBO taskExecuteRecordBO = taskExecuteRecordService.queryByRecordId(recordId);
        if (Objects.isNull(taskExecuteRecordBO)) {
            PlatformLogUtil.logFail("初始化客户-商家沟通群聊成员任务，更新任务异步执行结果，查询任务执行明细为空", LogListUtil.newArrayList(recordId));
            return;
        }

        boolean firstCallback = Lists.newArrayList(TaskSingleDataExecuteResultEnum.SUCCESS).contains(taskExecuteRecordBO.getStatusEnum());
        // 第一个回调，作为异步执行结果
        if (firstCallback) {
            taskExecuteRecordBO.setStatusEnum(scrmCallbackMsg.getResult() ? TaskSingleDataExecuteResultEnum.ASYNC_SUCCESS : TaskSingleDataExecuteResultEnum.ASYNC_FAIL);
            PlatformLogUtil.logInfo("初始化客户-商家沟通群聊成员任务，更新任务异步执行结果，首次更新异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        }

        JSONObject extraInfo = taskExecuteRecordBO.getExtraInfo();
        List<String> asyncExecuteResultList = extraInfo.containsKey("asyncExecuteResult") ? extraInfo.getObject("asyncExecuteResult", new TypeReference<List<String>>() {
        }) : new ArrayList<>();
        if (StringUtils.isNotBlank(scrmCallbackMsg.getContent())) {
            asyncExecuteResultList.add(scrmCallbackMsg.getContent());
            extraInfo.put("asyncExecuteResultList", asyncExecuteResultList);
        }
        if (StringUtils.isNotBlank(scrmCallbackMsg.getMessage())) {
            extraInfo.put("asyncExecuteResultMessage", scrmCallbackMsg.getMessage());
        }
        taskExecuteRecordBO.setExtraInfo(extraInfo);
        PlatformLogUtil.logInfo("初始化客户-商家沟通群聊成员任务，更新任务异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        taskExecuteRecordService.upsert(taskExecuteRecordBO);

        if (!firstCallback) {
            return;
        }

        TaskAsyncRequestDataVO taskAsyncRequestDataVO = JSONObject.parseObject(ldbTairManager.get(TairConstant.LDB_PROXY_REQUEST_DATA_PREFIX + scrmCallbackMsg.getRequestId()).toString(), new TypeReference<TaskAsyncRequestDataVO>() {
        });
        Map<String, String> requestDataExtraInfo = taskAsyncRequestDataVO.getExtraInfo();
        String sellerId = MapUtils.getString(requestDataExtraInfo, "sellerId");
        String channelId = MapUtils.getString(requestDataExtraInfo, "channelId");
        String userId = MapUtils.getString(requestDataExtraInfo, "userId");
        String unionId = MapUtils.getString(requestDataExtraInfo, "unionId");
        String externalUserId = MapUtils.getString(requestDataExtraInfo, "externalUserId");
        String chatId = MapUtils.getString(requestDataExtraInfo, "chatId");
        Integer itemType = MapUtils.getInteger(requestDataExtraInfo, "itemType");
        String itemId = MapUtils.getString(requestDataExtraInfo, "itemId");
        String itemName = MapUtils.getString(requestDataExtraInfo, "itemName");
        Long activityId = MapUtils.getLong(requestDataExtraInfo, "activityId");
        String contextId = MapUtils.getString(requestDataExtraInfo, "contextId");
        String sellerFollowUserId = MapUtils.getString(requestDataExtraInfo, "sellerFollowUserId");

        if (!scrmCallbackMsg.getResult()) {
            DingTalkApi.sendTaskMessage(String.format("【初始化客户-商家沟通群聊成员】存在邀请失败的用户！！，企微成员userId：%s，客户externalUserId：%s，商家Id：%s", userId, externalUserId, sellerId));
            return;
        }

        WechatGroupDO wechatGroup = wechatGroupService.getByChatId(chatId);
        JSONObject message = new JSONObject();
        message.put("sellerId", sellerId);
        message.put("unionId", unionId);
        message.put("userId", userId);
        message.put("externalUserId", externalUserId);
        message.put("chatId", chatId);
        message.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
        message.put("itemType", itemType);
        message.put("itemId", itemId);
        message.put("itemName", itemName);
        message.put("sellerFollowUserId", sellerFollowUserId);
        message.put("contextId", contextId);
        message.put("activityId", activityId);
        message.put("channelId", channelId);
        message.put("messageContent", String.format("已为您拉商家咨询群「%s」，请移步群内沟通，感谢您的理解", wechatGroup.getName()));
        metaqProducer.send(MQEnum.SCRM_INVITE_SELLER_AND_CUSTOMER_JOIN_GROUP_SUCCESS, null, null, message.toJSONString(), MetaQDeleyLevel.LEVEL_2.getLevel());
        metaqProducer.send(MQEnum.SCRM_INVITE_SELLER_CUSTOMER_JOIN_GROUP_SUCCESS_FOR_SINGLE_CHAT, null, null, message.toJSONString(), MetaQDeleyLevel.LEVEL_2.getLevel());
    }
}
