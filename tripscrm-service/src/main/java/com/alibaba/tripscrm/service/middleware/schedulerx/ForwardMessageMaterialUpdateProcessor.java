package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.service.model.vo.material.MaterialInfoVO;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * @description：转发消息定时删除任务
 * @Author：wangrui
 * @create：2024/12/17 下午8:53
 * @Filename：ForwardMessageMaterialUpdateProcessor
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ForwardMessageMaterialUpdateProcessor extends JavaProcessor  {
    MaterialService materialService;

    @Override
    @AteyeInvoker(description = "转发消息定时删除任务", paraDesc = "JobContext")
    public ProcessResult process(JobContext context) throws Exception {

        MaterialInfoVO queryVO = new MaterialInfoVO();


        return new ProcessResult(true);
    }

}
