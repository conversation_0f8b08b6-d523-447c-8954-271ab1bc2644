package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fusionchat.FusionChatService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.UpdateUserLockRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.UpdateUserLockResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.List;

/**
 * 锁定/解锁企微成员账号 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/13
 */
@Service
public class UpdateUserLockProcessor implements WsEventProcessor {
    @Resource
    private FusionChatService fusionChatService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private WechatUserService wechatUserService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.UPDATE_USER_LOCK;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        UpdateUserLockRequest request = wsEvent.getData().toJavaObject(UpdateUserLockRequest.class);
        if (wsEvent.getUserId() == null) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        User account = wsContextInfo.getAccount();
        Long spaceId = wsContextInfo.getSpaceId();
        if (!request.getLock() && !wechatUserService.checkHasLock(wsEvent.getUserId(), account.getUserId(), spaceId)) {
            throw new TripscrmException("未锁定当前企微账号，无法执行该操作");
        }
        // 锁定/解锁企微成员账号
        fusionChatService.lockUser(account, spaceId, wsEvent.getUserId(), request.getLock());
        // 寻找当前企微号有关系的account列表，推送到websocket
        List<String> accountIds = wechatUserService.listAccountIdWithPermission(wsEvent.getUserId(), spaceId);
        UpdateUserLockResponse response = new UpdateUserLockResponse();
        if (request.getLock()) {
            response.setLockUser(account.getUserId());
            response.setLockUserName(account.getUserName());
        }
        response.setLock(request.getLock());
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        webSocketFactory.pushMessageByDistributedWithAccount(wsEvent, accountIds);
    }

    /**
     * 企微号解锁
     *
     * @param userId  userId
     * @param spaceId spaceId
     */
    public Boolean unlockUser(String userId, Long spaceId) {
        try {
            wechatUserService.unlock(null, userId, null, spaceId, true);
        } catch (Exception e) {
            PlatformLogUtil.logException("企微号解锁失败", e.getMessage(), e, LogListUtil.newArrayList(userId, spaceId));
            return false;
        }
        // 寻找当前企微号有关系的account列表，推送到websocket
        List<String> accountIds = wechatUserService.listAccountIdWithPermission(userId, spaceId);
        // 根据accountId推送解锁 推送到websocket
        UpdateUserLockResponse response = new UpdateUserLockResponse();
        response.setLock(false);
        WsEvent wsEvent = new WsEvent();
        wsEvent.setUserId(userId);
        wsEvent.setType(WsEventTypeEnum.UPDATE_USER_LOCK.getValue());
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        webSocketFactory.pushMessageByDistributedWithAccount(wsEvent, accountIds);
        return true;
    }
}
