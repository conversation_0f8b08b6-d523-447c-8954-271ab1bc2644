package com.alibaba.tripscrm.service.service.strategy.message;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.message.MessageInfoDTO;
import com.alibaba.tripscrm.service.model.dto.message.MessageTransferDTO;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 素材内容转换器
 *
 * <AUTHOR>
 * @date 2023-12-30 14:50:08
 */
@Component
public abstract class AbstractMessageConverter<T extends MessageInfoDTO> {

    /**
     * 转化为消息BO对象
     * @param messageResourceInfo 素材json信息（原始数据）
     * @param relationDTO 素材-trackId关系（数据统计使用）
     * @param context 素材上下文
     * @param taskType 任务类型 （某些任务场景需要定制化判断）
     * @param messageType 消息类型（不同消息不同处理）
     * @param msgIndex 消息下标
     * @return 消息BO对象
     */
    public List<MessageBO> convertMessageBO(JSONObject messageResourceInfo, MaterialTrackRelationDTO relationDTO,
                                            MaterialContentConvertContext context, TaskType taskType,
                                            MessageTypeEnum messageType, Integer msgIndex) {
        ArrayList<MessageBO> messageBOList = new ArrayList<>();
        if (messageResourceInfo == null) {
            PlatformLogUtil.logFail("转化为消息BO对象，消息入参为空", LogListUtil.newArrayList(relationDTO.getMaterialId(), taskType, messageType));
            return messageBOList;
        }
        return buildMessageBO(buildMessageInfoDTO(messageResourceInfo, relationDTO, taskType, messageType, msgIndex), context, msgIndex);
    }

    /**
     * 转化为微信消息BO对象
     * @param messageResourceInfo 素材json信息（原始数据）
     * @param relationDTO 素材-trackId关系（数据统计使用）
     * @param context 素材上下文
     * @param taskType 任务类型 （某些任务场景需要定制化判断）
     * @param messageType 消息类型（不同消息不同处理）
     * @param msgIndex 消息下标
     * @return 微信消息BO对象
     */
    public List<WxMessageBO> convertWxMessageBO(JSONObject messageResourceInfo, MaterialTrackRelationDTO relationDTO,
                                                MaterialContentConvertContext context, TaskType taskType,
                                                MessageTypeEnum messageType, Integer msgIndex) {
        ArrayList<WxMessageBO> messageBOList = new ArrayList<>();
        if (messageResourceInfo == null) {
            PlatformLogUtil.logFail("转化为微信消息BO对象，消息入参为空", LogListUtil.newArrayList(relationDTO.getMaterialId(), taskType, messageType));
            return messageBOList;
        }
        return buildWxMessageBO(buildMessageInfoDTO(messageResourceInfo, relationDTO, taskType, messageType, msgIndex), context, taskType);
    }

    /**
     * 构建信息转化对象
     * @param messageResourceInfo 单个类型的信息JSONObject
     * @param relationDTO 素材-track_id关系
     * @param taskType 任务类型
     * @param msgIndex 消息下标
     * @param messageType 消息类型
     * @return 消息转化对象
     */
    T buildMessageInfoDTO(JSONObject messageResourceInfo, MaterialTrackRelationDTO relationDTO,
                                               TaskType taskType, MessageTypeEnum messageType, Integer msgIndex) {
        MessageTransferDTO messageTransferDTO = new MessageTransferDTO();
        messageTransferDTO.setMessageType(messageType);
        relationDTO.setMsgIndexId(msgIndex);
        messageTransferDTO.setMaterialTrackRelationDTO(relationDTO);
        messageTransferDTO.setTaskType(taskType);
        messageTransferDTO.setMsgIndex(msgIndex);
        messageTransferDTO.setContent(messageResourceInfo);
        return convert(messageTransferDTO);
    }

    /**
     * 转换素材内容(json -> DTO)
     * @param messageTransferDTO 消息转化DTO
     * @return 消息DTO对象
     */
    abstract T convert(MessageTransferDTO messageTransferDTO);

    /**
     * 构建消息BO对象
     * @param messageInfoDTO 消息信息对象
     * @param context 素材上下文
     * @param messageIndex 消息下标
     * @return 消息BO对象
     */
    abstract List<MessageBO> buildMessageBO(T messageInfoDTO, MaterialContentConvertContext context, Integer messageIndex);

    /**
     * 构建微信BO对象
     * @param messageInfoDTO 消息信息对象
     * @param context 素材上下文
     * @param taskType 任务类型
     * @return 微信消息对象
     */
    abstract List<WxMessageBO> buildWxMessageBO(T messageInfoDTO, MaterialContentConvertContext context, TaskType taskType);

    /**
     * 处理的消息类型
     * @return 消息类型
     */
    public abstract MessageTypeEnum getMsgType();
}
