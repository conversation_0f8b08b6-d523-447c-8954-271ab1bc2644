package com.alibaba.tripscrm.service.middleware.diamond;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * 任务odps的sql配置
 */
@Slf4j
@DiamondListener(dataId = "task_odps_sql_config.json")
public class TaskOdpsSqlConfigDiamond implements DiamondDataCallback {

    protected static Map<Long, String> odpsSqlMap;

    @Override
    public void received(String data) {
        try {
            Map<Long, String> newOdpsSqlMap = Maps.newHashMap();
            JSONObject config = JSONObject.parseObject(data);
            for (String taskId : config.keySet()) {
                newOdpsSqlMap.put(Long.valueOf(taskId), config.getString(taskId));
            }
            odpsSqlMap = newOdpsSqlMap;
        } catch (Exception e) {
            PlatformLogUtil.logException("任务odps的sql配置监听消息处理失败", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    /**
     * 获取任务的odps表配置sql
     * @param taskId
     * @return
     */
    public static String getOdpsSql(Long taskId) {
        if(odpsSqlMap == null || MapUtils.isEmpty(odpsSqlMap)) {
            PlatformLogUtil.logFail("获取任务的odps表配置sql失败", LogListUtil.newArrayList(taskId));
            return null;
        }
        return odpsSqlMap.get(taskId);
    }

}
