package com.alibaba.tripscrm.service.repository.impl.crowd;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.tddl.CrowdTagConvertTaskMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CrowdTagConvertTaskDO;
import com.alibaba.tripscrm.dal.model.domain.query.CrowdTagConvertTaskParam;
import com.alibaba.tripscrm.service.convert.CrowdTagConvertTaskConverter;
import com.alibaba.tripscrm.service.model.domain.query.CrowdTagConvertTaskQuery;
import com.alibaba.tripscrm.service.model.dto.crowd.CrowdTagConvertTaskDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.repository.crowd.CrowdTagConvertTaskRepository;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 人群标签转换任务Repository实现类
 *
 * <AUTHOR>
 * @create 2025/10/11 17:42
 */
@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CrowdTagConvertTaskRepositoryImpl implements CrowdTagConvertTaskRepository {

    private final CrowdTagConvertTaskMapper crowdTagConvertTaskMapper;
    private final CrowdTagConvertTaskConverter crowdTagConvertTaskConverter;

    /**
     * 查询人群标签转换任务
     *
     * @param query 查询条件
     * @return 任务列表
     */
    @Override
    public List<CrowdTagConvertTaskDTO> select(CrowdTagConvertTaskQuery query) {
        CrowdTagConvertTaskParam param = buildParam(query);
        List<CrowdTagConvertTaskDO> crowdTagConvertTaskDOList = crowdTagConvertTaskMapper.selectByParam(param);

        if (CollectionUtils.isEmpty(crowdTagConvertTaskDOList)) {
            return Collections.emptyList();
        }

        List<CrowdTagConvertTaskDTO> result = crowdTagConvertTaskConverter.convert2DTOs(crowdTagConvertTaskDOList);
        return result;
    }

    /**
     * 根据ID查询人群标签转换任务
     *
     * @param id 任务ID
     * @return 任务信息
     */
    @Override
    public CrowdTagConvertTaskDTO selectById(Long id) {

        if (Objects.isNull(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务ID不能为空");
        }

        CrowdTagConvertTaskDO crowdTagConvertTaskDO = crowdTagConvertTaskMapper.selectByPrimaryKey(id);
        if (Objects.isNull(crowdTagConvertTaskDO)) {
            return null;
        }

        CrowdTagConvertTaskDTO result = crowdTagConvertTaskConverter.convert2DTO(crowdTagConvertTaskDO);

        return result;

    }

    /**
     * 插入人群标签转换任务
     *
     * @param crowdTagConvertTaskDTO 任务信息
     * @return 影响行数
     */
    @Override
    public Integer insert(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO) {

        if (Objects.isNull(crowdTagConvertTaskDTO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务信息不能为空");
        }

        CrowdTagConvertTaskDO crowdTagConvertTaskDO = crowdTagConvertTaskConverter.convert2DO(crowdTagConvertTaskDTO);
        int result = crowdTagConvertTaskMapper.insert(crowdTagConvertTaskDO);
        return result;

    }

    /**
     * 选择性插入人群标签转换任务
     *
     * @param crowdTagConvertTaskDTO 任务信息
     * @return 影响行数
     */
    @Override
    public Integer insertSelective(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO) {

        if (Objects.isNull(crowdTagConvertTaskDTO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务信息不能为空");
        }

        CrowdTagConvertTaskDO crowdTagConvertTaskDO = crowdTagConvertTaskConverter.convert2DO(crowdTagConvertTaskDTO);
        int result = crowdTagConvertTaskMapper.insertSelective(crowdTagConvertTaskDO);

        return result;

    }

    /**
     * 批量插入人群标签转换任务
     *
     * @param insertList 任务列表
     * @return 影响行数
     */
    @Override
    public Integer batchInsert(List<CrowdTagConvertTaskDTO> insertList) {

        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }

        int totalCount = 0;
        for (CrowdTagConvertTaskDTO crowdTagConvertTaskDTO : insertList) {
            if (Objects.nonNull(crowdTagConvertTaskDTO)) {
                CrowdTagConvertTaskDO crowdTagConvertTaskDO = crowdTagConvertTaskConverter.convert2DO(crowdTagConvertTaskDTO);
                totalCount += crowdTagConvertTaskMapper.insertSelective(crowdTagConvertTaskDO);
            }
        }

        return totalCount;

    }

    /**
     * 更新人群标签转换任务
     *
     * @param crowdTagConvertTaskDTO 任务信息
     * @param query                  更新条件
     * @return 影响行数
     */
    @Override
    public Integer updateSelective(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO, CrowdTagConvertTaskQuery query) {

        if (Objects.isNull(crowdTagConvertTaskDTO) || Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务信息和更新条件不能为空");
        }

        CrowdTagConvertTaskParam param = buildParam(query);
        CrowdTagConvertTaskDO crowdTagConvertTaskDO = crowdTagConvertTaskConverter.convert2DO(crowdTagConvertTaskDTO);

        int result = crowdTagConvertTaskMapper.updateByParamSelective(crowdTagConvertTaskDO, param);

        return result;

    }

    /**
     * 根据ID更新人群标签转换任务
     *
     * @param crowdTagConvertTaskDTO 任务信息
     * @return 影响行数
     */
    @Override
    public Integer updateByIdSelective(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO) {

        if (Objects.isNull(crowdTagConvertTaskDTO) || Objects.isNull(crowdTagConvertTaskDTO.getId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务信息和任务ID不能为空");
        }

        CrowdTagConvertTaskDO crowdTagConvertTaskDO = crowdTagConvertTaskConverter.convert2DO(crowdTagConvertTaskDTO);
        int result = crowdTagConvertTaskMapper.updateByPrimaryKeySelective(crowdTagConvertTaskDO);

        return result;

    }

    /**
     * 删除人群标签转换任务
     *
     * @param query 删除条件
     * @return 影响行数
     */
    @Override
    public Integer delete(CrowdTagConvertTaskQuery query) {

        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "删除条件不能为空");
        }

        CrowdTagConvertTaskParam param = buildParam(query);
        int result = crowdTagConvertTaskMapper.deleteByParam(param);

        return result;

    }

    /**
     * 根据ID删除人群标签转换任务
     *
     * @param id 任务ID
     * @return 影响行数
     */
    @Override
    public Integer deleteById(Long id) {

        if (!NumberUtils.biggerThanZero( id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务ID不能为空");
        }
        CrowdTagConvertTaskDO crowdTagConvertTaskDO = crowdTagConvertTaskMapper.selectByPrimaryKey(id);
        crowdTagConvertTaskDO.setIsDelete((byte) 1);
        int result = crowdTagConvertTaskMapper.updateByPrimaryKeySelective(crowdTagConvertTaskDO);
        return result;

    }

    /**
     * 统计人群标签转换任务数量
     *
     * @param query 查询条件
     * @return 任务数量
     */
    @Override
    public Long count(CrowdTagConvertTaskQuery query) {

        CrowdTagConvertTaskParam param = buildParam(query);
        long result = crowdTagConvertTaskMapper.countByParam(param);
        return result;

    }

    /**
     * 分页查询人群标签转换任务
     *
     * @param query 查询条件（包含分页参数）
     * @return 分页结果
     */
    @Override
    public PageInfo<CrowdTagConvertTaskDTO> selectWithPage(CrowdTagConvertTaskQuery query) {
        // 参数校验
        if (Objects.isNull(query) || Objects.isNull(query.getPageNum()) || Objects.isNull(query.getPageSize())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "查询条件和分页参数不能为空");
        }

        // 构建查询参数（包含分页设置）
        CrowdTagConvertTaskParam param = buildParam(query);

        // 执行分页查询
        List<CrowdTagConvertTaskDO> crowdTagConvertTaskDOList = crowdTagConvertTaskMapper.selectByParam(param);

        // 查询总数 - 构建新的查询参数避免分页影响
        CrowdTagConvertTaskQuery countQuery = new CrowdTagConvertTaskQuery();
        // 复制查询条件但不包含分页参数
        copyQueryWithoutPagination(query, countQuery);
        CrowdTagConvertTaskParam countParam = buildParam(countQuery);
        long total = crowdTagConvertTaskMapper.countByParam(countParam);

        // 转换为DTO
        List<CrowdTagConvertTaskDTO> result = Collections.emptyList();
        if (!CollectionUtils.isEmpty(crowdTagConvertTaskDOList)) {
            result = crowdTagConvertTaskConverter.convert2DTOs(crowdTagConvertTaskDOList);
        }

        // 构建分页信息
        PageInfo<CrowdTagConvertTaskDTO> pageInfo = new PageInfo<>(result);
        pageInfo.setPageNum(query.getPageNum());
        pageInfo.setPageSize(query.getPageSize());
        pageInfo.setTotal(total);
        pageInfo.setHasNextPage(query.getPageNum() < pageInfo.getPages());
        return pageInfo;
    }

    /**
     * 复制查询条件但不包含分页参数
     *
     * @param source 源查询条件
     * @param target 目标查询条件
     */
    private void copyQueryWithoutPagination(CrowdTagConvertTaskQuery source, CrowdTagConvertTaskQuery target) {
        target.setId(source.getId());
        target.setIdList(source.getIdList());
        target.setSourcePlatform(source.getSourcePlatform());
        target.setSourcePlatformList(source.getSourcePlatformList());
        target.setTargetPlatform(source.getTargetPlatform());
        target.setTargetPlatformList(source.getTargetPlatformList());
        target.setTargetCrowdId(source.getTargetCrowdId());
        target.setTargetCrowdIdList(source.getTargetCrowdIdList());
        target.setTargetCrowdNameLike(source.getTargetCrowdNameLike());
        target.setSourceCrowdId(source.getSourceCrowdId());
        target.setSourceCrowdIdList(source.getSourceCrowdIdList());
        target.setSourceCrowdNameLike(source.getSourceCrowdNameLike());
        target.setFirstLevelTag(source.getFirstLevelTag());
        target.setFirstLevelTagList(source.getFirstLevelTagList());
        target.setSecondLevelTag(source.getSecondLevelTag());
        target.setSecondLevelTagList(source.getSecondLevelTagList());
        target.setStatus(source.getStatus());
        target.setStatusList(source.getStatusList());
        target.setExecuteType(source.getExecuteType());
        target.setExecuteTypeList(source.getExecuteTypeList());
        target.setOperatorId(source.getOperatorId());
        target.setOperatorIdList(source.getOperatorIdList());
        target.setGmtCreateStart(source.getGmtCreateStart());
        target.setGmtCreateEnd(source.getGmtCreateEnd());
        target.setGmtModifiedStart(source.getGmtModifiedStart());
        target.setGmtModifiedEnd(source.getGmtModifiedEnd());
        target.setLastExecuteTimeStart(source.getLastExecuteTimeStart());
        target.setLastExecuteTimeEnd(source.getLastExecuteTimeEnd());
        target.setStartDateStart(source.getStartDateStart());
        target.setStartDateEnd(source.getStartDateEnd());
        target.setEndDateStart(source.getEndDateStart());
        target.setEndDateEnd(source.getEndDateEnd());
        // 不复制分页参数 pageNum 和 pageSize
    }

    /**
     * 构建查询参数
     *
     * @param query 查询条件
     * @return 查询参数
     */
    private CrowdTagConvertTaskParam buildParam(CrowdTagConvertTaskQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "查询条件不能为空");
        }

        CrowdTagConvertTaskParam param = new CrowdTagConvertTaskParam();
        CrowdTagConvertTaskParam.Criteria criteria = param.or();

        // 处理分页参数
        if (Objects.nonNull(query.getPageNum()) && Objects.nonNull(query.getPageSize())) {
            param.setPage(true);
            param.setPageStart(query.getPageNum());
            param.setPageSize(query.getPageSize());
        }

        // ID条件
        if (Objects.nonNull(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (!CollectionUtils.isEmpty(query.getIdList())) {
            criteria.andIdIn(query.getIdList());
        }

        // 源平台条件
        if (Objects.nonNull(query.getSourcePlatform())) {
            criteria.andSourcePlatformEqualTo(query.getSourcePlatform());
        }
        if (!CollectionUtils.isEmpty(query.getSourcePlatformList())) {
            criteria.andSourcePlatformIn(query.getSourcePlatformList());
        }

        // 目标平台条件
        if (Objects.nonNull(query.getTargetPlatform())) {
            criteria.andTargetPlatformEqualTo(query.getTargetPlatform());
        }
        if (!CollectionUtils.isEmpty(query.getTargetPlatformList())) {
            criteria.andTargetPlatformIn(query.getTargetPlatformList());
        }

        // 目标人群ID条件
        if (StringUtils.hasText(query.getTargetCrowdId())) {
            criteria.andTargetCrowdIdEqualTo(query.getTargetCrowdId());
        }
        if (!CollectionUtils.isEmpty(query.getTargetCrowdIdList())) {
            criteria.andTargetCrowdIdIn(query.getTargetCrowdIdList());
        }

        // 目标人群名称模糊查询
        if (StringUtils.hasText(query.getTargetCrowdNameLike())) {
            criteria.andTargetCrowdNameLike("%" + query.getTargetCrowdNameLike() + "%");
        }

        // 源人群ID条件
        if (StringUtils.hasText(query.getSourceCrowdId())) {
            criteria.andSourceCrowdIdEqualTo(query.getSourceCrowdId());
        }
        if (!CollectionUtils.isEmpty(query.getSourceCrowdIdList())) {
            criteria.andSourceCrowdIdIn(query.getSourceCrowdIdList());
        }

        // 源人群名称模糊查询
        if (StringUtils.hasText(query.getSourceCrowdNameLike())) {
            criteria.andSourceCrowdNameLike("%" + query.getSourceCrowdNameLike() + "%");
        }

        // 一级标签条件
        if (StringUtils.hasText(query.getFirstLevelTag())) {
            criteria.andFirstLevelTagEqualTo(query.getFirstLevelTag());
        }
        if (!CollectionUtils.isEmpty(query.getFirstLevelTagList())) {
            criteria.andFirstLevelTagIn(query.getFirstLevelTagList());
        }

        // 二级标签条件
        if (StringUtils.hasText(query.getSecondLevelTag())) {
            criteria.andSecondLevelTagEqualTo(query.getSecondLevelTag());
        }
        if (!CollectionUtils.isEmpty(query.getSecondLevelTagList())) {
            criteria.andSecondLevelTagIn(query.getSecondLevelTagList());
        }

        // 任务状态条件
        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (!CollectionUtils.isEmpty(query.getStatusList())) {
            criteria.andStatusIn(query.getStatusList());
        }

        // 执行类型条件
        if (Objects.nonNull(query.getExecuteType())) {
            criteria.andExecuteTypeEqualTo(query.getExecuteType());
        }
        if (!CollectionUtils.isEmpty(query.getExecuteTypeList())) {
            criteria.andExecuteTypeIn(query.getExecuteTypeList());
        }

        // 操作人条件
        if (StringUtils.hasText(query.getOperatorId())) {
            criteria.andOperatorIdEqualTo(query.getOperatorId());
        }
        if (!CollectionUtils.isEmpty(query.getOperatorIdList())) {
            criteria.andOperatorIdIn(query.getOperatorIdList());
        }

        // 创建时间条件
        if (Objects.nonNull(query.getGmtCreateStart())) {
            criteria.andGmtCreateGreaterThanOrEqualTo(query.getGmtCreateStart());
        }
        if (Objects.nonNull(query.getGmtCreateEnd())) {
            criteria.andGmtCreateLessThanOrEqualTo(query.getGmtCreateEnd());
        }

        // 修改时间条件
        if (Objects.nonNull(query.getGmtModifiedStart())) {
            criteria.andGmtModifiedGreaterThanOrEqualTo(query.getGmtModifiedStart());
        }
        if (Objects.nonNull(query.getGmtModifiedEnd())) {
            criteria.andGmtModifiedLessThanOrEqualTo(query.getGmtModifiedEnd());
        }

        // 最近执行时间条件
        if (Objects.nonNull(query.getLastExecuteTimeStart())) {
            criteria.andLastExecuteTimeGreaterThanOrEqualTo(query.getLastExecuteTimeStart());
        }
        if (Objects.nonNull(query.getLastExecuteTimeEnd())) {
            criteria.andLastExecuteTimeLessThanOrEqualTo(query.getLastExecuteTimeEnd());
        }

        // 同步开始日期条件
        if (Objects.nonNull(query.getStartDateStart())) {
            criteria.andStartDateGreaterThanOrEqualTo(query.getStartDateStart());
        }
        if (Objects.nonNull(query.getStartDateEnd())) {
            criteria.andStartDateLessThanOrEqualTo(query.getStartDateEnd());
        }

        // 同步结束日期条件
        if (Objects.nonNull(query.getEndDateStart())) {
            criteria.andEndDateGreaterThanOrEqualTo(query.getEndDateStart());
        }
        if (Objects.nonNull(query.getEndDateEnd())) {
            criteria.andEndDateGreaterThanOrEqualTo(query.getEndDateEnd());
        }
        // 删除条件
        if (Objects.nonNull(query.getIsDelete())) {
            criteria.andIsDeleteEqualTo(query.getIsDelete().byteValue());
        }

        // 默认按修改时间倒序排列
        param.appendOrderByClause(CrowdTagConvertTaskParam.OrderCondition.GMTMODIFIED, CrowdTagConvertTaskParam.SortType.DESC);

        return param;
    }

}