package com.alibaba.tripscrm.service.service;

import com.alibaba.tripscrm.service.model.domain.query.CreateWeComRecordQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.CreateWeComRecordDTO;
import com.github.pagehelper.PageInfo;

import java.util.Date;
import java.util.List;

/**
 * 企业微信上号申请记录
 *
 * <AUTHOR>
 * @data 2025-01-21 14:28
 */
public interface CreateWeComRecordService {

    /**
     * 插入单条记录
     *
     * @param record
     * @return
     */
    Integer insertSelective(CreateWeComRecordDTO record);

    /**
     * 根据主键更新单条记录
     *
     * @param record
     * @return
     */
    Integer updateByIdSelective(CreateWeComRecordDTO record);

    /**
     * 根据主键id查询
     *
     * @param id
     * @return
     */
    CreateWeComRecordDTO selectById(Long id);

    /**
     * 根据主键删除
     *
     * @param id
     * @return
     */
    Integer deleteById(Long id);

    /**
     * 根据条件规则查询
     *
     * @param query
     * @return
     */
    List<CreateWeComRecordDTO> selectByCondition(CreateWeComRecordQuery query);

    /**
     * 根据起止日期和订单Id查询
     *
     * @param startTime 起始日期
     * @param endTime 结束日期
     * @param orderId 订单id
     * @return
     */
    CreateWeComRecordDTO selectByDateAndOrderId(Date startTime, Date endTime, String orderId);

    /**
     * 根据条件规则分页查询
     *
     * @param query
     * @return
     */
    PageInfo<CreateWeComRecordDTO> pageQuery(CreateWeComRecordQuery query);
}
