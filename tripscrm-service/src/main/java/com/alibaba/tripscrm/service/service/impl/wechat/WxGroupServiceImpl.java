package com.alibaba.tripscrm.service.service.impl.wechat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.mapper.tddl.WxEnterpriseConsumerGroupInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WxEnterpriseConsumerGroupInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WxEnterpriseConsumerGroupInfoParam;
import com.alibaba.tripscrm.service.constant.WxAccountConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.constant.WxUrlConstant;
import com.alibaba.tripscrm.service.enums.wechat.WxAccessTokenType;
import com.alibaba.tripscrm.service.model.domain.query.ConsumerGroupQuery;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.vo.wechat.GroupListVO;
import com.alibaba.tripscrm.service.model.vo.wechat.GroupMemberVO;
import com.alibaba.tripscrm.service.service.wechat.WxGroupService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.trip.wireless.mc.client.hsf.EnterpriseWeChatService;
import com.taobao.trip.wireless.mc.client.hsf.domain.weChat.WxResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/6
 */
@Slf4j
@Service
public class WxGroupServiceImpl implements WxGroupService {

    @Autowired
    private WxEnterpriseConsumerGroupInfoMapper wxEnterpriseConsumerGroupInfoMapper;

    @Autowired
    private EnterpriseWeChatService enterpriseWeChatService;

    @Override
    public GroupListVO listWxEnterpriseConsumerGroup(ConsumerGroupQuery consumerGroupQuery) {
        try {
            GroupListVO groupListVO = new GroupListVO();
            WxResponse wxResponse = enterpriseWeChatService.postExternalContact(WxUrlConstant.GROUP_LIST, WxAccountConstant.groupId_jimei, WxAccessTokenType.external_contacts.name(), JSON.toJSONString(consumerGroupQuery));
            if (!wxResponse.getSuccess()) {
                PlatformLogUtil.logFail("get group list fail", LogListUtil.newArrayList(consumerGroupQuery, wxResponse));
                return null;
            }
            JSONObject msgJson = JSONObject.parseObject(wxResponse.getWxRepSourceMsg());
            JSONArray groupChatArr = msgJson.getJSONArray("group_chat_list");
            if (groupChatArr == null || groupChatArr.isEmpty()) {
                PlatformLogUtil.logFail("group list is empty", LogListUtil.newArrayList(consumerGroupQuery, wxResponse));
                return null;
            }
            for (int i = 0; i < groupChatArr.size(); i++) {
                groupListVO.getGroupChatList().add(groupChatArr.getJSONObject(i).getString("chat_id"));
            }
            groupListVO.setNextCursor(msgJson.getString("next_cursor"));
            return groupListVO;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(consumerGroupQuery));
            return null;
        }
    }

    @Override
    public WxEnterpriseConsumerGroupInfoDO getWxEnterpriseConsumerGroupInfo(String chatId) {
        try {
            WxEnterpriseConsumerGroupInfoDO groupInfoDO = new WxEnterpriseConsumerGroupInfoDO();
            JSONObject param = new JSONObject();
            param.put("chat_id", chatId);
            //返回群成员的名字,0-不返回；1-返回
            param.put("need_name", 1);
            WxResponse wxResponse = enterpriseWeChatService.postExternalContact(WxUrlConstant.GROUP_GET, WxAccountConstant.groupId_jimei, WxAccessTokenType.external_contacts.name(), JSON.toJSONString(param));
            if (!wxResponse.getSuccess()) {
                PlatformLogUtil.logFail("get group info fail", LogListUtil.newArrayList(chatId, wxResponse));
                return null;
            }
            PlatformLogUtil.logFail("get group info success", LogListUtil.newArrayList(chatId, wxResponse));
            JSONObject msgJson = JSONObject.parseObject(wxResponse.getWxRepSourceMsg());
            JSONObject groupChat = msgJson.getJSONObject("group_chat");
            if (Objects.isNull(groupChat)) {
                PlatformLogUtil.logFail("get group info empty", LogListUtil.newArrayList(chatId, wxResponse));
                return null;
            }
            groupInfoDO.setChatId(chatId);
            StringBuilder name = new StringBuilder(groupChat.getString("name"));
            groupInfoDO.setAdminList(groupChat.getString("admin_list"));
            groupInfoDO.setCreateTime(new Date(groupChat.getLong("create_time") * 1000));
            groupInfoDO.setDeleted(WxConstants.not_deleted);
            groupInfoDO.setOwner(groupChat.getString("owner"));
            List<GroupMemberVO> memberVOS = JSON.parseArray(groupChat.getString("member_list"), GroupMemberVO.class);
            groupInfoDO.setMemberNum(memberVOS.size());
            groupInfoDO.setMemberList(JSON.toJSONString(memberVOS));
            if (StringUtils.isBlank(name.toString())) {
                for (int i = 0; i < memberVOS.size() && i < 3; i++) {
                    name.append(memberVOS.get(i).getName()).append(",");
                }
            }
            groupInfoDO.setName(name.toString());
            return groupInfoDO;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(chatId));
            return null;
        }
    }

    @Override
    public Long recoverGroupInfo(WxEnterpriseConsumerGroupInfoDO groupInfoDO) {
        try {
            WxEnterpriseConsumerGroupInfoParam param = new WxEnterpriseConsumerGroupInfoParam();
            param.createCriteria().andChatIdEqualTo(groupInfoDO.getChatId());
            List<WxEnterpriseConsumerGroupInfoDO> groupInfoDOS = wxEnterpriseConsumerGroupInfoMapper.selectByParam(param);
            if (CollectionUtils.isEmpty(groupInfoDOS)) {
                //新增
                wxEnterpriseConsumerGroupInfoMapper.insert(groupInfoDO);
            } else {
                //覆盖
                groupInfoDO.setId(groupInfoDOS.get(0).getId());
                wxEnterpriseConsumerGroupInfoMapper.updateByPrimaryKeySelective(groupInfoDO);
            }
            return groupInfoDO.getId();
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(groupInfoDO));
            return null;
        }
    }

    @Override
    public int delSyncByJobLabel(Long jobLabel) {
        WxEnterpriseConsumerGroupInfoParam wxEnterpriseConsumerGroupInfoParam = new WxEnterpriseConsumerGroupInfoParam();
        wxEnterpriseConsumerGroupInfoParam.createCriteria().andSyncTaskIdNotEqualTo(String.valueOf(jobLabel));
        WxEnterpriseConsumerGroupInfoDO wxEnterpriseConsumerGroupInfoDO = new WxEnterpriseConsumerGroupInfoDO();
        wxEnterpriseConsumerGroupInfoDO.setDeleted(WxConstants.deleted);
        wxEnterpriseConsumerGroupInfoMapper.updateByParamSelective(wxEnterpriseConsumerGroupInfoDO, wxEnterpriseConsumerGroupInfoParam);
        return wxEnterpriseConsumerGroupInfoMapper.deleteByParam(wxEnterpriseConsumerGroupInfoParam);
    }

    @Override
    public PageResultVO<WxEnterpriseConsumerGroupInfoDO> listWxEnterpriseGroup(ConsumerGroupQuery consumerGroupQuery) {
        WxEnterpriseConsumerGroupInfoParam groupInfoParam = new WxEnterpriseConsumerGroupInfoParam();
        groupInfoParam.appendOrderByClause(WxEnterpriseConsumerGroupInfoParam.OrderCondition.CREATETIME, WxEnterpriseConsumerGroupInfoParam.SortType.DESC);
        if (StringUtils.isNotBlank(consumerGroupQuery.getName())) {
            groupInfoParam.createCriteria().andNameLike("%" + consumerGroupQuery.getName() + "%");
        }
        if (StringUtils.isNotBlank(consumerGroupQuery.getChatId())) {
            groupInfoParam.createCriteria().andChatIdEqualTo(consumerGroupQuery.getChatId());
        }
        groupInfoParam.setPage(true);
        groupInfoParam.setPageSize(consumerGroupQuery.getLimit());
        List<WxEnterpriseConsumerGroupInfoDO> wxEnterpriseConsumerGroupInfoDOS = wxEnterpriseConsumerGroupInfoMapper.selectByParam(groupInfoParam);
        if (CollectionUtils.isEmpty(wxEnterpriseConsumerGroupInfoDOS)) {
            return PageResultVO.failResult("查询失败");
        }
        return PageResultVO.successResult(wxEnterpriseConsumerGroupInfoDOS, wxEnterpriseConsumerGroupInfoDOS.size(), 1, 1);

    }
}
