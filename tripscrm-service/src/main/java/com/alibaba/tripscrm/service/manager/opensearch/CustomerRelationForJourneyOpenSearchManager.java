package com.alibaba.tripscrm.service.manager.opensearch;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.service.model.domain.query.CustomerRelationForJourneyQuery;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.aliyun.opensearch.sdk.generated.search.Order;
import com.aliyun.opensearch.sdk.generated.search.SearchParams;
import com.aliyun.opensearch.sdk.generated.search.Sort;
import com.aliyun.opensearch.sdk.generated.search.SortField;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/24 18:54
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerRelationForJourneyOpenSearchManager extends BaseOpenSearchManager {
    private final WXCorpStorage wxCorpStorage;

    /**
     * 查询客户的企微成员好友（行程管家专用）
     *
     * @param query 查询条件
     * @return FollowUserInfo
     */
    public FollowUserInfo getFollowUserInfoForJourney(CustomerRelationForJourneyQuery query) {
        try {
            SearchParams searchParams = buildPageSearchParams(query, null);
            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (StringUtils.hasText(query.getUid())) {
                queryList.add(String.format("uid:'%s'", query.getUid()));
            }

            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);

            PageInfo<FollowUserInfo.CustomerRelation> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convert2JourneyCustomerRelation);
            PlatformLogUtil.logFail("Opensearch查询客户的企微成员好友(行程管家专用)成功", LogListUtil.newArrayList(searchParams, pageInfo));
            PlatformLogUtil.logFail("Opensearch查询客户的企微成员好友(行程管家专用)成功", LogListUtil.newArrayList(searchParams, pageInfo));
            return new FollowUserInfo(pageInfo.getList());
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的企微成员好友(行程管家专用)异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }

        return new FollowUserInfo();
    }

    private FollowUserInfo.CustomerRelation convert2JourneyCustomerRelation(JSONObject o) {
        FollowUserInfo.CustomerRelation customerRelation = new FollowUserInfo.CustomerRelation();
        customerRelation.setUserId(o.getString("user_id"));
        customerRelation.setStatus(o.getInteger("status"));
        customerRelation.setCorpName(wxCorpStorage.getCorpName(SpaceInfoThreadLocalUtils.getCorpId()));
        return customerRelation;
    }

    @Override
    protected String getAppName() {
        return "customer_relation_for_journey";
    }

    @Override
    protected List<String> getFetchFields() {
        return Lists.newArrayList("id", "uid", "union_id", "user_id", "status");
    }
}
