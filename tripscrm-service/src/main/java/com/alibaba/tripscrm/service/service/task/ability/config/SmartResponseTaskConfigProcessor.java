package com.alibaba.tripscrm.service.service.task.ability.config;

import java.util.List;
import java.util.Objects;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.base.TaskService;

import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 智能回复任务配置处理器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SmartResponseTaskConfigProcessor extends AbstractTaskConfigProcessor {

    private final TaskService taskService;

    @Override
    public TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName) {
        // 复用父类初始化配置
        TaskInfoDO initConfig = super.getInitConfig(activityId, spaceId, taskName);
        // 前置校验拦截
        preCheck(activityId, spaceId, initConfig);
        return initConfig;
    }

    /**
     * 前置校验拦截
     *
     * @param activityId 活动id
     * @param spaceId    空间id
     * @param initConfig 任务初始化对象
     */
    private void preCheck(Long activityId, Long spaceId, TaskInfoDO initConfig) {
        // 校验空间下是否已存在智能回复任务。保障一个空间下只有一个智能回复任务数据
        TaskQuery taskQuery = new TaskQuery();
        taskQuery.setSpaceId(Objects.nonNull(spaceId) ? spaceId : initConfig.getSpaceId());
        taskQuery.setActivityId(Objects.nonNull(activityId) ? activityId : initConfig.getActivityId());
        taskQuery.setType(getTaskType().getCode());
        // 查询任务数据
        List<TaskInfoDO> taskInfoDOList = taskService.query(taskQuery);
        // 如果存在有效数据，则业务链路异常，报错提示
        if (CollectionUtils.isNotEmpty(taskInfoDOList)) {
            throw new TripscrmException(ErrorCodeEnum.SMART_RESPONSE_TASK_EXIST);
        }
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {

    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {

    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {

    }

    @Override
    public TaskType getTaskType() {
        return TaskType.SMART_RESPONSE;
    }
}
