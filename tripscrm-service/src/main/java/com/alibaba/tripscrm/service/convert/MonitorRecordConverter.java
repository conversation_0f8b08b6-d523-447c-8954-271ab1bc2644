package com.alibaba.tripscrm.service.convert;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.MonitorRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.CustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.domain.query.MonitorRecordQuery;
import com.alibaba.tripscrm.service.model.domain.request.MonitorRecordQueryRequest;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.monitor.MonitorRecordExtraInfoVO;
import com.alibaba.tripscrm.service.model.vo.monitor.MonitorRecordVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.MonitorRecordService;
import com.alibaba.tripscrm.service.service.space.SpaceResourceService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatDepartmentService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.wx.DepartmentUtils;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.request.WxGroupBlackListRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/21 15:48
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MonitorRecordConverter {

    private final WechatGroupService wechatGroupService;
    private final WechatCustomerService wechatCustomerService;
    private final AccountService accountService;
    private final SpaceResourceService spaceResourceService;
    private final WechatDepartmentService wechatDepartmentService;
    private final CustomerRelationOpenSearchManager customerRelationOpenSearchManager;
    private final CustomerService customerService;
    private final MonitorRecordService monitorRecordService;
    private final WechatUserService wechatUserService;
    private final GroupRelationRepository groupRelationRepository;
    /**
     * 将MonitorRecordQueryRequest转换为MonitorRecordQuery
     *
     * @param request
     * @return
     */
    public MonitorRecordQuery queryRequest2Query(MonitorRecordQueryRequest request) {
        MonitorRecordQuery query = new MonitorRecordQuery();
        BeanUtils.copyProperties(request, query);
        if (Objects.nonNull(request.getCreateEndTime())) {
            // createEndTime的后一天
            query.setCreateEndTime(DateUtils.addDays(request.getCreateEndTime(), 1));
        }
        query.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        return query;
    }

    /**
     * 将MonitorRecordDO转换为MonitorRecordVO
     *
     * @param monitorRecordDO
     * @return
     */
    public MonitorRecordVO do2vo(MonitorRecordDO monitorRecordDO) {
        if (Objects.isNull(monitorRecordDO)) {
            return null;
        }
        MonitorRecordVO monitorRecordVO = new MonitorRecordVO();
        BeanUtils.copyProperties(monitorRecordDO, monitorRecordVO);
        MonitorRecordExtraInfoVO extraInfo = JSONObject.parseObject(monitorRecordDO.getExtraInfo(), MonitorRecordExtraInfoVO.class);
        monitorRecordVO.setExtraInfo(extraInfo);

        // 根据微信号获取个人信息
        // wechatCustomerVO2MonitorRecordVO(monitorRecordVO, monitorRecordDO);


        if (StringUtils.hasText(monitorRecordVO.getTargetId())) {
            List<WechatCustomerVO> wechatCustomerVOS = wechatCustomerService.listByExternalUserIdList(Collections.singletonList(monitorRecordVO.getTargetId()));
            if (!wechatCustomerVOS.isEmpty()) {
                WechatCustomerVO wechatCustomerVO = wechatCustomerVOS.get(0);
                monitorRecordVO.setGender(wechatCustomerVO.getGender().byteValue());
                monitorRecordVO.setTargetName(wechatCustomerVO.getName());
                monitorRecordVO.setAvatarUrl(wechatCustomerVO.getAvatarUrl());
                monitorRecordVO.setCorpName(wechatCustomerVO.getCorpName());
                monitorRecordVO.setType(wechatCustomerVO.getType());
                monitorRecordVO.setCorpId(wechatCustomerVO.getCorpId());

                List<Integer> bindSpaceDepartmentIdList = spaceResourceService.listBySpaceIdAndResourceType(monitorRecordDO.getSpaceId(), ResourceTypeEnum.DEPARTMENT)
                        .stream()
                        .map(SpaceResourceDO::getTargetId)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());

                FollowUserQuery followUserQuery = new FollowUserQuery();
                followUserQuery.setDepartmentIdList(DepartmentUtils.getAllSubIdWithCurrent(wechatDepartmentService.list(SpaceInfoThreadLocalUtils.getCorpId()), bindSpaceDepartmentIdList));
                followUserQuery.setExternalUserId(monitorRecordVO.getTargetId());
                FollowUserInfo followUserInfo = customerRelationOpenSearchManager.getFollowUserInfo(followUserQuery);

                monitorRecordVO.setFollowUserList(followUserInfo.getRelationList());
            }

        }


        // 根据群号获取群名、群主名
        // getGroupInfoByGroupId(monitorRecordVO);

        if (Objects.nonNull(monitorRecordVO.getExtraInfo())) {
            MonitorRecordExtraInfoVO monitorRecordExtraInfoVO = monitorRecordVO.getExtraInfo();

            WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(monitorRecordExtraInfoVO.getGroupChatId());
            if (Objects.nonNull(wechatGroupDO)) {
                monitorRecordVO.setGroupName(wechatGroupDO.getName());
                WechatUserDTO wechatUserDTO = wechatUserService.listById(Collections.singletonList(wechatGroupDO.getOwnerUser())).get(0);
                if (Objects.nonNull(wechatUserDTO)) {
                    monitorRecordVO.setGroupOwner(wechatUserDTO.getName());
                }
            }
            // 如果从wechat_customer中获取的客户微信名为空，尝试从group_relation中获取微信名
            if (!StringUtils.hasLength(monitorRecordVO.getTargetName())) {
                List<GroupRelationDO> groupRelationDOS = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(monitorRecordExtraInfoVO.getGroupChatId()), Lists.newArrayList(monitorRecordVO.getTargetId()), wechatGroupDO.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
                if (!CollectionUtils.isEmpty(groupRelationDOS)) {
                    monitorRecordVO.setTargetName(groupRelationDOS.get(0).getName());
                }
            }
        }

        if (StringUtils.hasText(monitorRecordVO.getCreatorId())) {
            User user = accountService.getUserByAccountId(monitorRecordVO.getCreatorId());
            if (Objects.nonNull(user)) {
                monitorRecordVO.setCreatorName(user.getUserName());
            }
        }


        return monitorRecordVO;
    }


    /**
     * 将MonitorRecordVO转换为MonitorRecordDO
     *A
     * @param monitorRecordVO
     * @return
     */
    public Boolean settleMonitorRecordRequest(MonitorRecordVO monitorRecordVO) {
        if (Objects.isNull(monitorRecordVO)) {
            return false;
        }
        monitorRecordVO.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());

        if (Objects.nonNull(monitorRecordVO.getExtraInfo())) {
            checkExtraInfo(monitorRecordVO.getExtraInfo());
        }

        // 删除请求
        if (Objects.nonNull(monitorRecordVO.getDeleted()) && monitorRecordVO.getDeleted() == 1) {
            return vo2doDelete(monitorRecordVO);
        }

        // 确认请求
        if (Objects.nonNull(monitorRecordVO.getStatus()) && monitorRecordVO.getStatus() == 1) {
            return vo2doConfirm(monitorRecordVO);
        }

        return createActionInitRecord(monitorRecordVO);
    }

    @NotNull
    private Boolean createActionInitRecord(MonitorRecordVO monitorRecordVO) {
        MonitorRecordDO monitorRecordDO = new MonitorRecordDO();
        BeanUtils.copyProperties(monitorRecordVO, monitorRecordDO);
        monitorRecordDO.setExtraInfo(JSONObject.toJSONString(Optional.ofNullable(monitorRecordVO.getExtraInfo())));
        monitorRecordDO.setLastOperatorId(monitorRecordVO.getCreatorId());
        monitorRecordDO.setLastOperatorId(monitorRecordVO.getCreatorId());
        monitorRecordDO.setDeleted((byte) 0);
        monitorRecordDO.setBizId("0");
        monitorRecordDO.setStatus((byte) 0);

        int effect = monitorRecordService.insert(monitorRecordDO);
        return effect >= 1 ? true : false;
    }

    private Boolean vo2doDelete(MonitorRecordVO monitorRecordVO) {
        if(monitorRecordVO.getId() == null){
            PlatformLogUtil.logFail("规则记录删除请求入参为空", LogListUtil.newArrayList(monitorRecordVO));
            return false;
        }
        MonitorRecordDO oldRecordDO = monitorRecordService.selectByPrimaryKey(monitorRecordVO.getId());
        if(oldRecordDO == null){
            PlatformLogUtil.logFail("规则记录删除请求根据id获取监控记录为空", LogListUtil.newArrayList(monitorRecordVO));
            return false;
        }
        List<MonitorRecordDO> monitorRecordDOS = getRelatedMonitorRecords(oldRecordDO);
        PlatformLogUtil.logInfo("规则记录删除请求", LogListUtil.newArrayList(monitorRecordVO, oldRecordDO, monitorRecordDOS));

        JSONObject extraInfo = JSONObject.parseObject(oldRecordDO.getExtraInfo());
        if (monitorRecordDOS.size() == 1 && NumberUtils.isEqual(monitorRecordDOS.get(0).getId(), monitorRecordVO.getId())) {
            WxGroupBlackListRequest wxGroupBlackListRequest = getWxGroupBlackRequest(oldRecordDO, extraInfo);
            ResultDO<String> resultDO = customerService.asyncSetGroupBlackList(wxGroupBlackListRequest);
            if (resultDO == null || resultDO.getSuccess() == null || !resultDO.getSuccess() || resultDO.getModel() == null) {
                PlatformLogUtil.logFail("规则记录删除请求ISV删除失败", LogListUtil.newArrayList(wxGroupBlackListRequest, resultDO));
                return false;
            }
        }
        extraInfo.remove("firstConfirmGroupChatId");
        oldRecordDO.setExtraInfo(JSONObject.toJSONString(extraInfo));
        oldRecordDO.setDeleted((byte) 1);
        int effect = monitorRecordService.updateByPrimaryKey(oldRecordDO);
        if(effect <= 0){
            PlatformLogUtil.logFail("监控规则记录本地删除失败", LogListUtil.newArrayList(oldRecordDO));
            return false;
        }
        return true;
    }

    private List<MonitorRecordDO> getRelatedMonitorRecords(MonitorRecordDO oldRecordDO) {
        MonitorRecordQuery query = new MonitorRecordQuery();
        // 如果只剩一条已确认未删除的记录，则删除本条后关闭黑名单开关
        query.setTargetId(oldRecordDO.getTargetId());
        query.setTargetType(oldRecordDO.getTargetType());
        query.setDeleted((byte) 0);
        query.setStatus((byte) 1);
        return monitorRecordService.selectByCondition(query);
    }

    @NotNull
    private WxGroupBlackListRequest getWxGroupBlackRequest(MonitorRecordDO oldRecordDO, JSONObject extraInfo) {
        WxGroupBlackListRequest wxGroupBlackListRequest = new WxGroupBlackListRequest();
        wxGroupBlackListRequest.setWechatGroupChatId(extraInfo.getString("firstConfirmGroupChatId"));
        wxGroupBlackListRequest.setWechatGroupUserId(oldRecordDO.getTargetId());
        WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(extraInfo.getString("firstConfirmGroupChatId"));
        wxGroupBlackListRequest.setUserId(wechatGroupDO.getOwnerUser());
        wxGroupBlackListRequest.setEnable(false);
        return wxGroupBlackListRequest;
    }

    private Boolean vo2doConfirm(MonitorRecordVO monitorRecordVO) {
        if(monitorRecordVO.getId() == null){
            PlatformLogUtil.logFail("规则记录确认请求入参为空", LogListUtil.newArrayList(monitorRecordVO));
            return false;
        }
        MonitorRecordDO oldRecordDO = monitorRecordService.selectByPrimaryKey(monitorRecordVO.getId());
        if(oldRecordDO == null){
            PlatformLogUtil.logFail("规则记录确认请求获取监控记录失败", LogListUtil.newArrayList(monitorRecordVO));
            return null;
        }
        List<MonitorRecordDO> monitorRecordDOS = getRelatedMonitorRecordDOS(oldRecordDO);
        if(CollectionUtils.isEmpty(monitorRecordDOS)){
            PlatformLogUtil.logFail("规则记录确认请求查询该用户所有未确认的规则记录为空", LogListUtil.newArrayList(monitorRecordVO, oldRecordDO));
            return null;
        }
        PlatformLogUtil.logInfo("规则记录确认请求查询该用户所有未确认的规则记录", monitorRecordVO, monitorRecordDOS);

        JSONObject extraInfo = JSONObject.parseObject(oldRecordDO.getExtraInfo());
        WxGroupBlackListRequest wxGroupBlackListRequest = getWxGroupBlackListRequest(oldRecordDO, extraInfo);
        ResultDO<String> resultDO = customerService.asyncSetGroupBlackList(wxGroupBlackListRequest);

        PlatformLogUtil.logInfo("规则记录确认请求发送", wxGroupBlackListRequest, resultDO);
        if (resultDO != null && resultDO.getSuccess() != null && resultDO.getSuccess() && resultDO.getModel() != null) {
            monitorRecordDOS.stream().forEach(recordDO -> {
                        recordDO.setStatus((byte) 1);
                        String oldExtraInfo = recordDO.getExtraInfo();
                        recordDO.setExtraInfo(oldExtraInfo.substring(0, oldExtraInfo.length() - 1) + "," + "\"firstConfirmGroupChatId\":\"" + extraInfo.get("groupChatId") + "\"}");
                        int effect = monitorRecordService.updateByPrimaryKey(recordDO);
                        if(effect <= 0){
                            PlatformLogUtil.logFail("监控规则记录本地更新失败", LogListUtil.newArrayList(recordDO));
                        }
                    });
            return true;
        }
        return false;
    }

    private List<MonitorRecordDO> getRelatedMonitorRecordDOS(MonitorRecordDO oldRecordDO) {
        MonitorRecordQuery query = new MonitorRecordQuery();
        // 如果该用户的其中一条记录被点了确认，则该用户的所有记录都变成已确认状态
        query.setTargetId(oldRecordDO.getTargetId());
        query.setTargetType(oldRecordDO.getTargetType());
        query.setDeleted((byte) 0);
        query.setStatus((byte) 0);
        return monitorRecordService.selectByCondition(query);
    }

    @NotNull
    private WxGroupBlackListRequest getWxGroupBlackListRequest(MonitorRecordDO oldRecordDO, JSONObject extraInfo) {
        WxGroupBlackListRequest wxGroupBlackListRequest = new WxGroupBlackListRequest();
        wxGroupBlackListRequest.setWechatGroupChatId(extraInfo.getString("groupChatId"));
        wxGroupBlackListRequest.setWechatGroupUserId(oldRecordDO.getTargetId());
        WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(extraInfo.getString("groupChatId"));
        wxGroupBlackListRequest.setUserId(wechatGroupDO.getOwnerUser());
        wxGroupBlackListRequest.setEnable(true);
        return wxGroupBlackListRequest;
    }

    private void checkExtraInfo(MonitorRecordExtraInfoVO extraInfo) {
        if (!RuleTypeEnum.isGroupMonitorType(Integer.valueOf(extraInfo.getEntryMethodL2()))) {
            PlatformLogUtil.logFail("MonitorRecordConverter.checkExtraInfo", LogListUtil.newArrayList(extraInfo));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
    }


    /**
     * 获取性别，1:男，2:女，0:未知
     *
     * @param gender
     */
    private String getSex(Integer gender) {
        return gender == 1 ? "男" : gender == 2 ? "女" : "未知";
    }
}
