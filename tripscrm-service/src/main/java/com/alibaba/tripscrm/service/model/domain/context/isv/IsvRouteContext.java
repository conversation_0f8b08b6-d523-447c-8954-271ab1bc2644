package com.alibaba.tripscrm.service.model.domain.context.isv;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/22 21:27
 */
@Data
public class IsvRouteContext {
    /**
     * 行动项
     */
    private RiskActionEnum riskActionEnum;

    /**
     * 企微外部联系人Id
     */
    private String externalUserId;

    /**
     * 企微群聊Id
     */
    private String chatId;

    /**
     * 企微成员Id（如果指定，所选择结果必须在userIdList范围内）
     */
    private List<String> userIdList;

    /**
     * 业务空间Id
     */
    private Long spaceId;

    /**
     * 额外信息
     */
    private JSONObject extraJson = new JSONObject();

    public void addExtraInfo(String key, Object value) {
        extraJson.put(key, value);
    }
}
