package com.alibaba.tripscrm.service.service.task.ability.sub.retry;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

import static com.alibaba.tripscrm.TripSCRMErrorCode.*;

/**
 * <AUTHOR>
 * @since 2024/11/18 20:00
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RetryPreCheckProcessor implements ExecuteProcessor {
    private final TaskService taskService;
    private final LdbTairManager ldbTairManager;
    private final SubTaskInstanceService subTaskInstanceService;

    @Override
    @TaskExecuteLog("单条记录重试_前置校验")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        if (!context.getRetry()) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        checkValid(context);
        return TripSCRMResult.success(null);
    }

    private void checkValid(TaskExecuteContext context) {
        // 没有主任务实例&没有子任务实例，直接结束吧
        if (!Objects.isNull(context.getMainTaskInstanceId()) && Objects.isNull(context.getInstanceId())) {
            throw new TripscrmException(NOT_EXIST_TASK_INSTANCE_DATA);
        }

        TaskInstanceStatusEnum subTaskInstanceStatus = getSubTaskInstanceStatus(context);
        if (Objects.isNull(subTaskInstanceStatus)) {
            throw new TripscrmException(NOT_EXIST_SUB_TASK_INSTANCE_DATA);
        }

        // 只有提交成功和运行中才能重试
        if (!Lists.newArrayList(TaskInstanceStatusEnum.RUNNING, TaskInstanceStatusEnum.SUBMIT_SUCCESS).contains(subTaskInstanceStatus)) {
            throw new TripscrmException(SUB_TASK_INSTANCE_STATUS_NOT_SUPPORT_RETRY);
        }

        SubTaskInstanceDO subTaskInstance = getSubTaskInstance(context);

        // 任务ID
        Long taskId = context.getTaskId();
        // 将保存的任务快照设置到上下文
        context.setTaskInfoDOSnapshot(JSONObject.parseObject(subTaskInstance.getTaskConfig(), new TypeReference<TaskInfoDO>() {
        }));
        context.setMaterialSnapshot(JSONObject.parseObject(subTaskInstance.getMaterialInfo(), new TypeReference<MaterailInfoDO>() {
        }));
        context.setAbBucketListSnapshot(JSONObject.parseObject(subTaskInstance.getAbBucketList(), new TypeReference<List<AbTestBucketVO>>() {
        }));
        // 动态校验任务的合法性
        taskService.checkTaskValid(context.getTaskInfoDOSnapshot(), context.getTestFlag());
        if (!taskService.checkInWorkTimeWindow(context.getTaskInfoDOSnapshot())) {
            throw new TripscrmException(TASK_EXECUTE_NOT_IN_WORK_TIME);
        }
        PlatformLogUtil.logInfo("单条记录重试_前置校验，检查通过", LogListUtil.newArrayList(taskId, context.getInstanceId(), context));
    }


    private TaskInstanceStatusEnum getSubTaskInstanceStatus(TaskExecuteContext context) {
        String status = getFromTair(TairConstant.SUB_TASK_INSTANCE_STATUS_PREFIX + context.getInstanceId());
        if (StringUtils.hasText(status)) {
            return TaskInstanceStatusEnum.getByStatus(status);
        }

        SubTaskInstanceDO subTaskInstanceDO = subTaskInstanceService.queryById(context.getInstanceId());
        if (Objects.isNull(subTaskInstanceDO)) {
            return null;
        }
        return TaskInstanceStatusEnum.getByStatus(subTaskInstanceDO.getStatus());
    }

    private SubTaskInstanceDO getSubTaskInstance(TaskExecuteContext context) {
        SubTaskInstanceDO subTaskInstanceDO = subTaskInstanceService.queryByIdWithCache(context.getInstanceId());
        if (Objects.isNull(subTaskInstanceDO)) {
            return null;
        }
        return subTaskInstanceDO;
    }

    private String getFromTair(String key) {
        Object o = ldbTairManager.get(key);
        if (Objects.isNull(o)) {
            return null;
        }
        return String.valueOf(o);
    }
}
