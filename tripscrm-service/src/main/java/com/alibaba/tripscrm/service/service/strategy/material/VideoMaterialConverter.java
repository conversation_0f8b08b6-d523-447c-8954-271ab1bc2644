package com.alibaba.tripscrm.service.service.strategy.material;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.VideoMaterialDTO;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.WxMediaUtils;
import com.alibaba.tripzoo.proxy.enums.AttachmentSceneTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@AllArgsConstructor
public class VideoMaterialConverter extends AbstractMaterialConverter<VideoMaterialDTO> {
    private final WxMediaUtils wxMediaUtils;

    @Override
    public VideoMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        return JSONObject.parseObject(content, VideoMaterialDTO.class);
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        return new ArrayList<>();
    }

    @Override
    public List<MessageBO> buildMessageBO(VideoMaterialDTO materialDTO, MaterialContentConvertContext context, String sceneType) {
        ArrayList<MessageBO> messageBOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(materialDTO.getUrlList())) {
            return messageBOList;
        }
        int msgIndex = materialDTO.getIndex();
        for (int i = 0; i < materialDTO.getUrlList().size(); i++) {
            MessageBO messageBO = new MessageBO();
            messageBO.setMsgType(MessageTypeEnum.VIDEO);
            messageBO.setMsgNum(msgIndex++);
            messageBO.setHref(materialDTO.getUrlList().get(i));
            if (CollectionUtils.isNotEmpty(materialDTO.getConverPicture()) && materialDTO.getConverPicture().size() > i) {
                messageBO.setMsgContent(materialDTO.getConverPicture().get(i));
            }
            if (CollectionUtils.isNotEmpty(materialDTO.getDuration())) {
                messageBO.setVoiceTime(materialDTO.getDuration().get(i));
            }
            messageBOList.add(messageBO);
        }
        return messageBOList;
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(VideoMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        ArrayList<WxMessageBO> wxMessageList = new ArrayList<>();
        for (String url : materialDTO.getUrlList()) {
            WxMessageBO wxMessageBO3 = new WxMessageBO();
            wxMessageBO3.setMsgType(WxAttachmentTypeEnum.VIDEO);
            wxMessageBO3.setMediaId(wxMediaUtils.getMediaId(WxAttachmentTypeEnum.VIDEO, sendMessage ? null : AttachmentSceneTypeEnum.MOMENT, url, SpaceInfoThreadLocalUtils.getCorpId(), false));
            wxMessageList.add(wxMessageBO3);
        }
        return wxMessageList;
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.VIDEO;
    }

    @Override
    public String buildSubscribeMsgTemplateContent(String content, String paramType) {
        return "";
    }
}
