package com.alibaba.tripscrm.service.middleware.diamond;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 群聊黑名单（临时）
 */
@Slf4j
@DiamondListener(dataId = "wechat_group_black_list")
public class WechatGroupBlackListDiamond implements DiamondDataCallback {
    private Map<String, Set<String>> corpId2ChatIdList = new HashMap<>();

    @Override
    public void received(String data) {
        try {
            corpId2ChatIdList = JSONObject.parseObject(data, new TypeReference<HashMap<String, Set<String>>>() {
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    /**
     * 获取任务自定义参数schema模板
     *
     * @param corpId 企业Id
     * @param chatId 群聊Id
     * @return JSONObject
     */
    public Boolean checkInBlackList(String corpId, String chatId) {
        if (!StringUtils.hasLength(corpId) || !StringUtils.hasLength(chatId)) {
            return false;
        }

        return corpId2ChatIdList.getOrDefault(corpId, new HashSet<>()).contains(chatId);
    }
}
