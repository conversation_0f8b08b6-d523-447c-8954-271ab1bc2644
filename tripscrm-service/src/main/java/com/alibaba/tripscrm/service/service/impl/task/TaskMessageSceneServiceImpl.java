package com.alibaba.tripscrm.service.service.impl.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.TaskMessageSceneMapper;
import com.alibaba.tripscrm.dal.model.domain.data.TaskMessageSceneDO;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.task.CycleEnum;
import com.alibaba.tripscrm.service.enums.task.TaskMessageLimitUnitEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.task.TaskMessageSceneVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.base.TaskMessageSceneService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-19 18:35:10
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskMessageSceneServiceImpl implements TaskMessageSceneService {
    private final TaskMessageSceneMapper taskMessageSceneMapper;

    @Override
    public TaskMessageSceneVO getById(Long id) {
        if (!NumberUtils.validLong(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return convert(taskMessageSceneMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<TaskMessageSceneVO> listByTaskMessageTypeId(Long taskMessageTypeId) {
        if (!NumberUtils.validLong(taskMessageTypeId)) {
            PlatformLogUtil.logFail("获取任务场景失败，参数非法", LogListUtil.newArrayList(taskMessageTypeId));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<TaskMessageSceneDO> taskMessageSceneDOList = taskMessageSceneMapper.listByTaskMessageTypeId(taskMessageTypeId).stream()
                .filter(taskMessageSceneDO -> Objects.nonNull(taskMessageSceneDO) && Objects.equals(IsDeleteEnum.NO, IsDeleteEnum.of(taskMessageSceneDO.getIsDeleted())) && !NumberUtils.isEqual(SwitchConfig.TASK_MESSAGE_SCENE_QUNMA_SILIAO_RW, taskMessageSceneDO.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskMessageSceneDOList)) {
            return new ArrayList<>();
        }

        return taskMessageSceneDOList.stream().map(this::convert).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public Integer batchUpsert(List<TaskMessageSceneVO> list) {
        if (CollectionUtils.isEmpty(list) || list.stream().anyMatch(Objects::isNull)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        int res = 0;
        for (TaskMessageSceneVO taskMessageSceneVO : list) {
            res += taskMessageSceneMapper.upsert(convert(taskMessageSceneVO));
        }
        return res;
    }

    @Override
    public Integer updateById(TaskMessageSceneVO taskMessageSceneVO) {
        if (Objects.isNull(taskMessageSceneVO) || !NumberUtils.validLong(taskMessageSceneVO.getId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return taskMessageSceneMapper.updateByPrimaryKey(convert(taskMessageSceneVO));
    }

    @Override
    public Integer deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids) || ids.stream().anyMatch(id -> !NumberUtils.validLong(id))) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return taskMessageSceneMapper.deleteByIds(ids);
    }

    @AteyeInvoker(description = "删除任务场景", paraDesc = "id列表")
    public Integer deleteById(String ids) {
        return taskMessageSceneMapper.deleteByIds(Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList()));
    }

    private TaskMessageSceneDO convert(TaskMessageSceneVO taskMessageSceneVO) {
        if (Objects.isNull(taskMessageSceneVO)) {
            return null;
        }

        TaskMessageSceneDO taskMessageSceneDO = new TaskMessageSceneDO();
        taskMessageSceneDO.setId(taskMessageSceneVO.getId());
        taskMessageSceneDO.setTaskMessageTypeId(taskMessageSceneVO.getTaskMessageTypeId());
        taskMessageSceneDO.setName(taskMessageSceneVO.getName());
        JSONObject jsonConfig = new JSONObject();
        if (Objects.nonNull(taskMessageSceneVO.getLimit()) && taskMessageSceneVO.getLimit() >= 0) {
            jsonConfig.put("limit", taskMessageSceneVO.getLimit());
            jsonConfig.put("unit", Optional.ofNullable(taskMessageSceneVO.getLimitUnit()).orElse(TaskMessageLimitUnitEnum.MESSAGE.getCode()));
            jsonConfig.put("limitType", Optional.ofNullable(taskMessageSceneVO.getLimitType()).orElse(CycleEnum.DAY.getCode()));
        }
        taskMessageSceneDO.setConfig(jsonConfig.toJSONString());
        return taskMessageSceneDO;
    }

    private TaskMessageSceneVO convert(TaskMessageSceneDO taskMessageSceneDO) {
        if (Objects.isNull(taskMessageSceneDO)) {
            return null;
        }

        TaskMessageSceneVO taskMessageSceneVO = new TaskMessageSceneVO();
        taskMessageSceneVO.setId(taskMessageSceneDO.getId());
        taskMessageSceneVO.setTaskMessageTypeId(taskMessageSceneDO.getTaskMessageTypeId());
        taskMessageSceneVO.setName(taskMessageSceneDO.getName());
        JSONObject jsonConfig = JSONObject.parseObject(taskMessageSceneDO.getConfig());
        taskMessageSceneVO.setLimit(jsonConfig.getLong("limit"));
        taskMessageSceneVO.setLimitUnit(jsonConfig.getInteger("unit"));
        taskMessageSceneVO.setLimitType(jsonConfig.getInteger("limitType"));
        return taskMessageSceneVO;
    }
}
