package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.SellerWorkScheduleInfoDO;
import com.alibaba.tripscrm.service.model.vo.seller.SellerWorkScheduleInfoVO;
import com.alibaba.tripscrm.service.model.dto.seller.SellerWorkScheduleInfoDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class SellerWorkScheduleInfoConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param sellerWorkScheduleInfoDTO
     */
    public SellerWorkScheduleInfoDO convertFromDTO(SellerWorkScheduleInfoDTO sellerWorkScheduleInfoDTO) {
        if (Objects.isNull(sellerWorkScheduleInfoDTO)) {
            return null;
        }
        SellerWorkScheduleInfoDO sellerWorkScheduleInfoDO = new SellerWorkScheduleInfoDO();
        BeanUtils.copyProperties(sellerWorkScheduleInfoDTO, sellerWorkScheduleInfoDO);
        return sellerWorkScheduleInfoDO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param sellerWorkScheduleInfoDO
     */
    public SellerWorkScheduleInfoDTO convertFromDO(SellerWorkScheduleInfoDO sellerWorkScheduleInfoDO) {
        if (Objects.isNull(sellerWorkScheduleInfoDO)) {
            return null;
        }
        SellerWorkScheduleInfoDTO sellerWorkScheduleInfoDTO = new SellerWorkScheduleInfoDTO();
        BeanUtils.copyProperties(sellerWorkScheduleInfoDO, sellerWorkScheduleInfoDTO);
        return sellerWorkScheduleInfoDTO;
    }

    public SellerWorkScheduleInfoVO convertDo2vo(SellerWorkScheduleInfoDO infoDO) {
        if (Objects.isNull(infoDO)) {
            return null;
        }
        SellerWorkScheduleInfoVO infoVO = new SellerWorkScheduleInfoVO();
        BeanUtils.copyProperties(infoDO, infoVO);
        return infoVO;
    }

    public SellerWorkScheduleInfoVO convertDo2vo(SellerWorkScheduleInfoDTO infoDTO) {
        if (Objects.isNull(infoDTO)) {
            return null;
        }

        SellerWorkScheduleInfoVO infoVO = new SellerWorkScheduleInfoVO();
        BeanUtils.copyProperties(infoDTO, infoVO);
        return infoVO;
    }

}