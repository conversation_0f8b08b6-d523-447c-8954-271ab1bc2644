package com.alibaba.tripscrm.service.model.domain.fusionchat.dto;

import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/7/15 09:58
 **/
@Data
public class MessageRelationInfoDTO {

    /**
     * 会话类型
     */
    private ChatTypeEnum chatTypeEnum;

    /**
     * 会话id
     */
    private String chatId;

    /**
     * 发送消息的微信企业员工id
     */
    private String sendUserId;

    /**
     * 操作的飞猪员工id
     */
    private String operatorId;

    public MessageRelationInfoDTO(ChatTypeEnum chatTypeEnum, String chatId, String sendUserId, String operatorId) {
        this.chatTypeEnum = chatTypeEnum;
        this.chatId = chatId;
        this.sendUserId = sendUserId;
        this.operatorId = operatorId;
    }

}
