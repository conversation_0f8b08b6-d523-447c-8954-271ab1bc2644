package com.alibaba.tripscrm.service.manager.opensearch;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.RobotDTO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.model.domain.query.WechatUserPageQuery;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.aliyun.opensearch.sdk.generated.search.Order;
import com.aliyun.opensearch.sdk.generated.search.SearchParams;
import com.aliyun.opensearch.sdk.generated.search.Sort;
import com.aliyun.opensearch.sdk.generated.search.SortField;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/24 18:54
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatUserOpenSearchManager extends BaseOpenSearchManager {
    /**
     * 查询指定企微成员信息
     *
     * @param userId 企微成员Id
     * @param corpId 企微组织Id
     * @return PageInfoDTO<WechatUserDTO>
     */
    public WechatUserDTO getByUserId(String userId, String corpId) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(corpId)) {
            PlatformLogUtil.logFail("Opensearch查询企微成员信息，参数非法", LogListUtil.newArrayList(userId, corpId));
            return null;
        }

        List<WechatUserDTO> wechatUserList = queryByUserIdList(Lists.newArrayList(userId), corpId);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            PlatformLogUtil.logFail("Opensearch查询企微成员信息，结果为空", LogListUtil.newArrayList(userId, corpId));
            return null;
        }

        return wechatUserList.get(0);
    }

    /**
     * 查询指定企微成员信息
     *
     * @param userIdList 企微成员Id
     * @param corpId     企微组织Id
     * @return PageInfoDTO<WechatUserDTO>
     */
    public List<WechatUserDTO> queryByUserIdList(List<String> userIdList, String corpId) {
        List<WechatUserDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(userIdList) || !StringUtils.hasText(corpId)) {
            PlatformLogUtil.logFail("Opensearch查询企微成员信息，参数非法", LogListUtil.newArrayList(userIdList, corpId));
            return null;
        }

        try {
            List<List<String>> partitions = Lists.partition(userIdList, 20);
            for (List<String> partition : partitions) {
                SearchParams searchParams = buildPageSearchParams(0, 20);
                searchParams.setQuery("(" + partition.stream().map(userId -> String.format("user_id:'%s'", userId)).collect(Collectors.joining(" OR ")) + ")");
                searchParams.setFilter(String.format("corp_id=\"%s\"", corpId));

                // 排序方式
                Sort sorter = new Sort();
                sorter.addToSortFields(new SortField("id", Order.DECREASE));
                searchParams.setSort(sorter);
                List<WechatUserDTO> wechatUserList = getSearchResultItems(getSearcherClient().execute(searchParams), this::convert2WechatUser);
                if (CollectionUtils.isEmpty(wechatUserList)) {
                    PlatformLogUtil.logFail("Opensearch批量查询企微成员信息，结果为空", LogListUtil.newArrayList(searchParams, wechatUserList));
                    continue;
                }
                PlatformLogUtil.logInfo("Opensearch批量查询企微成员信息，成功", LogListUtil.newArrayList(searchParams, wechatUserList));

                result.addAll(wechatUserList);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch批量查询企微成员信息，异常", e.getMessage(), e, LogListUtil.newArrayList(userIdList, corpId, e));
            return null;
        }
        return result;
    }

    /**
     * 查询成员信息
     *
     * @param query 查询条件
     * @return PageInfoDTO<WechatUserDTO>
     */
    public PageInfo<WechatUserDTO> pageQuery(WechatUserPageQuery query) {
        List<WechatUserDTO> res = new ArrayList<>();
        try {
            SearchParams searchParams = buildPageSearchParams(query, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (StringUtils.hasText(query.getNameLike())) {
                queryList.add(String.format("name:'%s'", query.getNameLike()));
            }

            if (StringUtils.hasText(query.getMemberId())) {
                queryList.add(String.format("member_id:'%s'", query.getMemberId()));
            }

            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            // 过滤条件
            List<String> filterList = new ArrayList<>();
            filterList.addAll(buildRobotFilterList(query));
            if (!CollectionUtils.isEmpty(query.getDepartmentIdList())) {
                filterList.add("(" + query.getDepartmentIdList().stream().map(code -> String.format("main_department='%d'", code)).collect(Collectors.joining(" OR ")) + ")");
            }
            if (Objects.nonNull(query.getGender())) {
                filterList.add(String.format("gender='%d'", query.getGender()));
            }
            if (Objects.nonNull(query.getActivateStatus())) {
                filterList.add(String.format("activate_status='%d'", query.getActivateStatus()));
            }
            filterList.add(String.format("status!='%d'", WechatUserStatusEnum.QUIT.getCode()));
            // 添加租户过滤参数
            String corpId = query.getCorpId();
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            if (query.getRobotType() != null) {
                filterList.add(String.format("robot_type='%d'", query.getRobotType()));
            }

            searchParams.setFilter(String.join(" AND ", filterList));

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);
            PageInfo<WechatUserDTO> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convert2WechatUser);
            PlatformLogUtil.logInfo("Opensearch分页查询企微成员信息成功", LogListUtil.newArrayList(searchParams, pageInfo));
            return pageInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch分页查询企微成员信息异常", e.getMessage(), e, LogListUtil.newArrayList(query, e));
        }

        return PageUtils.getPageInfo(res, a -> a);
    }

    private static List<String> buildRobotFilterList(WechatUserPageQuery query) {
        List<String> filterList = new ArrayList<>();
        // 服务商类型
        if (Objects.nonNull(query.getIsvType())) {
            filterList.add(String.format("isv_type_list=\"%d\"", query.getIsvType()));
        }

        if (CollectionUtils.isEmpty((query.getStatusList()))) {
            return filterList;
        }

        if (query.getStatusList().size() > 1) {
            filterList.add("(" + query.getStatusList().stream().map(code -> String.format("robot_status='%d'", code)).collect(Collectors.joining(" OR ")) + ")");
            return filterList;
        }

        if (NumberUtils.validInteger(query.getIsvType())) {
            switch (RobotStatusEnum.of(query.getStatusList().get(0))) {
                case RECYCLED:
                    filterList.add(String.format("isv_type_status_list!=\"%d_%d\"", query.getIsvType(), RobotStatusEnum.ONLINE.getCode()));
                    filterList.add(String.format("isv_type_status_list!=\"%d_%d\"", query.getIsvType(), RobotStatusEnum.OFFLINE.getCode()));
                    break;
                case OFFLINE:
                case ONLINE:
                    filterList.add(String.format("isv_type_status_list=\"%d_%d\"", query.getIsvType(), query.getStatusList().get(0)));
                    break;
                default:
                    break;
            }
            return filterList;
        }

        switch (RobotStatusEnum.of(query.getStatusList().get(0))) {
            case RECYCLED:
                filterList.add(String.format("isv_online_status_list!=\"%d\"", RobotStatusEnum.ONLINE.getCode()));
                filterList.add(String.format("isv_online_status_list!=\"%d\"", RobotStatusEnum.OFFLINE.getCode()));
                break;
            case ONLINE:
                filterList.add(String.format("isv_online_status_list=\"%d\"", RobotStatusEnum.ONLINE.getCode()));
                break;
            case OFFLINE:
                filterList.add(String.format("isv_online_status_list!=\"%d\"", RobotStatusEnum.ONLINE.getCode()));
                filterList.add(String.format("isv_online_status_list=\"%d\"", RobotStatusEnum.OFFLINE.getCode()));
            default:
                break;
        }
        return filterList;
    }

    private WechatUserDTO convert2WechatUser(JSONObject o) {
        WechatUserDTO wechatUserDTO = new WechatUserDTO();
        wechatUserDTO.setId(o.getLong("id"));
        wechatUserDTO.setUserId(o.getString("user_id"));
        wechatUserDTO.setDepartmentId(o.getInteger("main_department"));
        wechatUserDTO.setName(o.getString("name"));
        wechatUserDTO.setAlias(o.getString("alias"));
        wechatUserDTO.setRealName(o.getString("real_name"));
        wechatUserDTO.setGender(o.getInteger("gender"));
        wechatUserDTO.setOnlineStatus(o.getInteger("robot_status"));
        wechatUserDTO.setActivateStatus(o.getInteger("activate_status"));
        wechatUserDTO.setStatus(o.getInteger("status"));
        wechatUserDTO.setAvatarUrl(o.getString("avatar"));
        wechatUserDTO.setRobotType(o.getInteger("robot_type"));
        wechatUserDTO.setCorpId(o.getString("corp_id"));

        String isvTypeListStr = o.getString("isv_type_list");
        if (!StringUtils.hasText(isvTypeListStr)) {
            return wechatUserDTO;
        }

        List<IsvTypeEnum> isvTypeList = Arrays.stream(isvTypeListStr.split("\t")).map(Byte::parseByte).map(IsvTypeEnum::valueOf).collect(Collectors.toList());
        wechatUserDTO.setRobotList(new ArrayList<>());
        for (IsvTypeEnum isvTypeEnum : isvTypeList) {
            RobotDTO robotDTO = new RobotDTO();
            robotDTO.setUserId(o.getString("user_id"));
            JSONObject isvOnlineStatusJo = JSONObject.parseObject(o.getString("isv_online_status"));
            robotDTO.setIsvType(isvTypeEnum.getCode());
            for (String key : isvOnlineStatusJo.keySet()) {
                if (!Objects.equals(isvTypeEnum.getCode().toString(), key.split(":")[1])) {
                    continue;
                }

                robotDTO.setRobotStatus(isvOnlineStatusJo.getInteger(key));
                robotDTO.setIsvType(isvTypeEnum.getCode());
            }
            wechatUserDTO.getRobotList().add(robotDTO);
        }

        if (wechatUserDTO.getRobotList().stream().map(RobotDTO::getRobotStatus).anyMatch(status -> Objects.equals(RobotStatusEnum.ONLINE.getCode(), status))) {
            wechatUserDTO.setOnlineStatus(RobotStatusEnum.ONLINE.getCode());
        } else {
            wechatUserDTO.setOnlineStatus(RobotStatusEnum.OFFLINE.getCode());
        }
        return wechatUserDTO;
    }

    @Override
    protected String getAppName() {
        return "scrm_user_info";
    }

    @Override
    protected List<String> getFetchFields() {
        return Lists.newArrayList("id", "corp_id", "user_id", "main_department", "name", "alias", "real_name", "gender", "status", "activate_status", "robot_status", "avatar", "robot_type", "isv_online_status", "isv_type_list", "isv_type_status");
    }
}
