package com.alibaba.tripscrm.service.manager.opensearch;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.alibaba.tripscrm.domain.FieldCountInfo;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.aliyun.opensearch.SearcherClient;
import com.aliyun.opensearch.sdk.generated.search.Aggregate;
import com.aliyun.opensearch.sdk.generated.search.Config;
import com.aliyun.opensearch.sdk.generated.search.SearchFormat;
import com.aliyun.opensearch.sdk.generated.search.SearchParams;
import com.aliyun.opensearch.sdk.generated.search.general.SearchResult;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2024/7/24 17:45
 */
@Service
public abstract class BaseOpenSearchManager {
    protected static final String RESULT = "result";
    protected static final String ITEMS = "items";
    protected static final String TOTAL = "total";
    protected static final String COUNT = "count";
    protected static final String DISTINCT_COUNT = "distinct_count";
    protected static final String VALUE = "value";
    protected static final String KEY = "key";
    protected static final String FACET = "facet";

    @Autowired
    private SearcherClient searcherClient;

    protected SearcherClient getSearcherClient() {
        return searcherClient;
    }

    /**
     * 应用名称
     */
    protected abstract String getAppName();

    /**
     * 查询字段
     */
    protected abstract List<String> getFetchFields();

    protected SearchParams buildSearchParams() {
        Config config = new Config(Lists.newArrayList(getAppName()));
        config.setSearchFormat(SearchFormat.JSON);
        config.setFetchFields(getFetchFields());
        return new SearchParams(config);
    }

    protected SearchParams buildPageSearchParams(Integer pageNum, Integer pageSize) {
        // 分页条件
        Config config = new Config(Lists.newArrayList(getAppName()));
        config.setStart(pageNum);
        config.setHits(pageSize);
        config.setSearchFormat(SearchFormat.JSON);
        config.setFetchFields(getFetchFields());
        config.setKvpairs(null);
        return new SearchParams(config);
    }

    protected SearchParams buildPageSearchParams(BasePageRequest query, String kvPairs) {
        // 分页条件
        Config config = new Config(Lists.newArrayList(getAppName()));
        config.setStart((query.getPageNum() - 1) * query.getPageSize());
        config.setHits(query.getPageSize());
        config.setSearchFormat(SearchFormat.JSON);
        config.setFetchFields(getFetchFields());
        config.setKvpairs(kvPairs);
        return new SearchParams(config);
    }

    protected <R> PageInfo<R> getPageSearchResultItems(BasePageRequest query, SearchResult result, Function<JSONObject, R> convertor) {
        List<R> res = new ArrayList<>();
        JSONObject obj = JSONObject.parseObject(result.getResult());
        if (!obj.containsKey(RESULT)) {
            return PageUtils.getPageInfo(res, a -> a);
        }

        JSONObject resultJo = obj.getJSONObject(RESULT);
        if (!resultJo.containsKey(ITEMS)) {
            return PageUtils.getPageInfo(res, a -> a);
        }

        JSONArray items = resultJo.getJSONArray(ITEMS);
        for (int i = 0; i < items.size(); i++) {
            res.add(convertor.apply(items.getJSONObject(i)));
        }

        return PageUtils.getPageInfo(query.getPageNum(), query.getPageSize(), resultJo.getInteger(TOTAL), res);
    }

    protected <R> List<R> getSearchResultItems(SearchResult result, Function<JSONObject, R> convertor) {
        List<R> res = new ArrayList<>();
        JSONObject obj = JSONObject.parseObject(result.getResult());
        if (!obj.containsKey(RESULT)) {
            return res;
        }

        JSONObject resultJo = obj.getJSONObject(RESULT);
        if (!resultJo.containsKey(ITEMS)) {
            return res;
        }

        JSONArray items = resultJo.getJSONArray(ITEMS);
        for (int i = 0; i < items.size(); i++) {
            res.add(convertor.apply(items.getJSONObject(i)));
        }

        return res;
    }

    /**
     * 构建客户关系统计子句
     *
     * @param countFields 求和字段
     * @return 统计子句
     */
    protected Set<Aggregate> buildAggregate(List<String> countFields) {
        if (CollectionUtils.isEmpty(countFields)) {
            return Sets.newHashSet();
        }
        Set<Aggregate> aggregates = new HashSet<>();
        for (String field : countFields) {
            Aggregate aggregate = new Aggregate();
            aggregate.setGroupKey(field).setAggFun("count()");
            aggregates.add(aggregate);
        }
        return aggregates;
    }

    /**
     * 获取结果集中的数量字段
     *
     * @param result 结果集
     * @return 数量Map
     */
    protected Map<String, Map<String, Long>> getResultFacetCount(SearchResult result) {
        return getResultFacet(result, COUNT);
    }

    /**
     * 获取结果集中的去重数量字段
     *
     * @param result 结果集
     * @return 数量Map
     */
    protected Map<String, Map<String, Long>> getResultFacetDistinctCount(SearchResult result) {
        return getResultFacet(result, DISTINCT_COUNT);
    }

    /**
     * 获取结果集中的数量字段
     *
     * @param result 结果集
     * @return 数量Map
     */
    protected List<FieldCountInfo> getFacetCount(SearchResult result) {
        ArrayList<FieldCountInfo> fieldCountList = new ArrayList<>();
        Map<String, Map<String, Long>> countMap = getResultFacetCount(result);
        for (Map.Entry<String, Map<String, Long>> entry : countMap.entrySet()) {
            FieldCountInfo fieldCountInfo = new FieldCountInfo();
            fieldCountInfo.setField(entry.getKey());
            fieldCountInfo.setCountMap(entry.getValue());
            fieldCountList.add(fieldCountInfo);
        }
        return fieldCountList;
    }

    protected Map<String, Map<String, Long>> getResultFacet(SearchResult result, String countKey) {
        Map<String, Map<String, Long>> res = new HashMap<>();
        JSONObject obj = JSONObject.parseObject(result.getResult());
        if (!obj.containsKey(RESULT)) {
            return res;
        }

        JSONObject resultJo = obj.getJSONObject(RESULT);
        if (!resultJo.containsKey(FACET)) {
            return res;
        }

        JSONArray facets = resultJo.getJSONArray(FACET);
        for (int i = 0; i < facets.size(); i++) {
            String key = facets.getJSONObject(i).getString(KEY);
            JSONArray items = facets.getJSONObject(i).getJSONArray(ITEMS);
            Map<String, Long> map = new HashMap<>();
            res.put(key, map);
            for (int j = 0; j < items.size(); j++) {
                map.put(items.getJSONObject(j).getString(VALUE), items.getJSONObject(j).getLong(countKey));
            }
        }

        return res;
    }
}
