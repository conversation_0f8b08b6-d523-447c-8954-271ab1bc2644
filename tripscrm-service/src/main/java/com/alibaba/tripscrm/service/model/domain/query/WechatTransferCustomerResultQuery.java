package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.service.enums.wechat.WechatTransferCustomerStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WechatTransferCustomerResultQuery implements Serializable {
    /**
     * 最小主键Id
     */
    private Long minId;

    /**
     * 任务Id
     */
    private Long taskId;

    /**
     * 原添加成员的userId
     */
    private String handoverUserId;

    /**
     * 接替成员的userId
     */
    private String takeoverUserId;

    /**
     * 客户的外部联系人userId
     */
    private String externalUserId;

    /**
     * 发送状态：0-初始化 1-接替完毕 2-等待接替 3-客户拒绝 4-接替成员客户达到上限 5-非好友关系 6-其他
     */
    private List<WechatTransferCustomerStatusEnum> statusList;

    /**
     * 单次查询数量
     */
    private Integer size;

    /**
     * 最早创建时间
     */
    private Date startCreateTime;
}