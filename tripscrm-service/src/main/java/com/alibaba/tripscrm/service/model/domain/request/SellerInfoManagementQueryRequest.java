package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

@Data
public class SellerInfoManagementQueryRequest {

    /**
     * 商家id
     */
    private String sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 飞猪店铺查询 = sellerId
     */
    private String fliggyShopId;

    /**
     * 企微部门
     */
    private String departmentId;

    /**
     * SCRM空间
     */
    private Long scrmSpaceId;

    /**
     * 协议签署 0:未签署 1:已签署
     */
    private Integer protocolSign;

    /**
     * 商家启用状态 0:未启用 1:已启用
     */
    private Integer sellerStatus;

    /**
     * 添加的开始时间
     */
    private String addStartTime;

    /**
     * 添加的结束时间
     */
    private String addEndTime;

    /**
     * 分页数字
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;
}
