package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.ScrmConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.middleware.OdpsManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.strategy.account.TbSessionManager;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.RecordReader;
import com.aliyun.odps.tunnel.InstanceTunnel;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-08-02 15:05:38
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CallPushDataQueryProcessor extends AbstractTaskDataProcessor {
    private final OdpsManager odpsManager;
    private final TbSessionManager tbSessionManager;

    /**
     * 诸葛人群数据查询SQL
     */
    public static final String CROWD_ODPS_FETCH_SQL = "SELECT  DISTINCT user_id\n" +
            "FROM    trip_profile.dwd_trip_crowd_data_snapshot\n" +
            "WHERE   group_id = '%s';";

    @Switch(name = "SLEEP_PERIOD", description = "downloadSession获取失败时间隔多久再试一次。默认值30秒")
    public static Long sleepPeriod = 30 * 1000L;

    private static final long PAGE_SIZE = 100L;

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.CALL_PUSH);
    }

    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        Long taskId = context.getTaskId();
        Long taskInstanceId = context.getInstanceId();

        try {
            String downSql = getOdpsSql(context);
            PlatformLogUtil.logInfo("外呼推送任务数据查询器，生成Odps查询语句", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), downSql));
            InstanceTunnel.DownloadSession downloadSession = odpsManager.getDefaultDownloadSession(downSql);
            if (downloadSession == null) {
                Thread.sleep(sleepPeriod);
                downloadSession = odpsManager.getDefaultDownloadSession(downSql);
            }

            if (downloadSession == null) {
                PlatformLogUtil.logFail("外呼推送任务数据查询器，创建Odps下载会话失败", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), downSql));
                throw new TripscrmException(TripSCRMErrorCode.QUERY_ODPS_FAIL);
            }

            long recordCount = downloadSession.getRecordCount();
            if (recordCount == 0) {
                PlatformLogUtil.logFail("外呼推送任务数据查询器，Odps查询记录数为0", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), downSql));
                throw new TripscrmException(TripSCRMErrorCode.NOT_FOUND_TASK_EXECUTE_DATA);
            }
            PlatformLogUtil.logFail("外呼推送任务数据查询器，Odps查询记录数", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), downSql, recordCount));

            // 分页查询
            int totalPage = Long.valueOf((recordCount + PAGE_SIZE - 1) / PAGE_SIZE).intValue();
            for (int page = 0; page < totalPage; page++) {
                List<TaskDataVO.DataBodyVO> dataBodyVOS = readData(context, page, downloadSession);
                dataVO.getData().addAll(dataBodyVOS);
            }

            dataVO.setTotalCount((long) dataVO.getData().size());
        } catch (Exception e) {
            PlatformLogUtil.logException("外呼推送任务数据查询器，查询任务离线数据异常", e.getMessage(), e, LogListUtil.newArrayList(context.getInstanceId(), context.getTaskId()));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】查询任务离线数据异常，任务ID:%s，实例ID:%s", taskId, taskInstanceId));
            throw new TripscrmException(TripSCRMErrorCode.QUERY_ODPS_FAIL);
        }

        return dataVO;
    }

    private List<TaskDataVO.DataBodyVO> readData(TaskExecuteContext context, int page, InstanceTunnel.DownloadSession downloadSession) throws Exception {
        List<TaskDataVO.DataBodyVO> res = new ArrayList<>();
        Record record;
        List<Long> userIdList = new ArrayList<>();
        long start = page * PAGE_SIZE;
        try (RecordReader recordReader = downloadSession.openRecordReader(start, PAGE_SIZE)) {
            while ((record = recordReader.read()) != null) {
                userIdList.add(NumberUtils.toLong(record.getString("user_id")));
            }
        }

        Map<Long, String> userId2Phone = tbSessionManager.getPhoneListByUserIdList(userIdList);
        for (Long userId : userIdList) {
            if (!userId2Phone.containsKey(userId)) {
                PlatformLogUtil.logFail("外呼推送任务数据查询器，客户手机号未查询到", LogListUtil.newArrayList(context.getInstanceId(), context.getTaskId(), userId));
                continue;
            }

            TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
            dataBodyVO.setTargetId(String.valueOf(userId));
            dataBodyVO.setTargetType(ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode());
            JSONObject extraInfo = new JSONObject();
            extraInfo.put(ScrmConstant.PHONE, userId2Phone.get(userId));
            dataBodyVO.setExtInfo(extraInfo.toJSONString());
            res.add(dataBodyVO);
        }

        return res;
    }

    private String getOdpsSql(TaskExecuteContext context) {
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        Long crowdId = MapUtils.getLong(extInfo, TaskConstant.CROWD_ID_FIELD_KEY, -1L);
        if (!NumberUtils.validLong(crowdId)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        return String.format(CROWD_ODPS_FETCH_SQL, crowdId);
    }
}