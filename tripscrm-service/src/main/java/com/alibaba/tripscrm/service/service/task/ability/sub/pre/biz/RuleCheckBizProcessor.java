package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.manager.task.FatigueManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 单条任务数据处理_规则校验
 *
 * <AUTHOR>
 * @since 2024/4/22 15:22
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RuleCheckBizProcessor implements BizProcessor {
    private final FatigueManager fatigueManager;

    @Override
    @TaskExecuteLog("单条任务数据处理_规则校验")
    public TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception {
        if (context.getTestFlag()) {
            return TripSCRMResult.success(null);
        }

        String taskType = context.getTaskInfoDOSnapshot().getType();
        AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(TaskType.getByCode(taskType));
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        Integer messageCount = AbstractTaskExecutor.getMessageCount(context, todoTaskVO);
        boolean checkResult = fatigueManager.check(taskExecutor.getFinalTargetType(context), taskExecutor.getFinalTargetId(context, taskDataBody), context.getTaskInfoDOSnapshot(), messageCount, new Date());
        if (!checkResult) {
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_OVER_FATIGUE_LIMIT);
        }

        TripSCRMResult<Void> result = taskExecutor.checkValid(context, todoTaskVO);
        if (!result.isSuccess()) {
            throw new TripscrmException(result.getCode(), result.getMsg());
        }
        return TripSCRMResult.success(null);
    }
}