package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;

/**
 * <AUTHOR>
 * @date 2023-12-30 15:19:32
 */
public interface TaskConfigProcessor {
    /**
     * 任务额外信息（活码QRCode等）
     */
    JSONObject getExtraInfo(TaskInfoDO taskInfoDO);

    /**
     * 获取任务默认配置
     *
     * @param activityId 活动Id
     * @param taskName   任务名
     * @return
     */
    TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName);

    /**
     * 任务创建后执行的逻辑
     *
     * @param taskInfoDO
     */
    void postCreate(TaskInfoDO taskInfoDO);

    /**
     * 任务更新后执行的逻辑
     *
     * @param taskInfoDO
     */
    void postUpdate(TaskInfoDO taskInfoDO);

    /**
     * 任务删除后执行的逻辑
     *
     * @param taskInfoDO
     */
    void postDelete(TaskInfoDO taskInfoDO);

    /**
     * 任务类型
     *
     * @return
     */
    TaskType getTaskType();
}
