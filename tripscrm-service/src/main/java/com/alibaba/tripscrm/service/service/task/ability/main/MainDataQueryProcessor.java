package com.alibaba.tripscrm.service.service.task.ability.main;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.ability.data.query.AbstractTaskDataProcessor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 任务执行_主任务_数据查询
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MainDataQueryProcessor implements ExecuteProcessor {
    @Override
    @TaskExecuteLog("任务执行_主任务_数据查询")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        Long taskId = context.getTaskId();
        Long instanceId = context.getInstanceId();

        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        // 数据缺失，则不查询
        if (Objects.isNull(taskInfoDO) || Objects.isNull(taskInfoDO.getTriggerType())) {
            return null;
        }
        // 两种场景不需要查询：1. 携带数据进来的 2. 测试链路
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        boolean isTest = context.getTestFlag();
        if (isStreamTask || isTest) {
            PlatformLogUtil.logFail("主任务执行-数据查询，接口触发/事件触发/测试链路，无需查询", LogListUtil.newArrayList(taskId, instanceId, context));
            return TripSCRMResult.success(null);
        }

        // 正常全量数据查询
        PlatformLogUtil.logFail("主任务执行-数据查询，开始", LogListUtil.newArrayList(taskId, instanceId, context));
        // 读取数据并返回
        TaskDataVO taskDataVO = AbstractTaskDataProcessor.readAllData(context);

        // 设置到上下文中
        context.setTaskDataVO(taskDataVO);
        PlatformLogUtil.logInfo("主任务执行-数据查询，结束", LogListUtil.newArrayList(taskId, instanceId, taskDataVO.getTotalCount()));
        return TripSCRMResult.success(null);
    }
}
