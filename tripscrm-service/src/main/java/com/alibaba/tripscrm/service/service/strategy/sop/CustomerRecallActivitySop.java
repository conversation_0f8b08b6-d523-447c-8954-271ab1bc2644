package com.alibaba.tripscrm.service.service.strategy.sop;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.convert.TaskConverter;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.activity.CustomerRecallSopConfigVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskVO;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EXECUTION_PLAN;

/**
 * @description：单向好友召回活动SOP
 * @Author：wangrui
 * @create：2025/1/6 下午2:39
 * @Filename：CustomerRecallActivitySop
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerRecallActivitySop extends AbstractActivitySop<CustomerRecallSopConfigVO> {
    private final TaskService taskService;
    private final MaterialService materialService;

    @Switch(description = "顾客召回活动触发时间类型")
    public static String CUSTOMER_RECALL_ACTIVITY_TRIGGER_TIME_TYPE = "day";
    @Switch(description = "顾客召回活动周期触发：触发时间类型对应的值")
    public static String CUSTOMER_RECALL_ACTIVITY_TRIGGER_TIME_VALUE = "1";
    public static String MAXREACHDAYS = "maxReachDays";
    public static String MAXRECALLCOUNT = "maxRecallCount";


    @Override
    protected void checkConfigValid(CustomerRecallSopConfigVO config, Boolean isUpdate) {
        // 验证语不可为空
        if (CollectionUtils.isEmpty(config.getAddCustomerSendMessageList())) {
            throw new TripscrmException("验证语不可为空");
        }
        //最新客户触达时间不能小于30天
        if (!NumberUtils.validInteger(config.getMaxReachDays()) || config.getMaxReachDays() < 30) {
            throw new TripscrmException("最新客户触达时间无效或小于30天");
        }
        //单账户当日召回人数不能大于40
        if (!NumberUtils.validInteger(config.getMaxRecallCount()) || config.getMaxRecallCount() > 40) {
            throw new TripscrmException("单账户当日召回人数无效或小于40");
        }


    }

    @Override
    protected void createTaskList(CustomerRecallSopConfigVO config, Long activityId) {
//        // 1.创建发送私聊任务
//        createSingleChatTask(config, activityId);
        // 2.创建召回好友任务
        createRecallTask(config, activityId);
    }

    private void createRecallTask(CustomerRecallSopConfigVO config, Long activityId) {


        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.CUSTOMER_RECALL).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addTenYear(config.getEffectEndTime()));
        taskInfoDO.setMaterialId(config.getMaterialId());
        taskInfoDO.setTriggerType(Integer.parseInt(EXECUTION_PLAN.getCode()));
        String triggerTimeCron = getTriggerTimeCron(config);
        taskInfoDO.setTriggerTimeCron(triggerTimeCron);
        String executeStartTime = config.getExecuteStartTime();
        String executeEndTime = config.getExecuteEndTime();
        taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);

        taskInfoDO.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put(MAXREACHDAYS, config.getMaxReachDays());
        extraInfo.put(MAXRECALLCOUNT, config.getMaxRecallCount());
        extraInfo.put(TaskConstant.ADD_CUSTOMER_SEND_MESSAGE_LIST, config.getAddCustomerSendMessageList());
        extraInfo.put(TaskConstant.TRIGGER_TIME_TYPE, CUSTOMER_RECALL_ACTIVITY_TRIGGER_TIME_TYPE);
        extraInfo.put(TaskConstant.TRIGGER_TIME_VALUE, CUSTOMER_RECALL_ACTIVITY_TRIGGER_TIME_VALUE);
        if(!CollectionUtils.isEmpty(config.getUserIdList())){
            extraInfo.put(TaskConstant.WECHAT_USER_ID_List, config.getUserIdList());
        }
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建私聊任务失败");
        }
    }

//    private void createSingleChatTask(CustomerRecallSopConfigVO config, Long activityId) {
//        if (!NumberUtils.validLong(config.getMaterialId())) {
//            return;
//        }
//
//        String triggerTimeCron = getTriggerTimeCron(config);
//
//        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.UNILATERAL_CHAT_MESSAGE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
//        taskInfoDO.setEffectStartTime(new Date());
//        taskInfoDO.setEffectEndTime(DateUtils.addTenYear(config.getEffectEndTime()));
//        taskInfoDO.setMaterialId(config.getMaterialId());
//        taskInfoDO.setTriggerType(Integer.parseInt(EXECUTION_PLAN.getCode()));
//        taskInfoDO.setTriggerTimeCron(triggerTimeCron);
//
//        taskInfoDO.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
//        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
//        extraInfo.put(MAXREACHDAYS, config.getMaxReachDays());
//        extraInfo.put(MAXRECALLCOUNT, config.getMaxRecallCount());
//        extraInfo.put(TaskConstant.TRIGGER_TIME_TYPE, CUSTOMER_RECALL_ACTIVITY_TRIGGER_TIME_TYPE);
//        extraInfo.put(TaskConstant.TRIGGER_TIME_VALUE, CUSTOMER_RECALL_ACTIVITY_TRIGGER_TIME_VALUE);
//        if(!CollectionUtils.isEmpty(config.getUserIdList())){
//            extraInfo.put(TaskConstant.WECHAT_USER_ID_List, config.getUserIdList());
//        }
//        taskInfoDO.setExtInfo(extraInfo.toJSONString());
//        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
//        if (effectLines == 0) {
//            throw new TripscrmException("创建私聊任务失败");
//        }
//
//    }

    private static  String getTriggerTimeCron(CustomerRecallSopConfigVO config) {
        TaskVO taskVO = new TaskVO();
        taskVO.setTriggerType(Integer.parseInt(EXECUTION_PLAN.getCode()));
        taskVO.setTriggerTimeType(CUSTOMER_RECALL_ACTIVITY_TRIGGER_TIME_TYPE);
        taskVO.setTriggerTimeValue(CUSTOMER_RECALL_ACTIVITY_TRIGGER_TIME_VALUE);
        // 任务开始时间段、结束时间段
        String executeStartTime = config.getExecuteStartTime();
        String executeEndTime = config.getExecuteEndTime();
        taskVO.setExecuteStartTime(executeStartTime);
        return TaskConverter.generateCron(taskVO);
    }

    @Override
    protected void updateTaskList(CustomerRecallSopConfigVO config) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        //召回好友任务
        if (taskType2TaskInfoDO.containsKey(TaskType.CUSTOMER_RECALL.getCode())) {
            TaskInfoDO recallTask = taskType2TaskInfoDO.get(TaskType.CUSTOMER_RECALL.getCode());
            updateRecallTask(config, recallTask);
        } else {
            createRecallTask(config, config.getActivityId());
        }

    }

    private void updateRecallTask(CustomerRecallSopConfigVO config, TaskInfoDO taskInfoDO) {

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        String executeStartTime = config.getExecuteStartTime();
        String executeEndTime = config.getExecuteEndTime();
        taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);
        String triggerTimeCron = getTriggerTimeCron(config);
        taskInfoDO.setTriggerTimeCron(triggerTimeCron);
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put(MAXREACHDAYS, config.getMaxReachDays());
        extraInfo.put(MAXRECALLCOUNT, config.getMaxRecallCount());
        extraInfo.put(TaskConstant.WECHAT_USER_ID_List,
                CollectionUtils.isEmpty(config.getUserIdList())
                        ? Collections.emptyList()
                        : config.getUserIdList());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
    }

//    private void updateSingleChatTask(CustomerRecallSopConfigVO config, TaskInfoDO taskInfoDO) {
//        // 活动改名字了
//        if (!taskInfoDO.getName().startsWith(config.getName())) {
//            taskInfoDO.setName(getTaskName(config));
//        }
//        taskInfoDO.setMaterialId(config.getMaterialId());
//        String executeStartTime = config.getExecuteStartTime();
//        String executeEndTime = config.getExecuteEndTime();
//        String triggerTimeCron = getTriggerTimeCron(config);
//        taskInfoDO.setTriggerTimeCron(triggerTimeCron);
//        taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);
//        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
//        extraInfo.put(MAXREACHDAYS, config.getMaxReachDays());
//        extraInfo.put(MAXRECALLCOUNT, config.getMaxRecallCount());
//
//        extraInfo.put(TaskConstant.WECHAT_USER_ID_List,
//                CollectionUtils.isEmpty(config.getUserIdList())
//                        ? Collections.emptyList()
//                        : config.getUserIdList());
//        taskInfoDO.setExtInfo(extraInfo.toJSONString());
//        taskService.updateTaskInfo(taskInfoDO, false);
//        Integer effectLines = taskService.updateTaskInfo(taskInfoDO,false);
//
//    }

    @Override
    public SopTypeEnum getSopType() {
        return SopTypeEnum.CUSTOMER_RECALL;
    }

    @Override
    public void fillInfo(ActivitySopVO<CustomerRecallSopConfigVO> activitySopVO) {
        CustomerRecallSopConfigVO config = activitySopVO.getConfig();
        // 包含标签信息
        if (config.getMaterialId() != null) {
            MaterailInfoDO materailInfoDO = materialService.queryById(config.getMaterialId());
            config.setMaterialName(materailInfoDO.getName());
        }
    }
}
