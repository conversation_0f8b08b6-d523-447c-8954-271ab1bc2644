package com.alibaba.tripscrm.service.service.impl.wechat;

import com.alibaba.tripscrm.dal.mapper.tddl.WechatContactMeMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatContactMeDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatContactMeQuery;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.wechat.WechatContactMeService;
import com.alibaba.tripzoo.proxy.api.service.ContactMeService;
import com.alibaba.tripzoo.proxy.model.WechatContactMeBO;
import com.alibaba.tripzoo.proxy.request.AddContactMeRequest;
import com.alibaba.tripzoo.proxy.request.DeleteContactMeRequest;
import com.alibaba.tripzoo.proxy.request.UpdateContactMeRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-01-01 15:16:25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatContactMeServiceImpl implements WechatContactMeService {
    private final ContactMeService contactMeService;
    private final WechatContactMeMapper wechatContactMeMapper;
    private final SpaceService spaceService;
    private final ResourceRelationService resourceRelationService;

    @Override
    public WechatContactMeDO create(List<String> userIdList, Boolean skipVerify, String state, Long spaceId) {
        // 调用
        String corpId = spaceService.getCorpIdBySpaceId(spaceId);
        AddContactMeRequest request = new AddContactMeRequest();
        request.setUserIdList(userIdList);
        request.setSkipVerify(skipVerify);
        request.setCorpId(corpId);
        request.setState(state);
        ResultDO<WechatContactMeBO> resultDO = contactMeService.addContactMe(request);

        if (Objects.isNull(resultDO)) {
            throw new RuntimeException("系统出错");
        }

        if (!resultDO.getSuccess()) {
            throw new RuntimeException(resultDO.getResultMessage());
        }
        WechatContactMeBO wechatContactMeBO = resultDO.getModel();
        WechatContactMeDO wechatContactMeDO = new WechatContactMeDO();
        wechatContactMeDO.setConfigId(wechatContactMeBO.getConfigId());
        wechatContactMeDO.setType(wechatContactMeBO.getType());
        wechatContactMeDO.setScene(wechatContactMeBO.getScene());
        wechatContactMeDO.setSkipVerify(wechatContactMeBO.getSkipVerify());
        wechatContactMeDO.setRemark(Optional.ofNullable(wechatContactMeBO.getRemark()).orElse(""));
        wechatContactMeDO.setState(wechatContactMeBO.getState());
        wechatContactMeDO.setQrCode(wechatContactMeBO.getQrCode());
        if (CollectionUtils.isEmpty(wechatContactMeBO.getParty())) {
            wechatContactMeBO.setParty(new ArrayList<>());
        }

        if (CollectionUtils.isEmpty(wechatContactMeBO.getUser())) {
            wechatContactMeBO.setUser(new ArrayList<>());
        }
        wechatContactMeDO.setParty(wechatContactMeBO.getParty().stream().map(String::valueOf).collect(Collectors.joining(",")));
        wechatContactMeDO.setUser(wechatContactMeBO.getUser().stream().map(String::valueOf).collect(Collectors.joining(",")));
        wechatContactMeDO.setCorpId(corpId);
        int effectLines = wechatContactMeMapper.insert(wechatContactMeDO);
        if (effectLines == 0) {
            throw new RuntimeException("数据写入失败");
        }

        return wechatContactMeDO;
    }

    @Override
    public Integer update(Long id, List<String> userIdList, Boolean skipVerify) {
        WechatContactMeDO wechatContactMeDO = wechatContactMeMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wechatContactMeDO)) {
            throw new RuntimeException("要修改的个码信息不存在！");
        }

        // 调用
        UpdateContactMeRequest request = new UpdateContactMeRequest();
        request.setUserIdList(userIdList);
        request.setSkipVerify(skipVerify);
        request.setConfigId(wechatContactMeDO.getConfigId());
        request.setCorpId(wechatContactMeDO.getCorpId());
        ResultDO<WechatContactMeBO> resultDO = contactMeService.updateContactMe(request);

        if (Objects.isNull(resultDO)) {
            throw new RuntimeException("系统出错");
        }

        if (!resultDO.getSuccess()) {
            throw new RuntimeException(resultDO.getResultMessage());
        }

        WechatContactMeBO wechatContactMeBO = resultDO.getModel();
        wechatContactMeDO = new WechatContactMeDO();
        wechatContactMeDO.setId(id);
        wechatContactMeDO.setConfigId(wechatContactMeBO.getConfigId());
        wechatContactMeDO.setType(wechatContactMeBO.getType());
        wechatContactMeDO.setScene(wechatContactMeBO.getScene());
        wechatContactMeDO.setSkipVerify(wechatContactMeBO.getSkipVerify());
        wechatContactMeDO.setRemark(wechatContactMeBO.getRemark());
        wechatContactMeDO.setState(wechatContactMeBO.getState());
        wechatContactMeDO.setQrCode(wechatContactMeBO.getQrCode());

        if (CollectionUtils.isEmpty(wechatContactMeBO.getParty())) {
            wechatContactMeBO.setParty(new ArrayList<>());
        }

        if (CollectionUtils.isEmpty(wechatContactMeBO.getUser())) {
            wechatContactMeBO.setUser(new ArrayList<>());
        }

        wechatContactMeDO.setParty(wechatContactMeBO.getParty().stream().map(String::valueOf).collect(Collectors.joining(",")));
        wechatContactMeDO.setUser(wechatContactMeBO.getUser().stream().map(String::valueOf).collect(Collectors.joining(",")));

        return wechatContactMeMapper.updateByPrimaryKey(wechatContactMeDO);
    }

    @Override
    @AteyeInvoker(description = "删除个人活码信息", paraDesc = "id")
    public Integer delete(Long id) {
        WechatContactMeDO wechatContactMeDO = wechatContactMeMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wechatContactMeDO)) {
            throw new RuntimeException("数据不存在");
        }

        wechatContactMeDO.setDeleted(1);
        DeleteContactMeRequest request = new DeleteContactMeRequest();
        request.setConfig(wechatContactMeDO.getConfigId());
        request.setCorpId(wechatContactMeDO.getCorpId());
        ResultDO<Void> resultDO = contactMeService.deleteContactMe(request);
        if (Objects.isNull(resultDO)) {
            throw new RuntimeException("系统出错");
        }

        if (!resultDO.getSuccess()) {
            throw new RuntimeException(resultDO.getResultMessage());
        }

        return wechatContactMeMapper.updateByPrimaryKey(wechatContactMeDO);
    }

    @Override
    public WechatContactMeDO getById(Long id) {
        return wechatContactMeMapper.selectByPrimaryKey(id);
    }

    @Override
    public WechatContactMeDO getByState(String state) {
        return wechatContactMeMapper.selectByState(state);
    }

    @Override
    public List<WechatContactMeDO> getByCondition(WechatContactMeQuery query) {
        return wechatContactMeMapper.selectByCondition(query);
    }

    @Override
    public String buildMakerPage(Long spaceId, String state) {
        String pageUrl = SwitchConfig.CONTACT_ME_MARKER_PAGE;
        // 个性化链接查询
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.SPACE_LINK_RELATION.getCode());
        query.setSourceType(ResourceTypeEnum.SPACE.getCode().byteValue());
        query.setSourceId(String.valueOf(spaceId));
        query.setTargetType(ResourceTypeEnum.CONCAT_ME_PAGE_MARKER_LINK.getCode().byteValue());
        List<ResourceRelationDO> resourceRelationList = resourceRelationService.query(query);
        if (!CollectionUtils.isEmpty(resourceRelationList) && StringUtils.isNotBlank(resourceRelationList.get(0).getTargetId())) {
            pageUrl = resourceRelationList.get(0).getTargetId();
        }

        return String.format(pageUrl, state);
    }
}
