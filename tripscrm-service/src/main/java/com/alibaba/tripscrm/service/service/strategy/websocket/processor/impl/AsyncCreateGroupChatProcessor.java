package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.manager.tair.LdbLockManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.CreateGroupChatRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.CreateGroupChatResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.dingtalk.DingRobotUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.request.GroupCreateRequest;
import com.alibaba.tripzoo.proxy.request.GroupInviteJoinRequest;
import com.alibaba.tripzoo.proxy.result.GroupInviteJoinResponse;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.*;

import static com.alibaba.tripscrm.service.constant.TairConstant.CREATE_GROUP_RECORD;
import static com.alibaba.tripscrm.service.constant.TairConstant.CREATE_GROUP_RECORD_LOCK;
import static com.alibaba.tripscrm.service.service.impl.fusionchat.ChatConversationServiceImpl.DEFAULT_GROUP_AVATAR;

/**
 * 异步发起群聊 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j
public class AsyncCreateGroupChatProcessor implements WsEventProcessor {
    /**
     * 异步操作传输数据的过期时间 30分钟
     */
    private static final Integer ASYNC_OPERATE_EXPIRE_SECOND = 30 * 60;
    public static final String CACHE_KEY = "createGroupChat|";
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private GroupService groupService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private LdbLockManager ldbLockManager;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private IsvRouteService isvRouteService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.CREATE_GROUP_CHAT;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        CreateGroupChatRequest request = wsEvent.getData().toJavaObject(CreateGroupChatRequest.class);
        if (wsEvent.getUserId() == null) {
            throw new TripscrmException("缺少必要的参数 userId");
        }
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        if (!wechatUserService.checkHasLock(wsEvent.getUserId(), wsContextInfo.getAccount().getUserId(), wsContextInfo.getSpaceId())) {
            throw new TripscrmException("未锁定当前企微账号，无法执行该操作");
        }
        // 通过RPC调用proxy Api，发起群聊
        GroupCreateRequest groupCreateRequest = new GroupCreateRequest();
        groupCreateRequest.setGroupName(request.getChatName());
        groupCreateRequest.setUserId(wsEvent.getUserId());
        groupCreateRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<String> result = groupService.asyncCreate(groupCreateRequest);
        if (result.getSuccess()) {
            PlatformLogUtil.logInfo("异步发起创建群聊，请求发送成功", LogListUtil.newArrayList(request, result, session.getId(), wsEvent.getUserId()));
            // 保存群聊上下文，以便回调时取到群聊数据，key为requestId
            wsEvent.setTimestamp(System.currentTimeMillis());
            ldbTairManager.put(CACHE_KEY + result.getModel(), JSONObject.toJSONString(wsEvent), ASYNC_OPERATE_EXPIRE_SECOND);
            // 记录还未创建成功的群聊createGroupRecord
            this.addCreateGroupRecord(result.getModel());
        } else {
            PlatformLogUtil.logFail("异步发起创建群聊，请求发送失败", LogListUtil.newArrayList(request, result, session.getId(), wsEvent.getUserId()));
            throw new TripscrmException("群聊创建失败：" + result.getResultMessage());
        }
    }

    /**
     * 成功创建群聊后置动作
     */
    public WsEvent afterCreate(String requestId, String chatId) {
        // 获取群聊上下文
        String eventJson = (String) ldbTairManager.get(CACHE_KEY + requestId);
        if (eventJson != null) {
            WsEvent wsEvent = JSONObject.parseObject(eventJson, WsEvent.class);
            // 判断是否超过5分钟，若超过，则推送告警
            long costTime = System.currentTimeMillis() - wsEvent.getTimestamp();
//            if (costTime > 1000 * 60 * 5) {
            DingRobotUtils.sendMessageForFusionChat(wsEvent.getUserId(), wsEvent.getType(), "创建群聊耗时" + costTime / 1000 + "秒，wsEvent:" + JSONObject.toJSONString(wsEvent));
//            }
            CreateGroupChatRequest request = wsEvent.getData().toJavaObject(CreateGroupChatRequest.class);
            // 通过RPC调用proxy Api，添加群成员
            GroupInviteJoinRequest groupInviteJoinRequest = new GroupInviteJoinRequest();
            groupInviteJoinRequest.setUserId(wsEvent.getUserId());
            groupInviteJoinRequest.setChatId(chatId);
            if (request.getUserIds() != null) {
                request.getUserIds().remove(wsEvent.getUserId());
            }
            groupInviteJoinRequest.setUserIdList(request.getUserIds());
            groupInviteJoinRequest.setExternalUserIdList(request.getExternalUserIds());
            groupInviteJoinRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
            ResultDO<GroupInviteJoinResponse> result = groupService.asyncInviteJoinGroup(groupInviteJoinRequest);
            if (result.getSuccess()) {
                PlatformLogUtil.logInfo("成功创建群聊后置动作，邀请入群成功", LogListUtil.newArrayList(request, result));
            } else {
                PlatformLogUtil.logFail("成功创建群聊后置动作，邀请入群失败", LogListUtil.newArrayList(request, result));
            }
            return wsEvent;
        }
        return null;
    }

    public void pushMessageByDistributed(WsEvent wsEvent, String chatId, String groupName) {
        CreateGroupChatResponse createGroupChatResponse = new CreateGroupChatResponse();
        createGroupChatResponse.setChatId(chatId);
        createGroupChatResponse.setChatType(ChatTypeEnum.GROUP.getValue());
        createGroupChatResponse.setChatName(groupName);
        createGroupChatResponse.setTopNo(0);
        createGroupChatResponse.setChatAvatar(DEFAULT_GROUP_AVATAR);
        createGroupChatResponse.setUpdateTimestamp(System.currentTimeMillis());
        createGroupChatResponse.setGroupWork(true);
        wsEvent.setData((JSONObject) JSONObject.toJSON(createGroupChatResponse));
        webSocketFactory.pushMessageByDistributed(wsEvent);
    }

    /**
     * 记录还未创建成功的群聊
     *
     * @param groupRequestId groupRequestId
     */
    private void addCreateGroupRecord(String groupRequestId) {
        try {
            ldbLockManager.lockWithRunnable(CREATE_GROUP_RECORD_LOCK, 3, true, () -> {
                Map<String, Long> createGroupRecordMap = this.getCreateGroupRecordMap();
                createGroupRecordMap.put(groupRequestId, System.currentTimeMillis());
                ldbTairManager.put(CREATE_GROUP_RECORD, JSONObject.toJSONString(createGroupRecordMap));
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("记录还未创建成功的群聊失败", e.getMessage(), e, LogListUtil.newArrayList(groupRequestId));
        }
    }

    /**
     * 删除记录创建成功的群聊
     *
     * @param groupRequestId groupRequestId
     */
    public void deleteCreateGroupRecord(String groupRequestId) {
        try {
            ldbLockManager.lockWithRunnable(CREATE_GROUP_RECORD_LOCK, 3, true, () -> {
                Map<String, Long> createGroupRecordMap = this.getCreateGroupRecordMap();
                createGroupRecordMap.remove(groupRequestId);
                ldbTairManager.put(CREATE_GROUP_RECORD, JSONObject.toJSONString(createGroupRecordMap));
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("删除记录创建成功的群聊失败", e.getMessage(), e, LogListUtil.newArrayList(groupRequestId));
        }
    }

    /**
     * 获取还未创建成功的群聊列表
     *
     * @return return
     */
    public Map<String, Long> getCreateGroupRecordMap() {
        Map<String, Long> createGroupRecordMap = new HashMap<>();
        String createGroupRecord = (String) ldbTairManager.get(CREATE_GROUP_RECORD);
        if (createGroupRecord != null) {
            createGroupRecordMap = JSONObject.parseObject(createGroupRecord, new TypeReference<Map<String, Long>>() {
            });
        }
        return createGroupRecordMap;
    }
}
