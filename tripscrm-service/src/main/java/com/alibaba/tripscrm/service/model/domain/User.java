package com.alibaba.tripscrm.service.model.domain;

import com.alibaba.tripscrm.service.service.strategy.account.BucManager;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/3/3
 **/
@Data
public class User {

    private String userId;

    private String userName;

    private Boolean isInnerStaff;

    public User() { }

    public User(String userId) {
        this(userId, null, false);
    }

    public User(String userId, String userName, boolean isInnerStaff) {
        this.userId = BucManager.formatEmpId(userId);
        this.userName = userName;
        this.isInnerStaff = isInnerStaff;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof User)) {
            return false;
        }
        User user = (User) o;
        return BucManager.empIdEquals(getUserId(), user.getUserId());
    }
}
