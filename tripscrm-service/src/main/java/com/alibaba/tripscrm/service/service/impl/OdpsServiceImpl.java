package com.alibaba.tripscrm.service.service.impl;

import com.alibaba.tripscrm.service.constant.OdpsSqlConstant;
import com.alibaba.tripscrm.service.manager.middleware.OdpsManager;
import com.alibaba.tripscrm.service.model.dto.tag.TagCoverCustomerDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagCoverGroupDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.OdpsService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.RecordReader;
import com.aliyun.odps.tunnel.InstanceTunnel;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/8 14:52
 **/
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class OdpsServiceImpl implements OdpsService {

    private final OdpsManager odpsManager;

    @Override
    public List<TagCoverCustomerDTO> queryTagCoverCustomerInfo(String corpId, Set<Long> tagIdList) {
        if (CollectionUtils.isEmpty(tagIdList)) {
            return Lists.newArrayList();
        }
        List<Record> recordList = queryTagCoverRecord(tagIdList, String.format(OdpsSqlConstant.TAG_COVER_CUSTOMER_INFO, corpId, tagIdList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(", "))));
        if (CollectionUtils.isEmpty(recordList)) {
            return Lists.newArrayList();
        }
        return recordList.stream().map(this::buildTagCoverCustomerDTO).collect(Collectors.toList());
    }

    @Override
    public List<TagCoverGroupDTO> queryTagCoverGroupInfo(String corpId, Set<Long> tagIdList) {
        if (CollectionUtils.isEmpty(tagIdList)) {
            return Lists.newArrayList();
        }
        List<Record> recordList = queryTagCoverRecord(tagIdList, String.format(OdpsSqlConstant.TAG_COVER_GROUP_INFO, corpId, tagIdList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(", "))));
        if (CollectionUtils.isEmpty(recordList)) {
            return Lists.newArrayList();
        }
        return recordList.stream().map(this::buildTagCoverGroupDTO).collect(Collectors.toList());
    }

    /**
     * 查询标签覆盖的记录使用(群/客户)
     * @param tagIdList 标签id列表
     * @param sql 查询SQL
     * @return 记录列表
     */
    private List<Record> queryTagCoverRecord(Set<Long> tagIdList, String sql) {
        ArrayList<Record> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(tagIdList) || StringUtils.isBlank(sql)) {
            PlatformLogUtil.logFail("查询参数为空", LogListUtil.newArrayList(tagIdList, sql));
            return result;
        }
        PlatformLogUtil.logFail("标签覆盖查询SQL", LogListUtil.newArrayList(sql));
        try {
            InstanceTunnel.DownloadSession downloadSession = odpsManager.getDefaultDownloadSession(sql);
            if (downloadSession == null) {
                PlatformLogUtil.logFail("ODPS资源链接获取失败", LogListUtil.newArrayList(tagIdList, sql));
                throw new TripscrmException("ODPS资源链接获取失败");
            }
            long recordCount = downloadSession.getRecordCount();
            if (recordCount == 0) {
                PlatformLogUtil.logFail("ODPS资源大小为0", LogListUtil.newArrayList(tagIdList, sql));
                return result;
            }
            PlatformLogUtil.logFail("标签覆盖查询数量", LogListUtil.newArrayList(sql, recordCount));
            Record record;
            RecordReader recordReader = downloadSession.openRecordReader(0, recordCount);
            while ((record = recordReader.read()) != null) {
                result.add(record);
            }
            return result;
        } catch (Exception e) {
            PlatformLogUtil.logFail("ODPS读取数据失败", LogListUtil.newArrayList(tagIdList));
            throw new TripscrmException("ODPS读取数据失败", e);
        }
    }

    /**
     * 构建标签覆盖客户对象
     * @param record 离线读取记录
     * @return 客户对象
     */
    private TagCoverCustomerDTO buildTagCoverCustomerDTO(Record record) {
        TagCoverCustomerDTO tagCoverCustomerDTO = new TagCoverCustomerDTO();
        tagCoverCustomerDTO.setUserId(record.getString("user_id"));
        tagCoverCustomerDTO.setUnionId(record.getString("union_id"));
        tagCoverCustomerDTO.setExternalUserId(record.getString("external_user_id"));
        tagCoverCustomerDTO.setFollowUserId(record.getString("follow_user_id"));
        tagCoverCustomerDTO.setTagIds(record.getString("tag_ids"));
        tagCoverCustomerDTO.setWechatTagNames(record.getString("wechat_tag_names"));
        return tagCoverCustomerDTO;
    }

    /**
     * 构建标签覆盖群聊对象
     * @param record 离线读取记录
     * @return 群聊对象
     */
    private TagCoverGroupDTO buildTagCoverGroupDTO(Record record) {
        TagCoverGroupDTO tagCoverGroupDTO = new TagCoverGroupDTO();
        tagCoverGroupDTO.setChatId(record.getString("chat_id"));
        tagCoverGroupDTO.setCorpId(record.getString("corp_id"));
        tagCoverGroupDTO.setOwnerUser(record.getString("owner_user"));
        tagCoverGroupDTO.setAdminUser(record.getString("admin_user"));
        tagCoverGroupDTO.setTags(record.getString("tags"));
        tagCoverGroupDTO.setNotice(record.getString("notice"));
        return tagCoverGroupDTO;
    }
}
