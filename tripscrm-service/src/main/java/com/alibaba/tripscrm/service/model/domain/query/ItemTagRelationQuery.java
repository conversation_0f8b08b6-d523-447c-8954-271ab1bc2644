package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/6/6 19:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemTagRelationQuery extends BasePageRequest {
    /**
     * 标签id
     */
    private Long tagId;

    /**
     * 是否删除
     */
    private Byte deleted = 0;

    /**
     * item类型
     */
    private Integer itemType;

    /**
     * 关联id
     */
    private String itemId;

    /**
     * 虚拟标签的具体值
     */
    private List<String> subCodeList;

    /**
     * item_id的标签归属关系
     */
    private String relationId;

    public void setTag(String tagId) {
        if (!StringUtils.hasText(tagId)) {
            return;
        }

        if (!tagId.contains("_")) {
            this.tagId = Long.valueOf(tagId);
            return;
        }

        String[] split = tagId.split("_");
        this.tagId = Long.valueOf(split[0]);
        this.subCodeList = Lists.newArrayList(split[1]);
    }
}
