package com.alibaba.tripscrm.service.service.strategy.websocket.param.request;

import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/27 14:19
 **/
@Data
public class GetUserConversationListRequest {

    /**
     * 请求最小排序数
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 200;

    /**
     * 目标聊天ID
     */
    private List<String> chatIdList;

    /**
     * 群聊类型
     * @see ChatTypeEnum
     */
    private Integer chatType;

    /**
     * 非全量
     */
    private Boolean append;
}
