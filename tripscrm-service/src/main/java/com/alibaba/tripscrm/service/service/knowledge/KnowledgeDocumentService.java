package com.alibaba.tripscrm.service.service.knowledge;


import com.alibaba.tripscrm.dal.model.domain.data.KnowledgeDocumentDO;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.DocumentReq;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.DocumentSyncFaiReq;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.DocumentTestReq;
import com.alibaba.tripscrm.service.model.dto.knowledge.KnowledgeDocumentDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 知识文档
 * @date 2025/9/15
 */
public interface KnowledgeDocumentService {

    /**
     * 根据条件查询知识文档
     */
    List<KnowledgeDocumentDTO> selectByParam(DocumentReq query);

    /**
     * 根据条件查询（分页）
     */
    PageInfoDTO<KnowledgeDocumentDTO> pageQuery(DocumentReq query);

    /**
     * 新增知识文档
     */
    Long insert(KnowledgeDocumentDO knowledgeDocumentDO);

    /**
     * 根据主键ID查询知识文档
     */
    KnowledgeDocumentDTO selectById(Long id);

    /**
     * 更新知识文档
     */
    Long update(KnowledgeDocumentDTO knowledgeDocumentDO);

    /**
     * 根据主键ID删除知识文档
     */
    boolean deleteById(Long id, String userId, String userName);

    /**
     * 同步知识文档上下线
     */
    boolean syncFai(DocumentSyncFaiReq request);

    /**
     * 知识文档自测
     */
    Boolean selfTest(DocumentTestReq req);
}
