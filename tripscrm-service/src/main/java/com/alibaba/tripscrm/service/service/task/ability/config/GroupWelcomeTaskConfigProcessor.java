package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatJoinGroupDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatJoinGroupService;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;

/**
 * 群活码管理任务配置处理器
 *
 * <AUTHOR>
 * @date 2023-12-30 15:26:28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupWelcomeTaskConfigProcessor extends AbstractTaskConfigProcessor {
    @Switch(description = "企微客户入群（群欢迎语）事件源", name = "wechatCustomerJoinGroupEventSourceId")
    public static Long wechatCustomerJoinGroupEventSourceId = 21L;

    private final TaskService taskService;
    private final WechatJoinGroupService wechatJoinGroupService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        JSONObject extraInfoJson = super.getExtraInfo(taskInfoDO);
        TaskQuery query = new TaskQuery();
        query.setActivityId(taskInfoDO.getActivityId());
        query.setType(TaskType.GET_WECHAT_JOIN_GROUP.getCode());
        List<TaskInfoDO> taskList = taskService.query(query);
        if (CollectionUtils.isEmpty(taskList)) {
            return extraInfoJson;
        }

        TaskInfoDO getJoinGroupTask = taskList.get(0);
        JSONObject configJson = JSONObject.parseObject(getJoinGroupTask.getConfig());
        if (!configJson.containsKey("wechatJoinGroupId")) {
            return extraInfoJson;
        }

        Long wechatJoinGroupId = configJson.getLong("wechatJoinGroupId");
        String state = configJson.getString("contextId");
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getById(wechatJoinGroupId);
        extraInfoJson.put("qrCodeUrl", wechatJoinGroupDO.getQrCode());
        extraInfoJson.put("state", state);
        extraInfoJson.put("chatIdList", wechatJoinGroupDO.getChatIdList());
        return extraInfoJson;
    }

    @Override
    public TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName) {
        TaskInfoDO taskInfoDO = super.getInitConfig(activityId, spaceId, taskName);
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        Date effectEndTime = new Date(LocalDate.of(2040, 12, 30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        taskInfoDO.setEffectEndTime(effectEndTime);
        taskInfoDO.setEventSourceId(wechatCustomerJoinGroupEventSourceId);
        return taskInfoDO;
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.GROUP_WELCOME;
    }
}
