package com.alibaba.tripscrm.service.service.task.ability.data.split;

import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:05:38
 */
@Component
public class SplitBySizeProcessor extends AbstractTaskDataSplitProcessor {
    @Switch(name = "taskDataSplitSize", description = "任务数据拆分-分片大小")
    public static Integer taskDataSplitSize = 10000;

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.CALL_PUSH, TaskType.SINGLE_CHAT_GROUP_MSG, TaskType.SEND_TO_MOMENTS, TaskType.ALIPAY_GROUP_MSG, TaskType.ALIPAY_DIRECT_MSG);
    }

    @Override
    protected List<List<TaskDataVO.DataBodyVO>> handleSplitData(TaskExecuteContext context) {
        List<TaskDataVO.DataBodyVO> allData = context.getTaskDataVO().getData();
        return Lists.partition(allData, taskDataSplitSize);
    }
}