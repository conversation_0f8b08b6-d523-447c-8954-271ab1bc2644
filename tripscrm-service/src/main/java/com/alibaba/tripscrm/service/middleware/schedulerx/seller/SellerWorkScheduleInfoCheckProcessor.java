package com.alibaba.tripscrm.service.middleware.schedulerx.seller;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.seller.SellerWorkScheduleInfoQuery;
import com.alibaba.tripscrm.service.model.dto.seller.SellerInfoDTO;
import com.alibaba.tripscrm.service.model.dto.seller.SellerWorkScheduleInfoDTO;
import com.alibaba.tripscrm.service.service.seller.SellerInfoService;
import com.alibaba.tripscrm.service.service.seller.SellerWorkScheduleInfoService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商家排班信息巡检定时任务
 * 找出没有兜底排班的商家，并随机选取一个可用账号作为兜底账号。
 *
 * <AUTHOR>
 * @since 2025/9/23 11:32
 */
@Component
public class SellerWorkScheduleInfoCheckProcessor extends JavaProcessor {
    private final SellerInfoService sellerInfoService;
    private final WechatUserService wechatUserService;
    private final SellerWorkScheduleInfoService sellerWorkScheduleInfoService;

    @Switch(description = "商家排班信息巡检-修改开关", name = "modifySwitch")
    public static boolean modifySwitch = true;

    public SellerWorkScheduleInfoCheckProcessor(SellerInfoService sellerInfoService, WechatUserService wechatUserService, SellerWorkScheduleInfoService sellerWorkScheduleInfoService) {
        this.sellerInfoService = sellerInfoService;
        this.wechatUserService = wechatUserService;
        this.sellerWorkScheduleInfoService = sellerWorkScheduleInfoService;
    }

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.sellerCorpId);
            List<SellerInfoDTO> validSellerInfoList = sellerInfoService.listAllValid();
            for (SellerInfoDTO sellerInfo : validSellerInfoList) {
                SellerWorkScheduleInfoQuery query = new SellerWorkScheduleInfoQuery();
                query.setSellerId(Long.parseLong(sellerInfo.getSellerId()));
                query.setDayOfWeekList(Lists.newArrayList((byte) 0));
                List<SellerWorkScheduleInfoDTO> list = sellerWorkScheduleInfoService.list(query);
                if (CollectionUtils.isEmpty(list)) {
                    PlatformLogUtil.logFail("商家排班信息巡检定时任务，商家空间下未设置兜底账号", LogListUtil.newArrayList(sellerInfo, list));
                    setBackUpSellerWorkScheduleInfo(sellerInfo, null);
                    continue;
                }
                SellerWorkScheduleInfoDTO sellerWorkScheduleInfo = list.get(0);
                List<String> wechatUserIdList = Arrays.stream(sellerWorkScheduleInfo.getWechatUserIds().split(",")).collect(Collectors.toList());
                WechatUserQuery wechatUserQuery = new WechatUserQuery();
                wechatUserQuery.setUserIdList(wechatUserIdList);
                wechatUserQuery.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
                wechatUserQuery.setStatus(WechatUserStatusEnum.ACTIVE.getCode().byteValue());
                wechatUserQuery.setSpaceId(sellerInfo.getSpaceId());
                List<WechatUserDTO> wechatUserList = wechatUserService.listByCondition(wechatUserQuery);
                if (!CollectionUtils.isEmpty(wechatUserList)) {
                    PlatformLogUtil.logFail("商家排班信息巡检定时任务，商家空间下设置的兜底账号当前不可用", LogListUtil.newArrayList(sellerInfo, list, wechatUserList));
                    continue;
                }
                setBackUpSellerWorkScheduleInfo(sellerInfo, sellerWorkScheduleInfo);
            }
            return new ProcessResult(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("商家排班信息巡检定时任务，执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return new ProcessResult(false);
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    /**
     * 设置兜底排班信息
     */
    private void setBackUpSellerWorkScheduleInfo(SellerInfoDTO sellerInfo, SellerWorkScheduleInfoDTO sellerWorkScheduleInfo) {
        WechatUserQuery wechatUserQuery = new WechatUserQuery();
        wechatUserQuery.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wechatUserQuery.setStatus(WechatUserStatusEnum.ACTIVE.getCode().byteValue());
        wechatUserQuery.setSpaceId(sellerInfo.getSpaceId());
        List<WechatUserDTO> wechatUserList = wechatUserService.listByCondition(wechatUserQuery);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            PlatformLogUtil.logFail("商家排班信息巡检定时任务，商家空间下无可用账号", LogListUtil.newArrayList());
            return;
        }

        if (!modifySwitch) {
            return;
        }

        // 随机选取一个可用账号作为兜底账号
        WechatUserDTO wechatUser = wechatUserList.get(0);
        if (Objects.isNull(sellerWorkScheduleInfo)) {
            SellerWorkScheduleInfoDTO newSellerWorkScheduleInfo = new SellerWorkScheduleInfoDTO();
            newSellerWorkScheduleInfo.setSellerId(Long.parseLong(sellerInfo.getSellerId()));
            newSellerWorkScheduleInfo.setSpaceId(sellerInfo.getSpaceId());
            newSellerWorkScheduleInfo.setDayOfWeek((byte) 0);
            newSellerWorkScheduleInfo.setBeginTime("00:00");
            newSellerWorkScheduleInfo.setEndTime("23:59");
            newSellerWorkScheduleInfo.setWechatUserIds(wechatUser.getUserId());
            sellerWorkScheduleInfoService.createSelective(newSellerWorkScheduleInfo);
            PlatformLogUtil.logInfo("商家排班信息巡检定时任务，商家空间下未设置兜底账号，现在新增", LogListUtil.newArrayList(sellerInfo, newSellerWorkScheduleInfo, wechatUserList));
            return;
        }
        SellerWorkScheduleInfoDTO newSellerWorkScheduleInfo = new SellerWorkScheduleInfoDTO();
        newSellerWorkScheduleInfo.setWechatUserIds(wechatUser.getUserId());
        SellerWorkScheduleInfoQuery condition = new SellerWorkScheduleInfoQuery();
        condition.setId(sellerWorkScheduleInfo.getId());
        sellerWorkScheduleInfoService.updateSelective(newSellerWorkScheduleInfo, condition);
        PlatformLogUtil.logInfo("商家排班信息巡检定时任务，商家空间下设置的兜底账号不可用，现在修改", LogListUtil.newArrayList(sellerInfo, sellerWorkScheduleInfo, newSellerWorkScheduleInfo, wechatUserList));
    }
}
