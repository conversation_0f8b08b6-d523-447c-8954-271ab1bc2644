package com.alibaba.tripscrm.service.synchronizer;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;



@Slf4j
@Data
public class SyncResult<T> {
    private Boolean success;
    private String message;
    private T data;

    SyncResult(Boolean success) {
        this(success, null, null);
    }

    SyncResult(Boolean success, String message) {
        this(success, message, null);
    }

    SyncResult(Boolean success, String message, T data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public static <T> SyncResult<T> success() {
        return new SyncResult<>(true, null, null);
    }

    public static <T> SyncResult<T> success(String message) {
        return new SyncResult<>(true, message, null);
    }

    public static <T> SyncResult<T> success(T data) {
        return new SyncResult<>(true, null, data);
    }

    public static <T> SyncResult<T> success(String message, T data) {
        return new SyncResult<>(true, message, data);
    }

    public static <T> SyncResult<T> fail() {
        return new SyncResult<>(false, null, null);
    }

    public static <T> SyncResult<T> fail(String message) {
        return new SyncResult<>(false, message, null);
    }

    public static <T> SyncResult<T> fail(T data) {
        return new SyncResult<>(false, null, data);
    }

    public static <T> SyncResult<T> fail(String message, T data) {
        return new SyncResult<>(false, message, data);
    }
}