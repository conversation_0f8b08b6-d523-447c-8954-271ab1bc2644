package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceExecuteStatusEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceSaveVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskInstanceExecuteResultVO;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;

import java.util.List;

/**
 * 子任务实例Service
 *
 * <AUTHOR>
 */
public interface SubTaskInstanceService {
    /**
     * 新增子任务实例信息
     *
     * @param request
     * @return 主键ID
     */
    Long add(SubTaskInstanceSaveVO request);

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    SubTaskInstanceDO queryById(Long id);

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    SubTaskInstanceDO queryByIdWithCache(Long id);

    /**
     * 查询列表,不带分页
     *
     * @param request 查询条件
     * @return
     */
    List<SubTaskInstanceDO> list(SubTaskInstanceQueryRequest request);

    /**
     * 更新子任务实例执行状态
     *
     * @param id                 主键ID
     * @param taskInstanceExecuteStatusEnum 执行执行状态
     * @return
     */
    Boolean updateSubTaskStatus(Long id, Boolean isRetry, TaskInstanceExecuteStatusEnum taskInstanceExecuteStatusEnum);

    /**
     * 从Tair中获取子任务执行结果
     */
    JSONObject getExecuteResultFromTair(Long id);

    /**
     * 获取子任务执行结果
     *
     * @param subTaskInstanceDO
     * @return
     */
    TaskInstanceExecuteResultVO getExecuteResult(SubTaskInstanceDO subTaskInstanceDO);

    /**
     * 更新子任务实例执行进度
     */
    void updateSubTaskInstanceExecuteResult(TaskExecuteContext context, TaskDataVO.DataBodyVO dataBodyVO);

    /**
     * 获取子任务重试延时等级
     */
    MetaQDeleyLevel getRetryDelayLevel(TaskExecuteContext context, TripSCRMErrorCode errorCode);

    /**
     * 更新子任务实例信息
     *
     * @param request
     * @return
     */
    Integer update(SubTaskInstanceSaveVO request);

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return
     */
    Integer deleteById(Long id);

    /**
     * 根据主键ID更新
     *
     * @return
     */
    Integer updateById(SubTaskInstanceDO subTaskInstanceDO);
}
