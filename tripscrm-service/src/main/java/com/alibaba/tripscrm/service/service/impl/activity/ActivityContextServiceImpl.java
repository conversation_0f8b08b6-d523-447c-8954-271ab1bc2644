package com.alibaba.tripscrm.service.service.impl.activity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.mapper.lindorm.ActivityTaskInfoDAO;
import com.alibaba.tripscrm.dal.model.lindorm.ActivityTaskInfoDO;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.tag.TagUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.tddl.client.sequence.Sequence;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * <p>
 * 实例任务信息服务
 */
@Slf4j
@Service
public class ActivityContextServiceImpl implements ActivityContextService {

    /**
     * 构造器注入
     */
    private final ActivityTaskInfoDAO activityTaskInfoDAO;
    private final Sequence activityTaskIdSequence;

    public ActivityContextServiceImpl(ActivityTaskInfoDAO activityTaskInfoDAO, @Qualifier("activityTaskIdSequence") Sequence activityTaskIdSequence) {
        this.activityTaskInfoDAO = activityTaskInfoDAO;
        this.activityTaskIdSequence = activityTaskIdSequence;
    }

    @Override
    public Long generateContextId() {
        return activityTaskIdSequence.nextValue();
    }

    @AteyeInvoker(description = "更新活动上下文", paraDesc = "jsonStr")
    public int upsert(String jsonStr) {
        ActivityTaskInfoBO bo = JSONObject.parseObject(jsonStr, new TypeReference<ActivityTaskInfoBO>() {
        });

        if (Objects.isNull(bo) || bo.invalid()) {
            return 0;
        }

        ActivityTaskInfoDO record = convertToDO(bo);
        if (Objects.isNull(record)) {
            return 0;
        }

        return activityTaskInfoDAO.upsert(record);
    }

    @Override
    public int upsert(ActivityTaskInfoBO bo) {
        if (Objects.isNull(bo) || bo.invalid()) {
            return 0;
        }

        ActivityTaskInfoDO record = convertToDO(bo);
        if (Objects.isNull(record)) {
            return 0;
        }

        return activityTaskInfoDAO.upsert(record);
    }

    @Override
    public int batchUpsert(List<ActivityTaskInfoBO> bos) {
        if (CollectionUtils.isEmpty(bos)) {
            return 0;
        }

        List<ActivityTaskInfoDO> records = batchConvertToDO(bos);
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }

        return activityTaskInfoDAO.batchUpsert(records);
    }

    @Override
    public int deleteByTaskId(Long contextId) {
        if (!NumberUtils.validLong(contextId)) {
            return 0;
        }

        String pkey = NumberUtils.toReverseStr(contextId);
        return activityTaskInfoDAO.deleteByPrimaryKey(pkey);
    }

    @Override
    public int batchDeleteByTaskId(List<Long> contextIds) {
        if (CollectionUtils.isEmpty(contextIds)) {
            return 0;
        }

        List<String> pkeys = contextIds.stream()
                .filter(NumberUtils::validLong)
                .map(NumberUtils::toReverseStr)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        return activityTaskInfoDAO.batchDeleteByPrimaryKey(pkeys);
    }

    @Override
    public ActivityTaskInfoBO queryByTaskId(Long contextId) {
        if (!NumberUtils.validLong(contextId)) {
            return null;
        }

        String pkey = NumberUtils.toReverseStr(contextId);
        ActivityTaskInfoDO record = activityTaskInfoDAO.queryByPrimaryKey(pkey);

        return convertToBO(record);
    }

    @Override
    public List<ActivityTaskInfoBO> batchQueryByTaskId(List<Long> contextIds) {
        if (CollectionUtils.isEmpty(contextIds)) {
            return Lists.newArrayList();
        }

        List<String> pkeys = contextIds.stream()
                .filter(NumberUtils::validLong)
                .map(NumberUtils::toReverseStr)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        List<ActivityTaskInfoDO> records = activityTaskInfoDAO.batchQueryByPrimaryKey(pkeys);

        return batchConvertToBO(records);
    }

    @Override
    public List<ActivityTaskInfoBO> queryByActivityAndTarget(ActivityTaskInfoBO query) {
        ActivityTaskInfoDO dQuery = convertToDO(query);
        if (Objects.isNull(dQuery)) {
            return Lists.newArrayList();
        }

        List<ActivityTaskInfoDO> records = activityTaskInfoDAO.queryByActivityAndTarget(dQuery);
        return batchConvertToBO(records);
    }

    @Override
    public List<ActivityTaskInfoBO> queryByTarget(ActivityTaskInfoBO query) {
        ActivityTaskInfoDO dQuery = convertToDO(query);
        if (Objects.isNull(dQuery)) {
            return Lists.newArrayList();
        }

        List<ActivityTaskInfoDO> records = activityTaskInfoDAO.queryByTarget(dQuery);
        return batchConvertToBO(records);
    }

    @Override
    public List<ActivityTaskInfoBO> batchQueryByActivityAndTarget(List<ActivityTaskInfoBO> queries) {
        if (CollectionUtils.isEmpty(queries)) {
            return Lists.newArrayList();
        }

        List<ActivityTaskInfoDO> dQueries = batchConvertToDO(queries);
        if (CollectionUtils.isEmpty(dQueries)) {
            return Lists.newArrayList();
        }

        List<ActivityTaskInfoDO> records = activityTaskInfoDAO.batchQueryByActivityAndTarget(dQueries);
        return batchConvertToBO(records);
    }

    /**
     * 批量bo转do
     *
     * @param bos bo列表
     * @return do列表
     */
    private List<ActivityTaskInfoDO> batchConvertToDO(List<ActivityTaskInfoBO> bos) {
        if (CollectionUtils.isEmpty(bos)) {
            return Lists.newArrayList();
        }

        return bos.stream()
                .map(this::convertToDO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * bo转do
     *
     * @param bo bo
     * @return do
     */
    private ActivityTaskInfoDO convertToDO(ActivityTaskInfoBO bo) {
        if (Objects.isNull(bo)) {
            return null;
        }

        ActivityTaskInfoDO result = new ActivityTaskInfoDO();
        BeanUtils.copyProperties(bo, result);
        ;
        result.setTaskIdReverse(NumberUtils.toReverseStr(bo.getContextId()));
        result.setTargetType(bo.getTargetTypeEnum().getCode());
        result.setTags(NumberUtils.concat(bo.getTagIdList(), NumberUtils.SEPARATOR_COMMA));
        if (Objects.nonNull(bo.getExtraJson())) {
            result.setExtra(bo.getExtraJson().toJSONString());
        }

        return result;
    }

    /**
     * 批量do转bo
     *
     * @param records do列表
     * @return bo列表
     */
    private List<ActivityTaskInfoBO> batchConvertToBO(List<ActivityTaskInfoDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream()
                .map(this::convertToBO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * do转bo
     *
     * @param record do
     * @return bo
     */
    private ActivityTaskInfoBO convertToBO(ActivityTaskInfoDO record) {
        if (Objects.isNull(record)) {
            return null;
        }

        ActivityTaskInfoBO result = new ActivityTaskInfoBO();
        BeanUtils.copyProperties(record, result);
        result.setContextId(NumberUtils.fromReverseStr(record.getTaskIdReverse()));
        if (Objects.nonNull(record.getTargetType())) {
            result.setTargetTypeEnum(ActivityTargetTypeEnum.codeOf(record.getTargetType()));
        }

        result.setTagIdList(TagUtils.getTagList(record.getTags()));
        if (StringUtils.isNotBlank(record.getExtra())) {
            try {
                JSONObject extraJson = JSON.parseObject(record.getExtra());
                if (Objects.nonNull(extraJson)) {
                    result.setExtraJson(extraJson);
                }
            } catch (Exception e) {
                log.warn("ActivityTaskInfoServiceImpl convertToBO exception, record={}", record, e);
            }
        }

        return result;
    }
}
