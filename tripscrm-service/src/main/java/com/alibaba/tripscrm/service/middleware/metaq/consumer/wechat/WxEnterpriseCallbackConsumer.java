package com.alibaba.tripscrm.service.middleware.metaq.consumer.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.wechat.EnterpriseWeChatEventEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.EventMetaInfo;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.task.ability.old.FissionTaskServiceImpl;
import com.alibaba.tripscrm.service.service.task.ability.old.WelcomeTaskServiceImpl;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import com.taobao.trip.wireless.mc.client.hsf.EnterpriseWeChatService;
import com.taobao.trip.wireless.mc.client.hsf.domain.weChat.WxCustomerInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.List;

/**
 * 企微事件回调消息消费
 */
@Slf4j
@Service(value = "wxEnterpriseCallbackConsumer")
public class WxEnterpriseCallbackConsumer implements MessageListenerConcurrently {

    @Autowired
    private MetaqProducer metaqProducer;

    @Autowired
    private EnterpriseWeChatService enterpriseWeChatService;

    @Autowired
    private WelcomeTaskServiceImpl welcomeTaskService;

    @Autowired
    private FissionTaskServiceImpl fissionTaskService;

    @Autowired
    private WechatCustomerService wechatCustomerService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("企微事件回调消息消费", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("企微事件回调消息消费失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            SpaceInfoThreadLocalUtils.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            PlatformLogUtil.logInfo("企微事件回调消息消费，消息处理", LogListUtil.newArrayList(message));
            JSONObject data = JSONObject.parseObject(message);
            String wxMsg = data.getString("wxMsg");
            String groupId = data.getString("groupId");

            //对xml信息进行解析
            EventMetaInfo eventMetaInfo = covertMessage(wxMsg);
            if (eventMetaInfo == null) {
                PlatformLogUtil.logFail("企微事件回调消息消费，消息体为空");
                return false;
            }
            //解析后的数据
            PlatformLogUtil.logInfo("企微事件回调消息消费，解析后的数据", LogListUtil.newArrayList(eventMetaInfo));
            // 获取用户unionid
            String unionId;
            List<WechatCustomerVO> wechatCustomerList = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(eventMetaInfo.getExternalUserID()));
            if (!CollectionUtils.isEmpty(wechatCustomerList)) {
                unionId = wechatCustomerList.get(0).getUnionId();
            } else {
                WxCustomerInfoVO wxInfoByCustomerId = enterpriseWeChatService.getWxInfoByCustomerId(groupId, eventMetaInfo.getExternalUserID());
                if (wxInfoByCustomerId == null || StringUtils.isEmpty(wxInfoByCustomerId.getUnionId())) {
                    PlatformLogUtil.logFail("企微事件回调消息消费，wxInfoByCustomerId为空", LogListUtil.newArrayList(wxInfoByCustomerId));
                    return false;
                }

                unionId = wxInfoByCustomerId.getUnionId();
            }

            // 多个业务执行回执信息处理操作，
            // 行程管家
            eventMetaInfo = welcomeTaskService.getCallBackInfo(queryJourneyActivity(), eventMetaInfo, unionId);
            // 裂变
            eventMetaInfo = fissionTaskService.getCallBackInfo(queryWxFissionActivity(), eventMetaInfo, unionId);

            // 发送消息
            String key = eventMetaInfo.getState() + System.currentTimeMillis();
            String sendMsg = JSON.toJSONString(eventMetaInfo);
            data.put("wxMsg", sendMsg);
            if (!metaqProducer.send(MQEnum.TRIPSCRM_ENTERPRISE_WX_SEND_SYNC, key, "", JSON.toJSONString(data), null)) {
                PlatformLogUtil.logFail("企微事件回调消息消费，发送wxEnterpriseCallbackSyncProducer失败", LogListUtil.newArrayList(key, data));
                return false;
            }
            PlatformLogUtil.logFail("企微事件回调消息消费，发送wxEnterpriseCallbackSyncProducer成功", LogListUtil.newArrayList(key, data));
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
        return true;
    }


    /**
     * 行程管家
     *
     * @return
     */
    private Long queryJourneyActivity() {
        // 行程管家
        return 1L;
    }


    /**
     * 查询活动Id，绑定后动态获取
     *
     * @return
     */
    private Long queryWxFissionActivity() {
        // 企微裂变
        return 3L;
    }


    /**
     * 将企微中xml格式数据转换为bean模式
     *
     * @param xmltext
     * @return
     */
    public EventMetaInfo covertMessage(String xmltext) {

        try {
            DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
            dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            dbf.setFeature("http://xml.org/sax/features/external-general-entities", false);
            dbf.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            DocumentBuilder db = dbf.newDocumentBuilder();
            StringReader sr = new StringReader(xmltext);
            InputSource is = new InputSource(sr);
            Document document = db.parse(is);
            Element root = document.getDocumentElement();

            String changeType = root.getElementsByTagName("ChangeType").item(0).getTextContent();

            String descByName = EnterpriseWeChatEventEnum.getDescByName(changeType);
            if (StringUtils.isNotEmpty(descByName)) {

                NodeList state = root.getElementsByTagName("State");
                NodeList welcomeCode = root.getElementsByTagName("WelcomeCode");
                NodeList source = root.getElementsByTagName("Source");
                EventMetaInfo eventMetaInfo = new EventMetaInfo();

                eventMetaInfo.setToUserName(root.getElementsByTagName("ToUserName").item(0).getTextContent());
                eventMetaInfo.setFromUserName(root.getElementsByTagName("FromUserName").item(0).getTextContent());
                eventMetaInfo.setCreateTime(Long.valueOf(root.getElementsByTagName("CreateTime").item(0).getTextContent()));
                eventMetaInfo.setMsgType(root.getElementsByTagName("MsgType").item(0).getTextContent());
                eventMetaInfo.setEvent(root.getElementsByTagName("Event").item(0).getTextContent());
                eventMetaInfo.setChangeType(changeType);
                eventMetaInfo.setUserID(root.getElementsByTagName("UserID").item(0).getTextContent());
                eventMetaInfo.setExternalUserID(root.getElementsByTagName("ExternalUserID").item(0).getTextContent());

                if (state != null && state.item(0) != null) {
                    eventMetaInfo.setState(state.item(0).getTextContent());
                }
                if (welcomeCode != null && welcomeCode.item(0) != null) {
                    eventMetaInfo.setWelcomeCode(welcomeCode.item(0).getTextContent());
                }
                if (source != null && source.item(0) != null) {
                    eventMetaInfo.setSource(source.item(0).getTextContent());
                }

                return eventMetaInfo;
            }
            PlatformLogUtil.logFail("将企微中xml格式数据转换为bean模式", LogListUtil.newArrayList(descByName));
            return null;

        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(xmltext));
            return null;
        }
    }
}
