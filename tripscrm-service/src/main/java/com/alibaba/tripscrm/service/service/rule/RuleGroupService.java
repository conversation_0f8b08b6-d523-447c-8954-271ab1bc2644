package com.alibaba.tripscrm.service.service.rule;

import com.alibaba.tripscrm.dal.model.domain.data.RuleGroupDO;
import com.alibaba.tripscrm.service.model.domain.query.RuleGroupQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;


/**
 * 规则组相关接口
 *
 * <AUTHOR>
 * @since 2024/6/6 19:33
 */
public interface RuleGroupService {
    /**
     * 根据id查询规则组
     */
    RuleGroupDO selectById(Long id);

    /**
     * 根据条件查询规则组
     */
    List<RuleGroupDO> selectByCondition(RuleGroupQuery query);

    /**
     * 根据条件查询规则组
     */
    PageInfo<RuleGroupDO> pageQuery(RuleGroupQuery query);

    /**
     * 创建规则组
     */
    int create(RuleGroupDO ruleGroupDO);

    /**
     * 更新规则组
     */
    int update(RuleGroupDO ruleGroupDO);

    /**
     * 删除规则组
     */
    int delete(Long id);
}
