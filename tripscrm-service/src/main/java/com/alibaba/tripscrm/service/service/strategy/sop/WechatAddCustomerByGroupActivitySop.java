package com.alibaba.tripscrm.service.service.strategy.sop;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterialTemplateInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialTemplateInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.convert.WechatUserConverter;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatAddCustomerByGroupActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTemplateService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;

/**
 * 个人活码SOP
 *
 * <AUTHOR>
 * @date 2023-12-30 14:50:08
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatAddCustomerByGroupActivitySop extends AbstractActivitySop<WechatAddCustomerByGroupActivitySopConfigVO> {
    private final TaskService taskService;
    private final MaterialService materialService;
    private final MaterialTemplateService materialTemplateService;
    private final TagInfoService tagInfoService;
    private final WechatUserService wechatUserService;
    private final WechatUserConverter wechatUserConverter;

    @Override
    protected void checkConfigValid(WechatAddCustomerByGroupActivitySopConfigVO config, Boolean isUpdate) {
        // 目前只有群内艾特企微成员时触发好友添加，所以默认都是true
        if (Objects.isNull(config.getAddCustomerIfAt())) {
            config.setAddCustomerIfAt(true);
        }

        if (!config.getAddCustomerIfAt()) {
            throw new RuntimeException("@企微号会话必选");
        }

        // 客户自动打标标签列表
        if (Objects.isNull(config.getCustomerAutoAddTagList())) {
            config.setCustomerAutoAddTagList(new ArrayList<>());
        }

        // 客户自动打标标签列表长度不可超过20
        if (config.getCustomerAutoAddTagList().size() > 20) {
            throw new RuntimeException("标签列表长度不可超过20");
        }

        List<String> userIdList = Optional.ofNullable(config.getUserIdList()).orElse(new ArrayList<>()).stream().filter(StringUtils::hasText).collect(Collectors.toList());
        List<String> chatIdList = Optional.ofNullable(config.getChatIdList()).orElse(new ArrayList<>()).stream().filter(StringUtils::hasText).collect(Collectors.toList());
        List<String> addCustomerSendMessageList = Optional.ofNullable(config.getAddCustomerSendMessageList()).orElse(new ArrayList<>()).stream().filter(StringUtils::hasText).collect(Collectors.toList());

        config.setUserIdList(userIdList);
        config.setChatIdList(chatIdList);
        config.setAddCustomerSendMessageList(addCustomerSendMessageList);

        // 企微号不可为空
        if (CollectionUtils.isEmpty(config.getUserIdList())) {
            throw new RuntimeException("企微号不可为空");
        }

        // 客户群不可为空
        if (CollectionUtils.isEmpty(config.getChatIdList())) {
            throw new RuntimeException("客户群不可为空");
        }

        // 验证语不可为空
        if (CollectionUtils.isEmpty(config.getAddCustomerSendMessageList())) {
            throw new RuntimeException("验证语不可为空");
        }

        // 企微号列表长度不可超过10
        if (config.getUserIdList().size() > 10) {
            throw new RuntimeException("企微号列表长度不可超过10");
        }

        // 客户群列表长度不可超过10
        if (config.getChatIdList().size() > 10) {
            throw new RuntimeException("客户群列表长度不可超过10");
        }

        // 验证语列表长度不可超过10
        if (config.getAddCustomerSendMessageList().size() > 10) {
            throw new RuntimeException("企微号列表长度不可超过20");
        }

        // 验证语长度不可超过30
        if (config.getAddCustomerSendMessageList().stream().anyMatch(s -> !StringUtils.hasText(s) || s.codePointCount(0, s.length()) > 30)) {
            throw new RuntimeException("验证语长度为1-30");
        }

        // 没有配置欢迎语
        if (Optional.ofNullable(config.getWelcomeTaskMaterialId()).orElse(-1L) == -1L) {
            config.setWelcomeTaskMaterialId(-1L);
            return;
        }

        MaterailInfoDO materailInfoDO = materialService.queryById(config.getWelcomeTaskMaterialId());
        // 欢迎语素材不存在
        if (Objects.isNull(materailInfoDO)) {
            throw new RuntimeException("欢迎语素材不存在");
        }


        MaterialTemplateInfoQuery query = new MaterialTemplateInfoQuery();
        query.setId(materailInfoDO.getMaterialTemplateId());
        List<MaterialTemplateInfoDO> templateInfoDOList = materialTemplateService.list(query);
        if (CollectionUtils.isEmpty(templateInfoDOList)) {
            throw new RuntimeException("素材类型非欢迎语");
        }

        // 素材类型非欢迎语
        if (!Objects.equals(TaskType.PERSONAL_WELCOME, TaskType.getByCode(templateInfoDOList.get(0).getType()))) {
            throw new RuntimeException("素材类型非欢迎语");
        }
    }

    @Override
    protected void createTaskList(WechatAddCustomerByGroupActivitySopConfigVO config, Long activityId) {
        // 1.创建加客户群内好友好友任务
        createAddWechatCustomerByGroupTask(config, activityId);
        // 2.创建个人欢迎语任务
        createPersonWelcomeTask(config, activityId);
        // 3.创建客户打标任务
        createWechatCustomerAddTagTask(config, activityId);
    }

    @Override
    protected void updateTaskList(WechatAddCustomerByGroupActivitySopConfigVO config) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        // 1.更新加客户群内好友任务
        if (taskType2TaskInfoDO.containsKey(TaskType.ADD_WECHAT_CUSTOMER_BY_GROUP.getCode())) {
            TaskInfoDO addWechatCustomerByGroupTask = taskType2TaskInfoDO.get(TaskType.ADD_WECHAT_CUSTOMER_BY_GROUP.getCode());
            updateAddWechatCustomerByGroupTask(config, addWechatCustomerByGroupTask);
        } else {
            createAddWechatCustomerByGroupTask(config, config.getActivityId());
        }

        // 2.更新个人欢迎语任务
        if (taskType2TaskInfoDO.containsKey(TaskType.PERSONAL_WELCOME.getCode())) {
            TaskInfoDO welcomeTask = taskType2TaskInfoDO.get(TaskType.PERSONAL_WELCOME.getCode());
            updatePersonWelcomeTask(config, welcomeTask);
        } else {
            createPersonWelcomeTask(config, config.getActivityId());
        }

        // 3.更新客户打标任务
        if (taskType2TaskInfoDO.containsKey(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode())) {
            TaskInfoDO wechatCustomerAddTagTask = taskType2TaskInfoDO.get(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode());
            updateWechatCustomerAddTagTask(config, wechatCustomerAddTagTask);
        } else {
            createWechatCustomerAddTagTask(config, config.getActivityId());
        }
    }

    private void createAddWechatCustomerByGroupTask(WechatAddCustomerByGroupActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.ADD_WECHAT_CUSTOMER_BY_GROUP).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setSendUserId(config.getUserIdList().get(0));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("userIdList", config.getUserIdList());
        extraInfo.put("chatIdList", config.getChatIdList());
        extraInfo.put("addCustomerIfAt", config.getAddCustomerIfAt());
        extraInfo.put("addCustomerSendMessageList", config.getAddCustomerSendMessageList());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建加客户群内好友任务失败");
        }
    }

    private void createPersonWelcomeTask(WechatAddCustomerByGroupActivitySopConfigVO config, Long activityId) {
        // 没有配置欢迎语打标
        if (Objects.isNull(config.getWelcomeTaskMaterialId()) || Objects.equals(-1L, config.getWelcomeTaskMaterialId())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.PERSONAL_WELCOME).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setMaterialId(config.getWelcomeTaskMaterialId());
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerEventSourceId);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建欢迎语任务失败");
        }
    }

    private void createWechatCustomerAddTagTask(WechatAddCustomerByGroupActivitySopConfigVO config, Long activityId) {
        // 没有配置自动打标
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.WECHAT_CUSTOMER_ADD_TAG).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setEventSourceId(SwitchConfig.wechatCustomerAddTagEventSourceId);

        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("tagList", config.getCustomerAutoAddTagList());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建客户打标任务失败");
        }
    }

    private void updateAddWechatCustomerByGroupTask(WechatAddCustomerByGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());

        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("userIdList", config.getUserIdList());
        extraInfo.put("chatIdList", config.getChatIdList());
        extraInfo.put("addCustomerIfAt", config.getAddCustomerIfAt());
        extraInfo.put("addCustomerSendMessageList", config.getAddCustomerSendMessageList());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void updatePersonWelcomeTask(WechatAddCustomerByGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了欢迎语配置
        if (Objects.isNull(config.getWelcomeTaskMaterialId()) || Objects.equals(-1L, config.getWelcomeTaskMaterialId())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setMaterialId(config.getWelcomeTaskMaterialId());
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerEventSourceId);

        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void updateWechatCustomerAddTagTask(WechatAddCustomerByGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了自动打标配置
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());

        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extInfo.put("tagList", config.getCustomerAutoAddTagList());
        taskInfoDO.setExtInfo(extInfo.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    @Override
    public SopTypeEnum getSopType() {
        return SopTypeEnum.WECHAT_ADD_CUSTOMER_BY_GROUP;
    }

    @Override
    public void fillInfo(ActivitySopVO<WechatAddCustomerByGroupActivitySopConfigVO> activitySopVO) {
        WechatAddCustomerByGroupActivitySopConfigVO config = activitySopVO.getConfig();
        // 标签信息
        if (!CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(config.getCustomerAutoAddTagList());
            config.setCustomerAutoAddTagList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
            config.setCustomerAutoAddTagNameList(tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        }

        // 标签信息
        if (!CollectionUtils.isEmpty(config.getUserIdList())) {
            List<WechatUserDTO> wechatUserList = wechatUserService.listById(config.getUserIdList());
            config.setUserList(wechatUserList.stream().map(wechatUserConverter::convert2VO).collect(Collectors.toList()));
        }

        // 素材信息
        Long materialId = Optional.ofNullable(config.getWelcomeTaskMaterialId()).orElse(-1L);
        if (materialId > 0L) {
            MaterailInfoDO materailInfoDO = materialService.queryById(materialId);
            config.setWelcomeTaskMaterialName(materailInfoDO.getName());
        }

        // 任务数据统计
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        query.setType(TaskType.ADD_WECHAT_CUSTOMER_BY_GROUP.getCode());
        List<TaskInfoDO> taskList = taskService.query(query);
        TaskInfoDO addCustomerByGroupTask = taskList.stream().max(Comparator.comparing(TaskInfoDO::getGmtCreate)).orElse(null);
        if (Objects.isNull(addCustomerByGroupTask)) {
            return;
        }

        JSONObject extraInfo = TaskConfigProcessorFactory.getExtraInfo(addCustomerByGroupTask);
        activitySopVO.getExtraInfo().put("sendAddCustomerRequestCount", extraInfo.getInteger("sendAddCustomerRequestCount"));
        activitySopVO.getExtraInfo().put("sendAddCustomerRequestSuccessCount", extraInfo.getInteger("sendAddCustomerRequestSuccessCount"));
        activitySopVO.getExtraInfo().put("sendAddCustomerRequestFailCount", extraInfo.getInteger("sendAddCustomerRequestFailCount"));
        activitySopVO.getExtraInfo().put("addCustomerSuccessCount", extraInfo.getInteger("addCustomerSuccessCount"));
        activitySopVO.getExtraInfo().put("sendAddCustomerRequestSuccessPercent", extraInfo.getInteger("sendAddCustomerRequestSuccessPercent"));
        activitySopVO.getExtraInfo().put("addCustomerSuccessPercent", extraInfo.getInteger("addCustomerSuccessPercent"));
    }
}
