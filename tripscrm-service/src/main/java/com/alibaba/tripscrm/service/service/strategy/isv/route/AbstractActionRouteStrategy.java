package com.alibaba.tripscrm.service.service.strategy.isv.route;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.RobotDTO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.domain.risk.ActionCheckResult;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.dto.isv.IsvRouteStrategyDTO;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteWechatUserContext;
import com.alibaba.tripscrm.service.service.impl.isv.route.IsvRouteServiceImpl;
import com.alibaba.tripscrm.service.service.risk.RiskActionChecker;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatDepartmentService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/27 10:17
 */
@Component
public abstract class AbstractActionRouteStrategy {
    public static final Map<RiskActionEnum, AbstractActionRouteStrategy> ACTION_ROUTE_STRATEGY_MAP = new ConcurrentHashMap<>();
    @Resource
    private SpaceService spaceService;
    @Resource
    private WechatCustomerService wechatCustomerService;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private WechatDepartmentService wechatDepartmentService;
    @Resource
    private IsvRouteServiceImpl isvRouteService;
    @Resource
    private RiskActionChecker riskActionChecker;

    protected static final List<RobotTypeEnum> ROBOT_TYPE_LIST = Arrays.asList(
            RobotTypeEnum.EMPLOYEE,
            RobotTypeEnum.ASSISTANT
    );

    @PostConstruct
    public void baseInit() {
        for (RiskActionEnum riskActionEnum : Optional.ofNullable(getRiskActionEnumList()).orElse(new ArrayList<>())) {
            ACTION_ROUTE_STRATEGY_MAP.put(riskActionEnum, this);
        }
    }

    public static TripSCRMResult<WechatUserDTO> route(IsvRouteContext isvRouteContext) {
        if (Objects.isNull(isvRouteContext) || Objects.isNull(isvRouteContext.getRiskActionEnum())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!ACTION_ROUTE_STRATEGY_MAP.containsKey(isvRouteContext.getRiskActionEnum())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.UN_SUPPORTED_ROUTE_ACTION);
        }

        return ACTION_ROUTE_STRATEGY_MAP.get(isvRouteContext.getRiskActionEnum()).doRoute(isvRouteContext);
    }

    protected List<IsvTypeEnum> getValidIsvTypeList(IsvRouteStrategyDTO routeStrategy) {
        return SwitchConfig.ROBOT_ISV_LIST.stream().map(IsvTypeEnum::valueOf).filter(x -> !Optional.ofNullable(routeStrategy.getIsvBlackList()).orElse(new ArrayList<>()).contains(x)).collect(Collectors.toList());
    }

    protected List<RobotTypeEnum> getValidRobotTypeList(IsvRouteStrategyDTO routeStrategy) {
        return ROBOT_TYPE_LIST;
    }

    protected TripSCRMResult<List<WechatUserDTO>> getValidOnlineUserList(IsvRouteContext isvRouteContext) {
        String corpId = spaceService.getCorpIdBySpaceId(isvRouteContext.getSpaceId());
        TripSCRMResult<List<String>> allWechatUserIdListResult = getAllWechatUserIdList(isvRouteContext);
        if (Objects.isNull(allWechatUserIdListResult) || !allWechatUserIdListResult.isSuccess() || CollectionUtils.isEmpty(allWechatUserIdListResult.getData())) {
            PlatformLogUtil.logFail("获取执行行动项对应的企微号，未找到有效企微号", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        WechatUserQuery query = new WechatUserQuery();
        query.setUserIdList(allWechatUserIdListResult.getData());
        query.setCorpId(corpId);
        query.setSpaceId(isvRouteContext.getSpaceId());
        query.setQueryRobotInfo(true);
        List<WechatUserDTO> wechatUserList = wechatUserService.listValidWechatUserByCondition(query);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        return TripSCRMResult.success(wechatUserList);
    }

    protected TripSCRMResult<Map<Integer, Map<Integer, List<IsvRouteWechatUserContext>>>> getIsvType2RobotType2ValidOnlineUserList(IsvRouteContext isvRouteContext) {
        TripSCRMResult<List<WechatUserDTO>> validOnlineUserList = getValidOnlineUserList(isvRouteContext);
        if (Objects.isNull(validOnlineUserList) || !validOnlineUserList.isSuccess() || CollectionUtils.isEmpty(validOnlineUserList.getData())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        List<WechatUserDTO> wechatUserList = validOnlineUserList.getData();

        Map<Integer, Map<Integer, List<IsvRouteWechatUserContext>>> isvType2RobotType2ValidOnlineUserList = new HashMap<>();
        for (WechatUserDTO wechatUserDTO : wechatUserList) {
            if (CollectionUtils.isEmpty(wechatUserDTO.getRobotList())) {
                continue;
            }

            RobotDTO onlineRobotInfo = wechatUserDTO.getRobotList().stream().filter(robot -> Objects.equals(robot.getRobotStatus(), RobotStatusEnum.ONLINE.getCode())).findFirst().orElse(null);
            if (Objects.isNull(onlineRobotInfo)) {
                continue;
            }

            // 风控检查
            ActionCheckResult actionCheckResult = riskActionChecker.riskCheck(wechatUserDTO.getCorpId(), wechatUserDTO.getUserId(), isvRouteContext.getRiskActionEnum());
            if (Lists.newArrayList(RiskStatusEnum.HOLD, RiskStatusEnum.ABNORMAL).contains(actionCheckResult.getRiskStatus())) {
                // 处于异常或挂起状态下，无法使用
                // 注：保护状态不做强过滤，因为要重试。。后面会有限选取非保护中的账号
                continue;
            }

            IsvRouteWechatUserContext isvRouteWechatUserContext = new IsvRouteWechatUserContext();
            isvRouteWechatUserContext.setRiskStatusEnum(actionCheckResult.getRiskStatus());
            isvRouteWechatUserContext.setWechatUserDTO(wechatUserDTO);

            isvType2RobotType2ValidOnlineUserList.putIfAbsent(onlineRobotInfo.getIsvType(), new HashMap<>());
            isvType2RobotType2ValidOnlineUserList.get(onlineRobotInfo.getIsvType()).putIfAbsent(wechatUserDTO.getRobotType(), new ArrayList<>());
            isvType2RobotType2ValidOnlineUserList.get(onlineRobotInfo.getIsvType()).get(wechatUserDTO.getRobotType()).add(isvRouteWechatUserContext);
        }

        return TripSCRMResult.success(isvType2RobotType2ValidOnlineUserList);
    }

    protected IsvTypeEnum isvTypeRoute(List<IsvTypeEnum> validIsvTypeList, IsvRouteStrategyDTO routeStrategy, Map<Integer, Map<Integer, List<IsvRouteWechatUserContext>>> isvType2RobotType2OnlineUserList) {
        List<IsvTypeEnum> finalValidIsvTypeList = new ArrayList<>();
        // 计算所有有可用账号的服务商的总权重
        int totalCount = 0;
        for (IsvTypeEnum isvTypeEnum : validIsvTypeList) {
            if (CollectionUtils.isEmpty(isvType2RobotType2OnlineUserList.get(isvTypeEnum.getCode()))) {
                continue;
            }
            if (isvType2RobotType2OnlineUserList.get(isvTypeEnum.getCode()).values().stream().mapToLong(List::size).sum() == 0) {
                continue;
            }
            finalValidIsvTypeList.add(isvTypeEnum);
            totalCount += routeStrategy.getIsvRoutePercentMap().get(isvTypeEnum);
        }

        if (CollectionUtils.isEmpty(finalValidIsvTypeList)) {
            PlatformLogUtil.logFail("获取执行行动项对应的企微号，没有可用企微号", LogListUtil.newArrayList(validIsvTypeList, routeStrategy, isvType2RobotType2OnlineUserList));
            return null;
        }

        if (totalCount == 0) {
            // 可用账号所属的ISV分流比例都为0，那就从没加黑的里面兜底选
            PlatformLogUtil.logInfo("获取执行行动项对应的企微号，有效企微号所属服务商分流比例都为0，那就只能从这里面兜底选择了", LogListUtil.newArrayList());
            return finalValidIsvTypeList.get(ThreadLocalRandom.current().nextInt(finalValidIsvTypeList.size()));
        }

        int randomValue = ThreadLocalRandom.current().nextInt(totalCount);
        int currentSum = 0;

        for (IsvTypeEnum isvTypeEnum : finalValidIsvTypeList) {
            currentSum += routeStrategy.getIsvRoutePercentMap().getOrDefault(isvTypeEnum, 0);
            if (currentSum > randomValue) {
                return isvTypeEnum;
            }
        }

        return null;
    }

    protected RobotTypeEnum robotTypeRoute(List<RobotTypeEnum> validRobotTypeList, IsvRouteStrategyDTO routeStrategy, Map<Integer, List<IsvRouteWechatUserContext>> robotType2OnlineUserList) {
        int totalCount = routeStrategy.getRobotTypeRoutePercentMap().values().stream().mapToInt(x -> x).sum();
        int randomValue = ThreadLocalRandom.current().nextInt(totalCount);
        int currentSum = 0;

        RobotTypeEnum result = null;
        for (RobotTypeEnum robotTypeEnum : validRobotTypeList) {
            currentSum += routeStrategy.getRobotTypeRoutePercentMap().getOrDefault(robotTypeEnum, 0);
            if (currentSum > randomValue) {
                result = robotTypeEnum;
                break;
            }
        }
        if (Objects.isNull(result)) {
            return result;
        }

        // 如果当前选取的账号类型无可用账号，选取其他类型账号作为兜底
        if (CollectionUtils.isEmpty(robotType2OnlineUserList.get(result.getCode()))) {
            PlatformLogUtil.logInfo("对行动项执行账号类型路由，被选取的账号类型下无可用账号，选取其他类型账号作为兜底", LogListUtil.newArrayList(routeStrategy, robotType2OnlineUserList, validRobotTypeList));
            result = Objects.equals(result, RobotTypeEnum.ASSISTANT) ? RobotTypeEnum.EMPLOYEE : RobotTypeEnum.ASSISTANT;
        }

        if (CollectionUtils.isEmpty(robotType2OnlineUserList.get(result.getCode()))) {
            PlatformLogUtil.logFail("对行动项执行账号类型路由，无可用账号", LogListUtil.newArrayList(routeStrategy, robotType2OnlineUserList, validRobotTypeList));
            return null;
        }
        return result;
    }

    protected WechatUserDTO route(List<RobotTypeEnum> validRobotTypeList, Map<Integer, List<IsvRouteWechatUserContext>> robotType2OnlineValidUserList, IsvTypeEnum isvTypeEnum, IsvRouteStrategyDTO routeStrategy) {
        if (CollectionUtils.isEmpty(robotType2OnlineValidUserList)) {
            return null;
        }

        // 根据分流策略获取选取的账号类型
        RobotTypeEnum robotTypeEnum = robotTypeRoute(validRobotTypeList, routeStrategy, robotType2OnlineValidUserList);
        if (Objects.isNull(robotTypeEnum)) {
            PlatformLogUtil.logFail("根据传入的企微号UserId，对群成员账号进行分配，随机分配账号类型失败", LogListUtil.newArrayList(routeStrategy));
            return null;
        }

        // 如果当前选取的账号类型无可用账号，选取其他类型账号作为兜底
        if (CollectionUtils.isEmpty(robotType2OnlineValidUserList.get(robotTypeEnum.getCode()))) {
            PlatformLogUtil.logInfo("根据传入的企微号UserId，对群成员账号进行分配，随机分配的账号类型下无可用账号，选取其他类型账号作为兜底", LogListUtil.newArrayList(robotType2OnlineValidUserList, isvTypeEnum, robotTypeEnum));
            robotTypeEnum = Objects.equals(robotTypeEnum, RobotTypeEnum.ASSISTANT) ? RobotTypeEnum.EMPLOYEE : RobotTypeEnum.ASSISTANT;
        }

        // 其他类型账号列表也为空
        List<IsvRouteWechatUserContext> onlineWechatUserList = robotType2OnlineValidUserList.get(robotTypeEnum.getCode());
        if (CollectionUtils.isEmpty(onlineWechatUserList)) {
            return null;
        }

        List<IsvRouteWechatUserContext> notProtectedOnlineWechatUserList = onlineWechatUserList.stream().filter(isvRouteWechatUserContext -> !Objects.equals(isvRouteWechatUserContext.getRiskStatusEnum(), RiskStatusEnum.PROTECT)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notProtectedOnlineWechatUserList)) {
            PlatformLogUtil.logInfo("根据传入的企微号UserId，对群成员账号进行分配，所有账号均处于被保护状态", LogListUtil.newArrayList(robotType2OnlineValidUserList, isvTypeEnum, robotTypeEnum));
            return onlineWechatUserList.get(ThreadLocalRandom.current().nextInt(onlineWechatUserList.size())).getWechatUserDTO();
        }

        // 从可用的账号中随机选取一个
        return notProtectedOnlineWechatUserList.get(ThreadLocalRandom.current().nextInt(notProtectedOnlineWechatUserList.size())).getWechatUserDTO();
    }

    /**
     * 不同行动项对应的路由策略实现
     *
     * @param isvRouteContext 路由上下文
     * @return 选中的企微号
     */
    protected TripSCRMResult<WechatUserDTO> doRoute(IsvRouteContext isvRouteContext) {
        IsvRouteStrategyDTO routeStrategy = isvRouteService.getIsvRouteStrategyByActionWithCache(isvRouteContext.getRiskActionEnum(), EnvUtils.getEnvironment());
        if (Objects.isNull(routeStrategy)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.UN_SUPPORTED_ROUTE_ACTION);
        }

        List<IsvTypeEnum> validIsvTypeList = getValidIsvTypeList(routeStrategy);
        if (CollectionUtils.isEmpty(validIsvTypeList)) {
            PlatformLogUtil.logFail("获取执行行动项对应的企微号，所有服务商都被禁止使用", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        List<RobotTypeEnum> validRobotTypeList = getValidRobotTypeList(routeStrategy);
        if (CollectionUtils.isEmpty(validRobotTypeList)) {
            PlatformLogUtil.logFail("获取执行行动项对应的企微号，所有账号类型都被禁止使用", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        TripSCRMResult<Map<Integer, Map<Integer, List<IsvRouteWechatUserContext>>>> isvType2RobotType2ValidOnlineUserListResult = getIsvType2RobotType2ValidOnlineUserList(isvRouteContext);
        if (Objects.isNull(isvType2RobotType2ValidOnlineUserListResult) || !isvType2RobotType2ValidOnlineUserListResult.isSuccess() || CollectionUtils.isEmpty(isvType2RobotType2ValidOnlineUserListResult.getData())) {
            PlatformLogUtil.logFail("获取执行行动项对应的企微号，查询企微成员信息，结果为空", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        Map<Integer, Map<Integer, List<IsvRouteWechatUserContext>>> isvType2RobotType2ValidOnlineUserList = isvType2RobotType2ValidOnlineUserListResult.getData();
        IsvTypeEnum isvTypeEnum = isvTypeRoute(validIsvTypeList, routeStrategy, isvType2RobotType2ValidOnlineUserList);
        if (Objects.isNull(isvTypeEnum)) {
            PlatformLogUtil.logFail("获取执行行动项对应的企微号，服务商类型路由失败", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        WechatUserDTO wechatUserDTO = route(validRobotTypeList, isvType2RobotType2ValidOnlineUserList.get(isvTypeEnum.getCode()), isvTypeEnum, routeStrategy);
        if (Objects.isNull(wechatUserDTO)) {
            PlatformLogUtil.logFail("获取执行行动项对应的企微号，获取账号失败", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }
        PlatformLogUtil.logInfo("获取执行行动项对应的企微号，选取企微号成功", LogListUtil.newArrayList(isvRouteContext, wechatUserDTO));
        return TripSCRMResult.success(wechatUserDTO);
    }

    protected List<String> getWechatUserIdListWithCustomerRelation(List<String> userIdList, String externalUserId, Long spaceId) {
        FollowUserQuery followUserQuery = new FollowUserQuery();
        followUserQuery.setExternalUserId(externalUserId);
        followUserQuery.setUserIdList(userIdList);
        followUserQuery.setRobotStatus(RobotStatusEnum.ONLINE.getCode());

        List<Integer> bindDepartmentIdList = wechatDepartmentService.queryBindDepartmentIdListBySpaceIdWithCache(spaceId);
        if (CollectionUtils.isEmpty(bindDepartmentIdList)) {
            return new ArrayList<>();
        }

        followUserQuery.setDepartmentIdList(bindDepartmentIdList);
        followUserQuery.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode()));
        FollowUserInfo followUserInfo = wechatCustomerService.listFollowUser(followUserQuery);

        if (Objects.isNull(followUserInfo) || CollectionUtils.isEmpty(followUserInfo.getRelationList())) {
            PlatformLogUtil.logFail("查询客户添加的企微成员信息，无好友关系", LogListUtil.newArrayList(externalUserId));
            return null;
        }

        return followUserInfo.getRelationList().stream().map(FollowUserInfo.CustomerRelation::getUserId).collect(Collectors.toList());
    }

    /**
     * 获取可执行该操作的企微号列表
     */
    protected abstract TripSCRMResult<List<String>> getAllWechatUserIdList(IsvRouteContext isvRouteContext);

    /**
     * 需要处理的行动项列表
     *
     * @return 需要处理的行动项列表
     */
    protected abstract List<RiskActionEnum> getRiskActionEnumList();
}
