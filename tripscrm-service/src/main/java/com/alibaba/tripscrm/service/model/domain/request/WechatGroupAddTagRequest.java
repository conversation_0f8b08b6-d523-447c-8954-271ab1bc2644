package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.model.vo.tag.TagInfoVO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class WechatGroupAddTagRequest implements Serializable {
    /**
     * 群聊 chatId
     */
    @NotEmpty(message = "群聊Id不可为空")
    private List<String> chatIdList;

    /**
     * 标签 Id
     */
    private List<String> tagIdList;

    /**
     * 业务空间 Id
     */
    private Long spaceId;

    /**
     * 批量操作（批量标签只做新增，非批量做覆盖）
     */
    private boolean batchOperate = true;

    /**
     * 标签信息列表
     */
    private List<TagInfoVO> tagList;
}
