package com.alibaba.tripscrm.service.service.impl.risk;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.base.PageInfo;
import com.alibaba.tripscrm.dal.model.domain.data.RiskInvokeRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.RiskObjectDO;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.dal.model.domain.query.RiskInvokeRecordPageQuery;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserRobotPageQuery;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserRobotSpecialPageQuery;
import com.alibaba.tripscrm.dal.repository.RiskInvokeRecordRepository;
import com.alibaba.tripscrm.dal.repository.RiskObjectRepository;
import com.alibaba.tripscrm.dal.repository.WechatUserRepository;
import com.alibaba.tripscrm.service.enums.risk.*;
import com.alibaba.tripscrm.service.enums.rule.RuleTargetEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.request.*;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionConfig;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionLimitProcess;
import com.alibaba.tripscrm.service.model.domain.risk.UserRiskSchema;
import com.alibaba.tripscrm.service.model.vo.risk.*;
import com.alibaba.tripscrm.service.service.risk.RiskActionLimitTargetHandler;
import com.alibaba.tripscrm.service.service.risk.RiskConfigHandler;
import com.alibaba.tripscrm.service.service.risk.RiskPanelService;
import com.alibaba.tripscrm.service.service.risk.controller.RiskActionLimitController;
import com.alibaba.tripscrm.service.service.risk.status.RiskRobotStatusController;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.alibaba.tripscrm.service.service.strategy.account.BucManager;
import com.alibaba.tripscrm.service.util.system.BeanCopyUtils;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import com.taobao.eagleeye.EagleEye;
import com.taobao.tair.DataEntry;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 风控面板 Manager实现类
 *
 * <AUTHOR>
 * @date 2024/07/07
 */
@Service
public class RiskPanelServiceImpl implements RiskPanelService {
    public static final String GLOBAL_AVATAR1 = "https://img.alicdn.com/imgextra/i3/O1CN011MTnsN261lr1oIeOT_!!*************-0-tps-400-400.jpg";
    public static final String GLOBAL_AVATAR2 = "https://img.alicdn.com/imgextra/i3/O1CN01z3zVgc20XvyOghQXd_!!*************-0-tps-400-400.jpg";
    public static final String ROBOT_ACTION_INVOKE_SUCCESS_COUNT_PREFIX = "ROBOT_ACTION_INVOKE_SUCCESS_COUNT_";
    public static final String ROBOT_ACTION_INVOKE_FAIL_COUNT_PREFIX = "ROBOT_ACTION_INVOKE_FAIL_COUNT_";
    public static final String ROBOT_ACTION_INVOKE_RISK_COUNT_PREFIX = "ROBOT_ACTION_INVOKE_RISK_COUNT_";
    public static final String ROBOT_INVOKE_RISK_COUNT_PREFIX = "ROBOT_INVOKE_RISK_COUNT_";
    @Resource
    private RiskInvokeRecordRepository riskInvokeRecordRepository;
    @Resource
    private RiskObjectRepository riskObjectRepository;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private WechatUserRepository wechatUserRepository;
    @Resource
    private BucManager bucManager;
    @Resource
    private RuleService ruleService;
    @Resource
    private RiskRobotStatusController riskRobotStatusController;
    @Resource
    private RiskActionLimitController riskActionLimitController;

    @Override
    public AccountRiskDataVO getData() {
        AccountRiskDataVO riskData = new AccountRiskDataVO();
        List<WechatUserDO> robots = wechatUserRepository.listRobots();
        List<RiskObjectDO> riskObjects = riskObjectRepository.listByTargetType(RiskTargetEnum.ROBOT.getValue());
        Map<Integer, List<RiskObjectDO>> robotGroup = riskObjects.stream().collect(Collectors.groupingBy(RiskObjectDO::getRiskStatus));
        riskData.setRobotCount(robots.size());
        riskData.setAbnormalCount(robotGroup.getOrDefault(RiskStatusEnum.ABNORMAL.getCode(), new ArrayList<>()).size());
        riskData.setEscapeCount(robotGroup.getOrDefault(RiskStatusEnum.ESCAPE.getCode(), new ArrayList<>()).size());
        riskData.setHoldCount(robotGroup.getOrDefault(RiskStatusEnum.HOLD.getCode(), new ArrayList<>()).size());
        riskData.setProtectCount(robotGroup.getOrDefault(RiskStatusEnum.PROTECT.getCode(), new ArrayList<>()).size());
        riskData.setNormalCount(robotGroup.getOrDefault(RiskStatusEnum.NORMAL.getCode(), new ArrayList<>()).size() + (robots.size() - riskObjects.size()));
        return riskData;
    }

    @Override
    public void createRecord(RiskInvokeRecordCreateParam param) {
        // 创建调用记录
        RiskInvokeRecordDO create = BeanCopyUtils.copy(param, RiskInvokeRecordCreateParam.class, RiskInvokeRecordDO.class);
        create.setTraceId(EagleEye.getTraceId());
        riskInvokeRecordRepository.create(create);
        // 统计以行动项维度的成功调用量、失败调用量、限流调用量
        String accountActionKey;
        switch (RiskInvokeStatusEnum.parse(param.getInvokeStatus())) {
            case SUCCESS:
                accountActionKey = ROBOT_ACTION_INVOKE_SUCCESS_COUNT_PREFIX + param.getCorpId() + "|" + param.getUserId() + "|" + param.getAction();
                ldbTairManager.incr(accountActionKey, 1, 0, 0);
                break;
            case FAIL:
                accountActionKey = ROBOT_ACTION_INVOKE_FAIL_COUNT_PREFIX + param.getCorpId() + "|" + param.getUserId() + "|" + param.getAction();
                ldbTairManager.incr(accountActionKey, 1, 0, 0);
                break;
            case RISK:
                accountActionKey = ROBOT_ACTION_INVOKE_RISK_COUNT_PREFIX + param.getCorpId() + "|" + param.getUserId() + "|" + param.getAction();
                ldbTairManager.incr(accountActionKey, 1, 0, 0);
                // 统计以机器人维度的限流调用量
                String accountLimitKey = ROBOT_INVOKE_RISK_COUNT_PREFIX + param.getCorpId() + "|" + param.getUserId();
                ldbTairManager.incr(accountLimitKey, 1, 0, 0);
                break;
        }
    }

    @Override
    public AccountRiskConfigInfoVO pageRiskConfig(String searchKey, Integer robotType, Integer pageSize, Integer pageNum) {
        AccountRiskConfigInfoVO configInfo = new AccountRiskConfigInfoVO();
        WechatUserRobotSpecialPageQuery query = new WechatUserRobotSpecialPageQuery();
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        query.setSearchKey(searchKey);
        query.setRobotType(robotType);
        PageInfo<WechatUserDO> pageInfo = wechatUserRepository.pageRobots(query);
        PageInfo<AccountRiskConfigVO> result = new PageInfo<>();
        List<AccountRiskConfigVO> defaultConfigs = new ArrayList<>();
        List<AccountRiskConfigVO> list = new ArrayList<>();
        // 获取机器人类型默认风控配置，扫码号和平台号
        RuleDO employeeRule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_TYPE_RISK.getCode().byteValue(),
                RuleTargetEnum.ROBOT_TYPE.getCode().byteValue(), RobotTypeEnum.EMPLOYEE.getDesc());
        defaultConfigs.add(rule2RiskConfig(true, AccountRiskConfigVO.buildRiskId(RobotTypeEnum.EMPLOYEE),
                employeeRule.getName(), null, GLOBAL_AVATAR1, RobotTypeEnum.EMPLOYEE.getCode(), employeeRule, null));
        RuleDO assistantRule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_TYPE_RISK.getCode().byteValue(),
                RuleTargetEnum.ROBOT_TYPE.getCode().byteValue(), RobotTypeEnum.ASSISTANT.getDesc());
        defaultConfigs.add(rule2RiskConfig(true, AccountRiskConfigVO.buildRiskId(RobotTypeEnum.ASSISTANT),
                assistantRule.getName(), null, GLOBAL_AVATAR2, RobotTypeEnum.ASSISTANT.getCode(), assistantRule, null));
        // 批量获取风控状态
        List<String> targetIds = pageInfo.getList().stream().map(x -> RiskObjectDO.buildRobotTargetId(x.getCorpId(), x.getUserId())).collect(Collectors.toList());
        List<RiskObjectDO> robotRiskObjects = riskObjectRepository.listByTargetIds(targetIds, RiskTargetEnum.ROBOT.getValue());
        Map<String, RiskObjectDO> robotRiskObjectMap = robotRiskObjects.stream().collect(Collectors.toMap(RiskObjectDO::getTargetId, Function.identity()));
        // 批量获取机器人风控配置
        List<RuleDO> rules = ruleService.listByTargetIds(RuleTypeEnum.ROBOT_RISK.getCode().byteValue(),
                RuleTargetEnum.WECHAT_USER.getCode().byteValue(), targetIds);
        Map<String, RuleDO> ruleMap = rules.stream().collect(Collectors.toMap(RuleDO::getTargetId, Function.identity()));
        for (WechatUserDO wechatUser : pageInfo.getList()) {
            RuleDO rule = ruleMap.get(wechatUser.getCorpId() + "|" + wechatUser.getUserId());
            list.add(rule2RiskConfig(false, AccountRiskConfigVO.buildRiskId(wechatUser.getCorpId(), wechatUser.getUserId(), RobotTypeEnum.of(Integer.valueOf(wechatUser.getRobotType()))),
                    wechatUser.getName(), wechatUser.getAlias(), wechatUser.getAvatar(), Integer.valueOf(wechatUser.getRobotType()), rule, robotRiskObjectMap.get(wechatUser.getCorpId() + "|" + wechatUser.getUserId())));
        }
        result.setTotal(pageInfo.getTotal());
        result.setPageSize(pageInfo.getPageSize());
        result.setPageNum(pageInfo.getPageNum());
        result.setList(list);
        configInfo.setPageInfo(result);
        configInfo.setDefaultRiskConfigs(defaultConfigs);
        return configInfo;
    }

    private AccountRiskConfigVO rule2RiskConfig(Boolean global, String riskId, String name, String alias, String avatarUrl, Integer robotType, RuleDO rule, RiskObjectDO riskObject) {
        AccountRiskConfigVO riskConfig = new AccountRiskConfigVO();
        riskConfig.setRiskId(riskId);
        riskConfig.setGlobal(global);
        riskConfig.setName(name);
        riskConfig.setAlias(alias);
        riskConfig.setAvatarUrl(avatarUrl);
        riskConfig.setRobotType(robotType);
        riskConfig.setRiskStatus(riskObject != null ? riskObject.getRiskStatus() : RiskStatusEnum.NORMAL.getCode());
        UserRiskSchema schema = rule != null ? JSONObject.parseObject(rule.getConfig(), UserRiskSchema.class) : null;
        for (RiskConfigHandler riskConfigHandler : RiskConfigHandler.getConfigHandlers()) {
            riskConfigHandler.buildListShow(schema, riskConfig);
        }
        return riskConfig;
    }

    @Override
    public AccountRiskConfigDetailVO getRiskConfig(String riskId) {
        AccountRiskConfigDetailVO result = new AccountRiskConfigDetailVO();
        result.setRiskId(riskId);
        AccountRiskConfigVO.RiskIdInfo riskIdInfo = AccountRiskConfigVO.getRiskIdInfo(riskId);
        RuleDO rule = this.getRuleByRiskId(riskIdInfo, true);
        UserRiskSchema schema = rule != null ? JSONObject.parseObject(rule.getConfig(), UserRiskSchema.class) : null;
        for (RiskConfigHandler riskConfigHandler : RiskConfigHandler.getConfigHandlers()) {
            riskConfigHandler.buildDetailShow(schema, result);
        }
        return result;
    }

    @Override
    public boolean setRiskConfig(AccountRiskConfigUpdateRequest param) {
        AccountRiskConfigVO.RiskIdInfo riskIdInfo = AccountRiskConfigVO.getRiskIdInfo(param.getRiskId());
        RuleDO rule = this.getRuleByRiskId(riskIdInfo, false);
        if (rule == null) {
            rule = initRiskRule(riskIdInfo);
        }
        UserRiskSchema schema = JSONObject.parseObject(rule.getConfig(), UserRiskSchema.class);
        for (RiskConfigHandler riskConfigHandler : RiskConfigHandler.getConfigHandlers()) {
            riskConfigHandler.updateConfig(schema, param, riskIdInfo);
        }
        rule.setConfig(JSONObject.toJSONString(schema));
        return ruleService.update(rule, true) > 0 ? true : false;
    }

    private RuleDO initRiskRule(AccountRiskConfigVO.RiskIdInfo riskIdInfo) {
        RuleDO defaultRule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_TYPE_RISK.getCode().byteValue(),
                RuleTargetEnum.ROBOT_TYPE.getCode().byteValue(), riskIdInfo.getRobotType().getDesc());
        WechatUserDO wechatUser = wechatUserRepository.getByUserIdAndCorpId(riskIdInfo.getUserId(), riskIdInfo.getCorpId());
        RuleDO rule = new RuleDO();
        rule.setType(RuleTypeEnum.ROBOT_RISK.getCode().byteValue());
        rule.setPriority(1);
        rule.setName(wechatUser.getName() + "的风控配置");
        rule.setGroupId(0L);
        rule.setStatus((byte) 2);
        rule.setConfig(defaultRule.getConfig());
        rule.setDynamicConfig("{}");
        rule.setCreatorId(bucManager.getUserNameAndEmpId());
        rule.setMemberIds(bucManager.getUserNameAndEmpId());
        rule.setLastOperatorId(bucManager.getUserNameAndEmpId());
        rule.setTargetType(RuleTargetEnum.WECHAT_USER.getCode().byteValue());
        rule.setTargetId(riskIdInfo.getCorpId() + "|" + riskIdInfo.getUserId());
        ruleService.create(rule);
        return ruleService.selectById(rule.getId());
    }

    private RuleDO getRuleByRiskId(AccountRiskConfigVO.RiskIdInfo idInfo, Boolean includeDefault) {
        RuleDO rule = null;
        switch (idInfo.getRuleType()) {
            case ROBOT_RISK:
                rule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_RISK.getCode().byteValue(),
                        RuleTargetEnum.WECHAT_USER.getCode().byteValue(), idInfo.getCorpId() + "|" + idInfo.getUserId());
                if (rule == null) {
                    if (includeDefault) {
                        rule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_TYPE_RISK.getCode().byteValue(),
                                RuleTargetEnum.ROBOT_TYPE.getCode().byteValue(), idInfo.getRobotType().getDesc());
                    }
                }
                break;
            case ROBOT_TYPE_RISK:
                rule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_TYPE_RISK.getCode().byteValue(),
                        RuleTargetEnum.ROBOT_TYPE.getCode().byteValue(), idInfo.getRobotType().getDesc());
                break;
            default:
                break;
        }
        return rule;
    }

    @Override
    public void resetConfig(AccountRiskResetConfigRequest param) {
        AccountRiskConfigVO.RiskIdInfo riskIdInfo = AccountRiskConfigVO.getRiskIdInfo(param.getRiskId());
        if (RuleTypeEnum.ROBOT_RISK.equals(riskIdInfo.getRuleType())) {
            ruleService.deleteByTargetId(riskIdInfo.getCorpId() + "|" + riskIdInfo.getUserId(), RuleTargetEnum.WECHAT_USER.getCode());
        }
        // 删除风控状态，重置行动项至全局
        riskRobotStatusController.toNormal(riskIdInfo.getCorpId(), riskIdInfo.getUserId(), false, true);
    }

    @Override
    public void holdAccount(AccountRiskHoldRequest param) {
        AccountRiskConfigVO.RiskIdInfo riskIdInfo = AccountRiskConfigVO.getRiskIdInfo(param.getRiskId());
        riskRobotStatusController.toHold(riskIdInfo.getCorpId(), riskIdInfo.getUserId());
    }

    @Override
    public void escapeAccount(AccountRiskEscapeRequest param) {
        AccountRiskConfigVO.RiskIdInfo riskIdInfo = AccountRiskConfigVO.getRiskIdInfo(param.getRiskId());
        riskRobotStatusController.toEscape(riskIdInfo.getCorpId(), riskIdInfo.getUserId());
    }

    @Override
    public void normalAccount(AccountRiskNormalRequest param) {
        AccountRiskConfigVO.RiskIdInfo riskIdInfo = AccountRiskConfigVO.getRiskIdInfo(param.getRiskId());
        riskRobotStatusController.toNormal(riskIdInfo.getCorpId(), riskIdInfo.getUserId(), false, false);
    }

    @Override
    public PageInfo<AccountRiskSituationVO> pageAccountRiskSituation(String searchKey, Integer robotType, Integer pageSize, Integer pageNum) {
        WechatUserRobotPageQuery query = new WechatUserRobotPageQuery();
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        query.setSearchKey(searchKey);
        query.setRobotType(robotType);
        PageInfo<WechatUserDO> pageInfo = wechatUserRepository.pageRobots(query);
        List<String> targetIds = pageInfo.getList().stream().map(x -> RiskObjectDO.buildRobotTargetId(x.getCorpId(), x.getUserId())).collect(Collectors.toList());
        List<RiskObjectDO> robotRiskObjects = riskObjectRepository.listByTargetIds(targetIds, RiskTargetEnum.ROBOT.getValue());
        Map<String, RiskObjectDO> robotRiskObjectMap = robotRiskObjects.stream().collect(Collectors.toMap(RiskObjectDO::getTargetId, Function.identity()));
        List<RiskObjectDO> actionRiskObjects = riskObjectRepository.listByGroups(targetIds, RiskTargetEnum.ACTION.getValue());
        Map<String, List<RiskObjectDO>> actionRiskObjectGroup = actionRiskObjects.stream().collect(Collectors.groupingBy(RiskObjectDO::getRiskGroup));
        PageInfo<AccountRiskSituationVO> result = new PageInfo<>();
        List<AccountRiskSituationVO> list = new ArrayList<>();
        for (WechatUserDO wechatUser : pageInfo.getList()) {
            RiskObjectDO robotRiskObject = robotRiskObjectMap.get(wechatUser.getCorpId() + "|" + wechatUser.getUserId());
            List<RiskObjectDO> userActionRiskObjects = actionRiskObjectGroup.get(wechatUser.getCorpId() + "|" + wechatUser.getUserId());
            AccountRiskSituationVO riskSituation = new AccountRiskSituationVO();
            // 设置基础信息
            riskSituation.setCorpId(wechatUser.getCorpId());
            riskSituation.setUserId(wechatUser.getUserId());
            riskSituation.setName(wechatUser.getName());
            riskSituation.setAlias(wechatUser.getAlias());
            riskSituation.setAvatarUrl(wechatUser.getAvatar());
            riskSituation.setRobotType(wechatUser.getRobotType());
            riskSituation.setRobotStatus(wechatUser.getRobotStatus());
            // 设置机器人风控状态和行动点风控状态
            riskSituation.setRiskStatus(robotRiskObject != null ? robotRiskObject.getRiskStatus() : RiskStatusEnum.NORMAL.getCode());
            List<AccountRiskSituationActionVO> actionStatuses = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(userActionRiskObjects)) {
                for (RiskObjectDO actionRiskObject : userActionRiskObjects) {
                    RiskObjectDO.TargetIdInfo targetIdInfo = actionRiskObject.getTargetIdInfo();
                    RiskActionEnum riskAction = RiskActionEnum.parse(targetIdInfo.getActionCode());
                    if (riskAction != null) {
                        AccountRiskSituationActionVO actionStatus = new AccountRiskSituationActionVO();
                        actionStatus.setActionName(riskAction.getName());
                        actionStatus.setRiskStatus(actionRiskObject.getRiskStatus());
                        actionStatuses.add(actionStatus);
                    }
                }
            }
            actionStatuses.sort(Comparator.comparing(AccountRiskSituationActionVO::getActionName));
            riskSituation.setRiskActions(actionStatuses);
            // 在线时长
            if (robotRiskObject != null && robotRiskObject.getLoginTime() != null) {
                DecimalFormat df = new DecimalFormat("#0.0");
                riskSituation.setOnlineHour(df.format((double) (System.currentTimeMillis() - robotRiskObject.getLoginTime()) / 1000 / 60 / 60) + "小时");
            } else {
                riskSituation.setOnlineHour("未统计");
            }
            // 掉线次数 todo
            riskSituation.setOfflineCount("未统计");
            // 批量从tair获取
            List<String> keys = new ArrayList<>(10);
            String sendCustomerSuccessCountKey = ROBOT_ACTION_INVOKE_SUCCESS_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.CUSTOMER_SEND_MESSAGE.getActionCode();
            String sendCustomerFailCountKey = ROBOT_ACTION_INVOKE_FAIL_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.CUSTOMER_SEND_MESSAGE.getActionCode();
            String sendCustomerLimitCountKey = ROBOT_ACTION_INVOKE_RISK_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.CUSTOMER_SEND_MESSAGE.getActionCode();
            String sendGroupSuccessCountKey = ROBOT_ACTION_INVOKE_SUCCESS_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.GROUP_SEND_MESSAGE.getActionCode();
            String sendGroupFailCountKey = ROBOT_ACTION_INVOKE_FAIL_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.GROUP_SEND_MESSAGE.getActionCode();
            String sendGroupLimitCountKey = ROBOT_ACTION_INVOKE_RISK_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.GROUP_SEND_MESSAGE.getActionCode();
            String createGroupSuccessCountKey = ROBOT_ACTION_INVOKE_SUCCESS_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.GROUP_CREATE_GROUP.getActionCode();
            String createGroupFailCountKey = ROBOT_ACTION_INVOKE_FAIL_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.GROUP_CREATE_GROUP.getActionCode();
            String createGroupLimitCountKey = ROBOT_ACTION_INVOKE_RISK_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId() + "|" + RiskActionEnum.GROUP_CREATE_GROUP.getActionCode();
            String limitCountKey = ROBOT_INVOKE_RISK_COUNT_PREFIX + wechatUser.getCorpId() + "|" + wechatUser.getUserId();
            keys.add(sendCustomerSuccessCountKey);
            keys.add(sendCustomerFailCountKey);
            keys.add(sendCustomerLimitCountKey);
            keys.add(sendGroupSuccessCountKey);
            keys.add(sendGroupFailCountKey);
            keys.add(sendGroupLimitCountKey);
            keys.add(createGroupSuccessCountKey);
            keys.add(createGroupFailCountKey);
            keys.add(createGroupLimitCountKey);
            keys.add(limitCountKey);
            List<DataEntry> results = ldbTairManager.mGet(keys);
            Map<String, Integer> resultMap = results.stream().collect(Collectors.toMap(x -> (String) x.getKey(), x -> (Integer) x.getValue()));
            PlatformLogUtil.logInfo("调用数据通过LDB批量查询", LogListUtil.newArrayList(keys, results));
            // 设置私聊调用量
            AccountRiskSituationActionCountVO actionCount = new AccountRiskSituationActionCountVO();
            Integer sendCustomerMessageCountSuccess = resultMap.get(sendCustomerSuccessCountKey);
            actionCount.setSuccessCount(sendCustomerMessageCountSuccess != null ? sendCustomerMessageCountSuccess : 0);
            Integer sendCustomerMessageCountFail = resultMap.get(sendCustomerFailCountKey);
            actionCount.setFailCount(sendCustomerMessageCountFail != null ? sendCustomerMessageCountFail : 0);
            Integer sendCustomerMessageCountLimit = resultMap.get(sendCustomerLimitCountKey);
            actionCount.setLimitCount(sendCustomerMessageCountLimit != null ? sendCustomerMessageCountLimit : 0);
            riskSituation.setSendCustomerMessageCount(actionCount);
            // 设置群聊调用量
            AccountRiskSituationActionCountVO groupActionCount = new AccountRiskSituationActionCountVO();
            Integer sendGroupMessageCountSuccess = resultMap.get(sendGroupSuccessCountKey);
            groupActionCount.setSuccessCount(sendGroupMessageCountSuccess != null ? sendGroupMessageCountSuccess : 0);
            Integer sendGroupMessageCountFail = resultMap.get(sendGroupFailCountKey);
            groupActionCount.setFailCount(sendGroupMessageCountFail != null ? sendGroupMessageCountFail : 0);
            Integer sendGroupMessageCountLimit = resultMap.get(sendGroupLimitCountKey);
            groupActionCount.setLimitCount(sendGroupMessageCountLimit != null ? sendGroupMessageCountLimit : 0);
            riskSituation.setSendGroupMessageCount(groupActionCount);
            // 设置创建群聊调用量
            AccountRiskSituationActionCountVO createGroupActionCount = new AccountRiskSituationActionCountVO();
            Integer createGroupCountSuccess = resultMap.get(createGroupSuccessCountKey);
            createGroupActionCount.setSuccessCount(createGroupCountSuccess != null ? createGroupCountSuccess : 0);
            Integer createGroupCountFail = resultMap.get(createGroupFailCountKey);
            createGroupActionCount.setFailCount(createGroupCountFail != null ? createGroupCountFail : 0);
            Integer createGroupCountLimit = resultMap.get(createGroupLimitCountKey);
            createGroupActionCount.setLimitCount(createGroupCountLimit != null ? createGroupCountLimit : 0);
            riskSituation.setCreateGroupCount(createGroupActionCount);
            // 设置限流调用量
            Integer limitCount = resultMap.get(limitCountKey);
            riskSituation.setActionLimitCount(limitCount != null ? limitCount : 0);
            list.add(riskSituation);
        }
        result.setTotal(pageInfo.getTotal());
        result.setPageSize(pageInfo.getPageSize());
        result.setPageNum(pageInfo.getPageNum());
        result.setList(list);
        return result;
    }

    @Override
    public PageInfo<RiskInvokeRecordVO> pageRecord(String corpId, String userId, String actionCode, Integer invokeStatus, Integer pageSize, Integer pageNum) {
        PageInfo<RiskInvokeRecordVO> result = new PageInfo<>();
        RiskInvokeRecordPageQuery query = new RiskInvokeRecordPageQuery();
        query.setCorpId(corpId);
        query.setUserId(userId);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);
        query.setAction(actionCode);
        query.setInvokeStatus(invokeStatus);
        PageInfo<RiskInvokeRecordDO> records = riskInvokeRecordRepository.pageRecords(query);
        result.setTotal(records.getTotal());
        result.setPageSize(records.getPageSize());
        result.setPageNum(records.getPageNum());
        result.setList(records.getList().stream().filter(x -> RiskActionEnum.parse(x.getAction()) != null).map(x -> {
            RiskInvokeRecordVO recordVO = BeanCopyUtils.copy(x, RiskInvokeRecordDO.class, RiskInvokeRecordVO.class);
            recordVO.setActionName(RiskActionEnum.parse(x.getAction()).getName());
            recordVO.setSourceTypeName(SourceTypeEnum.parse(x.getSourceType()).getName());
            recordVO.setInvokeSubStatusName(x.getInvokeSubStatus() != null ? RiskInvokeSubStatusEnum.fromValue(x.getInvokeSubStatus()).getDesc() : null);
            return recordVO;
        }).collect(Collectors.toList()));
        return result;
    }

    @Override
    public List<RiskActionLimitProcess> listActionLimitProcess(String corpId, String userId) {
        List<RiskActionLimitProcess> riskActionLimitProcesses = new ArrayList<>();
        // 获取风控配置，先取类型维度再取机器人维度
        UserRiskSchema schema = riskActionLimitController.getRiskSchema(corpId, userId);
        List<RiskActionConfig> actionConfigs = new ArrayList<>();
        if (schema != null) {
            actionConfigs = schema.getActionConfigs();
        }
        Map<String, RiskActionConfig> riskActionMap = actionConfigs.stream().filter(RiskActionConfig::getEnable).collect(Collectors.toMap(RiskActionConfig::getActionCode, Function.identity()));
        for (RiskActionEnum riskAction : RiskActionEnum.values()) {
            RiskActionConfig riskActionConfig = riskActionMap.get(riskAction.getActionCode());
            if (riskActionConfig != null) {
                RiskActionLimitTargetHandler riskActionLimitTargetHandler = RiskActionLimitTargetHandler.get(riskAction.getTarget());
                riskActionLimitProcesses.add(riskActionLimitTargetHandler.getLimitProcess(corpId, userId, riskActionConfig));
            }
        }
        return riskActionLimitProcesses;
    }

    @Override
    public void resetInvokeCount(AccountRiskResetInvokeCountRequest param) {
        String corpId = param.getCorpId();
        String userId = param.getUserId();
        for (RiskActionEnum riskAction : RiskActionEnum.values()) {
            ldbTairManager.delete(ROBOT_ACTION_INVOKE_SUCCESS_COUNT_PREFIX + corpId + "|" + userId + "|" + riskAction.getActionCode());
            ldbTairManager.delete(ROBOT_ACTION_INVOKE_FAIL_COUNT_PREFIX + corpId + "|" + userId + "|" + riskAction.getActionCode());
            ldbTairManager.delete(ROBOT_ACTION_INVOKE_RISK_COUNT_PREFIX + corpId + "|" + userId + "|" + riskAction.getActionCode());
        }
        ldbTairManager.delete(ROBOT_INVOKE_RISK_COUNT_PREFIX + corpId + "|" + userId);
    }
}