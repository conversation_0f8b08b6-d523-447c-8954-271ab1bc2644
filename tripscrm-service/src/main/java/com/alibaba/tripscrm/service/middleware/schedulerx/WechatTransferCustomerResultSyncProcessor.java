package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatTransferCustomerResultDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.wechat.WechatTransferCustomerStatusEnum;
import com.alibaba.tripscrm.service.model.domain.query.WechatTransferCustomerResultQuery;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatTransferCustomerResultService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.request.WechatQueryTransferCustomerResultRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.WechatQueryTransferCustomerResultResponse;
import com.google.common.collect.Lists;
import com.taobao.mtop.commons.utils.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/9/28 10:52
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatTransferCustomerResultSyncProcessor extends JavaProcessor {
    private final TaskService taskService;
    private final SpaceService spaceService;
    private final CustomerService customerService;
    private final WechatTransferCustomerResultService wechatTransferCustomerResultService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        for (TaskType taskType : Lists.newArrayList(TaskType.TRANSFER_CUSTOMER, TaskType.RESIGNED_TRANSFER_CUSTOMER)) {
            handleTaskType(taskType);
        }
        return new ProcessResult(true);
    }

    /**
     * 查询好友继承任务，并进行分类，针对每个任务进行处理
     * handoverUserId -> takeoverUserId -> taskInfoList
     */
    private void handleTaskType(TaskType taskType) {
        TaskQuery query = new TaskQuery();
        query.setType(taskType.getCode());
        query.setDeleted(0);
        List<TaskInfoDO> taskInfoDOList = taskService.query(query);

        Map<String, Map<String, List<TaskInfoDO>>> handoverUserId2TakeoverUserId2TaskInfoMap = new HashMap<>();
        for (TaskInfoDO taskInfoDO : taskInfoDOList) {
            List<WechatTransferCustomerResultDO> wechatTransferCustomerResultList = getWechatTransferCustomerResultList(taskInfoDO);
            for (WechatTransferCustomerResultDO wechatTransferCustomerResultDO : wechatTransferCustomerResultList) {
                handoverUserId2TakeoverUserId2TaskInfoMap
                        .putIfAbsent(wechatTransferCustomerResultDO.getHandoverUserId(), new HashMap<>());
                handoverUserId2TakeoverUserId2TaskInfoMap
                        .get(wechatTransferCustomerResultDO.getHandoverUserId())
                        .putIfAbsent(wechatTransferCustomerResultDO.getTakeoverUserId(), new ArrayList<>());

                boolean duplicate = handoverUserId2TakeoverUserId2TaskInfoMap
                        .get(wechatTransferCustomerResultDO.getHandoverUserId())
                        .get(wechatTransferCustomerResultDO.getTakeoverUserId())
                        .stream().anyMatch(t -> t.getId().equals(taskInfoDO.getId()));
                if (duplicate) {
                    continue;
                }

                handoverUserId2TakeoverUserId2TaskInfoMap
                        .get(wechatTransferCustomerResultDO.getHandoverUserId())
                        .get(wechatTransferCustomerResultDO.getTakeoverUserId())
                        .add(taskInfoDO);
            }
        }

        for (String handoverUserId : handoverUserId2TakeoverUserId2TaskInfoMap.keySet()) {
            for (String takeoverUserId : handoverUserId2TakeoverUserId2TaskInfoMap.get(handoverUserId).keySet()) {
                for (TaskInfoDO taskInfoDO : handoverUserId2TakeoverUserId2TaskInfoMap.get(handoverUserId).get(takeoverUserId)) {
                    handleTask(handoverUserId, takeoverUserId, taskInfoDO);
                }
            }
        }
    }

    /**
     * 拉取每个任务需要更新的客户继承数据
     */
    private List<WechatTransferCustomerResultDO> getWechatTransferCustomerResultList(TaskInfoDO taskInfoDO) {
        List<WechatTransferCustomerResultDO> wechatTransferCustomerResultList = new ArrayList<>();
        for (long minId = 1L; minId > 0; ) {
            WechatTransferCustomerResultQuery query = new WechatTransferCustomerResultQuery();
            query.setTaskId(taskInfoDO.getId());
            query.setStartCreateTime(DateUtils.addDays(new Date(), -3));
            query.setMinId(minId);
            query.setSize(500);
            query.setStatusList(Lists.newArrayList(WechatTransferCustomerStatusEnum.INIT, WechatTransferCustomerStatusEnum.WAITING));
            List<WechatTransferCustomerResultDO> list = wechatTransferCustomerResultService.list(query);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            minId = list.stream().map(WechatTransferCustomerResultDO::getId).mapToLong(i -> i + 1).max().orElse(0L);
            wechatTransferCustomerResultList.addAll(list);
        }

        return wechatTransferCustomerResultList;
    }

    private void handleTask(String handoverUserId, String takeoverUserId, TaskInfoDO taskInfoDO) {
        for (String cursor = null; ; ) {
            WechatQueryTransferCustomerResultRequest request = new WechatQueryTransferCustomerResultRequest();
            Boolean isResigned = Objects.equals(TaskType.getByCode(taskInfoDO.getType()), TaskType.RESIGNED_TRANSFER_CUSTOMER);
            request.setIsResigned(isResigned);
            request.setCorpId(spaceService.getCorpIdBySpaceId(taskInfoDO.getSpaceId()));
            request.setHandoverUserId(handoverUserId);
            request.setTakeoverUserId(takeoverUserId);
            request.setCursor(cursor);
            ResultDO<WechatQueryTransferCustomerResultResponse> resultDO = customerService.queryTransferResult(request);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
                PlatformLogUtil.logFail("查询客户继承结果出错", LogListUtil.newArrayList(request, resultDO));
                break;
            }

            handleTransferResult(handoverUserId, takeoverUserId, taskInfoDO, resultDO);
            cursor = resultDO.getModel().getNextCursor();
            if (!StringUtil.hasText(cursor)) {
                break;
            }
        }
    }

    /**
     * 更新继承结果数据
     */
    private void handleTransferResult(String handoverUserId, String takeoverUserId, TaskInfoDO taskInfoDO, ResultDO<WechatQueryTransferCustomerResultResponse> resultDO) {
        List<WechatQueryTransferCustomerResultResponse.Customer> customers = Optional.ofNullable(resultDO.getModel().getCustomers()).orElse(Lists.newArrayList());
        for (WechatQueryTransferCustomerResultResponse.Customer customer : customers) {
            WechatTransferCustomerResultQuery condition = new WechatTransferCustomerResultQuery();
            condition.setTaskId(taskInfoDO.getId());
            condition.setExternalUserId(customer.getExternalUserId());
            condition.setHandoverUserId(handoverUserId);
            condition.setTakeoverUserId(takeoverUserId);

            WechatTransferCustomerResultDO wechatTransferCustomerResultDO = new WechatTransferCustomerResultDO();
            wechatTransferCustomerResultDO.setStatus(WechatTransferCustomerStatusEnum.of(customer.getStatus()).getCode().byteValue());
            if (NumberUtils.validLong(customer.getTakeoverTime())) {
                wechatTransferCustomerResultDO.setTakeoverTime(new Date(customer.getTakeoverTime() * 1000));
            }
            wechatTransferCustomerResultService.updateSelective(wechatTransferCustomerResultDO, condition);
        }
    }
}