package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/27 19:12
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TransferCustomerDataQueryProcessor extends AbstractTaskDataProcessor {
    private final WechatCustomerService wechatCustomerService;
    private final TaskService taskService;

    /**
     * 从数据库中查询企微成员的客户信息（需要实时），只取为好友的数据
     *
     * @param context 任务执行上下文
     * @return TaskDataVO
     */
    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        return getTransferData(context.getTaskInfoDOSnapshot());
    }

    @AteyeInvoker(description = "查询继承好友数据", paraDesc = "任务Id")
    public void getTransferData(Long taskId) {
        TaskDataVO dataVO = getTransferData(taskService.queryTaskById(taskId));
    }

    private TaskDataVO getTransferData(TaskInfoDO taskInfoDO) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        String extInfo = taskInfoDO.getExtInfo();
        if (!StringUtils.hasText(extInfo)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        JSONObject extInfoJson = JSONObject.parseObject(extInfo);
        // 原企微成员
        List<String> handoverUserIdList = extInfoJson.getObject(TaskConstant.HANDOVER_USER_ID_LIST, new TypeReference<List<String>>() {
        });
        // 接替的企微成员
        List<String> takeoverUserIdList = extInfoJson.getObject(TaskConstant.TAKEOVER_USER_ID_LIST, new TypeReference<List<String>>() {
        });
        // 原企微成员的客户列表
        Map<String, List<CustomerRelationDO>> handoverUserId2ExternalUserIdMap = new HashMap<>();
        // 接替成员的客户额度
        Map<String, Long> takeoverUserId2QuotaMap = new HashMap<>();

        for (String handoverUserId : handoverUserIdList) {
            List<CustomerRelationDO> customerRelationList = wechatCustomerService.listByUserId(handoverUserId, SpaceInfoThreadLocalUtils.getCorpId());
            List<CustomerRelationDO> filterList = filterTargetAudience(extInfoJson, customerRelationList);
            PlatformLogUtil.logInfo("在职继承任务过滤客户", LogListUtil.newArrayList(handoverUserId, customerRelationList.size(), filterList.size()));
            handoverUserId2ExternalUserIdMap.put(handoverUserId, filterList);
        }

        for (String takeoverUserId : takeoverUserIdList) {
            Long customerCount = wechatCustomerService.getCustomerCount(takeoverUserId, true);
            takeoverUserId2QuotaMap.put(takeoverUserId, Math.max(SwitchConfig.WECHAT_USER_FRIEND_COUNT_LIMIT - customerCount, 0));
        }

        // 按规则进行客户分配
        Map<String, Map<String, List<String>>> handoverUserId2takeoverUserId2CustomerMap = new HashMap<>();
        for (String handoverUserId : handoverUserIdList) {
            List<CustomerRelationDO> customerRelationList = handoverUserId2ExternalUserIdMap.get(handoverUserId);
            for (CustomerRelationDO customerRelationDO : customerRelationList) {
                // 找到有剩余额度的第一个takeoverUserId
                String takeoverUserId = takeoverUserIdList.stream()
                        .filter(userId -> takeoverUserId2QuotaMap.getOrDefault(userId, 0L) > 0)
                        .findFirst().orElse(null);
                if (Objects.isNull(takeoverUserId)) {
                    PlatformLogUtil.logFail("好友继承时，分配客户额度不足，剩余客户被丢弃", LogListUtil.newArrayList(taskInfoDO.getId()));
                    break;
                }

                takeoverUserId2QuotaMap.put(takeoverUserId, takeoverUserId2QuotaMap.get(takeoverUserId) - 1);
                handoverUserId2takeoverUserId2CustomerMap.putIfAbsent(handoverUserId, new HashMap<>());
                handoverUserId2takeoverUserId2CustomerMap.get(handoverUserId).putIfAbsent(takeoverUserId, new ArrayList<>());
                handoverUserId2takeoverUserId2CustomerMap.get(handoverUserId).get(takeoverUserId).add(customerRelationDO.getExternalUserId());
            }
        }

        for (String handoverUserId : handoverUserId2takeoverUserId2CustomerMap.keySet()) {
            Map<String, List<String>> takeoverUserId2CustomerMap = handoverUserId2takeoverUserId2CustomerMap.get(handoverUserId);
            for (String takeoverUserId : takeoverUserId2CustomerMap.keySet()) {
                List<List<String>> partitions = Lists.partition(takeoverUserId2CustomerMap.get(takeoverUserId), 100);
                List<TaskDataVO.DataBodyVO> taskDataBodyList = buildTaskDataBodyList(handoverUserId, takeoverUserId, partitions);
                dataVO.getData().addAll(taskDataBodyList);
            }
        }

        dataVO.setTotalCount((long) dataVO.getData().size());
        return dataVO;
    }

    private List<CustomerRelationDO> filterTargetAudience(JSONObject extInfoJson, List<CustomerRelationDO> customerRelationList) {

        Boolean allMatch = Optional.ofNullable(extInfoJson.getBoolean(TaskConstant.ALL_MATCH)).orElse(false);
        List<String> wechatTagNameList = Optional.ofNullable(
                        extInfoJson.getString(TaskConstant.WECHAT_TAG_NAME))
                .map(s -> Arrays.stream(s.split(",")).filter(StringUtils::hasText).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        List<String> scrmIncludeTagIDList = extInfoJson.containsKey(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST) ? extInfoJson.getObject(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
        }) : Collections.emptyList();

        if (CollectionUtils.isEmpty(scrmIncludeTagIDList) && CollectionUtils.isEmpty(wechatTagNameList)) {
            return customerRelationList;
        }
        List<CustomerRelationDO> resultList;
        List<String> externalUserIdList = customerRelationList.stream().map(CustomerRelationDO::getExternalUserId).collect(Collectors.toList());
        List<WechatCustomerVO> wechatCustomerVOList = wechatCustomerService.listByExternalUserIdList(externalUserIdList);
        Map<String, WechatCustomerVO> wechatCustomerVOMap = wechatCustomerVOList.stream().collect(Collectors.toMap(WechatCustomerVO::getExternalUserId, Function.identity()));

        resultList = customerRelationList.stream()
                .filter(cr -> {
                    WechatCustomerVO wechatCustomerVO = wechatCustomerVOMap.get(cr.getExternalUserId());
                    List<String> wechatTags = Optional.ofNullable(cr.getTagName())
                            .map(tagName -> Arrays.stream(tagName.split(",")).filter(StringUtils::hasText).collect(Collectors.toList()))
                            .orElseGet(Collections::emptyList);
                    boolean haveScrmTag = wechatCustomerVO != null && CollectionUtils.isNotEmpty(scrmIncludeTagIDList)
                            && wechatCustomerVO.getTagIdList().stream().anyMatch(scrmIncludeTagIDList::contains);

                    boolean haveWechatTag = CollectionUtils.isNotEmpty(wechatTags) && CollectionUtils.isNotEmpty(wechatTagNameList)
                            && wechatTags.stream().anyMatch(wechatTagNameList::contains);

                    return allMatch ? haveScrmTag && haveWechatTag : haveScrmTag || haveWechatTag;
                })
                .collect(Collectors.toList());

        return resultList;
    }


    private List<TaskDataVO.DataBodyVO> buildTaskDataBodyList(String handoverUserId, String takeoverUserId, List<List<String>> externalUserIdListPatition) {
        List<TaskDataVO.DataBodyVO> taskDataBodyList = new ArrayList<>();
        for (List<String> externalUserIdList : externalUserIdListPatition) {
            TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
            dataBodyVO.setTargetId(takeoverUserId);
            dataBodyVO.setTargetType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
            JSONObject dataExtInfo = new JSONObject();
            dataExtInfo.put("sendUserId", handoverUserId);
            dataExtInfo.put("handoverUserId", handoverUserId);
            dataExtInfo.put("takeoverUserId", takeoverUserId);
            dataExtInfo.put("externalUserIdList", externalUserIdList);
            dataExtInfo.put("uuid", UUID.randomUUID().toString());
            dataBodyVO.setExtInfo(dataExtInfo.toJSONString());
            taskDataBodyList.add(dataBodyVO);
        }
        return taskDataBodyList;
    }

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.TRANSFER_CUSTOMER, TaskType.RESIGNED_TRANSFER_CUSTOMER);
    }
}
