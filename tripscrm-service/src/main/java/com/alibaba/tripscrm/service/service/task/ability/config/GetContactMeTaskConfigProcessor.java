package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatContactMeDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.convert.WechatUserConverter;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.service.second.ShortLinkService;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatContactMeService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.INTERFACE;

/**
 * 获取个人活码任务配置处理器
 *
 * <AUTHOR>
 * @date 2023-12-30 15:26:28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GetContactMeTaskConfigProcessor extends AbstractTaskConfigProcessor {
    private final TaskService taskService;
    private final WechatContactMeService wechatContactMeService;
    private final ActivityContextService activityContextService;
    private final LdbTairManager ldbTairManager;
    private final WechatUserService wechatUserService;
    private final WechatUserConverter wechatUserConverter;
    private final ShortLinkService shortLinkService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        JSONObject extraInfoJson = super.getExtraInfo(taskInfoDO);
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        if (!configJson.containsKey("wechatContactMeId") || !extraInfoJson.containsKey("userIdList")) {
            return extraInfoJson;
        }

        List<String> userIdList = extraInfoJson.getObject("userIdList", new TypeReference<List<String>>() {
        });
        String state = configJson.getString("contextId");
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(userIdList);
        Long wechatContactMeId = configJson.getLong("wechatContactMeId");
        WechatContactMeDO wechatContactMeDO = wechatContactMeService.getById(wechatContactMeId);
        Object addCount = ldbTairManager.get(TairConstant.WECHAT_CONTACT_ME_ADD_CUSTOMER_COUNT_PREFIX + wechatContactMeDO.getState());
        extraInfoJson.put("addCount", Optional.ofNullable(addCount).orElse(0));
        extraInfoJson.put("qrCodeUrl", wechatContactMeDO.getQrCode());
        extraInfoJson.put("state", wechatContactMeDO.getState());
        extraInfoJson.put("wechatUserList", wechatUserList.stream().map(wechatUserConverter::convert2VO).collect(Collectors.toList()));
        // 推广链接
        String contactMeLink = getContactMeLink(taskInfoDO.getSpaceId(), state);
        if (StringUtils.isNotBlank(contactMeLink)) {
            extraInfoJson.put("link", contactMeLink);
        }
        return extraInfoJson;
    }

    /**
     * 获取联系我推广链接
     *
     * @param spaceId 业务空间Id
     * @param state   活码唯一标识
     * @return 推广链接
     */
    private String getContactMeLink(Long spaceId, String state) {
        String link = wechatContactMeService.buildMakerPage(spaceId, state);
        // 转短链
        String finalUrl = MaterialUtils.convert2MiniProgramMiddlePath(link);
        String shortLink = shortLinkService.convertToShortUrl(finalUrl);
        return shortLink;
    }

    @Override
    public TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName) {
        TaskInfoDO taskInfoDO = super.getInitConfig(activityId, spaceId, taskName);
        taskInfoDO.setTriggerType(Integer.parseInt(INTERFACE.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        Date effectEndTime = new Date(LocalDate.of(2040, 12, 30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        taskInfoDO.setEffectEndTime(effectEndTime);
        return taskInfoDO;
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
        JSONObject extInfoJson = JSONObject.parseObject(taskInfoDO.getExtInfo());
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());

        // 默认参数
        extInfoJson.putIfAbsent("skipVerify", true);
        extInfoJson.putIfAbsent("userIdList", new ArrayList<>());

        // 处理个码数据
        WechatContactMeDO wechatContactMeDO = processWechatContactMe(extInfoJson, configJson, taskInfoDO.getSpaceId());
        // 处理活动上下文数据
        processActivityContext(taskInfoDO, wechatContactMeDO);

        // 更新任务配置信息
        configJson.put("wechatContactMeId", wechatContactMeDO.getId());
        configJson.put("contextId", Long.valueOf(wechatContactMeDO.getState()));
        configJson.put("qrCodeUrl", wechatContactMeDO.getQrCode());
        taskInfoDO.setExtInfo(extInfoJson.toJSONString());
        taskInfoDO.setConfig(configJson.toJSONString());
        taskService.updateTaskInfoWithoutPost(taskInfoDO);
    }

    private WechatContactMeDO processWechatContactMe(JSONObject extInfoJson, JSONObject configJson, Long spaceId) {
        Boolean skipVerify = extInfoJson.getBoolean("skipVerify");
        List<String> userIdList = extInfoJson.getObject("userIdList", new TypeReference<List<String>>() {
        });

        // 创建个人活码
        if (!configJson.containsKey("wechatContactMeId")) {
            return wechatContactMeService.create(userIdList, skipVerify, activityContextService.generateContextId().toString(), spaceId);
        }

        // 更新个人活码
        Long wechatContactMeId = configJson.getLong("wechatContactMeId");
        int effectLines = wechatContactMeService.update(wechatContactMeId, userIdList, skipVerify);
        if (effectLines == 0) {
            throw new RuntimeException("数据更新失败");
        }

        return wechatContactMeService.getById(wechatContactMeId);
    }

    private void processActivityContext(TaskInfoDO taskInfoDO, WechatContactMeDO wechatContactMeDO) {
        // 活动上下文信息
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(NumberUtils.toLong(wechatContactMeDO.getState()));
        activityTaskInfoBO.setActivityId(taskInfoDO.getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CONTACT_ME_ID);
        activityTaskInfoBO.setTargetId(String.valueOf(wechatContactMeDO.getId()));
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("wechatContactMeId", wechatContactMeDO.getId());
        extraJson.put("qrCodeUrl", wechatContactMeDO.getQrCode());
        extraJson.put("dataTime", System.currentTimeMillis());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);
        PlatformLogUtil.logFail("upsert", LogListUtil.newArrayList(activityTaskInfoBO));
    }

    private void deleteActivityContext(WechatContactMeDO wechatContactMeDO) {
        Long contextId = NumberUtils.toLong(wechatContactMeDO.getState());
        // 查询上下文信息
        ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        if (Objects.isNull(activityTaskInfoBO)) {
            return;
        }

        // 删除个码上下文
        activityContextService.deleteByTaskId(contextId);
        PlatformLogUtil.logFail("deleteByTaskId", LogListUtil.newArrayList(activityTaskInfoBO));
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
        postCreate(taskInfoDO);
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {
        String config = taskInfoDO.getConfig();
        JSONObject configJson = JSONObject.parseObject(config);
        Long wechatContactMeId = configJson.getLong("wechatContactMeId");

        WechatContactMeDO wechatContactMeDO = wechatContactMeService.getById(wechatContactMeId);
        // 删除个码信息
        try {
            wechatContactMeService.delete(wechatContactMeId);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(taskInfoDO));
        }
        // 删除上下文信息
        deleteActivityContext(wechatContactMeDO);
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.GET_WECHAT_CONTACT_ME;
    }
}
