package com.alibaba.tripscrm.service.service.strategy.isv.route.customer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.service.strategy.isv.route.AbstractActionRouteStrategy;
import com.google.common.collect.Lists;
import com.taobao.csp.courier.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/27 10:38
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerSendMessageRouteStrategy extends AbstractActionRouteStrategy {
    @Override
    protected TripSCRMResult<List<String>> getAllWechatUserIdList(IsvRouteContext isvRouteContext) {
        if (!StringUtils.hasText(isvRouteContext.getExternalUserId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<String> wechatUserIdList = getWechatUserIdListWithCustomerRelation(isvRouteContext.getUserIdList(), isvRouteContext.getExternalUserId(), isvRouteContext.getSpaceId());
        if (CollectionUtils.isEmpty(wechatUserIdList)) {
            PlatformLogUtil.logInfo("获取发送私聊执行行动项对应的企微号，没有匹配到具有好友关系的企微账号", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }
        return TripSCRMResult.success(wechatUserIdList);
    }

    @Override
    protected List<RiskActionEnum> getRiskActionEnumList() {
        return Lists.newArrayList(RiskActionEnum.CUSTOMER_SEND_MESSAGE);
    }
}
