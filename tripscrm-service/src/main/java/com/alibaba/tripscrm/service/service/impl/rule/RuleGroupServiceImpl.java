package com.alibaba.tripscrm.service.service.impl.rule;

import com.alibaba.tripscrm.dal.mapper.tddl.RuleGroupMapper;
import com.alibaba.tripscrm.dal.model.domain.data.RuleGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.RuleGroupParam;
import com.alibaba.tripscrm.service.model.domain.query.RuleGroupQuery;
import com.alibaba.tripscrm.service.service.rule.RuleGroupService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:51
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RuleGroupServiceImpl implements RuleGroupService {
    private final RuleGroupMapper ruleGroupMapper;

    @Override
    public RuleGroupDO selectById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return null;
        }

        return ruleGroupMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<RuleGroupDO> selectByCondition(RuleGroupQuery query) {
        if (Objects.isNull(query)) {
            return new ArrayList<>();
        }

        RuleGroupParam param = new RuleGroupParam();
        RuleGroupParam.Criteria criteria = param.or();
        if (NumberUtils.validLong(query.getSpaceId())) {
            criteria.andSpaceIdEqualTo(query.getSpaceId());
        }

        if (Objects.nonNull(query.getType())) {
            criteria.andTypeEqualTo(query.getType());
        }

        if (Objects.nonNull(query.getEnv())) {
            criteria.andEnvEqualTo(query.getEnv());
        }

        if (StringUtils.hasLength(query.getName())) {
            criteria.andNameEqualTo(query.getName());
        }

        criteria.andDeletedEqualTo((byte) 0);
        return ruleGroupMapper.selectByParam(param);
    }

    @Override
    public PageInfo<RuleGroupDO> pageQuery(RuleGroupQuery query) {
        try {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());

            RuleGroupParam param = new RuleGroupParam();
            RuleGroupParam.Criteria criteria = param.or();
            if (NumberUtils.validLong(query.getSpaceId())) {
                criteria.andSpaceIdEqualTo(query.getSpaceId());
            }

            if (Objects.nonNull(query.getType())) {
                criteria.andTypeEqualTo(query.getType());
            }

            if (StringUtils.hasLength(query.getName())) {
                criteria.andNameLike("%" + query.getName() + "%");
            }

            if (Objects.nonNull(query.getCreatorId())) {
                criteria.andCreatorIdEqualTo(query.getCreatorId());
            }

            if (Objects.nonNull(query.getMemberIds())) {
                criteria.andMemberIdsLike("%" + query.getMemberIds() + "%");
            }

            if (Objects.nonNull(query.getCreateStartTime())) {
                criteria.andGmtCreateGreaterThanOrEqualTo(query.getCreateStartTime());
            }

            if (Objects.nonNull(query.getCreateEndTime())) {
                criteria.andGmtCreateLessThanOrEqualTo(query.getCreateEndTime());
            }

            if (Objects.nonNull(query.getEnv())) {
                criteria.andEnvEqualTo(query.getEnv());
            }

            criteria.andDeletedEqualTo((byte) 0);

            List<RuleGroupDO> list = ruleGroupMapper.selectByParam(param);
            // 结果处理
            if (Objects.isNull(list)) {
                return null;
            }

            return new PageInfo<>(list);
        } catch (Exception e) {
            PlatformLogUtil.logException("pageQuery执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public int create(RuleGroupDO ruleGroupDO) {
        if (Objects.isNull(ruleGroupDO)) {
            return 0;
        }

        return ruleGroupMapper.insertSelective(ruleGroupDO);
    }

    @Override
    public int update(RuleGroupDO ruleGroupDO) {
        if (Objects.isNull(ruleGroupDO)) {
            return 0;
        }

        if (!NumberUtils.validLong(ruleGroupDO.getId())) {
            return 0;
        }

        return ruleGroupMapper.updateByPrimaryKeySelective(ruleGroupDO);
    }

    @Override
    public int delete(Long id) {
        if (!NumberUtils.validLong(id)) {
            return 0;
        }

        return ruleGroupMapper.deleteByPrimaryKey(id);
    }
}
