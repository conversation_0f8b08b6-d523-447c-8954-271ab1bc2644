package com.alibaba.tripscrm.service.service.task.ability.sub.callback;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.vo.task.TaskAsyncRequestDataVO;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.log.monitor.TaskMonitorLoggerUtils;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.fliggy.pokemon.lock.client.api.TairLockManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/5/3 15:39
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class DefaultTaskAsyncRequestCallbackProcessor implements TaskAsyncRequestCallbackProcessor {
    private final LdbTairManager ldbTairManager;
    private final TairLockManager tairLockManager;

    @Override
    public void process(ScrmCallbackMsg scrmCallbackMsg) {
        Object o = ldbTairManager.get(TairConstant.LDB_PROXY_REQUEST_DATA_PREFIX + scrmCallbackMsg.getRequestId());
        if (Objects.isNull(o)) {
            return;
        }

        TaskAsyncRequestDataVO taskAsyncRequestDataVO = JSONObject.parseObject(o.toString(), new TypeReference<TaskAsyncRequestDataVO>() {
        });

        long startTime = System.currentTimeMillis();
        String className = "DefaultTaskAsyncRequestCallbackProcessor";
        String methodName = "process";
        String mark = "单条任务数据处理_异步请求结果";
        Long activityId = taskAsyncRequestDataVO.getActivityId();
        Long taskId = taskAsyncRequestDataVO.getTaskId();
        Boolean isSubTask = taskAsyncRequestDataVO.getIsSubTask();
        Long mainTaskInstanceId = taskAsyncRequestDataVO.getMainTaskInstanceId();
        Long subTaskInstanceId = taskAsyncRequestDataVO.getSubTaskInstanceId();
        ActivityTargetTypeEnum targetType = ActivityTargetTypeEnum.codeOf(taskAsyncRequestDataVO.getTargetType());
        String targetId = taskAsyncRequestDataVO.getTargetId();
        TripSCRMErrorCode errorCode = TripSCRMErrorCode.OK;
        if (Objects.isNull(scrmCallbackMsg.getResult()) || !scrmCallbackMsg.getResult()) {
            errorCode = TripSCRMErrorCode.PROCESS_FAILED;
        }

        TaskMonitorLoggerUtils.record(mark, className, methodName, activityId, taskId, mainTaskInstanceId, subTaskInstanceId, isSubTask, targetId, targetType, errorCode, System.currentTimeMillis() - startTime, scrmCallbackMsg.getMessage());
        if (!NumberUtils.validLong(taskAsyncRequestDataVO.getRecordId())) {
            return;
        }

        AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(TaskType.getByCode(taskAsyncRequestDataVO.getTaskType()));
        if (Objects.isNull(taskExecutor)) {
            return;
        }

        tairLockManager.tryRunWithLock(TairConstant.TASK_SINGLE_DATA_EXECUTE_LOCK_PREFIX + taskAsyncRequestDataVO.getRecordId(),
                5000L,
                () -> taskExecutor.updateAsyncExecuteResult(taskAsyncRequestDataVO.getRecordId(), scrmCallbackMsg),
                () -> PlatformLogUtil.logFail("单条任务数据处理_异步请求结果，获取分布式锁失败", LogListUtil.newArrayList(scrmCallbackMsg))
        );
    }
}
