package com.alibaba.tripscrm.service.service.wechat.groupmsg;


import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgDO;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupMsgQuery;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WechatGroupMsgService {
    /**
     * 根据参数统计总数
     *
     * @param query
     */
    long count(WechatGroupMsgQuery query);

    /**
     * 根据参数查询
     *
     * @param query
     */
    WechatGroupMsgDO find(WechatGroupMsgQuery query);

    /**
     * 列表查询
     *
     * @param query
     */
    List<WechatGroupMsgDO> list(WechatGroupMsgQuery query);

    /**
     * 创建
     *
     * @param wechatGroupMsgDO
     */
    int insertSelective(WechatGroupMsgDO wechatGroupMsgDO);

    /**
     * 选择性修改
     *
     * @param dto
     * @param condition
     */
    int updateSelective(WechatGroupMsgDO dto, WechatGroupMsgQuery condition);
}