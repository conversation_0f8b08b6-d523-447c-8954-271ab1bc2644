package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ContactUpdateCallbackProcessor implements ProxyCallbackProcessor {
    private final MetaqProducer metaqProducer;
    private static final String CREATE_USER = "create_user";
    private static final String DELETE_USER = "delete_user";

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        // 目前只处理入群消息
        PlatformLogUtil.logInfo("收到SCRM企微通讯录回执信息", LogListUtil.newArrayList(scrmCallbackMsg));
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        if (!content.containsKey(CallbackConstant.CHANGE_TYPE) || !content.containsKey(CallbackConstant.EVENT)) {
            return true;
        }
        String changeType = content.getString(CallbackConstant.CHANGE_TYPE);
        if (!Lists.newArrayList(CREATE_USER, DELETE_USER).contains(changeType)) {
            return true;
        }
        processContactChange(scrmCallbackMsg);
        return true;
    }

    private void processContactChange(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        content.put(CallbackConstant.CORP_ID, content.getString(CallbackConstant.CORP_ID));
        content.put(CallbackConstant.DEPARTMENT_ID, content.getString(CallbackConstant.DEPARTMENT_ID));
        content.put(CallbackConstant.USER_ID, content.getString(CallbackConstant.USER_ID));
        content.put(CallbackConstant.CHANGE_TYPE, content.getString(CallbackConstant.CHANGE_TYPE));
        metaqProducer.send(MQEnum.WECHAT_CONTACT_USER_CHANGE, "", content.getString(CallbackConstant.CHANGE_TYPE), content.toJSONString());
    }

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.CONTACT_CHANGE_CALLBACK;
    }
}
