package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.dto.wechat.ScrmWechatCustomerRecallDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.data.query.common.CommonCustomerDataQueryProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerRecallService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 单向好友私聊
 * <AUTHOR>
 * @date 2024-02-29 17:04:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerRecallDataQueryProcessor extends AbstractTaskDataProcessor {
    private final CommonCustomerDataQueryProcessor commonCustomerDataQueryProcessor;
    private final WechatUserService wechatUserService;
    private final WechatCustomerRecallService wechatCustomerRecallService;
    private final LdbTairManager ldbTairManager;

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.CUSTOMER_RECALL);
    }

    /**
     * @param context 任务执行上下文
     * @return TaskDataVO
     */
    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());

        //获取企微用户id
        List<String> wechatUserIdList =
                extInfo.containsKey(TaskConstant.WECHAT_USER_ID_List)
                        ? extInfo.getObject(TaskConstant.WECHAT_USER_ID_List, new TypeReference<List<String>>() {
                }) : new ArrayList<>();
        if (CollectionUtils.isEmpty(wechatUserIdList)){
            wechatUserIdList = wechatUserService.listBySpaceId(taskInfoDO.getSpaceId()).stream()
                    .map(WechatUserDTO::getUserId).collect(Collectors.toList());
        }

        List<String> addCustomerSendMessageList = extInfo.containsKey(TaskConstant.ADD_CUSTOMER_SEND_MESSAGE_LIST)
                ?extInfo.getObject(TaskConstant.ADD_CUSTOMER_SEND_MESSAGE_LIST, new TypeReference<List<String>>() {
        })  : new ArrayList<>();

        if( CollectionUtils.isEmpty(addCustomerSendMessageList)){
            PlatformLogUtil.logFail("加好友欢迎语为空");
            throw new TripscrmException(TripSCRMErrorCode.ADD_FRIENDS_WELCOME_LANGUAGE_EMPTY);
        }
        if(!extInfo.containsKey("maxReachDays") || !extInfo.containsKey("maxRecallCount")){
            PlatformLogUtil.logFail("最新客户触达时间为空或单账号当日召回人数上限");
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        Integer maxReachDays =  extInfo.getInteger("maxReachDays");
        Integer maxRecallCount =  extInfo.getInteger("maxRecallCount");
        List<ScrmWechatCustomerRecallDTO> scrmWechatCustomerRecallDTOList = new ArrayList<>();

        //查询召回好友数据
        for(String wechatUserId : wechatUserIdList){
            //获得剩余召回数量
            Integer limit = getLimit( wechatUserId,maxRecallCount);
            List<ScrmWechatCustomerRecallDTO> scrmWechatCustomerRecallDTOS = wechatCustomerRecallService.selectByUserIdAndDeletTime(wechatUserId,maxReachDays,limit);
            scrmWechatCustomerRecallDTOList.addAll(scrmWechatCustomerRecallDTOS);
        }
        ArrayList<TaskDataVO.DataBodyVO> result = new ArrayList<>();
        //组装数据
        for (ScrmWechatCustomerRecallDTO scrmWechatCustomerRecallDTO : scrmWechatCustomerRecallDTOList){
            TaskDataVO.DataBodyVO dataBodyVO = getDataBodyVO(scrmWechatCustomerRecallDTO, addCustomerSendMessageList, taskInfoDO);
            result.add(dataBodyVO);
        }
        dataVO.setData(result);
        dataVO.setTotalCount((long) dataVO.getData().size());
        return dataVO;
    }

    private static TaskDataVO.DataBodyVO getDataBodyVO(ScrmWechatCustomerRecallDTO scrmWechatCustomerRecallDTO, List<String> addCustomerSendMessageList, TaskInfoDO taskInfoDO) {
        TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
        Random random = new Random();
        int randomIndex = random.nextInt(addCustomerSendMessageList.size());
        dataBodyVO.setTargetId(scrmWechatCustomerRecallDTO.getExternalUserId());
        dataBodyVO.setTargetType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
        JSONObject extInfoJson = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extInfoJson.put(TaskConstant.SEND_USER_ID, scrmWechatCustomerRecallDTO.getUserId());
        extInfoJson.put("message", addCustomerSendMessageList.get(randomIndex));
        extInfoJson.put("uuid", UUID.randomUUID().toString());
        dataBodyVO.setExtInfo(JSONObject.toJSONString(extInfoJson));
        return dataBodyVO;
    }

    private Integer getLimit(String wechatUserId, Integer maxRecallCount) {
        Date date = new Date();
        Long dayVersion = DateUtils.getDayVersion(date);

        String dayKey = TairConstant.WECHAT_USER_ADD_CUSTOMER_COUNT_IN_WINDOW_PREFIX + dayVersion + "_d_" + SpaceInfoThreadLocalUtils.getCorpId() + "_" + wechatUserId;
        Integer addFriendsNumToday = (Integer) ldbTairManager.get(dayKey);
        if (Optional.ofNullable(addFriendsNumToday).orElse(0) > SwitchConfig.ADD_FRIENDS_SINGLE_USER_ID_DAY_MAX_LIMIT) {
            PlatformLogUtil.logInfo(
                    "主动加好友，匹配企微号时，触发当天添加好友数上限",
                            LogListUtil.newArrayList(dayVersion, wechatUserId)
            );
            return 0;
        }
        Integer remainNumber = SwitchConfig.ADD_FRIENDS_SINGLE_USER_ID_DAY_MAX_LIMIT - Optional.ofNullable(addFriendsNumToday).orElse(0);

        return remainNumber > maxRecallCount ? maxRecallCount : remainNumber ;
    }

}
