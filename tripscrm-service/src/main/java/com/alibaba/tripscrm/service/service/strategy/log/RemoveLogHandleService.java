package com.alibaba.tripscrm.service.service.strategy.log;

import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.model.domain.log.SopLogContentBO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/3/17 13:51
 **/
@Service
public class RemoveLogHandleService extends SopLogHandleService {

    @Override
    public LogShowTypeEnum handleType() {
        return LogShowTypeEnum.REMOVE;
    }

    @Override
    public String getLogContentStr(SopLogContentBO sopLogContentBO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(sopLogContentBO.getNameDesc())
                .append("移除【")
                .append(sopLogContentBO.getPresentValue())
                .append("】");
        return stringBuilder.toString();
    }
}
