package com.alibaba.tripscrm.service.middleware.metaq.consumer.share;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.BizDelayContext;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.fliggy.fliggyplaycore.client.share.model.ShareSignUpRecordDTO;
import com.taobao.ateye.annotation.Switch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 玩法人传人报名消息消费
 */
@Slf4j
@Service(value = "playShareSignUpConsumer")
public class PlayShareSignUpConsumer implements MessageListenerConcurrently {
    @Autowired
    private MetaqProducer metaqProducer;
    @Switch(description = "临期消息提前秒数", name = "expireRemindSecond")
    private Integer expireRemindSecond = -2 * 60 * 60;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logFail("玩法人传人报名消息消费", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("玩法人传人报名消息消费，消息处理失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("玩法人传人报名消息消费，解析消息", LogListUtil.newArrayList(message));
            ShareSignUpRecordDTO shareSignUpRecordDTO = JSONObject.parseObject(message, ShareSignUpRecordDTO.class);
            String key = shareSignUpRecordDTO.getPlayId() + "_" + shareSignUpRecordDTO.getUserId();
            if (!Arrays.asList(PlayShareRelationConsumer.shareReminderPlayIds.split(",")).contains(shareSignUpRecordDTO.getPlayId())){
                return true;
            }
            // 临期通知,提前expireRemindHour小时
            metaqProducer.send(MQEnum.DELAY_BIZ_SCHEDULING, key, "", JSON.toJSONString(
                    new BizDelayContext(
                            DateUtils.addSeconds(shareSignUpRecordDTO.getExpireTime(), expireRemindSecond),
                            message,
                            key,
                            0)
            ), null);
            // 过期通知,任务过期提醒,原有基础上加3分钟
            metaqProducer.send(MQEnum.DELAY_BIZ_SCHEDULING, key, "", JSON.toJSONString(
                    new BizDelayContext(
                            DateUtils.addSeconds(shareSignUpRecordDTO.getExpireTime(), 3 * 60),
                            message,
                            key,
                            0)
            ), null);
        } catch (Exception e) {
            PlatformLogUtil.logException("玩法人传人报名消息消费，消息处理异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
        return true;
    }

}
