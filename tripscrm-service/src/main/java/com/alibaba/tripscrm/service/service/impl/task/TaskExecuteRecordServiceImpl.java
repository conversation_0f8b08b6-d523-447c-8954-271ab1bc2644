package com.alibaba.tripscrm.service.service.impl.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.mapper.lindorm.TaskExecuteRecordDAO;
import com.alibaba.tripscrm.dal.model.lindorm.TaskExecuteRecordDO;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.tddl.client.sequence.Sequence;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/11/18 16:47
 */
@Service
public class TaskExecuteRecordServiceImpl implements TaskExecuteRecordService {
    /**
     * 构造器注入
     */
    private final TaskExecuteRecordDAO taskExecuteRecordDAO;
    private final Sequence taskExecuteRecordIdSequence;

    public TaskExecuteRecordServiceImpl(TaskExecuteRecordDAO taskExecuteRecordDAO, @Qualifier("taskExecuteRecordIdSequence") Sequence taskExecuteRecordIdSequence) {
        this.taskExecuteRecordDAO = taskExecuteRecordDAO;
        this.taskExecuteRecordIdSequence = taskExecuteRecordIdSequence;
    }

    @Override
    public Long generateRecordId() {
        return taskExecuteRecordIdSequence.nextValue();
    }

    @Override
    public int upsert(TaskExecuteRecordBO bo) {
        if (Objects.isNull(bo) || bo.invalid()) {
            return 0;
        }

        TaskExecuteRecordDO record = convertToDO(bo);
        if (Objects.isNull(record)) {
            return 0;
        }

        record.setGmtModified(System.currentTimeMillis());
        return taskExecuteRecordDAO.upsert(record);
    }

    @Override
    public int deleteByRecordId(Long recordId) {
        if (!NumberUtils.validLong(recordId)) {
            return 0;
        }

        String pkey = NumberUtils.toReverseStr(recordId);
        return taskExecuteRecordDAO.deleteByPrimaryKey(pkey);
    }

    @Override
    public TaskExecuteRecordBO queryByRecordId(Long recordId) {
        if (!NumberUtils.validLong(recordId)) {
            return null;
        }

        String pkey = NumberUtils.toReverseStr(recordId);
        TaskExecuteRecordDO record = taskExecuteRecordDAO.queryByPrimaryKey(pkey);

        return convertToBO(record);
    }

    @Override
    public List<TaskExecuteRecordBO> queryByTargetAndInstance(TaskExecuteRecordBO query) {
        TaskExecuteRecordDO dQuery = convertToDO(query);
        if (Objects.isNull(dQuery)) {
            return Lists.newArrayList();
        }

        List<TaskExecuteRecordDO> records = taskExecuteRecordDAO.queryByTargetAndInstance(dQuery);
        return batchConvertToBO(records);
    }

    /**
     * bo转do
     *
     * @param bo bo
     * @return do
     */
    private TaskExecuteRecordDO convertToDO(TaskExecuteRecordBO bo) {
        if (Objects.isNull(bo)) {
            return null;
        }

        TaskExecuteRecordDO result = new TaskExecuteRecordDO();
        BeanUtils.copyProperties(bo, result);
        result.setRecordIdReverse(NumberUtils.toReverseStr(bo.getRecordId()));
        result.setTargetType(bo.getTargetTypeEnum().getCode());
        result.setStatus(bo.getStatusEnum().getCode());
        if (Objects.nonNull(bo.getExecuteInfo())) {
            result.setExecuteInfo(bo.getExecuteInfo().toJSONString());
        }
        if (Objects.nonNull(bo.getExtraInfo())) {
            result.setExtraInfo(bo.getExtraInfo().toJSONString());
        }

        return result;
    }

    /**
     * 批量do转bo
     *
     * @param records do列表
     * @return bo列表
     */
    private List<TaskExecuteRecordBO> batchConvertToBO(List<TaskExecuteRecordDO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream()
                .map(this::convertToBO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * do转bo
     *
     * @param record do
     * @return bo
     */
    private TaskExecuteRecordBO convertToBO(TaskExecuteRecordDO record) {
        if (Objects.isNull(record)) {
            return null;
        }

        TaskExecuteRecordBO result = new TaskExecuteRecordBO();
        BeanUtils.copyProperties(record, result);
        result.setRecordId(NumberUtils.fromReverseStr(record.getRecordIdReverse()));
        if (Objects.nonNull(record.getTargetType())) {
            result.setTargetTypeEnum(ActivityTargetTypeEnum.codeOf(record.getTargetType()));
        }

        result.setStatusEnum(TaskSingleDataExecuteResultEnum.of(record.getStatus()));
        if (StringUtils.hasText(record.getExecuteInfo())) {
            try {
                JSONObject executeInfo = JSON.parseObject(record.getExecuteInfo());
                if (Objects.nonNull(executeInfo)) {
                    result.setExecuteInfo(executeInfo);
                }
            } catch (Exception e) {
                PlatformLogUtil.logException("任务执行明细，DO转BO出错", e.getMessage(), e, LogListUtil.newArrayList(record));
            }
        }
        if (StringUtils.hasText(record.getExtraInfo())) {
            try {
                JSONObject extraInfo = JSON.parseObject(record.getExtraInfo());
                if (Objects.nonNull(extraInfo)) {
                    result.setExtraInfo(extraInfo);
                }
            } catch (Exception e) {
                PlatformLogUtil.logException("任务执行明细，DO转BO出错", e.getMessage(), e, LogListUtil.newArrayList(record));
            }
        }

        return result;
    }
}
