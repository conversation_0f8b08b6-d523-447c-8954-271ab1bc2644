package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/01/20 14:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateWeComRecordQuery extends BasePageRequest {
    /**
     *   主键
     */
    private Long id;

    /**
     * 创建时间（开始时间）
     */
    private Date createStartTime;

    /**
     * 创建时间（结束时间）
     */
    private Date createEndTime;

    /**
     * 服务商类型，1:比邻,10:百业
     */
    private Byte isvType;

    /**
     * 企业Id
     */
    private String corpId;
}
