package com.alibaba.tripscrm.service.middleware.metaq.consumer.wechat;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.wechat.CustomerChangeTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.SCRMCustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * topic: customer_relation_crowd_tag_sync
 * consumerId: CID_customer_relation_crowd_tag_sync
 *
 * <AUTHOR>
 * @since 2025/07/07
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerRelationCrowdTagSyncConsumer implements MessageListenerConcurrently {

    private final SCRMCustomerRelationOpenSearchManager scrmCustomerRelationOpenSearchManager;
    private final WechatCustomerService wechatCustomerService;


    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            String receivedMsg = new String(msg.getBody());
            PlatformLogUtil.logInfo("接收到客户关系诸葛标签变更消息", receivedMsg, context);
            if (msg.getReconsumeTimes() > SwitchConfig.CUSTOMER_RELATION_CROWD_TAG_SYNC_RETRY_COUNT) {
                PlatformLogUtil.logInfo("客户关系诸葛标签变更消息，消息重复消费次数超过上限，跳过执行", msg);
                continue;
            }
            ConsumeConcurrentlyStatus status = dealWithMessage(receivedMsg);
            if (Objects.equals(ConsumeConcurrentlyStatus.RECONSUME_LATER, status)) {
                PlatformLogUtil.logFail("客户关系诸葛标签更新失败，重试", LogListUtil.newArrayList(msg, context));
                return status;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private ConsumeConcurrentlyStatus dealWithMessage(String message) {
        JSONObject data = JSONObject.parseObject(message);
        String externalUserId = data.getString(CallbackConstant.EXTERNAL_USER_ID);
        String unionId = data.getString(CallbackConstant.UNION_ID);
        String changeType = data.getString(CallbackConstant.CHANGE_TYPE);
        String userId = data.getString(CallbackConstant.USER_ID);
        String corpId = data.getString(CallbackConstant.CORP_ID);
        CustomerChangeTypeEnum changeTypeEnum = CustomerChangeTypeEnum.codeOf(changeType);
        if (Objects.isNull(changeTypeEnum)) {
            PlatformLogUtil.logFail("客户关系诸葛标签更新失败，变更类型不合法", LogListUtil.newArrayList(changeType));
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        if (!StringUtils.hasLength(unionId)) {
            unionId = wechatCustomerService.getUnionIdByExternalUserId(externalUserId, corpId);
        }
        if (!StringUtils.hasLength(unionId)) {
            PlatformLogUtil.logFail("客户关系诸葛标签更新失败，查询客户unionId失败", LogListUtil.newArrayList(externalUserId, corpId));
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        FollowUserQuery query = new FollowUserQuery();
        query.setUnionId(unionId);
        query.setUserIdList(Lists.newArrayList(userId));
        query.setCorpId(corpId);
        TripSCRMResult<FollowUserInfo> openSearchResult = scrmCustomerRelationOpenSearchManager.getFollowUserInfo(query);
        if (Objects.isNull(openSearchResult) || !openSearchResult.isSuccess()) {
            PlatformLogUtil.logFail("客户关系诸葛标签更新失败，查询客户好友关系失败", LogListUtil.newArrayList(externalUserId, unionId, corpId));
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        FollowUserInfo followUserInfo = openSearchResult.getData();
        if (Objects.isNull(followUserInfo) || CollectionUtils.isEmpty(followUserInfo.getRelationList()) || Objects.isNull(followUserInfo.getRelationList().get(0))) {
            PlatformLogUtil.logFail("客户关系诸葛标签更新失败，查询客户好友关系为空，OpenSearch未同步完成", LogListUtil.newArrayList(externalUserId, unionId, corpId));
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        FollowUserInfo.CustomerRelation relation = followUserInfo.getRelationList().get(0);
        switch (changeTypeEnum) {
            case ADD_EXTERNAL_CONTACT:
            case ADD_HALF_EXTERNAL_CONTACT:
                if (Objects.isNull(relation.getStatus()) || !Objects.equals(CustomerRelationStatusEnum.FRIEND, CustomerRelationStatusEnum.of(relation.getStatus()))) {
                    PlatformLogUtil.logFail("客户关系诸葛标签更新失败，OpenSearch数据与回调数据不一致，需要重试消息", LogListUtil.newArrayList(externalUserId, unionId, corpId, userId));
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
                break;
            case DEL_EXTERNAL_CONTACT:
            case DEL_FOLLOW_USER:
                if (Objects.isNull(relation.getStatus()) || Objects.equals(CustomerRelationStatusEnum.FRIEND, CustomerRelationStatusEnum.of(relation.getStatus()))) {
                    PlatformLogUtil.logFail("客户关系诸葛标签更新失败，OpenSearch数据与回调数据不一致，需要重试消息", LogListUtil.newArrayList(externalUserId, unionId, corpId, userId));
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
                break;
        }
        String uid = wechatCustomerService.getUidByExternalUserIdAndCorpIdAndUnionId(externalUserId, corpId, unionId);
        if (!StringUtils.hasLength(uid)) {
            PlatformLogUtil.logFail("客户关系诸葛标签更新失败，查询客户淘宝id失败", LogListUtil.newArrayList(externalUserId, unionId, corpId));
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        boolean result = wechatCustomerService.updateCustomerRelationCrowdTag(unionId, uid);
        if (!result) {
            PlatformLogUtil.logFail("客户关系诸葛标签更新失败", LogListUtil.newArrayList(externalUserId, unionId, corpId));
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
