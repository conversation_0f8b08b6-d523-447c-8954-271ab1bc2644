package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 客户打标任务配置处理器
 *
 * <AUTHOR>
 * @date 2023-12-30 15:26:28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerAddTagTaskConfigProcessor extends AbstractTaskConfigProcessor {
    private final TagInfoService tagInfoService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        JSONObject jsonObject = super.getExtraInfo(taskInfoDO);
        if (!jsonObject.containsKey("tagList")) {
            return jsonObject;
        }

        List<String> tagList = jsonObject.getObject("tagList", new TypeReference<List<String>>() {
        });
        List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(tagList);
        jsonObject.put("tagNameList", tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        return jsonObject;
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.WECHAT_CUSTOMER_ADD_TAG;
    }
}
