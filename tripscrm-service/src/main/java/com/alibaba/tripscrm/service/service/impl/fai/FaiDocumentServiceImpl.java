package com.alibaba.tripscrm.service.service.impl.fai;

import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.model.domain.request.fai.CreateDocumentReq;
import com.alibaba.tripscrm.service.model.domain.request.fai.RemoveDocumentReq;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.service.fai.FaiDocumentService;
import com.fliggy.fai.client.FaiResponse;
import com.fliggy.fai.client.common.Operator;
import com.fliggy.fai.client.knowledge.model.StructuredContentDTO;
import com.fliggy.fai.client.knowledge.request.CreateDocumentRequest;
import com.fliggy.fai.client.knowledge.request.RemoveDocumentRequest;
import com.fliggy.fai.client.knowledge.service.DocumentService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/9/17
 */
@Slf4j
@Service
public class FaiDocumentServiceImpl implements FaiDocumentService {

    @Resource
    private DocumentService documentService;

    private final String APP = "tripscrm";

    @Override
    public BaseResult<Boolean> createDocument(CreateDocumentReq request) {
        CreateDocumentRequest createDocumentRequest = new CreateDocumentRequest();

        createDocumentRequest.setApp(APP);
        createDocumentRequest.setSource(request.getSource());
        createDocumentRequest.setLibraryId(request.getLibraryId());
        createDocumentRequest.setBusinessId(request.getBusinessId());

        Operator operator = new Operator();
        operator.setEmpId(request.getEmpId());
        operator.setUsername(request.getUsername());
        createDocumentRequest.setOperator(operator);

        if (MapUtils.isNotEmpty(request.getProperties())) {
            StructuredContentDTO structuredContentDTO = new StructuredContentDTO();
            structuredContentDTO.setProperties(request.getProperties());
            createDocumentRequest.setStructuredContent(structuredContentDTO);
        }
        try {
            PlatformLogUtil.logInfo("start调用fai创建文档", request, createDocumentRequest);
            FaiResponse<String> createResp = documentService.createDocument(createDocumentRequest);
            PlatformLogUtil.logInfo("end调用fai创建文档", request, createDocumentRequest, createResp);
            if (BooleanUtils.isNotTrue(createResp.getSuccess())) {
                return BaseResult.fail(createResp.getErrorCode(), createResp.getErrorMessage());
            }
            return BaseResult.success(true);
        } catch (Exception e) {
            PlatformLogUtil.logCommon(false, "调用fai创建文档异常", Lists.newArrayList(request, createDocumentRequest), e);
            return BaseResult.fail("调用fai创建文档异常, " + e.getMessage());
        }
    }

    @Override
    public BaseResult<Boolean> removeDocument(RemoveDocumentReq request) {
        RemoveDocumentRequest removeDocumentRequest = new RemoveDocumentRequest();

        removeDocumentRequest.setApp(APP);
        removeDocumentRequest.setSource(request.getSource());
        removeDocumentRequest.setLibraryId(request.getLibraryId());
        removeDocumentRequest.setBusinessId(request.getBusinessId());

        Operator operator = new Operator();
        operator.setEmpId(request.getEmpId());
        operator.setUsername(request.getUsername());
        removeDocumentRequest.setOperator(operator);
        try {
            PlatformLogUtil.logInfo("start调用fai删除文档", request, removeDocumentRequest);
            FaiResponse<?> removeResp = documentService.removeDocument(removeDocumentRequest);
            PlatformLogUtil.logInfo("end调用fai删除文档", request, removeDocumentRequest, removeResp);
            if (BooleanUtils.isNotTrue(removeResp.getSuccess())) {
                return BaseResult.fail(removeResp.getErrorCode(), removeResp.getErrorMessage());
            }
            return BaseResult.success(true);
        } catch (Exception e) {
            PlatformLogUtil.logCommon(false, "调用fai删除文档异常", Lists.newArrayList(request, removeDocumentRequest), e);
            return BaseResult.fail("调用fai删除文档异常, " + e.getMessage());
        }
    }
}
