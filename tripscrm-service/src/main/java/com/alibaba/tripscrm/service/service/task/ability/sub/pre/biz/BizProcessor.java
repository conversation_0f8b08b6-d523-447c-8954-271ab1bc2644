package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;

/**
 * 任务执行器业务逻辑的单个节点处理器
 */
public interface BizProcessor {
    /**
     * 处理逻辑
     *
     * @param context 任务执行上下文
     */
    TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception;
}
