package com.alibaba.tripscrm.service.service;

import com.alibaba.tripscrm.service.model.dto.tag.TagCoverCustomerDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagCoverGroupDTO;

import java.util.List;
import java.util.Set;

/**
 * SCRM离线数据服务
 * <AUTHOR>
 * @Date 2024/4/8 14:11
 **/
public interface OdpsService {

    /**
     * 查询标签覆盖的客户信息
     * @param tagIdList 标签id列表
     * @return 客户信息
     */
    List<TagCoverCustomerDTO> queryTagCoverCustomerInfo(String corpId, Set<Long> tagIdList);

    /**
     * 查询标签覆盖的客户信息
     * @param tagIdList 标签id列表
     * @return 群聊信息
     */
    List<TagCoverGroupDTO> queryTagCoverGroupInfo(String corpId, Set<Long> tagIdList);

}
