package com.alibaba.tripscrm.service.service.factory;

import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/3/12 11:22
 **/
public class ThreadPoolExecutorFactory {
    /**
     * 外呼推送更新本地线程池
     */
    public static final ThreadPoolExecutor VIP_PUSH_UPDATE_THREAD_POOL;

    /**
     * 朋友圈评论线程池
     */
    public static final ThreadPoolExecutor MOMENT_COMMENT_THREAD_POOL;

    /**
     * 物料-标签关系同步线程池
     */
    public static final ThreadPoolExecutor ITEM_TAG_RELATION_SYNC_THREAD_POOL;

    /**
     * 物料-标签关系写入线程池
     */
    public static final ThreadPoolExecutor ITEM_TAG_RELATION_INSERT_THREAD_POOL;

    static {
        VIP_PUSH_UPDATE_THREAD_POOL = new ThreadPoolExecutor(4, 8,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000)
                , new CustomizableThreadFactory("VIP_PUSH_UPDATE_THREAD_POOL")
                , new ThreadPoolExecutor.CallerRunsPolicy());

        MOMENT_COMMENT_THREAD_POOL = new ThreadPoolExecutor(4, 8,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(500)
                , new CustomizableThreadFactory("MOMENT_COMMENT_THREAD_POOL_")
                , new ThreadPoolExecutor.AbortPolicy());

        ITEM_TAG_RELATION_SYNC_THREAD_POOL = new ThreadPoolExecutor(2, 4,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(50000)
                , new CustomizableThreadFactory("ITEM_TAG_RELATION_SYNC_THREAD_POOL")
                , new ThreadPoolExecutor.AbortPolicy());

        ITEM_TAG_RELATION_INSERT_THREAD_POOL = new ThreadPoolExecutor(4, 8,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000)
                , new CustomizableThreadFactory("ITEM_TAG_RELATION_INSERT_THREAD_POOL")
                , new ThreadPoolExecutor.AbortPolicy());
    }

}
