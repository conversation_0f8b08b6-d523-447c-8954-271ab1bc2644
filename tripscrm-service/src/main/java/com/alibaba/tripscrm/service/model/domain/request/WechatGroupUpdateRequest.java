package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class WechatGroupUpdateRequest implements Serializable {
    /**
     * 群聊 chatId 列表
     */
    @NotEmpty(message = "群聊Id不可为空")
    private List<String> chatIdList;

    /**
     * 管理员 userIdList（修改管理员时传）
     */
    private List<String> adminUserIdList;

    /**
     * 群主 userId （修改群主时传）
     */
    private String ownerUserId;
}
