package com.alibaba.tripscrm.service.middleware.metaq.consumer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.trip.tripdivision.domain.TrdiDivisionDO;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.act.SerachNotBuyParamKeyEnum;
import com.alibaba.tripscrm.service.enums.act.UserActBhvTypeEnum;
import com.alibaba.tripscrm.service.enums.act.UserActBizTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.second.DivisionService;
import com.alibaba.tripscrm.service.service.space.SpaceResourceService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripscrm.service.util.system.FunctionUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.fliggy.tccp.client.api.model.OrderDTO;
import com.fliggy.tccp.client.api.model.OrderItemDTO;
import com.fliggy.tccp.client.api.query.request.QueryInfoType;
import com.fliggy.tccp.client.enumerate.BizTypeEnum;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.tccpos.query.biz.TccpBizSearchService;
import com.taobao.tccpos.request.OrderListSearchRequest;
import com.taobao.tccpos.response.OrderListSearchResponse;
import com.taobao.trip.train.dataobject.TrStationCityDo;
import com.taobao.trip.tripjourneyop.api.JourneyOpQueryFacade;
import com.taobao.trip.tripjourneyop.domain.index.JourneyOpCardInfo;
import com.taobao.trip.tripjourneyop.domain.request.QueryJourneyByTimeRequest;
import com.taobao.trip.tripjourneyop.enums.IndexSortKeyEnum;
import com.taobao.trip.tripjourneyop.enums.JourneyTypeEnum;
import com.taobao.tripjourneyop.domain.result.JourneyOpPageQueryResult;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * topic: tripscrm_fliggy_user_acts
 * consumerId: CID_tripscrm_fliggy_user_acts
 * <p>
 * 消息格式为：${window_end_time}\u0001${uid}\u0001${bhv_type}\u0001${bhv_time}\u0001${item_id}\u0001${biz_type}\u0001${extra_info}
 * ${biz_type}*${bhv_type}对应${extra_info}
 * 1.DMESTIC_FLIGHT_SEARCH: "dep_time"、"dep_city_name"、"dep_city_code"、"arr_city_name"、"arr_city_code"
 * 2.DMESTIC_FLIGHT_CLICK: 暂无数据
 * 3.TRAIN_TICKET_SEARCH: "dep_time"、"dep_station_code"、"dep_station"、"dep_city_id"、"arr_station"、"arr_station_code"、"arr_city_id"
 * 4.TRAIN_TICKET_CLICK: "item_id"、"dep_time"、"dep_station"、"arr_station"、"arr_time"、"train_no"
 * 5.NON_PACKAGE_HOTEL_SEARCH: "city_code"、"checkin_date"、"checkout_date"
 * 6.NON_PACKAGE_HOTEL_CLICK: "city_code"、"checkin_date"、"checkout_date"
 *
 * <AUTHOR>
 * @since 2024/6/6 10:46
 */
@Slf4j
@Component
public class FliggyUserActsConsumer implements MessageListenerConcurrently {
    @Resource
    private UicUtils uicUtils;
    @Resource
    private MetaqProducer metaqProducer;
    @Resource
    private DivisionService divisionService;
    @Resource
    private JourneyOpQueryFacade journeyOpQueryFacade;
    @Resource
    private WechatCustomerService wechatCustomerService;
    @Resource
    private SpaceResourceService spaceResourceService;
    @Resource
    private TccpBizSearchService tccpBizSearchService;

    /**
     * 机票出发日期（yyyy-MM-dd）
     * 机票出发时间
     */
    private final static String FLIGHT_LEAVE_DATE = "dmesticFlightLeaveDate";

    /**
     * 机票出发地（三字码）
     * 机票出发地三字码
     */
    private final static String FLIGHT_DEP_CITY_CODE3 = "dmesticFlightDepCityCode3";

    /**
     * 机票到达地（三字码）
     * 机票到达地三字码
     */
    private final static String FLIGHT_ARR_CITY_CODE3 = "dmesticFlightArrCityCode3";

    /**
     * 国内酒店目的地城市divisionId（例如30010）
     * 酒店入住城市/机票到达城市/火车票到达城市
     */
    private final static String NON_PACKAGE_HOTEL_DIVISION_ID = "nonPackageHotelDivisionId";

    /**
     * 国内酒店入住时间（yyyy-MM-dd）
     * 酒店入住时间/机票出发时间/火车票出发时间
     */
    private final static String NON_PACKAGE_HOTEL_CHECK_IN = "nonPackageHotelCheckIn";

    /**
     * 国内酒店离店时间（yyyy-MM-dd）
     * 酒店离店时间/机票出发时间+1d/火车票出发时间+1d
     */
    private final static String NON_PACKAGE_HOTEL_CHECK_OUT = "nonPackageHotelCheckOut";

    /**
     * 火车票出发站点
     */
    private final static String TRAIN_TICKET_DEP_LOCATION = "trainTicketDepLocation";

    /**
     * 火车票到达站点
     */
    private final static String TRAIN_TICKET_ARR_LOCATION = "trainTicketArrLocation";

    /**
     * 火车票出发日期
     */
    private final static String TRAIN_TICKET_DEP_DATE = "trainTicketDepDate";

    @Switch(description = "搜未购时间（分钟）", name = "delayMinute")
    public static Integer delayMinute = 60;

    @Switch(description = "搜未购时间误差（秒）", name = "maxDiffSeconds")
    public static Integer maxDiffSeconds = 60 * 30;

    private static final Map<UserActBizTypeEnum, FunctionUtils.Function4<Long, String, Long, JSONObject, Boolean>> FUNCTION_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        FUNCTION_MAP.put(UserActBizTypeEnum.DMESTIC_FLIGHT, this::processDmesticFlight);
        FUNCTION_MAP.put(UserActBizTypeEnum.TRAIN_TICKET, this::processTrainTicket);
        FUNCTION_MAP.put(UserActBizTypeEnum.NON_PACKAGE_HOTEL, this::processNonPackageHotel);
        FUNCTION_MAP.put(UserActBizTypeEnum.INTERNATIONAL_FLIGHT_LIST, this::processInternationalFlight);
        FUNCTION_MAP.put(UserActBizTypeEnum.INTERNATIONAL_FLIGHT_OTA, this::processInternationalFlight);
        FUNCTION_MAP.put(UserActBizTypeEnum.VACATION_AUCTION, this::processVacation);
        FUNCTION_MAP.put(UserActBizTypeEnum.VACATION_POI, this::processVacation);
    }


    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("接收到用户搜索或点击行为消息", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("处理用户搜索或点击行为消息失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            List<String> contentList = paramCheckAndGetContent(message);
            if (contentList == null) {
                return false;
            }
            PlatformLogUtil.logInfo("用户搜索或点击行为消息解析后结果", message);
            // ${window_end_time}\u0001${uid}\u0001${bhv_type}\u0001${bhv_time}\u0001${item_id}\u0001${biz_type}\u0001${extra_info}
            // 用户uid
            Long uid = Long.parseLong(contentList.get(1));
            // 行为类型
            String bhvType = contentList.get(2);
            // 行为时间（单位秒）
            Long bhvTimestamp = Long.parseLong(contentList.get(3));
            // 物料Id
            String itemId = contentList.get(4);
            // 搜索或点击行为对应的业务类型
            String bizType = contentList.get(5);
            // 额外信息
            String extraInfo = contentList.get(6);

            if (invalidCheck(uid, contentList, bhvTimestamp, extraInfo, bhvType)) {
                PlatformLogUtil.logFail("用户搜索或点击行为消息处理，校验失败", LogListUtil.newArrayList(contentList));
                return false;
            }

            JSONObject extraInfoJson = JSONObject.parseObject(extraInfo);
            if (CollectionUtils.isEmpty(extraInfoJson)) {
                PlatformLogUtil.logFail("用户搜索或点击行为消息处理，额外信息为空", LogListUtil.newArrayList(contentList));
                return false;
            }

            UserActBizTypeEnum userActBizTypeEnum = UserActBizTypeEnum.of(bizType);
            if (Objects.isNull(userActBizTypeEnum) || !FUNCTION_MAP.containsKey(userActBizTypeEnum)) {
                PlatformLogUtil.logFail("用户搜索或点击行为消息处理，业务类型非法", LogListUtil.newArrayList(contentList));
                return false;
            }

            String unionId = uicUtils.getUnionIdByUid(String.valueOf(uid));
            if (!StringUtils.hasText(unionId)) {
                PlatformLogUtil.logFail("用户搜索或点击行为消息处理，根据uid查询unionId为空", LogListUtil.newArrayList(uid));
                return false;
            }

            if (!checkValidCustomerRelation(unionId)) {
                PlatformLogUtil.logFail("用户搜索或点击行为消息处理，根据unionId查询用户运营空间好友关系，结果为空", LogListUtil.newArrayList(uid, unionId));
                return false;
            }

            if (orderPayCheck(userActBizTypeEnum, uid, extraInfoJson, bhvTimestamp)) {
                PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，已购买", uid, unionId, userActBizTypeEnum, extraInfoJson);
                return true;
            }

            process(unionId, uid, userActBizTypeEnum, extraInfoJson, bhvTimestamp);
        } catch (Exception e) {
            PlatformLogUtil.logException("用户搜索或点击行为消息处理出错", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
        return true;
    }

    private void process(String unionId, Long uid, UserActBizTypeEnum userActBizTypeEnum, JSONObject extraInfoJson, Long bhvTimestamp) {
        for (String corpId : Lists.newArrayList(SwitchConfig.sellerCorpId, WxConstants.DEFAULT_ENTERPRISE_CORP_ID)) {
            try {
                SpaceInfoThreadLocalUtils.setCorpId(corpId);
                String externalUserId = wechatCustomerService.getExternalUserIdByUnionId(unionId);
                if (!StringUtils.hasText(externalUserId)) {
                    PlatformLogUtil.logFail("用户搜索或点击行为消息处理，根据unionId查询externalUserId为空", LogListUtil.newArrayList(uid, unionId, corpId));
                    continue;
                }

                PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，未购买", uid, externalUserId, userActBizTypeEnum, extraInfoJson, corpId);
                FUNCTION_MAP.get(userActBizTypeEnum).apply(uid, externalUserId, bhvTimestamp, extraInfoJson);   
            } catch (Exception e) {
                PlatformLogUtil.logException("用户搜索或点击行为消息处理，执行异常", e.getMessage(), e, LogListUtil.newArrayList(uid, unionId, corpId));
            } finally {
                SpaceInfoThreadLocalUtils.remove();
            }
        }
    }

    private boolean checkValidCustomerRelation(String unionId) {
        // 查询指定空间资源列表
        List<Integer> departmentIdList = new ArrayList<>();
        for (Long spaceId : SwitchConfig.visitedNotPaySpaceIdList) {
            List<SpaceResourceDO> spaceResourceList = spaceResourceService.listBySpaceIdAndResourceTypeWithCache(spaceId, ResourceTypeEnum.DEPARTMENT);
            // 绑定部门Id列表
            List<Integer> depList = spaceResourceList.stream()
                    .map(SpaceResourceDO::getTargetId)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            departmentIdList.addAll(depList);
        }

        if (CollectionUtils.isEmpty(departmentIdList)) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，未找到用户运营空间下企微部门id", LogListUtil.newArrayList(unionId));
            return false;
        }

        FollowUserQuery query = new FollowUserQuery();
        query.setUnionId(unionId);
        query.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode()));
        query.setDepartmentIdList(departmentIdList);
        query.setPageSize(1);
        FollowUserInfo followUserInfo = wechatCustomerService.listFollowUser(query);
        return Objects.nonNull(followUserInfo) && !CollectionUtils.isEmpty(followUserInfo.getRelationList());
    }

    private boolean orderPayCheck(UserActBizTypeEnum userActBizTypeEnum, Long uid, JSONObject extraInfoJson, Long bhvTimestamp) {
        List<JourneyTypeEnum> journeyTypeEnumList = new ArrayList<>();
        Boolean isVacation = false;
        switch (userActBizTypeEnum) {
            case DMESTIC_FLIGHT:
                journeyTypeEnumList.add(JourneyTypeEnum.CIVIL_FLIGHT);
                break;
            case TRAIN_TICKET:
                journeyTypeEnumList.add(JourneyTypeEnum.TRAIN);
                break;
            case NON_PACKAGE_HOTEL:
                journeyTypeEnumList.add(JourneyTypeEnum.HOTEL);
                break;
            case INTERNATIONAL_FLIGHT_LIST:
            case INTERNATIONAL_FLIGHT_OTA:
                journeyTypeEnumList.add(JourneyTypeEnum.INTER_FLIGHT);
                break;
            case VACATION_POI:
            case VACATION_AUCTION:
                isVacation = true;
                break;
            default:
                PlatformLogUtil.logFail("用户搜索或点击行为消息处理失败, 行程类型非法", LogListUtil.newArrayList(uid, userActBizTypeEnum, extraInfoJson));
                break;
        }

        if (isOrderPay(uid, journeyTypeEnumList, new Date((bhvTimestamp - 60 * 10) * 1000L), isVacation)) {
            PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，已购买", uid, userActBizTypeEnum, extraInfoJson);
            return true;
        }
        return false;
    }

    private static boolean invalidCheck(Long uid, List<String> contentList, Long bhvTimestamp, String extraInfo, String bhvType) {
        if (!NumberUtils.validLong(uid)) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，uid非法", LogListUtil.newArrayList(contentList));
            return true;
        }

        if (!NumberUtils.validLong(bhvTimestamp)) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，行为时间戳非法", LogListUtil.newArrayList(contentList));
            return true;
        }

        if (!StringUtils.hasLength(extraInfo)) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，额外信息为空", LogListUtil.newArrayList(contentList));
            return true;
        }

        UserActBhvTypeEnum userActBhvTypeEnum = UserActBhvTypeEnum.of(bhvType);
        if (Objects.isNull(userActBhvTypeEnum)) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，行为类型非法", LogListUtil.newArrayList(contentList));
            return true;
        }

        Long currentTimeStamp = System.currentTimeMillis() / 1000L;
        if (Math.abs(currentTimeStamp - bhvTimestamp - delayMinute * 60) > (maxDiffSeconds)) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，用户行为时间超出误差范围", LogListUtil.newArrayList(bhvTimestamp, currentTimeStamp));
            return true;
        }
        return false;
    }

    private static @Nullable List<String> paramCheckAndGetContent(String message) {
        if (!StringUtils.hasLength(message)) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息解析为空", LogListUtil.newArrayList(message));
            return null;
        }

        List<String> contentList = Arrays.stream(message.split("\\u0001")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contentList)) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息解析内容非法", LogListUtil.newArrayList(message));
            return null;
        }

        if (contentList.size() < 7) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息解析内容不足7个字段", LogListUtil.newArrayList(message));
            return null;
        }
        return contentList;
    }

    /**
     * 国内机票
     */
    private boolean processDmesticFlight(Long uid, String externalUserId, Long bhvTimestamp, JSONObject extraInfoJson) {
        // 1. 构造拼接国内机票和国内酒店Listing页面链接所需参数
        // https://outfliggys.m.taobao.com/app/trip/rx-flight-eco/pages/listing?searchType=1&leaveDate=2024-11-10&depCityCode=CTU&arrCityCode=KMG&depCityName=%E6%88%90%E9%83%BD&arrCityName=%E6%98%86%E6%98%8E
        // https://outfliggys.m.taobao.com/app/trip/rx-hotel-listing/pages/home?disableNav=YES&titleBarHidden=2&_projVer=1.0.76&cityCode=310100&checkIn=2024-11-02&checkOut=2024-11-04
        // {"arr_city_code":"KMG","arr_city_name":"昆明","dep_city_code":"CTU","dep_city_name":"成都","dep_time":"1730736000"}
        return processFlight(uid, externalUserId, bhvTimestamp, extraInfoJson, MQEnum.WECHAT_CUSTOMER_VISITED_DMESTIC_FLIGHT_NOT_PAY);
    }

    /**
     * 火车票
     */
    private boolean processTrainTicket(Long uid, String externalUserId, Long bhvTimestamp, JSONObject extraInfoJson) {
        // 1. 构造拼接火车票和国内酒店Listing页面链接所需参数
        // https://outfliggys.m.taobao.com/app/trip/rx-train-main/pages/listing?_use_stream=1&_fli_online=true&depLocation=%E6%9D%AD%E5%B7%9E%E4%B8%9C&arrLocation=%E5%AE%81%E6%B3%A2&depDate=2024-10-29&_projVer=2.43.38
        // https://outfliggys.m.taobao.com/app/trip/rx-hotel-listing/pages/home?disableNav=YES&titleBarHidden=2&_projVer=1.0.76&cityCode=310100&checkIn=2024-11-02&checkOut=2024-11-04
        // 搜索：{"arr_city_id":"330200","arr_station":"宁波","arr_station_code":"NGH","dep_city_id":"330100","dep_station":"杭州东","dep_station_code":"HGH","dep_time":"1729958400"}
        // 点击：{"arr_station":"西昌西","arr_time":"1730083800","dep_station":"成都南","dep_time":"1730070360","item_id":"************","train_no":"D251"}
        if (!extraInfoJson.containsKey("dep_time") || !extraInfoJson.containsKey("arr_station") || !extraInfoJson.containsKey("dep_station")) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，火车票参数信息不足", LogListUtil.newArrayList(externalUserId, extraInfoJson));
            return false;
        }

        Date depTime = new Date(extraInfoJson.getLong("dep_time") * 1000L);
        String arrStation = extraInfoJson.getString("arr_station");
        String depStation = extraInfoJson.getString("dep_station");

        String depDateStr = depTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String checkOutDateStr = depTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 2. 发送搜未购触达消息
        JSONObject message = new JSONObject();
        TrStationCityDo depTrStationCityDo = divisionService.getTrStationCityDoByName(depStation);
        TrStationCityDo arrTrStationCityDo = divisionService.getTrStationCityDoByName(arrStation);
        Long nonPackageHotelDivisionId = Optional.ofNullable(arrTrStationCityDo).map(TrStationCityDo::getCityCode).orElse(null);
        String depCityName = Optional.ofNullable(depTrStationCityDo).map(TrStationCityDo::getCityName).orElse(null);
        String arrCityName = Optional.ofNullable(arrTrStationCityDo).map(TrStationCityDo::getCityName).orElse(null);

        if (!StringUtils.hasText(depCityName) || !StringUtils.hasText(arrCityName)
                || !StringUtils.hasText(arrStation) || !StringUtils.hasText(depStation)
                || !StringUtils.hasText(depDateStr) || !StringUtils.hasText(externalUserId)
                || !StringUtils.hasText(arrCityName) || !StringUtils.hasText(checkOutDateStr)
                || !NumberUtils.validLong(nonPackageHotelDivisionId)) {
            PlatformLogUtil.logFail("火车票搜未购事件参数缺失，无法触发任务执行", LogListUtil.newArrayList(message));
            return false;
        }

        /**
         * 去掉定义
         */
        message.put(TRAIN_TICKET_DEP_LOCATION, depStation);
        message.put(TRAIN_TICKET_DEP_DATE, depDateStr);
        message.put(TRAIN_TICKET_ARR_LOCATION, arrStation);

        message.put(NON_PACKAGE_HOTEL_DIVISION_ID, nonPackageHotelDivisionId);
        message.put(NON_PACKAGE_HOTEL_CHECK_IN, depDateStr);
        message.put(NON_PACKAGE_HOTEL_CHECK_OUT, checkOutDateStr);

        /**
         * 通用定义
         */
        message.put(SerachNotBuyParamKeyEnum.CORP_ID.getKey(), WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
        message.put(SerachNotBuyParamKeyEnum.EXTERNAL_USER_ID.getKey(), externalUserId);
        message.put(SerachNotBuyParamKeyEnum.USER_ID.getKey(), uid);

        message.put(SerachNotBuyParamKeyEnum.DEP_TIME.getKey(), depDateStr);
        message.put(SerachNotBuyParamKeyEnum.DEP_CITY_NAME.getKey(), depCityName);
        message.put(SerachNotBuyParamKeyEnum.DEP_STATION.getKey(), depStation);
        message.put(SerachNotBuyParamKeyEnum.ARR_CITY_NAME.getKey(), arrCityName);
        message.put(SerachNotBuyParamKeyEnum.ARR_STATION.getKey(), arrStation);

        message.put(SerachNotBuyParamKeyEnum.ARR_DIVISION_ID.getKey(), nonPackageHotelDivisionId);
        message.put(SerachNotBuyParamKeyEnum.CHECK_IN_DATE.getKey(), depDateStr);
        message.put(SerachNotBuyParamKeyEnum.CHECK_OUT_DATE.getKey(), checkOutDateStr);

        message.put(TaskConstant.CORP_ID, SpaceInfoThreadLocalUtils.getCorpId());

        // 填充参数
        return metaqProducer.send(MQEnum.WECHAT_CUSTOMER_VISITED_TRAIN_TICKET_NOT_PAY, externalUserId, "", message.toJSONString());
    }

    /**
     * 酒店
     */
    private boolean processNonPackageHotel(Long uid, String externalUserId, Long bhvTimestamp, JSONObject extraInfoJson) {
        // 1. 构造拼接酒店Listing页面链接所需参数
        // https://outfliggys.m.taobao.com/app/trip/rx-hotel-listing/pages/home?disableNav=YES&titleBarHidden=2&_projVer=1.0.76&cityCode=310100&checkIn=2024-11-02&checkOut=2024-11-04
        // https://outfliggys.m.taobao.com/app/trip/rx-hotel-listing/pages/home?disableNav=YES&titleBarHidden=2&_projVer=1.0.76&cityCode=310100&checkIn=2024-11-02&checkOut=2024-11-04
        // {"checkin_date":"1729958400","checkout_date":"1730044800","city_code":"310100"}
        if (!extraInfoJson.containsKey("checkin_date") || !extraInfoJson.containsKey("checkout_date") || !extraInfoJson.containsKey("city_code")) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，酒店参数信息不足", LogListUtil.newArrayList(externalUserId, extraInfoJson));
            return false;
        }

        Date checkinDate = new Date(extraInfoJson.getLong("checkin_date") * 1000L);
        Date checkoutDate = new Date(extraInfoJson.getLong("checkout_date") * 1000L);
        String cityCode = extraInfoJson.getString("city_code");
        // 根据cityCode查询城市名称
        String checkinDateStr = checkinDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String checkoutDateStr = checkoutDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 2. 发送搜未购触达消息
        JSONObject message = new JSONObject();
        List<TrdiDivisionDO> divisions = divisionService.getDivision(Lists.newArrayList(Long.parseLong(cityCode)));
        String cityName = null;
        if (!CollectionUtils.isEmpty(divisions)) {
            cityName = divisions.get(0).getName();
        }

        if (!StringUtils.hasText(cityName) || !StringUtils.hasText(checkinDateStr)
                || !StringUtils.hasText(checkoutDateStr) || !StringUtils.hasText(externalUserId)
                || !StringUtils.hasText(cityCode) || !StringUtils.hasText(externalUserId)) {
            PlatformLogUtil.logFail("酒店搜未购事件参数缺失，无法触发任务执行", LogListUtil.newArrayList(message));
            return false;
        }
        /**
         * 去掉定义
         */
        message.put("checkinDate", checkinDateStr);
        message.put("checkoutDate", checkoutDateStr);
        message.put(NON_PACKAGE_HOTEL_DIVISION_ID, cityCode);
        message.put(NON_PACKAGE_HOTEL_CHECK_IN, checkinDateStr);
        message.put(NON_PACKAGE_HOTEL_CHECK_OUT, checkoutDateStr);

        /**
         * 通用定义
         */
        message.put(SerachNotBuyParamKeyEnum.CORP_ID.getKey(), WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
        message.put(SerachNotBuyParamKeyEnum.EXTERNAL_USER_ID.getKey(), externalUserId);
        message.put(SerachNotBuyParamKeyEnum.USER_ID.getKey(), uid);

        message.put(SerachNotBuyParamKeyEnum.CHECK_IN_DATE.getKey(), checkinDateStr);
        message.put(SerachNotBuyParamKeyEnum.CHECK_OUT_DATE.getKey(), checkoutDateStr);
        message.put(SerachNotBuyParamKeyEnum.ARR_DIVISION_ID.getKey(), cityCode);
        message.put(SerachNotBuyParamKeyEnum.ARR_CITY_NAME.getKey(), cityName);

        message.put(TaskConstant.CORP_ID, SpaceInfoThreadLocalUtils.getCorpId());

        // 填充参数
        return metaqProducer.send(MQEnum.WECHAT_CUSTOMER_VISITED_NON_PACKAGE_HOTEL_NOT_PAY, externalUserId, "", message.toJSONString());
    }

    /**
     * 判断用户是否已支付
     *
     * @param uid          用户淘宝id
     * @param journeyTypes 行程类型
     * @param bhvTime      用户行为时间
     * @param isVacation   行程类型是否为度假相关类型
     * @return
     */
    private Boolean isOrderPay(Long uid, List<JourneyTypeEnum> journeyTypes, Date bhvTime, Boolean isVacation) {
        if (isVacation) {
            return isOrderPayVacation(uid, bhvTime);
        }
        return isOrderPay(uid, journeyTypes, bhvTime);
    }

    private Boolean isOrderPayVacation(Long uid, Date bhvTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String gmtCreateStart = formatter.format(bhvTime);
        OrderListSearchRequest request = new OrderListSearchRequest();
        request.setBuyerId(uid);
        request.setGmtCreateStart(gmtCreateStart);
        request.setPageSize(10);
        request.setPageIndex(0);
        request.setBizType(BizTypeEnum.ITEM_GOODS.getCode());
        request.setQueryInfoTypes(new QueryInfoType[]{QueryInfoType.NEW_QUERY_ORDER_ITEM});
        OrderListSearchResponse response = tccpBizSearchService.searchOrderList(request);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getTccpOrders())) {
            PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，查询用户订单信息，订单信息为空", LogListUtil.newArrayList(request, response));
            return false;
        }
        for (OrderDTO orderDTO : response.getTccpOrders()) {
            // 如果订单信息为空，跳过
            if (CollectionUtils.isEmpty(orderDTO.getOrderItemDTOs()) || Objects.isNull(orderDTO.getOrderDO()) || Objects.isNull(orderDTO.getOrderDO().getPayTime())) {
                continue;
            }
            Date payTime = orderDTO.getOrderDO().getPayTime();
            // 如果支付时间早于行为时间，跳过
            if (payTime.before(bhvTime)) {
                PlatformLogUtil.logFail("用户搜索或点击行为消息处理，查询用户订单信息，是之前的订单", LogListUtil.newArrayList(uid, payTime, bhvTime));
                continue;
            }
            for (OrderItemDTO orderItemDTO : orderDTO.getOrderItemDTOs()) {
                if (Objects.isNull(orderItemDTO) || Objects.isNull(orderItemDTO.getOrderItemDO()) || !NumberUtils.validLong(orderItemDTO.getOrderItemDO().getOutRootCat())) {
                    continue;
                }
                if (SwitchConfig.VACATION_CATE_ID_LIST.contains(orderItemDTO.getOrderItemDO().getOutRootCat())) {
                    PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，查询用户订单信息，存在已支付的订单", LogListUtil.newArrayList(orderItemDTO, request, payTime, orderDTO.getOrderDO()));
                    return true;
                }
            }
        }
        PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，查询用户订单信息，不存在已支付的订单", LogListUtil.newArrayList(request, response));
        return false;
    }

    private Boolean isOrderPay(Long uid, List<JourneyTypeEnum> journeyTypes, Date bhvTime) {
        if (journeyTypes.size() == 0) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，查询用户订单信息失败, 行程类型为空", LogListUtil.newArrayList(uid, journeyTypes));
            return true;
        }
        QueryJourneyByTimeRequest request = new QueryJourneyByTimeRequest();
        request.setJourneyTypes(journeyTypes);
        request.setSortKey(IndexSortKeyEnum.BUY_TIME);
        request.setUserId(String.valueOf(uid));
        request.setLeftStartTime(new Date());
        request.setPageNum(1);
        request.setPageSize(10);
        JourneyOpPageQueryResult journeyOpPageQueryResult = journeyOpQueryFacade.queryOrderCardsByUserIdAndTimeAndTypeAndBuyTime(request);
        if (Objects.isNull(journeyOpPageQueryResult) || !journeyOpPageQueryResult.isSuccess()) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，查询用户订单信息失败", LogListUtil.newArrayList(uid, journeyTypes));
            throw new TripscrmException(TripSCRMErrorCode.QUERY_USER_ORDER_FAIL);
        }

        if (CollectionUtils.isEmpty(journeyOpPageQueryResult.getJourneyOpCardInfos())) {
            PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，查询用户订单信息，订单信息为空", LogListUtil.newArrayList(uid, journeyTypes));
            return false;
        }

        // 过滤之前的订单
        List<JourneyOpCardInfo> validCardInfoList = journeyOpPageQueryResult.getJourneyOpCardInfos()
                .stream().filter(journeyOpCardInfo -> {
                    if (journeyOpCardInfo.getBuyTime().before(bhvTime)) {
                        PlatformLogUtil.logFail("用户搜索或点击行为消息处理，查询用户订单信息，是之前的订单", LogListUtil.newArrayList(uid, journeyOpCardInfo, journeyTypes));
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validCardInfoList)) {
            return false;
        }

        return journeyOpPageQueryResult.getJourneyOpCardInfos().stream().allMatch(journeyOpCardInfo -> isPay(uid, journeyTypes, journeyOpCardInfo));
    }

    private static boolean isPay(Long uid, List<JourneyTypeEnum> journeyTypes, JourneyOpCardInfo journeyOpCardInfo) {
        // 无法判断
        if (!StringUtils.hasText(journeyOpCardInfo.getCardJson())) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，查询用户订单信息，订单状态信息为空", LogListUtil.newArrayList(uid, journeyOpCardInfo, journeyTypes));
            return false;
        }

        // 无法判断
        JSONObject cardJson = JSONObject.parseObject(journeyOpCardInfo.getCardJson());
        if (!cardJson.containsKey("cusOrderStatus")) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，查询用户订单信息，订单状态信息为空", LogListUtil.newArrayList(uid, journeyOpCardInfo, journeyTypes));
            return false;
        }

        String cusOrderStatus = cardJson.getString("cusOrderStatus");
        boolean isPay = true;
        JourneyTypeEnum journeyType = JourneyTypeEnum.getByName(journeyOpCardInfo.getJourneyType(), journeyOpCardInfo.getJourneySubType());
        if (Objects.equals(journeyType, JourneyTypeEnum.CIVIL_FLIGHT) || Objects.equals(journeyType, JourneyTypeEnum.INTER_FLIGHT)) {
            isPay = !Lists.newArrayList("INIT", "TOPAY").contains(cusOrderStatus);
        } else if (Objects.equals(journeyType, JourneyTypeEnum.TRAIN)) {
            isPay = !Lists.newArrayList("-1", "3").contains(cusOrderStatus);
        } else if (Objects.equals(journeyType, JourneyTypeEnum.HOTEL)) {
            isPay = !Lists.newArrayList("PREPAY_NOT_PAY", "CASH_STATUS_BOOK_WAITBUYERPAY", "ORDER_ORDER_STATUS_BOOK_WAIT_PAY").contains(cusOrderStatus);
        }
        PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，查询用户订单信息", LogListUtil.newArrayList(uid, journeyOpCardInfo, journeyTypes, isPay));
        return isPay;
    }

    /**
     * 度假
     *
     * @param externalUserId 用户id
     * @param bhvTimestamp   用户行为时间
     * @param extraInfoJson  额外信息
     * @return
     */
    private boolean processVacation(Long uid, String externalUserId, Long bhvTimestamp, JSONObject extraInfoJson) {
        String arrCityName;
        if (!extraInfoJson.containsKey("arr_city_name")) {
            PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，度假参数信息不足，采用默认值", externalUserId, bhvTimestamp, extraInfoJson);
            arrCityName = SwitchConfig.SEARCH_NOT_PAY_VACATION_DEFAULT_KEYWORD;
        } else {
            arrCityName = extraInfoJson.getString("arr_city_name");
            if (SwitchConfig.VACATION_CATE_DEFAULT_ARR_CITY_NAME_LIST.contains(arrCityName)) {
                PlatformLogUtil.logInfo("用户搜索或点击行为消息处理，度假参数信息不足，采用默认值", externalUserId, bhvTimestamp, extraInfoJson);
                arrCityName = SwitchConfig.SEARCH_NOT_PAY_VACATION_DEFAULT_KEYWORD;
            }
        }
        if (!StringUtils.hasText(arrCityName) || !StringUtils.hasText(externalUserId)) {
            PlatformLogUtil.logFail("度假搜未购事件参数缺失，无法触发任务执行", LogListUtil.newArrayList(externalUserId, bhvTimestamp, extraInfoJson));
            return false;
        }
        // 2. 发送搜未购触达消息
        JSONObject message = new JSONObject();
        message.put(SerachNotBuyParamKeyEnum.CORP_ID.getKey(), WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
        message.put(SerachNotBuyParamKeyEnum.EXTERNAL_USER_ID.getKey(), externalUserId);
        message.put(SerachNotBuyParamKeyEnum.USER_ID.getKey(), uid);

        message.put(SerachNotBuyParamKeyEnum.ARR_CITY_NAME.getKey(), arrCityName);

        message.put(TaskConstant.CORP_ID, SpaceInfoThreadLocalUtils.getCorpId());

        // 填充参数
        boolean send = metaqProducer.send(MQEnum.WECHAT_CUSTOMER_VISITED_VACATION_NOT_PAY, externalUserId, "", message.toJSONString());
        PlatformLogUtil.logInfo("度假搜未购事件, 消息发送完成", LogListUtil.newArrayList(send, message, externalUserId, bhvTimestamp, extraInfoJson));
        return send;
    }

    /**
     * 国际机票
     *
     * @param externalUserId 用户id
     * @param bhvTimestamp   用户行为时间
     * @param extraInfoJson  额外信息
     */
    private boolean processInternationalFlight(Long uid, String externalUserId, Long bhvTimestamp, JSONObject extraInfoJson) {
        // 1. 构造拼接国内机票和国内酒店Listing页面链接所需参数
        // https://market.m.taobao.com/app/trip/rx-iflight-eco/pages/listing?searchType=1&leaveDate=2024-11-10&depCityCode=CTU&arrCityCode=KMG&depCityName=%E6%88%90%E9%83%BD&arrCityName=%E6%98%86%E6%98%8E
        // {"arr_city_code":"KMG","arr_city_name":"昆明","dep_city_code":"CTU","dep_city_name":"成都","dep_time":"1730736000"}
        return processFlight(uid, externalUserId, bhvTimestamp, extraInfoJson, MQEnum.WECHAT_CUSTOMER_VISITED_INTERNATIONAL_FLIGHT_NOT_PAY);
    }

    /**
     * 机票类型数据的处理
     *
     * @param externalUserId 用户id
     * @param bhvTimestamp   用户行为时间
     * @param extraInfoJson  额外信息
     * @param mqEnum         消息枚举
     * @return
     */
    private boolean processFlight(Long uid, String externalUserId, Long bhvTimestamp, JSONObject extraInfoJson, MQEnum mqEnum) {
        if (!extraInfoJson.containsKey("dep_time") || !extraInfoJson.containsKey("dep_city_code") || !extraInfoJson.containsKey("arr_city_code")) {
            PlatformLogUtil.logFail("用户搜索或点击行为消息处理，机票参数信息不足", LogListUtil.newArrayList(externalUserId, bhvTimestamp, extraInfoJson, mqEnum));
            return false;
        }

        Date depTime = new Date(extraInfoJson.getLong("dep_time") * 1000L);
        String depCityCode = extraInfoJson.getString("dep_city_code");
        String depCityName = extraInfoJson.getString("dep_city_name");
        String arrCityCode = extraInfoJson.getString("arr_city_code");
        String arrCityName = extraInfoJson.getString("arr_city_name");

        String leaveDateStr = depTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String checkOutDateStr = depTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        Long divisionId = divisionService.getDivisionIdByCityCode3(arrCityCode);
        // 2. 发送搜未购触达消息
        /**
         * 需要去掉
         */
        JSONObject message = new JSONObject();
        message.put(FLIGHT_LEAVE_DATE, leaveDateStr);
        message.put(FLIGHT_DEP_CITY_CODE3, depCityCode);
        message.put(FLIGHT_ARR_CITY_CODE3, arrCityCode);
        // arrCityCode 转成 cityCode
        message.put(NON_PACKAGE_HOTEL_DIVISION_ID, divisionId);
        message.put(NON_PACKAGE_HOTEL_CHECK_IN, leaveDateStr);
        message.put(NON_PACKAGE_HOTEL_CHECK_OUT, checkOutDateStr);

        /**
         * 通用定义
         */
        message.put(SerachNotBuyParamKeyEnum.CORP_ID.getKey(), WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
        message.put(SerachNotBuyParamKeyEnum.EXTERNAL_USER_ID.getKey(), externalUserId);
        message.put(SerachNotBuyParamKeyEnum.USER_ID.getKey(), uid);

        message.put(SerachNotBuyParamKeyEnum.DEP_TIME.getKey(), leaveDateStr);
        message.put(SerachNotBuyParamKeyEnum.DEP_CITY_NAME.getKey(), depCityName);
        message.put(SerachNotBuyParamKeyEnum.DEP_CITY_CODE_3.getKey(), depCityCode);

        message.put(SerachNotBuyParamKeyEnum.ARR_CITY_NAME.getKey(), arrCityName);
        message.put(SerachNotBuyParamKeyEnum.ARR_CITY_CODE_3.getKey(), arrCityCode);

        message.put(SerachNotBuyParamKeyEnum.ARR_DIVISION_ID.getKey(), divisionId);
        message.put(SerachNotBuyParamKeyEnum.CHECK_IN_DATE.getKey(), leaveDateStr);
        message.put(SerachNotBuyParamKeyEnum.CHECK_OUT_DATE.getKey(), checkOutDateStr);

        message.put(TaskConstant.CORP_ID, SpaceInfoThreadLocalUtils.getCorpId());

        if (!StringUtils.hasText(depCityName) || !StringUtils.hasText(arrCityName)
                || !StringUtils.hasText(leaveDateStr) || !StringUtils.hasText(checkOutDateStr)
                || !StringUtils.hasText(externalUserId) || !StringUtils.hasText(depCityCode)
                || !StringUtils.hasText(arrCityCode) || !NumberUtils.validLong(divisionId)) {
            PlatformLogUtil.logFail("机票搜未购事件参数缺失，无法触发任务执行", LogListUtil.newArrayList(externalUserId, bhvTimestamp, extraInfoJson, message, mqEnum));
            return false;
        }
        PlatformLogUtil.logInfo("机票搜未购事件, 消息发送成功", message, mqEnum, externalUserId, bhvTimestamp, extraInfoJson);
        // 填充参数
        return metaqProducer.send(mqEnum, externalUserId, "", message.toJSONString());
    }

    @AteyeInvoker(description = "手动触发搜未购消息", paraDesc = "message")
    public boolean processSearchNotPayMessage(String message) {
        return dealWithMessage(message);
    }
}
