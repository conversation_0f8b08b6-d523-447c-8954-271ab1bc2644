package com.alibaba.tripscrm.service.middleware.metaq.consumer.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.schedulerx.shade.com.google.common.collect.Lists;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.act.UserActBizTypeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * topic: scrm_wechat_customer_expiring_coupon
 * consumerId: CID_scrm_wechat_customer_expiring_coupon
 * <p>
 * 消息格式为：${external_user_id}\t${template_code}\t${template_name}\t${end_time}\t${coupon_amt}\t${biz_type}
 * <AUTHOR>
 * @since 2025/04/03
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerExpiringCouponConsumer implements MessageListenerConcurrently {

    private final MetaqProducer metaqProducer;

    private final WechatCustomerService wechatCustomerService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("接收到用户券过期消息", msg);
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("处理用户券过期消息失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private Boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("用户券过期消息解析后结果", message);
            if (!StringUtils.hasLength(message)) {
                PlatformLogUtil.logFail("用户券过期消息解析为空", LogListUtil.newArrayList(message));
                return false;
            }
            List<String> contentList = Arrays.stream(message.split("\t")).filter(StringUtils::hasLength).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contentList) || contentList.size() != 6 || !contentList.stream().allMatch(StringUtils::hasLength)) {
                PlatformLogUtil.logFail("用户券过期消息解析为空", LogListUtil.newArrayList(message));
                return false;
            }
            String externalUserId = contentList.get(0);
            List<WechatCustomerVO> wechatCustomerVOS = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(externalUserId));
            if (CollectionUtils.isEmpty(wechatCustomerVOS) || Objects.isNull(wechatCustomerVOS.get(0))) {
                PlatformLogUtil.logFail("处理用户券过期消息失败，用户信息不存在", LogListUtil.newArrayList(message));
                return false;
            }

            if (!EnvUtils.isOnline() && !SwitchConfig.COUPON_EXPIRING_WHITE_LIST.contains(externalUserId)) {
                PlatformLogUtil.logFail("处理用户券过期消息失败，用户不在预发白名单中", LogListUtil.newArrayList(message));
                return false;
            }
            String templateCode = contentList.get(1);
            String templateName = contentList.get(2);
            String endTimeStr = contentList.get(3);
            String couponAmt = contentList.get(4);
            String bizType = contentList.get(5);
            MQEnum mqEnum;
            switch (UserActBizTypeEnum.of(bizType)) {
                case DMESTIC_FLIGHT:
                    mqEnum = MQEnum.WECHAT_CUSTOMER_EXPIRING_COUPON_DMESTIC_FLIGHT;
                    break;
                case VACATION:
                    mqEnum = MQEnum.WECHAT_CUSTOMER_EXPIRING_COUPON_VACATION;
                    break;
                case NON_PACKAGE_HOTEL:
                    mqEnum = MQEnum.WECHAT_CUSTOMER_EXPIRING_COUPON_NON_PACKAGE_HOTEL;
                    break;
                case INTERNATIONAL_FLIGHT_LIST:
                    mqEnum = MQEnum.WECHAT_CUSTOMER_EXPIRING_COUPON_INTERNATIONAL_FLIGHT;
                    break;
                case TRAIN_TICKET:
                    mqEnum = MQEnum.WECHAT_CUSTOMER_EXPIRING_COUPON_TRAIN_TICKET;
                    break;
                case UNIVERSAL:
                    mqEnum = MQEnum.WECHAT_CUSTOMER_EXPIRING_COUPON_UNIVERSAL;
                    break;
                default:
                    PlatformLogUtil.logFail("用户券过期消息解析失败，业务类型非法", LogListUtil.newArrayList(message));
                    return false;
            }
            PlatformLogUtil.logInfo("用户券过期消息处理成功", mqEnum, externalUserId, endTimeStr, templateCode, templateName, couponAmt);
            JSONObject msg = new JSONObject();
            msg.put("externalUserId", externalUserId);
            msg.put("templateCode", templateCode);
            msg.put("templateName", templateName);
            msg.put("couponAmt", couponAmt);
            msg.put("corpId", WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            return metaqProducer.send(mqEnum, externalUserId, "", msg.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("用户券过期消息处理失败", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
    }
}
