package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 微信客户信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatCustomerListQuery extends BasePageRequest {
    /**
     * 客户名称/备注
     */
    private String name;

    /**
     * 企微成员 userId
     */
    private String userId;

    /**
     * 企微客户 unionId
     */
    private String unionId;

    /**
     * 企微客户 externalUserId
     */
    private String externalUserId;

    /**
     * 成员性别（0未知，1男，2女）
     */
    private Integer gender;

    /**
     * 客户账号类型（1=个人微信，2=企业微信）
     */
    private Integer userType;

    /**
     * 标签类型（0：包含全部，1：包含任意，2：未打标签）
     */
    private Integer tagType;

    /**
     * 标签Id列表
     */
    private List<String> tagIdList;

    /**
     * 不包含的标签列表
     */
    private List<String> excludeTagIdList;

    /**
     * 部门Id列表
     */
    private List<Integer> departmentIdList;

    /**
     * 业务空间 Id
     */
    private Long spaceId;

    /**
     * 群聊id
     */
    private String chatId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 添加时间
     */
    private List<String> addTime;

    /**
     * 分组求count字段
     */
    private List<String> countFields;

    /**
     * 和所属企微号的好友关系列表，1-好友，2-单向好友，3-已删除
     */
    private List<Integer> statusList;
}
