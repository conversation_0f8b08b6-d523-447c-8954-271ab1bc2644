package com.alibaba.tripscrm.service.service.impl.knowledge;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.mapper.tddl.KnowledgeDocumentMapper;
import com.alibaba.tripscrm.dal.model.domain.data.KnowledgeDocumentDO;
import com.alibaba.tripscrm.dal.model.domain.data.KnowledgeDocumentParam;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.enums.system.KnowledgeDocumentStatusEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.dto.MessageRelationInfoDTO;
import com.alibaba.tripscrm.service.model.domain.fusionchat.message.TextMessage;
import com.alibaba.tripscrm.service.model.domain.request.fai.CreateDocumentReq;
import com.alibaba.tripscrm.service.model.domain.request.fai.RemoveDocumentReq;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.DocumentReq;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.DocumentSyncFaiReq;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.DocumentTestReq;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.model.dto.knowledge.KnowledgeCategoryDTO;
import com.alibaba.tripscrm.service.model.dto.knowledge.KnowledgeDocumentDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fai.FaiDocumentService;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.knowledge.KnowledgeCategoryService;
import com.alibaba.tripscrm.service.service.knowledge.KnowledgeDocumentService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxCustomerSendMsgRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.testng.collections.Lists;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/9/15
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class KnowledgeDocumentServiceImpl implements KnowledgeDocumentService {

    private final KnowledgeDocumentMapper knowledgeDocumentMapper;
    private final FaiDocumentService faiDocumentService;
    private final KnowledgeCategoryService knowledgeCategoryService;
    private final WechatCustomerService wechatCustomerService;
    private final IsvRouteService isvRouteService;
    private final CustomerService customerService;
    private final MaterialService materialService;
    private final MaterialTransferService materialTransferService;
    private final MessageUtils messageUtils;
    private final UicUtils uicUtils;
    private final String FAI_SOURCE = "API";

    @Override
    public List<KnowledgeDocumentDTO> selectByParam(DocumentReq query) {
        try {
            PlatformLogUtil.logInfo("查询知识文档", LogListUtil.newArrayList(query));
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("参数校验失败");
                return null;
            }
            List<KnowledgeDocumentDO> knowledgeDocumentDOS = knowledgeDocumentMapper.selectByParam(buildParam(query));
            if (CollectionUtils.isEmpty(knowledgeDocumentDOS)) {
                return null;
            }
            return knowledgeDocumentDOS.stream().map(this::convert).collect(Collectors.toList());
        } catch (Exception e) {
            PlatformLogUtil.logException("query执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public PageInfoDTO<KnowledgeDocumentDTO> pageQuery(DocumentReq query) {
        try {
            PlatformLogUtil.logInfo("查询知识文档", LogListUtil.newArrayList(query));
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("参数校验失败");
                return null;
            }
            KnowledgeDocumentParam param = new KnowledgeDocumentParam();
            KnowledgeDocumentParam.Criteria questionCriteria = param.createCriteria();
            if (Objects.nonNull(query.getSpaceId())) {
                questionCriteria.andSpaceIdEqualTo(query.getSpaceId());
            }
            if (Objects.nonNull(query.getId())) {
                questionCriteria.andIdEqualTo(query.getId());
            }
            if (Objects.nonNull(query.getCategoryId())) {
                questionCriteria.andCategoryIdEqualTo(query.getCategoryId());
            }
            if (StringUtils.isNotBlank(query.getSearchKey())) {
                questionCriteria.andAnswerLike("%" + query.getSearchKey() + "%");
            }
            if (StringUtils.isNotBlank(query.getStatus()) && Objects.nonNull(KnowledgeDocumentStatusEnum.findByCode(query.getStatus()))) {
                questionCriteria.andStatusEqualTo(query.getStatus());
            }
            questionCriteria.andDeletedEqualTo(0);
            questionCriteria.andEnvEqualTo(EnvUtils.getEnvironment());

            KnowledgeDocumentParam.Criteria or = param.or();
            if (Objects.nonNull(query.getSpaceId())) {
                or.andSpaceIdEqualTo(query.getSpaceId());
            }
            if (Objects.nonNull(query.getId())) {
                or.andIdEqualTo(query.getId());
            }
            if (Objects.nonNull(query.getCategoryId())) {
                or.andCategoryIdEqualTo(query.getCategoryId());
            }
            if (StringUtils.isNotBlank(query.getSearchKey())) {
                or.andQuestionsLike("%" + query.getSearchKey() + "%");
            }
            if (StringUtils.isNotBlank(query.getStatus()) && Objects.nonNull(KnowledgeDocumentStatusEnum.findByCode(query.getStatus()))) {
                or.andStatusEqualTo(query.getStatus());
            }
            or.andDeletedEqualTo(0);
            or.andEnvEqualTo(EnvUtils.getEnvironment());

            param.appendOrderByClause(KnowledgeDocumentParam.OrderCondition.GMTMODIFIED, KnowledgeDocumentParam.SortType.DESC);
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
            List<KnowledgeDocumentDO> knowledgeDocumentDOS = knowledgeDocumentMapper.selectByParam(param);
            if (CollectionUtils.isEmpty(knowledgeDocumentDOS)) {
                return null;
            }
            return PageUtils.getPageInfoDTO(knowledgeDocumentDOS, this::convert);
        } catch (Exception e) {
            PlatformLogUtil.logException("query执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public Long insert(KnowledgeDocumentDO knowledgeDocumentDO) {
        if (Objects.isNull(knowledgeDocumentDO)) {
            PlatformLogUtil.logFail("insert参数校验失败");
            return 0L;
        }
        knowledgeDocumentDO.setStatus(KnowledgeDocumentStatusEnum.EDITING.getCode());
        knowledgeDocumentDO.setDeleted(0);
        knowledgeDocumentDO.setEnv(EnvUtils.getEnvironment());
        knowledgeDocumentDO.setGmtCreate(new Date());
        knowledgeDocumentDO.setGmtModified(new Date());
        return (long) knowledgeDocumentMapper.insert(knowledgeDocumentDO);
    }

    @Override
    public KnowledgeDocumentDTO selectById(Long id) {
        KnowledgeDocumentDO knowledgeDocumentDO = knowledgeDocumentMapper.selectByPrimaryKey(id);
        if (Objects.isNull(knowledgeDocumentDO)) {
            return null;
        }
        return convert(knowledgeDocumentDO);
    }

    @Override
    public Long update(KnowledgeDocumentDTO knowledgeDocument) {
        if (Objects.isNull(knowledgeDocument)) {
            PlatformLogUtil.logFail("update参数校验失败");
            return 0L;
        }
        KnowledgeDocumentDO knowledgeDocumentDO = new KnowledgeDocumentDO();
        knowledgeDocumentDO.setId(knowledgeDocument.getId());
        knowledgeDocumentDO.setCategoryId(knowledgeDocument.getCategoryId());
        knowledgeDocumentDO.setStatus(knowledgeDocument.getStatus());
        knowledgeDocumentDO.setQuestions(knowledgeDocument.getQuestions());
        knowledgeDocumentDO.setAnswerType(knowledgeDocument.getAnswerType());
        knowledgeDocumentDO.setAnswer(knowledgeDocument.getAnswer());
        knowledgeDocumentDO.setMaterialId(knowledgeDocument.getMaterialId());
        knowledgeDocumentDO.setSpaceId(knowledgeDocument.getSpaceId());
        knowledgeDocumentDO.setCreatorId(knowledgeDocument.getCreatorId());
        knowledgeDocumentDO.setCreatorName(knowledgeDocument.getCreatorName());
        knowledgeDocumentDO.setLastOperatorId(knowledgeDocument.getLastOperatorId());
        knowledgeDocumentDO.setLastOperatorName(knowledgeDocument.getLastOperatorName());
        knowledgeDocumentDO.setGmtModified(new Date());
        knowledgeDocumentDO.setEnv(knowledgeDocument.getEnv());
        knowledgeDocumentDO.setDeleted(knowledgeDocument.getDeleted());
        knowledgeDocumentDO.setGmtCreate(knowledgeDocument.getGmtCreate());
        return (long) knowledgeDocumentMapper.updateByPrimaryKey(knowledgeDocumentDO);
    }

    @Override
    public boolean deleteById(Long id, String userId, String userName) {
        if (Objects.isNull(id)) {
            PlatformLogUtil.logFail("参数校验失败");
            return false;
        }
        KnowledgeDocumentDO knowledgeDocumentDO = new KnowledgeDocumentDO();
        knowledgeDocumentDO.setId(id);
        knowledgeDocumentDO.setDeleted(1);
        knowledgeDocumentDO.setGmtModified(new Date());
        knowledgeDocumentDO.setLastOperatorId(userId);
        knowledgeDocumentDO.setLastOperatorName(userName);
        return knowledgeDocumentMapper.updateByPrimaryKeySelective(knowledgeDocumentDO) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncFai(DocumentSyncFaiReq request) {
        if (BooleanUtils.isTrue(request.getSyncFai())) {
            return upFai(request);
        } else {
            return downFai(request);
        }
    }

    private boolean upFai(DocumentSyncFaiReq request){
        // 修改数据库的状态为上线中
        KnowledgeDocumentDO knowledgeDocumentDO = new KnowledgeDocumentDO();
        knowledgeDocumentDO.setId(request.getId());
        knowledgeDocumentDO.setStatus(KnowledgeDocumentStatusEnum.ONLINE_IN_PROGRESS.getCode());
        knowledgeDocumentDO.setGmtModified(new Date());
        knowledgeDocumentDO.setLastOperatorId(request.getUserId());
        knowledgeDocumentDO.setLastOperatorName(request.getUserName());
        knowledgeDocumentMapper.updateByPrimaryKeySelective(knowledgeDocumentDO);
        // 调用fai创建文档
        CreateDocumentReq createDocumentReq = new CreateDocumentReq();
        createDocumentReq.setSource(FAI_SOURCE);
        createDocumentReq.setEmpId(request.getUserId());
        createDocumentReq.setUsername(request.getUserName());
        createDocumentReq.setLibraryId(SwitchConfig.TRIPSCRM_DOCUMENT_LIBRARY_ID);
        createDocumentReq.setBusinessId(request.getId().toString());

        KnowledgeDocumentDTO documentDO = request.getKnowledgeDocument();

        Map<String,Object> properties = MapUtils.newHashMap();
        KnowledgeCategoryDTO category = knowledgeCategoryService.selectById(documentDO.getCategoryId());
        if (Objects.isNull(category)) {
            PlatformLogUtil.logFail("知识文档类目不存在", LogListUtil.newArrayList(documentDO.getCategoryId()));
            throw new RuntimeException("知识文档类目不存在");
        }
        properties.put("cate", category.getName());
        List<String> questions = JSONObject.parseArray(documentDO.getQuestions(), String.class);
        if (CollectionUtils.isNotEmpty(questions)) {
            properties.put("question", String.join("|", questions));
        }
        properties.put("answer", documentDO.getAnswer());
        properties.put("biz_id", documentDO.getId());
        properties.put("space_id", documentDO.getSpaceId());
        // 设置可用业务id字段
        properties.put("businessIdColumn", true);
        createDocumentReq.setProperties(properties);
        BaseResult<Boolean> document = faiDocumentService.createDocument(createDocumentReq);
        if (!document.isSuccess()) {
            PlatformLogUtil.logFail("调用fai创建文档异常", LogListUtil.newArrayList(createDocumentReq, document));
            throw new RuntimeException("调用fai创建文档异常");
        }
        return true;
    }

    private boolean downFai(DocumentSyncFaiReq request){
        // 修改数据库的状态为下线中
        KnowledgeDocumentDO knowledgeDocumentDO = new KnowledgeDocumentDO();
        knowledgeDocumentDO.setId(request.getId());
        knowledgeDocumentDO.setStatus(KnowledgeDocumentStatusEnum.EDITING.getCode());
        knowledgeDocumentDO.setGmtModified(new Date());
        knowledgeDocumentDO.setLastOperatorId(request.getUserId());
        knowledgeDocumentDO.setLastOperatorName(request.getUserName());
        knowledgeDocumentMapper.updateByPrimaryKeySelective(knowledgeDocumentDO);
        // 调用fai删除文档
        RemoveDocumentReq removeDocumentReq = new RemoveDocumentReq();
        removeDocumentReq.setSource(FAI_SOURCE);
        removeDocumentReq.setEmpId(request.getUserId());
        removeDocumentReq.setUsername(request.getUserName());
        removeDocumentReq.setLibraryId(SwitchConfig.TRIPSCRM_DOCUMENT_LIBRARY_ID);
        removeDocumentReq.setBusinessId(request.getId().toString());
        BaseResult<Boolean> document = faiDocumentService.removeDocument(removeDocumentReq);
        if (!document.isSuccess()) {
            PlatformLogUtil.logFail("调用fai删除文档异常", LogListUtil.newArrayList(removeDocumentReq, document));
            throw new RuntimeException("调用fai删除文档异常");
        }
        return true;
    }


    private KnowledgeDocumentParam buildParam(DocumentReq query) {
        KnowledgeDocumentParam param = new KnowledgeDocumentParam();
        KnowledgeDocumentParam.Criteria criteria = param.createCriteria();
        if (Objects.nonNull(query.getSpaceId())) {
            criteria.andSpaceIdEqualTo(query.getSpaceId());
        }
        if (Objects.nonNull(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (Objects.nonNull(query.getCategoryId())) {
            criteria.andCategoryIdEqualTo(query.getCategoryId());
        }
        if (StringUtils.isNotBlank(query.getStatus()) && Objects.nonNull(KnowledgeDocumentStatusEnum.findByCode(query.getStatus()))) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        criteria.andDeletedEqualTo(0);
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());
        param.appendOrderByClause(KnowledgeDocumentParam.OrderCondition.GMTMODIFIED, KnowledgeDocumentParam.SortType.DESC);

        return param;
    }

    @AteyeInvoker(description = "修改知识文档状态", paraDesc = "documentId&status")
    public int delete(Long documentId, String status) {
        KnowledgeDocumentDO knowledgeDocumentDO = new KnowledgeDocumentDO();
        knowledgeDocumentDO.setId(documentId);
        knowledgeDocumentDO.setStatus(Objects.requireNonNull(KnowledgeDocumentStatusEnum.findByCode(status)).getCode());
        return knowledgeDocumentMapper.updateByPrimaryKeySelective(knowledgeDocumentDO);
    }

    @Override
    public Boolean selfTest(DocumentTestReq req) {
        // 参数校验
        if (req == null || Objects.isNull(req.getId()) || !ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode().equals(req.getTargetType())) {
            throw new TripscrmException("自测必须传入测试人员的淘系ID");
        }
        // 查询知识文档对象
        KnowledgeDocumentDO documentDO = knowledgeDocumentMapper.selectByPrimaryKey(req.getId());
        if (Objects.isNull(documentDO)) {
            throw new TripscrmException("知识文档数据异常操作");
        }
        // 查询企微unionId
        String unionId = uicUtils.getUnionIdByUid(req.getTargetId());
        if (!org.springframework.util.StringUtils.hasText(unionId)) {
            throw new TripscrmException("自测淘系ID对应的账号需要在飞猪微信小程序登陆过");
        }
        // 查询企微externalUserId
        String externalUserId = wechatCustomerService.getExternalUserIdByUnionId(unionId);
        if (!org.springframework.util.StringUtils.hasText(externalUserId)) {
            throw new TripscrmException("请确保自测ID对应的微信账号添加过飞猪企业微信成员");
        }
        // 发送人选取
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setExternalUserId(externalUserId);
        isvRouteContext.setRiskActionEnum(RiskActionEnum.CUSTOMER_SEND_MESSAGE);
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        TripSCRMResult<WechatUserDTO> wechatUserResult = isvRouteService.matchWechatUser(isvRouteContext);
        if (Objects.isNull(wechatUserResult) || !wechatUserResult.isSuccess() || Objects.isNull(wechatUserResult.getData())) {
            throw new TripscrmException("请确保自测ID对应的微信账号存在可用的在线企微成员");
        }
        // 获取企微成员id
        String userId = wechatUserResult.getData().getUserId();

        // 1. 构建消息发送对象
        List<MessageBO> messageList = buildSendMessageBOList(documentDO, userId, externalUserId);

        // 2. 发起请求
        WxCustomerSendMsgRequest wxCustomerSendMsgRequest = new WxCustomerSendMsgRequest();
        wxCustomerSendMsgRequest.setIsTest(true);
        wxCustomerSendMsgRequest.setRequestId(UUID.randomUUID().toString());
        wxCustomerSendMsgRequest.setExternalUserId(externalUserId);
        wxCustomerSendMsgRequest.setUserId(userId);
        wxCustomerSendMsgRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wxCustomerSendMsgRequest.setMessageList(messageList);
        ResultDO<String> resultDO = customerService.asyncSendMessage(wxCustomerSendMsgRequest);
        // 3. 日志留存
        if (resultDO.getSuccess()) {
            PlatformLogUtil.logInfo("智能回复发送消息调用成功-自测", LogListUtil.newArrayList(wxCustomerSendMsgRequest, resultDO, messageList));
        } else {
            PlatformLogUtil.logFail("智能回复发送消息调用失败-自测", LogListUtil.newArrayList(wxCustomerSendMsgRequest, resultDO, messageList));
            throw new TripscrmException("企微自测消息发送失败：" + resultDO.getResultMessage());
        }
        return true;
    }

    /**
     * 构建消息发送对象
     *
     * @param documentDO     知识文档对象
     * @param sendUserId     发送人id
     * @param externalUserId 接收人id
     * @return 消息发送对象
     */
    private List<MessageBO> buildSendMessageBOList(KnowledgeDocumentDO documentDO, String sendUserId, String externalUserId) {
        List<MessageBO> messageBOList = Lists.newArrayList();
        // 处理普通文案回复消息
        if (StringUtils.isNotBlank(documentDO.getAnswer())) {
            messageBOList.add(buildCommonTextMessageBO(sendUserId, externalUserId, documentDO.getAnswer()));
        }
        // 处理素材回复消息
        if (Objects.nonNull(documentDO.getMaterialId())) {
            messageBOList.addAll(buildMaterialMessageBOList(sendUserId, externalUserId, documentDO.getMaterialId()));
        }
        // 重置消息编号
        for (int i = 0; i < messageBOList.size(); i++) {
            messageBOList.get(i).setMsgNum(i + 1);
        }
        return messageBOList;
    }

    /**
     * 构建普通文案回复消息
     *
     * @param sendUserId     发送人id
     * @param externalUserId 接收人id
     * @param answer         回复内容
     * @return 消息发送对象
     */
    private MessageBO buildCommonTextMessageBO(String sendUserId, String externalUserId, String answer) {
        // 1. 借助聚合聊天工具处理
        // 1.1 构建消息基础对象
        FusionChatMessageBody fusionChatMessageBody = new FusionChatMessageBody();
        fusionChatMessageBody.setMsgType(FusionChatMessageTypeEnum.TEXT.getValue());
        TextMessage textMessage = new TextMessage();
        textMessage.setContent(answer);
        fusionChatMessageBody.setMsgContent(JSONObject.toJSONString(textMessage));
        // 1.2 构建消息关系对象
        MessageRelationInfoDTO messageRelationInfoDTO = new MessageRelationInfoDTO(ChatTypeEnum.SINGLE_FOR_CUSTOMER, externalUserId, sendUserId, null);
        // 2. 构建消息发送对象
        return messageUtils.fusionChatMessage2SendMessage(fusionChatMessageBody, messageRelationInfoDTO);
    }

    /**
     * 构建素材回复消息
     *
     * @param sendUserId     发送人id
     * @param externalUserId 接收人id
     * @param materialId     素材id
     * @return 消息发送对象
     */
    private List<MessageBO> buildMaterialMessageBOList(String sendUserId, String externalUserId, Long materialId) {
        // 1. 查询素材对象
        MaterailInfoDO materailInfoDO = materialService.queryById(materialId);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("智能回复任务结果回答素材id不存在", LogListUtil.newArrayList(materialId));
            throw new TripscrmException(ErrorCodeEnum.SMART_RESPONSE_TASK_RESULT_MATERIAL_ERROR);
        }

        // 关系构建
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(MaterialSendSceneTypeConstant.SMART_RESPONSE_SILIAO, sendUserId, externalUserId, materialId);
        // 素材上下文
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setWechatUserId(sendUserId);
        materialContentConvertContext.setExtraInfo(Collections.emptyMap());
        materialContentConvertContext.setOriginContent(materailInfoDO.getContent());
        return materialTransferService.buildMessages(materailInfoDO, materialTrackRelationDTO, materialContentConvertContext, TaskType.SMART_RESPONSE.getCode());
    }

    /**
     * 构建素材关系对象
     *
     * @param sceneType  场景类型
     * @param sendUserId 发送人id
     * @param chatId     接收人id
     * @param materialId 素材id
     * @return 素材关系对象
     */
    protected MaterialTrackRelationDTO buildMaterialTrackRelationDTO(String sceneType, String sendUserId, String chatId, Long materialId) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setMaterialId(materialId);
        materialTrackRelationDTO.setTaskId(-1L);
        materialTrackRelationDTO.setTaskInsId(-1L);
        materialTrackRelationDTO.setAbBucketId("-1");
        materialTrackRelationDTO.setSceneType(sceneType);
        materialTrackRelationDTO.setWxUserId(sendUserId);
        materialTrackRelationDTO.setSendChatId(chatId);
        return materialTrackRelationDTO;
    }


    @AteyeInvoker(description = "模拟知识文档上/下架fai处理结果消息", paraDesc = "knowledgeDocId&true上架&false下架")
    public String testFaiConsumer(String docId, Boolean isUp) {
        if (StringUtils.isBlank(docId) || Objects.isNull(isUp)) {
            return "参数校验失败";
        }
        KnowledgeDocumentDO documentDO = knowledgeDocumentMapper.selectByPrimaryKey(Long.valueOf(docId));
        if (Objects.isNull(documentDO)) {
            return "文档为空";
        }
        documentDO.setStatus(isUp ? KnowledgeDocumentStatusEnum.ONLINE.getCode() : KnowledgeDocumentStatusEnum.EDITING.getCode());
        documentDO.setGmtModified(new Date());
        knowledgeDocumentMapper.updateByPrimaryKeySelective(documentDO);
        return "处理成功";
    }

    private KnowledgeDocumentDTO convert(KnowledgeDocumentDO documentDO) {
        if (Objects.isNull(documentDO)) {
            return null;
        }
        KnowledgeDocumentDTO knowledgeDocumentDTO = new KnowledgeDocumentDTO();
        knowledgeDocumentDTO.setId(documentDO.getId());
        knowledgeDocumentDTO.setCategoryId(documentDO.getCategoryId());
        knowledgeDocumentDTO.setStatus(documentDO.getStatus());
        knowledgeDocumentDTO.setQuestions(documentDO.getQuestions());
        knowledgeDocumentDTO.setAnswerType(documentDO.getAnswerType());
        knowledgeDocumentDTO.setAnswer(documentDO.getAnswer());
        knowledgeDocumentDTO.setMaterialId(documentDO.getMaterialId());
        knowledgeDocumentDTO.setSpaceId(documentDO.getSpaceId());
        knowledgeDocumentDTO.setCreatorId(documentDO.getCreatorId());
        knowledgeDocumentDTO.setCreatorName(documentDO.getCreatorName());
        knowledgeDocumentDTO.setLastOperatorId(documentDO.getLastOperatorId());
        knowledgeDocumentDTO.setLastOperatorName(documentDO.getLastOperatorName());
        knowledgeDocumentDTO.setGmtCreate(documentDO.getGmtCreate());
        knowledgeDocumentDTO.setGmtModified(documentDO.getGmtModified());
        knowledgeDocumentDTO.setEnv(documentDO.getEnv());
        knowledgeDocumentDTO.setDeleted(documentDO.getDeleted());
        return knowledgeDocumentDTO;
    }
}
