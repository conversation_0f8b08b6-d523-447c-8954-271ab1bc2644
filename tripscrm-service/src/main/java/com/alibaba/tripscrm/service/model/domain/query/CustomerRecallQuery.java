package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/6/6 19:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerRecallQuery extends BasePageRequest {

    /**
     * 是否删除
     */
    private Byte deleted = 0;

    /*
     *   corp_id
     */
    private String corpId;

    /*
     *   企微id
     */
    private String userId;

    /*
     *   客户id
     */
    private String externalUserId;

    /*
     *   删除时间
     */
    private Date deleteTime;

    /*
     *   isv类型
     */
    private Integer isvType;

    /*
     *   扩展信息更新时间
     */
    private Date extInfoUpdateTime;

    /*
     *   召回状态:0待召回 1召回中 2召回失败 3召回成功 4无法召回 5其他方式已加好友
     */
    private Integer status;

    /*
     *   加好友时间
     */
    private Date addTime;
}
