package com.alibaba.tripscrm.service.convert;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.util.IpUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskInstanceQuery;
import com.alibaba.tripscrm.service.model.domain.request.TaskInstanceQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.TaskInstanceSaveRequest;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.function.Supplier;

/**
 * 主任务实例模型的防腐层
 */
public class TaskInstanceConverter {

    /**
     * 保存请求的参数 转化为 数据模型需要的DO
     *
     * @param request
     * @param supplier
     * @return
     */
    public static TaskInstanceDO saveRequest2Do(TaskInstanceSaveRequest request, Supplier<TaskInfoDO> supplier) {
        TaskInstanceDO instanceDO = new TaskInstanceDO();
        BeanUtils.copyProperties(request, instanceDO);
        Date now = new Date();
        instanceDO.setGmtCreate(now);
        instanceDO.setGmtModified(now);
        instanceDO.setDeleted((byte) 0);
        instanceDO.setIpAddress(IpUtil.getIPV4Address());
        // 优先以传入为主，未传入则查询
        if (StringUtils.isBlank(request.getTaskConfig())) {
            TaskInfoDO taskInfoDO = supplier.get();
            if (taskInfoDO == null) {
                throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_TASK_INFO_DATA);
            }
            instanceDO.setTaskConfig(JSONObject.toJSONString(taskInfoDO));
        }
        return instanceDO;
    }

    /**
     * 查询请求的参数 转化为 查询模型需要的Query对象
     *
     * @param request
     * @return
     */
    public static TaskInstanceQuery queryRequest2Query(TaskInstanceQueryRequest request) {
        TaskInstanceQuery query = new TaskInstanceQuery();
        BeanUtils.copyProperties(request, query);
        if (request.getStatus() != null) {
            query.setStatus(request.getStatus().getStatus());
        }
        return query;
    }
}
