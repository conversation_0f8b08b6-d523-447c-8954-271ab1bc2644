package com.alibaba.tripscrm.service.service.task.ability.factory;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.service.task.ability.config.TaskConfigProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 任务创建初始化处理器工厂
 */
@Slf4j
@Component
public class TaskConfigProcessorFactory {
    public static final Map<TaskType, TaskConfigProcessor> TASK_CONFIG_PROCESSOR_MAP = new ConcurrentHashMap<>();

    /**
     * 注册
     */
    public static void registry(TaskType taskType, TaskConfigProcessor taskConfigProcessor) {
        TASK_CONFIG_PROCESSOR_MAP.put(taskType, taskConfigProcessor);
    }

    public static TaskConfigProcessor getDataProcessorByType(TaskType taskType) {
        return TASK_CONFIG_PROCESSOR_MAP.get(taskType);
    }

    public static JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        TaskType taskType = TaskType.getByCode(taskInfoDO.getType());
        TaskConfigProcessor taskConfigProcessor = TaskConfigProcessorFactory.getDataProcessorByType(taskType);
        if (Objects.isNull(taskConfigProcessor)) {
            return new JSONObject();
        }

        return taskConfigProcessor.getExtraInfo(taskInfoDO);
    }

    public static void postCreate(TaskInfoDO taskInfoDO) {
        TaskType taskType = TaskType.getByCode(taskInfoDO.getType());
        TaskConfigProcessor taskConfigProcessor = TaskConfigProcessorFactory.getDataProcessorByType(taskType);
        if (Objects.isNull(taskConfigProcessor)) {
            return;
        }

        taskConfigProcessor.postCreate(taskInfoDO);
    }

    public static void postUpdate(TaskInfoDO taskInfoDO) {
        TaskType taskType = TaskType.getByCode(taskInfoDO.getType());
        TaskConfigProcessor taskConfigProcessor = TaskConfigProcessorFactory.getDataProcessorByType(taskType);
        if (Objects.isNull(taskConfigProcessor)) {
            return;
        }

        taskConfigProcessor.postUpdate(taskInfoDO);
    }

    public static void postDelete(TaskInfoDO taskInfoDO) {
        TaskType taskType = TaskType.getByCode(taskInfoDO.getType());
        TaskConfigProcessor taskConfigProcessor = TaskConfigProcessorFactory.getDataProcessorByType(taskType);
        if (Objects.isNull(taskConfigProcessor)) {
            return;
        }

        taskConfigProcessor.postDelete(taskInfoDO);
    }
}
