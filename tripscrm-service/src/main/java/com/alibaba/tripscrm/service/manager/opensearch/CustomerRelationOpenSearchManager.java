package com.alibaba.tripscrm.service.manager.opensearch;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.FieldCountInfo;
import com.alibaba.tripscrm.domain.FollowUserCustomerRelationInfo;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.TagInfo;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.CustomerInfoQuery;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserPageQuery;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerListQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.aliyun.opensearch.sdk.generated.search.*;
import com.aliyun.opensearch.sdk.generated.search.general.SearchResult;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/24 21:59
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerRelationOpenSearchManager extends BaseOpenSearchManager {
    private final WXCorpStorage wxCorpStorage;
    private final TagInfoService tagInfoService;

    /**
     * 查询客户的企微成员好友
     *
     * @param query 查询条件
     * @return FollowUserInfo
     */
    public PageInfo<FollowUserCustomerRelationInfo> pageFollowUserInfo(FollowUserPageQuery query) {
        try {
            SearchParams searchParams = buildPageSearchParams(query, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (StringUtils.hasText(query.getUnionId())) {
                queryList.add(String.format("union_id:'%s'", query.getUnionId()));
            }
            if (StringUtils.hasText(query.getNameLike())) {
                queryList.add(String.format("name_like:'%s'", query.getNameLike()));
            }
            if (StringUtils.hasText(query.getRemarkLike())) {
                queryList.add(String.format("remark:'%s'", query.getRemarkLike()));
            }
            if (StringUtils.hasText(query.getNameOrRemarkLike())) {
                queryList.add("(" + String.format("remark:'%s'", query.getNameOrRemarkLike()) + " OR " +
                        String.format("name_like:'%s'", query.getNameOrRemarkLike()) + ")");
            }
            if (Objects.nonNull(query.getExternalUserIdList())) {
                queryList.add("(" + query.getExternalUserIdList().stream().map(userId -> String.format("external_user_id:'%s'", userId))
                        .collect(Collectors.joining(" OR ")) + ")");
            }
            if (!CollectionUtils.isEmpty(query.getUserIdList())) {
                queryList.add("(" + query.getUserIdList().stream().map(userId -> String.format("follow_user_id:'%s'", userId))
                        .collect(Collectors.joining(" OR ")) + ")");
            }
            if (Objects.nonNull(query.getDepartmentIdList())) {
                queryList.add("(" + query.getDepartmentIdList().stream().map(code -> String.format("department_id:'%d'", code))
                        .collect(Collectors.joining(" OR ")) + ")");
            }

            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            List<String> filterList = new ArrayList<>();
            if (Objects.nonNull(query.getRobotStatus())) {
                filterList.add(String.format("robot_status='%d'", query.getRobotStatus()));
            }
            if (NumberUtils.validInteger(query.getStatus())) {
                filterList.add(String.format("status='%d'", query.getStatus()));
            }
            // 添加租户过滤参数
            String corpId = SpaceInfoThreadLocalUtils.getCorpId();
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);

            PageInfo<FollowUserCustomerRelationInfo> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convertFollowUserCustomerRelationInfo);
            PlatformLogUtil.logFail("Opensearch查询客户的企微成员好友成功", LogListUtil.newArrayList(searchParams, pageInfo));
            return pageInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的企微成员好友异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }

        return new PageInfo<>();
    }

    /**
     * 查询客户的企微成员好友
     *
     * @param query 查询条件
     * @return FollowUserInfo
     */
    public FollowUserInfo getFollowUserInfo(FollowUserQuery query) {
        try {
            SearchParams searchParams = buildPageSearchParams(query, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (StringUtils.hasText(query.getUnionId())) {
                queryList.add(String.format("union_id:'%s'", query.getUnionId()));
            }
            if (StringUtils.hasText(query.getExternalUserId())) {
                queryList.add(String.format("external_user_id:'%s'", query.getExternalUserId()));
            }
            if (!CollectionUtils.isEmpty(query.getUserIdList())) {
                queryList.add("(" + query.getUserIdList().stream().map(userId -> String.format("follow_user_id:'%s'", userId)).collect(Collectors.joining(" OR ")) + ")");
            }
            if (!CollectionUtils.isEmpty(query.getDepartmentIdList())) {
                queryList.add("(" + query.getDepartmentIdList().stream().map(code -> String.format("department_id:'%d'", code)).collect(Collectors.joining(" OR ")) + ")");
            }

            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            List<String> filterList = new ArrayList<>();
            if (Objects.nonNull(query.getRobotStatus())) {
                filterList.add(String.format("robot_status='%d'", query.getRobotStatus()));
            }
            if (!CollectionUtils.isEmpty(query.getStatusList())) {
                String statusStr = query.getStatusList().stream()
                        .filter(s -> Objects.nonNull(CustomerRelationStatusEnum.of(s)))
                        .map(code -> "status=" + code)
                        .collect(Collectors.joining(" OR ", "(", ")"));
                filterList.add(statusStr);
            }
            Set<String> corpIdSet = new HashSet<>();
            Optional.ofNullable(query.getCorpIdList()).ifPresent(corpIdSet::addAll);
            Optional.ofNullable(query.getCorpId()).ifPresent(corpIdSet::add);
            Optional.ofNullable(SpaceInfoThreadLocalUtils.getCorpId()).ifPresent(corpIdSet::add);
            // 添加租户过滤参数
            if (!CollectionUtils.isEmpty(corpIdSet)) {
                String statusStr = corpIdSet.stream()
                        .filter(StringUtils::hasText)
                        .map(code -> String.format("corp_id=\"%s\"", code))
                        .collect(Collectors.joining(" OR ", "(", ")"));
                filterList.add(statusStr);
            }
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);

            PageInfo<FollowUserInfo.CustomerRelation> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convert2CustomerRelation);
            PlatformLogUtil.logFail("Opensearch查询客户的企微成员好友成功", LogListUtil.newArrayList(searchParams, pageInfo));
            return new FollowUserInfo(pageInfo.getList());
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的企微成员好友异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }

        return new FollowUserInfo();
    }

    public List<FieldCountInfo> getCustomerCount(CustomerInfoQuery query) {
        List<FieldCountInfo> countInfoList = new ArrayList<>();
        try {
            if (Objects.isNull(query.getUserId())) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
            }
            SearchParams searchParams = buildSearchParams();
            List<String> queryList = new ArrayList<>();
            if (Objects.nonNull(query.getUserId())) {
                queryList.add(String.format("follow_user_id:'%s'", query.getUserId()));
            }
            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            Set<Aggregate> aggregates = new HashSet<>();
            for (String field : query.getCountFields()) {
                Aggregate aggregate = new Aggregate();
                aggregate.setGroupKey(field).setAggFun("count()");
                aggregates.add(aggregate);
            }
            searchParams.setAggregates(aggregates);
            // 添加租户过滤参数
            List<String> filterList = new ArrayList<>();
            String corpId = query.getCorpId();
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            if (NumberUtils.validInteger(query.getStatus())) {
                filterList.add(String.format("status=%d", CustomerRelationStatusEnum.FRIEND.getCode()));
            } else {
                filterList.add(String.format("status<%d", CustomerRelationStatusEnum.NON_FRIEND.getCode()));
            }
            searchParams.setFilter(String.join(" AND ", filterList));
            SearchResult result = getSearcherClient().execute(searchParams);
            Map<String, Map<String, Long>> countMap = getResultFacetCount(result);
            for (Map.Entry<String, Map<String, Long>> entry : countMap.entrySet()) {
                FieldCountInfo fieldCountInfo = new FieldCountInfo();
                fieldCountInfo.setField(entry.getKey());
                fieldCountInfo.setCountMap(entry.getValue());
                countInfoList.add(fieldCountInfo);
            }
            PlatformLogUtil.logFail("Opensearch查询客户数成功", LogListUtil.newArrayList(searchParams, countInfoList));
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户数异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }
        return countInfoList;
    }

    public PageInfo<Set<String>> getCustomerTagIds(WechatCustomerListQuery query) {
        try {
            List<String> res = new ArrayList<>();
            SearchParams searchParams = buildPageSearchParams(query, null);
            searchParams.setQuery("union_id:'" + query.getUnionId() + "'");
            PageInfo<Set<String>> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convert2WechatCustomerTagIds);
            return pageInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户tag异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }
        return null;
    }

    /**
     * 查询客户Id
     *
     * @param query 查询条件
     * @return PageInfo<String>
     */
    public PageInfo<String> getCustomerExternalUserIdList(WechatCustomerListQuery query) {
        List<String> res = new ArrayList<>();
        try {
            SearchParams searchParams = buildPageSearchParams(query, "duniqfield:external_user_id");
            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (StringUtils.hasText(query.getName())) {
                queryList.add(String.format("name:'%s'", query.getName()));
            }
            if (StringUtils.hasText(query.getExternalUserId())) {
                queryList.add(String.format("external_user_id:'%s'", query.getExternalUserId()));
            }
            if (StringUtils.hasText(query.getUserId())) {
                queryList.add(String.format("follow_user_id:'%s'", query.getUserId()));
            }
            if (StringUtils.hasText(query.getUnionId())) {
                queryList.add(String.format("union_id:'%s'", query.getUnionId()));
            }
            if (StringUtils.hasText(query.getRemark())) {
                queryList.add(String.format("remark:'%s'", query.getRemark()));
            }
            queryList.addAll(buildTagQueryList(query));
            if (Objects.nonNull(query.getDepartmentIdList())) {
                queryList.add("(" + query.getDepartmentIdList().stream().map(code -> String.format("department_id:'%d'", code)).collect(Collectors.joining(" OR ")) + ")");
            }
            searchParams.setQuery(String.join(" AND ", queryList));
            // 过滤条件
            List<String> filterList = new ArrayList<>();
            // 添加租户过滤参数
            String corpId = SpaceInfoThreadLocalUtils.getCorpId();
            if (!CollectionUtils.isEmpty(query.getStatusList())) {
                String statusStr = query.getStatusList().stream()
                        .filter(s -> Objects.nonNull(CustomerRelationStatusEnum.of(s)))
                        .map(code -> "status=" + code)
                        .collect(Collectors.joining(" OR ", "(", ")"));
                filterList.add(statusStr);
            }
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            if (Objects.nonNull(query.getUserType())) {
                filterList.add(String.format("type='%d'", query.getUserType()));
            }
            if (Objects.nonNull(query.getGender())) {
                filterList.add(String.format("gender='%d'", query.getGender()));
            }
            filterList.addAll(buildTagFilterList(query));
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // "dist_key:external_user_id,dist_count:1,dist_times:1,reserved:false"
            Distinct distinct = new Distinct();
            distinct.setKey("external_user_id").setUpdateTotalHit(false).setDistCount(1).setDistTimes(1).setReserved(false);
            searchParams.setDistincts(Sets.newHashSet(distinct));

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);
            PageInfo<String> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convert2WechatCustomerExternalUserId);
            PlatformLogUtil.logFail("Opensearch查询客户Id成功", LogListUtil.newArrayList(searchParams, pageInfo));
            return pageInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户Id异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }

        return PageUtils.getPageInfo(res, a -> a);
    }

    private List<String> buildTagFilterList(WechatCustomerListQuery query) {
        if (Objects.isNull(query.getTagType()) || query.getTagType() != 2) {
            return new ArrayList<>();
        }

        if (SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
            return Lists.newArrayList("fieldlen(tags)=0", "fieldlen(wechat_tag_ids)=0");
        }

        return Lists.newArrayList("fieldlen(customer_tag_ids)=0", "fieldlen(wechat_tag_ids)=0");
    }

    private List<String> buildTagQueryList(WechatCustomerListQuery query) {
        List<String> queryList = new ArrayList<>();
        if (CollectionUtils.isEmpty(query.getTagIdList())) {
            return queryList;
        }

        if (Objects.isNull(query.getTagType()) || query.getTagType() == 2) {
            return queryList;
        }

        List<TagInfoDTO> tagInfoDTOS = tagInfoService.selectByTagIdList(query.getTagIdList());
        if (CollectionUtils.isEmpty(tagInfoDTOS)) {
            return queryList;
        }
        if (SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
            return Lists.newArrayList("(" +
                    tagInfoDTOS.stream()
                            .map(tagInfoDTO -> {
                                if (Objects.equals(TagTypeEnum.of(tagInfoDTO.getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG)) {
                                    return Lists.newArrayList(String.format("wechat_tag_ids:'%s'", tagInfoDTO.getId()));
                                }
                                return Lists.newArrayList(String.format("tag:'%s'", tagInfoDTO.getId()));
                            })
                            .flatMap(Collection::stream)
                            .collect(Collectors.joining(query.getTagType() == 0 ? " AND " : " OR "))
                    + ")");
        }

        return Lists.newArrayList("(" +
                tagInfoDTOS.stream()
                        .map(tagInfoDTO -> {
                            if (Objects.equals(TagTypeEnum.of(tagInfoDTO.getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG)) {
                                return Lists.newArrayList(String.format("wechat_tag_ids:'%s'", tagInfoDTO.getId()));
                            }
                            return Lists.newArrayList(String.format("customer_tag_ids:'%s'", tagInfoDTO.getId()));
                        })
                        .flatMap(Collection::stream)
                        .collect(Collectors.joining(query.getTagType() == 0 ? " AND " : " OR "))
                + ")");
    }

    /**
     * 查询成员的客户数量
     *
     * @return 数量
     */
    public List<FieldCountInfo> queryCustomerCount(WechatCustomerListQuery query) {
        if (CollectionUtils.isEmpty(query.getCountFields())) {
            PlatformLogUtil.logFail("求和的分组字段必填", LogListUtil.newArrayList(query));
            return Lists.newArrayList();
        }
        try {
            SearchParams searchParams = buildPageSearchParams(query, "duniqfield:external_user_id");
            // 查询子句
            List<String> queryList = buildCustomerRelationQueryList(query);
            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            // 过滤子句
            List<String> filterList = buildCustomerRelationFilterList(query);
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }
            // 统计子句
            if (!CollectionUtils.isEmpty(query.getCountFields())) {
                searchParams.setAggregates(buildAggregate(query.getCountFields()));
            }
            // 结果处理
            return getFacetCount(getSearcherClient().execute(searchParams));
        } catch (Exception e) {
            PlatformLogUtil.logException("OpenSearch查询客户数量异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return Lists.newArrayList();
        }
    }


    /**
     * 构建客户关系查询列表
     *
     * @param query 查询条件
     * @return 查询语句列表
     */
    private List<String> buildCustomerRelationQueryList(WechatCustomerListQuery query) {
        List<String> queryList = new ArrayList<>();
        if (StringUtils.hasText(query.getName())) {
            queryList.add(String.format("name:'%s'", query.getName()));
        }
        if (StringUtils.hasText(query.getUserId())) {
            queryList.add(String.format("follow_user_id:'%s'", query.getUserId()));
        }
        if (StringUtils.hasText(query.getUnionId())) {
            queryList.add(String.format("union_id:'%s'", query.getUnionId()));
        }
        if (StringUtils.hasText(query.getExternalUserId())) {
            queryList.add(String.format("external_user_id:'%s'", query.getExternalUserId()));
        }
        if (!CollectionUtils.isEmpty(query.getTagIdList()) && Objects.nonNull(query.getTagType()) && query.getTagType() != 2) {
            if (SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
                queryList.add("(" + query.getTagIdList().stream().map(tagId -> String.format("tag:'%s'", tagId)).collect(Collectors.joining(query.getTagType() == 0 ? " AND " : " OR ")) + ")");
            } else {
                queryList.add("(" +
                        query.getTagIdList().stream()
                                .map(tagId -> Lists.newArrayList(String.format("customer_tag_ids:'%s'", tagId), String.format("wechat_tag_ids:'%s'", tagId)))
                                .flatMap(Collection::stream)
                                .collect(Collectors.joining(query.getTagType() == 0 ? " AND " : " OR "))
                        + ")");
            }
        }
        if (Objects.nonNull(query.getDepartmentIdList())) {
            queryList.add("(" + query.getDepartmentIdList().stream().map(code -> String.format("department_id:'%d'", code)).collect(Collectors.joining(" OR ")) + ")");
        }
        if (StringUtils.hasText(query.getRemark())) {
            queryList.add(String.format("remark:'%s'", query.getRemark()));
        }
        return queryList;
    }

    /**
     * 构建客户关系过滤列表
     *
     * @param query 查询条件
     * @return 过滤语句列表
     */
    private List<String> buildCustomerRelationFilterList(WechatCustomerListQuery query) {
        List<String> filterList = new ArrayList<>();
        filterList.add("status=1");
        String corpId = Optional.ofNullable(String.valueOf(query.getSpaceId())).orElse(SpaceInfoThreadLocalUtils.getCorpId());
        if (StringUtils.hasText(corpId)) {
            filterList.add(String.format("corp_id=\"%s\"", corpId));
        }
        if (Objects.nonNull(query.getUserType())) {
            filterList.add(String.format("type='%d'", query.getUserType()));
        }
        if (Objects.nonNull(query.getGender())) {
            filterList.add(String.format("gender='%d'", query.getGender()));
        }
        if (Objects.nonNull(query.getTagType()) && query.getTagType() == 2) {
            if (SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
                filterList.add("fieldlen(tags)=0");
                filterList.add("fieldlen(wechat_tag_ids)=0");
            } else {
                filterList.add("fieldlen(customer_tag_ids)=0");
                filterList.add("fieldlen(wechat_tag_ids)=0");
            }
        }
        return filterList;
    }

    private FollowUserCustomerRelationInfo convertFollowUserCustomerRelationInfo(JSONObject o) {
        FollowUserCustomerRelationInfo customerRelation = new FollowUserCustomerRelationInfo();
        customerRelation.setUserId(o.getString("follow_user_id"));
        customerRelation.setName(o.getString("name"));
        customerRelation.setUserName(o.getString("user_name"));
        customerRelation.setStatus(o.getInteger("status"));
        customerRelation.setRemark(o.getString("remark"));
        customerRelation.setAddTime(o.getDate("add_time"));
        customerRelation.setUserAvatarUrl(o.getString("user_avatar"));
        customerRelation.setAvatarUrl(o.getString("avatar"));
        customerRelation.setExternalUserId(o.getString("external_user_id"));
        customerRelation.setCorpName(wxCorpStorage.getCorpName(SpaceInfoThreadLocalUtils.getCorpId()));
        String wechatTagIds = o.getString("wechat_tag_ids");
        if (StringUtils.hasLength(wechatTagIds)) {
            List<String> tagIdList = Lists.newArrayList(wechatTagIds.split("\t"));
            List<TagInfo> tagInfoList = tagIdList.stream()
                    .map(tagId -> {
                        TagInfo tagInfo = new TagInfo();
                        tagInfo.setId(tagId);
                        return tagInfo;
                    }).collect(Collectors.toList());
            customerRelation.setTagInfoList(tagInfoList);
        }
        return customerRelation;
    }

    private FollowUserInfo.CustomerRelation convert2CustomerRelation(JSONObject o) {
        FollowUserInfo.CustomerRelation customerRelation = new FollowUserInfo.CustomerRelation();
        customerRelation.setUserId(o.getString("follow_user_id"));
        customerRelation.setName(o.getString("user_name"));
        customerRelation.setStatus(o.getInteger("status"));
        customerRelation.setRemark(o.getString("remark"));
        customerRelation.setAddTime(o.getDate("add_time"));
        customerRelation.setAvatarUrl(o.getString("user_avatar"));
        customerRelation.setCorpName(wxCorpStorage.getCorpName(SpaceInfoThreadLocalUtils.getCorpId()));
        String wechatTagIds = o.getString("wechat_tag_ids");
        if (StringUtils.hasLength(wechatTagIds)) {
            List<String> tagIdList = Lists.newArrayList(wechatTagIds.split("\t"));
            List<TagInfo> tagInfoList = tagIdList.stream()
                    .map(tagId -> {
                        TagInfo tagInfo = new TagInfo();
                        tagInfo.setId(tagId);
                        return tagInfo;
                    }).collect(Collectors.toList());
            customerRelation.setTagInfoList(tagInfoList);
        }
        return customerRelation;
    }

    private String convert2WechatCustomerExternalUserId(JSONObject o) {
        return o.getString("external_user_id");
    }

    private Set<String> convert2WechatCustomerTagIds(JSONObject o) {
        Set<String> tagIdList = new HashSet<>();
        String customerTagIdsStr = o.getString("customer_tag_ids");
        String wechatTagIdsStr = o.getString("wechat_tag_ids");
        if (StringUtils.hasText(customerTagIdsStr)) {
            tagIdList.addAll(Arrays.asList(customerTagIdsStr.split("\t")));
        }
        if (StringUtils.hasText(wechatTagIdsStr)) {
            tagIdList.addAll(Arrays.asList(wechatTagIdsStr.split("\t")));
        }
        return tagIdList;
    }


    @Override
    protected String getAppName() {
        return "scrm_customer_relation_full";
    }

    @Override
    protected List<String> getFetchFields() {
        return Lists.newArrayList("id", "corp_id", "external_user_id", "union_id", "user_name", "name", "type", "corp_name", "gender", "avatar", "user_avatar", "add_time", "tags", "follow_user_id", "remark", "follow_user", "main_department", "status", "customer_tag_ids", "wechat_tag_ids");
    }
}
