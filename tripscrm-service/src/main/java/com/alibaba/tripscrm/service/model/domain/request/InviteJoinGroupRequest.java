package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/29 20:36
 **/
@Data
public class InviteJoinGroupRequest {

    /**
     * 企微成员Id（必须为在线状态）
     */
    private String userId;

    /**
     * 群聊Id
     */
    private String chatId;

    /**
     * 邀请的企微成员userId
     */
    private List<String> userIdList;

    /**
     * 邀请的企微客户externalUserId
     */
    private List<String> externalUserIdList;

}
