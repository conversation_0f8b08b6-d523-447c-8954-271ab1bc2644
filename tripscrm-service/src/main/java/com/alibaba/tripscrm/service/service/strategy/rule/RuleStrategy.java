package com.alibaba.tripscrm.service.service.strategy.rule;

import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.RuleExecuteContext;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:56
 */
public interface RuleStrategy {
    /**
     * 规则创建后执行的逻辑
     */
    void postCreate(RuleDO ruleDO);

    /**
     * 规则更新后执行的逻辑
     */
    void postUpdate(RuleDO oldRuleDO, RuleDO newRuleDO);

    /**
     * 规则删除后执行的逻辑
     */
    void postDelete(RuleDO ruleDO);

    /**
     * 规则更新后执行的逻辑
     */
    void postOnline(RuleDO ruleDO);

    /**
     * 规则删除后执行的逻辑
     */
    void postOffline(RuleDO ruleDO);

    /**
     * 规则执行
     */
    TripSCRMResult<Void> run(Long id, RuleExecuteContext context);

    /**
     * 规则执行
     */
    TripSCRMResult<Void> run(RuleDO ruleDO, RuleExecuteContext context);

    /**
     * 规则类型
     */
    RuleTypeEnum getRuleType();
}
