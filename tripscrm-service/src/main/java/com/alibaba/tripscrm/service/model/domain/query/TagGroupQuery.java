package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/12/10 16:10
 */
@Data
public class TagGroupQuery implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 标签列表
     */
    private List<Long> ids;

    /**
     * 名称
     */
    private String name;

    /**
     * 业务空间id
     */
    private List<Long> spaceIdList;

    /**
     * 标签类型
     */
    private List<Integer> tagType;

    /**
     * 标签来源
     */
    private List<String> sourceList;

    /**
     * 是否删除
     */
    private Byte deleted = 0;

    /**
     * 名称(用于模糊匹配)
     */
    private String nameLike;
}
