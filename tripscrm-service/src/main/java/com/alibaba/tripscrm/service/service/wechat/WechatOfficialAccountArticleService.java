package com.alibaba.tripscrm.service.service.wechat;


import com.alibaba.tripscrm.dal.model.domain.data.WechatOfficialAccountArticleDO;
import com.alibaba.tripzoo.proxy.model.WechatOfficialFreePublishBO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2024/3/26 15:02
 */
public interface WechatOfficialAccountArticleService {
    /**
     * 分页查询（每页20条）
     *
     * @param offset 起始记录，从0开始
     * @return 微信公众号文章列表
     */
    WechatOfficialFreePublishBO fetchFreePublishList(Integer offset);

    /**
     * 根据文章id查询
     */
    List<WechatOfficialAccountArticleDO> listByArticleId(String articleId);

    /**
     * 插入
     */
    Integer insert(WechatOfficialAccountArticleDO wechatOfficialAccountArticleDO);

    /**
     * 更新
     */
    Integer updateByArticleIdAndUrl(WechatOfficialAccountArticleDO wechatOfficialAccountArticleDO);

    /**
     * 根据条件查询
     */
    List<WechatOfficialAccountArticleDO> listByParam(Map<String, Object> paramMap);

    /**
     * 查询记录总数
     * @param paramMap
     * @return
     */
    int countByParam(Map<String, Object> paramMap);

    WechatOfficialAccountArticleDO getById(Long id);
}
