package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 微信客户关系信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FollowUserPageQuery extends BasePageRequest {
    /**
     * 企微成员列表 userIdList
     */
    private List<String> userIdList;

    /**
     * 成员名称/真名 模糊
     */
    private String nameLike;
    /**
     * 备注 模糊
     */
    private String remarkLike;
    /**
     * 名字or备注 模糊
     */
    private String nameOrRemarkLike;

    /**
     * 企微客户 unionId
     */
    private String unionId;

    /**
     * 企微客户 externalUserIdList
     */
    private List<String> externalUserIdList;

    /**
     * 机器人在线状态
     */
    private Integer robotStatus;

    /**
     * 好友关系状态
     */
    private Integer status;

    /**
     * 部门Id列表
     */
    private List<Integer> departmentIdList;
}
