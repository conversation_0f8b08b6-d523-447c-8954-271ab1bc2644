package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.tripscrm.service.model.vo.task.TaskMessageSceneVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-19 17:42:18
 */
public interface TaskMessageSceneService {
    /**
     * 根据id查询
     *
     * @param id 主键
     * @return TaskMessageSceneVO
     */
    TaskMessageSceneVO getById(Long id);

    /**
     * 根据任务类型查询
     *
     * @param taskMessageTypeId 消息类型Id
     * @return List<TaskMessageSceneVO>
     */
    List<TaskMessageSceneVO> listByTaskMessageTypeId(Long taskMessageTypeId);

    /**
     * 批量upsert
     *
     * @param list 新增的数据
     * @return 影响行数
     */
    Integer batchUpsert(List<TaskMessageSceneVO> list);

    /**
     * 修改数据
     *
     * @param taskMessageSceneVO 新的数据
     * @return 影响行数
     */
    Integer updateById(TaskMessageSceneVO taskMessageSceneVO);

    /**
     * 根据主键id列表批量删除
     *
     * @param ids 主键id列表
     * @return
     */
    Integer deleteByIds(List<Long> ids);}
