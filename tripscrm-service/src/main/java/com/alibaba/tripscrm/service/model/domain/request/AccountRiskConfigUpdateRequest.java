package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.model.domain.risk.RiskActionConfig;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionStatus;
import com.alibaba.tripscrm.service.model.domain.risk.RiskOfflineProtect;
import com.alibaba.tripscrm.service.model.domain.risk.RiskOnlineProtect;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 账号风控配置设置/修改
 *
 * <AUTHOR>
 * @date 2024/07/07
 */
@Data
public class AccountRiskConfigUpdateRequest {
    @ApiModelProperty(value = "风控配置id")
    private String riskId;
    @ApiModelProperty(value = "功能开关")
    private RiskActionStatus actionStatuses;
    @ApiModelProperty(value = "在线时长阈值（分钟）")
    private RiskOnlineProtect riskOnlineProtect;
    @ApiModelProperty(value = "掉线次数阈值")
    private RiskOfflineProtect riskOfflineProtect;
    @ApiModelProperty(value = "功能频率限制")
    private RiskActionConfig actionConfig;
    @ApiModelProperty(value = "是否强制更新")
    private Boolean force = false;
}