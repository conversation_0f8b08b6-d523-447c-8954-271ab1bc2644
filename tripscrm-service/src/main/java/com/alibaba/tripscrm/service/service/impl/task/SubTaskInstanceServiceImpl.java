package com.alibaba.tripscrm.service.service.impl.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.common.util.IpUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.SubTaskInstanceMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.SubTaskInstanceQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.convert.SubTaskInstanceConverter;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceExecuteStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceSaveVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskInstanceExecuteResultVO;
import com.alibaba.tripscrm.service.model.dto.task.TaskSingleDataExecuteRetryInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class SubTaskInstanceServiceImpl implements SubTaskInstanceService {

    @Resource
    private SubTaskInstanceMapper subTaskInstanceMapper;
    @Resource
    private TaskInstanceService taskInstanceService;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private TaskExecuteRecordService taskExecuteRecordService;
    @Resource
    private TaskService taskService;
    @Resource
    private MetaqProducer metaqProducer;

    @Override
    public Long add(SubTaskInstanceSaveVO request) {
        SubTaskInstanceDO subTaskInstanceDO = SubTaskInstanceConverter.saveVo2Do(
                request, () -> taskInstanceService.queryById(request.getMainTaskInstanceId())
        );
        subTaskInstanceMapper.insert(subTaskInstanceDO);

        return subTaskInstanceDO.getId();
    }

    @Override
    public SubTaskInstanceDO queryById(Long id) {
        return subTaskInstanceMapper.selectByPrimaryKey(id);
    }

    @Override
    @Cacheable(key = "'sub_task_instance_info:id_' + #id", value = "oneMinutesAnd128MaximumSizeCacheManager", unless = "#result == null")
    public SubTaskInstanceDO queryByIdWithCache(Long id) {
        return queryById(id);
    }

    @Override
    public List<SubTaskInstanceDO> list(SubTaskInstanceQueryRequest request) {
        if (!NumberUtils.validLong(request.getTaskId()) && !NumberUtils.validLong(request.getMainTaskInstanceId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        SubTaskInstanceQuery queryParam = SubTaskInstanceConverter.queryRequest2Query(request);
        return subTaskInstanceMapper.selectByCondition(queryParam);
    }

    @Override
    @TairLock(value = "'updateSubTaskStatus_' + #id", waitMilliseconds = 2000)
    public Boolean updateSubTaskStatus(Long id, Boolean isRetry, TaskInstanceExecuteStatusEnum taskInstanceExecuteStatusEnum) {
        if (!NumberUtils.validLong(id) || Objects.isNull(taskInstanceExecuteStatusEnum) || Objects.isNull(taskInstanceExecuteStatusEnum.getTaskInstanceStatusEnum())) {
            PlatformLogUtil.logFail("更新子任务实例状态，参数非法", LogListUtil.newArrayList(id, isRetry, taskInstanceExecuteStatusEnum));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TaskInstanceStatusEnum taskInstanceStatus = taskInstanceExecuteStatusEnum.getTaskInstanceStatusEnum();
        SubTaskInstanceDO subTaskInstanceDO = queryById(id);
        if (Objects.isNull(subTaskInstanceDO)) {
            PlatformLogUtil.logFail("更新子任务实例状态，子任务实例不存在", LogListUtil.newArrayList(id, isRetry, taskInstanceStatus));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_SUB_TASK_INSTANCE_DATA);
        }

        TaskInstanceStatusEnum currentStatus = TaskInstanceStatusEnum.getByStatus(subTaskInstanceDO.getStatus());
        if (Objects.equals(taskInstanceStatus, currentStatus)) {
            PlatformLogUtil.logInfo("更新子任务实例状态，要修改的状态与db中相同，无需更新", LogListUtil.newArrayList(id, isRetry, taskInstanceStatus));
            return true;
        }

        PlatformLogUtil.logInfo("更新子任务实例状态", LogListUtil.newArrayList(id, isRetry, taskInstanceStatus));
        SubTaskInstanceDO updateDO = new SubTaskInstanceDO();
        updateDO.setId(id);
        updateDO.setStatus(taskInstanceStatus.getStatus());
        updateDO.setIpAddress(IpUtil.getIPV4Address());

        boolean result;
        switch (taskInstanceStatus) {
            case RUNNING:
                // 只有待运行才能修改为运行中
                if (!Lists.newArrayList(TaskInstanceStatusEnum.READY).contains(currentStatus)) {
                    throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
                }
                updateDO.setStartTime(new Date());
                result = subTaskInstanceMapper.updateSelectiveByPrimaryKey(updateDO) >= 1;
                break;
            case RUN_SUCCESS:
                if (isRetry && !Lists.newArrayList(TaskInstanceStatusEnum.SUBMIT_SUCCESS).contains(currentStatus)) {
                    PlatformLogUtil.logFail("更新子任务实例状态，重试无法更新非已提交完成的任务实例为执行成功/失败", LogListUtil.newArrayList(id, isRetry, taskInstanceStatus));
                    result = false;
                    break;
                }

                // 只有待运行和运行中才能修改为成功或失败
                if (!Lists.newArrayList(TaskInstanceStatusEnum.RUNNING, TaskInstanceStatusEnum.READY, TaskInstanceStatusEnum.SUBMIT_SUCCESS).contains(currentStatus)) {
                    throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
                }

                // 如果有数据正在重试中，还不能直接更新为执行成功，需要先更新为提交成功
                // 获取任务状态
                String taskStatus = getStringFromTair(TairConstant.LDB_TASK_STATUS_PREFIX + subTaskInstanceDO.getMainTaskId());
                if (!StringUtils.hasText(taskStatus)) {
                    PlatformLogUtil.logInfo("更新子任务实例状态，从缓存中查询任务状态失败", LogListUtil.newArrayList(subTaskInstanceDO.getMainTaskId()));
                    TaskInfoDO taskInfoDO = taskService.queryTaskById(subTaskInstanceDO.getMainTaskId());
                    taskStatus = taskInfoDO.getStatus();
                }

                // 任务已下线/下线中
                if (Lists.newArrayList(TaskStatusEnum.EDITING, TaskStatusEnum.OFFLINE_IN_PROGRESS).contains(TaskStatusEnum.getByStatus(taskStatus))) {
                    PlatformLogUtil.logInfo("更新子任务实例状态，任务已下线/下线中", LogListUtil.newArrayList(id, isRetry, taskInstanceStatus));
                } else {
                    Integer retryRecordCount = getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_EXECUTE_RETRY_COUNT_PREFIX + id);
                    PlatformLogUtil.logInfo("尝试更新子任务实例状态为执行成功，查询重试中数量", LogListUtil.newArrayList(id, isRetry, retryRecordCount, taskInstanceStatus));
                    if (Optional.ofNullable(retryRecordCount).orElse(0) > 0) {
                        PlatformLogUtil.logInfo("更新子任务实例状态，重试中数量为0，直接更新状态为执行完成", LogListUtil.newArrayList(id, isRetry, retryRecordCount, taskInstanceStatus));
                        updateDO.setStatus(TaskInstanceStatusEnum.SUBMIT_SUCCESS.getStatus());
                    }
                }

                result = subTaskInstanceMapper.updateSelectiveByPrimaryKey(updateDO) >= 1;
                break;
            case RUN_FAIL:
                if (isRetry) {
                    PlatformLogUtil.logFail("更新子任务实例状态，重试无法更新任务实例状态为执行失败", LogListUtil.newArrayList(id, isRetry, taskInstanceStatus));
                    result = false;
                    break;
                }

                // 只有待运行和运行中才能修改为成功或失败
                if (!Lists.newArrayList(TaskInstanceStatusEnum.RUNNING, TaskInstanceStatusEnum.READY, TaskInstanceStatusEnum.SUBMIT_SUCCESS).contains(currentStatus)) {
                    throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
                }

                updateDO.setEndTime(new Date());
                result = subTaskInstanceMapper.updateSelectiveByPrimaryKey(updateDO) >= 1;
                break;
            case TIMEOUT:
                // 只有运行中和提交成功才能修改为超时
                if (!Lists.newArrayList(TaskInstanceStatusEnum.RUNNING, TaskInstanceStatusEnum.SUBMIT_SUCCESS).contains(currentStatus)) {
                    throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
                }
                updateDO.setEndTime(new Date());
                result = subTaskInstanceMapper.updateSelectiveByPrimaryKey(updateDO) >= 1;
                break;
            default:
                throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
        }

        if (!result) {
            return result;
        }

        if (!Lists.newArrayList(TaskInstanceStatusEnum.RUN_SUCCESS, TaskInstanceStatusEnum.RUN_FAIL, TaskInstanceStatusEnum.TIMEOUT).contains(taskInstanceStatus)) {
            return result;
        }

        ldbTairManager.put(TairConstant.SUB_TASK_INSTANCE_STATUS_PREFIX + id, updateDO.getStatus(), TairConstant.EXPIRE_TIME_DAY_UNIT);
        String extraInfo = subTaskInstanceDO.getExtInfo();
        if (!StringUtils.hasText(extraInfo)) {
            extraInfo = "{}";
        }
        JSONObject extraInfoJson = JSONObject.parseObject(extraInfo);
        JSONObject executeResultFromTair = getExecuteResultFromTair(id);
        extraInfoJson.putAll(executeResultFromTair);
        updateDO.setExtInfo(extraInfoJson.toJSONString());
        // 这里没有考虑长尾导致的数据不一致情况，上线后先看吧，不行换方式，数据倒不用那么精确
        subTaskInstanceMapper.updateSelectiveByPrimaryKey(updateDO);
        JSONObject msg = new JSONObject();
        msg.put("subTaskInstanceId", id);
        metaqProducer.send(MQEnum.SUB_TASK_INSTANCE_EXECUTE_RESULT_SYNC, String.valueOf(id), "", msg.toJSONString(), MetaQDeleyLevel.LEVEL_16.getLevel());
        return result;
    }

    public JSONObject getExecuteResultFromTair(Long id) {
        JSONObject extraInfoJson = new JSONObject();
        Integer executeSuccessCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_EXECUTE_SUCCESS_COUNT_PREFIX + id)).orElse(0);
        extraInfoJson.put(TaskSingleDataExecuteResultEnum.SUCCESS.getDesc(), executeSuccessCount);

        Integer executeFailCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_EXECUTE_FAIL_COUNT_PREFIX + id)).orElse(0);
        Integer executeRetryCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_EXECUTE_RETRY_COUNT_PREFIX + id)).orElse(0);
        Integer asyncExecuteSuccessCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_SUCCESS_COUNT_PREFIX + id)).orElse(0);
        Integer asyncExecuteFailCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_FAIL_COUNT_PREFIX + id)).orElse(0);
        extraInfoJson.put(TaskSingleDataExecuteResultEnum.FAIL.getDesc(), executeFailCount + executeRetryCount);
        extraInfoJson.put(TaskSingleDataExecuteResultEnum.ASYNC_SUCCESS.getDesc(), asyncExecuteSuccessCount);
        extraInfoJson.put(TaskSingleDataExecuteResultEnum.ASYNC_FAIL.getDesc(), asyncExecuteFailCount);
        return extraInfoJson;
    }

    @Override
    public TaskInstanceExecuteResultVO getExecuteResult(SubTaskInstanceDO subTaskInstanceDO) {
        if (Objects.isNull(subTaskInstanceDO) || !NumberUtils.validLong(subTaskInstanceDO.getId())) {
            return null;
        }

        TaskInstanceExecuteResultVO result = new TaskInstanceExecuteResultVO();
        String extraInfo = subTaskInstanceDO.getExtInfo();
        if (!StringUtils.hasText(extraInfo)) {
            extraInfo = "{}";
        }
        JSONObject extraInfoJson = JSONObject.parseObject(extraInfo);
        Integer dataCount = MapUtils.getIntValue(extraInfoJson, TaskConstant.SUB_TASK_EXT_DATA_COUNT);
        Integer executeSuccessCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_EXECUTE_SUCCESS_COUNT_PREFIX + subTaskInstanceDO.getId())).orElse(MapUtils.getIntValue(extraInfoJson, TaskSingleDataExecuteResultEnum.SUCCESS.getDesc(), 0));
        Integer executeFailCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_EXECUTE_FAIL_COUNT_PREFIX + subTaskInstanceDO.getId())).orElse(MapUtils.getIntValue(extraInfoJson, TaskSingleDataExecuteResultEnum.FAIL.getDesc(), 0));
        Integer executeRetryCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_EXECUTE_RETRY_COUNT_PREFIX + subTaskInstanceDO.getId())).orElse(MapUtils.getIntValue(extraInfoJson, TaskSingleDataExecuteResultEnum.WAIT_RETRY.getDesc(), 0));
        Integer asyncExecuteSuccessCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_SUCCESS_COUNT_PREFIX + subTaskInstanceDO.getId())).orElse(MapUtils.getIntValue(extraInfoJson, TaskSingleDataExecuteResultEnum.ASYNC_SUCCESS.getDesc(), 0));
        Integer asyncExecuteFailCount = Optional.ofNullable(getIntegerFromTair(TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_FAIL_COUNT_PREFIX + subTaskInstanceDO.getId())).orElse(MapUtils.getIntValue(extraInfoJson, TaskSingleDataExecuteResultEnum.ASYNC_FAIL.getDesc(), 0));
        result.setScheduleTime(subTaskInstanceDO.getStartTime());
        result.setStatus(subTaskInstanceDO.getStatus());
        result.setAll(dataCount);
        result.setSuccess(executeSuccessCount);
        result.setFail(executeFailCount);
        result.setWaitRetry(executeRetryCount);
        result.setAsyncSuccess(asyncExecuteSuccessCount);
        result.setAsyncFail(asyncExecuteFailCount);
        return result;
    }

    public MetaQDeleyLevel getRetryDelayLevel(TaskExecuteContext context, TripSCRMErrorCode errorCode) {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        if (context.getTestFlag() || isStreamTask) {
            return null;
        }

        if (Objects.isNull(context.getTaskInfoDOSnapshot())) {
            PlatformLogUtil.logInfo("子任务执行，对每条数据执行业务逻辑，任务快照未赋值");
            return null;
        }

        AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(TaskType.getByCode(context.getTaskInfoDOSnapshot().getType()));
        if (!taskExecutor.supportRetry(errorCode)) {
            PlatformLogUtil.logInfo("子任务执行，对每条数据执行业务逻辑，获取重试延迟时间，不支持重试的错误类型", LogListUtil.newArrayList(errorCode));
            return null;
        }

        MetaQDeleyLevel retryDelayLevel = getRetryDelayLevel(context.getTaskDataVO().getData().get(0).getRetryCount());
        if (Objects.isNull(retryDelayLevel)) {
            return null;
        }

        String[] workTimeWindow = taskService.checkAndGetWorkTimeWindow(context.getTaskInfoDOSnapshot());
        // 留1分钟gap
        LocalTime futureTime = LocalTime.now().plusSeconds(retryDelayLevel.getLevel() + 60);
        LocalTime workWindowEndTime = LocalTime.parse(workTimeWindow[1], DateTimeFormatter.ofPattern("HH:mm"));
        // 超出执行时间窗口了
        if (!futureTime.isBefore(workWindowEndTime)) {
            PlatformLogUtil.logInfo("子任务执行，对每条数据执行业务逻辑，获取重试延迟时间，超出执行时间窗口", LogListUtil.newArrayList(futureTime, workWindowEndTime, retryDelayLevel));
            return null;
        }

        PlatformLogUtil.logInfo("子任务执行，对每条数据执行业务逻辑，获取重试延迟时间", LogListUtil.newArrayList(retryDelayLevel));
        return retryDelayLevel;
    }

    @Override
    @TairLock(value = "'updateSubTaskInstanceExecuteResult' + #dataBodyVO.getRecordId()", waitMilliseconds = 2000)
    public void updateSubTaskInstanceExecuteResult(TaskExecuteContext context, TaskDataVO.DataBodyVO dataBodyVO) {
        TaskExecuteRecordBO taskExecuteRecordBO = getTaskExecuteRecordBO(context, dataBodyVO);
        JSONObject extraInfo = Optional.ofNullable(taskExecuteRecordBO.getExtraInfo()).orElse(new JSONObject());
        extraInfo.put("errorCode", dataBodyVO.getResultErrorCode().getCode());
        extraInfo.put("taskExecuteResult", context.getResult());
        taskExecuteRecordBO.setExtraInfo(extraInfo);

        // 流式，没有子任务实例
        boolean updateExecuteResult = NumberUtils.validLong(context.getInstanceId());
        switch (dataBodyVO.getResultErrorCode()) {
            case OK:
                taskExecuteRecordBO.setStatusEnum(TaskSingleDataExecuteResultEnum.SUCCESS);
                if (!updateExecuteResult) {
                    break;
                }
                ldbTairManager.incr(TairConstant.SUB_TASK_INSTANCE_EXECUTE_SUCCESS_COUNT_PREFIX + context.getInstanceId(), 1, 0, 86400);
                if (context.getRetry()) {
                    ldbTairManager.decr(TairConstant.SUB_TASK_INSTANCE_EXECUTE_RETRY_COUNT_PREFIX + context.getInstanceId(), 1, 0, 3, 86400);
                }
                break;
            case FAIL_AND_RETRY:
                taskExecuteRecordBO.setStatusEnum(TaskSingleDataExecuteResultEnum.WAIT_RETRY);
                extraInfo.put("retryCount", dataBodyVO.getRetryCount());
                // 首次重试
                if (updateExecuteResult && !context.getRetry()) {
                    ldbTairManager.incr(TairConstant.SUB_TASK_INSTANCE_EXECUTE_RETRY_COUNT_PREFIX + context.getInstanceId(), 1, 0, 86400);
                }

                TaskSingleDataExecuteRetryInfoDTO taskSingleDataExecuteRetryInfoDTO = new TaskSingleDataExecuteRetryInfoDTO();
                taskSingleDataExecuteRetryInfoDTO.setRecordId(taskExecuteRecordBO.getRecordId());
                metaqProducer.send(MQEnum.TASK_SINGLE_DATA_EXECUTE_RETRY, String.valueOf(taskExecuteRecordBO.getRecordId()), "", JSONObject.toJSONString(taskSingleDataExecuteRetryInfoDTO), dataBodyVO.getNextRetryDelayLevel().getLevel());
                break;
            default:
                taskExecuteRecordBO.setStatusEnum(TaskSingleDataExecuteResultEnum.FAIL);
                if (!updateExecuteResult) {
                    break;
                }

                ldbTairManager.incr(TairConstant.SUB_TASK_INSTANCE_EXECUTE_FAIL_COUNT_PREFIX + context.getInstanceId(), 1, 0, 86400);
                if (context.getRetry()) {
                    ldbTairManager.decr(TairConstant.SUB_TASK_INSTANCE_EXECUTE_RETRY_COUNT_PREFIX + context.getInstanceId(), 1, 0, 3, 86400);
                }
                break;
        }

        PlatformLogUtil.logInfo("子任务执行，更新任务执行明细", LogListUtil.newArrayList(taskExecuteRecordBO));
        taskExecuteRecordService.upsert(taskExecuteRecordBO);
    }

    private TaskExecuteRecordBO getTaskExecuteRecordBO(TaskExecuteContext context, TaskDataVO.DataBodyVO dataBodyVO) {
        TaskExecuteRecordBO taskExecuteRecordBO = taskExecuteRecordService.queryByRecordId(dataBodyVO.getRecordId());
        if (Objects.nonNull(taskExecuteRecordBO)) {
            return taskExecuteRecordBO;
        }

        taskExecuteRecordBO = new TaskExecuteRecordBO();
        taskExecuteRecordBO.setRecordId(dataBodyVO.getRecordId());
        taskExecuteRecordBO.setTaskId(context.getTaskId());
        Long timeStamp = System.currentTimeMillis();
        taskExecuteRecordBO.setGmtCreate(timeStamp);
        taskExecuteRecordBO.setGmtModified(timeStamp);
        taskExecuteRecordBO.setMainTaskInstanceId(context.getMainTaskInstanceId());
        taskExecuteRecordBO.setSubTaskInstanceId(context.getInstanceId());
        taskExecuteRecordBO.setTargetId(dataBodyVO.getTargetId());
        taskExecuteRecordBO.setTargetTypeEnum(ActivityTargetTypeEnum.codeOf(dataBodyVO.getTargetType()));
        JSONObject executeInfo = new JSONObject();
        executeInfo.put("triggerType", context.getTriggerType().getCode());
        if (StringUtils.hasText(dataBodyVO.getExtInfo())) {
            executeInfo.put("dataExtInfo", JSONObject.parse(dataBodyVO.getExtInfo()));
        } else {
            executeInfo.put("dataExtInfo", new JSONObject());
        }
        if (MapUtils.isNotEmpty(context.getExtInfo())) {
            executeInfo.put("contextExtInfo", context.getExtInfo());
        } else {
            executeInfo.put("contextExtInfo", new HashMap<String, Object>());
        }
        taskExecuteRecordBO.setExecuteInfo(executeInfo);
        return taskExecuteRecordBO;
    }

    private MetaQDeleyLevel getRetryDelayLevel(Integer retryCount) {
        if (retryCount >= 16) {
            PlatformLogUtil.logInfo("子任务执行，对每条数据执行业务逻辑，获取重试延迟时间，重试次数已达上限", LogListUtil.newArrayList(retryCount));
            return null;
        }

        switch (retryCount) {
            case 0:
                return MetaQDeleyLevel.LEVEL_6;
            case 1:
                return MetaQDeleyLevel.LEVEL_8;
            case 2:
                return MetaQDeleyLevel.LEVEL_12;
            case 3:
                return MetaQDeleyLevel.LEVEL_14;
            case 4:
                return MetaQDeleyLevel.LEVEL_15;
            default:
                return MetaQDeleyLevel.LEVEL_16;
        }
    }

    @Override
    public Integer update(SubTaskInstanceSaveVO request) {
        SubTaskInstanceDO subTaskInstanceDO = SubTaskInstanceConverter.saveVo2Do(
                request, () -> taskInstanceService.queryById(request.getMainTaskInstanceId())
        );
        return subTaskInstanceMapper.updateSelectiveByPrimaryKey(subTaskInstanceDO);
    }

    @Override
    public Integer deleteById(Long id) {
        return subTaskInstanceMapper.deleteByPrimaryKey(id);
    }

    @Override
    public Integer updateById(SubTaskInstanceDO subTaskInstanceDO) {
        return subTaskInstanceMapper.updateSelectiveByPrimaryKey(subTaskInstanceDO);
    }

    private String getStringFromTair(String key) {
        Object o = ldbTairManager.get(key);
        if (Objects.isNull(o)) {
            return null;
        }

        return String.valueOf(o);
    }

    private Integer getIntegerFromTair(String key) {
        Object o = ldbTairManager.get(key);
        if (Objects.isNull(o)) {
            return null;
        }

        return Integer.parseInt(String.valueOf(o));
    }
}
