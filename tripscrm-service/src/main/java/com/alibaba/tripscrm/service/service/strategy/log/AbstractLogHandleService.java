package com.alibaba.tripscrm.service.service.strategy.log;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.ManagementGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.system.LogFieldEnum;
import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.domain.log.LogContentBO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.common.ManagementGroupService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/17 13:50
 **/
@Service
public abstract class AbstractLogHandleService<T extends LogContentBO> {

    @Resource
    private ManagementGroupService managementGroupService;
    @Resource
    private TagInfoService tagInfoService;
    @Resource
    private MaterialService materialService;
    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private AccountService accountService;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private WechatCustomerService wechatCustomerService;

    /**
     * 服务处理类型
     * @return 日志展示类型
     */
    public abstract LogShowTypeEnum handleType();

    /**
     * 获取日志的可视化内容
     * @param sopLogContentBO 日志内容对象
     * @return 可视化内容
     */
    public String getLogContentStr(T sopLogContentBO) {
        return Optional.ofNullable(sopLogContentBO.getNameDesc()).orElse("") + "【%s】";
    }

    /**
     * 获取日志内容
     * @param sopLogContentBO 日志内容对象
     * @return 可视化内容
     */
    public String getLogContent(T sopLogContentBO) {
        // 填充信息
        fillLogContentInfo(sopLogContentBO);
        // 替换字符
        LogFieldEnum sopLogFieldEnum = LogFieldEnum.codeOf(sopLogContentBO.getName());
        if (sopLogFieldEnum != null && sopLogFieldEnum.getResourceTypeEnum() != null) {
            replaceLogContentInfo(sopLogContentBO, sopLogFieldEnum.getResourceTypeEnum());
        }
        // 获取日志内容
        return getLogContentStr(sopLogContentBO);
    }

    /**
     * 替换日志内容信息
     * @param sopLogContentBO sop日志内容BO对象
     * @param resourceTypeEnum 资源类型枚举
     */
    protected abstract void replaceLogContentInfo(T sopLogContentBO, ResourceTypeEnum resourceTypeEnum);

    /**
     * 填充日志对象内容信息
     * @param sopLogContentBO 日志内容对象
     */
    public void fillLogContentInfo(T sopLogContentBO) {
        LogFieldEnum sopLogFieldEnum = LogFieldEnum.codeOf(sopLogContentBO.getName());
        if (sopLogFieldEnum == null) {
            return ;
        }
        sopLogContentBO.setNameDesc(sopLogFieldEnum.getDesc());
    }

    /**
     * 获取成员名称
     * @param userId 用户id
     * @return 成员名称
     */
    protected String getUserName(String userId) {
        return Arrays.stream(JSONObject.parseObject(userId, String.class).split(",")).map(memberId -> {
            User user = Optional.ofNullable(accountService.getUserByAccountId(memberId)).orElse(new User());
            return user.getUserName();
        }).collect(Collectors.joining(","));
    }

    /**
     * 获取群聊名称列表
     * @param chatIdStr 群聊id字符串
     * @return 名称列表
     */
    protected List<String> queryGroupNameList(String chatIdStr) {
        if (StringUtils.isBlank(chatIdStr)) {
            return null;
        }
        List<String> chatIdList = JSONObject.parseObject(chatIdStr, new TypeReference<List<String>>() {
        });
        return queryGroupNameList(chatIdList);
    }

    /**
     * 获取群聊名称列表
     * @param chatIdList 群聊id列表
     * @return 名称列表
     */
    protected List<String> queryGroupNameList(List<String> chatIdList) {
        if (CollectionUtils.isEmpty(chatIdList)) {
            return Lists.newArrayList();
        }
        List<WechatGroupVO> wechatGroupVOList = wechatGroupService.listByChatIdList(chatIdList, false);
        if (CollectionUtils.isEmpty(wechatGroupVOList)) {
            return null;
        }
        return wechatGroupVOList.stream().map(WechatGroupVO::getName).collect(Collectors.toList());
    }

    /**
     * 查询标签名称列表
     * @param tagIdStr 标签Id
     * @return 名称列表
     */
    protected List<String> queryTagNameList(String tagIdStr) {
        if (StringUtils.isBlank(tagIdStr)) {
            return null;
        }
        List<String> tagIdList = JSONObject.parseObject(tagIdStr, new TypeReference<List<String>>() {
        });
        if (CollectionUtils.isEmpty(tagIdList)) {
            return null;
        }
        List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(tagIdList);
        if (CollectionUtils.isEmpty(tagInfoList)) {
            return null;
        }
        return tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList());
    }

    /**
     * 获取素材名称
     * @param materialId 获取素材名称
     * @return 素材名称
     */
    protected String queryMaterialName(String materialId) {
        if (StringUtils.isBlank(materialId) || !StringUtils.isNumeric(materialId)) {
            return null;
        }
        MaterailInfoDO materailInfoDO = materialService.queryById(Long.valueOf(materialId));
        if (materailInfoDO == null) {
            return null;
        }
        return materailInfoDO.getName();
    }

    /**
     * 获取管理组名称
     * @param managementGroupId 管理组Id
     * @return 管理组名称
     */
    protected String queryManagementGroupName(String managementGroupId) {
        if (StringUtils.isBlank(managementGroupId) || !StringUtils.isNumeric(managementGroupId)) {
            return null;
        }
        ManagementGroupDO managementGroupDO = managementGroupService.queryById(Long.valueOf(managementGroupId));
        if (managementGroupDO == null) {
            return null;
        }
        return managementGroupDO.getName();
    }

    /**
     * 获取企微成员名称
     * @param userIdList 成员id列表
     * @return 成员名称列表
     */
    protected List<String> queryUserName(List<String> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Lists.newArrayList();
        }
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(userIdList);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return Lists.newArrayList();
        }
        return wechatUserList.stream().map(WechatUserDTO::getName).collect(Collectors.toList());
    }

    /**
     * 获取企微客户名称
     * @param externalUserIdList 客户id列表
     * @return 客户名称列表
     */
    protected List<String> queryCustomerName(List<String> externalUserIdList) {
        if (CollectionUtils.isEmpty(externalUserIdList)) {
            return Lists.newArrayList();
        }
        List<WechatCustomerVO> wechatCustomerList = wechatCustomerService.listByExternalUserIdList(externalUserIdList);
        if (CollectionUtils.isEmpty(wechatCustomerList)) {
            return Lists.newArrayList();
        }
        return wechatCustomerList.stream().map(WechatCustomerVO::getName).collect(Collectors.toList());
    }

}
