package com.alibaba.tripscrm.service.service.risk.config;

import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.model.domain.request.AccountRiskConfigUpdateRequest;
import com.alibaba.tripscrm.service.model.domain.risk.*;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigDetailVO;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigVO;
import com.alibaba.tripscrm.service.service.risk.RiskConfigHandler;
import com.alibaba.tripscrm.service.service.risk.status.RiskActionStatusController;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 行动项开关 配置处理器
 * 注意：配置态的状态，只有三个：可用、挂起、脱离
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class RiskActionStatusConfigHandler extends RiskConfigHandler {
    @Resource
    private RiskActionStatusController riskActionStatusController;

    @Override
    public void buildListShow(UserRiskSchema schema, AccountRiskConfigVO riskConfig) {
        if (schema != null) {
            List<RiskActionStatus> actionStatuses = schema.getActionStatuses();
            List<String> actionCodes = Arrays.stream(RiskActionEnum.values()).map(RiskActionEnum::getActionCode).collect(Collectors.toList());
            if (actionStatuses == null) {
                riskConfig.setActionStatuses(RiskActionEnum.values().length + "个行动项风控中");
            } else {
                int enableCount = RiskActionEnum.values().length - (int) actionStatuses.stream().filter(x -> actionCodes.contains(x.getActionCode()) && !x.getStatusCode().equals(RiskStatusEnum.NORMAL.getCode())).count();
                riskConfig.setActionStatuses(enableCount + "个行动项风控中");
            }
        } else {
            riskConfig.setActionStatuses("遵循默认风控");
        }
    }

    @Override
    public void buildDetailShow(UserRiskSchema schema, AccountRiskConfigDetailVO result) {
        // 设置功能开关，加载全量开关，设置已设置的配置
        List<RiskActionStatus> actionStatuses = new ArrayList<>();
        if (schema != null && schema.getActionStatuses() != null) {
            actionStatuses = schema.getActionStatuses();
        }
        List<RiskActionStatus> showActionStatuses = new ArrayList<>(RiskActionEnum.values().length);
        Map<String, RiskActionStatus> actionStatusMap = actionStatuses.stream().collect(Collectors.toMap(RiskActionStatus::getActionCode, Function.identity()));
        for (RiskActionEnum riskAction : RiskActionEnum.values()) {
            RiskActionStatus riskActionStatus = actionStatusMap.get(riskAction.getActionCode());
            if (riskActionStatus != null) {
                showActionStatuses.add(new RiskActionStatus(riskAction.getActionCode(), riskAction.getName(), riskActionStatus.getStatusCode()));
            } else {
                showActionStatuses.add(new RiskActionStatus(riskAction.getActionCode(), riskAction.getName(), RiskStatusEnum.NORMAL.getCode()));
            }
        }
        result.setActionStatuses(showActionStatuses);
    }

    @Override
    public void updateConfig(UserRiskSchema schema, AccountRiskConfigUpdateRequest param, AccountRiskConfigVO.RiskIdInfo riskIdInfo) {
        // 设置功能开关
        List<RiskActionStatus> actionStatuses = schema.getActionStatuses();
        if (param.getActionStatuses() != null) {
            RiskActionStatus newActionStatuses = param.getActionStatuses();
            Map<String, RiskActionStatus> actionStatusMap = actionStatuses != null ? actionStatuses.stream().collect(Collectors.toMap(RiskActionStatus::getActionCode, Function.identity())) : new HashMap<>();
            actionStatusMap.put(newActionStatuses.getActionCode(), newActionStatuses);
            RiskActionEnum riskAction = RiskActionEnum.parse(newActionStatuses.getActionCode());
            if (riskAction != null) {
                switch (riskIdInfo.getRuleType()) {
                    case ROBOT_RISK:
                        // 对机器人操作
                        switch (RiskStatusEnum.of(newActionStatuses.getStatusCode())) {
                            case HOLD:
                                riskActionStatusController.toHold(riskIdInfo.getCorpId(), riskIdInfo.getUserId(), riskAction);
                                break;
                            case NORMAL:
                                riskActionStatusController.toNormal(riskIdInfo.getCorpId(), riskIdInfo.getUserId(), riskAction, null);
                                break;
                            case ESCAPE:
                                riskActionStatusController.toEscape(riskIdInfo.getCorpId(), riskIdInfo.getUserId(), riskAction);
                                break;
                        }
                        break;
                    case ROBOT_TYPE_RISK:
                        // 对机器人类型操作
                        switch (RiskStatusEnum.of(newActionStatuses.getStatusCode())) {
                            case HOLD:
                                riskActionStatusController.toHoldWithRobotType(riskIdInfo.getRobotType().getCode(), riskAction);
                                break;
                            case NORMAL:
                                riskActionStatusController.toNormalWithRobotType(riskIdInfo.getRobotType().getCode(), riskAction);
                                break;
                            case ESCAPE:
                                riskActionStatusController.toEscapeWithRobotType(riskIdInfo.getRobotType().getCode(), riskAction);
                                break;
                        }
                        break;
                }
            }
            actionStatuses = new ArrayList<>(actionStatusMap.values());
        }
        schema.setActionStatuses(actionStatuses);
    }
}

