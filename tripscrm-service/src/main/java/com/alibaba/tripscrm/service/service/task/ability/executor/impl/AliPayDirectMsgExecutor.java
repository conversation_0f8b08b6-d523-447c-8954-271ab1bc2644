package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.alipay.AliPayMsgManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.alipay.msg.AliPayDirectMsgRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.CrowdTypeEnum;
import com.taobao.csp.courier.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 支付宝定向消息任务执行器
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@Service
public class AliPayDirectMsgExecutor extends AbstractTaskExecutor {
    @Autowired
    protected AliPayMsgManager aliPayMsgManager;
    @Autowired
    protected LdbTairManager ldbTairManager;
    @Autowired
    protected MetaqProducer metaqProducer;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        // 从todoTaskVO中取出任务配置
        List<String> alipayGroupTemplateList;
        String alipayCrowdId;
        String crowdType;
        Date pushTime;
        String taskName;
        try {
            alipayGroupTemplateList = Arrays.asList(getFinalTargetId(context, taskDataBody).split(","));
            JSONObject extInfo = JSONObject.parseObject(taskDataBody.getExtInfo());
            alipayCrowdId = extInfo.getString(TaskConstant.ALIPAY_CROWD_ID);
            crowdType = extInfo.getString(TaskConstant.ALIPAY_CROWD_TYPE);
            Long pushTimeStamp = Long.valueOf(extInfo.getString(TaskConstant.ALIPAY_TRIGGER_TIME));
            taskName = taskInfoDO.getName();
            pushTime = new Date(pushTimeStamp);
        } catch (Exception e) {
            PlatformLogUtil.logException("支付宝定向消息任务执行器执行出错，从todoTaskVO中取出任务配置出错", e.getMessage(), e, LogListUtil.newArrayList(taskDataBody, taskInfoDO.getId()));
            throw new TripscrmException(TripSCRMErrorCode.ALIPAY_MSG_EXT_INFO_INVALID);
        }

        // 素材
        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("支付宝定向消息任务执行器执行失败，素材获取失败", LogListUtil.newArrayList(taskDataBody, taskInfoDO.getId()));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MATERIAL_IN_TASK);
        }


        MaterialContentConvertContext materialContentConvertContext = buildMaterialContentConvertContext(context, taskDataBody, materailInfoDO, alipayCrowdId, alipayGroupTemplateList,crowdType);
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(context, todoTaskVO);

        AliPayDirectMsgRequest request = buildAliPayDirectMsgRequest(context, todoTaskVO, materailInfoDO, materialTrackRelationDTO, materialContentConvertContext, alipayGroupTemplateList, pushTime, alipayCrowdId, taskName, crowdType);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> aliPayMsgManager.asyncSendSingleMessage(request));
        if (!result.isSuccess()) {
            PlatformLogUtil.logFail("支付宝定向消息任务执行器执行失败，调用支付宝接口失败", LogListUtil.newArrayList(request, result, taskInfoDO.getId()));
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        JSONObject data = new JSONObject();
        data.put("extraInfo", materialContentConvertContext.getExtraInfo());
        data.put("materialId", materailInfoDO.getId());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    private AliPayDirectMsgRequest buildAliPayDirectMsgRequest(TaskExecuteContext context
            , TodoTaskVO todoTaskVO
            , MaterailInfoDO materailInfoDO
            , MaterialTrackRelationDTO materialTrackRelationDTO
            , MaterialContentConvertContext materialContentConvertContext
            , List<String> alipayGroupTemplateIdList
            , Date pushTime
            , String alipayChessboardCrowdId
            , String taskName, String crowdType) {
        AliPayDirectMsgRequest request = new AliPayDirectMsgRequest();
        request.setAlipayGroupTemplateIdList(alipayGroupTemplateIdList);
        request.setAssistantMsgName(taskName);
        request.setPublishTime(pushTime);
        request.setCrowdCode(alipayChessboardCrowdId);
        //人群类型
        request.setCrowdType(CrowdTypeEnum.of(crowdType));
        request.setMainTaskInstanceId(todoTaskVO.getMainTaskInstanceId());
        request.setAbTestBucketId(getAbTestBucketId(todoTaskVO));
        request.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        request.setMaterailInfoDO(materailInfoDO);
        request.setTaskType(getTaskType());
        request.setMaterialContentConvertContext(materialContentConvertContext);

        // 指定requestId
        if (Objects.nonNull(context.getExtInfo()) && context.getExtInfo().containsKey("requestId")
                && Objects.nonNull(context.getExtInfo().get("requestId"))
                && context.getExtInfo().get("requestId") instanceof String) {
            request.setRequestId(context.getExtInfo().get("requestId").toString());
        }
        return request;
    }

    private MaterialTrackRelationDTO buildMaterialTrackRelationDTO(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(getScene());
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setTargetId(getFinalTargetId(context, todoTaskVO.getData().get(0)));
        return materialTrackRelationDTO;
    }

    private MaterialContentConvertContext buildMaterialContentConvertContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody, MaterailInfoDO materailInfoDO, String alipayChessboardId, List<String> alipayGroupTemplateIdList, String crowdType) {
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setAlipayCrowdCode(alipayChessboardId);
        materialContentConvertContext.setAlipayCrowdType(crowdType);
        materialContentConvertContext.setAlipayGroupTemplateIdList(alipayGroupTemplateIdList);
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        if (StringUtils.hasLength(taskDataBody.getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(taskDataBody.getExtInfo()));
        }
        materialContentConvertContext.setImageUrlList(context.getImageUrlList());
        materialContentConvertContext.setOriginContent(materailInfoDO.getContent());
        return materialContentConvertContext;
    }


    protected String getScene() {
        return MaterialSendSceneTypeConstant.ALIPAY_DIRECT_MSG;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        if (Objects.isNull(context)
                || Objects.isNull(context.getTaskDataVO())
                || CollectionUtils.isEmpty(context.getTaskDataVO().getData())
                || Objects.isNull(context.getTaskDataVO().getData().get(0))) {
            PlatformLogUtil.logFail("获取支付宝定向消息目标type失败，context或taskDataBody为空", LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        TaskDataVO.DataBodyVO taskDataBody = context.getTaskDataVO().getData().get(0);
        String groupTemplateListStr = taskDataBody.getTargetId();
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!StringUtils.hasLength(groupTemplateListStr) || !Objects.equals(targetTypeEnum, ActivityTargetTypeEnum.ALIPAY_GROUP_TEMPLATE_ID_LIST)) {
            PlatformLogUtil.logFail("获取支付宝定向消息目标type失败，targetId或targetType非法", LogListUtil.newArrayList(context, taskDataBody, groupTemplateListStr, targetTypeEnum));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        return targetTypeEnum;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (Objects.isNull(context) || Objects.isNull(taskDataBody)) {
            PlatformLogUtil.logFail("获取支付宝定向消息目标id失败，context或taskDataBody为空", LogListUtil.newArrayList(context, taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        String groupTemplateListStr = taskDataBody.getTargetId();
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!StringUtils.hasLength(groupTemplateListStr) || !Objects.equals(targetTypeEnum, ActivityTargetTypeEnum.ALIPAY_GROUP_TEMPLATE_ID_LIST)) {
            PlatformLogUtil.logFail("获取支付宝定向消息目标id失败，targetId或targetType非法", LogListUtil.newArrayList(context, taskDataBody, groupTemplateListStr, targetTypeEnum));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        return groupTemplateListStr;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.ALIPAY_DIRECT_MSG;
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return "";
    }
}
