package com.alibaba.tripscrm.service.model.domain.query;


import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 群组模板信息查询条件
 */
@Data
public class GroupTemplateQuery extends BasePageRequest {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID列表
     */
    private List<Long> idList;

    /**
     * 群组模板ID
     */
    private String groupTemplateId;

    /**
     * 平台类型
     */
    private PlatformTypeEnum platformType;

    /**
     * 群组模板ID列表
     */
    private List<String> groupTemplateIdList;


    /**
     * 模板名称模糊查询
     */
    private String templateNameLike;
    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 企业ID列表
     */
    private List<String> corpIdList;

    /**
     * 是否删除
     */
    private Integer deleted;
}
