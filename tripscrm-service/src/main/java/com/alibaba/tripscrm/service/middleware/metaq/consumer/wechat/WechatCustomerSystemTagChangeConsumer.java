package com.alibaba.tripscrm.service.middleware.metaq.consumer.wechat;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.factory.DelayScheduleMethodFactory;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.DelayMetaqContext;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.fusionchat.ProfileService;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * topic: scrm_wechat_customer_system_tag_diff
 * consumerId: CID_tripscrm_wechat_customer_system_tag_change
 * <p>
 * 消息格式为：${corp_id}\t${union_id}\t${external_user_id}
 * ${corp_id}必传
 * ${union_id}和${external_user_id}至少传一个
 *
 * <AUTHOR>
 * @since 2024/6/6 10:46
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerSystemTagChangeConsumer implements MessageListenerConcurrently {
    private final WechatCustomerService wechatCustomerService;
    private final ProfileService profileService;
    private final TagGroupService tagGroupService;
    private final TagInfoService tagInfoService;
    private final TagRelationService tagRelationService;
    private final UicUtils uicUtils;
    private final MetaqProducer metaqProducer;

    @Switch(description = "获取uid失败后重试等待时间", name = "delayRetrySeconds")
    public static Integer delayRetrySeconds = 60 * 2;

    private volatile Set<String> systemTagSyncLbsPermanentCitySet;

    private volatile Set<String> systemTagSyncLbsPermanentProvinceSet;

    @PostConstruct
    public void init() {
        DelayScheduleMethodFactory.register(DelayScheduleMethodEnum.SEND_WECHAT_CUSTOMER_SYSTEM_TAG_CHANGE_MESSAGE, this::sendRetryMessage);
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("接收到客户标签变更消息", LogListUtil.newArrayList(msgs));
            String receivedMsg = new String(msg.getBody());
            String tags = msg.getTags();
            boolean isRetry = StringUtils.hasLength(tags) && tags.contains("retry");
            if (!dealWithMessage(receivedMsg, isRetry)) {
                PlatformLogUtil.logFail("处理客户标签变更消息失败", LogListUtil.newArrayList(isRetry, receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message, boolean isRetry) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("客户标签变更消息解析后结果", LogListUtil.newArrayList(isRetry, message));
            if (!StringUtils.hasLength(message)) {
                PlatformLogUtil.logFail("客户标签变更消息内容解析为空");
                return false;
            }

            List<String> contentList = Arrays.stream(message.split("\t")).filter(StringUtils::hasLength).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contentList) || contentList.size() < 2 || contentList.size() > 3) {
                PlatformLogUtil.logFail("客户标签变更消息内容非法");
                return false;
            }

            String corpId = contentList.get(0);
            String unionId = contentList.get(1);
            String externalUserId = contentList.size() == 3 ? contentList.get(2) : null;
            SpaceInfoThreadLocalUtils.setCorpId(corpId);

            if (!StringUtils.hasLength(unionId) && !StringUtils.hasLength(externalUserId)) {
                PlatformLogUtil.logFail("unionId和externalUserId不可同时为空");
                return false;
            }

            // 查询客户信息
            WechatCustomerVO wechatCustomerInfo = getWechatCustomerInfo(unionId, externalUserId);
            if (Objects.isNull(wechatCustomerInfo)) {
                PlatformLogUtil.logFail("查询客户信息失败");
                return false;
            }

            processCustomerSystemTag(wechatCustomerInfo, corpId, isRetry);
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("处理客户标签变更消息异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    @TairLock("'processCustomerSystemTag_' + #wechatCustomerInfo.corpId + '_' + #wechatCustomerInfo.externalUserId")
    public void processCustomerSystemTag(WechatCustomerVO wechatCustomerInfo, String corpId, boolean isRetry) {
        // 查询诸葛标签
        Map<String, String> picassoLabelInfo = getPicassoLabelInfo(wechatCustomerInfo, corpId, isRetry);
        // 查询scrm系统标签
        Map<String, String> customerSystemTagInfo = getCustomerSystemTagInfo(wechatCustomerInfo.getTagIdList());

        Map<String, String> newTagInfo = getNewTagInfo(picassoLabelInfo, customerSystemTagInfo);
        Map<String, String> updateTagInfo = getUpdateTagInfo(picassoLabelInfo, customerSystemTagInfo);
        Map<String, String> deleteTagInfo = getDeleteTagInfo(picassoLabelInfo, customerSystemTagInfo);

        PlatformLogUtil.logFail("查询诸葛标签与客户系统标签结果", LogListUtil.newArrayList(picassoLabelInfo, customerSystemTagInfo));
        PlatformLogUtil.logFail("标签比对结果", LogListUtil.newArrayList(newTagInfo, updateTagInfo, deleteTagInfo));

        Map<String, String> checkNeedToCreateTagInfo = new HashMap<>();
        checkNeedToCreateTagInfo.putAll(newTagInfo);
        checkNeedToCreateTagInfo.putAll(updateTagInfo);

        for (String tagName : checkNeedToCreateTagInfo.keySet()) {
            TagGroupDTO tagGroupDTO = tagGroupService.selectBySpaceIdAndTagTypeAndSourceWithCache(SwitchConfig.SCRM_BASE_SPACE_ID, TagTypeEnum.CROWD_TAG, tagName);
            if (Objects.isNull(tagGroupDTO)) {
                PlatformLogUtil.logFail("查询标签组结果为空，无法处理", LogListUtil.newArrayList(tagName));
                continue;
            }

            createTagIfAbsent(tagGroupDTO.getId(), checkNeedToCreateTagInfo.get(tagName));
        }

        // 获取标签Id
        List<String> picassoTagIdList = picassoLabelInfo.entrySet().stream().map(x -> getTagId(x.getKey(), x.getValue())).collect(Collectors.toList());
        List<String> customerSystemTagIdList = customerSystemTagInfo.entrySet().stream().map(x -> getTagId(x.getKey(), x.getValue())).collect(Collectors.toList());

        List<String> oldCustomerTagIdList = wechatCustomerInfo.getTagIdList();
        List<String> newCustomerTagIdList = wechatCustomerInfo.getTagIdList().stream()
                .filter(x -> !customerSystemTagIdList.contains(x))
                .collect(Collectors.toList());
        newCustomerTagIdList.addAll(picassoTagIdList);

        List<String> customerDeleteTagIdList = oldCustomerTagIdList.stream()
                .filter(x -> !newCustomerTagIdList.contains(x))
                .collect(Collectors.toList());

        List<String> customerAddTagIdList = newCustomerTagIdList.stream()
                .filter(x -> !oldCustomerTagIdList.contains(x))
                .collect(Collectors.toList());

        List<ItemTagRelationDTO> addList = customerAddTagIdList.stream()
                .map(id -> {
                    ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                    itemTagRelationDTO.setItemId(wechatCustomerInfo.getExternalUserId() + "_" + wechatCustomerInfo.getCorpId());
                    itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_CUSTOMER.getCode());
                    itemTagRelationDTO.setTag(id);
                    itemTagRelationDTO.setDeleted((byte) 0);
                    return itemTagRelationDTO;
                })
                .collect(Collectors.toList());
        tagRelationService.batchUpsertSelective(addList);

        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setDeleted((byte) 1);
        for (String deleteTagId : customerDeleteTagIdList) {
            ItemTagRelationQuery condition = new ItemTagRelationQuery();
            condition.setItemId(wechatCustomerInfo.getExternalUserId() + "_" + wechatCustomerInfo.getCorpId());
            condition.setItemType(BizTypeEnum.WECHAT_CUSTOMER.getCode());
            condition.setTag(deleteTagId);
            tagRelationService.updateSelective(itemTagRelationDTO, condition);
        }

//        // 没有新的标签，无需执行规则
//        if (CollectionUtils.isEmpty(checkNeedToCreateTagInfo)) {
//            return;
//        }
//
//        // 客户新增的系统标签Id
//        List<String> newTagIdList = checkNeedToCreateTagInfo.entrySet().stream().map(x -> getTagId(x.getKey(), x.getValue())).collect(Collectors.toList());
//        // 从规则配置获取需要调度的任务
//        RuleExecuteContext context = new RuleExecuteContext();
//        JSONObject params = new JSONObject();
//        params.put(RuleConstant.NEW_TAG_ID_LIST, newTagIdList);
//        params.put(RuleConstant.CORP_ID, corpId);
//        params.put(RuleConstant.EXTERNAL_USER_ID, wechatCustomerInfo.getExternalUserId());
//        context.setParams(params);
//        AbstractRuleGroupStrategy ruleGroupStrategy = AbstractRuleGroupStrategy.getStrategyByType(RuleTypeEnum.TASK);
//        if (Objects.isNull(ruleGroupStrategy)) {
//            PlatformLogUtil.logFail("获取规则组执行策略失败，本次处理结束");
//            return;
//        }
//        TripSCRMResult<Void> result = ruleGroupStrategy.run(context);
//        if (!result.isSuccess()) {
//            PlatformLogUtil.logFail("规则组策略执行失败", LogListUtil.newArrayList(result));
//            return;
//        }
//
//        PlatformLogUtil.logFail("规则组策略执行成功", LogListUtil.newArrayList(result));
    }

    private String getTagId(String tagName, String tagValue) {
        TagGroupDTO tagGroupDTO = tagGroupService.selectBySpaceIdAndTagTypeAndSourceWithCache(SwitchConfig.SCRM_BASE_SPACE_ID, TagTypeEnum.CROWD_TAG, tagName);
        TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(tagGroupDTO.getId(), tagValue);
        return String.valueOf(tagInfoDTO.getId());
    }

    /**
     * 加个小锁，怕并发冲突
     */
    @TairLock(value = "'checkCreateNewTag_' + #groupId + '_' + #source")
    public void createTagIfAbsent(Long groupId, String source) {
        TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(groupId, source);
        if (Objects.nonNull(tagInfoDTO)) {
            // 标签已经创建出来，无需处理
            return;
        }

        PlatformLogUtil.logFail("查询标签结果为空，需要新建", LogListUtil.newArrayList(groupId, source));
        List<TagInfoDTO> records = new ArrayList<>();
        TagInfoDTO record = new TagInfoDTO();
        record.setName(source);
        record.setDescription("");
        record.setParentId(0L);
        record.setTagType(TagTypeEnum.CROWD_TAG.getCode());
        record.setSpaceId(SwitchConfig.SCRM_BASE_SPACE_ID);
        record.setGroupId(groupId);
        record.setSource(source);
        records.add(record);
        tagInfoService.batchInsertSelective(records, false);
    }

    private Map<String, String> getNewTagInfo(Map<String, String> picassoLabelInfo, Map<String, String> customerSystemTagInfo) {
        Map<String, String> result = new HashMap<>();
        for (String picassoLabelName : picassoLabelInfo.keySet()) {
            if (customerSystemTagInfo.containsKey(picassoLabelName)) {
                continue;
            }
            result.put(picassoLabelName, picassoLabelInfo.get(picassoLabelName));
        }

        return result;
    }

    private Map<String, String> getUpdateTagInfo(Map<String, String> picassoLabelInfo, Map<String, String> customerSystemTagInfo) {
        Map<String, String> result = new HashMap<>();
        for (String picassoLabelName : picassoLabelInfo.keySet()) {
            if (!customerSystemTagInfo.containsKey(picassoLabelName)) {
                continue;
            }
            if (Objects.equals(picassoLabelInfo.get(picassoLabelName), customerSystemTagInfo.get(picassoLabelName))) {
                continue;
            }
            result.put(picassoLabelName, picassoLabelInfo.get(picassoLabelName));
        }

        return result;
    }

    private Map<String, String> getDeleteTagInfo(Map<String, String> picassoLabelInfo, Map<String, String> customerSystemTagInfo) {
        Map<String, String> result = new HashMap<>();
        for (String customerSystemTagName : customerSystemTagInfo.keySet()) {
            if (picassoLabelInfo.containsKey(customerSystemTagName)) {
                continue;
            }
            result.put(customerSystemTagName, customerSystemTagInfo.get(customerSystemTagName));
        }

        return result;
    }

    private Map<String, String> getCustomerSystemTagInfo(List<String> tagIdList) {
        Map<String, String> customerTagInfo = new HashMap<>();
        if (CollectionUtils.isEmpty(tagIdList)) {
            return customerTagInfo;
        }

        for (String tagId : tagIdList) {
            TagInfoDTO tagInfoDTO = tagInfoService.queryByIdWithCache(tagId);
            // 只处理系统标签
            if (Objects.isNull(tagInfoDTO) || !Objects.equals(TagTypeEnum.CROWD_TAG, TagTypeEnum.of(tagInfoDTO.getTagType()))) {
                continue;
            }

            TagGroupDTO tagGroupDTO = tagGroupService.selectByIdWithCache(tagInfoDTO.getGroupId());
            // 只处理系统标签
            if (Objects.isNull(tagGroupDTO) || !Objects.equals(TagTypeEnum.CROWD_TAG, TagTypeEnum.of(tagInfoDTO.getTagType()))) {
                continue;
            }

            String labelName = tagGroupDTO.getSource();
            String labelValue = tagInfoDTO.getSource();
            customerTagInfo.put(labelName, labelValue);
        }

        return customerTagInfo;
    }

    private Map<String, String> getPicassoLabelInfo(WechatCustomerVO wechatCustomerVO, String corpId, Boolean isRetry) {
        String uid = uicUtils.getUidByUnionId(wechatCustomerVO.getUnionId());
        // 没有绑定uid
        if (!StringUtils.hasLength(uid)) {
            // 需要稍后重试
            if (!isRetry) {
                Long triggerTime = System.currentTimeMillis() + delayRetrySeconds * 1000L;
                DelayMetaqContext delayMetaqContext = buildDelayMetaqContext(wechatCustomerVO.getUnionId(), corpId, wechatCustomerVO.getExternalUserId(), triggerTime);
                metaqProducer.send(MQEnum.DELAY_MQ_SCHEDULING, "wechat_customer_system_tag_change" + wechatCustomerVO.getUnionId(), "", JSON.toJSONString(delayMetaqContext), null);
            }

            throw new TripscrmException(TripSCRMErrorCode.ID_MAPPING_FAIL);
        }

        TripSCRMResult<Map<String, String>> result = profileService.getProfileValue(uid, SwitchConfig.ZHUGE_SYNC_LABEL_LIST);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            PlatformLogUtil.logFail("查询客户诸葛标签失败");
            throw new TripscrmException(TripSCRMErrorCode.QUERY_PICASSO_LABEL_INFO_ERROR);
        }

        return result.getData().entrySet()
                .stream()
                .filter(entry -> {
                    if (!StringUtils.hasLength(entry.getValue())) {
                        return false;
                    }

                    if (Lists.newArrayList("未知", "unknown").contains(entry.getValue())) {
                        return false;
                    }

                    if (Objects.equals("lbs_permanent_city", entry.getKey())) {
                        return getSystemTagSyncLbsPermanentCitySet().contains(entry.getValue());
                    }

                    if (Objects.equals("lbs_permanent_province", entry.getKey())) {
                        return getSystemTagSyncLbsPermanentProvinceSet().contains(entry.getValue());
                    }

                    return true;
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private WechatCustomerVO getWechatCustomerInfo(String unionId, String externalUserId) {
        if (!StringUtils.hasLength(externalUserId)) {
            externalUserId = wechatCustomerService.getExternalUserIdByUnionId(unionId);
        }

        if (!StringUtils.hasLength(externalUserId)) {
            PlatformLogUtil.logFail("根据unionId查询externalUserId失败");
            return null;
        }

        List<WechatCustomerVO> wechatCustomerList = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(externalUserId));
        if (CollectionUtils.isEmpty(wechatCustomerList)) {
            return null;
        }

        return wechatCustomerList.get(0);
    }

    private static DelayMetaqContext buildDelayMetaqContext(String unionId, String corpId, String externalUserId, Long triggerTime) {
        DelayMetaqContext delayMetaqContext = new DelayMetaqContext();
        delayMetaqContext.setTriggerTime(triggerTime);
        delayMetaqContext.setTriggerType(TriggerTimeEnum.CUSTOM.getCode());
        delayMetaqContext.setSecondDelayLevel(true);
        delayMetaqContext.setFunctionType(DelayScheduleMethodEnum.SEND_WECHAT_CUSTOMER_SYSTEM_TAG_CHANGE_MESSAGE);
        delayMetaqContext.setDelayKey("delay_wechat_customer_system_tag_change" + unionId);

        JSONObject param = new JSONObject();
        param.put("corpId", corpId);
        param.put("unionId", unionId);
        param.put("externalUserId", externalUserId);
        delayMetaqContext.setParam(JSON.toJSONString(param));
        return delayMetaqContext;
    }

    public void sendRetryMessage(String param) {
        JSONObject jsonParam = JSONObject.parseObject(param);
        metaqProducer.send(MQEnum.SCRM_WECHAT_CUSTOMER_SYSTEM_TAG_DIFF, "", "retry", String.format("%s\t%s\t%s", jsonParam.getString("corpId"), jsonParam.getString("unionId"), jsonParam.getString("externalUserId")));
    }

    private Set<String> getSystemTagSyncLbsPermanentCitySet() {
        if (Objects.nonNull(systemTagSyncLbsPermanentCitySet)) {
            return systemTagSyncLbsPermanentCitySet;
        }

        synchronized (this) {
            if (Objects.nonNull(systemTagSyncLbsPermanentCitySet)) {
                return systemTagSyncLbsPermanentCitySet;
            }

            systemTagSyncLbsPermanentCitySet = Arrays.stream(SwitchConfig.SYSTEM_TAG_SYNC_LBS_PERMANENT_CITY_LIST_STR.split(",")).filter(StringUtils::hasLength).collect(Collectors.toSet());
        }

        return systemTagSyncLbsPermanentCitySet;
    }

    private Set<String> getSystemTagSyncLbsPermanentProvinceSet() {
        if (Objects.nonNull(systemTagSyncLbsPermanentProvinceSet)) {
            return systemTagSyncLbsPermanentProvinceSet;
        }

        synchronized (this) {
            if (Objects.nonNull(systemTagSyncLbsPermanentProvinceSet)) {
                return systemTagSyncLbsPermanentProvinceSet;
            }

            systemTagSyncLbsPermanentProvinceSet = Arrays.stream(SwitchConfig.SYSTEM_TAG_SYNC_LBS_PERMANENT_PROVINCE_LIST_STR.split(",")).filter(StringUtils::hasLength).collect(Collectors.toSet());
        }

        return systemTagSyncLbsPermanentProvinceSet;
    }
}
