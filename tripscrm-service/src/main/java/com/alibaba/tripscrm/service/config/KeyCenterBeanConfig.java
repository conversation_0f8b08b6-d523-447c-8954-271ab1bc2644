package com.alibaba.tripscrm.service.config;

import com.alibaba.keycenter.client.properties.KeyCenterProperties;
import com.taobao.common.keycenter.keystore.KeyStore;
import com.taobao.common.keycenter.keystore.KeyStoreImpl;
import com.taobao.common.keycenter.security.Cryptograph;
import com.taobao.common.keycenter.security.CryptographImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

@Configuration
@Component
public class KeyCenterBeanConfig {

    // 服务器端链接地址，具体参考：https://yuque.antfin.com/mkf08w/cvdlr6/environment
    // 如果更换服务器地址，千万不要遗漏 /keycenter 这个 path
    @Value("${key.center.http.service}")
    private String httpServiceAddress;

    // 应用发放码，和应用一一绑定，请不要共享
    // 注册应用文档：https://yuque.antfin.com/mkf08w/cvdlr6/iuveir
    @Value("${key.center.app.publish}")
    private String appPublishNum;

    @Bean(name = "keyStore", initMethod = "init")
    public KeyStore initKeyStore() {
        KeyCenterProperties keyCenterProperties = new KeyCenterProperties();
        keyCenterProperties.setAppPublishNum(appPublishNum);
        keyCenterProperties.setHttpServiceAddress(httpServiceAddress);
        keyCenterProperties.setPreferProtocal("http");

        KeyStoreImpl keyStore = new KeyStoreImpl();
        keyStore.setKeyCenterProperties(keyCenterProperties);
        return keyStore;
    }

    /**
     * 加解密服务。按需，若不用加解密服务可以删除。
     *
     * @param keyStore
     * @return Cryptograph
     */
    @Bean(name = "cryptograph")
    public Cryptograph initCryptoGraph(KeyStore keyStore) {
        return new CryptographImpl(keyStore);
    }

}