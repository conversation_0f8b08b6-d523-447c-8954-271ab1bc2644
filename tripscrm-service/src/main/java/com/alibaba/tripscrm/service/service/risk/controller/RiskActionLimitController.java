package com.alibaba.tripscrm.service.service.risk.controller;

import com.alibaba.tripscrm.service.enums.risk.RateLimitTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskItemEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.model.domain.request.RiskActionRequest;
import com.alibaba.tripscrm.service.model.domain.risk.RateLimitResult;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionConfig;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionHitConfig;
import com.alibaba.tripscrm.service.model.domain.risk.UserRiskSchema;
import com.alibaba.tripscrm.service.service.risk.RiskController;
import com.alibaba.tripscrm.service.service.risk.RiskActionLimitTargetHandler;
import com.alibaba.tripscrm.service.service.risk.status.RiskActionStatusController;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.hsf.invocation.Invocation;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 机器人行动项 限流 风控控制器
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class RiskActionLimitController extends RiskController<RiskActionRequest> {
    @Resource
    private RiskActionStatusController riskActionStatusController;

    @Override
    protected RiskItemEnum riskItem() {
        return RiskItemEnum.LIMIT;
    }

    /**
     * 行动项进入保护
     *
     * @param param param
     * @return return
     */
    @Override
    public Boolean judgeIntoProtect(RiskActionRequest param) {
        String corpId = param.getCorpId();
        String userId = param.getUserId();
        RiskActionEnum riskAction = param.getRiskAction();
        Invocation invocation = param.getInvocation();
        RateLimitResult result = param.getResult();
        // 获取风控配置，先取类型维度再取机器人维度
        UserRiskSchema riskSchema = getRiskSchema(corpId, userId);
        if (riskSchema == null) {
            result.setLimit(false);
        } else {
            // 获取当前风控的行动配置
            Map<String, RiskActionConfig> actionConfigMap = riskSchema.getActionConfigs().stream()
                    .collect(Collectors.toMap(RiskActionConfig::getActionCode, Function.identity()));
            RiskActionConfig configValue = actionConfigMap.get(riskAction.getActionCode());
            if (configValue != null && configValue.getEnable()) {
                RiskActionLimitTargetHandler riskActionLimitTargetHandler = RiskActionLimitTargetHandler.get(riskAction.getTarget());
                RiskActionHitConfig hitConfig = riskActionLimitTargetHandler.hitRiskConfig(corpId, userId, configValue, invocation);
                RateLimitResult rateLimitResult = riskActionLimitTargetHandler.limitControl(corpId, userId, hitConfig);
                if (rateLimitResult.getLimit() && !rateLimitResult.isHitSub()) {
                    // 若触发限流，并且是父项限流非子项，则行动项状态变更
                    riskActionStatusController.toProtect(param.getCorpId(), param.getUserId(), param.getRiskAction(), rateLimitResult.getLimitType());
                }
                result.setLimit(rateLimitResult.getLimit());
                result.setHitActionCode(rateLimitResult.getHitActionCode());
                result.setLimitType(rateLimitResult.getLimitType());
            } else {
                result.setLimit(false);
            }
        }
        if (result.getLimit()) {
            PlatformLogUtil.logFail("风控控制-触发行动项限流，进入保护", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
            return true;
        } else {
            PlatformLogUtil.logInfo("风控控制-未触发行动项限流，无需进入保护", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
            return false;
        }
    }

    /**
     * 行动项退出保护
     *
     * @return return
     */
    @Override
    public Boolean judgeOutProtect(RiskActionRequest param) {
        String corpId = param.getCorpId();
        String userId = param.getUserId();
        RiskActionEnum riskAction = param.getRiskAction();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
        Integer limitType = param.getRiskObject().getLimitType();
        String limitTimeIndex = param.getRiskObject().getLimitTimeIndex();
        String currentLimitTimeIndex = sdf.format(new Date());
        long limitTimeMinuteIndex = Long.parseLong(limitTimeIndex);
        long currentLimitTimeMinuteIndex = Long.parseLong(currentLimitTimeIndex);
        RateLimitTypeEnum limitTypeEnum = RateLimitTypeEnum.fromValue(limitType);
        boolean flag = false;
        if (limitTypeEnum != null) {
            switch (limitTypeEnum) {
                case MINUTE:
                    // 若不是同一分钟，则需要退出保护
                    if (currentLimitTimeMinuteIndex > limitTimeMinuteIndex) {
                        riskActionStatusController.toNormal(corpId, userId, riskAction, RiskStatusEnum.PROTECT);
                        flag = true;
                    }
                    break;
                case HOUR:
                    // 若不是同一小时，则需要退出保护
                    long limitTimeHourIndex = limitTimeMinuteIndex / 100;
                    long currentLimitTimeHourIndex = currentLimitTimeMinuteIndex / 100;
                    if (currentLimitTimeHourIndex > limitTimeHourIndex) {
                        riskActionStatusController.toNormal(corpId, userId, riskAction, RiskStatusEnum.PROTECT);
                        flag = true;
                    }
                    break;
                case DAY:
                    // 若不是同一天，则需要退出保护
                    long limitTimeDayIndex = limitTimeMinuteIndex / 10000;
                    long currentLimitTimeDayIndex = currentLimitTimeMinuteIndex / 10000;
                    if (currentLimitTimeDayIndex > limitTimeDayIndex) {
                        riskActionStatusController.toNormal(corpId, userId, riskAction, RiskStatusEnum.PROTECT);
                        flag = true;
                    }
                    break;
            }
        }
        if (flag) {
            PlatformLogUtil.logInfo("风控控制-行动项满足限流规则，退出保护", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
            return true;
        }
        PlatformLogUtil.logFail("风控控制-行动项未满足限流规则，无法退出保护", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
        return false;
    }
}

