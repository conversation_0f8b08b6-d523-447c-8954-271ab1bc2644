package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-19 17:42:18
 */
public interface AbTestBucketService {
    /**
     * 根据id查询
     *
     * @param id 主键
     * @return AbTestBucketVO
     */
    AbTestBucketVO getById(Long id);

    /**
     * 根据任务Id查询
     *
     * @param taskId 任务Id
     * @return List<AbTestBucketVO>
     */
    List<AbTestBucketVO> listByTaskId(Long taskId);

    /**
     * 根据素材Id查询
     *
     * @param materialId 素材Id
     * @return List<AbTestBucketVO>
     */
    List<AbTestBucketVO> listByMaterialId(Long materialId);

    /**
     * 更新任务所有分桶数据（包含新增+更新+删除）
     *
     * @param taskId
     * @param list
     * @return
     */
    Integer update(Long taskId, List<AbTestBucketVO> list);

    /**
     * 插入数据
     *
     * @param abTestBucketVO
     * @return
     */
    Integer insert(AbTestBucketVO abTestBucketVO);

    /**
     * 删除指定分桶
     *
     * @param taskId
     * @return
     */
    Integer deleteById(Long taskId);

    /**
     * 修改指定分桶
     *
     * @param abTestBucketVO 新的数据
     * @return 影响行数
     */
    Integer updateById(AbTestBucketVO abTestBucketVO);

    /**
     * 获取AB实验分桶
     *
     * @param taskId 任务 Id
     * @param userId userId
     * @return AbTestBucketVO
     */
    AbTestBucketVO getBucket(Long taskId, String userId);

    /**
     * 获取AB实验分桶
     *
     * @param bucketList ab分桶列表
     * @param userId     userId
     * @return AbTestBucketVO
     */
    AbTestBucketVO getBucket(List<AbTestBucketVO> bucketList, String userId);

    /**
     * 填充订阅消息类型素材的模版信息
     */
    void fillTemplateInfo(List<AbTestBucketVO> bucketList);
}
