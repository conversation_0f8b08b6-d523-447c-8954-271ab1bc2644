package com.alibaba.tripscrm.service.service.strategy.variable;

import com.alibaba.tripscrm.service.enums.material.DataSourceTypeEnum;
import com.alibaba.tripscrm.service.model.dto.material.VariableInfoDTO;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@AllArgsConstructor
public class UpstreamVariableDataServiceImpl extends AbstractVariableDataService{
    @Override
    public DataSourceTypeEnum type() {
        return DataSourceTypeEnum.UPSTREAM;
    }

    @Override
    public String getValue(VariableInfoDTO variableInfoDTO, Map<String, Object> carryMap) {
        if (variableInfoDTO == null || StringUtils.isBlank(variableInfoDTO.getRule())) {
            PlatformLogUtil.logFail("变量信息缺失", LogListUtil.newArrayList(variableInfoDTO));
            return "";
        }
        return MapUtils.getString(carryMap, variableInfoDTO.getRule(), "");
    }
}
