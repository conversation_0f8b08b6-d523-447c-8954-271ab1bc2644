package com.alibaba.tripscrm.service.service.strategy.websocket.domain;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * websocket通讯消息体
 *
 * <AUTHOR>
 * @date 2023/9/28
 */
@Data
public class WsEvent implements Serializable {
    private static final long serialVersionUID = -744679055441158809L;
    /**
     * 当前操作的企微员工id
     */
    private String userId;
    /**
     * 当前建立连接的空间所属的corpId
     */
    private String corpId;
    /**
     * ws请求类型
     * @see WsEventTypeEnum
     */
    private String type;
    /**
     * ws流转数据体
     */
    private JSONObject data;
    /**
     * 用于前端定位是不是同一个请求
     */
    private String requestId;

    /**
     * 仅推送到前端时会带上该参数，用于判断是否成功
     */
    private Boolean success = true;
    /**
     * 仅推送到前端时会带上该参数，失败情况下的错误信息
     */
    private String errorMessage;
    /**
     * 是否是警告
     */
    private Boolean warn = false;
    /**
     * 后台自传的时间戳，用于判断事件处理时长
     */
    private Long timestamp;
    /**
     * 与该事件相关的平台账号id，此参数仅用于metaQ分发
     */
    private String accountId;
}
