package com.alibaba.tripscrm.service.middleware.metaq.consumer;

import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.model.domain.Pair;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.metaq.client.MetaPushConsumer;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;

/**
 * metaq消息处理基类，用于建立metaq消息接收的模版，
 * 具体消息接收请继承该抽象类并实现相关方法，
 * spring配置时，请注意配置init和destroy方法
 *
 * <AUTHOR>
 * @date 2024/04/20
 */
public abstract class AbstractMetaqConsumer {

    private MetaPushConsumer consumer;

    /**
     * 消息topic
     */
    protected abstract String topic();

    /**
     * 消息的group
     */
    protected abstract String group();

    /**
     * 关注的消息tag
     */
    protected abstract String tag();

    /**
     * 消息处理逻辑
     */
    protected abstract Pair<ConsumeConcurrentlyStatus, String> consume(String tag, byte[] content);

    /**
     * 处理接收到的消息队列
     */
    @PostConstruct
    public void init() {
        try {
            consumer = new MetaPushConsumer(group());
            consumer.subscribe(topic(), tag());

            consumer.registerMessageListener((MessageListenerConcurrently) (msgs, context) -> {
                MessageExt msg = msgs.get(0);
                String key = msg.getKeys();
                final String tag = msg.getTags();
                final byte[] content = msg.getBody();
                String code = "success";
                long startTime = System.currentTimeMillis();

                try {
                    if (content == null || content.length == 0) {
                        code = "empty_data";
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }

                    ConsumeConcurrentlyStatus status = null;
                    try {
                        Pair<ConsumeConcurrentlyStatus, String> result = consume(tag, content);
                        status = result.getFirst();
                        code = result.getSecond();
                    } catch (Exception e) {
                        code = "run_exp";
                        PlatformLogUtil.logException("deal with msg exception", e.getMessage(), e, LogListUtil.newArrayList(topic(), group(), tag(), key, new String(content, StandardCharsets.UTF_8)));
                    }
                    return status;
                } finally {
                    PlatformLogUtil.logInfo("deal with msg finish", LogListUtil.newArrayList(topic(), group(), tag, key, code, System.currentTimeMillis() - startTime));
                }
            });
            // 应用启动时，执行start来初始化，只需要初始化一次
            consumer.start();
        } catch (Exception e) {
            PlatformLogUtil.logException("init metaq consumer failed", e.getMessage(), e, LogListUtil.newArrayList(topic(), group(), tag()));
        }
    }

    @PreDestroy
    public void destroy() {
        if (consumer == null) {
            return;
        }
        try {
            consumer.shutdown();
        } catch (Exception e) {
            PlatformLogUtil.logException("close metaq consumer failed", e.getMessage(), e, LogListUtil.newArrayList(topic(), group(), tag()));
        }
    }
}
