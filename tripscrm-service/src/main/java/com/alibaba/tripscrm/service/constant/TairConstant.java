package com.alibaba.tripscrm.service.constant;

/**
 * Tair相关常量
 *
 * <AUTHOR>
 * @date 2023-05-24 16:14:26
 */
public class TairConstant {
    /**
     * BPMS 上下文
     */
    public static final String LDB_BPMS_CONTEXT_PREFIX = "LDB_BPMS_CONTEXT_";
    /**
     * 任务已执行的数据游标
     */
    public static final String LDB_TASK_FINISH_TARGET_DATA_PREFIX = "LDB_TASK_FINISH_TARGET_DATA_";
    /**
     * 任务测试链路存储target数据的缓存前缀
     */
    public static final String LDB_TASK_TEST_DATA_PREFIX = "LDB_TASK_TEST_DATA_";

    /**
     * 疲劳度控制分布式锁 key 前缀
     */
    public static final String LDB_FATIGUE_LOCK_PREFIX = "LDB_FATIGUE_LOCK_";

    /**
     * 疲劳度控制天级发送数据-消息类型
     */
    public static final String LDB_FATIGUE_DAY_DATA_PREFIX = "LDB_FATIGUE_DAY_DATA_";

    /**
     * 疲劳度控制天级发送数据-消息子类型
     */
    public static final String LDB_SUB_FATIGUE_DAY_DATA_PREFIX = "LDB_SUB_FATIGUE_DAY_DATA_";

    /**
     * 保存请求proxy时的上下文数据
     */
    public static final String LDB_PROXY_REQUEST_DATA_PREFIX = "LDB_PROXY_REQUEST_DATA_";

    /**
     * 过期时间，天级纬度
     **/
    public static final int EXPIRE_TIME_DAY_UNIT = 24 * 60 * 60;

    /**
     * 任务状态缓存前缀
     **/
    public static final String LDB_TASK_STATUS_PREFIX = "LDB_TASK_STATUS_";

    /**
     * 创建业务空间请求去重使用
     **/
    public static final String LDB_CREATE_SPACE_PREFIX = "LDB_CREATE_SPACE_";

    /**
     * 更新websocket用户在线信息的分布式锁
     */
    public static final String WEB_SOCKET_USER_ONLINE_INFO_LOCK = "WEB_SOCKET_USER_ONLINE_INFO_LOCK";
    /**
     * websocket用户在线信息
     */
    public static final String NEW_WEB_SOCKET_USER_ONLINE_INFO = "NEW_WEB_SOCKET_USER_ONLINE_INFO";
    /**
     * 更新websocket账号在线信息的分布式锁
     */
    public static final String WEB_SOCKET_ACCOUNT_ONLINE_INFO_LOCK = "WEB_SOCKET_ACCOUNT_ONLINE_INFO_LOCK";
    /**
     * websocket账号在线信息
     */
    public static final String NEW_WEB_SOCKET_ACCOUNT_ONLINE_INFO = "NEW_WEB_SOCKET_ACCOUNT_ONLINE_INFO";
    /**
     * 发送的消息信息聚合体的分布式锁
     */
    public static final String SEND_MESSAGE_RECORD_LOCK = "SEND_MESSAGE_RECORD_LOCK";
    /**
     * 发送的消息信息聚合体，用于记录没有发送超过的消息列表
     */
    public static final String SEND_MESSAGE_RECORD = "SEND_MESSAGE_RECORD";
    /**
     * 创建群聊信息聚合体的分布式锁
     */
    public static final String CREATE_GROUP_RECORD_LOCK = "CREATE_GROUP_RECORD_LOCK";
    /**
     * 创建群聊信息聚合体，用于记录没有创建成功的群聊列表
     */
    public static final String CREATE_GROUP_RECORD = "CREATE_GROUP_RECORD";
    /**
     * 发送消息时，缓存msgId和messageId，用于消息撤回
     */
    public static final String MESSAGE_ID_MAPPING_PREFIX = "MESSAGE_ID_MAPPING_";

    /**
     * 客户链接新增客户数量
     */
    public static final String LINK_ADD_CLIENT_PREFIX_ = "LINK_ADD_CLIENT_PREFIX_";

    /**
     * 个人活码/获客链接添加客户数量（不包含已流失客户）
     */
    public static final String WECHAT_CONTACT_ME_ADD_CUSTOMER_COUNT_PREFIX = "WECHAT_CONTACT_ME_ADD_CUSTOMER_COUNT_";

    /**
     * 群活码入群客户数量（不包含已流失客户）
     */
    public static final String WECHAT_JOIN_GROUP_JOIN_GROUP_COUNT_PREFIX = "WECHAT_JOIN_GROUP_JOIN_GROUP_COUNT_";

    /**
     * 群内添加好友申请数量（不包含已流失客户）
     */
    public static final String ADD_WECHAT_CUSTOMER_BY_GROUP_REQUEST_COUNT_PREFIX = "ADD_WECHAT_CUSTOMER_BY_GROUP_REQUEST_COUNT_";

    /**
     * 群内添加好友申请成功数量（不包含已流失客户）
     */
    public static final String ADD_WECHAT_CUSTOMER_BY_GROUP_REQUEST_SUCCESS_COUNT_PREFIX = "ADD_WECHAT_CUSTOMER_BY_GROUP_REQUEST_SUCCESS_COUNT_";

    /**
     * 群内添加好友通过数量（不包含已流失客户）
     */
    public static final String ADD_WECHAT_CUSTOMER_BY_GROUP_SUCCESS_COUNT_PREFIX = "ADD_WECHAT_CUSTOMER_BY_GROUP_SUCCESS_COUNT_";

    /**
     * 修改群活码配置的分布式锁
     */
    public static final String UPDATE_JOIN_GROUP_CONFIG_LOCK = "UPDATE_JOIN_GROUP_CONFIG_LOCK_";

    /**
     * 自动建群群名后缀索引
     */
    public static final String AUTO_CREATE_GROUP_NAME_INDEX_PREFIX = "AUTO_CREATE_GROUP_NAME_INDEX_";

    /**
     * 群欢迎语上次发送时间
     */
    public static final String GROUP_WELCOME_LAST_SEND_TIME_PREFIX = "GROUP_WELCOME_LAST_SEND_TIME_";

    /**
     * 任务执行去重
     */
    public static final String TASK_EXECUTE_DUPLICATE_CHECK_PREFIX = "TASK_EXECUTE_DUPLICATE_CHECK_";

    /**
     * 任务创建群聊状态
     */
    public static final String TASK_CREATE_GROUP_PREFIX = "TASK_CREATE_GROUP_";

    /**
     * 发送申请加好友请求
     */
    public static final String APPLY_TO_BE_FRIEND_PREFIX = "APPLY_TO_BE_FRIEND_PREFIX_";

    /**
     * 发送申请拉入群聊请求
     */
    public static final String APPLY_TO_JOIN_GROUP_PREFIX = "APPLY_TO_JOIN_GROUP_PREFIX_";

    /**
     * 朋友圈发送信息前缀
     */
    public static final String MOMENT_RESOURCE_INFO_PREFIX = "MOMENT_RESOURCE_INFO_PREFIX_";

    /**
     * 企业群发任务发送时间
     */
    public static final String WECHAT_GROUP_MSG_SEND_TIME = "WECHAT_GROUP_MSG_SEND_TIME";

    /**
     * 钉钉单聊机器人小时级频控
     */
    public static final String DING_TALK_ROBOT_SINGLE_MESSAGE_HOUR_RATE_LIMIT_PREFIX_ = "DING_TALK_ROBOT_SINGLE_MESSAGE_HOUR_RATE_LIMIT_";

    /**
     * 钉钉单聊机器人天级频控
     */
    public static final String DING_TALK_ROBOT_SINGLE_MESSAGE_DAY_RATE_LIMIT_PREFIX_ = "DING_TALK_ROBOT_SINGLE_MESSAGE_DAY_RATE_LIMIT_";

    /**
     * 企微成员打卡记录（存储最近7天的打卡记录）
     */
    public static final String WECHAT_USER_CLOCK_IN_RECORD_PREFIX = "WECHAT_USER_CLOCK_IN_RECORD_";

    /**
     * 企微客户群自动关注任务扫描记录（1小时内只能被定时任务执行一次关注操作）
     */
    public static final String WECHAT_GROUP_AUTO_OPEN_WORK_JOB_SCAN_PREFIX = "WECHAT_GROUP_AUTO_OPEN_WORK_JOB_SCAN_";

    /**
     * 个人活码缓存
     */
    public static final String CONCAT_ME_QR_CODE = "CONCAT_ME_QR_CODE_";

    /**
     * 百应用户外推
     */
    public static final String BAIYING_USER_PUSH = "BAIYING_USER_PUSH_";

    /**
     * 羚羊用户外推
     */
    public static final String LINGYANG_USER_PUSH = "LINGYANG_USER_PUSH_";

    /**
     * 批量更新群管理
     */
    public static final String BATCH_UPDATE_ADMIN_INVITE_JOIN_GROUP = "BATCH_UPDATE_ADMIN_INVITE_JOIN_GROUP_";

    /**
     * 批量更新群主
     */
    public static final String BATCH_UPDATE_OWNER_INVITE_JOIN_GROUP = "BATCH_UPDATE_OWNER_INVITE_JOIN_GROUP_";

    /**
     * 批量群操作
     */
    public static final String BATCH_GROUP_OPERATE_MAPPING = "BATCH_GROUP_OPERATE_MAPPING_";

    /**
     * 异步串联操作结果
     */
    public static final String ASYNC_SERIES_OPERATE_RESULT = "ASYNC_SERIES_OPERATE_RESULT_";

    /**
     * 接收消息唯一标识
     */
    public static final String RECEIVE_MESSAGE_UNIQUE_KEY_LOCK = "RECEIVE_MESSAGE_UNIQUE_KEY_LOCK_";

    /**
     * 自动回复接收消息唯一标识
     */
    public static final String AUTO_RESPONSE_MESSAGE_UNIQUE_KEY_LOCK = "AUTO_RESPONSE_MESSAGE_UNIQUE_KEY_";

    /**
     * 智能回复接收消息唯一标识
     */
    public static final String SMART_RESPONSE_MESSAGE_UNIQUE_KEY_LOCK = "SMART_RESPONSE_MESSAGE_UNIQUE_KEY_";

    /**
     * 聚合聊天发送消息唯一标识
     */
    public static final String FUSION_CHAT_SEND_MESSAGE_UNIQUE_KEY_PREFIX = "FUSION_CHAT_SEND_MESSAGE_UNIQUE_KEY_";

    /**
     * 更新群聊消息锁
     */
    public static final String UPDATE_GROUP_MESSAGE_LOCK = "UPDATE_GROUP_MESSAGE_LOCK_";

    /**
     * 自动回复消息锁
     */
    public static final String AUTO_RESPONSE_MESSAGE_LOCK = "AUTO_RESPONSE_MESSAGE_LOCK_";

    /**
     * 当前时间窗口，企微成员主动添加好友数量
     */
    public final static String WECHAT_USER_ADD_CUSTOMER_COUNT_IN_WINDOW_PREFIX = "WECHAT_USER_TEN_MINUTE_ADD_CUSTOMER_COUNT_";

    /**
     * 当前时间窗口，企微成员创建群聊数量
     */
    public final static String WECHAT_USER_CREATE_GROUP_COUNT_IN_WINDOW_PREFIX = "WECHAT_USER_CREATE_GROUP_COUNT_IN_WINDOW_";

    /**
     * 企微成员好友数量
     */
    public final static String WECHAT_USER_CUSTOMER_COUNT_PREFIX = "WECHAT_USER_CUSTOMER_COUNT_";

    /**
     * 任务实例原子能力执行成功次数
     */
    public final static String TASK_INSTANCE_BIZ_EXECUTE_SUCCESS_COUNT_PREFIX = "TASK_INSTANCE_BIZ_EXECUTE_SUCCESS_COUNT_";

    /**
     * 子任务实例状态缓存
     */
    public final static String SUB_TASK_INSTANCE_STATUS_PREFIX = "SUB_TASK_INSTANCE_STATUS_";

    /**
     * 任务单条数据执行分布式锁
     */
    public final static String TASK_SINGLE_DATA_EXECUTE_LOCK_PREFIX = "TASK_SINGLE_DATA_EXECUTE_LOCK_";

    /**
     * 子任务实例执行成功数量
     */
    public final static String SUB_TASK_INSTANCE_EXECUTE_SUCCESS_COUNT_PREFIX = "SUB_TASK_INSTANCE_EXECUTE_SUCCESS_COUNT_";

    /**
     * 子任务实例执行失败数量
     */
    public final static String SUB_TASK_INSTANCE_EXECUTE_FAIL_COUNT_PREFIX = "SUB_TASK_INSTANCE_EXECUTE_FAIL_COUNT_";

    /**
     * 子任务实例重试中数量
     */
    public final static String SUB_TASK_INSTANCE_EXECUTE_RETRY_COUNT_PREFIX = "SUB_TASK_INSTANCE_EXECUTE_RETRY_COUNT_";

    /**
     * 子任务实例异步执行成功数量
     */
    public final static String SUB_TASK_INSTANCE_ASYNC_EXECUTE_SUCCESS_COUNT_PREFIX = "SUB_TASK_INSTANCE_ASYNC_EXECUTE_SUCCESS_COUNT_";

    /**
     * 子任务实例异步执行失败数量
     */
    public final static String SUB_TASK_INSTANCE_ASYNC_EXECUTE_FAIL_COUNT_PREFIX = "SUB_TASK_INSTANCE_ASYNC_EXECUTE_FAIL_COUNT_";

    /**
     * 朋友圈评论信息前缀
     */
    public static final String MOMENT_COMMENT_INFO_PREFIX = "MOMENT_COMMENT_INFO_PREFIX_";

    /**
     * 诸葛标签同步器锁前缀
     */
    public static final String CROWD_TAG_SYNCHRONIZER_LOCK_PREFIX = "CROWD_TAG_SYNCHRONIZER_LOCK_";

    /**
     * 企微标签同步器锁前缀
     */
    public static final String WECHAT_TAG_SYNCHRONIZER_LOCK_PREFIX = "WECHAT_TAG_SYNCHRONIZER_LOCK_";

    /**
     * 客户标签关系同步器锁前缀
     */
    public static final String WECHAT_CUSTOMER_TAG_RELATION_SYNCHRONIZER_LOCK_PREFIX = "WECHAT_CUSTOMER_TAG_RELATION_SYNCHRONIZER_LOCK_";

    /**
     * 转发消息前缀
     */
    public static final String FORWARD_MESSAGE_INFO_PREFIX = "FORWARD_MESSAGE_INFO_PREFIX_";

    /**
     * 客户标签变更数量
     */
    public static final String WECHAT_CUSTOMER_TAG_DIFF_COUNT_PREFIX = "WECHAT_CUSTOMER_TAG_DIFF_COUNT_";

    /**
     * tccp消费去重
     */
    public static final String TCCP_CONSUME_DISTINCT_KEY = "TCCP_CONSUME_DISTINCT_KEY_";

    /**
     * metaQ消息消费去重
     */
    public static final String METAQ_MESSAGE_ID_DISTINCT_KEY_PREFIX = "METAQ_MESSAGE_ID_DISTINCT_KEY_";

    /**
     * 商家获客活动新增客户数量
     */
    public static final String SELLER_ACQUISITION_ACTIVITY_ADD_CUSTOMER_COUNT_PREFIX = "SELLER_ACQUISITION_ACTIVITY_ADD_CUSTOMER_COUNT_";

    /**
     * 客户最近一次访问商家获客页面对应的活动Id
     */
    public static final String CUSTOMER_LAST_VISIT_SELLER_ACQUISITION_PAGE_ACTIVITY = "CUSTOMER_LAST_VISIT_SELLER_ACQUISITION_PAGE_ACTIVITY_PREFIX_";

    /**
     * 群池刷新异步创建请求Id前缀
     */
    public static final String GROUP_POOL_REFRESH_ASYNC_CREATE_REQUEST_ID_PREFIX = "GROUP_POOL_REFRESH_ASYNC_CREATE_REQUEST_ID_";

    /**
     * 创建群活码分布式锁前缀
     */
    public static final String CREATE_GROUP_QR_CODE_LOCK_PREFIX = "CREATE_GROUP_QR_CODE_LOCK_";

    /**
     * 群组模版同步器锁前缀
     */
    public static final String GROUP_TEMPLATE_SYNCHRONIZER_LOCK_PREFIX = "GROUP_TEMPLATE_SYNCHRONIZER_LOCK_";

    /**
     * 群信息同步器锁前缀
     */
    public static final String CHAT_SYNCHRONIZER_LOCK_PREFIX = "CHAT_SYNCHRONIZER_LOCK_";

    /**
     * 支付宝群成员数缓存
     */
    public static final String ALIPAY_GROUP_MEMBER_COUNT_CACHE = "ALIPAY_GROUP_MEMBER_COUNT_CACHE_";
    /**
     * 同步支付宝群id缓存
     */
    public static final String ALIPAY_GROUP_ID_CACHE = "ALIPAY_GROUP_ID_CACHE_";
}
