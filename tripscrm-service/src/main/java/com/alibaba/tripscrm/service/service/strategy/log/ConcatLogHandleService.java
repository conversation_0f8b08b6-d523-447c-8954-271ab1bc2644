package com.alibaba.tripscrm.service.service.strategy.log;

import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.model.domain.log.SopLogContentBO;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/3/17 13:50
 **/
@Service
public class ConcatLogHandleService extends SopLogHandleService {

    @Override
    public LogShowTypeEnum handleType() {
        return LogShowTypeEnum.CONCAT;
    }

    @Override
    public String getLogContentStr(SopLogContentBO sopLogContentBO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(sopLogContentBO.getNameDesc())
                .append("从【")
                .append(Optional.ofNullable(sopLogContentBO.getSourceValue()).orElse("空值"))
                .append("】")
                .append("修改为")
                .append("【")
                .append(sopLogContentBO.getPresentValue())
                .append("】");
        return stringBuilder.toString();
    }
}
