package com.alibaba.tripscrm.service.service.strategy.message;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.constant.MaterialJsonKeyConstant;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.manager.task.MaterialTrackIdRelationManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.message.MessageTransferDTO;
import com.alibaba.tripscrm.service.model.dto.message.MiniProgramMessageInfoDTO;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.WxMediaUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-04 23:32:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MiniProgramMessageConverter extends AbstractMessageConverter<MiniProgramMessageInfoDTO> {

    private final MaterialTrackIdRelationManager materialTrackIdRelationManager;
    private final WxMediaUtils wxMediaUtils;

    @Override
    public MiniProgramMessageInfoDTO convert(MessageTransferDTO messageTransferDTO) {
        MiniProgramMessageInfoDTO miniProgramMessageInfoDTO = new MiniProgramMessageInfoDTO();
        JSONObject messageContent = messageTransferDTO.getContent();
        JSONObject miniProgramConfig = messageContent.getJSONObject(MaterialJsonKeyConstant.miniProgramConfig);
        String originUrl = getOriginUrl(miniProgramConfig);
        JSONArray pictureArray = miniProgramConfig.getJSONArray(MaterialJsonKeyConstant.cardMainImage);
        String picture = CollectionUtils.isEmpty(pictureArray) ? "" : pictureArray.getString(0);
        String title = miniProgramConfig.getString(MaterialJsonKeyConstant.cardTitle);
        messageTransferDTO.getMaterialTrackRelationDTO().setMsgParagraphId(-1);
        BaseResult<String> result = materialTrackIdRelationManager.insertMaterialRelationAndGetTrackId(messageTransferDTO.getMaterialTrackRelationDTO());
        PlatformLogUtil.logInfo("小程序消息转换器，插入发送素材的信息获取匹配的跟踪id", LogListUtil.newArrayList(messageTransferDTO.getMaterialTrackRelationDTO(), result));
        if (result == null || !result.isSuccess()) {
            throw new RuntimeException("生成trackId失败");
        }

        // 生成 trackId
        String trackId = result.getData();
        String pagePath = MaterialUtils.convert2MiniProgramPath(originUrl, trackId, null);
        if (!Lists.newArrayList(TaskType.WELCOME).contains(messageTransferDTO.getTaskType())) {
            pagePath = MaterialUtils.replaceMiniProgramPathWithDotHtml(pagePath);
        }

        miniProgramMessageInfoDTO.setPathUrl(pagePath);
        miniProgramMessageInfoDTO.setTitle(title);
        miniProgramMessageInfoDTO.setCoverUrl(picture);
        return miniProgramMessageInfoDTO;
    }

    @Override
    public List<MessageBO> buildMessageBO(MiniProgramMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, Integer messageIndex) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgNum(messageIndex);
        messageBO.setMsgType(messageInfoDTO.getMessageType());
        JSONObject jsonObject = new JSONObject();
        JSONObject webMsgJsonObject = JSONObject.parseObject(SwitchConfig.MINI_PROGRAM_DEFAULT_PROP);
        jsonObject.put("we_app_msg", webMsgJsonObject);
        webMsgJsonObject.put("name", MaterialConstant.DEFAULT_TITLE);
        webMsgJsonObject.put("description", messageInfoDTO.getTitle());
        webMsgJsonObject.put("app_id", MaterialConstant.SINGLE_CHAT_APP_ID);
        webMsgJsonObject.put("uri", messageInfoDTO.getPathUrl());
        String messageContent = Base64.getEncoder().encodeToString(jsonObject.toJSONString().getBytes());
        messageBO.setTitle(messageInfoDTO.getTitle());
        messageBO.setMsgContent(messageContent);
        messageBO.setHref(messageInfoDTO.getCoverUrl());
        return Lists.newArrayList(messageBO);
    }

    @Override
    List<WxMessageBO> buildWxMessageBO(MiniProgramMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, TaskType taskType) {
        WxMessageBO wxMessageBO = new WxMessageBO();
        wxMessageBO.setMsgType(WxAttachmentTypeEnum.MINI_PROGRAM);
        wxMessageBO.setPicUrl(messageInfoDTO.getCoverUrl());
        wxMessageBO.setMediaId(wxMediaUtils.getMediaId(WxAttachmentTypeEnum.IMAGE, messageInfoDTO.getCoverUrl(), SpaceInfoThreadLocalUtils.getCorpId(), false));
        wxMessageBO.setTitle(messageInfoDTO.getTitle());
        wxMessageBO.setPathUrl(messageInfoDTO.getPathUrl());
        wxMessageBO.setAppId(MaterialConstant.OTHER_APP_ID);
        return Lists.newArrayList(wxMessageBO);
    }

    private String getOriginUrl(JSONObject message) {
        if (MaterialSupplyTypeEnum.PAGE_MAKER.getType().equals(message.getString(MaterialJsonKeyConstant.supplyType))) {
            JSONObject pageMaker = message.getJSONObject(MaterialJsonKeyConstant.pageMakerData);
            return pageMaker.getString(MaterialJsonKeyConstant.url);
        }

        if (MaterialSupplyTypeEnum.SHOP.getType().equals(message.getString(MaterialJsonKeyConstant.supplyType))) {
            JSONObject shop = message.getJSONObject(MaterialJsonKeyConstant.hotelData);
            String id = shop.getString(MaterialJsonKeyConstant.id);
            return MaterialConstant.HOTEL_H5_PRE + id;
        }

        if (MaterialSupplyTypeEnum.COMMODITY.getType().equals(message.getString(MaterialJsonKeyConstant.supplyType))) {
            JSONObject commodity = message.getJSONObject(MaterialJsonKeyConstant.commodityData);
            String id = commodity.getString(MaterialJsonKeyConstant.id);
            return MaterialConstant.GOODS_MINI_PROGRAM_URL_PRE + id;
        }

        if (MaterialSupplyTypeEnum.COMMUNITY_CONTENT.getType().equals(message.getString(MaterialJsonKeyConstant.supplyType))) {
            JSONObject data = message.getJSONObject(MaterialJsonKeyConstant.DATA);
            String id = data.getString(MaterialJsonKeyConstant.id);
            return String.format(MapUtils.getString(SwitchConfig.COMMUNITY_CONTENT_DETAIL_URL_MAP, EnvUtils.getEnvironment(), MapUtils.getString(SwitchConfig.COMMUNITY_CONTENT_DETAIL_URL_MAP, "online")), id);
        }

        return message.getString(MaterialJsonKeyConstant.pagePath);
    }

    @Override
    public MessageTypeEnum getMsgType() {
        return MessageTypeEnum.MINI_PROGRAM;
    }
}
