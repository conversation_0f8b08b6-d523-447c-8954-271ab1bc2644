package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class WechatUserCreateRequest {
    /**
     * 企微成员列表
     */
    private List<WechatUserInfo> wechatUserList;

    @Data
    public static class WechatUserInfo {
        /**
         * 企微组织Id
         */
        private String corpId;
        /**
         * userId
         */
        private String userId;
        /**
         * 名称
         */
        private String name;
        /**
         * 别名
         */
        private String alias;
        /**
         * 手机号
         */
        private String mobile;
        /**
         * 部门Id
         */
        private Integer department;
        /**
         * 性别
         */
        private Integer gender;
        /**
         * 头像url
         */
        private String avatarUrl;
    }
}
