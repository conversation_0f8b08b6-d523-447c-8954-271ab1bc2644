package com.alibaba.tripscrm.service.service.impl.wechat.groupmsg;

import com.alibaba.tripscrm.dal.mapper.tddl.WechatGroupMsgTaskMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgTaskDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgTaskParam;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupMsgTaskQuery;
import com.alibaba.tripscrm.service.service.wechat.groupmsg.WechatGroupMsgTaskService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatGroupMsgTaskServiceImpl implements WechatGroupMsgTaskService {
    private final WechatGroupMsgTaskMapper wechatGroupMsgTaskMapper;

    /**
     * 根据参数统计总数
     * @param query
     */
    @Override
    public long count(WechatGroupMsgTaskQuery query) {
        WechatGroupMsgTaskParam wechatGroupMsgTaskParam = buildParam(query);
        return wechatGroupMsgTaskMapper.countByParam(wechatGroupMsgTaskParam);
    }

    /**
     * 根据参数查询
     * @param param
     */
    @Override
    public WechatGroupMsgTaskDO find(WechatGroupMsgTaskQuery param) {
        List<WechatGroupMsgTaskDO> list = list(param);
        if (CollectionUtils.isEmpty(list)) {
                return null;
        }
        return list.get(0);
    }

    /**
     * 列表查询
     * @param query
     */
    @Override
    public List<WechatGroupMsgTaskDO> list(WechatGroupMsgTaskQuery query) {
        WechatGroupMsgTaskParam wechatGroupMsgTaskParam = buildParam(query);
        List<WechatGroupMsgTaskDO> list = wechatGroupMsgTaskMapper.selectByParam(wechatGroupMsgTaskParam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list;
    }

    /**
     * 创建
     * @param record
     */
    @Override
    public int insertSelective(WechatGroupMsgTaskDO record) {
        return wechatGroupMsgTaskMapper.insertSelective(record);
    }

    /**
     * 修改
     * @param record
     * @param condition
     */
    @Override
    public int updateSelective(WechatGroupMsgTaskDO record, WechatGroupMsgTaskQuery condition) {
        WechatGroupMsgTaskParam wechatGroupMsgTaskParam = buildParam(condition);
        return wechatGroupMsgTaskMapper.updateByParamSelective(record, wechatGroupMsgTaskParam);
    }

    @Override
    public int upsertSelective(WechatGroupMsgTaskDO record) {
        return wechatGroupMsgTaskMapper.upsertSelective(record);
    }

    private WechatGroupMsgTaskParam buildParam(WechatGroupMsgTaskQuery query) {
        WechatGroupMsgTaskParam wechatGroupMsgTaskParam = new WechatGroupMsgTaskParam();
        WechatGroupMsgTaskParam.Criteria criteria = wechatGroupMsgTaskParam.createCriteria();
        if (NumberUtils.biggerThanZero(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (StringUtils.hasText(query.getMsgId())) {
            criteria.andMsgIdEqualTo(query.getMsgId());
        }
        if (StringUtils.hasText(query.getUserId())) {
            criteria.andUserIdEqualTo(query.getUserId());
        }
        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }
        if (StringUtils.hasText(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }
        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        return wechatGroupMsgTaskParam;
    }
}