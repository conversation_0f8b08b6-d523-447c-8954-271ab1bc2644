package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.WechatTransferCustomerResultDO;
import com.alibaba.tripscrm.service.model.domain.query.WechatTransferCustomerResultQuery;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WechatTransferCustomerResultService {
    /**
     * 根据参数统计总数
     *
     * @param query
     */
    long count(WechatTransferCustomerResultQuery query);

    /**
     * 根据参数查询
     *
     * @param query
     */
    WechatTransferCustomerResultDO find(WechatTransferCustomerResultQuery query);

    /**
     * 列表查询
     *
     * @param query
     */
    List<WechatTransferCustomerResultDO> list(WechatTransferCustomerResultQuery query);

    /**
     * 创建
     *
     * @param record
     */
    int insertSelective(WechatTransferCustomerResultDO record);

    /**
     * 选择性修改
     *
     * @param record
     * @param condition
     */
    int updateSelective(WechatTransferCustomerResultDO record, WechatTransferCustomerResultQuery condition);
}