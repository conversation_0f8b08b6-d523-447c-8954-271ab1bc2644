package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatReceiveTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/6/20 19:50
 **/
@Data
public class MaterialToMessageRequest {

    /**
     * 素材Id
     */
    private Long materialId;

    /**
     * 群聊类型
     * @see FusionChatReceiveTypeEnum
     */
    private Integer chatType;

    /**
     * 发送人
     */
    private String sendUserId;

    /**
     * 目标Id(群聊：chatId, 私聊：客户unionId)
     */
    private String targetId;

    /**
     * 操作人Id
     */
    private String operatorId;
}
