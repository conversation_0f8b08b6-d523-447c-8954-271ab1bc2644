package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 企微成员打卡结果回调
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatUserClockInCallbackProcessor implements ProxyCallbackProcessor {
    private final LdbTairManager ldbTairManager;
    private final WechatUserService wechatUserService;

    @Switch(description = "企微成员新手任务完成需要连续打卡天数", name = "wechatUserClockInDays")
    private static Integer wechatUserClockInDays = 7;
    private static final Integer EXPIRE_TIME = 24 * 60 * 60 * 8;

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject jsonContent = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String corpId = scrmCallbackMsg.getPlatformCorpId();
        String userId = jsonContent.getString("userId");

        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("【企微成员打卡】打卡失败", LogListUtil.newArrayList(corpId, userId, scrmCallbackMsg.getMessage()));
            DingTalkApi.sendTaskMessage(String.format("【企微成员打卡】打卡失败，CorpId:%s，UserId:%s，原因:%s", corpId, userId, scrmCallbackMsg.getMessage()));
            return true;
        }

        PlatformLogUtil.logFail("【企微成员打卡】打卡成功", LogListUtil.newArrayList(corpId, userId));

        WechatUserQuery query = new WechatUserQuery();
        query.setCorpId(corpId);
        query.setUserIdList(Collections.singletonList(userId));
        List<WechatUserDTO> wechatUserList = wechatUserService.listByCondition(query);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            PlatformLogUtil.logFail("企微成员查询失败", LogListUtil.newArrayList(corpId, userId));
            return false;
        }

        WechatUserDTO wechatUserDTO = wechatUserList.get(0);
        if (Objects.equals(wechatUserDTO.getActivateStatus(), 1)) {
            return true;
        }

        updateWechatUserClockInRecord(corpId, userId);
        return true;
    }

    @TairLock("'updateWechatUserClockInRecord_' + #corpId + '_' + #userId")
    public void updateWechatUserClockInRecord(String corpId, String userId) {
        String record = (String) Optional.ofNullable(ldbTairManager.get(TairConstant.WECHAT_USER_CLOCK_IN_RECORD_PREFIX + corpId + "_" + userId)).orElse("");
        Set<Long> dayVersionSet = Arrays.stream(record.split(",")).filter(StringUtils::hasLength).map(Long::parseLong).collect(Collectors.toSet());
        Long currentDayVersion = DateUtils.getDayVersion(new Date());
        dayVersionSet.add(currentDayVersion);
        // 前面7天的都要留
        long clockInDays = dayVersionSet.stream().filter(dayVersion -> dayVersion + wechatUserClockInDays >= currentDayVersion).count();
        if (clockInDays >= wechatUserClockInDays) {
            if (wechatUserService.updateActivateStatus(corpId, userId, 1) <= 0) {
                PlatformLogUtil.logFail("【企微成员打卡】更新新手任务完成状态失败", LogListUtil.newArrayList(corpId, userId));
                DingTalkApi.sendTaskMessage(String.format("【企微成员打卡】更新新手任务完成状态失败，CorpId:%s，UserId:%s", corpId, userId));
            }
        }

        // 最多只留6天前的数据
        String newRecord = dayVersionSet.stream().filter(dayVersion -> dayVersion + wechatUserClockInDays > currentDayVersion).map(String::valueOf).collect(Collectors.joining(","));
        ldbTairManager.put(TairConstant.WECHAT_USER_CLOCK_IN_RECORD_PREFIX + corpId + "_" + userId, newRecord, EXPIRE_TIME);
    }

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.WECHAT_USER_CLOCK_IN_RESULT;
    }
}
