package com.alibaba.tripscrm.service.service.impl.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatUserMapper;
import com.alibaba.tripscrm.dal.model.domain.data.RiskObjectDO;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.RobotDTO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.RoleTypeEnum;
import com.alibaba.tripscrm.domain.request.DeleteUserRequest;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.convert.WechatUserConverter;
import com.alibaba.tripscrm.service.enums.risk.RiskModuleEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WechatUserMatchStrategyEnum;
import com.alibaba.tripscrm.service.manager.opensearch.WechatUserOpenSearchManager;
import com.alibaba.tripscrm.service.middleware.metaq.factory.DelayScheduleMethodFactory;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.SpaceInfo;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.query.WechatUserListQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatUserPageQuery;
import com.alibaba.tripscrm.service.model.domain.request.WechatUserCreateRequest;
import com.alibaba.tripscrm.service.model.domain.request.WechatUserSetPermissionConfigRequest;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.risk.RiskActionChecker;
import com.alibaba.tripscrm.service.service.space.SpaceResourceService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.wechat.WechatDepartmentService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.wx.DepartmentUtils;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.alibaba.tripscrm.service.util.wx.WxMediaUtils;
import com.alibaba.tripzoo.proxy.api.service.RobotService;
import com.alibaba.tripzoo.proxy.api.service.UserService;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.model.DepartmentBO;
import com.alibaba.tripzoo.proxy.model.RobotBO;
import com.alibaba.tripzoo.proxy.request.AsyncLogoutRequest;
import com.alibaba.tripzoo.proxy.request.BatchGetRobotInfoRequest;
import com.alibaba.tripzoo.proxy.request.RobotAsyncClockInRequest;
import com.alibaba.tripzoo.proxy.request.WechatUserDeleteRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.common.utils.JsonUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-04 11:39:19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatUserServiceImpl implements WechatUserService {
    private final WechatUserOpenSearchManager wechatUserOpenSearchManager;
    private final WechatUserMapper wechatUserMapper;
    private final UserService userService;
    private final RobotService robotService;
    private final WechatDepartmentService wechatDepartmentService;
    private final SpaceResourceService spaceResourceService;
    private final AccountService accountService;
    private final WXCorpStorage wxCorpStorage;
    private final SpaceService spaceService;
    private final WxMediaUtils wxMediaUtils;
    private final WechatUserConverter wechatUserConverter;
    private final RiskActionChecker riskActionChecker;

    @PostConstruct
    public void init() {
        DelayScheduleMethodFactory.register(DelayScheduleMethodEnum.WECHAT_USER_CLOCK_IN, this::asyncClock);
    }



    public TripSCRMResult<Boolean> deleteUser(DeleteUserRequest deleteUserRequest){
        WechatUserDeleteRequest wechatUserDeleteRequest = new WechatUserDeleteRequest();
        wechatUserDeleteRequest.setUserId(deleteUserRequest.getUserId());
        wechatUserDeleteRequest.setCorpId(deleteUserRequest.getCorpId());
        ResultDO<Boolean> resultDO = userService.delete(wechatUserDeleteRequest);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess() || !resultDO.getModel()) {
            PlatformLogUtil.logFail("删除企微成员失败", LogListUtil.newArrayList(deleteUserRequest, resultDO));
            return TripSCRMResult.fail(TripSCRMErrorCode.DELETE_USER_ERROR);
        }
        return TripSCRMResult.success(resultDO.getModel());
    }


    @Override
    public TripSCRMResult<Integer> batchCreate(WechatUserCreateRequest request) {
        if (Objects.isNull(request) || CollectionUtils.isEmpty(request.getWechatUserList())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        int result = 0;
        for (WechatUserCreateRequest.WechatUserInfo wechatUserInfo : request.getWechatUserList()) {
            com.alibaba.tripzoo.proxy.request.WechatUserCreateRequest wechatUserCreateRequest = new com.alibaba.tripzoo.proxy.request.WechatUserCreateRequest();
            wechatUserCreateRequest.setCorpId(wechatUserInfo.getCorpId());
            wechatUserCreateRequest.setUserId(wechatUserInfo.getUserId());
            wechatUserCreateRequest.setName(wechatUserInfo.getName());
            wechatUserCreateRequest.setAlias(wechatUserInfo.getAlias());
            wechatUserCreateRequest.setGender(wechatUserInfo.getGender());
            wechatUserCreateRequest.setAvatarMediaId(wxMediaUtils.getMediaId(WxAttachmentTypeEnum.IMAGE, wechatUserInfo.getAvatarUrl(), wechatUserInfo.getCorpId(), true));
            wechatUserCreateRequest.setDepartment(wechatUserInfo.getDepartment());
            wechatUserCreateRequest.setMobile(wechatUserInfo.getMobile());
            ResultDO<Boolean> resultDO = userService.create(wechatUserCreateRequest);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess() || !resultDO.getModel()) {
                PlatformLogUtil.logFail("创建企微成员失败", LogListUtil.newArrayList(wechatUserInfo, resultDO));
                continue;
            }
            PlatformLogUtil.logInfo("创建企微成员成功", LogListUtil.newArrayList(wechatUserInfo, resultDO));
            result++;
        }

        PlatformLogUtil.logInfo("批量创建企微成员完成", LogListUtil.newArrayList(request, result));
        return TripSCRMResult.success(result);
    }

    @Override
    public PageInfoDTO<WechatUserDTO> listPageInfo(WechatUserListQuery query) {
        WechatUserPageQuery wechatUserPageQuery = new WechatUserPageQuery();
        wechatUserPageQuery.setPageNum(query.getPageNum());
        wechatUserPageQuery.setPageSize(query.getPageSize());
        wechatUserPageQuery.setCorpId(query.getCorpId());
        wechatUserPageQuery.setStatusList(query.getStatusList());
        wechatUserPageQuery.setGender(query.getGender());
        wechatUserPageQuery.setNameLike(query.getName());
        wechatUserPageQuery.setRobotType(query.getRobotType());
        wechatUserPageQuery.setIsvType(query.getIsvType());
        wechatUserPageQuery.setActivateStatus(query.getActivateStatus());
        wechatUserPageQuery.setMemberId(query.getManager());

        // 获取部门信息
        DepartmentBO departmentBO = wechatDepartmentService.list(SpaceInfoThreadLocalUtils.getCorpId());
        Map<Integer, DepartmentBO> demartmentMap = DepartmentUtils.convert2Map(departmentBO);
        DepartmentUtils.flat2Map(departmentBO, demartmentMap);
        Long spaceId = SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId();
        List<Integer> bindDepartmentIdList = wechatDepartmentService.queryBindDepartmentIdListBySpaceId(spaceId);
        if (NumberUtils.validInteger(query.getDepartmentId())) {
            wechatUserPageQuery.setDepartmentIdList(DepartmentUtils.getAllSubIdWithCurrent(demartmentMap.get(query.getDepartmentId()), bindDepartmentIdList));
        } else {
            List<Integer> departmentIdList = DepartmentUtils.getAllSubIdWithCurrent(departmentBO, bindDepartmentIdList);
            if (CollectionUtils.isEmpty(departmentIdList)) {
                return new PageInfoDTO<>();
            }
            wechatUserPageQuery.setDepartmentIdList(departmentIdList);
        }

        PageInfo<WechatUserDTO> pageInfo = wechatUserOpenSearchManager.pageQuery(wechatUserPageQuery);


        // 补充其他信息
        List<SpaceResourceDO> spaceResourceList = getSpaceResourceDOS(query.getSpaceId(), pageInfo.getList().stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()));
        Map<String, SpaceResourceDO> spaceResourceMap = spaceResourceList.stream().collect(Collectors.toMap(SpaceResourceDO::getTargetId, Function.identity()));
        pageInfo.getList().forEach(wechatUser -> {
            wechatUser.setCorpName(wxCorpStorage.getCorpName(query.getCorpId()));
            wechatUser.setDepartmentName(demartmentMap.getOrDefault(wechatUser.getDepartmentId(), new DepartmentBO()).getName());
            wechatUser.setAvatarUrl(StringUtils.hasText(wechatUser.getAvatarUrl()) ? wechatUser.getAvatarUrl() : WxConstants.DEFAULT_AVATAR);

            if (spaceResourceMap.containsKey(wechatUser.getUserId())) {
                wechatUser.setAdminList(Arrays.stream(spaceResourceMap.get(wechatUser.getUserId()).getAdminList().split(",")).filter(StringUtils::hasText).collect(Collectors.toList()));
                wechatUser.setMemberList(Arrays.stream(spaceResourceMap.get(wechatUser.getUserId()).getMemberList().split(",")).filter(StringUtils::hasText).collect(Collectors.toList()));

                wechatUser.setAdminNameList(
                        wechatUser.getAdminList().stream()
                                .map(x -> Optional.ofNullable(accountService.getUserByAccountId(x)).map(User::getUserName).orElse(null))
                                .filter(StringUtils::hasText)
                                .collect(Collectors.toList())
                );
                wechatUser.setMemberNameList(
                        wechatUser.getMemberList().stream()
                                .map(x -> Optional.ofNullable(accountService.getUserByAccountId(x)).map(User::getUserName).orElse(null))
                                .collect(Collectors.toList())
                );
            }
        });

        return PageUtils.getPageInfoDTO(pageInfo, a -> a);
    }

    @Override
    public List<WechatUserDTO> listById(List<String> userIdList) {
        userIdList = userIdList.stream().distinct().collect(Collectors.toList());
        List<WechatUserDTO> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            return res;
        }

        List<List<String>> partition = Lists.partition(userIdList, 100);
        for (List<String> list : partition) {
            List<WechatUserDO> wechatUserList = wechatUserMapper.listByUserIdAndCorpId(list, SpaceInfoThreadLocalUtils.getCorpId());
            res.addAll(wechatUserList.stream().map(wechatUserConverter::convert).collect(Collectors.toList()));
        }
        return res;
    }

    @Override
    public List<WechatUserDTO> listWithRobotInfoById(List<String> userIdList) {
        List<WechatUserDTO> wechatUserList = listById(userIdList);
        doRobotInfoQuery(SpaceInfoThreadLocalUtils.getCorpId(), wechatUserList);
        return wechatUserList;
    }

    @Override
    public List<WechatUserDTO> listAll() {
        List<WechatUserDTO> res = new ArrayList<>();
        for (long minId = 0; ; ) {
            List<WechatUserDO> selectResult = wechatUserMapper.listByCorpIdAndMinIdLimit(SpaceInfoThreadLocalUtils.getCorpId(), minId, 300);
            if (CollectionUtils.isEmpty(selectResult)) {
                break;
            }

            minId = selectResult.stream().map(WechatUserDO::getId).max(Long::compareTo).orElse(0L);
            res.addAll(selectResult.stream().map(wechatUserConverter::convert).collect(Collectors.toList()));
        }

        return res;
    }

    @Override
    public List<WechatUserDTO> listByRobotStatus(List<RobotStatusEnum> robotStatusEnumList) {
        if (CollectionUtils.isEmpty(robotStatusEnumList)) {
            return listAll();
        }
        return listAll()
                .stream()
                .filter(wechatUserDTO -> robotStatusEnumList.stream().map(RobotStatusEnum::getCode).collect(Collectors.toList()).contains(wechatUserDTO.getOnlineStatus()))
                .collect(Collectors.toList());
    }

    @Override
    public List<WechatUserDTO> listBySpaceId(Long spaceId) {
        List<Integer> bindDepartmentIdList = wechatDepartmentService.queryBindDepartmentIdListBySpaceId(spaceId);
        List<WechatUserDTO> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(bindDepartmentIdList)) {
            return res;
        }

        String corpId = spaceService.getCorpIdBySpaceId(spaceId);
        for (long minId = 0; ; ) {
            List<WechatUserDO> selectResult = wechatUserMapper.listByCorpIdAndDepartmentIdAndMinIdLimit(corpId, bindDepartmentIdList, minId, 300);
            if (CollectionUtils.isEmpty(selectResult)) {
                break;
            }

            minId = selectResult.stream().map(WechatUserDO::getId).max(Long::compareTo).orElse(0L);
            res.addAll(selectResult.stream().map(wechatUserConverter::convert).collect(Collectors.toList()));
        }

        return res;
    }

    @Override
    public List<WechatUserDTO> listByOnlineStatus(Byte onlineStatus) {
        List<WechatUserDTO> res = new ArrayList<>();
        if (Objects.isNull(onlineStatus)) {
            return res;
        }

        for (long minId = 0; ; ) {
            List<WechatUserDO> selectResult = wechatUserMapper.listByCorpIdAndStatusAndMinIdLimit(SpaceInfoThreadLocalUtils.getCorpId(), onlineStatus, minId, 300);
            if (CollectionUtils.isEmpty(selectResult)) {
                break;
            }

            minId = selectResult.stream().map(WechatUserDO::getId).max(Long::compareTo).orElse(0L);
            res.addAll(selectResult.stream().map(wechatUserConverter::convert).collect(Collectors.toList()));
        }

        return res;
    }

    @Override
    public List<WechatUserDTO> listByCondition(WechatUserQuery query) {
        if (Objects.isNull(query)) {
            return new ArrayList<>();
        }

        if (query.getSpaceId() != null) {
            List<Integer> bindDepartmentIdList = wechatDepartmentService.queryBindDepartmentIdListBySpaceId(query.getSpaceId());
            if (CollectionUtils.isEmpty(bindDepartmentIdList)) {
                return new ArrayList<>();
            }
            query.setDepartmentIdList(bindDepartmentIdList);
        }

        List<WechatUserDO> list = wechatUserMapper.listByCondition(query);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<WechatUserDTO> wechatUserList = list.stream().map(wechatUserConverter::convert).collect(Collectors.toList());
        if (Optional.ofNullable(query.getQueryLockUser()).orElse(false)) {
            doLockUserQuery(query.getCorpId(), wechatUserList);
        }

        if (Optional.ofNullable(query.getQueryRobotInfo()).orElse(false)) {
            doRobotInfoQuery(query.getCorpId(), wechatUserList);
        }

        return wechatUserList;
    }

    private void doRobotInfoQuery(String corpId, List<WechatUserDTO> wechatUserList) {
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return;
        }

        Map<String, WechatUserDTO> userId2WechatUser = wechatUserList.stream().collect(Collectors.toMap(WechatUserDTO::getUserId, x -> x));
        BatchGetRobotInfoRequest request = new BatchGetRobotInfoRequest();
        request.setCorpId(corpId);
        request.setUserIdList(new ArrayList<>(userId2WechatUser.keySet()));
        ResultDO<List<RobotBO>> robotList = robotService.batchGetRobotInfo(request);
        if (Objects.isNull(robotList) || !robotList.getSuccess()) {
            PlatformLogUtil.logFail("查询企微成员的机器人信息，查询失败", LogListUtil.newArrayList(robotList));
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED);
        }

        for (RobotBO robotBO : robotList.getModel()) {
            WechatUserDTO wechatUserDTO = userId2WechatUser.get(robotBO.getUserId());
            List<RobotDTO> robotDTOList = Optional.ofNullable(wechatUserDTO.getRobotList()).orElse(new ArrayList<>());
            wechatUserDTO.setRobotList(robotDTOList);
            RobotDTO robotDTO = new RobotDTO();
            robotDTO.setUserId(robotBO.getUserId());
            robotDTO.setRobotStatus(robotBO.getStatus().getCode());
            robotDTO.setIsvType(robotBO.getIsvType().getCode());
            robotDTOList.add(robotDTO);
        }
    }

    private void doLockUserQuery(String corpId, List<WechatUserDTO> wechatUserList) {
        List<String> userIdList = wechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList());
        List<SpaceResourceDO> spaceResourceList = getSpaceResourceDOS(corpId, userIdList);
        // 过滤仅当前corpId的spaceId
        Map<String, String> userId2LockUser = spaceResourceList.stream()
                .collect(Collectors.toMap(SpaceResourceDO::getTargetId, x -> JSONObject.parseObject(x.getExtraInfo(), new TypeReference<HashMap<String, String>>() {
                }).getOrDefault("lockUser", ""), (x1, x2) -> x1));

        Map<String, List<String>> userId2MemberList = spaceResourceList.stream()
                .collect(Collectors.toMap(SpaceResourceDO::getTargetId, x -> Arrays.stream(Optional.ofNullable(x.getMemberList()).orElse("").split(",")).collect(Collectors.toList()), (x1, x2) -> x1));
        Map<String, List<String>> userId2AdminList = spaceResourceList.stream()
                .collect(Collectors.toMap(SpaceResourceDO::getTargetId, x -> Arrays.stream(Optional.ofNullable(x.getAdminList()).orElse("").split(",")).collect(Collectors.toList()), (x1, x2) -> x1));

        wechatUserList.forEach(x -> {
            if (StringUtils.hasText(userId2LockUser.getOrDefault(x.getUserId(), ""))) {
                x.setLockUser(userId2LockUser.get(x.getUserId()));
                x.setMemberList(userId2MemberList.get(x.getUserId()));
                x.setAdminList(userId2AdminList.get(x.getUserId()));
                User user = accountService.getUserByAccountId(x.getLockUser());
                x.setLockUserName(user != null ? user.getUserName() : "未找到");
            }
        });
    }

    @Override
    public String queryLockUser(String userId, Long spaceId) {
        List<SpaceResourceDO> spaceResourceList = getSpaceResourceDOS(spaceId, Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(spaceResourceList)) {
            return null;
        }

        Map<String, String> extraInfo = JSONObject.parseObject(spaceResourceList.get(0).getExtraInfo(), new TypeReference<HashMap<String, String>>() {
        });
        return extraInfo.get("lockUser");
    }

    @Override
    @Cacheable(key = "'userId:' + #userId + ',corpId:' + #corpId", value = "wechatUserLockCacheManager")
    public String queryLockUserWithCache(String userId, String corpId) {
        List<SpaceResourceDO> spaceResourceList = getSpaceResourceDOS(corpId, Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(spaceResourceList)) {
            PlatformLogUtil.logFail("获取企微账号所属空间资源数据为空", LogListUtil.newArrayList(corpId, userId));
            return null;
        }

        if (spaceResourceList.size() > 1) {
            PlatformLogUtil.logFail("获取企微账号所属空间资源数据长度大于一，取最新一条", LogListUtil.newArrayList(corpId, userId, spaceResourceList));
        }

        Map<String, String> extraInfo = JSONObject.parseObject(spaceResourceList.get(0).getExtraInfo(), new TypeReference<HashMap<String, String>>() {
        });
        return extraInfo.get("lockUser");
    }

    @Override
    public List<WechatUserDTO> listValidWechatUserByCondition(WechatUserQuery query) {
        query.setStatus(WechatUserStatusEnum.ACTIVE.getCode().byteValue());
        query.setOnlineStatus(RobotStatusEnum.ONLINE.getCode().byteValue());
        if (Objects.nonNull(query.getSpaceId()) && SwitchConfig.wechatUserClockInSpaceIdList.contains(query.getSpaceId())) {
            query.setActivateStatus((byte) 1);
        }

        if (Objects.nonNull(query.getCorpId()) && !CollectionUtils.isEmpty(query.getDepartmentIdList())
                && SwitchConfig.wechatUserClockInCorpDepartmentIdList.contains(query.getCorpId() + "_" + query.getDepartmentIdList().get(0))) {
            query.setActivateStatus((byte) 1);
        }

        return listByCondition(query);
    }

    @Override
    public List<WechatUserDTO> listByIdAndStatus(List<String> userIdList, WechatUserStatusEnum wechatUserStatusEnum) {
        List<WechatUserDTO> res = new ArrayList<>();

        List<List<String>> partition = Lists.partition(userIdList, 100);
        for (List<String> list : partition) {
            List<WechatUserDO> wechatUserList = wechatUserMapper.listByUserIdAndCorpIdAndStatus(list, SpaceInfoThreadLocalUtils.getCorpId(), wechatUserStatusEnum.getCode().byteValue());
            res.addAll(wechatUserList.stream().map(wechatUserConverter::convert).collect(Collectors.toList()));
        }

        return res;
    }

    @Override
    public List<String> listUserIdWithPermission(String accountId, Long spaceId) {
        List<SpaceResourceDO> spaceResourceList = spaceResourceService.listBySpaceIdAndResourceType(spaceId, ResourceTypeEnum.WECHAT_USER);
        return spaceResourceList.stream().filter(x -> Arrays.asList(x.getAdminList().split(",")).contains(accountId) || Arrays.asList(x.getMemberList().split(",")).contains(accountId)).map(SpaceResourceDO::getTargetId).collect(Collectors.toList());
    }

    @Override
    public Boolean checkHasManagerPermission(String userId, String accountId, Long spaceId) {
        SpaceResourceDO spaceResourceDO = spaceResourceService.getBySpaceIdAndResourceTypeAndResourceId(spaceId, ResourceTypeEnum.WECHAT_USER, userId);
        if (Objects.isNull(spaceResourceDO)) {
            return false;
        }

        return Arrays.asList(spaceResourceDO.getAdminList().split(",")).contains(accountId) || Arrays.asList(spaceResourceDO.getMemberList().split(",")).contains(accountId);
    }

    @Override
    public Boolean checkHasLock(String userId, String accountId, Long spaceId) {
        String lockAccountId = queryLockUser(userId, spaceId);
        return Objects.equals(lockAccountId, accountId);
    }

    @Override
    public List<String> listAccountIdWithPermission(String userId, Long spaceId) {
        List<SpaceResourceDO> spaceResourceList = getSpaceResourceDOS(spaceId, Lists.newArrayList(userId));
        Set<String> accountIds = new HashSet<>();
        if (CollectionUtils.isEmpty(spaceResourceList)) {
            return new ArrayList<>(accountIds);
        }

        accountIds.addAll(Arrays.asList(spaceResourceList.get(0).getAdminList().split(",")));
        accountIds.addAll(Arrays.asList(spaceResourceList.get(0).getMemberList().split(",")));
        return new ArrayList<>(accountIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lock(User account, String userId, String empId, Long spaceId, Boolean force) {
        List<SpaceResourceDO> spaceResourceList = getSpaceResourceDOS(spaceId, Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(spaceResourceList)) {
            throw new RuntimeException("当前企微账号未配置使用人员信息！");
        }

        SpaceResourceDO spaceResourceDO = spaceResourceList.get(0);
        if (!hasUsePermission(spaceResourceDO, account)) {
            throw new RuntimeException("你未拥有当前企微账号使用权限！");
        }

        Map<String, String> extraInfo = JSONObject.parseObject(spaceResourceDO.getExtraInfo(), new TypeReference<HashMap<String, String>>() {
        });
        String lockUser = extraInfo.getOrDefault("lockUser", "");
        if (StringUtils.hasText(lockUser) && !Objects.equals(lockUser, empId)) {
            if (!force) {
                throw new RuntimeException(String.format("当前企微账号已被%s绑定！", account.getUserName()));
            }
        }

        // 校验是否锁定了超过20个账号
        List<SpaceResourceDO> spaceResources = spaceResourceService.listBySpaceIdAndResourceType(spaceId, ResourceTypeEnum.WECHAT_USER);
        Map<String, List<SpaceResourceDO>> spaceResourceGroup = spaceResources.stream()
                .collect(Collectors.groupingBy(x -> JSONObject.parseObject(x.getExtraInfo(), new TypeReference<HashMap<String, String>>() {
                }).getOrDefault("lockUser", "")));
        if (spaceResourceGroup.getOrDefault(empId, Collections.emptyList()).size() >= 20) {
            throw new RuntimeException("最多支持锁定20个企微账号，如有更多诉求请联系管理员");
        }
        extraInfo.put("lockUser", empId);
        spaceResourceDO.setExtraInfo(JSONObject.toJSONString(extraInfo));
        spaceResourceService.update(spaceResourceDO);
    }

    @Override
    @Transactional
    public void unlock(User account, String userId, String empId, Long spaceId, Boolean force) {
        List<SpaceResourceDO> spaceResourceList = getSpaceResourceDOS(spaceId, Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(spaceResourceList)) {
            throw new RuntimeException("当前企微账号未配置使用人员信息！");
        }

        SpaceResourceDO spaceResourceDO = spaceResourceList.get(0);
        Map<String, String> extraInfo = JSONObject.parseObject(spaceResourceDO.getExtraInfo(), new TypeReference<HashMap<String, String>>() {
        });
        // 是否强制解锁，若非强制解锁要判断
        if (!force) {
            if (!hasUsePermission(spaceResourceDO, account)) {
                throw new RuntimeException("你未拥有当前企微账号使用权限！");
            }
            String lockUser = extraInfo.getOrDefault("lockUser", "");
            if (!StringUtils.hasText(lockUser) || !Objects.equals(lockUser, empId)) {
                throw new RuntimeException("您未锁定当前企微账号！");
            }
        }

        extraInfo.put("lockUser", "");
        spaceResourceDO.setExtraInfo(JSONObject.toJSONString(extraInfo));
        spaceResourceService.update(spaceResourceDO);
    }

    @Override
    @Transactional
    public Integer setPermissionConfig(WechatUserSetPermissionConfigRequest request) {
        boolean seller = StringUtils.hasText(SpaceInfoThreadLocalUtils.getSpaceInfo().getSellerId());
        String userId = request.getUserId();
        List<SpaceResourceDO> spaceResourceDOList = getSpaceResourceDOS(request.getSpaceId(), Lists.newArrayList(userId));

        // 更新
        if (!CollectionUtils.isEmpty(spaceResourceDOList)) {
            SpaceResourceDO spaceResourceDO = spaceResourceDOList.get(0);
            if (!seller && !hasOperatePermission(spaceResourceDO)) {
                throw new RuntimeException("您无操作权限！");
            }

            spaceResourceDO.setSpaceId(request.getSpaceId());
            spaceResourceDO.setLastOperatorId(request.getOperatorId());
            spaceResourceDO.setMemberList(String.join(",", request.getMemberList()));
            return spaceResourceService.update(spaceResourceDO);
        }

        if (!seller && !hasOperatePermission()) {
            throw new RuntimeException("您无操作权限！");
        }

        // 新增
        SpaceResourceDO spaceResourceDO = new SpaceResourceDO();
        spaceResourceDO.setSpaceId(request.getSpaceId());
        spaceResourceDO.setTargetId(userId);
        spaceResourceDO.setTargetType(ResourceTypeEnum.WECHAT_USER.getCode().byteValue());
        spaceResourceDO.setCreatorId(request.getOperatorId());
        spaceResourceDO.setLastOperatorId(request.getOperatorId());
        spaceResourceDO.setAdminList(request.getOperatorId());
        spaceResourceDO.setMemberList(String.join(",", request.getMemberList()));
        return spaceResourceService.insert(spaceResourceDO);
    }

    private List<SpaceResourceDO> getSpaceResourceDOS(Long spaceId, List<String> userIdList) {
        return getSpaceResourceDOS(spaceService.getCorpIdBySpaceId(spaceId), userIdList);
    }

    private List<SpaceResourceDO> getSpaceResourceDOS(String corpId, List<String> userIdList) {
        List<SpaceResourceDO> spaceResourceDOList = spaceResourceService.listByResourceTypeAndResourceIdList(ResourceTypeEnum.WECHAT_USER, userIdList);
        Map<String, List<SpaceResourceDO>> corpId2SpaceResources = spaceResourceDOList.stream().collect(Collectors.groupingBy(spaceResourceDO -> spaceService.getCorpIdBySpaceId(spaceResourceDO.getSpaceId())));
        spaceResourceDOList = corpId2SpaceResources.getOrDefault(corpId, new ArrayList<>());
        return spaceResourceDOList;
    }

    @Override
    public String logout(String userId) {
        List<SpaceResourceDO> spaceResourceDOList = spaceResourceService.listByResourceTypeAndSpaceIdAndResourceIdList(ResourceTypeEnum.WECHAT_USER, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(), Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(spaceResourceDOList)) {
            if (!hasOperatePermission()) {
                throw new RuntimeException("您无操作权限！");
            }
        } else {
            SpaceResourceDO spaceResourceDO = spaceResourceDOList.get(0);
            if (!hasOperatePermission(spaceResourceDO)) {
                throw new RuntimeException("您无操作权限！");
            }
        }
        AsyncLogoutRequest request = new AsyncLogoutRequest();
        request.setUserId(userId);
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<String> resultDO = robotService.asyncLogout(request);
        if (!resultDO.getSuccess()) {
            throw new RuntimeException(resultDO.getResultMessage());
        }

        return resultDO.getModel();
    }

    @Override
    public Long getSpaceId(String userId, String corpId) {
        WechatUserDO wechatUserDO = wechatUserMapper.selectByUserIdAndCorpId(userId, corpId);
        if (Objects.isNull(wechatUserDO)) {
            return null;
        }

        return wechatDepartmentService.getSpaceIdByDepartmentId(wechatUserDO.getMainDepartment());
    }

    @Override
    public TripSCRMResult<String> asyncClock(String corpId, String userId) {
        RobotAsyncClockInRequest request = new RobotAsyncClockInRequest();
        request.setCorpId(corpId);
        request.setUserId(userId);
        ResultDO<String> result = robotService.asyncClockIn(request);
        if (!result.getSuccess()) {
            PlatformLogUtil.logFail("【企微成员打卡】打卡失败", LogListUtil.newArrayList(corpId, userId, result.getResultMessage()));

//            ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(result.getResultCode());
//            if (!Lists.newArrayList(ErrorCodeEnum.WECHAT_USER_NOT_ONLINE, ErrorCodeEnum.ISV_EXCEPTION).contains(errorCodeEnum)) {
//                DingTalkApi.sendTaskMessage(String.format("【企微成员打卡】打卡失败，CorpId:%s，UserId:%s，原因:%s", corpId, userId, result.getResultMessage()));
//            }
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        PlatformLogUtil.logFail("企微成员打卡", LogListUtil.newArrayList(corpId, userId));
        return TripSCRMResult.success(result.getModel());
    }

    @Override
    public TripSCRMResult<String> asyncClock(String jsonRequest) {
        if (!StringUtils.hasLength(jsonRequest)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        JSONObject jsonObject = JsonUtils.parseOrNewJson(jsonRequest);
        if (!jsonObject.containsKey("corpId") || !jsonObject.containsKey("userId")) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return asyncClock(jsonObject.getString("corpId"), jsonObject.getString("userId"));
    }

    @Override
    @AteyeInvoker(description = "更新企微成员新人任务完成状态", paraDesc = "corpId&userId&activateStatus")
    public Integer updateActivateStatus(String corpId, String userId, Integer activateStatus) {
        WechatUserDO wechatUserDO = new WechatUserDO();
        wechatUserDO.setCorpId(corpId);
        wechatUserDO.setUserId(userId);
        wechatUserDO.setActivateStatus(activateStatus.byteValue());
        return wechatUserMapper.updateByUserIdAndCorpId(wechatUserDO);
    }

    @Override
    public List<String> getAutoMatchUserIdList(TaskInfoDO taskInfoDO) {
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        if (extraInfo.containsKey("wechatUserMatchStrategy") && Objects.equals(Integer.parseInt(extraInfo.get("wechatUserMatchStrategy").toString()), WechatUserMatchStrategyEnum.SMART.getCode())) {
            WechatUserQuery query = new WechatUserQuery();
            query.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
            query.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
            List<WechatUserDTO> WechatUserList = listValidWechatUserByCondition(query);
            if (CollectionUtils.isEmpty(WechatUserList)) {
                return new ArrayList<>();
            }
            if (RiskModuleEnum.fromCode(taskInfoDO.getType()) != null) {
                // 获取风控信息
                Map<String, RiskStatusEnum> riskStatusMap = riskActionChecker.showUserRiskStatus(WechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()), SpaceInfoThreadLocalUtils.getCorpId(), taskInfoDO.getType());
                // 过滤出风控信息为normal的机器人
                return WechatUserList.stream()
                        .filter(x -> RiskStatusEnum.NORMAL.equals(riskStatusMap.get(RiskObjectDO.buildRobotTargetId(x.getCorpId(), x.getUserId()))))
                        .map(WechatUserDTO::getUserId)
                        .collect(Collectors.toList());
            } else {
                return WechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }

    @Override
    public Boolean needCheckSaveInContact(String userId, String corpId) {
        WechatUserDTO wechatUserDTO = wechatUserOpenSearchManager.getByUserId(userId, SpaceInfoThreadLocalUtils.getCorpId());
        if (Objects.isNull(wechatUserDTO)) {
            return false;
        }

        Integer onlineIsvType = wechatUserDTO.getOnlineIsvType();
        return Objects.equals(onlineIsvType, IsvTypeEnum.BI_LIN.getCode());
    }

    @Override
    public Boolean needCheckOpenWorkGroup(String userId, String corpId) {
        return needCheckSaveInContact(userId, corpId);
    }

    @Override
    @Cacheable(cacheNames = "oneMinutesAnd128MaximumSizeCacheManager", key = "'user_id:' + #userId")
    @AteyeInvoker(description = "根据userId查询企微成员", paraDesc = "userId")
    public List<WechatUserDTO> listByUserIdWithCache(String userId) {
        if (!StringUtils.hasLength(userId)) {
            PlatformLogUtil.logFail("根据userId查询企微成员信息失败，userId为空", Lists.newArrayList(userId));
            return null;
        }
        WechatUserQuery query = new WechatUserQuery();
        query.setUserIdList(Lists.newArrayList(userId));
        List<WechatUserDTO> wechatUserDTOList = listByCondition(query);
        if (CollectionUtils.isEmpty(wechatUserDTOList)) {
            PlatformLogUtil.logFail("根据userId查询企微成员信息失败，查询结果为空", Lists.newArrayList(userId));
            return null;
        }
        return wechatUserDTOList;
    }

    private boolean hasOperatePermission() {
        SpaceInfo spaceInfo = SpaceInfoThreadLocalUtils.getSpaceInfo();
        return Lists.newArrayList(RoleTypeEnum.SUPER_ADMIN, RoleTypeEnum.ADMIN).contains(spaceInfo.getRoleType());
    }

    private boolean hasUsePermission(SpaceResourceDO spaceResourceDO, User account) {
        return Arrays.stream(spaceResourceDO.getAdminList().split(","))
                .anyMatch(x -> Objects.equals(x, account.getUserId()))
                || Arrays.stream(spaceResourceDO.getMemberList().split(","))
                .anyMatch(x -> Objects.equals(x, account.getUserId()));
    }

    private boolean hasOperatePermission(SpaceResourceDO spaceResourceDO) {
        return hasOperatePermission() ||
                Arrays.stream(spaceResourceDO.getAdminList().split(","))
                        .anyMatch(x -> Objects.equals(x, accountService.getUserInWebThread().getUserId()));
    }

    @AteyeInvoker(description = "批量查询企微成员", paraDesc = "userIdList")
    public List<WechatUserDTO> listByUserIdList(String userIdList) {
        return listById(Lists.newArrayList(userIdList.split(",")));
    }
}
