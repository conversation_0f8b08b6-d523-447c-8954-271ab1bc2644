package com.alibaba.tripscrm.service.manager.second;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.fliggy.tccp.client.api.model.OrderDTO;
import com.fliggy.tccp.client.api.query.TccpQueryService;
import com.fliggy.tccp.client.api.query.request.QueryInfoType;
import com.fliggy.tccp.client.api.query.response.QueryOrderResponse;
import com.fliggy.tccp.limit.enums.LimitSceneEnum;
import com.fliggy.tccp.limit.enums.SourceFromEnum;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.hsf.context.RPCContext;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/13 19:23
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TccpManager {
    private final TccpQueryService tccpQueryService;

    /**
     * 查询订单信息
     *
     * @param orderId 订单id
     * @param userId  用户id
     * @return 订单金额
     */
    @AteyeInvoker(description = "查询tccp订单信息", paraDesc = "orderId&userId&queryVertical")
    public OrderDTO queryOrderDTO(long orderId, long userId, boolean queryVertical) {
        if (!NumberUtils.biggerThanZero(orderId) || !NumberUtils.biggerThanZero(userId)) {
            PlatformLogUtil.logFail("查询tccp订单信息，orderId或userId非法", TripSCRMErrorCode.INVALID_PARAMS.getCode(), LogListUtil.newArrayList(orderId, userId));
            return null;
        }

        RPCContext rpcContext = RPCContext.getClientContext();
        rpcContext.putAttachment("tccpLimitSourceFrom", SourceFromEnum.COMMON.getCode());
        rpcContext.putAttachment("tccpLimitScene", LimitSceneEnum.NOT_LIMIT.getCode());
        QueryOrderResponse queryOrderResponse;
        if (queryVertical) {
            queryOrderResponse = tccpQueryService.queryOrderByOrderId(orderId, userId, QueryInfoType.NEW_QUERY_ORDER_ITEM, QueryInfoType.NEW_QUERY_PASSENGER, QueryInfoType.NEW_QUERY_RESOURCE, QueryInfoType.NEW_QUERY_VERTICAL);
        } else {
            queryOrderResponse = tccpQueryService.queryOrderByOrderId(orderId, userId, QueryInfoType.NEW_QUERY_ORDER_ITEM, QueryInfoType.NEW_QUERY_PASSENGER, QueryInfoType.NEW_QUERY_RESOURCE);
        }

        if (Objects.isNull(queryOrderResponse) || !queryOrderResponse.isSuccess()) {
            PlatformLogUtil.logFail("查询tccp订单信息，查询失败", TripSCRMErrorCode.PROCESS_FAILED.getCode(), LogListUtil.newArrayList(orderId, userId, queryOrderResponse));
            return null;
        }

        OrderDTO orderDTO = queryOrderResponse.getOrderDTO();
        if (orderDTO == null) {
            PlatformLogUtil.logFail("查询tccp订单信息，OrderDTO为空", TripSCRMErrorCode.PROCESS_FAILED.getCode(), LogListUtil.newArrayList(orderId, userId, queryOrderResponse));
            return null;
        }

        return orderDTO;
    }
}
