package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateParam;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.UpdateUserConversationConfigRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.UpdateUserConversationConfigResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.List;

/**
 * 更新用户会话配置 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class UpdateUserConversationConfigProcessor implements WsEventProcessor {
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private WechatUserService wechatUserService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.UPDATE_USER_CONVERSATION_CONFIG;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        UpdateUserConversationConfigRequest request = wsEvent.getData().toJavaObject(UpdateUserConversationConfigRequest.class);
        if (wsEvent.getUserId() == null || request.getChatId() == null || request.getChatType() == null) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        if (!wechatUserService.checkHasLock(wsEvent.getUserId(), wsContextInfo.getAccount().getUserId(), wsContextInfo.getSpaceId())) {
            throw new TripscrmException("未锁定当前企微账号，无法执行该操作");
        }

        // 更新用户会话配置
        ChatConversationUpdateParam updateParam = new ChatConversationUpdateParam();
        updateParam.setUserId(wsEvent.getUserId());
        updateParam.setCorpId(wsEvent.getCorpId());
        updateParam.setChatId(request.getChatId());
        updateParam.setChatType(request.getChatType());
        updateParam.setTop(request.getTop());
        updateParam.setDnd(request.getDnd());
        updateParam.setEnterChat(request.getEnterChat());
        updateParam.setCloseChat(request.getCloseChat());
        ChatConversationDO conversation = chatConversationService.updateChatConversationConfig(updateParam);
        // 推送结果
        UpdateUserConversationConfigResponse response = new UpdateUserConversationConfigResponse();
        response.setChatId(request.getChatId());
        response.setChatType(request.getChatType());
        response.setTopNo(conversation.getTopNo());
        response.setDnd(request.getDnd());
        response.setEnterChat(request.getEnterChat());
        response.setCloseChat(request.getCloseChat());
        response.setUpdateTimestamp(conversation.getUpdateTimestamp());
        response.setUnreadCount(conversation.getUnreadCount());
        response.setAllUnreadCount(chatConversationService.getWechatUserUnreadCount(wsEvent.getCorpId(), wsEvent.getUserId()));
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));

        Long spaceId = wsContextInfo.getSpaceId();
        // 寻找当前企微号有关系的account列表，推送到websocket
        List<String> accountIds = wechatUserService.listAccountIdWithPermission(wsEvent.getUserId(), spaceId);
        webSocketFactory.pushMessageByDistributedWithAccount(wsEvent, accountIds);
    }
}
