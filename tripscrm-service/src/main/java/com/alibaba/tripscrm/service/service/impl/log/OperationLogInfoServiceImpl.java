package com.alibaba.tripscrm.service.service.impl.log;

import com.alibaba.tripscrm.dal.mapper.tddl.OperationLogInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoParam;
import com.alibaba.tripscrm.service.enums.system.OperationLogStatusEnum;
import com.alibaba.tripscrm.service.model.domain.query.OperationLogInfoQuery;
import com.alibaba.tripscrm.service.service.log.OperationLogInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/12 23:39
 **/
@Service
public class OperationLogInfoServiceImpl implements OperationLogInfoService {

    @Resource
    private OperationLogInfoMapper operationLogInfoMapper;

    @Override
    public OperationLogInfoDO queryById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return null;
        }
        return operationLogInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public Boolean insert(OperationLogInfoDO operationLogInfoDO) {
        if (operationLogInfoDO == null) {
            return false;
        }
        return operationLogInfoMapper.insert(operationLogInfoDO) > 0;
    }

    @Override
    @AteyeInvoker(description = "删除日志信息", paraDesc = "id")
    public Boolean deleteById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return false;
        }
        return operationLogInfoMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public Boolean updateById(OperationLogInfoDO operationLogInfoDO) {
        if (operationLogInfoDO == null || operationLogInfoDO.getId() == null) {
            return null;
        }
        return operationLogInfoMapper.updateByPrimaryKeySelective(operationLogInfoDO) > 0;
    }

    @Override
    public Boolean updateStatusById(Long id, OperationLogStatusEnum statusEnum) {
        OperationLogInfoDO operationLogInfoDO = new OperationLogInfoDO();
        operationLogInfoDO.setId(id);
        operationLogInfoDO.setOperatorStatus(statusEnum.getCode());
        return operationLogInfoMapper.updateByPrimaryKeySelective(operationLogInfoDO) > 0;
    }

    /**
     * 条件查询数量
     * @param query 查询条件
     * @return 数量
     */
    @Override
    public Long countByQuery(OperationLogInfoQuery query) {
        OperationLogInfoParam param = new OperationLogInfoParam();
        OperationLogInfoParam.Criteria criteria = param.createCriteria();
        if (query.getOperationLogType() != null) {
            criteria.andTypeEqualTo(query.getOperationLogType());
        }
        if (query.getTargetId() != null) {
            criteria.andTargetIdEqualTo(query.getTargetId());
        }
        if (query.getGroupId() != null) {
            criteria.andGroupIdEqualTo(query.getGroupId());
        }
        return operationLogInfoMapper.countByParam(param);
    }

    @Override
    public List<OperationLogInfoDO> queryLogInfoDOList(OperationLogInfoQuery query) {
        OperationLogInfoParam param = new OperationLogInfoParam();
        param.setPage(query.isPage());
        param.setPageSize(query.getPageSize());
        param.setPageStart(query.getPageNum());
        OperationLogInfoParam.Criteria criteria = param.createCriteria();
        if (query.getOperationLogType() != null) {
            criteria.andTypeEqualTo(query.getOperationLogType());
        }
        if (query.getTargetId() != null) {
            criteria.andTargetIdEqualTo(query.getTargetId());
        }
        param.appendOrderByClause(OperationLogInfoParam.OrderCondition.GMTCREATE, OperationLogInfoParam.SortType.DESC);
        return operationLogInfoMapper.selectByParam(param);
    }

    @Override
    public List<OperationLogInfoDO> queryLogInfoListByGroupId(Long groupId) {
        if (!NumberUtils.validLong(groupId)) {
            return Lists.newArrayList();
        }
        ArrayList<OperationLogInfoDO> operationLogInfoList = new ArrayList<>();
        for (long minId = 0; ;) {
            List<OperationLogInfoDO> logInfoList = operationLogInfoMapper.selectByGroupId(groupId, minId);
            if (CollectionUtils.isEmpty(logInfoList)) {
                break;
            }
            minId = logInfoList.stream().map(OperationLogInfoDO::getId).max(Long::compareTo).orElse(0L);
            operationLogInfoList.addAll(logInfoList);
        }
        return operationLogInfoList;
    }

}
