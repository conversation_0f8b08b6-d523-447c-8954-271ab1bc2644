package com.alibaba.tripscrm.service.service.impl.task;

import com.alibaba.schedulerx.common.util.IpUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.TaskInstanceMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskInstanceQuery;
import com.alibaba.tripscrm.service.convert.TaskInstanceConverter;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.TaskInstanceSaveRequest;
import com.alibaba.tripscrm.service.model.vo.task.TaskInstanceExecuteResultVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class TaskInstanceServiceImpl implements TaskInstanceService {

    @Resource
    private TaskInstanceMapper taskInstanceMapper;
    @Resource
    private TaskInstanceService taskInstanceService;
    @Resource
    private SubTaskInstanceService subTaskInstanceService;
    @Resource
    private TaskService taskService;

    @Override
    public TaskInstanceDO getNewest(TaskInstanceQuery query) {
        return taskInstanceMapper.getNewestByCondition(query);
    }

    @Override
    public TaskInstanceExecuteResultVO getExecuteResult(Long id) {
        if (!NumberUtils.validLong(id)) {
            return null;
        }

        TaskInstanceExecuteResultVO result = new TaskInstanceExecuteResultVO();

        TaskInstanceDO taskInstanceDO = queryById(id);
        result.setScheduleTime(taskInstanceDO.getStartTime());
        result.setStatus(taskInstanceDO.getStatus());

        SubTaskInstanceQueryRequest request = new SubTaskInstanceQueryRequest();
        request.setMainTaskInstanceId(id);
        List<SubTaskInstanceDO> subTaskInstanceDOList = subTaskInstanceService.list(request);
        for (SubTaskInstanceDO subTaskInstanceDO : subTaskInstanceDOList) {
            TaskInstanceExecuteResultVO subTaskExecuteResult = subTaskInstanceService.getExecuteResult(subTaskInstanceDO);
            if (Objects.nonNull(subTaskExecuteResult.getAll())) {
                result.setAll(Optional.ofNullable(result.getAll()).orElse(0) + subTaskExecuteResult.getAll());
            }
            if (Objects.nonNull(subTaskExecuteResult.getSuccess())) {
                result.setSuccess(Optional.ofNullable(result.getSuccess()).orElse(0) + subTaskExecuteResult.getSuccess());
            }
            if (Objects.nonNull(subTaskExecuteResult.getFail())) {
                result.setFail(Optional.ofNullable(result.getFail()).orElse(0) + subTaskExecuteResult.getFail());
            }
            if (Objects.nonNull(subTaskExecuteResult.getWaitRetry())) {
                result.setWaitRetry(Optional.ofNullable(result.getWaitRetry()).orElse(0) + subTaskExecuteResult.getWaitRetry());
            }
            if (Objects.nonNull(subTaskExecuteResult.getAsyncSuccess())) {
                result.setAsyncSuccess(Optional.ofNullable(result.getAsyncSuccess()).orElse(0) + subTaskExecuteResult.getAsyncSuccess());
            }
            if (Objects.nonNull(subTaskExecuteResult.getAsyncFail())) {
                result.setAsyncFail(Optional.ofNullable(result.getAsyncFail()).orElse(0) + subTaskExecuteResult.getAsyncFail());
            }
        }

        return result;
    }

    @Override
    public Long add(TaskInstanceSaveRequest request) {
        TaskInstanceDO instanceDO = TaskInstanceConverter.saveRequest2Do(
            request, () -> taskService.queryTaskById(request.getTaskId()));
        taskInstanceMapper.insert(instanceDO);
        return instanceDO.getId();
    }

    @Override
    public TaskInstanceDO queryById(Long id) {
        return taskInstanceMapper.selectByPrimaryKey(id);
    }

    @AteyeInvoker(description = "根据id查询任务实例（走缓存）", paraDesc = "id")
    public List<TaskInstanceDO> testListWithCache(Long taskId, String taskInstanceStatus) {
        TaskInstanceQuery query = new TaskInstanceQuery();
        query.setTaskId(taskId);
        query.setStatus(taskInstanceStatus);
        return taskInstanceService.listWithCache(query);
    }

    @Override
    @Cacheable(key = "'taskId:' + #query.taskId + ',status:' + #query.status", value = "taskInstanceListCacheManager", unless = "#result == null || #result.size() == 0")
    public List<TaskInstanceDO> listWithCache(TaskInstanceQuery query) {
        List<TaskInstanceDO> list = list(query);
        PlatformLogUtil.logFail("根据条件查询任务实例（未命中缓存）", LogListUtil.newArrayList(query, list));
        return list;
    }

    @Override
    public List<TaskInstanceDO> list(TaskInstanceQuery query) {
        return taskInstanceMapper.selectByCondition(query);
    }

    @AteyeInvoker(description = "更新任务实例状态")
    public Boolean updateStatus(Long id, String status) {
        return updateStatus(id, TaskInstanceStatusEnum.getByStatus(status));
    }

    @Override
    @TairLock(value = "'updateMainTaskStatus_' + #id", waitMilliseconds = 2000)
    public Boolean updateStatus(Long id, TaskInstanceStatusEnum taskInstanceStatus) {
        if (!NumberUtils.validLong(id) || taskInstanceStatus == null) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TaskInstanceDO instanceDO = queryById(id);
        if (Objects.isNull(instanceDO)) {
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MAIN_TASK_INSTANCE);
        }

        TaskInstanceStatusEnum currentStatus = TaskInstanceStatusEnum.getByStatus(instanceDO.getStatus());
        if (Objects.equals(taskInstanceStatus, currentStatus)) {
            return true;
        }

        TaskInstanceDO updateDO = new TaskInstanceDO();
        updateDO.setId(id);
        updateDO.setStatus(taskInstanceStatus.getStatus());
        updateDO.setIpAddress(IpUtil.getIPV4Address());

        switch (taskInstanceStatus) {
            case RUNNING:
                // 只有待运行才能修改为运行中
                if (!TaskInstanceStatusEnum.READY.equals(currentStatus)) {
                    throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
                }
                updateDO.setStartTime(new Date());
                return taskInstanceMapper.updateSelectiveByPrimaryKey(updateDO) >= 1;
            case RUN_SUCCESS:
            case RUN_FAIL:
                // 只有待运行和运行中才能修改为成功或失败
                if (!Lists.newArrayList(TaskInstanceStatusEnum.RUNNING, TaskInstanceStatusEnum.READY).contains(currentStatus)) {
                    throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
                }
                updateDO.setEndTime(new Date());
                return taskInstanceMapper.updateSelectiveByPrimaryKey(updateDO) >= 1;
            case TIMEOUT:
                // 只有运行中才能修改为超时
                if (!Lists.newArrayList(TaskInstanceStatusEnum.RUNNING).contains(currentStatus)) {
                    throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
                }
                updateDO.setEndTime(new Date());
                return taskInstanceMapper.updateSelectiveByPrimaryKey(updateDO) >= 1;
            default:
                throw new TripscrmException(TripSCRMErrorCode.UN_SUPPORTED_TASK_INSTANCE_STATUS_CHANGE);
        }
    }

    @Override
    public void offlineTaskInstanceByTaskId(Long taskId, TaskInstanceStatusEnum newTaskInstanceStatus) {
        TaskInstanceQuery query = new TaskInstanceQuery();
        query.setTaskId(taskId);
        query.setStatus(TaskInstanceStatusEnum.RUNNING.getStatus());
        List<TaskInstanceDO> oldTaskInstanceList = list(query);
        if (CollectionUtils.isEmpty(oldTaskInstanceList)) {
            return;
        }

        for (TaskInstanceDO taskInstanceDO : oldTaskInstanceList) {
            if (!updateStatus(taskInstanceDO.getId(), newTaskInstanceStatus)) {
                PlatformLogUtil.logFail("任务实例下线失败", LogListUtil.newArrayList(taskId));
                throw new RuntimeException("任务实例下线失败");
            }
        }
    }

    @Override
    public Integer deleteById(Long id) {
        return taskInstanceMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Cacheable(key = "'getTaskIdByTaskInstanceId:' + #taskInstanceId", value = "taskInstanceId2TaskIdCacheManager", unless = "#result == null")
    public Long getTaskIdByTaskInstanceId(Long taskInstanceId) {
        return Optional.ofNullable(queryById(taskInstanceId)).map(TaskInstanceDO::getTaskId).orElse(null);
    }

    @Override
    public Boolean updateByPrimaryKey(TaskInstanceDO taskInstanceDO) {
        if (Objects.isNull(taskInstanceDO) || !NumberUtils.validLong(taskInstanceDO.getId())) {
            return false;
        }
        return taskInstanceMapper.updateSelectiveByPrimaryKey(taskInstanceDO) >= 1;
    }
}
