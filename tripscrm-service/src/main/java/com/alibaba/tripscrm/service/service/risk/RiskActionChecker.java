package com.alibaba.tripscrm.service.service.risk;

import com.alibaba.tripscrm.dal.model.domain.data.RiskObjectDO;
import com.alibaba.tripscrm.dal.repository.RiskObjectRepository;
import com.alibaba.tripscrm.service.enums.risk.*;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.risk.ActionCheckResult;
import com.alibaba.tripscrm.service.service.risk.status.RiskActionStatusController;
import com.alibaba.tripscrm.service.service.risk.status.RiskRobotStatusController;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 风控检查器
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class RiskActionChecker {
    @Resource
    private RiskObjectRepository riskObjectRepository;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private RiskRobotStatusController riskRobotStatusController;
    @Resource
    private RiskActionStatusController riskActionStatusController;

    /**
     * 【前台界面降级】用户界面操作功能模块时看到的风控状态
     *
     * @param userIdList userIdList
     * @return return
     */
    public Map<String, RiskStatusEnum> showUserRiskStatus(List<String> userIdList, String corpId, String riskModuleCode) {
        Map<String, RiskStatusEnum> result = new HashMap<>(userIdList.size());
        List<String> targetIds = userIdList.stream().map(x -> RiskObjectDO.buildRobotTargetId(corpId, x)).collect(Collectors.toList());
        // 获取机器人风控状态
        List<RiskObjectDO> robotRiskObjects = riskObjectRepository.listByTargetIds(targetIds, RiskTargetEnum.ROBOT.getValue());
        Map<String, RiskObjectDO> robotRiskObjectMap = robotRiskObjects.stream().collect(Collectors.toMap(RiskObjectDO::getTargetId, Function.identity()));
        // 机器人状态映射
        for (String userId : userIdList) {
            String robotTargetId = RiskObjectDO.buildRobotTargetId(corpId, userId);
            RiskObjectDO riskObject = robotRiskObjectMap.get(robotTargetId);
            if (riskObject == null) {
                result.put(robotTargetId, RiskStatusEnum.NORMAL);
            } else {
                result.put(robotTargetId, RiskStatusEnum.of(riskObject.getRiskStatus()));
            }
        }

        if (Objects.isNull(riskModuleCode)) {
            return result;
        }

        // 获取行动项风控状态
        List<RiskObjectDO> actionRiskObjects = riskObjectRepository.listByGroups(
                userIdList.stream().map(x -> RiskObjectDO.buildRobotTargetId(corpId, x)).collect(Collectors.toList()), RiskTargetEnum.ACTION.getValue());
        Map<String, RiskObjectDO> actionRiskObjectMap = actionRiskObjects.stream().collect(Collectors.toMap(RiskObjectDO::getTargetId, Function.identity()));
        // 根据模块获取关联的行动项
        RiskModuleEnum riskModule = RiskModuleEnum.fromCode(riskModuleCode);
        if (Objects.isNull(riskModule)) {
            return result;
        }

        List<RiskActionEnum> riskActions = riskModule.getRiskActions();
        // 行动项状态映射覆盖（在用户界面操作功能时，关联行动项的风控状态将覆盖父节点的状态）
        for (String userId: userIdList) {
            String robotTargetId = RiskObjectDO.buildRobotTargetId(corpId, userId);
            // 仅机器人状态为风控可用的才做行动项替换逻辑
            RiskStatusEnum riskStatus = result.get(robotTargetId);
            if (riskStatus != RiskStatusEnum.NORMAL) {
                continue;
            }

            // 非空说明取到行动项的风控规则
            for (RiskActionEnum riskAction : riskActions) {
                String actionTargetId = RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode());
                RiskObjectDO actionRiskObject = actionRiskObjectMap.get(actionTargetId);
                if (actionRiskObject != null) {
                    result.put(robotTargetId, RiskStatusEnum.of(actionRiskObject.getRiskStatus()));
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 【接口风控层降级】风控校验，根据行动项返回风控结果
     *
     * @param corpId     corpId
     * @param userId     userId
     * @param riskAction riskAction
     * @return 风控检查结果
     */
    public ActionCheckResult riskCheck(String corpId, String userId, RiskActionEnum riskAction) {
        // 获取账号风控状态
        RiskStatusEnum robotRiskStatus = riskRobotStatusController.get(corpId, userId);
        // 获取行动项风控状态
        RiskStatusEnum actionRiskStatus = riskActionStatusController.get(corpId, userId, riskAction);

        // 账号正常可用
        if (robotRiskStatus == RiskStatusEnum.NORMAL && actionRiskStatus == RiskStatusEnum.NORMAL) {
            PlatformLogUtil.logInfo("风控控制-风控校验-行动项可用", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
            return new ActionCheckResult(RiskStatusEnum.NORMAL, true, null);
        }

        // 账号脱离风控
        if (robotRiskStatus == RiskStatusEnum.ESCAPE || (robotRiskStatus == RiskStatusEnum.NORMAL && actionRiskStatus == RiskStatusEnum.ESCAPE)) {
            PlatformLogUtil.logInfo("风控控制-风控校验-该账号脱离风控", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
            if (robotRiskStatus == RiskStatusEnum.NORMAL) {
                return new ActionCheckResult(RiskStatusEnum.ESCAPE, true, RiskInvokeSubStatusEnum.ESCAPE_FOR_ACTION);
            } else {
                return new ActionCheckResult(RiskStatusEnum.ESCAPE, false, RiskInvokeSubStatusEnum.ESCAPE_FOR_ROBOT);
            }
        }

        // 账号风控挂起
        if (robotRiskStatus == RiskStatusEnum.HOLD || (robotRiskStatus == RiskStatusEnum.NORMAL && actionRiskStatus == RiskStatusEnum.HOLD)) {
            PlatformLogUtil.logInfo("风控控制-风控校验-该账号风控挂起", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
            if (robotRiskStatus == RiskStatusEnum.NORMAL) {
                return new ActionCheckResult(RiskStatusEnum.HOLD, true, RiskInvokeSubStatusEnum.HOLD_FOR_ACTION);
            } else {
                return new ActionCheckResult(RiskStatusEnum.HOLD, false, RiskInvokeSubStatusEnum.HOLD_FOR_ROBOT);
            }
        }

        // 账号风控保护
        if (robotRiskStatus == RiskStatusEnum.PROTECT || (robotRiskStatus == RiskStatusEnum.NORMAL && actionRiskStatus == RiskStatusEnum.PROTECT)) {
            PlatformLogUtil.logInfo("风控控制-风控校验-该账号风控保护", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
            if (robotRiskStatus == RiskStatusEnum.NORMAL) {
                Integer limitType = (Integer) ldbTairManager.get(riskActionStatusController.buildLimitTypeKey(corpId, userId, riskAction));
                return new ActionCheckResult(RiskStatusEnum.PROTECT, true, RateLimitTypeEnum.fromValue(limitType).getInvokeSubStatusForProtect());
            } else {
                return new ActionCheckResult(RiskStatusEnum.PROTECT, false, RiskInvokeSubStatusEnum.PROTECT_FOR_ROBOT);
            }
        }

        // 账号风控异常
        if (robotRiskStatus == RiskStatusEnum.ABNORMAL || (robotRiskStatus == RiskStatusEnum.NORMAL && actionRiskStatus == RiskStatusEnum.ABNORMAL)) {
            PlatformLogUtil.logInfo("风控控制-风控校验-该账号风控异常", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
            if (robotRiskStatus == RiskStatusEnum.NORMAL) {
                return new ActionCheckResult(RiskStatusEnum.ABNORMAL, true, RiskInvokeSubStatusEnum.ABNORMAL_FOR_ROBOT);
            } else {
                return new ActionCheckResult(RiskStatusEnum.PROTECT, false, RiskInvokeSubStatusEnum.ABNORMAL_FOR_ACTION);
            }
        }

        PlatformLogUtil.logInfo("风控控制-风控校验-行动项可用", LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
        return new ActionCheckResult(RiskStatusEnum.NORMAL, false, null);
    }
}

