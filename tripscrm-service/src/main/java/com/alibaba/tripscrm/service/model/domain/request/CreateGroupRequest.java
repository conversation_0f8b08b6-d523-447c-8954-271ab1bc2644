package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/1
 */
@Data
public class CreateGroupRequest implements Serializable {
    private static final long serialVersionUID = -4797153587517755296L;
    private String userId;
    private String groupName;
    private String notice;
    private List<String> initUserIdList;
    private String requestId;
    private MaterailInfoDO materailInfoDO;
    private MaterialTrackRelationDTO materialTrackRelationDTO;
    private MaterialContentConvertContext materialContentConvertContext;
    private TaskType taskType;
}
