package com.alibaba.tripscrm.service.service.impl.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.TaskInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.*;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskInstanceQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.TaskInfo;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMTaskInnerService;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.convert.ResourceRelationConverter;
import com.alibaba.tripscrm.service.enums.activity.ActivityStatusEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationWayEnum;
import com.alibaba.tripscrm.service.enums.task.*;
import com.alibaba.tripscrm.service.manager.OperationActivityManager;
import com.alibaba.tripscrm.service.manager.opensearch.TaskInfoOpenSearchManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.diamond.TaskCustomerConfigTemplateDiamond;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.TaskInfoQuery;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.TaskTagRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.dto.task.CrowdExpressionDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.model.vo.task.TaskSelfVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import com.alibaba.tripscrm.service.service.task.ability.config.TaskConfigProcessor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.*;
import com.alibaba.tripzoo.admin.client.domain.model.ActivityDTO;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.triptower.domain.PushCheckResultDO;
import com.taobao.triptower.service.scrm.ScrmCheckService;
import com.tmall.beehive.common.lang.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.TripSCRMErrorCode.NOT_EXIST_TASK_INFO_DATA;
import static com.alibaba.tripscrm.TripSCRMErrorCode.TASK_STATUS_NOT_ONLINE;
import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EXECUTION_PLAN;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.SINGLE_EXECUTE;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskServiceImpl implements TaskService {
    private final LdbTairManager ldbTairManager;
    private final TaskInfoMapper taskInfoMapper;
    private final ActivityInfoService activityInfoService;
    private final TaskCustomerConfigTemplateDiamond taskCustomerConfigTemplateDiamond;
    private final ScrmCheckService scrmCheckService;
    private final TripSCRMTaskInnerService innerService;
    private final TaskInfoOpenSearchManager taskInfoOpenSearchManager;
    private final AccountService accountService;
    private final ActivityContextService activityContextService;
    private final ResourceRelationConverter resourceRelationConverter;
    private final ResourceRelationService resourceRelationService;
    private final TaskExecuteStrategy taskExecuteStrategy;
    private final TaskService taskService;
    private final SpaceService spaceService;
    private final TagInfoService tagInfoService;
    private final MaterialService materialService;
    private final MaterialTransferService materialTransferService;
    private final OperationActivityManager operationActivityManager;

    @Resource
    @Lazy
    private TaskInstanceService taskInstanceService;

    @Resource
    @Lazy
    private SubTaskInstanceService subTaskInstanceService;

    @Value("${task.execute.interval:10}")
    private Integer interval;

    @Switch(description = "工作窗口开始时间", name = "workWindowStartTime")
    private static String workWindowStartTime = "08:00";
    @Switch(description = "工作窗口结束时间", name = "workWindowEndTime")
    private static String workWindowEndTime = "22:00";

    @Override
    public List<TaskInfoDO> query(TaskQuery query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("任务服务，查询任务入参为空", LogListUtil.newArrayList());
                return new ArrayList<>();
            }
            query.setEnv(EnvUtils.getEnvironment());
            query.setDeleted(0);
            return Optional.ofNullable(taskInfoMapper.select(query)).orElse(new ArrayList<>());
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，查询任务异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return new ArrayList<>();
        }
    }

    @Override
    public PageInfo<TaskInfoDO> pageQuery(TaskInfoQuery query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("任务服务，分页查询任务入参为空", LogListUtil.newArrayList());
                return null;
            }
            query.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
            query.setEnv(EnvUtils.getEnvironment());
            if (Objects.nonNull(query.getCreateEndTime())) {
                // createEndTime的后一天
                query.setCreateEndTime(DateUtils.addDays(query.getCreateEndTime(), 1));
            }
            // openSearch查出任务Id
            PageInfo<Long> pageInfo = taskInfoOpenSearchManager.getTaskIdList(query);
            List<Long> taskIdList = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());
            if (CollectionUtils.isEmpty(taskIdList)) {
                return PageUtils.getPageInfo(pageInfo.getPageNum(), pageInfo.getPageSize(), (int) pageInfo.getTotal(), new ArrayList<>());
            }

            // 回表
            TaskQuery taskQuery = new TaskQuery();
            taskQuery.setIdList(taskIdList);
            List<TaskInfoDO> list = taskInfoMapper.select(taskQuery);
            return PageUtils.getPageInfo(pageInfo.getPageNum(), pageInfo.getPageSize(), (int) pageInfo.getTotal(), list);
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，分页查询任务异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    public TaskInfoDO queryTaskByNameAndSpaceId(String name, Long spaceId) {
        try {
            if (!StringUtils.hasText(name)) {
                PlatformLogUtil.logFail("任务服务，根据空间id和任务名称查询任务，任务名称校验失败", LogListUtil.newArrayList(name, spaceId));
                return null;
            }

            return taskInfoMapper.selectByNameAndSpaceIdAndEnv(name, spaceId, EnvUtils.getEnvironment());
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务根据空间id和任务名称查询任务，出现异常", e.getMessage(), e, LogListUtil.newArrayList(name, spaceId));
            return null;
        }
    }

    @Override
    public boolean checkDuplicateName(Long spaceId, String name, Long id) {
        try {
            if (!StringUtils.hasText(name)) {
                PlatformLogUtil.logFail("任务服务，名称判重，名称校验失败", LogListUtil.newArrayList(name, spaceId, id));
                return false;
            }
            TaskInfoDO taskInfoDO = taskInfoMapper.selectByNameAndSpaceIdAndPrimaryKeyAndEnv(name, spaceId, id, EnvUtils.getEnvironment());

            return Objects.nonNull(taskInfoDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，名称判重，过程异常", e.getMessage(), e, LogListUtil.newArrayList(name, spaceId, id));
            return false;
        }
    }

    @Override
    public Integer updateActivityIdWhereActivityIsNull(Long activityId) {
        return taskInfoMapper.updateActivityIdWhereActivityIsNull(activityId);
    }

    @Override
    public String getCustomerConfigTemplate(TaskType taskType) {
        return taskCustomerConfigTemplateDiamond.getTemplate(taskType);
    }

    @Override
    public TaskInfoDO queryTaskByIdAndSpaceId(Long id, Long spaceId) {
        try {
            TaskQuery query = new TaskQuery();
            query.setId(id);
            query.setSpaceId(spaceId);
            query.setEnv(EnvUtils.getEnvironment());
            List<TaskInfoDO> list = taskInfoMapper.select(query);
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }

            return list.get(0);
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，根据任务id和空间id查询任务异常", e.getMessage(), e, LogListUtil.newArrayList(spaceId, id));
            throw e;
        }
    }

    @Override
    @Cacheable(key = "'id:' + #id", value = "taskCacheManager", unless = "#result == null")
    public Long querySpaceIdByTaskIdWithCache(Long id) {
        TaskInfoDO taskInfoDO = queryTaskById(id);
        return Optional.ofNullable(taskInfoDO).map(TaskInfoDO::getSpaceId).orElse(null);
    }

    @Override
    public TaskInfoDO queryTaskById(Long id) {
        try {
            if (!NumberUtils.validLong(id)) {
                PlatformLogUtil.logFail("任务服务，根据任务id获取任务，id校验失败", LogListUtil.newArrayList(id));
                return null;
            }

            return taskInfoMapper.selectByPrimaryKey(id);
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，根据任务id获取任务，过程异常", e.getMessage(), e, LogListUtil.newArrayList(id));
            throw e;
        }
    }

    @Override
    @Cacheable(key = "'eventSource:' + #eventSourceId", value = "eventSourceBindTaskCacheManager")
    public List<TaskInfoDO> listByEventSourceIdAndStatus(Long eventSourceId, TaskStatusEnum status) {
        try {
            if (!NumberUtils.validLong(eventSourceId)) {
                return new ArrayList<>();
            }

            TaskQuery query = new TaskQuery();
            query.setEnv(EnvUtils.getEnvironment());
            query.setTriggerType(TaskTriggerTypeEnum.EVENT.getCode());
            query.setStatus(status.getStatus());
            query.setEventSourceId(eventSourceId);
            query.setDeleted(0);
            return taskInfoMapper.select(query);
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，根据事件源id和状态获取任务，过程异常", e.getMessage(), e, LogListUtil.newArrayList(eventSourceId));
            throw e;
        }
    }

    @Override
    public List<TaskInfoDO> list(TaskQuery query) {
        try {
            if (Objects.isNull(query)) {
                PlatformLogUtil.logFail("任务服务，条件查询任务列表，入参对象为空", LogListUtil.newArrayList(query));
                return null;
            }
            query.setEnv(EnvUtils.getEnvironment());
            return taskInfoMapper.select(query);
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，条件查询任务列表，过程异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            throw e;
        }
    }

    @Override
    public List<TaskInfoDO> queryTodoTaskList(TaskQuery query) throws Exception {
        if (Objects.isNull(query)) {
            query = new TaskQuery();
        }
        query.setEnv(EnvUtils.getEnvironment());
        query.setStatus(TaskStatusEnum.ONLINE.getStatus());
        query.setTriggerTypeList(Lists.newArrayList(EXECUTION_PLAN.getCode(), SINGLE_EXECUTE.getCode()));
        query.setEffect(true);
        query.setDeleted(0);
        List<TaskInfoDO> data = taskInfoMapper.queryTodoTaskList(query);
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        // 获取当前时间 分钟00秒
        Date now = org.apache.commons.lang3.time.DateUtils.truncate(new Date(), Calendar.MINUTE);
        // 获取时间分区游标
        Object cursor = ldbTairManager.get(TaskConstant.TASK_EXECUTE_TIME_SHARDING_KEY + "_" + EnvUtils.getEnvironment());
        PlatformLogUtil.logInfo("任务服务，条件查询待执行任务列表，获取时间分区游标", LogListUtil.newArrayList(cursor));
        long taskExecutedCursor = cursor != null ? (long) cursor : now.getTime();
        Date executedCursor = org.apache.commons.lang3.time.DateUtils.truncate(new Date(taskExecutedCursor), Calendar.MINUTE);

        // 记录被过滤的taskId列表
        List<Long> beFiltered = Lists.newArrayList();
        Iterator<TaskInfoDO> taskIterator = data.iterator();
        while (taskIterator.hasNext()) {
            TaskInfoDO taskInfoDO = taskIterator.next();
            // 筛选可执行的任务数据
            if (!check(taskInfoDO, now, executedCursor, query)) {
                beFiltered.add(taskInfoDO.getId());
                taskIterator.remove();
            }
        }

        // 记录哪些被过滤掉了
        if (!CollectionUtils.isEmpty(beFiltered)) {
            PlatformLogUtil.logInfo("任务服务，条件查询待执行任务列表，被过滤任务列表", LogListUtil.newArrayList(beFiltered));
        }

        return data;
    }

    @Override
    public List<TaskInfoDO> queryValidOnlineTaskList(TaskQuery query) throws Exception {
        if (Objects.isNull(query)) {
            query = new TaskQuery();
        }

        query.setEnv(EnvUtils.getEnvironment());
        query.setStatus(TaskStatusEnum.ONLINE.getStatus());
        query.setCheckEffectEndTime(true);
        query.setDeleted(0);
        return taskInfoMapper.queryTodoTaskList(query);
    }

    @Override
    @Transactional(propagation = Propagation.SUPPORTS)
    public Integer upsert(TaskInfoDO taskInfoDO) {
        if (Objects.isNull(taskInfoDO)) {
            PlatformLogUtil.logFail("任务服务，任务更新/插入，入参校验失败", LogListUtil.newArrayList(taskInfoDO));
            return null;
        }
        Long taskId = taskInfoDO.getId();
        if (!NumberUtils.validLong(taskId)) {
            // 填充默认配置
            TaskConfigProcessor taskConfigProcessor = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.getByCode(taskInfoDO.getType()));
            if (Objects.isNull(taskConfigProcessor)) {
                return addTaskInfo(taskInfoDO);
            }

            TaskInfoDO initTaskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.getByCode(taskInfoDO.getType())).getInitConfig(taskInfoDO.getActivityId(), taskInfoDO.getSpaceId(), taskInfoDO.getName());
            BeanCopyUtils.copyNonNullProperties(taskInfoDO, initTaskInfoDO);
            return addTaskInfo(initTaskInfoDO);
        }

        TaskInfoDO taskInfoDOInDb = queryTaskById(taskId);
        taskInfoDO.setActivityId(taskInfoDOInDb.getActivityId());
        taskInfoDO.setConfig(taskInfoDOInDb.getConfig());
        return updateTaskInfo(taskInfoDO, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addTaskInfo(TaskInfoDO taskInfoDO) {
        User user = accountService.getUserInWebThread();
        if (!checkConfigValid(taskInfoDO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        try {
            taskInfoDO.setEnv(EnvUtils.getEnvironment());
            taskInfoDO.setEventSourceId(NumberUtils.validLong(taskInfoDO.getEventSourceId()) ? taskInfoDO.getEventSourceId() : -1L);
            taskInfoDO.setDeleted(WxConstants.not_deleted);
            int rs = taskInfoMapper.insert(taskInfoDO);
            if (rs <= 0) {
                PlatformLogUtil.logFail("任务服务，任务新增失败", LogListUtil.newArrayList(user, taskInfoDO));
                return 0;
            }

            PlatformLogUtil.logInfo("任务服务，任务新增成功", LogListUtil.newArrayList(user, taskInfoDO));
            // 任务创建后的相关配置更新操作
            PlatformLogUtil.logInfo("任务服务，任务创建后的相关配置更新操作", LogListUtil.newArrayList(user, taskInfoDO.getId()));
            TaskConfigProcessorFactory.postCreate(taskInfoDO);
            return rs;
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，任务新增异常", e.getMessage(), e, LogListUtil.newArrayList(taskInfoDO));
            return 0;
        }
    }

    private boolean checkConfigValid(TaskInfoDO taskInfoDO) {
        if (Objects.isNull(taskInfoDO)) {
            return false;
        }

        if (!NumberUtils.validLong(taskInfoDO.getMaterialId())) {
            return true;
        }

        MaterailInfoDO materailInfoDO = materialService.queryById(taskInfoDO.getMaterialId());
        if (Objects.isNull(materailInfoDO) || materailInfoDO.getDeleted() == 1 || !StringUtils.hasText(materailInfoDO.getContent())) {
            return false;
        }

        List<MaterialSupplyDTO> supplyDTOList = materialTransferService.extractMaterialSupply(materailInfoDO.getContent());
        if (CollectionUtils.isEmpty(supplyDTOList)) {
            return true;
        }

        // 微信现金红包活动列表
        List<ActivityDTO> wechatRedPacketActivityList = supplyDTOList.stream()
                .filter(supplyDTO -> supplyDTO.getSupplyType().equals(MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY.getType()))
                .map(materialSupplyDTO -> operationActivityManager.queryById(Long.parseLong(materialSupplyDTO.getSupplyId())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wechatRedPacketActivityList)) {
            return true;
        }

        if (!Lists.newArrayList(TaskType.ROBOT_CHAT_MESSAGE, TaskType.GROUP_CHAT_MESSAGE, TaskType.AUTO_RESPONSE).contains(TaskType.getByCode(taskInfoDO.getType()))) {
            return false;
        }

        // 好友召回，定向类型不能为任务触达
        if (Objects.equals(TaskType.getByCode(taskInfoDO.getType()), TaskType.AUTO_RESPONSE)
                && wechatRedPacketActivityList.stream().anyMatch(x -> Objects.equals(x.getConfig().getInteger("targetTypeEnum"), 1))) {
            return false;
        }
        // 私聊/群聊任务，定向类型不能为好友召回
        if (!Objects.equals(TaskType.getByCode(taskInfoDO.getType()), TaskType.AUTO_RESPONSE)
                && wechatRedPacketActivityList.stream().anyMatch(x -> Objects.equals(x.getConfig().getInteger("targetTypeEnum"), 0))) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateTaskInfo(TaskInfoDO taskInfoDO, boolean preCheck, boolean doPost) {
        if (!checkConfigValid(taskInfoDO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        if (preCheck) {
            updatePreCheck(taskInfoDO);
        }

        User user = accountService.getUserInWebThread();
        try {
            // taskInfoDO.setDeleted(WxConstants.not_deleted);
            int rs = taskInfoMapper.updateByPrimaryKey(taskInfoDO);
            if (rs <= 0) {
                PlatformLogUtil.logFail("任务服务，任务更新失败", LogListUtil.newArrayList(user, taskInfoDO));
                return 0;
            }

            PlatformLogUtil.logInfo("任务服务，任务更新成功", LogListUtil.newArrayList(user, taskInfoDO));
            if (doPost) {
                // 任务更新后的相关配置更新操作
                PlatformLogUtil.logInfo("任务服务，任务更新后的相关配置更新操作", LogListUtil.newArrayList(taskInfoDO.getId()));
                TaskConfigProcessorFactory.postUpdate(taskInfoDO);
            }
            return rs;
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，任务更新异常", e.getMessage(), e, LogListUtil.newArrayList(taskInfoDO));
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateTaskInfoWithoutPost(TaskInfoDO taskInfoDO) {
        return updateTaskInfo(taskInfoDO, false, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateTaskInfo(TaskInfoDO taskInfoDO, boolean preCheck) {
        return updateTaskInfo(taskInfoDO, preCheck, true);
    }

    /**
     * 通过id更新任务信息
     *
     * @param taskInfo 任务对象
     * @return 是否更新成功
     */
    public Boolean updateById(TaskInfoDO taskInfo) {
        return taskInfoMapper.updateByPrimaryKey(taskInfo) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer delete(Long id, Long spaceId, boolean preCheck) {
        TaskInfoDO taskInfoDO = queryTaskByIdAndSpaceId(id, spaceId);
        if (Objects.isNull(taskInfoDO)) {
            return 0;
        }
        if (preCheck) {
            updatePreCheck(taskInfoDO);
        }
        try {
            TaskStatusEnum taskStatus = TaskStatusEnum.getByStatus(taskInfoDO.getStatus());
            if (!Objects.equals(taskStatus, TaskStatusEnum.EDITING)) {
                throw new Exception("只有编辑中的任务可执行删除操作");
            }

            int rs = taskInfoMapper.deleteByPrimaryKey(id);
            if (rs > 0) {
                TaskConfigProcessorFactory.postDelete(taskInfoDO);
            }

            return rs;
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，任务删除异常", e.getMessage(), e, LogListUtil.newArrayList(id, spaceId));
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer deleteByActivityId(Long activityId) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(activityId);
        query.setDeleted(0);
        List<TaskInfoDO> taskList = taskInfoMapper.select(query);
        if (CollectionUtils.isEmpty(taskList)) {
            return 0;
        }

        for (TaskInfoDO taskInfoDO : taskList) {
            int rs = taskInfoMapper.deleteByPrimaryKey(taskInfoDO.getId());
            if (rs <= 0) {
                PlatformLogUtil.logFail("任务服务，根据活动id删除任务失败", LogListUtil.newArrayList(activityId, taskInfoDO));
                throw new RuntimeException("任务删除失败");
            }
            TaskConfigProcessorFactory.postDelete(taskInfoDO);
        }

        return taskList.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean online(Long id, Long spaceId) {
        TaskInfoDO taskInfoDO = taskInfoMapper.selectByPrimaryKey(id);
        if (Objects.isNull(taskInfoDO) || !Objects.equals(taskInfoDO.getSpaceId(), spaceId)) {
            PlatformLogUtil.logFail("任务服务，上线任务，根据空间id和任务id查询任务失败", LogListUtil.newArrayList(id, spaceId));
            throw new RuntimeException("任务不存在");
        }

        TaskStatusEnum taskStatus = TaskStatusEnum.getByStatus(taskInfoDO.getStatus());
        if (!Objects.equals(taskStatus, TaskStatusEnum.EDITING)) {
            throw new RuntimeException("只有编辑中的任务可执行上线操作");
        }

        updatePreCheck(taskInfoDO);
        if (!alipayTaskOnlineCheck(taskInfoDO)) {
            throw new RuntimeException("支付宝任务上线失败，上线时间需小于执行时间减5分钟");
        }
        TaskStatusEnum newTaskStatus = TaskStatusEnum.ONLINE;
        int upRet = compareAndUpdateStatus(id, taskStatus, newTaskStatus);
        if (!NumberUtils.validInteger(upRet) || upRet < 1) {
            PlatformLogUtil.logFail("任务服务，上线任务失败", LogListUtil.newArrayList(id, spaceId));
            throw new RuntimeException("任务上线失败");
        }

        // 更新缓存
        ldbTairManager.put(TairConstant.LDB_TASK_STATUS_PREFIX + id, newTaskStatus.getStatus(), EXPIRE_TIME_DAY_UNIT);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean offline(Long id, Long spaceId) {
        TaskInfoDO taskInfoDO = taskInfoMapper.selectByPrimaryKey(id);
        if (Objects.isNull(taskInfoDO) || !Objects.equals(taskInfoDO.getSpaceId(), spaceId)) {
            PlatformLogUtil.logFail("任务服务，下线任务，根据空间id和任务id查询任务失败", LogListUtil.newArrayList(id, spaceId));
            throw new RuntimeException("任务不存在");
        }

        TaskStatusEnum taskStatus = TaskStatusEnum.getByStatus(taskInfoDO.getStatus());
        if (!Lists.newArrayList(TaskStatusEnum.ONLINE).contains(taskStatus)) {
            throw new RuntimeException("只有已上线的任务可执行下线操作");
        }

        updatePreCheck(taskInfoDO);
        processOffline(taskInfoDO);
        return true;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public TaskStatusEnum processOffline(TaskInfoDO taskInfoDO) {
        TaskStatusEnum taskStatus = TaskStatusEnum.getByStatus(taskInfoDO.getStatus());
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(TaskTriggerTypeEnum.getByCode(taskInfoDO.getTriggerType()));
        TaskStatusEnum newStatus = TaskStatusEnum.EDITING;

        // 批处理和单条触发逻辑不同
        // 单条触发直接置为下线状态
        // 批处理如果处于调度中状态，下线需要先置为下线中，等待所有任务实例执行完成后，再置为下线状态
        if (!isStreamTask && Objects.equals(TaskStatusEnum.ONLINE, taskStatus)) {
            List<TaskInstanceDO> runningInstanceList = getRunningInstanceList(taskInfoDO.getId());
            if (!CollectionUtils.isEmpty(runningInstanceList)) {
                List<SubTaskInstanceDO> runningSubInstanceList = getRunningSubInstanceList(taskInfoDO.getId());
                List<SubTaskInstanceDO> submitSuccessSubInstanceList = getSubmitSuccessSubInstanceList(taskInfoDO.getId());
                if (!CollectionUtils.isEmpty(runningSubInstanceList)) {
                    newStatus = TaskStatusEnum.OFFLINE_IN_PROGRESS;
                } else {
                    for (SubTaskInstanceDO subTaskInstanceDO : submitSuccessSubInstanceList) {
                        subTaskInstanceService.updateSubTaskStatus(subTaskInstanceDO.getId(), false, TaskInstanceExecuteStatusEnum.RUN_SUCCESS);
                    }
                    taskInstanceService.offlineTaskInstanceByTaskId(taskInfoDO.getId(), TaskInstanceStatusEnum.RUN_SUCCESS);
                }
            }
        }

        int upRet = compareAndUpdateStatus(taskInfoDO.getId(), taskStatus, newStatus);
        if (!NumberUtils.validInteger(upRet) || upRet < 1) {
            PlatformLogUtil.logFail("任务服务，下线任务失败", LogListUtil.newArrayList(taskInfoDO));
            throw new RuntimeException("任务下线失败");
        }

        // 更新缓存
        ldbTairManager.put(TairConstant.LDB_TASK_STATUS_PREFIX + taskInfoDO.getId(), newStatus.getStatus(), EXPIRE_TIME_DAY_UNIT);

        // 如果任务触发方式是事件和接口，那么直接任务下线成功
        if (isStreamTask) {
            // 查询出来老的任务实例，修改状态为运行成功
            taskInstanceService.offlineTaskInstanceByTaskId(taskInfoDO.getId(), TaskInstanceStatusEnum.RUN_SUCCESS);
            return TaskStatusEnum.EDITING;
        }

        return TaskStatusEnum.OFFLINE_IN_PROGRESS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean onlineActivity(Long activityId) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(activityId);
        query.setDeleted(0);
        List<TaskInfoDO> taskList = taskInfoMapper.select(query);

        for (TaskInfoDO taskInfoDO : taskList) {
            int upRet = compareAndUpdateStatus(taskInfoDO.getId(), TaskStatusEnum.EDITING, TaskStatusEnum.ONLINE);
            if (!NumberUtils.validInteger(upRet) || upRet < 1) {
                PlatformLogUtil.logFail("任务服务，根据活动id上线任务失败", LogListUtil.newArrayList(activityId));
                throw new RuntimeException("活动上线失败");
            }

            // 更新缓存
            ldbTairManager.put(TairConstant.LDB_TASK_STATUS_PREFIX + taskInfoDO.getId(), TaskStatusEnum.ONLINE.getStatus(), EXPIRE_TIME_DAY_UNIT);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean offlineActivity(Long activityId) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(activityId);
        query.setDeleted(0);
        List<TaskInfoDO> taskList = taskInfoMapper.select(query);

        boolean existsOfflineInProcess = false;
        for (TaskInfoDO taskInfoDO : taskList) {
            TaskStatusEnum taskStatusEnum = processOffline(taskInfoDO);
            existsOfflineInProcess = existsOfflineInProcess || Objects.equals(taskStatusEnum, TaskStatusEnum.OFFLINE_IN_PROGRESS);
        }

        return existsOfflineInProcess;
    }

    @Override
    public TaskInfoDO checkTaskValid(Long id) {
        // 查询任务数据
        TaskInfoDO taskInfoDO = queryTaskById(id);
        checkTaskValid(taskInfoDO, false);
        return taskInfoDO;
    }

    @Override
    public TaskInfoDO checkTaskValid(TaskInfoDO taskInfoDO, Boolean testFlag) {
        if (Objects.isNull(taskInfoDO)) {
            throw new TripscrmException(NOT_EXIST_TASK_INFO_DATA);
        }

        // 任务是否下线（非自测链路）
        TaskStatusEnum taskStatusEnum = TaskStatusEnum.getByStatus(taskInfoDO.getStatus());
        if (!testFlag && !Lists.newArrayList(TaskStatusEnum.ONLINE).contains(taskStatusEnum)) {
            throw new TripscrmException(TASK_STATUS_NOT_ONLINE);
        }

        // 校验任务执行器是否存在
        TaskType taskType = TaskType.getByCode(taskInfoDO.getType());
        if (taskType == null || TaskExecutorFactory.getTaskBeanByType(taskType) == null) {
            throw new TripscrmException(TripSCRMErrorCode.NOT_FOUND_TASK_EXECUTOR);
        }
        return taskInfoDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer compareAndUpdateStatus(Long id, TaskStatusEnum oldStatus, TaskStatusEnum newStatus) {
        int upRet = taskInfoMapper.updateStatus(id, oldStatus.getStatus(), newStatus.getStatus());
        if (upRet < 1) {
            return 0;
        }

        TaskInfoDO taskInfoDO = queryTaskById(id);
        ActivityInfoDO activityInfoDO = activityInfoService.queryInfoById(taskInfoDO.getActivityId());
        if (Objects.isNull(activityInfoDO) || Objects.isNull(SopTypeEnum.of(activityInfoDO.getSopType()))) {
            return upRet;
        }

        if (!NumberUtils.validInteger(activityInfoDO.getSopType())) {
            return upRet;
        }

        TaskQuery query = new TaskQuery();
        query.setActivityId(activityInfoDO.getId());
        query.setStatusList(Lists.newArrayList(TaskStatusEnum.ONLINE.getStatus(), TaskStatusEnum.OFFLINE_IN_PROGRESS.getStatus()));
        query.setDeleted(0);
        query.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        List<TaskInfoDO> taskList = taskInfoMapper.select(query);
        if (!CollectionUtils.isEmpty(taskList)) {
            // 活动下任务还没下线完成
            return upRet;
        }

        activityInfoService.compareAndUpdateStatus(activityInfoDO.getId(), ActivityStatusEnum.OFFLINE_IN_PROGRESS, ActivityStatusEnum.EDITING);
        return upRet;
    }

    @Override
    public TripSCRMResult<String> selfTest(TaskSelfVO taskSelfVO) {
        TaskExecuteParam param = new TaskExecuteParam();
        param.setTaskId(taskSelfVO.getTaskId());
        param.setTriggerType(TaskTriggerTypeEnum.INTERFACE);
        param.setTestFlag(Boolean.TRUE);
        TaskDataVO taskDataVO = getTaskDataVO(taskSelfVO);
        param.setTaskDataVO(taskDataVO);
        return taskExecuteStrategy.runMain(param);
    }

    @Override
    public TripSCRMResult<List<PushCheckResultDO>> onlineCheck(Long taskId) {
        TripSCRMResult<TaskInfo> result = innerService.queryByTaskId(taskId);
        if (Objects.isNull(result.getData())) {
            return TripSCRMResult.fail(String.valueOf(HttpServletResponse.SC_INTERNAL_SERVER_ERROR), NOT_EXIST_TASK_INFO_DATA.getDescCn());
        }
        List<PushCheckResultDO> resultDOList = scrmCheckService.checkEntry(result.getData());
        return TripSCRMResult.success(resultDOList);
    }

    @Override
    public Boolean addTags(TaskTagRequest request) {
        return operateTags(request, (oldTags, newTags) -> {
            Set<String> oldTagList = new HashSet<>();
            if (StringUtils.hasText(oldTags)) {
                oldTagList = Arrays.stream(oldTags.split(",")).collect(Collectors.toSet());
            }
            newTags.addAll(oldTagList);
            return newTags.stream().collect(Collectors.joining(","));
        });
    }

    @Override
    public Boolean deleteTags(TaskTagRequest request) {
        return operateTags(request, (oldTags, newTags) -> {
            if (!StringUtils.hasText(oldTags)) {
                return org.apache.commons.lang3.StringUtils.EMPTY;
            }
            List<String> oldTagList = Arrays.stream(oldTags.split(",")).collect(Collectors.toList());
            List<String> shouldExistTags = oldTagList.stream().filter(oldTag -> !newTags.contains(oldTag)).collect(Collectors.toList());
            return shouldExistTags.stream().collect(Collectors.joining(","));
        });
    }

    /**
     * 操作任务标签数据
     *
     * @param request  请求体
     * @param dealTags 函数表达式
     * @return 处理是否成功
     */
    private Boolean operateTags(TaskTagRequest request, FunctionUtils.Function2<String, Set<String>, String> dealTags) {
        if (CollectionUtils.isEmpty(request.getTagIdList()) || CollectionUtils.isEmpty(request.getTaskIds())) {
            return false;
        }
        for (Long taskId : request.getTaskIds()) {
            TaskInfoDO infoDO = queryTaskById(taskId);
            if (infoDO == null) {
                continue;
            }
            TaskInfoDO taskInfoDO = new TaskInfoDO();
            taskInfoDO.setId(taskId);
            taskInfoDO.setTags(dealTags.apply(infoDO.getTags(), request.getTagIdList().stream().collect(Collectors.toSet())));
            updateById(taskInfoDO);
        }
        return true;
    }

    @NotNull
    private static TaskDataVO getTaskDataVO(TaskSelfVO taskSelfVO) {
        List<TaskDataVO.DataBodyVO> data = Lists.newArrayList();
        String[] targetIdList = taskSelfVO.getTargetIds().split(",");
        for (String targetId : targetIdList) {
            TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
            dataBodyVO.setTargetId(targetId);
            dataBodyVO.setTargetType(taskSelfVO.getTargetType());
            data.add(dataBodyVO);
        }
        TaskDataVO taskDataVO = new TaskDataVO();
        taskDataVO.setData(data);
        return taskDataVO;
    }


    public boolean check(TaskInfoDO taskInfoDO, Date now, Date executedCursor, TaskQuery query) throws ParseException {
        String cron = taskInfoDO.getTriggerTimeCron();

        if (!CronUtils.checkValid(cron)) {
            return false;
        }

        // 判断查询时间间隔值，可以参数传入，默认使用diamond默认配置
        Integer finallyInterval = interval;

        if (Objects.nonNull(query)) {
            // 不为空则覆盖，否则还使用其本身
            finallyInterval = Optional.ofNullable(query.getInterval()).orElse(finallyInterval);
        }

        //计算筛选的执行时间起始区间
        Date start = executedCursor;
        Date end = DateUtils.addMinutes(now, finallyInterval);

        // 获取最近一次执行时间，单次触发直接取设置的时间，其余的走cron表达式解析
        String lastedExecuteTimeStr = null;
        if (Integer.valueOf(SINGLE_EXECUTE.getCode()).equals(taskInfoDO.getTriggerType())) {
            JSONObject extJ = JSONObject.parseObject(taskInfoDO.getExtInfo());
            Date triggerTime = new Date(MapUtils.getLong(extJ, "triggerTimeValue"));
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            lastedExecuteTimeStr = sdf.format(triggerTime);
        } else {
            lastedExecuteTimeStr = CronUtils.getExecutionTimeByNum(cron, 1).get(0);
        }

        Date lastedExecuteTime = org.apache.commons.lang3.time.DateUtils.parseDate(lastedExecuteTimeStr, "yyyy-MM-dd HH:mm:ss");
        // // 判断最近一次执行时间是否在区间内,确保区间从 [0s～ 1s)
        boolean inExecuteTime = (lastedExecuteTime.equals(start) || lastedExecuteTime.after(start)) && lastedExecuteTime.before(end);
        if (!inExecuteTime) {
            return false;
        }

        // 如果主任务下有运行中的实例数据，则不允许执行
        List<TaskInstanceDO> runningInstanceList = getRunningInstanceList(taskInfoDO.getId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(runningInstanceList)) {
            return false;
        }

        return true;
    }

    private List<SubTaskInstanceDO> getRunningSubInstanceList(Long taskId) {
        SubTaskInstanceQueryRequest subTaskInstanceQueryRequest = new SubTaskInstanceQueryRequest();
        subTaskInstanceQueryRequest.setTaskId(taskId);
        subTaskInstanceQueryRequest.setStatus(TaskInstanceStatusEnum.RUNNING);
        subTaskInstanceQueryRequest.setDeleted((byte) 0);
        return Optional.ofNullable(subTaskInstanceService.list(subTaskInstanceQueryRequest)).orElse(new ArrayList<>());
    }

    private List<SubTaskInstanceDO> getSubmitSuccessSubInstanceList(Long taskId) {
        SubTaskInstanceQueryRequest subTaskInstanceQueryRequest = new SubTaskInstanceQueryRequest();
        subTaskInstanceQueryRequest.setTaskId(taskId);
        subTaskInstanceQueryRequest.setStatus(TaskInstanceStatusEnum.SUBMIT_SUCCESS);
        subTaskInstanceQueryRequest.setDeleted((byte) 0);
        return Optional.ofNullable(subTaskInstanceService.list(subTaskInstanceQueryRequest)).orElse(new ArrayList<>());
    }

    private List<TaskInstanceDO> getRunningInstanceList(Long taskId) {
        TaskInstanceQuery taskInstanceQuery = new TaskInstanceQuery();
        taskInstanceQuery.setTaskId(taskId);
        taskInstanceQuery.setStatus(TaskInstanceStatusEnum.RUNNING.getStatus());
        taskInstanceQuery.setDeleted((byte) 0);
        return Optional.ofNullable(taskInstanceService.list(taskInstanceQuery)).orElse(new ArrayList<>());
    }

    @AteyeInvoker(description = "任务业务空间迁移", paraDesc = "oldSpaceId&newSpaceId")
    public int batchUpdateSpace(Long oldSpaceId, Long newSpaceId) {
        int res = 0;
        TaskQuery query = new TaskQuery();
        query.setSpaceId(oldSpaceId);
        List<TaskInfoDO> oldDataList = taskInfoMapper.select(query);
        for (TaskInfoDO data : oldDataList) {
            TaskInfoDO taskInfoDO = new TaskInfoDO();
            taskInfoDO.setId(data.getId());
            taskInfoDO.setSpaceId(newSpaceId);
            res += taskInfoMapper.updateByPrimaryKey(taskInfoDO);
        }

        return res;
    }

    @AteyeInvoker(description = "任务业务空间修改", paraDesc = "taskId&spaceId")
    public int updateSpace(Long taskId, Long spaceId) {
        int res = 0;
        TaskInfoDO taskInfoDO = new TaskInfoDO();
        taskInfoDO.setId(taskId);
        taskInfoDO.setSpaceId(spaceId);
        res += taskInfoMapper.updateByPrimaryKey(taskInfoDO);

        return res;
    }

    @AteyeInvoker(description = "删除任务（绝对谨慎！！）", paraDesc = "taskId")
    public int deleteById(Long id) {
        try {
            int ret = taskInfoMapper.deleteByPrimaryKey(id);
            if (ret < 1) {
                PlatformLogUtil.logFail("任务服务，删除任务失败", LogListUtil.newArrayList(id));
                return 0;
            }

            PlatformLogUtil.logInfo("任务服务，删除任务成功", LogListUtil.newArrayList(id));
            return ret;
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，任务删除异常", e.getMessage(), e, LogListUtil.newArrayList(id));
            throw e;
        }
    }

    private void updatePreCheck(TaskInfoDO taskInfoDO) {
        ActivityInfoDO activityInfoDO = activityInfoService.queryInfoById(taskInfoDO.getActivityId());
        if (Objects.isNull(activityInfoDO)) {
            throw new RuntimeException("任务没有归属的活动信息！！");
        }

        SopTypeEnum sopTypeEnum = SopTypeEnum.of(activityInfoDO.getSopType());
        if (Objects.nonNull(sopTypeEnum)) {
            throw new RuntimeException("通过SOP创建的任务，不可单独修改任务信息！！");
        }
    }

    /**
     * 支付宝相关任务上线前检查，执行时间舍去秒和毫秒后需大于当前时间
     * @param taskInfoDO 任务DO
     * @return 检查结果
     */
    private boolean alipayTaskOnlineCheck(TaskInfoDO taskInfoDO) {
        // 不是支付宝相关任务直接返回true
        if (Objects.isNull(taskInfoDO.getType()) || !Lists.newArrayList(TaskType.ALIPAY_DIRECT_MSG.getCode(), TaskType.ALIPAY_GROUP_MSG.getCode()).contains(taskInfoDO.getType())) {
            return true;
        }

        // 解析extInfo
        if (!StringUtils.hasText(taskInfoDO.getExtInfo())) {
            PlatformLogUtil.logFail("任务服务，支付宝相关任务上线检查失败，extInfo为空", LogListUtil.newArrayList(taskInfoDO.getId()));
            return false;
        }

        try {

            JSONObject extInfoJson = JSONObject.parseObject(taskInfoDO.getExtInfo());
            String triggerTimeValueStr = extInfoJson.getString(TaskConstant.TRIGGER_TIME_VALUE);

            if (triggerTimeValueStr == null) {
                PlatformLogUtil.logFail("任务服务，支付宝相关任务上线检查失败，triggerTimeValue为空", LogListUtil.newArrayList(taskInfoDO.getId()));
                return false;
            }
            long currentTime = System.currentTimeMillis();

            // 将字符串转换为long
            long triggerTimeValue = Long.parseLong(triggerTimeValueStr);
            
            // 按分钟取整，舍去秒和毫秒部分
            long triggerMinuteTime = (triggerTimeValue / 60000) * 60000;

            // 检查triggerTimeValue按分钟取整后是否大于当前时间
            return triggerMinuteTime > currentTime;
        } catch (Exception e) {
            PlatformLogUtil.logException("任务服务，支付宝相关任务上线检查出错", e.getMessage(), e, LogListUtil.newArrayList(taskInfoDO.getId()));
            return false;
        }
    }

    @AteyeInvoker(description = "同步历史群活码信息")
    public void updateGroupResource() {
        // 群管理任务列表
        TaskQuery taskQuery = new TaskQuery();
        taskQuery.setType(TaskType.MANAGER_WECHAT_JOIN_GROUP.getCode());
        List<TaskInfoDO> taskInfoDOList = query(taskQuery);
        if (CollectionUtils.isEmpty(taskInfoDOList)) {
            return;
        }
        for (TaskInfoDO taskInfoDO : taskInfoDOList) {
            List<ResourceRelationDO> resourceRelationList = getResourceRelationList(taskInfoDO);
            if (CollectionUtils.isEmpty(resourceRelationList)) {
                continue;
            }
            resourceRelationService.batchAdd(resourceRelationList);
        }
    }

    /**
     * 获取群管理任务的资源列表
     *
     * @param taskInfoDO 群管理任务
     * @return 资源关系列表
     */
    private List<ResourceRelationDO> getResourceRelationList(TaskInfoDO taskInfoDO) {
        ArrayList<ResourceRelationDO> resourceRelationDOS = new ArrayList<>();
        TaskQuery groupMaQuery = new TaskQuery();
        groupMaQuery.setActivityId(taskInfoDO.getActivityId());
        groupMaQuery.setType(TaskType.GET_WECHAT_JOIN_GROUP.getCode());
        List<TaskInfoDO> groupMaTaskList = query(groupMaQuery);
        if (CollectionUtils.isEmpty(groupMaTaskList)) {
            return resourceRelationDOS;
        }
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
        query.setSourceId(String.valueOf(taskInfoDO.getId()));
        if (!CollectionUtils.isEmpty(resourceRelationService.query(query))) {
            return resourceRelationDOS;
        }

        if (!StringUtils.hasText(groupMaTaskList.get(0).getConfig())) {
            return resourceRelationDOS;
        }
        Long contextId = JSONObject.parseObject(groupMaTaskList.get(0).getConfig()).getLong("contextId");
        if (Objects.isNull(contextId)) {
            return resourceRelationDOS;
        }
        ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        if (Objects.isNull(activityTaskInfoBO)) {
            return resourceRelationDOS;
        }
        com.alibaba.fastjson.JSONObject extraJson = activityTaskInfoBO.getExtraJson();
        if (extraJson == null) {
            return resourceRelationDOS;
        }
        List<String> allChatIdList = extraJson.getObject("allChatIdList", new TypeReference<List<String>>() {
        });
        if (CollectionUtils.isEmpty(allChatIdList)) {
            return resourceRelationDOS;
        }
        for (String chatId : allChatIdList) {
            resourceRelationDOS.add(
                    resourceRelationConverter.buildGroupManageTaskResourceRelation(String.valueOf(taskInfoDO.getId()), chatId, ResourceRelationWayEnum.AUTO_CREATED)
            );
        }
        return resourceRelationDOS;
    }

    @Override
    @Cacheable(key = "'getCorpIdByTaskId:' + #taskId", value = "taskId2CorpIdCacheManager")
    public String getCorpIdByTaskId(Long taskId) {
        TaskInfoDO taskInfo = taskService.queryTaskById(taskId);
        SpaceDO space = spaceService.getById(taskInfo.getSpaceId());
        return space.getCorpId();
    }

    @Override
    public boolean checkInWorkTimeWindow(TaskInfoDO taskInfoDO) {
        // 任务是否在有效期内
        Date now = new Date();
        if (now.before(taskInfoDO.getEffectStartTime()) || now.after(taskInfoDO.getEffectEndTime())) {
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_NOT_IN_EFFECTED_TIME);
        }

        String[] workTimeWindow = checkAndGetWorkTimeWindow(taskInfoDO);
        if (Objects.isNull(workTimeWindow)) {
            return true;
        }

        if (workTimeWindow.length != 2 || !StringUtils.hasText(workTimeWindow[0]) || !StringUtils.hasText(workTimeWindow[1])) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        Integer currentHour = DateUtils.getHourOfDate(new Date());
        Integer currentMinute = DateUtils.getMinuteOfDate(new Date());
        Date currentHm = DateUtils.parseToHourMinterOfDay(currentHour + ":" + currentMinute);
        return (!DateUtils.parseToHourMinterOfDay(workTimeWindow[0]).after(currentHm)) && DateUtils.parseToHourMinterOfDay(workTimeWindow[1]).after(currentHm);
    }

    @Override
    public CrowdExpressionDTO evaluateCrowdExpression(String expression) {
        if (!StringUtils.hasText(expression)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (expression.startsWith("crowdId:")) {
            return evaluateCrowdExpressionByCrowdId(expression);
        }

        if (expression.startsWith("tag:")) {
            return evaluateCrowdExpressionByTag(expression);
        }

        try {
            CrowdExpressionDTO crowdExpressionDTO = com.alibaba.fastjson.JSONObject.parseObject(expression, new TypeReference<CrowdExpressionDTO>() {
            });
            if (!checkCrowdExpressionDTO(crowdExpressionDTO)) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
            }
            return crowdExpressionDTO;

        } catch (Exception e) {
            PlatformLogUtil.logException("圈选表达式解析异常", e.getMessage(), e, LogListUtil.newArrayList(expression));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
    }

    private boolean checkCrowdExpressionDTO(CrowdExpressionDTO crowdExpressionDTO) {
        if (Objects.isNull(crowdExpressionDTO)) {
            PlatformLogUtil.logFail("圈选表达式解析失败，解析结果为空", LogListUtil.newArrayList(crowdExpressionDTO));
            return false;
        }
        CrowdExpressionMethodTypeEnum crowdExpressionMethodTypeEnum = CrowdExpressionMethodTypeEnum.codeOf(crowdExpressionDTO.getExpressionType());
        if (Objects.isNull(crowdExpressionMethodTypeEnum)) {
            PlatformLogUtil.logFail("圈选表达式解析失败，不支持的表达式类型", LogListUtil.newArrayList(crowdExpressionDTO));
            return false;
        }
        CrowdExpressionConditionEnum crowdExpressionConditionEnum = CrowdExpressionConditionEnum.codeOf(crowdExpressionDTO.getCondition());
        if (Objects.isNull(crowdExpressionConditionEnum) && !Lists.newArrayList(CrowdExpressionMethodTypeEnum.ADD_TIME).contains(crowdExpressionMethodTypeEnum)) {
            PlatformLogUtil.logFail("圈选表达式解析失败，不支持的表达式条件", LogListUtil.newArrayList(crowdExpressionDTO, crowdExpressionMethodTypeEnum));
            return false;
        }
        return true;
    }

    /**
     * 根据人群ID计算圈人表达式
     *
     * @param expression 圈人表达式
     * @return crowdExpressionDTO
     */
    private CrowdExpressionDTO evaluateCrowdExpressionByCrowdId(String expression) {
        CrowdExpressionDTO crowdExpressionDTO = new CrowdExpressionDTO();
        String crowdIdStr = expression.substring(8);
        if (!StringUtils.hasText(crowdIdStr) || !Strings.isNumeric(crowdIdStr)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        crowdExpressionDTO.setCrowdId(Long.parseLong(crowdIdStr));
        crowdExpressionDTO.setExpressionType(CrowdExpressionMethodTypeEnum.CROWD_ID.getCode());
        return crowdExpressionDTO;
    }

    /**
     * 根据标签计算圈人表达式
     *
     * @param expression 圈人表达式
     * @return crowdExpressionDTO
     */
    private CrowdExpressionDTO evaluateCrowdExpressionByTag(String expression) {
        CrowdExpressionDTO crowdExpressionDTO = new CrowdExpressionDTO();
        crowdExpressionDTO.setElementList(new ArrayList<>());
        String tagIdStr = expression.substring(4);
        List<String> splits = Arrays.stream(tagIdStr.split(" ")).filter(StringUtils::hasText).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(splits) || (splits.size() & 1) == 0) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        for (int i = 0; i < splits.size(); i += 2) {
            String operator = i == 0 ? null : splits.get(i - 1);
            List<String> tagIdList = Arrays.stream(splits.get(i).split(",")).filter(StringUtils::hasText).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(tagIdList) || (Objects.nonNull(operator) && !Lists.newArrayList("AND", "OR", "NOT").contains(operator))) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
            }

            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(tagIdList);
            if (CollectionUtils.isEmpty(tagInfoList)) {
                throw new TripscrmException(TripSCRMErrorCode.TAG_IS_NOT_EXISTED_OR_IS_DELETED);
            }

            crowdExpressionDTO.getElementList().add(new CrowdExpressionDTO.CrowdExpressionElementDTO(operator, tagInfoList));
        }
        crowdExpressionDTO.setExpressionType(CrowdExpressionMethodTypeEnum.TAG.getCode());
        return crowdExpressionDTO;
    }

    @Override
    public String[] getWorkTimeWindow(TaskInfoDO taskInfoDO) {
        if (Objects.isNull(taskInfoDO)) {
            return new String[]{workWindowStartTime, workWindowEndTime};
        }

        TaskTriggerTypeEnum triggerTypeEnum = TaskTriggerTypeEnum.getByCode(taskInfoDO.getTriggerType());
        if (Objects.isNull(triggerTypeEnum)) {
            return new String[]{workWindowStartTime, workWindowEndTime};
        }

        String notDisturbTime = taskInfoDO.getNotDisturbTime();
        // 如果没配置免打扰时间段，就设置默认值，但是事件和接口触发如果没配置就不加
        if ((!StringUtils.hasText(notDisturbTime) || notDisturbTime.contains("null"))) {
            if (!Lists.newArrayList(SINGLE_EXECUTE, EXECUTION_PLAN).contains(triggerTypeEnum)) {
                return null;
            }

            notDisturbTime = workWindowEndTime + ";" + workWindowStartTime;
        }

        String[] notDisturbTimeList = notDisturbTime.split(";");
        if (notDisturbTimeList[1].compareTo(notDisturbTimeList[0]) > 0) {
            notDisturbTime = workWindowEndTime + ";" + workWindowStartTime;
        }

        String workWindowStartTimeStr;
        String workWindowEndTimeStr;

        switch (triggerTypeEnum) {
            case SINGLE_EXECUTE:
                JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
                long triggerTimeValue = extInfo.getLongValue(TaskConstant.TRIGGER_TIME_VALUE);
                LocalTime time = Instant.ofEpochMilli(triggerTimeValue).atZone(ZoneId.systemDefault()).toLocalTime();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                String triggerTime = time.format(formatter);
                workWindowStartTimeStr = notDisturbTime.split(";")[1];
                workWindowEndTimeStr = notDisturbTime.split(";")[0];
                if (triggerTime.compareTo(workWindowStartTimeStr) <= 0) {
                    return new String[]{workWindowStartTimeStr, workWindowEndTimeStr};
                }
                if (triggerTime.compareTo(workWindowEndTimeStr) >= 0) {
                    return new String[]{workWindowEndTimeStr, workWindowEndTimeStr};
                }
                return new String[]{triggerTime, workWindowEndTimeStr};
            case EXECUTION_PLAN:
                String[] executeTimeStrList = taskInfoDO.getExecuteTime().split(";");
                executeTimeStrList[0] = executeTimeStrList[0].substring(0, 5);
                executeTimeStrList[1] = executeTimeStrList[1].substring(0, 5);
                workWindowStartTimeStr = notDisturbTime.split(";")[1];
                workWindowEndTimeStr = notDisturbTime.split(";")[0];
                if (executeTimeStrList[0].compareTo(workWindowStartTimeStr) <= 0) {
                    executeTimeStrList[0] = workWindowStartTimeStr;
                }
                if (executeTimeStrList[0].compareTo(workWindowEndTimeStr) >= 0) {
                    executeTimeStrList[0] = workWindowEndTimeStr;
                }
                if (executeTimeStrList[1].compareTo(workWindowEndTimeStr) >= 0) {
                    executeTimeStrList[1] = workWindowEndTimeStr;
                }
                if (executeTimeStrList[1].compareTo(workWindowStartTimeStr) <= 0) {
                    executeTimeStrList[1] = workWindowStartTimeStr;
                }
                return executeTimeStrList;
        }

        return new String[]{notDisturbTime.split(";")[1], notDisturbTime.split(";")[0]};
    }

    @Override
    public String[] checkAndGetWorkTimeWindow(TaskInfoDO taskInfoDO) {
        if (Objects.isNull(taskInfoDO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        TaskTriggerTypeEnum triggerTypeEnum = TaskTriggerTypeEnum.getByCode(taskInfoDO.getTriggerType());
        if (Objects.isNull(triggerTypeEnum)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        String notDisturbTime = taskInfoDO.getNotDisturbTime();
        // 如果没配置免打扰时间段，就设置默认值，但是事件和接口触发如果没配置就不加
        if ((!StringUtils.hasText(notDisturbTime) || notDisturbTime.contains("null"))) {
            if (!Lists.newArrayList(SINGLE_EXECUTE, EXECUTION_PLAN).contains(triggerTypeEnum)) {
                return null;
            }

            notDisturbTime = workWindowEndTime + ";" + workWindowStartTime;
        }

        String[] notDisturbTimeList = notDisturbTime.split(";");
        if (notDisturbTimeList[1].compareTo(notDisturbTimeList[0]) > 0) {
            notDisturbTime = workWindowEndTime + ";" + workWindowStartTime;
        }

        String workWindowStartTimeStr;
        String workWindowEndTimeStr;

        switch (triggerTypeEnum) {
            case SINGLE_EXECUTE:
                JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
                long triggerTimeValue = extInfo.getLongValue(TaskConstant.TRIGGER_TIME_VALUE);
                LocalTime time = Instant.ofEpochMilli(triggerTimeValue).atZone(ZoneId.systemDefault()).toLocalTime();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                String triggerTime = time.format(formatter);
                workWindowStartTimeStr = notDisturbTime.split(";")[1];
                workWindowEndTimeStr = notDisturbTime.split(";")[0];
                if (triggerTime.compareTo(workWindowStartTimeStr) <= 0) {
                    return new String[]{workWindowStartTimeStr, workWindowEndTimeStr};
                }
                if (triggerTime.compareTo(workWindowEndTimeStr) >= 0) {
                    throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
                }
                return new String[]{triggerTime, workWindowEndTimeStr};
            case EXECUTION_PLAN:
                String[] executeTimeStrList = taskInfoDO.getExecuteTime().split(";");
                executeTimeStrList[0] = executeTimeStrList[0].substring(0, 5);
                executeTimeStrList[1] = executeTimeStrList[1].substring(0, 5);
                workWindowStartTimeStr = notDisturbTime.split(";")[1];
                workWindowEndTimeStr = notDisturbTime.split(";")[0];
                if (executeTimeStrList[0].compareTo(workWindowStartTimeStr) <= 0) {
                    executeTimeStrList[0] = workWindowStartTimeStr;
                }
                if (executeTimeStrList[0].compareTo(workWindowEndTimeStr) >= 0) {
                    executeTimeStrList[0] = workWindowEndTimeStr;
                }
                if (executeTimeStrList[1].compareTo(workWindowEndTimeStr) >= 0) {
                    executeTimeStrList[1] = workWindowEndTimeStr;
                }
                if (executeTimeStrList[1].compareTo(workWindowStartTimeStr) <= 0) {
                    executeTimeStrList[1] = workWindowStartTimeStr;
                }
                return executeTimeStrList;
        }

        return new String[]{notDisturbTime.split(";")[1], notDisturbTime.split(";")[0]};
    }
}
