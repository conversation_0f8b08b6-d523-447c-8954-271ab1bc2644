package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 活动保存请求入口参数
 * <AUTHOR>
 */
@Data
public class ActivitySaveRequest implements Serializable {
    private static final long serialVersionUID = 1764896101511140475L;

    /**
     * 唯一ID
     */
    private Long id;
    /**
     * 活动名称
     */
    private String name;

    /**
     * 绑定的任务ID列表
     */
    private List<Long> taskIdList;
    /**
     * 活动上下文配置
     */
    private String context;

    /**
     * 业务空间 Id
     */
    private Long spaceId;

    /** 描述 **/
    private String desc;

    /** 管理员ID，逗号分隔字符串 **/
    private String memberIds;
}
