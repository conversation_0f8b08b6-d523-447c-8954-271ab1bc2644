package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;
import static com.alibaba.tripscrm.service.constant.TaskConstant.TASK_EXECUTE_TIME_SHARDING_KEY;
import static com.alibaba.tripscrm.service.enums.system.MQEnum.MAIN_INNER_TASK_TRIGGER_TOPIC;

/**
 * 全表扫描待执行任务，定时触发任务执行
 *
 * <AUTHOR>
 * @date 2023/8/30
 */
@Slf4j
@Component
public class TaskTodoListProcessor extends JavaProcessor {

    @Resource
    private TaskService taskService;

    @Resource
    private MetaqProducer metaqProducer;
    @Resource
    private LdbTairManager ldbTairManager;


    @Override
    public ProcessResult process(JobContext context) throws Exception {
        PlatformLogUtil.logFail("全表扫描待执行任务，定时触发任务执行，任务开始");
        List<TaskInfoDO> data = taskService.queryTodoTaskList(null);
        try {
            if (CollectionUtils.isEmpty(data)) {
                PlatformLogUtil.logFail("全表扫描待执行任务，定时触发任务执行，获取任务为空");
                return new ProcessResult(true);
            }

            List<Long> taskIdList = data.stream().map(TaskInfoDO::getId).collect(Collectors.toList());
            PlatformLogUtil.logFail("全表扫描待执行任务，定时触发任务执行，获取任务列表", LogListUtil.newArrayList(taskIdList));

            for (TaskInfoDO taskInfoDO : data) {
                Long taskId = taskInfoDO.getId();
                String key = taskId + "_" + System.currentTimeMillis();
                TaskExecuteParam param = new TaskExecuteParam();
                param.setTaskId(taskId);
                param.setTriggerType(TaskTriggerTypeEnum.getByCode(taskInfoDO.getTriggerType()));
                param.setScheduleTime(new Date());

                boolean sendResult = metaqProducer.send(MAIN_INNER_TASK_TRIGGER_TOPIC, key, "", JSON.toJSONString(param), null);
                PlatformLogUtil.logFail("全表扫描待执行任务，定时触发任务执行，发送主任务调度消息", LogListUtil.newArrayList(taskId, sendResult));
            }
        } finally {
            //更新执行任务时间游标，365天相当于不设置过期时间
            Date nowTruncate = DateUtils.addMinutes(DateUtils.truncate(new Date(), Calendar.MINUTE), 1);
            boolean updateCursorResult = ldbTairManager.put(TASK_EXECUTE_TIME_SHARDING_KEY + "_" + EnvUtils.getEnvironment(), nowTruncate.getTime(), 365 * EXPIRE_TIME_DAY_UNIT);
            PlatformLogUtil.logFail("全表扫描待执行任务，定时触发任务执行，更新执行任务时间游标", LogListUtil.newArrayList(updateCursorResult));
            PlatformLogUtil.logFail("全表扫描待执行任务，定时触发任务执行，任务结束");
        }
        return new ProcessResult(true);
    }

    @AteyeInvoker(description = "测试单个任务执行")
    public boolean process(String taskType) {
        try {
            TaskQuery taskQuery = new TaskQuery();
            taskQuery.setType(taskType);
            List<TaskInfoDO> data = taskService.query(taskQuery);

            if (CollectionUtils.isEmpty(data)) {
                PlatformLogUtil.logFail("测试单个任务执行，获取任务为空");
                return true;
            }

            data = data.stream().filter(taskInfoDO -> TaskType.SEND_TO_MOMENTS.getCode().equals(taskInfoDO.getType())).collect(Collectors.toList());

            List<Long> taskIdList = data.stream().map(TaskInfoDO::getId).collect(Collectors.toList());
            PlatformLogUtil.logFail("测试单个任务执行，获取任务列表", LogListUtil.newArrayList(taskIdList));

            for (TaskInfoDO taskInfoDO : data) {
                Long taskId = taskInfoDO.getId();
                String key = taskId + "_" + System.currentTimeMillis();
                TaskExecuteParam param = new TaskExecuteParam();
                param.setTaskId(taskId);
                param.setTriggerType(TaskTriggerTypeEnum.getByCode(taskInfoDO.getTriggerType()));

                boolean sendResult = metaqProducer.send(MAIN_INNER_TASK_TRIGGER_TOPIC, key, "", JSON.toJSONString(param), null);
                PlatformLogUtil.logFail("测试单个任务执行，发送主任务调度消息", LogListUtil.newArrayList(taskId, sendResult));
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
