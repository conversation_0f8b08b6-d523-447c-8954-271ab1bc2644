package com.alibaba.tripscrm.service.synchronizer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.fliggy.pokemon.lock.client.api.TairLockManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public abstract class BaseLockedSynchronizer<T> extends BaseSynchronizer<T> implements Lockable<T> {

    @Autowired
    private TairLockManager tairLockManager;

    @Override
    public SyncResult<T> sync(BaseSynchronizerContext<T> context) {
        SyncResult<T> syncResult = new SyncResult<>(true);
        try {
            // 加锁处理
            tairLockManager.tryRunWithLock(
                    getLockKey(context),
                    getWaitMilliseconds(context),
                    getLockSecond(context),
                    () -> {
                        try {
                            process(context);
                            syncResult.setData(context.getData());
                        } catch (Exception e) {
                            syncResult.setMessage("执行出错");
                            syncResult.setSuccess( false);
                            PlatformLogUtil.logException("锁定同步基类，上锁异常", e.getMessage(), e, LogListUtil.newArrayList(context));
                        }
                    },
                    () -> {
                        PlatformLogUtil.logFail("锁定同步基类，上锁失败", LogListUtil.newArrayList(context, getLockKey(context)));
                        syncResult.setMessage("加锁失败");
                        syncResult.setSuccess( false);
                    }
            );
            return syncResult;
        } catch (Exception e) {
            PlatformLogUtil.logException("锁定同步基类，执行异常", e.getMessage(), e, LogListUtil.newArrayList(context));
            return SyncResult.fail("执行出错");
        }
    }

}
