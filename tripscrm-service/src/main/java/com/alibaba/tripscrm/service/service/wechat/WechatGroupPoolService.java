package com.alibaba.tripscrm.service.service.wechat;


import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupPoolQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatGroupPoolDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WechatGroupPoolService {
    /**
     * 全量同步
     */
    void syncAll();

    /**
     * 根据条件统计数量
     *
     * @param query
     */
    long countByCondition(WechatGroupPoolQuery query);

    /**
     * 根据条件查询
     *
     * @param query 条件
     */
    List<WechatGroupPoolDTO> listByCondition(WechatGroupPoolQuery query);

    /**
     * 获取一个可用群聊，并标记为已使用
     *
     * @param userId
     * @param corpId
     * @return
     */
    TripSCRMResult<WechatGroupPoolDTO> getOneValidGroupAndMarkUsed(String userId, String corpId, String sellerId);

    /**
     * 更新群聊信息
     *
     * @param chatId
     * @return
     */
    void sync(String chatId);

    /**
     * 新增
     *
     * @param record 记录值
     * @return 影响行数
     */
    int insertSelective(WechatGroupPoolDTO record);

    /**
     * 更新
     *
     * @param record 记录值
     * @return 影响行数
     */
    int updateSelective(WechatGroupPoolDTO record, WechatGroupPoolQuery query);
}