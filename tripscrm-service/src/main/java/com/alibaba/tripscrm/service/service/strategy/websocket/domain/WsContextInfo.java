package com.alibaba.tripscrm.service.service.strategy.websocket.domain;

import com.alibaba.tripscrm.service.model.domain.User;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;

/**
 * websocket上下文
 *
 * <AUTHOR>
 * @date 2023/10/19
 */
@Data
public class WsContextInfo {
    /**
     * session订阅的keys（tag+userId）
     */
    private Set<String> keys;
    /**
     * 当前建立连接的平台账号
     */
    private User account;
    /**
     * 当前建立连接的空间
     */
    private Long spaceId;
    /**
     * 当前建立连接的空间所属的corpId
     */
    private String corpId;
    /**
     * 构建时间
     */
    private Date buildDate;
    /**
     * 最新交互时间
     */
    private Date lastUpdateTime;

    public String getLog(SimpleDateFormat sdf) {
        return "buildDate:" + (buildDate != null ? sdf.format(buildDate) : null) + ",lastUpdateTime:" + (lastUpdateTime != null ? sdf.format(lastUpdateTime) : null) + ",keys:" + keys + ",account:" + account.getUserName();
    }
}
