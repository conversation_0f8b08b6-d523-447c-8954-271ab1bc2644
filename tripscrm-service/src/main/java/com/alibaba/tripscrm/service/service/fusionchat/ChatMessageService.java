package com.alibaba.tripscrm.service.service.fusionchat;

import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageCreateParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageUpdateParam;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetMessageListResponseBody;

import java.util.List;

/**
 * 会话消息 Manager
 *
 * <AUTHOR>
 * @date 2023/10/10
 */
public interface ChatMessageService {

    /**
     * 保存会话消息
     *
     * @param create 对象
     * @return 结果
     */
    String createChatMessage(ChatMessageCreateParam create);

    /**
     * 更新会话消息
     *
     * @param param
     * @return
     */
    String updateChatMessage(ChatMessageUpdateParam param);

    /**
     * 将用户会话的chatId转换成会话消息的receiveId
     *
     * @param userId userId
     * @param chatId chatId
     * @param chatType chatType
     * @return return
     */
    String chatId2ReceiveId(String userId, String chatId, Integer chatType);

    /**
     * 根据会话id获取聊天记录
     *
     * @param userId userId
     * @param chatId chatId
     * @param chatType chatType
     * @return return
     */
    List<GetMessageListResponseBody> listByChatId(String userId, String chatId, Integer chatType);

    /**
     * 获取用户信息
     *
     * @param senderId senderId
     * @param senderType senderType
     * @return return
     */
    FusionChatUserBody getUserInfo(String senderId, Integer senderType);

    /**
     * 根据条件获取聊天记录
     *
     * @param query
     * @return return
     */
    List<GetMessageListResponseBody> listByParam(ChatMessageQueryRequest query);
}