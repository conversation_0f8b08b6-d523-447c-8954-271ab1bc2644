package com.alibaba.tripscrm.service.model.domain.request.alipay.msg;

import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 支付宝群发消息请求类
 *
 * <AUTHOR>
 * @since 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AliPayGroupMsgRequest extends BaseSendAliPayMsgRequest {

    /**
     * 支付宝群组id列表
     */
    private List<String> alipayGroupTemplateIdList;


    /**
     * 小助手的群发消息名称
     */
    private String assistantMsgName;

    /**
     * 消息发布时间
     */
    private Date publishTime;

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 素材
     */
    private MaterailInfoDO materailInfoDO;

    /**
     * 素材埋点
     */
    private MaterialTrackRelationDTO materialTrackRelationDTO;

    /**
     * 素材内容转换上下文
     */
    private MaterialContentConvertContext materialContentConvertContext;

    /**
     * 任务类型
     */
    private TaskType taskType;
}