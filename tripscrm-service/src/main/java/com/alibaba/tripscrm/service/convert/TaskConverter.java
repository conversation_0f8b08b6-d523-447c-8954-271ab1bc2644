package com.alibaba.tripscrm.service.convert;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.subscribemsg.SubscribeMsgTaskStrategyEnum;
import com.alibaba.tripscrm.service.enums.task.TaskOptionEnum;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.vo.tag.TagInfoVO;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.vo.task.TaskVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.system.CronUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.constant.TaskConstant.*;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EXECUTION_PLAN;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.SINGLE_EXECUTE;

/**
 * TaskInfo数据防腐层
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskConverter {

    private static final String[] IGNORE_ISOLATOR_PROPERTIES = new String[]{"gmtCreate", "gmtModified", "startTime", "endTime"};

    private final TagInfoService tagInfoService;
    private final ActivityInfoService activityInfoService;
    private final WechatGroupService wechatGroupService;
    private final TaskService taskService;
    private final TagConverter tagConverter;

    public TaskVO do2Vo(TaskInfoDO taskInfoDO) {
        TaskVO taskVO = new TaskVO();
        BeanUtils.copyProperties(taskInfoDO, taskVO, IGNORE_ISOLATOR_PROPERTIES);
        // 根据任务类型返回不同opts
        taskVO.setOpts(TaskOptionEnum.getTaskOptions(TaskStatusEnum.getByStatus(taskInfoDO.getStatus()), TaskType.getByCode(taskInfoDO.getType())));
        // 判断人群类型，然后生成triggerConfig
        taskVO.setTriggerConfig(ext2ConfigByCrowdType(taskInfoDO));
        // odps字段存扩展字段
        JSONObject extJs = JSONObject.parseObject(taskInfoDO.getExtInfo());
        // 发送机器人存扩展字段
        String sendUserIdListStr = taskInfoDO.getSendUserId();
        if (StringUtils.isBlank(sendUserIdListStr)) {
            sendUserIdListStr = ((String) extJs.getOrDefault("robotUser", ""));
        }
        taskVO.setRobotUser(sendUserIdListStr);
        if (extJs.containsKey("executeTime")) {
            taskVO.setExecuteTime(extJs.getLong("executeTime") * 1000);
        }
        // 自定义参数
        if (StringUtils.isNotBlank(taskInfoDO.getCustomerConfig())) {
            List<Map<String, String>> customerConfig = JSONObject.parseObject(taskInfoDO.getCustomerConfig(), new TypeReference<List<Map<String, String>>>() {
            });
            taskVO.setCustomerConfig(customerConfig);
        }
        // 免打扰时间拆分
        if (StringUtils.isNotBlank(taskInfoDO.getNotDisturbTime())) {
            taskVO.setNotDisturbStartTime(taskInfoDO.getNotDisturbTime().split(";")[0]);
            taskVO.setNotDisturbEndTime(taskInfoDO.getNotDisturbTime().split(";")[1]);
        }
        // 任务类型
        taskVO.setTaskTypeCode(taskInfoDO.getType());
        taskVO.setTaskTypeName(TaskType.getByCode(taskInfoDO.getType()).getDesc());
        // 任务开始时间段、结束时间段
        if (StringUtils.isNotBlank(taskInfoDO.getExecuteTime())) {
            taskVO.setExecuteStartTime(StringUtils.equals(taskInfoDO.getExecuteTime().split(";")[0], "null") ? null : taskInfoDO.getExecuteTime().split(";")[0]);
            taskVO.setExecuteEndTime(StringUtils.equals(taskInfoDO.getExecuteTime().split(";")[1], "null") ? null : taskInfoDO.getExecuteTime().split(";")[1]);
        }
        // 创建修改时间处理
        taskVO.setGmtCreate(DateUtils.getOtherSimpleDayDateString(taskInfoDO.getGmtCreate()));
        taskVO.setGmtModified(DateUtils.getOtherSimpleDayDateString(taskInfoDO.getGmtModified()));
        // 触发时间配置
        taskVO.setTriggerTimeType(extJs.getString(TaskConstant.TRIGGER_TIME_TYPE));
        taskVO.setTriggerTimeValue(extJs.getString(TRIGGER_TIME_VALUE));

        // 消息类型
        taskVO.setTaskMessageTypeId(extJs.getLong(TaskConstant.TASK_MESSAGE_TYPE_ID));
        // 使用场景
        taskVO.setTaskMessageSceneId(extJs.getLong(TASK_MESSAGE_SCENE_ID));
        String[] workTimeWindow = taskService.getWorkTimeWindow(taskInfoDO);
        if (Objects.nonNull(workTimeWindow) && workTimeWindow.length == 2) {
            taskVO.setNotDisturbStartTime(workTimeWindow[1]);
            taskVO.setNotDisturbEndTime(workTimeWindow[0]);
        }

        // 额外信息
        taskVO.setExtraInfo(TaskConfigProcessorFactory.getExtraInfo(taskInfoDO));
        String autoMatchSendUserIdListStr = taskVO.getExtraInfo().getString("userIdList");
        if (StringUtils.isBlank(taskVO.getRobotUser()) && StringUtils.isNotBlank(autoMatchSendUserIdListStr)) {
            taskVO.setRobotUser(autoMatchSendUserIdListStr);
        }

        // 兼容前端历史逻辑
        if (Objects.equals(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.getByCode(taskVO.getTriggerType()))) {
            taskVO.setTriggerCrowd(3);
        }
        if (Objects.equals(TaskTriggerTypeEnum.EVENT, TaskTriggerTypeEnum.getByCode(taskVO.getTriggerType()))) {
            taskVO.setTriggerCrowd(4);
        }

        // 标签
        if (StringUtils.isNotBlank(taskInfoDO.getTags())) {
            taskVO.setTagIdList(Arrays.stream(taskInfoDO.getTags().split(",")).collect(Collectors.toList()));
            List<TagInfoVO> tagInfoList = taskVO.getTagIdList().stream()
                    .map(tagInfoService::selectByTagId)
                    .map(tagConverter::convert2VO)
                    .collect(Collectors.toList());
            taskVO.setTagList(tagInfoList);
        }
        
        // 支付宝任务时间处理
        processAlipayTaskExecuteTimeForDo2Vo(taskVO);

        return taskVO;
    }

    public TaskInfoDO vo2Do(TaskVO taskVO) {
        // 支付宝任务时间校验
        processAlipayTaskExecuteTime(taskVO);
        
        TaskInfoDO taskInfoDO = new TaskInfoDO();
        BeanUtils.copyProperties(taskVO, taskInfoDO, IGNORE_ISOLATOR_PROPERTIES);
        // odps字段存扩展字段
        JSONObject extJs = new JSONObject();
        // 设置触发人群相关配置
        extJs.putAll(config2ExtByCrowdType(taskVO));
        taskInfoDO.setSendUserId("");
        if (StringUtils.isNotBlank(taskVO.getRobotUser())) {
            // 消息发送人存扩展字段
            extJs.put("robotUser", taskVO.getRobotUser());
            taskInfoDO.setSendUserId(taskVO.getRobotUser());
        }
        taskInfoDO.setCustomerConfig("[]");
        // 自定义参数
        if (Objects.nonNull(taskVO.getCustomerConfig())) {
            taskInfoDO.setCustomerConfig(JSONObject.toJSONString(taskVO.getCustomerConfig()));
        }

        if (Objects.nonNull(taskVO.getExtraInfo())) {
            extJs.putAll(taskVO.getExtraInfo());
        }

        // 素材Id兼容
        if (extJs.containsKey("materialId") && Objects.isNull(taskVO.getMaterialId())) {
            taskInfoDO.setMaterialId(extJs.getLong("materialId"));
        }

        // 消息类型
        if (NumberUtils.validLong(taskVO.getTaskMessageTypeId())) {
            extJs.put("taskMessageTypeId", taskVO.getTaskMessageTypeId());
        }

        // 使用场景
        if (NumberUtils.validLong(taskVO.getTaskMessageSceneId())) {
            extJs.put(TaskConstant.TASK_MESSAGE_SCENE_ID, taskVO.getTaskMessageSceneId());
        }

        // 免打扰时间合并
        if (StringUtils.isNotBlank(taskVO.getNotDisturbStartTime()) && StringUtils.isNotBlank(taskVO.getNotDisturbEndTime())) {
            taskInfoDO.setNotDisturbTime(taskVO.getNotDisturbStartTime() + ";" + taskVO.getNotDisturbEndTime());
        } else {
            taskInfoDO.setNotDisturbTime("");
        }
        // 偏移时间
        if (!NumberUtils.validLong(taskVO.getOffsetTime())) {
            taskInfoDO.setOffsetTime(0L);
        }
        // 任务类型
        taskInfoDO.setType(taskVO.getTaskTypeCode());
        // 触发方式
        if (Objects.isNull(taskInfoDO.getTriggerType()) && Objects.equals(TaskType.PUBLISH_GROUP_NOTICE.getCode(), taskInfoDO.getType())) {
            taskInfoDO.setTriggerType(Integer.parseInt(SINGLE_EXECUTE.getCode()));
        }
        // 任务开始结束时间
        String executeStartTime = taskVO.getExecuteStartTime();
        String executeEndTime = taskVO.getExecuteEndTime();
        if (Objects.equals(EXECUTION_PLAN, TaskTriggerTypeEnum.getByCode(taskVO.getTriggerType())) && (StringUtils.isBlank(executeStartTime) || StringUtils.isBlank(executeEndTime))) {
            throw new TripscrmException("任务开始结束时间不可为空");
        }
        taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);

        // 时间表达式
        taskInfoDO.setTriggerTimeCron(generateCron(taskVO));
        extJs.put("triggerTimeType", taskVO.getTriggerTimeType());
        extJs.put("triggerTimeValue", taskVO.getTriggerTimeValue());

        // 生效时间
        if (Objects.equals(SINGLE_EXECUTE, TaskTriggerTypeEnum.getByCode(taskVO.getTriggerType()))) {
            Long triggerTime = Long.valueOf(taskVO.getTriggerTimeValue());
            Date currDateStart = org.apache.commons.lang.time.DateUtils.truncate(new Date(triggerTime), Calendar.DATE);
            Date currDateEnd = DateUtils.addDays(currDateStart, 1);
            taskInfoDO.setEffectStartTime(currDateStart);
            taskInfoDO.setEffectEndTime(currDateEnd);
        } else {
            taskInfoDO.setEffectStartTime(Optional.ofNullable(taskVO.getEffectStartTime()).orElse(new Date()));
            taskInfoDO.setEffectEndTime(Optional.ofNullable(taskVO.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        }

        // AB
        taskInfoDO.setAbType(Objects.isNull(taskVO.getAbType()) ? (byte) 0 : taskVO.getAbType());

        // 环境
        taskInfoDO.setEnv(EnvUtils.getEnvironment());

        // 订阅消息任务执行策略校验
        checkSubscribeMsgTaskExecuteStrategy(taskVO.getTaskTypeCode(), extJs);

        // 最后将extInfo设置到模型里
        taskInfoDO.setExtInfo(extJs.toJSONString());
        return taskInfoDO;
    }

    /**
     * 解析前端页面传入的触发时间，生成cron表达式
     *
     * @param taskVO
     * @return
     */
    public static String generateCron(TaskVO taskVO) {

        String triggerTimeType = taskVO.getTriggerTimeType();
        String triggerTimeValue = taskVO.getTriggerTimeValue();
        String executeStartTime = taskVO.getExecuteStartTime();

        CronUtils.CronVO cronVO = new CronUtils.CronVO();
        cronVO.init();

        Integer triggerType = taskVO.getTriggerType();
        if (Integer.valueOf(SINGLE_EXECUTE.getCode()).equals(triggerType)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(Long.valueOf(triggerTimeValue));
            cronVO.setYear(String.valueOf(calendar.get(Calendar.YEAR)));
            cronVO.setMonth(String.valueOf(calendar.get(Calendar.MONTH) + 1));
            cronVO.setDay(String.valueOf(calendar.get(Calendar.DAY_OF_MONTH)));
            cronVO.setHour(String.valueOf(calendar.get(Calendar.HOUR_OF_DAY)));
            cronVO.setMinute(String.valueOf(calendar.get(Calendar.MINUTE)));
            cronVO.setSecond(String.valueOf(calendar.get(Calendar.SECOND)));
        } else if (Integer.valueOf(EXECUTION_PLAN.getCode()).equals(triggerType)) {
            String[] split = executeStartTime.split(":");
            String hourStr = split[0];
            String minuteStr = split[1];
            // 每月几号
            if ("month".equals(triggerTimeType)) {
                cronVO.setDay(triggerTimeValue);
                cronVO.setHour(hourStr);
            }
            // 每周几
            else if ("week".equals(triggerTimeType)) {
                cronVO.setWeek(triggerTimeValue);
                cronVO.setHour(hourStr);
                // 由于cron表达式限制，指定星期几时，不允许指定月份，*要换成?
                cronVO.setDay("?");
            }
            // 每隔几天
            else if ("day".equals(triggerTimeType)) {
                cronVO.setDay("*");
                cronVO.setHour(hourStr);
                cronVO.setDayEveryTime(Integer.valueOf(triggerTimeValue));
            }
            // 每几个小时
            else if ("hour".equals(triggerTimeType)) {
                cronVO.setHour("0");
                cronVO.setHourEveryTime(Integer.valueOf(triggerTimeValue));
            }
            cronVO.setMinute("00".equals(minuteStr) ? "0" : minuteStr);
            cronVO.setSecond("0");
        } else {
            // 接口触发和事件触发，不需要生成执行时间的cron表达式
            return null;
        }
        return CronUtils.generateCronExpression(cronVO);
    }

    /**
     * 根据触发人群类型，从ext字段里取配置转为triggerConfig
     *
     * @param taskInfoDO
     * @return
     */
    public JSONObject ext2ConfigByCrowdType(TaskInfoDO taskInfoDO) {
        if (taskInfoDO == null || taskInfoDO.getTriggerType() == null) {
            return null;
        }
        // odps字段存扩展字段
        JSONObject extJs = JSONObject.parseObject(taskInfoDO.getExtInfo());
        JSONObject triggerConfig = new JSONObject();
        if (extJs.containsKey(INCLUDE_CROWD_EXPRESSION)) {
            triggerConfig.put(INCLUDE_CROWD_EXPRESSION, extJs.getString(INCLUDE_CROWD_EXPRESSION));
        }
        if (extJs.containsKey(EXCLUDE_CROWD_EXPRESSION)) {
            triggerConfig.put(EXCLUDE_CROWD_EXPRESSION, extJs.getString(EXCLUDE_CROWD_EXPRESSION));
        }
        if (extJs.containsKey(ODPS_PROJECT_NAME_FIELD_KEY)) {
            triggerConfig.put(ODPS_PROJECT_NAME_FIELD_KEY, extJs.getString(ODPS_PROJECT_NAME_FIELD_KEY));
        }
        if (extJs.containsKey(ODPS_TABLE_NAME_FIELD_KEY)) {
            triggerConfig.put(ODPS_TABLE_NAME_FIELD_KEY, extJs.getString(ODPS_TABLE_NAME_FIELD_KEY));
        }
        if (extJs.containsKey(CROWD_ID_FIELD_KEY)) {
            triggerConfig.put(CROWD_ID_FIELD_KEY, extJs.getLong(CROWD_ID_FIELD_KEY));
        }
        if (extJs.containsKey(ALL_MATCH)) {
            triggerConfig.put(ALL_MATCH, extJs.getBoolean(ALL_MATCH));
        }
        if (extJs.containsKey(SCRM_INCLUDE_TAG_ID_LIST)) {
            List<String> includeTagIdList = extJs.getObject(SCRM_INCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
            });
            triggerConfig.put(SCRM_INCLUDE_TAG_ID_LIST, includeTagIdList);
            List<TagInfoVO> tagInfoList = includeTagIdList.stream()
                    .map(tagInfoService::selectByTagId)
                    .map(tagConverter::convert2VO)
                    .collect(Collectors.toList());
            triggerConfig.put(SCRM_INCLUDE_TAG_LIST, tagInfoList);
        }
        if (extJs.containsKey(SCRM_EXCLUDE_TAG_ID_LIST)) {
            List<String> excludeTagIdList = extJs.getObject(SCRM_EXCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
            });
            triggerConfig.put(SCRM_EXCLUDE_TAG_ID_LIST, excludeTagIdList);
            List<TagInfoVO> tagInfoList = excludeTagIdList.stream()
                    .map(tagInfoService::selectByTagId)
                    .map(tagConverter::convert2VO)
                    .collect(Collectors.toList());
            triggerConfig.put(SCRM_EXCLUDE_TAG_LIST, tagInfoList);
        }
        if (extJs.containsKey(WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST)) {
            List<Long> wechatJoinGroupActivityIdList = extJs.getObject(WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, new TypeReference<List<Long>>() {
            });

            triggerConfig.put(WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, wechatJoinGroupActivityIdList);
            List<ActivityInfoDO> activityInfoList = activityInfoService.selectByIdList(wechatJoinGroupActivityIdList);
            triggerConfig.put(WECHAT_JOIN_GROUP_ACTIVITY_NAME_LIST, Optional.ofNullable(activityInfoList).orElse(new ArrayList<>()).stream().map(ActivityInfoDO::getName).collect(Collectors.toList()));
        }
        if (extJs.containsKey(WECHAT_CHAT_ID_LIST)) {
            List<String> wechatChatIdList = extJs.getObject(WECHAT_CHAT_ID_LIST, new TypeReference<List<String>>() {
            });
            triggerConfig.put(WECHAT_CHAT_ID_LIST, wechatChatIdList);
            List<WechatGroupVO> wechatGroupList = wechatGroupService.listByChatIdList(wechatChatIdList, false);
            triggerConfig.put(WECHAT_CHAT_NAME_LIST, Optional.ofNullable(wechatGroupList).orElse(new ArrayList<>()).stream().map(WechatGroupVO::getName).collect(Collectors.toList()));
        }
        if(extJs.containsKey(WECHAT_TAG_NAME)){

            triggerConfig.put(WECHAT_TAG_NAME, extJs.getString(WECHAT_TAG_NAME));
        }
        if (extJs.containsKey(TAKEOVER_USER_ID_LIST)) {
            List<String> takeoverUserIdList = extJs.getObject(TAKEOVER_USER_ID_LIST, new TypeReference<List<String>>() {
            });
            triggerConfig.put(TAKEOVER_USER_ID_LIST, takeoverUserIdList);
        }
        if (extJs.containsKey(HANDOVER_USER_ID_LIST)) {
            List<String> takeoverUserIdList = extJs.getObject(HANDOVER_USER_ID_LIST, new TypeReference<List<String>>() {
            });
            triggerConfig.put(HANDOVER_USER_ID_LIST, takeoverUserIdList);
        }
        if (extJs.containsKey(TAKEOVER_USER_SPACE_ID)) {
            Long handoverSpaceId = extJs.getLong(TAKEOVER_USER_SPACE_ID);
            triggerConfig.put(TAKEOVER_USER_SPACE_ID, handoverSpaceId);
        }
        return triggerConfig;
    }

    /**
     * 根据触发人群类型，从视图triggerConfig字段里取配置设置到ext字段里
     *
     * @param taskVO
     * @return
     */
    public JSONObject config2ExtByCrowdType(TaskVO taskVO) {
        JSONObject triggerConfig = new JSONObject();
        if (taskVO == null || taskVO.getTriggerType() == null || taskVO.getTriggerConfig() == null) {
            return triggerConfig;
        }
        JSONObject voTriggerConfig = taskVO.getTriggerConfig();
        if (voTriggerConfig.containsKey(INCLUDE_CROWD_EXPRESSION)) {
            triggerConfig.put(INCLUDE_CROWD_EXPRESSION, voTriggerConfig.getString(INCLUDE_CROWD_EXPRESSION));
        }
        if (voTriggerConfig.containsKey(EXCLUDE_CROWD_EXPRESSION)) {
            triggerConfig.put(EXCLUDE_CROWD_EXPRESSION, voTriggerConfig.getString(EXCLUDE_CROWD_EXPRESSION));
        }
        if (voTriggerConfig.containsKey(ODPS_PROJECT_NAME_FIELD_KEY)) {
            triggerConfig.put(ODPS_PROJECT_NAME_FIELD_KEY, voTriggerConfig.getString(ODPS_PROJECT_NAME_FIELD_KEY));
        }
        if (voTriggerConfig.containsKey(ODPS_TABLE_NAME_FIELD_KEY)) {
            triggerConfig.put(ODPS_TABLE_NAME_FIELD_KEY, voTriggerConfig.getString(ODPS_TABLE_NAME_FIELD_KEY));
        }
        if (voTriggerConfig.containsKey(CROWD_ID_FIELD_KEY)) {
            triggerConfig.put(CROWD_ID_FIELD_KEY, voTriggerConfig.getLong(CROWD_ID_FIELD_KEY));
        }
        if (voTriggerConfig.containsKey(ALL_MATCH)) {
            triggerConfig.put(ALL_MATCH, voTriggerConfig.getBoolean(ALL_MATCH));
        }
        if (voTriggerConfig.containsKey(SCRM_INCLUDE_TAG_ID_LIST)) {
            triggerConfig.put(SCRM_INCLUDE_TAG_ID_LIST, voTriggerConfig.getObject(SCRM_INCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
            }));
        }
        if (voTriggerConfig.containsKey(SCRM_EXCLUDE_TAG_ID_LIST)) {
            triggerConfig.put(SCRM_EXCLUDE_TAG_ID_LIST, voTriggerConfig.getObject(SCRM_EXCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
            }));
        }
        if (voTriggerConfig.containsKey(WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST)) {
            triggerConfig.put(WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, voTriggerConfig.getObject(WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, new TypeReference<List<Long>>() {
            }));
        }
        if (voTriggerConfig.containsKey(WECHAT_CHAT_ID_LIST)) {
            triggerConfig.put(WECHAT_CHAT_ID_LIST, voTriggerConfig.getObject(WECHAT_CHAT_ID_LIST, new TypeReference<List<String>>() {
            }));
        }
        if (voTriggerConfig.containsKey(WECHAT_TAG_NAME)){
            triggerConfig.put(WECHAT_TAG_NAME, voTriggerConfig.getString(WECHAT_TAG_NAME));
        }
        if (voTriggerConfig.containsKey(TAKEOVER_USER_ID_LIST)) {
            triggerConfig.put(TAKEOVER_USER_ID_LIST, voTriggerConfig.getObject(TAKEOVER_USER_ID_LIST, new TypeReference<List<String>>() {
            }));
        }
        if (voTriggerConfig.containsKey(HANDOVER_USER_ID_LIST)) {
            triggerConfig.put(HANDOVER_USER_ID_LIST, voTriggerConfig.getObject(HANDOVER_USER_ID_LIST, new TypeReference<List<String>>() {
            }));
        }
        if (voTriggerConfig.containsKey(TAKEOVER_USER_SPACE_ID)) {
            triggerConfig.put(TAKEOVER_USER_SPACE_ID, voTriggerConfig.getString(TAKEOVER_USER_SPACE_ID));
        }
        if (voTriggerConfig.containsKey(ALIPAY_GROUP_TEMPLATE_ID_LIST)) {
            triggerConfig.put(ALIPAY_GROUP_TEMPLATE_ID_LIST, voTriggerConfig.getObject(ALIPAY_GROUP_TEMPLATE_ID_LIST, new TypeReference<List<String>>() {
            }));
        }
        if (voTriggerConfig.containsKey(ALIPAY_CROWD_ID)) {
            triggerConfig.put(ALIPAY_CROWD_ID, voTriggerConfig.getString(ALIPAY_CROWD_ID));
        }
        if (voTriggerConfig.containsKey(ALIPAY_CROWD_TYPE)) {
            triggerConfig.put(ALIPAY_CROWD_TYPE, voTriggerConfig.getString(ALIPAY_CROWD_TYPE));
        }
        if (voTriggerConfig.containsKey(ALIPAY_TRIGGER_TIME)) {
            triggerConfig.put(ALIPAY_TRIGGER_TIME, voTriggerConfig.getString(ALIPAY_TRIGGER_TIME));
        }

        return triggerConfig;
    }

    public static TaskExecuteContext record2Context(TaskExecuteRecordBO taskExecuteRecordBO) {
        JSONObject executeInfo = taskExecuteRecordBO.getExecuteInfo();
        TaskExecuteContext context = new TaskExecuteContext();
        TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
        dataBodyVO.setTargetId(taskExecuteRecordBO.getTargetId());
        dataBodyVO.setTargetType(taskExecuteRecordBO.getTargetTypeEnum().getCode());
        dataBodyVO.setRecordId(taskExecuteRecordBO.getRecordId());
        TaskDataVO taskDataVO = new TaskDataVO();
        taskDataVO.setData(Lists.newArrayList(dataBodyVO));
        taskDataVO.setTotalCount(1L);

        context.setTaskId(taskExecuteRecordBO.getTaskId());
        context.setIsSub(true);
        context.setTriggerType(TaskTriggerTypeEnum.getByCode(executeInfo.getInteger("triggerType")));
        dataBodyVO.setExtInfo(executeInfo.getJSONObject("dataExtInfo").toJSONString());
        context.setExtInfo(executeInfo.getObject("contextExtInfo", new TypeReference<Map<String, Object>>(){}));
        context.setMainTaskInstanceId(taskExecuteRecordBO.getMainTaskInstanceId());
        context.setInstanceId(taskExecuteRecordBO.getSubTaskInstanceId());
        context.setTaskDataVO(taskDataVO);
        context.setTestFlag(false);
        context.setRetry(true);
        return context;
    }

    public static TaskExecuteContext param2Context(TaskExecuteParam param) {
        TaskExecuteContext context = new TaskExecuteContext();
        if (param == null || param.getSubTask() == null || param.getTaskId() == null || param.getTriggerType() == null) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        BeanUtils.copyProperties(param, context);
        context.setInstanceId(param.getInstanceId());
        context.setIsSub(param.getSubTask());
        context.setTriggerType(param.getTriggerType());
        context.setTaskDataVO(param.getTaskDataVO());
        context.setTestFlag(param.getTestFlag());
        context.setExtInfo(param.getExtInfo());
        context.setScheduleTime(param.getScheduleTime());
        context.setAsyncTaskSequenceId(param.getAsyncTaskSequenceId());
        context.setImageUrlList(param.getImageUrlList());

        if (MapUtils.isEmpty(context.getExtInfo())) {
            context.setExtInfo(new HashMap<>());
        }
        return context;
    }
    
    /**
     * vo转do前处理支付宝任务的执行时间
     * @param taskVO 任务VO
     */
    private void processAlipayTaskExecuteTime(TaskVO taskVO) {
        // 构建需要校验的任务类型列表
        List<String> alipayTaskTypes = Arrays.asList(
            TaskType.ALIPAY_DIRECT_MSG.getCode(),
            TaskType.ALIPAY_GROUP_MSG.getCode()
        );
        
        // 如果不是支付宝任务类型，直接返回
        if (!alipayTaskTypes.contains(taskVO.getTaskTypeCode())) {
            return;
        }
        
        TaskTriggerTypeEnum triggerTypeEnum = TaskTriggerTypeEnum.getByCode(taskVO.getTriggerType());
        
        // 校验支付宝定向消息的触发类型
        if (TaskType.ALIPAY_DIRECT_MSG.getCode().equals(taskVO.getTaskTypeCode())) {
            if (!SINGLE_EXECUTE.equals(triggerTypeEnum)) {
                PlatformLogUtil.logFail("支付宝任务TaskVO转TaskDO，TaskVO校验失败，任务类型为支付宝定向消息，triggerType不为单次执行", LogListUtil.newArrayList(taskVO));
                throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
            }
        }
        
        // 校验支付宝群发消息的触发类型
        if (TaskType.ALIPAY_GROUP_MSG.getCode().equals(taskVO.getTaskTypeCode())) {
            if (!SINGLE_EXECUTE.equals(triggerTypeEnum) && !EXECUTION_PLAN.equals(triggerTypeEnum)) {
                PlatformLogUtil.logFail("支付宝任务TaskVO转TaskDO，TaskVO校验失败，任务类型为支付宝群发消息，triggerType不为单次执行或执行计划", LogListUtil.newArrayList(taskVO));
                throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
            }
        }
        
        // 校验并处理单次执行类型的时间
        if (SINGLE_EXECUTE.equals(triggerTypeEnum)) {
            processSingleExecuteTime(taskVO);
            return;
        }
        
        // 校验并处理周期类型的时间（仅支付宝群发消息）
        if (EXECUTION_PLAN.equals(triggerTypeEnum) && TaskType.ALIPAY_GROUP_MSG.getCode().equals(taskVO.getTaskTypeCode())) {
            processExecutionPlanTime(taskVO);
        }
    }
    
    /**
     * 校验并处理单次执行时间
     * @param taskVO 任务VO
     */
    private void processSingleExecuteTime(TaskVO taskVO) {
        if (StringUtils.isBlank(taskVO.getTriggerTimeValue())) {
            PlatformLogUtil.logFail("TaskVO转TaskDO，TaskVO校验失败，triggerTimeValue为空", LogListUtil.newArrayList(taskVO));
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
        }
        
        try {
            long triggerTime = Long.parseLong(taskVO.getTriggerTimeValue());
            // 当前时间 + (延迟时间)分钟
            long minAllowedTime = System.currentTimeMillis() + (SwitchConfig.ALIPAY_MSG_TASK_DELAY_TIME) * 60 * 1000L;
            if (triggerTime < minAllowedTime) {
                PlatformLogUtil.logInfo("支付宝任务TaskVO转TaskDO失败，TaskVO校验失败，triggerTime早于最小允许时间", LogListUtil.newArrayList(taskVO));
                throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
            }
            
            // 确保triggerConfig不为null
            if (taskVO.getTriggerConfig() == null) {
                taskVO.setTriggerConfig(new JSONObject());
            }
            // 保存原始触发时间到triggerConfig中
            taskVO.getTriggerConfig().put(ALIPAY_TRIGGER_TIME, triggerTime);
            // 设置实际触发时间为触发时间减去延迟时间
            taskVO.setTriggerTimeValue(String.valueOf(triggerTime - SwitchConfig.ALIPAY_MSG_TASK_DELAY_TIME * 60 * 1000L));
        } catch (NumberFormatException e) {
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
        }
    }
    
    /**
     * 校验并处理执行计划时间
     * @param taskVO 任务VO
     */
    private void processExecutionPlanTime(TaskVO taskVO) {
        if (StringUtils.isBlank(taskVO.getExecuteStartTime())) {
            PlatformLogUtil.logFail("TaskVO转TaskDO，TaskVO校验失败，executeStartTime为空", LogListUtil.newArrayList(taskVO));
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
        }
        
        // 校验executeStartTime格式是否为hh:mm
        if (!taskVO.getExecuteStartTime().matches("\\d{1,2}:\\d{2}")) {
            PlatformLogUtil.logFail("TaskVO转TaskDO，TaskVO校验失败，executeStartTime格式不为hh:mm", LogListUtil.newArrayList(taskVO));
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
        }
        
        // 解析时间
        String[] timeParts = taskVO.getExecuteStartTime().split(":");
        int hour = Integer.parseInt(timeParts[0]);
        int minute = Integer.parseInt(timeParts[1]);
        
        // 计算当前时间 + (延迟时间+1)分钟
        Calendar minAllowedCalendar = Calendar.getInstance();
        minAllowedCalendar.add(Calendar.MINUTE, SwitchConfig.ALIPAY_MSG_TASK_DELAY_TIME);
        
        // 计算设置的时间
        Calendar executeCalendar = Calendar.getInstance();
        executeCalendar.set(Calendar.HOUR_OF_DAY, hour);
        executeCalendar.set(Calendar.MINUTE, minute);
        executeCalendar.set(Calendar.SECOND, 0);
        executeCalendar.set(Calendar.MILLISECOND, 0);
        
        // 如果设置的时间早于最小允许时间，抛出异常
        if (executeCalendar.before(minAllowedCalendar)) {
            PlatformLogUtil.logFail("TaskVO转TaskDO，TaskVO校验失败，executeStartTime早于最小允许时间", LogListUtil.newArrayList(taskVO));
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
        }
        
        // 确保triggerConfig不为null
        if (taskVO.getTriggerConfig() == null) {
            taskVO.setTriggerConfig(new JSONObject());
        }
        
        // 保存原始执行时间到triggerConfig中
        taskVO.getTriggerConfig().put(ALIPAY_CYCLE_TASK_EXECUTE_TIME, taskVO.getExecuteStartTime());
        // 设置实际执行时间为执行时间减去延迟时间
        executeCalendar.add(Calendar.MINUTE, -SwitchConfig.ALIPAY_MSG_TASK_DELAY_TIME);
        taskVO.setExecuteStartTime(String.format("%02d:%02d", executeCalendar.get(Calendar.HOUR_OF_DAY), executeCalendar.get(Calendar.MINUTE)));
    }
    
    /**
     * 处理do2Vo中支付宝任务的时间
     * @param taskVO 任务VO
     */
    private void processAlipayTaskExecuteTimeForDo2Vo(TaskVO taskVO) {
        // 构建需要处理的任务类型列表
        List<String> alipayTaskTypes = Arrays.asList(
            TaskType.ALIPAY_DIRECT_MSG.getCode(),
            TaskType.ALIPAY_GROUP_MSG.getCode()
        );
        
        // 如果不是支付宝任务类型，直接返回
        if (!alipayTaskTypes.contains(taskVO.getTaskTypeCode())) {
            return;
        }
        
        TaskTriggerTypeEnum triggerTypeEnum = TaskTriggerTypeEnum.getByCode(taskVO.getTriggerType());
        
        // 处理单次执行类型的时间
        if (SINGLE_EXECUTE.equals(triggerTypeEnum)) {
            processSingleExecuteTimeForDo2Vo(taskVO);
            return;
        }
        
        // 处理执行计划类型的时间（仅支付宝群发消息）
        if (EXECUTION_PLAN.equals(triggerTypeEnum) && TaskType.ALIPAY_GROUP_MSG.getCode().equals(taskVO.getTaskTypeCode())) {
            processExecutionPlanTimeForDo2Vo(taskVO);
        }
    }
    
    /**
     * 处理do2Vo中单次执行时间
     * @param taskVO 任务VO
     */
    private void processSingleExecuteTimeForDo2Vo(TaskVO taskVO) {
        // 如果extraInfo为null，直接返回
        if (Objects.isNull(taskVO.getExtraInfo())) {
            PlatformLogUtil.logFail("支付宝TaskVO构建，获取支付宝侧触发时间失败，extraInfo为null", LogListUtil.newArrayList(taskVO));
            return;
        }
        // 从extraInfo中获取支付宝测触发时间
        try {
            String aliPayTriggerTimeValue = taskVO.getExtraInfo().getString(ALIPAY_TRIGGER_TIME);
            if (StringUtils.isBlank(aliPayTriggerTimeValue)) {
                PlatformLogUtil.logFail("支付宝TaskVO构建，获取支付宝侧触发时间失败，aliPayTriggerTimeValue为null", LogListUtil.newArrayList(taskVO));
                return;
            }
            taskVO.setTriggerTimeValue(aliPayTriggerTimeValue);
            JSONObject extraInfo = taskVO.getExtraInfo();
            if (Objects.isNull(extraInfo) || !extraInfo.containsKey(TRIGGER_TIME_VALUE)) {
                return;
            }
            extraInfo.put(TRIGGER_TIME_VALUE, aliPayTriggerTimeValue);
        } catch (Exception e) {
            PlatformLogUtil.logException("支付宝TaskVO构建，获取支付宝侧触发时间出错", e.getMessage(), e, LogListUtil.newArrayList(taskVO));
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
        }
    }
    
    /**
     * 处理do2Vo中执行计划时间
     * @param taskVO 任务VO
     */
    private void processExecutionPlanTimeForDo2Vo(TaskVO taskVO) {
        if (Objects.isNull(taskVO.getExtraInfo())) {

        }
        // 从triggerConfig中获取原始执行时间
        if (taskVO.getTriggerConfig() != null) {
            Object aliPayExecuteStartTime = taskVO.getTriggerConfig().get(ALIPAY_CYCLE_TASK_EXECUTE_TIME);
            if (aliPayExecuteStartTime != null) {
                // 设置执行时间为原始执行时间
                taskVO.setExecuteStartTime(aliPayExecuteStartTime.toString());
            }
        }

        // 如果extraInfo为null，直接返回
        if (Objects.isNull(taskVO.getExtraInfo())) {
            PlatformLogUtil.logFail("支付宝TaskVO构建，获取支付宝侧触发时间失败，extraInfo为null", LogListUtil.newArrayList(taskVO));
            return;
        }
        // 从extraInfo中获取支付宝测触发时间
        try {
            String aliPayCycleTaskExecuteTime = taskVO.getExtraInfo().getString(ALIPAY_CYCLE_TASK_EXECUTE_TIME);
            if (StringUtils.isBlank(aliPayCycleTaskExecuteTime)) {
                PlatformLogUtil.logFail("支付宝TaskVO构建，获取支付宝侧触发时间失败，alipayCycleTaskExecuteTime为null", LogListUtil.newArrayList(taskVO));
                return;
            }
            taskVO.setExecuteStartTime(aliPayCycleTaskExecuteTime);
        } catch (Exception e) {
            PlatformLogUtil.logException("支付宝TaskVO构建，获取支付宝侧触发时间出错", e.getMessage(), e, LogListUtil.newArrayList(taskVO));
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_TIME_ILLEGAL);
        }
    }

    private void checkSubscribeMsgTaskExecuteStrategy(String taskTypeCode, JSONObject extJs) {
        if (!Objects.equals(TaskType.MINI_PROGRAM_SUBSCRIBE_MSG, TaskType.getByCode(taskTypeCode))) {
            return;
        }
        if (Objects.isNull(extJs)) {
            PlatformLogUtil.logFail("订阅消息任务执行策略校验失败，extJs为null", LogListUtil.newArrayList(taskTypeCode, extJs));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
        }
        if (!extJs.containsKey(TaskConstant.SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY)) {
            PlatformLogUtil.logFail("订阅消息任务执行策略校验失败，extJs中不包含任务执行策略", LogListUtil.newArrayList(taskTypeCode, extJs));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
        }
        Integer executeStrategyCode = extJs.getInteger(TaskConstant.SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
        if (!NumberUtils.validInteger(executeStrategyCode) || Objects.isNull(SubscribeMsgTaskStrategyEnum.of(executeStrategyCode))) {
            PlatformLogUtil.logFail("订阅消息任务执行策略校验失败，任务执行策略非法", LogListUtil.newArrayList(taskTypeCode, extJs));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
        }
    }
}
