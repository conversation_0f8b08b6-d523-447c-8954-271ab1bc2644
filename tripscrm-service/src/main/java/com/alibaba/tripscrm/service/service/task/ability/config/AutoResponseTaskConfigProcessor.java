package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.dto.task.AutoResponseTaskExtInfoDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/3 14:49
 **/
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AutoResponseTaskConfigProcessor extends AbstractTaskConfigProcessor {

    private final MaterialService materialService;
    private final WechatGroupService wechatGroupService;
    private final TagInfoService tagInfoService;
    private final ActivityInfoService activityInfoService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        JSONObject extraInfo = super.getExtraInfo(taskInfoDO);
        if (StringUtils.isBlank(taskInfoDO.getExtInfo())) {
            return extraInfo;
        }
        AutoResponseTaskExtInfoDTO autoResponseTaskExtInfoDTO = JSONObject.parseObject(taskInfoDO.getExtInfo(), AutoResponseTaskExtInfoDTO.class);
        if (autoResponseTaskExtInfoDTO == null) {
            return extraInfo;
        }
        if (CollectionUtils.isNotEmpty(autoResponseTaskExtInfoDTO.getResponseMaterialIdList())) {
            extraInfo.put("responseMaterialIdList", autoResponseTaskExtInfoDTO.getResponseMaterialIdList().stream().sorted().collect(Collectors.toList()));
        }
        List<MaterailInfoDO> materialInfoDOList = materialService.listByIds(autoResponseTaskExtInfoDTO.getResponseMaterialIdList()).stream().sorted(Comparator.comparing(MaterailInfoDO::getId)).collect(Collectors.toList());
        extraInfo.put("responseMaterialNameList", materialInfoDOList.stream().map(MaterailInfoDO::getName).collect(Collectors.toList()));
        List<WechatGroupVO> wechatGroupVOList = wechatGroupService.listByChatIdList(autoResponseTaskExtInfoDTO.getChatIdList(), false);
        extraInfo.put("chatNameList", wechatGroupVOList.stream().map(WechatGroupVO::getName).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(autoResponseTaskExtInfoDTO.getIncludeTagList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(autoResponseTaskExtInfoDTO.getIncludeTagList());
            extraInfo.put("includeTagNameList", tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        }
        List<ActivityInfoDO> activityInfoList = activityInfoService.selectByIdList(autoResponseTaskExtInfoDTO.getActivityIdList());
        extraInfo.put("activityNameList", activityInfoList.stream().map(ActivityInfoDO::getName).collect(Collectors.toList()));
        Set<String> allMatchKeywordList = new HashSet<>(Optional.ofNullable(autoResponseTaskExtInfoDTO.getAllMatchKeywordList()).orElse(new ArrayList<>()));
        Set<String> anyMatchKeywordList = new HashSet<>(Optional.ofNullable(autoResponseTaskExtInfoDTO.getAnyMatchKeywordList()).orElse(new ArrayList<>()));
        allMatchKeywordList.addAll(anyMatchKeywordList);
        extraInfo.put("keyword", allMatchKeywordList);
        return extraInfo;
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {

    }

    @Override
    public TaskType getTaskType() {
        return TaskType.AUTO_RESPONSE;
    }
}
