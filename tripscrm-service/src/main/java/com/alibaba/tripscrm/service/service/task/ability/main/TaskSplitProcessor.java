package com.alibaba.tripscrm.service.service.task.ability.main;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.middleware.OssClientManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceQueryRequest;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceSaveVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.ability.data.split.AbstractTaskDataSplitProcessor;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Objects;

import static com.alibaba.tripscrm.TripSCRMErrorCode.UPLOAD_SUB_TASK_DATA_OSS_FAIL;
import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;
import static com.alibaba.tripscrm.service.constant.TairConstant.LDB_TASK_TEST_DATA_PREFIX;

/**
 * 任务执行_主任务_子任务拆分
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskSplitProcessor implements ExecuteProcessor {
    @Autowired
    private TaskSplitProcessor taskSplitProcessor;
    private final SubTaskInstanceService subTaskInstanceService;
    private final OssClientManager ossClientManager;
    private final LdbTairManager ldbTairManager;

    @Override
    @TaskExecuteLog("任务执行_主任务_子任务拆分")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        // 判断是否需要进行任务拆分
        TaskDataVO taskDataVO = context.getTaskDataVO();
        if (Objects.isNull(taskDataVO) || Objects.isNull(taskDataVO.getData())) {
            throw new TripscrmException(TripSCRMErrorCode.NOT_FOUND_TASK_EXECUTE_DATA);
        }

        // 返回拆分后的任务列表
        taskSplitProcessor.doSplit(context);
        return TripSCRMResult.success(null);
    }

    /**
     * 拆分步骤（原子事务）
     * 1. 创建子任务实例数据
     * 2. 上传到oss上
     * 3. 返回处理好的数据
     */
    @Transactional
    @TairLock(value = "'splitSubtask_' + #context.instanceId", waitMilliseconds = 10000)
    public void doSplit(TaskExecuteContext context) {
        List<TodoTaskVO> result = Lists.newArrayList();
        Long taskId = context.getTaskId();
        Long instanceId = context.getInstanceId();

        // 子任务实例已经创建完毕，不需要再执行
        SubTaskInstanceQueryRequest request = new SubTaskInstanceQueryRequest();
        request.setMainTaskInstanceId(instanceId);
        List<SubTaskInstanceDO> subTaskInstanceDOList = subTaskInstanceService.list(request);
        if (!CollectionUtils.isEmpty(subTaskInstanceDOList)) {
            throw new TripscrmException(TripSCRMErrorCode.SUB_TASK_INSTANCE_ALREADY_EXISTS);
        }

        // 取出所有刚刚查询出的数据
        List<TaskDataVO.DataBodyVO> allData = context.getTaskDataVO().getData();
        if (CollectionUtils.isEmpty(allData)) {
            PlatformLogUtil.logFail("主任务执行-子任务拆分，查询出的数据为空", LogListUtil.newArrayList(taskId, instanceId, context));
            return;
        }
        PlatformLogUtil.logInfo("主任务执行-子任务拆分，拆分开始", LogListUtil.newArrayList(taskId, instanceId, allData.size()));

        List<List<TaskDataVO.DataBodyVO>> subDataList = AbstractTaskDataSplitProcessor.splitData(context);
        for (List<TaskDataVO.DataBodyVO> subData : subDataList) {
            // 创建子任务实例数据
            SubTaskInstanceSaveVO addRequest = new SubTaskInstanceSaveVO();
            addRequest.setMainTaskId(taskId);
            addRequest.setMainTaskInstanceId(instanceId);
            addRequest.setStatus(TaskInstanceStatusEnum.READY.getStatus());
            addRequest.setTaskConfig(JSONObject.toJSONString(context.getTaskInfoDOSnapshot()));
            addRequest.setMaterialInfo(JSONObject.toJSONString(context.getMaterialSnapshot()));
            addRequest.setAbBucketList(JSONObject.toJSONString(context.getAbBucketListSnapshot()));
            Long subTaskInstanceId = subTaskInstanceService.add(addRequest);

            // 存储到oss
            String path = String.format(TaskConstant.SUB_TASK_DATA_OSS_PATH, taskId, subTaskInstanceId);
            boolean saveResult = this.getSaveResult(path, subData, context);
            if (saveResult) {
                // 更新oss文件路径到ext字段里
                SubTaskInstanceSaveVO updRequest = new SubTaskInstanceSaveVO();
                updRequest.setId(subTaskInstanceId);
                updRequest.setMainTaskInstanceId(instanceId);
                JSONObject extJs = new JSONObject();
                extJs.put(TaskConstant.SUB_TASK_EXT_OSS_PATH_KEY, path);
                extJs.put(TaskConstant.SUB_TASK_EXT_DATA_COUNT, subData.size());
                updRequest.setExtInfo(JSON.toJSONString(extJs));
                subTaskInstanceService.update(updRequest);

                // 封装成待办任务视图
                TodoTaskVO todoTaskVO = new TodoTaskVO();
                todoTaskVO.setIsSub(Boolean.TRUE);
                todoTaskVO.setTaskId(taskId);
                todoTaskVO.setInstanceId(subTaskInstanceId);
                todoTaskVO.setData(subData);
                result.add(todoTaskVO);
                continue;
            }
            PlatformLogUtil.logFail("【任务执行异常】拆分的数据分片上传OSS失败", LogListUtil.newArrayList(taskId, instanceId, path));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】拆分的数据分片上传OSS失败，任务ID:%s，实例ID:%s，路径Path:%s", taskId, instanceId, path));
            throw new TripscrmException(UPLOAD_SUB_TASK_DATA_OSS_FAIL);
        }
        // 设置到上下文里
        context.setTodoTaskVOList(result);
        PlatformLogUtil.logInfo("主任务执行-子任务拆分，拆分结束", LogListUtil.newArrayList(taskId, instanceId, allData.size(), subDataList.size()));
    }

    private boolean getSaveResult(String path, List<TaskDataVO.DataBodyVO> subList, TaskExecuteContext context) {
        if (context.getTestFlag()) {
            String instanceId = path.split("_")[2];
            ldbTairManager.put(LDB_TASK_TEST_DATA_PREFIX + instanceId, JSON.toJSONString(subList), EXPIRE_TIME_DAY_UNIT);
            return true;
        }

        if (ossClientManager.save2oss(path, subList)) {
            return true;
        }

        // 未成功，重试一次
        return ossClientManager.save2oss(path, subList);
    }
}
