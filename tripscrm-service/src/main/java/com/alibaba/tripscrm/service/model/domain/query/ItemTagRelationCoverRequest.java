package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/6/6 19:43
 */
@Data
public class ItemTagRelationCoverRequest implements Serializable {
    /**
     * item类型
     */
    private Integer itemType;

    /**
     * 关联id
     */
    private String itemId;

    /**
     * 创建者Id
     */
    private String creatorId;

    /**
     * 创建者Name
     */
    private String creatorName;

    /**
     * 标签id列表
     */
    private List<String> tagIdList;
}
