package com.alibaba.tripscrm.service.manager.middleware;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.common.utils.BinaryUtil;
import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.*;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.common.keycenter.security.Cryptograph;
import com.taobao.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * OSS 工具类，从触达应用迁移过来的，目前ak/sk使用的也是触达的
 */
@Component
@Slf4j
public class OssClientManager {
    /**
     * 默认过期时间(30s)
     */
    private static final long DEFAULT_EXPIRE_TIME = 30 * 1000;
    /**
     * 默认最大文件
     */
    private static final long MAX_FILE_SIZE = 1024 * 1024 * 1000;
    private static final int OSS_CONN_MAX_TIME_OUT = 3000;
    private static final FastDateFormat FILENAME_FORMAT = FastDateFormat.getInstance("yyyyMMddHHmmss");
    /**
     * SCRM专用OSS BUCKET
     */
    private static final String SCRM_BUCKET = "fliggy-scrm";
    private static final String SCRM_ENDPOINT = "oss-cn-hangzhou.aliyuncs.com";
    /**
     * ZHUGE专用OSS BUCKET
     */
    private static final String ZHUGE_BUCKET = "fliggy-zhuge";
    private static final String ZHUGE_ENDPOINT = "cn-zhangjiakou.oss.aliyuncs.com";
    /**
     * PUSH专用OSS BUCKET
     */
    private static final String PUSH_BUCKET = "fliggy-touch-push-batch";
    private static final String OSS_ENCODING = "UTF-8";

    @Resource
    private Cryptograph cryptograph;

    @Value("${oss.allowOrigin.domain}")
    private String allowDomain;
    @Value("${oss.accessId}")
    private String accessId;
    @Value("${oss.accessKey}")
    private String accessKey;

    private OSS ossClientForPush;
    private OSS ossClientForZhuGe;
    private OSS ossClientForSCRM;

    @PostConstruct
    public void initOssClient() {
        // SCRM OSS客户端
        ossClientForSCRM = new OSSClientBuilder().build(SCRM_ENDPOINT, accessId, cryptograph.decrypt(accessKey, "tripscrm_aone_key"));
        // 触达平台OSS客户端
        ClientConfiguration config = new ClientConfiguration();
        config.setConnectionTimeout(OSS_CONN_MAX_TIME_OUT);
        ossClientForPush = new OSSClientBuilder().build(SCRM_ENDPOINT, accessId, cryptograph.decrypt(accessKey, "tripscrm_aone_key"));
        // 诸葛平台OSS客户端
        ossClientForZhuGe = new OSSClientBuilder().build(ZHUGE_ENDPOINT, accessId, cryptograph.decrypt(accessKey, "tripscrm_aone_key"));
    }

    /**
     * 存储内容到oss上【push】
     *
     * @param filePath filePath
     * @param data     data
     * @return return
     */
    public boolean save2oss(String filePath, Object data) {
        try {
            byte[] contentBytes = JSON.toJSONBytes(data);
            ObjectMetadata objectMeta = new ObjectMetadata();
            objectMeta.setContentLength(contentBytes.length);
            objectMeta.setLastModified(new Date());
            Map<String, String> userMap = new HashMap<String, String>(16);
            userMap.put("tms-contenttype", OSS_ENCODING);
            objectMeta.setUserMetadata(userMap);
            InputStream input = new ByteArrayInputStream(contentBytes);
            PutObjectResult writeOssResult = ossClientForPush.putObject(PUSH_BUCKET, filePath, input, objectMeta);
            if (writeOssResult == null) {
                PlatformLogUtil.logFail("存储内容到oss上失败", LogListUtil.newArrayList(filePath));
                return false;
            }
            if (StringUtils.isBlank(writeOssResult.getETag())) {
                PlatformLogUtil.logFail("存储内容到oss上响应ETag为空", LogListUtil.newArrayList(filePath));
                return false;
            }
            PlatformLogUtil.logFail("存储内容到oss上成功", LogListUtil.newArrayList(filePath));
            return true;
        } catch (Throwable e) {
            PlatformLogUtil.logException("存储内容到oss上异常", e.getMessage(), e, LogListUtil.newArrayList(filePath));
        }
        return false;
    }

    /**
     * 读取oss文件内容，返回字符串，自行转化成所需对象【push】
     *
     * @param ossPath 路径
     * @return 结果json字符串
     */
    public String readOssFile(String ossPath) throws IOException {
        if (org.apache.commons.lang3.StringUtils.isBlank(ossPath)) {
            PlatformLogUtil.logFail("读取oss文件内容，入参路径为空");
            return StringUtils.EMPTY;
        }
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();
        try {
            OSSObject ossObject = ossClientForPush.getObject(PUSH_BUCKET, ossPath);
            if (Objects.isNull(ossObject)) {
                PlatformLogUtil.logFail("读取oss文件内容，oss响应为空", LogListUtil.newArrayList(ossPath));
                return StringUtils.EMPTY;
            }
            br = new BufferedReader(new InputStreamReader(ossObject.getObjectContent(), StandardCharsets.UTF_8));
            String line;
            while (Objects.nonNull(line = br.readLine())) {
                sb.append(line);
            }
        } finally {
            if (Objects.nonNull(br)) {
                try {
                    br.close();
                } catch (IOException e) {
                    PlatformLogUtil.logException("读取oss文件内容，IO处理异常", e.getMessage(), e, LogListUtil.newArrayList());
                }
            }
        }

        return sb.toString();
    }

    /**
     * 获取诸葛OSS文件【诸葛】
     *
     * @param filePath filePath
     * @return return
     */
    @AteyeInvoker(description = "【OSS工具类】获取诸葛OSS文件", paraDesc = "filePath")
    public OSSObject getZhugeOssObject(String filePath) {
        return ossClientForZhuGe.getObject(ZHUGE_BUCKET, filePath);
    }

    /**
     * 获取诸葛OSS文件流【诸葛】
     *
     * @param filePath filePath
     * @return return
     */
    public InputStream getZhugeOssContent(String filePath) {
        OSSObject ossObject = getZhugeOssObject(filePath);
        if (ossObject == null) {
            return null;
        }
        return ossObject.getObjectContent();
    }

    /**
     * 根据文件地址存储到OSS【通用方法】
     *
     * @param dir        目录
     * @param objectName 文件名
     * @param filePath   文件地址
     * @return 文件存储路径
     */
    public String storeByFilePath(String dir, String objectName, String filePath) {
        try {
            String fullObjectName = dir + "/" + objectName;
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(SCRM_BUCKET, fullObjectName, new URL(filePath).openStream());
            PutObjectResult result = ossClientForSCRM.putObject(putObjectRequest);
//            return fullObjectName;
            return "https://" + SCRM_BUCKET + "." + SCRM_ENDPOINT + "/" + fullObjectName;
        } catch (Exception e) {
            PlatformLogUtil.logException("根据文件地址存储到OSS出现异常", e.getMessage(), e, LogListUtil.newArrayList());
            return filePath;
        }
    }

    /**
     * 根据文件流存储到OSS【通用方法】
     *
     * @param dir        目录
     * @param objectName 文件名
     * @param inputStream 文件流
     * @return 文件存储路径
     */
    public String storeByStream(String dir, String objectName, InputStream inputStream) {
        try {
            String fullObjectName = dir + "/" + objectName;
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(SCRM_BUCKET, fullObjectName, inputStream);
            PutObjectResult result = ossClientForSCRM.putObject(putObjectRequest);
//            return fullObjectName;
            return "https://" + SCRM_BUCKET + "." + SCRM_ENDPOINT + "/" + fullObjectName;
        } catch (Exception e) {
            PlatformLogUtil.logException("根据文件流存储到OSS异常", e.getMessage(), e, LogListUtil.newArrayList());
            return null;
        }
    }

//    /**
//     * 获取可访问的文件地址，1小时后失效
//     *
//     * @param filePath filePath
//     * @return return
//     */
//    public String getOssUrl(String filePath) {
//        try {
//            String fullPath = "https://" + SCRM_BUCKET + "." + SCRM_ENDPOINT + "/" + filePath;
//            URL ossUrl = ossClientForSCRM.generatePresignedUrl(SCRM_BUCKET, fullPath, DateUtils.addHours(new Date(), 1));
//            return ossUrl.toString();
//        } catch (Exception e) {
//            logger.error("getOssUrl error. filePath is " + filePath, e);
//            return filePath;
//        }
//    }

    /**
     * 仅获取授权信息，用于前端直接上传到OSS【通用方法】
     *
     * @param uploadDirectory 目录
     * @param response        response
     * @return responseMap
     */
    public Map<String, String> auth(String uploadDirectory, String fileName, HttpServletResponse response) {
        Map<String, String> responseMap = new LinkedHashMap<>();
        //  上传文件目录
        //  Calendar calendar = Calendar.getInstance();
        //  String uploadDirectory = DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(calendar);
        Long expireEndTimestamp = System.currentTimeMillis() + DEFAULT_EXPIRE_TIME;
        Date expireEndTime = new Date(expireEndTimestamp);
        String randomFileName = RandomUtil.getRandomString(10) + FILENAME_FORMAT.format(new Date());
        if (StringUtils.isNotEmpty(fileName) && fileName.contains(".")) {
            randomFileName += fileName.substring(fileName.lastIndexOf("."));
        }

        String hostAddress = "https://" + SCRM_BUCKET + "." + SCRM_ENDPOINT;
        PolicyConditions policyConditions = new PolicyConditions();
        policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0,
                MAX_FILE_SIZE);
        policyConditions.addConditionItem(MatchMode.Exact, PolicyConditions.COND_KEY, uploadDirectory + "/" + randomFileName);
        String postPolicy = ossClientForSCRM.generatePostPolicy(expireEndTime, policyConditions);
        byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
        String encodedPolicy = BinaryUtil.toBase64String(binaryData);
        String postSignature = ossClientForSCRM.calculatePostSignature(postPolicy);
        // 响应数据
        responseMap.put("accessId", accessId);
        responseMap.put("policy", encodedPolicy);
        responseMap.put("signature", postSignature);
        responseMap.put("dir", uploadDirectory);
        responseMap.put("host", hostAddress);
        responseMap.put("expire", String.valueOf(expireEndTime.getTime() / 1000));
        responseMap.put("randomName", randomFileName);
        // 设置响应数据
        response.setHeader("Access-Control-Allow-Origin", allowDomain);
        response.setHeader("Access-Control-Allow-Methods", "GET, POST");
        return responseMap;
    }

    /**
     * 仅获取授权信息，用于前端直接上传到OSS【通用方法】
     *
     * @param uploadDirectory 目录
     * @return responseMap
     */
    public Map<String, String> auth(String uploadDirectory) {
        Map<String, String> responseMap = new LinkedHashMap<>();
        //  上传文件目录
        //  Calendar calendar = Calendar.getInstance();
        //  String uploadDirectory = DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(calendar);
        Long expireEndTimestamp = System.currentTimeMillis() + DEFAULT_EXPIRE_TIME;
        Date expireEndTime = new Date(expireEndTimestamp);
        String hostAddress = "https://" + SCRM_BUCKET + "." + SCRM_ENDPOINT;
        PolicyConditions policyConditions = new PolicyConditions();
        policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0,
                MAX_FILE_SIZE);
        policyConditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY,
                uploadDirectory);
        String postPolicy = ossClientForSCRM.generatePostPolicy(expireEndTime, policyConditions);
        byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
        String encodedPolicy = BinaryUtil.toBase64String(binaryData);
        String postSignature = ossClientForSCRM.calculatePostSignature(postPolicy);
        // 响应数据
        responseMap.put("accessId", accessId);
        responseMap.put("policy", encodedPolicy);
        responseMap.put("signature", postSignature);
        responseMap.put("dir", uploadDirectory);
        responseMap.put("host", hostAddress);
        responseMap.put("expire", String.valueOf(expireEndTime.getTime() / 1000));
        responseMap.put("randomName", RandomUtil.getRandomString(10) + FILENAME_FORMAT.format(new Date()));
        return responseMap;
    }

    public static void main(String[] args) {
        String x = "http://kfpt.oss-cn-hangzhou.aliyuncs.com/pc/pcwork/msgfile/20231107/7ced9fcb3257669093f98a0f2e9d7f56/8d4295a2958f437e90637de2a0ed22e3.jpg";
        OSS ossClientForSCRM = new OSSClientBuilder().build(SCRM_ENDPOINT, "LTAI5tGYUFeniA3rYUjVSerH", "/vCsYV3PGQ9Yk0hYHUj5ZzTRzOj+xjdNkawIlXdrjrQ=");
        // 创建PutObjectRequest对象。
        PutObjectRequest putObjectRequest = null;
        try {
            putObjectRequest = new PutObjectRequest(SCRM_BUCKET, "xxxxx", new URL(x).openStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        PutObjectResult result = ossClientForSCRM.putObject(putObjectRequest);
    }
}
