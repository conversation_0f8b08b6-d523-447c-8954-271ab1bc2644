package com.alibaba.tripscrm.service.service.strategy.callback;

import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;

/**
 * proxy回调消息处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
public interface ProxyCallbackProcessor {
    /**
     * 回调类型
     *
     * @return 回调类型
     */
    EventTypeEnum type();
    /**
     * 该回调类型处理方法
     *
     * @param scrmCallbackMsg 回调类型
     */
    Boolean handle(ScrmCallbackMsg scrmCallbackMsg);
}
