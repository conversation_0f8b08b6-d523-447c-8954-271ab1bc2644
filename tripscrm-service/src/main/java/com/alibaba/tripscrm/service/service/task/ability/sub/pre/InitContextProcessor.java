package com.alibaba.tripscrm.service.service.task.ability.sub.pre;

import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 任务执行_子任务_任务执行上下文初始化
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class InitContextProcessor implements ExecuteProcessor {
    @Override
    @TaskExecuteLog("任务执行_子任务_任务执行上下文初始化")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        // 初始化任务执行上下文
        initContext(context);
        return TripSCRMResult.success(null);
    }

    private void initContext(TaskExecuteContext context) {
        // 设置任务开始执行时间
        context.setStartTime(System.currentTimeMillis());
    }
}
