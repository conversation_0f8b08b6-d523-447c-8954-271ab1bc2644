package com.alibaba.tripscrm.service.model.domain.context;

import com.alibaba.fastjson.JSON;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 延迟任务上下文
 *
 * <AUTHOR>
 * @date 2023/5/24
 */
@Data
public class DelayTaskContext implements Serializable {
    private static final long serialVersionUID = 5801948832369613068L;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 触发时间
     */
    private Long triggerTime;
    /**
     * 触发类型
     */
    private String triggerType;

    /**
     * 是否需要秒级延迟
     * false时，精确到分钟级即可
     * true时，尽量帮忙满足，只能做到5秒内
     */
    private Boolean secondDelayLevel = false;

    /**
     * 重试次数
     */
    private Integer delayNum;
    /**
     * 重试key
     */
    private String delayKey;
    /**
     * 任务执行参数
     */
    private TaskBuildContext taskBuildContext;

    public DelayTaskContext() {
    }

    public DelayTaskContext(String delayKey, Integer delayNum, Long triggerTime, String triggerType, TaskInfoDO taskInfoDO, Integer targetType, String targetId, Map<String, Object> extInfo) {
        this(delayKey, delayNum, triggerTime, triggerType, taskInfoDO, targetType, targetId, extInfo, TimeUnit.HOURS);
    }

    public DelayTaskContext(String delayKey, Integer delayNum, Long triggerTime, String triggerType, TaskInfoDO taskInfoDO, Integer targetType, String targetId, Map<String, Object> extInfo, TimeUnit timeUnit) {
        this.taskId = taskInfoDO.getId();
        this.triggerType = triggerType;
        this.taskBuildContext = new TaskBuildContext();
        // 根据传的时间加上任务配置计算真实发送时间 单位s
        if (TriggerTimeEnum.CUSTOM.getCode().equals(Integer.parseInt(triggerType))) {
            timeUnit = Objects.isNull(timeUnit) ? TimeUnit.HOURS : timeUnit;
            switch (timeUnit) {
                case HOURS:
                    this.triggerTime = triggerTime + taskInfoDO.getOffsetTime() * 60 * 60;
                    break;
                default:
                    this.triggerTime = triggerTime + taskInfoDO.getOffsetTime();
            }
        }
        this.taskBuildContext.setActivityId(taskInfoDO.getActivityId());
        this.taskBuildContext.setTaskId(taskInfoDO.getId());
        this.taskBuildContext.setTargetType(targetType);
        this.taskBuildContext.setTargetId(targetId);
        this.taskBuildContext.setTaskInfoDO(taskInfoDO);
        this.taskBuildContext.setContext(MapUtils.isEmpty(extInfo) ? "{}" : JSON.toJSONString(extInfo));
        this.delayKey = delayKey;
        this.delayNum = delayNum;
    }
}
