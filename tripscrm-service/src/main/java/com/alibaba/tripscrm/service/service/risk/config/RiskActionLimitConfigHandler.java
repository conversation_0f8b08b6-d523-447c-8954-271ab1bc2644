package com.alibaba.tripscrm.service.service.risk.config;

import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.model.domain.request.AccountRiskConfigUpdateRequest;
import com.alibaba.tripscrm.service.model.domain.risk.*;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigDetailVO;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigVO;
import com.alibaba.tripscrm.service.model.exception.WarnException;
import com.alibaba.tripscrm.service.service.risk.RiskConfigHandler;
import com.alibaba.tripscrm.service.service.risk.RiskActionLimitTargetHandler;
import com.alibaba.tripscrm.service.service.risk.status.RiskActionStatusController;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 行动项限流 配置处理器
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class RiskActionLimitConfigHandler extends RiskConfigHandler {
    @Resource
    private RiskActionStatusController riskActionStatusController;

    @Override
    public void buildListShow(UserRiskSchema schema, AccountRiskConfigVO riskConfig) {
        if (schema != null) {
            List<RiskActionConfig> actionConfigs = schema.getActionConfigs();
            List<String> actionCodes = Arrays.stream(RiskActionEnum.values()).map(RiskActionEnum::getActionCode).collect(Collectors.toList());
            actionConfigs = actionConfigs.stream().filter(x -> actionCodes.contains(x.getActionCode()) && x.getEnable()).collect(Collectors.toList());
            riskConfig.setActionConfigs(actionConfigs.size() + "个行动项限流保护中");
        } else {
            riskConfig.setActionConfigs("遵循默认风控");
        }
    }

    @Override
    public void buildDetailShow(UserRiskSchema schema, AccountRiskConfigDetailVO result) {
        // 设置行动项限流，加载全量行动，设置已设置的配置
        List<RiskActionConfig> actionConfigs = new ArrayList<>();
        if (schema != null) {
            actionConfigs = schema.getActionConfigs();
        }
        List<RiskActionConfig> showActionConfigs = new ArrayList<>(RiskActionEnum.values().length);
        Map<String, RiskActionConfig> actionConfigMap = actionConfigs.stream()
                .collect(Collectors.toMap(RiskActionConfig::getActionCode, Function.identity()));
        for (RiskActionEnum riskAction : RiskActionEnum.values()) {
            RiskActionConfig configValue = actionConfigMap.get(riskAction.getActionCode());
            RiskActionLimitTargetHandler riskActionLimitTargetHandler = RiskActionLimitTargetHandler.get(riskAction.getTarget());
            showActionConfigs.add(riskActionLimitTargetHandler.fillConfig(riskAction, configValue));
        }
        result.setActionConfigs(showActionConfigs);
    }

    @Override
    public void updateConfig(UserRiskSchema schema, AccountRiskConfigUpdateRequest param, AccountRiskConfigVO.RiskIdInfo riskIdInfo) {
        // 设置行动项频率限制
        List<RiskActionConfig> actionConfigs = schema.getActionConfigs();
        if (param.getActionConfig() != null) {
            RiskActionConfig newActionConfig = param.getActionConfig();
            if (newActionConfig.getEnable()
                    && (newActionConfig.getMinuteRateLimit() == null || newActionConfig.getMinuteRateLimit() == 0)
                    && (newActionConfig.getHourRateLimit() == null || newActionConfig.getHourRateLimit() == 0)
                    && (newActionConfig.getDayRateLimit() == null || newActionConfig.getDayRateLimit() == 0)) {
                throw new WarnException("请填写限流配置，不可全为空");
            }
            boolean minuteRateLimitNotNull = newActionConfig.getMinuteRateLimit() != null;
            boolean hourRateLimitNotNull = newActionConfig.getHourRateLimit() != null;
            boolean dayRateLimitNotNull = newActionConfig.getDayRateLimit() != null;
            if (!param.getForce() && minuteRateLimitNotNull && hourRateLimitNotNull && newActionConfig.getHourRateLimit() <= newActionConfig.getMinuteRateLimit()) {
                throw new WarnException("小时级限流值必须大于分钟级限流值");
            }
            if (!param.getForce() && hourRateLimitNotNull && dayRateLimitNotNull && newActionConfig.getDayRateLimit() <= newActionConfig.getHourRateLimit()) {
                throw new WarnException("天级限流值必须大于小时级限流值");
            }
            if (!param.getForce() && minuteRateLimitNotNull && dayRateLimitNotNull && newActionConfig.getDayRateLimit() <= newActionConfig.getMinuteRateLimit()) {
                throw new WarnException("天级限流值必须大于分钟级限流值");
            }
            if (newActionConfig.getSubConfigs() != null) {
                for (RiskActionConfig subConfig : newActionConfig.getSubConfigs()) {
                    if (subConfig.getEnable()
                            && (subConfig.getMinuteRateLimit() == null || subConfig.getMinuteRateLimit() == 0)
                            && (subConfig.getHourRateLimit() == null || subConfig.getHourRateLimit() == 0)
                            && (subConfig.getDayRateLimit() == null || subConfig.getDayRateLimit() == 0)) {
                        throw new WarnException("请填写限流配置，不可全为空");
                    }
                    if (!param.getForce() && subConfig.getEnable() && !newActionConfig.getEnable()) {
                        throw new WarnException("子项无法启用，需先启用父项");
                    }
                    boolean subMinuteRateLimitNotNull = subConfig.getMinuteRateLimit() != null;
                    boolean subHourRateLimitNotNull = subConfig.getHourRateLimit() != null;
                    boolean subDayRateLimitNotNull = subConfig.getDayRateLimit() != null;
                    if (!param.getForce() && subMinuteRateLimitNotNull && subHourRateLimitNotNull && subConfig.getHourRateLimit() <= subConfig.getMinuteRateLimit()) {
                        throw new WarnException("小时级限流值必须大于分钟级限流值");
                    }
                    if (!param.getForce() && subHourRateLimitNotNull && subDayRateLimitNotNull && subConfig.getDayRateLimit() <= subConfig.getHourRateLimit()) {
                        throw new WarnException("天级限流值必须大于小时级限流值");
                    }
                    if (!param.getForce() && subMinuteRateLimitNotNull && subDayRateLimitNotNull && subConfig.getDayRateLimit() <= subConfig.getMinuteRateLimit()) {
                        throw new WarnException("天级限流值必须大于分钟级限流值");
                    }
                    if (!param.getForce() && subMinuteRateLimitNotNull && minuteRateLimitNotNull && newActionConfig.getMinuteRateLimit() <= subConfig.getMinuteRateLimit()) {
                        throw new WarnException("父项的分钟级限流值必须大于子项的分钟级限流值");
                    }
                    if (!param.getForce() && subHourRateLimitNotNull && hourRateLimitNotNull && newActionConfig.getHourRateLimit() <= subConfig.getHourRateLimit()) {
                        throw new WarnException("父项的小时级限流值必须大于子项的小时级限流值");
                    }
                    if (!param.getForce() && subDayRateLimitNotNull && dayRateLimitNotNull && newActionConfig.getDayRateLimit() <= subConfig.getDayRateLimit()) {
                        throw new WarnException("父项的天级限流值必须大于子项的天级限流值");
                    }
                }
            }
            Map<String, RiskActionConfig> actionConfigMap = actionConfigs.stream().collect(Collectors.toMap(RiskActionConfig::getActionCode, Function.identity()));
            actionConfigMap.put(newActionConfig.getActionCode(), newActionConfig);
            actionConfigs = new ArrayList<>(actionConfigMap.values());
            // 重置限流次数
            RiskActionEnum riskAction = RiskActionEnum.parse(newActionConfig.getActionCode());
            if (riskAction != null) {
                RiskActionLimitTargetHandler riskActionLimitTargetHandler = RiskActionLimitTargetHandler.get(riskAction.getTarget());
                riskActionLimitTargetHandler.resetLimit(riskIdInfo.getCorpId(), riskIdInfo.getUserId(), newActionConfig);
                // 移除行动项限流状态
                riskActionStatusController.toNormal(riskIdInfo.getCorpId(), riskIdInfo.getUserId(), riskAction, RiskStatusEnum.PROTECT);
            }
        }
        schema.setActionConfigs(actionConfigs);
    }
}

