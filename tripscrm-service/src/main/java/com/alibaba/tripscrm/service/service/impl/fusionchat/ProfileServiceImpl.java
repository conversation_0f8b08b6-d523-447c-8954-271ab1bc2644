package com.alibaba.tripscrm.service.service.impl.fusionchat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.service.fusionchat.ProfileService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.crowd.service.domain.profile.LabelQueryDTO;
import com.fliggy.crowd.service.domain.profile.ProfileQueryRequest;
import com.fliggy.crowd.service.domain.profile.ProfileValue;
import com.fliggy.crowd.service.open.api.PicassoCommonService;
import com.fliggy.crowd.service.result.TripCrowdCommonResult;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/29 15:44
 **/
@Service
public class ProfileServiceImpl implements ProfileService {

    @Resource
    private PicassoCommonService picassoCommonService;
    @Resource
    private MetaqProducer metaqProducer;

    /**
     * 常住地
     */
    public static final String STAY_CITY_NAME = "stay_city";
    public static final String STAY_CITY_CODE = "stay_city_id";

    @Override
    public Map<String, String> getUserStayCity(String userId) {
        // 参数组装
        ProfileQueryRequest profileQueryRequest = new ProfileQueryRequest();
        profileQueryRequest.setEntityId(userId);
        LabelQueryDTO stayCityName = new LabelQueryDTO();
        stayCityName.setLabelName(STAY_CITY_NAME);
        LabelQueryDTO stayCityCode = new LabelQueryDTO();
        stayCityCode.setLabelName(STAY_CITY_CODE);
        profileQueryRequest.setLabelQueryDTOS(Lists.newArrayList(stayCityName, stayCityCode));
        // 发起请求
        Map<String, ProfileValue> data = null;
        try {
            TripCrowdCommonResult<Map<String, ProfileValue>> mapTripCrowdCommonResult = picassoCommonService.queryProfiles(profileQueryRequest);
            if (Objects.nonNull(mapTripCrowdCommonResult) && mapTripCrowdCommonResult.isSuccess()) {
                data = mapTripCrowdCommonResult.getData();
            }
        } catch (Exception e) {
            PlatformLogUtil.logFail("用户常驻地接口调用异常", Lists.newArrayList(userId, e.getMessage()));
            return null;
        }
        if (Objects.isNull(data)) {
            return null;
        }
        // 结果集组装
        HashMap<String, String> result = new HashMap<>(2);
        result.put(STAY_CITY_NAME, Optional.of(data).map(x -> x.get(STAY_CITY_NAME)).map(ProfileValue::getValue).orElse(""));
        result.put(STAY_CITY_CODE, Optional.of(data).map(x -> x.get(STAY_CITY_CODE)).map(ProfileValue::getValue).orElse(""));
        return result;
    }

    @Override
    @AteyeInvoker(description = "查询用户的标签值", paraDesc = "userId&labelName")
    public TripSCRMResult<String> getProfileValue(String userId, String labelName) {
        TripSCRMResult<Map<String, String>> result = getProfileValue(userId, Lists.newArrayList(labelName));
        if (!result.isSuccess()) {
            return TripSCRMResult.fail(result.getCode(), result.getMsg());
        }

        return TripSCRMResult.success(result.getData().getOrDefault(labelName, null));
    }

    @Override
    public TripSCRMResult<Map<String, String>> getProfileValue(String userId, List<String> labelNameList) {
        ProfileQueryRequest request = new ProfileQueryRequest();
        request.setEntityId(userId);
        request.setLabelQueryDTOS(new ArrayList<>());
        for (String labelName : labelNameList) {
            LabelQueryDTO labelQueryDTO = new LabelQueryDTO();
            labelQueryDTO.setLabelName(labelName);
            request.getLabelQueryDTOS().add(labelQueryDTO);
        }

        TripCrowdCommonResult<Map<String, ProfileValue>> profileResult = picassoCommonService.queryProfiles(request);
        if (Objects.isNull(profileResult) || !profileResult.isSuccess()) {
            PlatformLogUtil.logFail("查询用户标签值失败", LogListUtil.newArrayList(request, profileResult));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        for (String labelName : labelNameList) {
            if (!profileResult.getData().containsKey(labelName)) {
                PlatformLogUtil.logFail("查询用户标签值失败", LogListUtil.newArrayList(request, profileResult));
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }
        }

        Map<String, String> result = profileResult.getData().entrySet().stream().filter(x -> Objects.nonNull(x.getValue()) && Objects.nonNull(x.getValue().getValue())).collect(Collectors.toMap(Map.Entry::getKey, x -> x.getValue().getValue()));
        return TripSCRMResult.success(result);
    }

    @Override
    @AteyeInvoker(description = "更新用户标签", paraDesc = "userId&tagKey&tagValue")
    public TripSCRMResult<Boolean> updateTagValue(String userId, String tagKey, String tagValue) {
        if (!StringUtils.hasLength(userId) || !StringUtils.hasLength(tagKey) || Objects.isNull(tagValue)) {
            PlatformLogUtil.logFail("更新实时人群标签失败，参数非法", LogListUtil.newArrayList(userId));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        //更新实时人群标签
        JSONObject data = new JSONObject();
        data.put("id", userId);
        data.put("key", tagKey);
        data.put("val", tagValue);
        data.put("ts", String.valueOf(System.currentTimeMillis()));
        boolean sendMsgResult =  metaqProducer.send(MQEnum.PICASSO_TAOBAO_USER, null, null, data.toJSONString());
        return TripSCRMResult.success(sendMsgResult);
    }
}
