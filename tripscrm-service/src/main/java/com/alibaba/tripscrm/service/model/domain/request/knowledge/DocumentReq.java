package com.alibaba.tripscrm.service.model.domain.request.knowledge;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @desc 知识文档查询条件
 * @date 2025/9/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentReq extends BasePageRequest {

    private Long id;

    private Long spaceId;

    private Long categoryId;

    private String status;

    private String searchKey;
}
