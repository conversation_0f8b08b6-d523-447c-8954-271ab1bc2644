package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerManageGroupDO;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.domain.query.CustomerManagementGroupQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fusionchat.CustomerManageService;
import com.alibaba.tripscrm.service.service.fusionchat.CustomerManagementGroupService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.ChatGroupRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 群聊分组处理器
 */
@Service
public class ChatGroupProcessor implements WsEventProcessor {

    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private CustomerManageService customerManageService;
    @Resource
    private CustomerManagementGroupService customerManagementGroupService;
    @Resource
    private GetUserConversationListProcessor getUserConversationListProcessor;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.CHAT_GROUP;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        ChatGroupRequest request = wsEvent.getData().toJavaObject(ChatGroupRequest.class);
        if (StringUtils.isBlank(wsEvent.getUserId()) || request == null || request.getGroupId() == null
                || StringUtils.isBlank(request.getChatId()) || request.getChatType() == null) {
            PlatformLogUtil.logFail("群聊分组缺少参数", LogListUtil.newArrayList(request));
            throw new TripscrmException("群聊分组缺少参数");
        }
        // 检查分组信息
        CustomerManagementGroupQuery query = new CustomerManagementGroupQuery();
        query.setId(request.getGroupId());
        query.setUserId(wsEvent.getUserId());
        List<CustomerManageGroupDO> customerManageGroupList = customerManagementGroupService.list(query);
        if (CollectionUtils.isEmpty(customerManageGroupList)) {
            PlatformLogUtil.logFail("当前成员不存在指定管理组", LogListUtil.newArrayList(wsEvent));
            throw new TripscrmException("当前成员不存在指定管理组");
        }
        // 用户分组
        if (StringUtils.isBlank(request.getUserId())) {
            request.setUserId(wsEvent.getUserId());
        }
        if (StringUtils.isBlank(request.getCorpId())) {
            request.setCorpId(Optional.ofNullable(wsEvent.getCorpId()).orElse(SpaceInfoThreadLocalUtils.getCorpId()));
        }
        if (request.getChatType() == null) {
            request.setChatType(ChatTypeEnum.SINGLE_FOR_CUSTOMER.getValue());
        }
        Boolean result = customerManageService.chatGroup(request);
        // 返回分组结果
        if (request.getRemove()) {
            request.setGroupId(-1L);
            request.setGroupName("未分类");
        }
        JSONObject json = (JSONObject) JSONObject.toJSON(request);
        json.put("result", result);
        wsEvent.setData(json);
        webSocketFactory.pushMessageBySession(session, wsEvent, false);
    }
}
