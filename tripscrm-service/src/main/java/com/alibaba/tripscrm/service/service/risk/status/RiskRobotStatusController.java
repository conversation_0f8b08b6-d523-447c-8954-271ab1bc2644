package com.alibaba.tripscrm.service.service.risk.status;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.RiskObjectDO;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.dal.repository.RiskObjectRepository;
import com.alibaba.tripscrm.dal.repository.WechatUserRepository;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskTargetEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTargetEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbLockManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionStatus;
import com.alibaba.tripscrm.service.model.domain.risk.UserRiskSchema;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 机器人风控状态控制器
 */
@Component
public class RiskRobotStatusController {
    public static final String ROBOT_STATUS_CHANGE_PREFIX = "ROBOT_STATUS_CHANGE_";
    public static final String ROBOT_STATUS_CACHE_PREFIX = "ROBOT_STATUS_CACHE_";
    @Resource
    private RiskObjectRepository riskObjectRepository;
    @Resource
    private WechatUserRepository wechatUserRepository;
    @Resource
    private RuleService ruleService;
    @Resource
    private RiskActionStatusController riskActionStatusController;
    @Resource
    private LdbLockManager ldbLockManager;
    @Resource
    private LdbTairManager ldbTairManager;

    /**
     * 获取账号风控状态，先取缓存
     */
    public RiskStatusEnum get(String corpId, String userId) {
        String robotLdbKey = this.buildCacheKey(corpId, userId);
        Integer robotRiskStatusCode = (Integer) ldbTairManager.get(robotLdbKey);
        RiskStatusEnum robotRiskStatus;
        if (robotRiskStatusCode == null) {
            RiskObjectDO robotRiskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildRobotTargetId(corpId, userId), RiskTargetEnum.ROBOT.getValue());
            robotRiskStatus = robotRiskObject != null ? RiskStatusEnum.of(robotRiskObject.getRiskStatus()) : RiskStatusEnum.NORMAL;
            ldbTairManager.put(robotLdbKey, robotRiskStatus.getCode());
        } else {
            robotRiskStatus = RiskStatusEnum.of(robotRiskStatusCode);
        }
        return robotRiskStatus;
    }

    /**
     * 转换至正常
     */
    public void toNormal(String corpId, String userId, Boolean login, Boolean initActionStatus) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId), 1, true, () -> {
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildRobotTargetId(corpId, userId), RiskTargetEnum.ROBOT.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObject.setTargetType(RiskTargetEnum.ROBOT.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.NORMAL.getCode());
                    if (login) {
                        riskObject.setLoginTime(System.currentTimeMillis());
                    }
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.NORMAL.getCode());
                    if (login) {
                        riskObject.setLoginTime(System.currentTimeMillis());
                    }
                    riskObjectRepository.update(riskObject);
                }
                // 初始化机器人的行动项风控状态
                if (initActionStatus) {
                    // 先清空，再根据默认风控初始化行动项风控状态
                    riskObjectRepository.deleteByGroup(RiskObjectDO.buildRobotTargetId(corpId, userId), RiskTargetEnum.ACTION.getValue());
                    WechatUserDO wechatUser = wechatUserRepository.getByUserIdAndCorpId(userId, corpId);
                    RuleDO globalRule = ruleService.selectByTargetId(RuleTypeEnum.ROBOT_TYPE_RISK.getCode().byteValue(),
                            RuleTargetEnum.ROBOT_TYPE.getCode().byteValue(), RobotTypeEnum.of(wechatUser.getRobotType().intValue()).getDesc());
                    UserRiskSchema schema = JSONObject.parseObject(globalRule.getConfig(), UserRiskSchema.class);
                    if (schema.getActionStatuses() != null) {
                        for (RiskActionStatus riskActionStatus : schema.getActionStatuses()) {
                            RiskActionEnum riskAction = RiskActionEnum.parse(riskActionStatus.getActionCode());
                            if (riskAction != null) {
                                switch (RiskStatusEnum.of(riskActionStatus.getStatusCode())) {
                                    case NORMAL:
                                        riskActionStatusController.toNormal(corpId, userId, riskAction, null);
                                        break;
                                    case HOLD:
                                        riskActionStatusController.toHold(corpId, userId, riskAction);
                                        break;
                                    case ESCAPE:
                                        riskActionStatusController.toEscape(corpId, userId, riskAction);
                                        break;

                                }
                            }
                        }
                    }
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("机器人状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId));
        }
    }

    /**
     * 转换至异常
     */
    public void toAbnormal(String corpId, String userId) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId), 1, true, () -> {
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildRobotTargetId(corpId, userId), RiskTargetEnum.ROBOT.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObject.setTargetType(RiskTargetEnum.ROBOT.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.ABNORMAL.getCode());
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.ABNORMAL.getCode());
                    riskObjectRepository.update(riskObject);
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("机器人状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId));
        }
    }

    /**
     * 转换至保护
     */
    public void toProtect(String corpId, String userId, Boolean isLogin) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId), 1, true, () -> {
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildRobotTargetId(corpId, userId), RiskTargetEnum.ROBOT.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObject.setTargetType(RiskTargetEnum.ROBOT.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.PROTECT.getCode());
                    if (isLogin) {
                        riskObject.setLoginTime(System.currentTimeMillis());
                    }
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.PROTECT.getCode());
                    if (isLogin) {
                        riskObject.setLoginTime(System.currentTimeMillis());
                    }
                    riskObjectRepository.update(riskObject);
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("机器人状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId));
        }
    }

    /**
     * 转换至挂起
     */
    public void toHold(String corpId, String userId) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId), 1, true, () -> {
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildRobotTargetId(corpId, userId), RiskTargetEnum.ROBOT.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObject.setTargetType(RiskTargetEnum.ROBOT.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.HOLD.getCode());
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.HOLD.getCode());
                    riskObjectRepository.update(riskObject);
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("机器人状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId));
        }
    }

    /**
     * 转换至离线
     */
    public void toEscape(String corpId, String userId) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId), 1, true, () -> {
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildRobotTargetId(corpId, userId), RiskTargetEnum.ROBOT.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObject.setTargetType(RiskTargetEnum.ROBOT.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.ESCAPE.getCode());
                    riskObject.setLoginTime(null);
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.ESCAPE.getCode());
                    riskObject.setLoginTime(null);
                    riskObjectRepository.update(riskObject);
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("机器人状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId));
        }
    }

    private String buildLockKey(String corpId, String userId) {
        return ROBOT_STATUS_CHANGE_PREFIX + corpId + "|" + userId;
    }

    private String buildCacheKey(String corpId, String userId) {
        return ROBOT_STATUS_CACHE_PREFIX + corpId + "|" + userId + "|" + RiskTargetEnum.ROBOT.getValue();
    }
}
