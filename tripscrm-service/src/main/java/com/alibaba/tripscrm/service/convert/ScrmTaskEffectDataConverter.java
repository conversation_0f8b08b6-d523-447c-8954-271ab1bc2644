package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.ScrmTaskEffectDataDO;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmTaskTotalEffectDataDO;
import com.alibaba.tripscrm.service.enums.task.CycleEnum;
import com.alibaba.tripscrm.service.enums.task.DataTypeEnum;
import com.alibaba.tripscrm.service.model.dto.task.ScrmTaskEffectDataDTO;
import com.alibaba.tripscrm.service.model.dto.task.ScrmTaskTotalEffectDataDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/6/26 16:32
 * @Filename：ScrmTaskEffectDataConverter
 */
@Component
public class ScrmTaskEffectDataConverter {

    public ScrmTaskEffectDataDTO convert2DTO(ScrmTaskEffectDataDO taskEffectData) {
        ScrmTaskEffectDataDTO taskEffectDataDTO = new ScrmTaskEffectDataDTO();
        taskEffectDataDTO.setId(taskEffectData.getId());
        taskEffectDataDTO.setTaskId(taskEffectData.getTaskId());
        taskEffectDataDTO.setSpaceId(taskEffectData.getSpaceId());
        taskEffectDataDTO.setTaskName(taskEffectData.getTaskName());
        taskEffectDataDTO.setTaskMessageType(taskEffectData.getTaskMessageType());
        taskEffectDataDTO.setTaskMessageScene(taskEffectData.getTaskMessageScene());
        taskEffectDataDTO.setExecuteDate(taskEffectData.getExecuteDate());
        taskEffectDataDTO.setOperableUserCount(taskEffectData.getOperableUserCount());
        taskEffectDataDTO.setReachUserCount(taskEffectData.getReachUserCount());
        taskEffectDataDTO.setClickUserCount(taskEffectData.getClickUserCount());
        taskEffectDataDTO.setWxTransformUserCount(taskEffectData.getWxTransformUserCount());
        taskEffectDataDTO.setWxTransformGmv(taskEffectData.getWxTransformGmv());
        taskEffectDataDTO.setTransformUserCount(taskEffectData.getTransformUserCount());
        taskEffectDataDTO.setTransformGmv(taskEffectData.getTransformGmv());
        taskEffectDataDTO.setDeleteUserCount(taskEffectData.getDeleteUserCount());
        taskEffectDataDTO.setGmtCreate(taskEffectData.getGmtCreate());
        taskEffectDataDTO.setGmtModified(taskEffectData.getGmtModified());
        taskEffectDataDTO.setMainTaskInstanceId(taskEffectData.getMainTaskInstanceId());
        taskEffectDataDTO.setTaskType(taskEffectData.getTaskType());
        taskEffectDataDTO.setAbTestBucketId(taskEffectData.getAbTestBucketId());
        taskEffectDataDTO.setMaterialId(taskEffectData.getMaterialId());
        return taskEffectDataDTO;
    }
    public ScrmTaskTotalEffectDataDTO convert2TotalDTO(ScrmTaskTotalEffectDataDO taskTotalEffectDataDO) {
        ScrmTaskTotalEffectDataDTO taskTotalEffectDataDTO = new ScrmTaskTotalEffectDataDTO();
        taskTotalEffectDataDTO.setId(taskTotalEffectDataDO.getId());
        taskTotalEffectDataDTO.setSpaceId(taskTotalEffectDataDO.getSpaceId());
        taskTotalEffectDataDTO.setDate(taskTotalEffectDataDO.getDate());
        taskTotalEffectDataDTO.setCycleType(CycleEnum.of(taskTotalEffectDataDO.getCycleType()));
        taskTotalEffectDataDTO.setDataType(DataTypeEnum.getByCode(taskTotalEffectDataDO.getDataType()));
        taskTotalEffectDataDTO.setTaskCount(taskTotalEffectDataDO.getTaskCount());
        taskTotalEffectDataDTO.setOperableUserCount(taskTotalEffectDataDO.getOperableUserCount());
        taskTotalEffectDataDTO.setReachUserCount(taskTotalEffectDataDO.getReachUserCount());
        taskTotalEffectDataDTO.setClickUserCount(taskTotalEffectDataDO.getClickUserCount());
        taskTotalEffectDataDTO.setWxTransformUserCount(taskTotalEffectDataDO.getWxTransformUserCount());
        taskTotalEffectDataDTO.setWxTransformGmv(taskTotalEffectDataDO.getWxTransformGmv());
        taskTotalEffectDataDTO.setTransformUserCount(taskTotalEffectDataDO.getTransformUserCount());
        taskTotalEffectDataDTO.setTransformGmv(taskTotalEffectDataDO.getTransformGmv());
        taskTotalEffectDataDTO.setDeleteUserCount(taskTotalEffectDataDO.getDeleteUserCount());
        return taskTotalEffectDataDTO;

    }
}
