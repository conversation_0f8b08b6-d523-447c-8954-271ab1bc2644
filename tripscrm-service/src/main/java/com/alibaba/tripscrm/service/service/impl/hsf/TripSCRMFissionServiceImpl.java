package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.FissionTaskContext;
import com.alibaba.tripscrm.domain.ShareActInfo;
import com.alibaba.tripscrm.domain.request.TripSCRMFissionRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMFissionService;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskBuildContext;
import com.alibaba.tripscrm.service.service.task.ability.old.FissionTaskServiceImpl;
import com.alibaba.tripscrm.service.service.task.ability.old.second.FliggyPlayService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.fliggyplaycore.client.biz.wechat.model.ShareWordQueryResponse;
import com.fliggy.fliggyplaycore.client.share.model.ShareActInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@HSFProvider(serviceInterface = TripSCRMFissionService.class)
public class TripSCRMFissionServiceImpl implements TripSCRMFissionService {
    private final FissionTaskServiceImpl fissionTaskService;
    private final FliggyPlayService fliggyPlayService;

    @Override
    @ServiceLog("裂变口令上报")
    public TripSCRMResult<Long> wordReport(TripSCRMFissionRequest fissionRequest) {
        try {
            if (!NumberUtils.validLong(fissionRequest.getActivityId()) || StringUtils.isBlank(fissionRequest.getFissionWord()) || StringUtils.isBlank(fissionRequest.getFliggyPlayId())) {
                return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
            }
            PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(fissionRequest));

            TaskBuildContext taskBuildContext = new TaskBuildContext();
            taskBuildContext.setTargetType(ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode());
            taskBuildContext.setTargetId(fissionRequest.getUserId().toString());
            taskBuildContext.setActivityId(fissionRequest.getActivityId());
            FissionTaskContext fissionTaskContext = new FissionTaskContext();
            fissionTaskContext.setFliggyPlayId(fissionRequest.getFliggyPlayId());
            fissionTaskContext.setFliggyPlayWord(fissionRequest.getFissionWord());
            taskBuildContext.setContext(JSON.toJSONString(fissionTaskContext));
            Long taskId = fissionTaskService.buildTask(taskBuildContext);
            if (!NumberUtils.validLong(taskId)) {
                PlatformLogUtil.logFail("buildTask fail", LogListUtil.newArrayList(fissionRequest, taskBuildContext));
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }
            return TripSCRMResult.success(taskId);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(fissionRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @Override
    @ServiceLog("裂变活动信息查询")
    public TripSCRMResult<ShareActInfo> queryActInfo(TripSCRMFissionRequest fissionRequest) {
        try {
            if (StringUtils.isBlank(fissionRequest.getFissionWord())) {
                return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
            }

            PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(fissionRequest));
            TripSCRMResult<ShareWordQueryResponse> result = fliggyPlayService.queryPlayActInfoByShareWord(fissionRequest.getShareScene(), fissionRequest.getFissionWord(), fissionRequest.getUserId());
            if (Objects.isNull(result) || !result.isSuccess()) {
                PlatformLogUtil.logFail("queryPlayActInfoByShareWord fail", LogListUtil.newArrayList(fissionRequest, result));
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }

            PlatformLogUtil.logFail("queryPlayActInfoByShareWord success", LogListUtil.newArrayList(fissionRequest, result));
            ShareActInfo shareActInfo = new ShareActInfo();
            ShareActInfoDTO shareActInfoDTO = result.getData().getActInfo();
            Integer status = result.getData().getStatus();
            shareActInfo.setActInfo(JSONObject.parseObject(JSONObject.toJSONString(shareActInfoDTO)));
            shareActInfo.setStatus(status);

            // 是否已经助力过
            boolean hasHelp = false;
            // 是否已经助力完成
            boolean hasFinish = false;
            if (!CollectionUtils.isEmpty(shareActInfoDTO.getRelationRecordDTOList())) {
                hasHelp = shareActInfoDTO.getRelationRecordDTOList().stream().allMatch(shareRelationRecordDTO -> Objects.nonNull(shareRelationRecordDTO) && Optional.ofNullable(shareRelationRecordDTO.getIsCurrentUser()).orElse(false));
                hasFinish = Objects.equals(shareActInfoDTO.getActConfig().getSharedCumulativeNum(), shareActInfoDTO.getRelationRecordDTOList().size());
            }

            PlatformLogUtil.logFail("hasHelp", LogListUtil.newArrayList(shareActInfoDTO.getIsCurrentUser(), hasHelp, hasFinish));
            // 是提现活动/是发起者/已经助力过/已经助力完成
            if (hasHelp || hasFinish || shareActInfoDTO.getIsCurrentUser()) {
                return TripSCRMResult.success(shareActInfo);
            }

            // 预判
            TripSCRMResult<Boolean> predictResult = fliggyPlayService.helpPredict(shareActInfoDTO.getActConfig().getPlayId(), fissionRequest.getUserId(), fissionRequest.getFissionWord());
            if (Objects.isNull(predictResult)) {
                PlatformLogUtil.logFail("helpPredict fail", LogListUtil.newArrayList(fissionRequest, shareActInfoDTO.getActConfig().getPlayId()));
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }

            if (!predictResult.isSuccess() || !predictResult.getData()) {
                shareActInfo.setHelpPredictErrorCode(predictResult.getCode());
            }
            return TripSCRMResult.success(shareActInfo);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(fissionRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
    }
}
