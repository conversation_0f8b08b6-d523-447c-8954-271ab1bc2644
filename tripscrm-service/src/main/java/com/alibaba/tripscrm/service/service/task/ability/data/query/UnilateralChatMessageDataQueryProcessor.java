package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.dto.wechat.ScrmWechatCustomerRecallDTO;
import com.alibaba.tripscrm.service.service.task.ability.data.query.common.CommonCustomerDataQueryProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerRecallService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 单向好友私聊
 * <AUTHOR>
 * @date 2024-02-29 17:04:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class UnilateralChatMessageDataQueryProcessor extends AbstractTaskDataProcessor {
    private final CommonCustomerDataQueryProcessor commonCustomerDataQueryProcessor;
    private final WechatUserService wechatUserService;
    private final WechatCustomerRecallService wechatCustomerRecallService;

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.UNILATERAL_CHAT_MESSAGE);
    }

    /**
     * @param context 任务执行上下文
     * @return TaskDataVO
     */
    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        Long taskInstanceId = context.getInstanceId();

        //获取企微用户id
        List<String> wechatUserIdList =
                extInfo.containsKey(TaskConstant.WECHAT_USER_ID_List)
                        ? extInfo.getObject(TaskConstant.WECHAT_USER_ID_List, new TypeReference<List<String>>() {
                }) : new ArrayList<>();
        if (CollectionUtils.isEmpty(wechatUserIdList)){
            wechatUserIdList = wechatUserService.listBySpaceId(taskInfoDO.getSpaceId()).stream()
                    .map(WechatUserDTO::getUserId).collect(Collectors.toList());
        }
        Integer maxReachDays = extInfo.containsKey("maxReachDays") ? extInfo.getInteger("maxReachDays") : 1;
        Integer maxRecallCount = extInfo.containsKey("maxRecallCount") ? extInfo.getInteger("maxRecallCount") : 1;
        List<ScrmWechatCustomerRecallDTO> scrmWechatCustomerRecallDTOList = new ArrayList<>();
        //查询召回好友数据
        for(String wechatUserId : wechatUserIdList){
            List<ScrmWechatCustomerRecallDTO> scrmWechatCustomerRecallDTOS = wechatCustomerRecallService.selectByUserIdAndDeletTime(wechatUserId, maxReachDays, maxRecallCount);
            scrmWechatCustomerRecallDTOList.addAll(scrmWechatCustomerRecallDTOS);
        }
        ArrayList<TaskDataVO.DataBodyVO> result = new ArrayList<>();
        //组装数据
        for (ScrmWechatCustomerRecallDTO scrmWechatCustomerRecallDTO : scrmWechatCustomerRecallDTOList){
            TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
            dataBodyVO.setTargetId(scrmWechatCustomerRecallDTO.getExternalUserId());
            dataBodyVO.setTargetType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
            JSONObject extInfoJson = new JSONObject();
            extInfoJson.put("sendUserId", scrmWechatCustomerRecallDTO.getUserId());
            extInfoJson.put("uuid", UUID.randomUUID().toString());
            dataBodyVO.setExtInfo(JSONObject.toJSONString(extInfoJson));
            result.add(dataBodyVO);
        }
        dataVO.setData(result);
        dataVO.setTotalCount((long) dataVO.getData().size());
        return dataVO;
    }

}
