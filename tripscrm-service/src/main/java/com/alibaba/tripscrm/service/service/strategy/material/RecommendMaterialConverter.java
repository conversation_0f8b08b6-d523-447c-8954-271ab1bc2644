package com.alibaba.tripscrm.service.service.strategy.material;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.*;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RecommendTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.message.RecommendMsgDTO;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import de.danielbechler.util.Collections;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class RecommendMaterialConverter extends AbstractMaterialConverter<RecommendMaterialDTO> {
    private final MaterialLinkService materialLinkService;

    @Override
    public RecommendMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        RecommendMaterialDTO recommendMaterialDTO = JSONObject.parseObject(content, RecommendMaterialDTO.class);
        if (!Objects.equals(RecommendTypeEnum.ACTIVITY_RECOMMEND.getCode(), recommendMaterialDTO.getRecommendType()) || Objects.isNull(recommendMaterialDTO.getActivityContent())) {
            return recommendMaterialDTO;
        }
        //链接转换

        materialTrackRelationDTO.setMsgParagraphId(-1);
        String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
        // 链接生成
        RecommendMaterialDTO.ActivityContent activityContent = recommendMaterialDTO.getActivityContent();

        String pathUrl = buildPathUrl(new MaterialSupplyDTO(activityContent.getSupplyType(), activityContent.getSupplyId()), null, activityContent.getActionUrl(), scrmTrackId, materialTrackRelationDTO.getSceneType());
        if (StringUtils.isBlank(pathUrl)) {
            return recommendMaterialDTO;
        }
        // 链接处理
        String tempUrl = getTempUrl(materialTrackRelationDTO, pathUrl, scrmTrackId);
        activityContent.setActionUrl(tempUrl);
        recommendMaterialDTO.setActivityContent(activityContent);
        return recommendMaterialDTO;
    }

    private String getTempUrl(MaterialTrackRelationDTO materialTrackRelationDTO, String pathUrl, String scrmTrackId) {
        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(pathUrl);
        request.setTargetLinkType(LinkTypeEnum.ALIPAY_LINK);
        request.setConvertShortLink(true);
        request.setTitle("");
        request.setScrmTrackId(scrmTrackId);
        request.setMaterialId(materialTrackRelationDTO.getMaterialId());
        String tempUrl = materialLinkService.convertLink(request);
        return tempUrl;
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        RecommendMaterialDTO recommendMaterialDTO = JSONObject.parseObject(content, RecommendMaterialDTO.class);
        if (Objects.isNull(recommendMaterialDTO) || Objects.isNull(recommendMaterialDTO.getActivityContent())) {
            return new ArrayList<>();
        }
        if (StringUtils.isNotBlank(recommendMaterialDTO.getActivityContent().getSupplyType()) && StringUtils.isNotBlank(recommendMaterialDTO.getActivityContent().getSupplyId())) {
            return Lists.newArrayList(new MaterialSupplyDTO(recommendMaterialDTO.getActivityContent().getSupplyType(), recommendMaterialDTO.getActivityContent().getSupplyId()));
        }
        return new ArrayList<>();
    }

    @Override
    public List<MessageBO> buildMessageBO(RecommendMaterialDTO materialDTO, MaterialContentConvertContext context, String sceneType) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgType(MessageTypeEnum.ALIPAY_DIRECT_MESSAGE_RECOMMEND);
        messageBO.setMsgNum(materialDTO.getIndex());
        RecommendMsgDTO recommendMsgDTO = new RecommendMsgDTO();
        recommendMsgDTO.setRecommendText(materialDTO.getRecommendText());
        recommendMsgDTO.setRecommendType(materialDTO.getRecommendType());
        converterRecommend(materialDTO, recommendMsgDTO);
        messageBO.setExtFields(JSONObject.toJSONString(recommendMsgDTO));
        return Lists.newArrayList(messageBO);
    }

    public void converterRecommend(RecommendMaterialDTO materialDTO, RecommendMsgDTO recommendMsgDTO) {
        switch (RecommendTypeEnum.of(materialDTO.getRecommendType())) {
            case ACTIVITY_RECOMMEND:
                if (Objects.isNull(materialDTO.getActivityContent())) {
                    break;
                }
                recommendMsgDTO.setActivityContent(new RecommendMsgDTO.ActivityContent());
                recommendMsgDTO.getActivityContent().setActionName(materialDTO.getActivityContent().getActionName());
                recommendMsgDTO.getActivityContent().setActionUrl(materialDTO.getActivityContent().getActionUrl());
                recommendMsgDTO.getActivityContent().setDesc(materialDTO.getActivityContent().getDesc());
                recommendMsgDTO.getActivityContent().setTitle(materialDTO.getActivityContent().getTitle());
                recommendMsgDTO.getActivityContent().setImageId(materialDTO.getActivityContent().getImageId());
                break;
            case BENEFIT_RECOMMEND:
                if (Objects.isNull(materialDTO.getBenefitContent())) {
                    break;
                }
                recommendMsgDTO.setBenefitContent(new RecommendMsgDTO.BenefitContent());
                recommendMsgDTO.getBenefitContent().setMultiCoupon(materialDTO.getBenefitContent().getMultiCoupon());
                if (materialDTO.getBenefitContent().getMultiCoupon()) {
                    recommendMsgDTO.getBenefitContent().setActivityIdList(materialDTO.getBenefitContent().getActivityMap().keySet().stream().collect(Collectors.toList()));
                } else {
                    recommendMsgDTO.getBenefitContent().setActivityId(materialDTO.getBenefitContent().getActivityMap().keySet().iterator().next());
                }
                break;
            case RED_PACKET_RECOMMEND:
                if (Collections.isEmpty(materialDTO.getRedPacketIdList())) {
                    break;
                }
                recommendMsgDTO.setRedPacketIdList(materialDTO.getRedPacketIdList());
            default:
                break;
        }
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(RecommendMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        return null;
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.RECOMMEND;
    }

    @Override
    public String buildSubscribeMsgTemplateContent(String content, String paramType) {
        return "";
    }
}
