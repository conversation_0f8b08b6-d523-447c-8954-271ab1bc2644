package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 微信群聊信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatGroupQuery extends BasePageRequest {
    /**
     * 企微成员
     */
    private String userId;

    /**
     * 企微成员列表
     */
    private List<String> userIdList;

    /**
     * 标签
     */
    private List<String> tagIdList;

    /**
     * 群id
     */
    private String chatId;

    /**
     * 成员类型
     */
    private String userType;

    /**
     * 需要过滤的群管理任务id
     */
    private Long groupManageTaskId;
}
