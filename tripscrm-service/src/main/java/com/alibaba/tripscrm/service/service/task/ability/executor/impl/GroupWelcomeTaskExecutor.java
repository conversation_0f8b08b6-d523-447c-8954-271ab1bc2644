package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.domain.task.TaskAsyncExecuteResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskAttributionConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.system.AsyncFunctionTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.request.SendMsgToGroupRequest;
import com.alibaba.tripscrm.service.model.dto.AsyncExecuteContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.AsyncFunctionUtils;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupWelcomeTaskExecutor extends AbstractTaskExecutor {
    private final EnterpriseWechatManager enterpriseWechatManager;
    private final WechatGroupService wechatGroupService;
    private final GroupRelationRepository groupRelationRepository;
    private final LdbTairManager ldbTairManager;
    private final IsvRouteService isvRouteService;
    private final TaskExecuteRecordService taskExecuteRecordService;
    private final MetaqProducer metaqProducer;

    @PostConstruct
    public void init() {
        AsyncFunctionUtils.register(this::retryWelcome, AsyncFunctionTypeEnum.GROUP_WELCOME);
    }

    private TripSCRMResult<String> retryWelcome(JSONObject jsonObject) {
        TaskExecuteContext context = jsonObject.getObject("taskExecuteContext", TaskExecuteContext.class);
        TodoTaskVO todoTaskVO = jsonObject.getObject("todoTaskVO", TodoTaskVO.class);
        SendMsgToGroupRequest request = jsonObject.getObject("request", SendMsgToGroupRequest.class);
        String userId = getSendUserId(context, todoTaskVO);
        request.setUserId(userId);
        return sendAsyncRequest(context, todoTaskVO, () -> enterpriseWechatManager.asyncSendMessageToGroup(request));
    }

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String chatId = getFinalTargetId(context, taskDataBody);

        Map<String, Object> extInfo = context.getExtInfo();
        if (!extInfo.containsKey("externalUserId")) {
            PlatformLogUtil.logFail("chatId is empty", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (checkDuplicate(context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            return;
        }

        String externalUserId = (String) extInfo.get("externalUserId");

        // 查询活动上下文
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        if (Objects.isNull(activityContext)) {
            PlatformLogUtil.logFail("not found activityContext", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));

            todoTaskVO.setSuccess(true);
            todoTaskVO.setAttribution(TaskAttributionConstant.NOT_FOUND_ACTIVITY_CONTEXT);
            return;
        }

        if (checkInSendInterval(context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            todoTaskVO.setAttribution(TaskAttributionConstant.IN_SEND_INTERVAL);
            return;
        }

        activityContext.getExtraJson().putIfAbsent("chatIdList", new ArrayList<>());
        List<String> chatIdList = activityContext.getExtraJson().getObject("chatIdList", new TypeReference<List<String>>() {
        });
        if (chatIdList.size() == 1) {
            chatIdList = Arrays.stream(chatIdList.get(0).split(",")).collect(Collectors.toList());
        }

        if (!chatIdList.contains(chatId)) {
            PlatformLogUtil.logFail("chatId not in activityContext", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            todoTaskVO.setSuccess(true);
            return;
        }

        // 素材
        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("material get fail", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MATERIAL_IN_TASK);
        }

        String userId = getSendUserId(context, todoTaskVO);
        // 群成员信息
        List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(chatId), Lists.newArrayList(externalUserId), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isEmpty(groupRelationList)) {
            // 客户不在群内，不处理
            PlatformLogUtil.logFail("member is not in group", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            todoTaskVO.setSuccess(true);
            return;
        }

        SendMsgToGroupRequest sendMsgToGroupRequest = buildSendMsgToGroupRequest(context, todoTaskVO, userId, extInfo, taskDataBody, groupRelationList, materailInfoDO, chatId);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> enterpriseWechatManager.asyncSendMessageToGroup(sendMsgToGroupRequest));
        if (!result.isSuccess()) {
            JSONObject retyrRequest = new JSONObject();
            retyrRequest.put("request", sendMsgToGroupRequest);
            retyrRequest.put("todoTaskVO", todoTaskVO);
            retyrRequest.put("taskExecuteContext", context);
            AsyncExecuteContext retyrAsyncExecuteContext = new AsyncExecuteContext();
            retyrAsyncExecuteContext.setRetryCount(1);
            retyrAsyncExecuteContext.setFunctionType(AsyncFunctionTypeEnum.GROUP_WELCOME);
            retyrAsyncExecuteContext.setRequest(retyrRequest);
            retyrAsyncExecuteContext.setRetry(true);
            retyrAsyncExecuteContext.setDelayTimeLevel(MetaQDeleyLevel.LEVEL_4.getLevel());
            AsyncFunctionUtils.submitWithRetry(retyrAsyncExecuteContext);
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        JSONObject data = new JSONObject();
        data.put("chatId", chatId);
        data.put("wechatUserId", userId);
        data.put("extraInfo", context.getExtInfo());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
    }

    private SendMsgToGroupRequest buildSendMsgToGroupRequest(TaskExecuteContext context, TodoTaskVO todoTaskVO, String userId, Map<String, Object> extInfo, TaskDataVO.DataBodyVO taskDataBody, List<GroupRelationDO> groupRelationList, MaterailInfoDO materailInfoDO, String chatId) {
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setWechatUserId(userId);
        materialContentConvertContext.setExtraInfo(extInfo);
        if (StringUtils.isNotBlank(taskDataBody.getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(taskDataBody.getExtInfo()));
        }
        GroupRelationDO groupRelationDO = groupRelationList.get(0);
        materialContentConvertContext.setExternalUserId(groupRelationDO.getUserId());
        materialContentConvertContext.setGroupNickName(groupRelationDO.getNickname());
        materialContentConvertContext.setCustomerName(groupRelationDO.getName());
        materialContentConvertContext.setOriginContent(materailInfoDO.getContent());
        materialContentConvertContext.setChatId(chatId);

        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.QUNMA_HYY);
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setWxUserId(userId);
        materialTrackRelationDTO.setSendChatId(chatId);

        SendMsgToGroupRequest sendMsgToGroupRequest = new SendMsgToGroupRequest();
        sendMsgToGroupRequest.setChatId(taskDataBody.getTargetId());
        sendMsgToGroupRequest.setUserId(userId);
        sendMsgToGroupRequest.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        sendMsgToGroupRequest.setMaterialContentConvertContext(materialContentConvertContext);
        sendMsgToGroupRequest.setMaterailInfoDO(materailInfoDO);
        sendMsgToGroupRequest.setTaskType(getTaskType());
        return sendMsgToGroupRequest;
    }

    private boolean checkInSendInterval(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        String chatId = getFinalTargetId(context, taskDataBody);
        // 发送间隔时间控制，这里不做强卡控，不考虑并发修改问题
        JSONObject taskExtraInfo = JSONObject.parseObject(Optional.ofNullable(context.getTaskInfoDOSnapshot().getExtInfo()).orElse("{}"));
        if (!taskExtraInfo.containsKey("intervalSeconds")) {
            return false;
        }

        int intervalSeconds = taskExtraInfo.getIntValue("intervalSeconds");
        Object data = ldbTairManager.get(TairConstant.GROUP_WELCOME_LAST_SEND_TIME_PREFIX + chatId);
        long lastSendSeconds = Objects.nonNull(data) ? NumberUtils.toLong(data.toString(), -1L) : -1L;
        // 获取当前秒数
        long currentSeconds = System.currentTimeMillis() / 1000;
        if (lastSendSeconds + intervalSeconds > currentSeconds) {
            PlatformLogUtil.logFail("intervalSeconds is not over", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            return true;
        }

        // 过期时间为间隔时间
        ldbTairManager.put(TairConstant.GROUP_WELCOME_LAST_SEND_TIME_PREFIX + chatId, currentSeconds, intervalSeconds);
        return false;
    }

    private boolean checkDuplicate(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        Long taskId = todoTaskVO.getTaskId();
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        Map<String, Object> extInfo = context.getExtInfo();
        if (!extInfo.containsKey("externalUserId")) {
            PlatformLogUtil.logFail("externalUserId is empty", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        String externalUserId = (String) extInfo.get("externalUserId");
        if (ldbTairManager.incr(TairConstant.TASK_EXECUTE_DUPLICATE_CHECK_PREFIX + taskId + "_" + getFinalTargetId(context, taskDataBody) + "_" + externalUserId, 1, 0, 10) > 1) {
            PlatformLogUtil.logFail("duplicate execute", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            return true;
        }

        return false;
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            PlatformLogUtil.logInfo("群欢迎语发送人由上下文带入", taskDataBody);
            return (String) taskDataBody.getContext().get("sendUserId");
        }

        // 查询群聊信息
        WechatGroupVO wechatGroupVO = wechatGroupService.getWechatGroupVOByChatId(getFinalTargetId(context, taskDataBody));
        if (Objects.isNull(wechatGroupVO)) {
            PlatformLogUtil.logFail("群欢迎语获取群信息失败", LogListUtil.newArrayList(context, taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.GROUP_INFO_NOT_EXISTS);
        }
        // 根据群账号选取策略获取发送人
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setChatId(wechatGroupVO.getChatId());
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        isvRouteContext.setRiskActionEnum(RiskActionEnum.GROUP_SEND_MESSAGE);
        TripSCRMResult<WechatUserDTO> isvRouteResult = isvRouteService.matchWechatUser(isvRouteContext);
        if (!isvRouteResult.isSuccess() || Objects.isNull(isvRouteResult.getData()) || StringUtils.isBlank(isvRouteResult.getData().getUserId())) {
            PlatformLogUtil.logFail("群欢迎语获取发送人失败，根据群账号选取策略获取发送人失败", LogListUtil.newArrayList(context, taskDataBody, isvRouteContext, isvRouteResult));
            throw new TripscrmException(TripSCRMErrorCode.MATCH_WECHAT_USER_BY_ISV_ROUTE_STRATEGY_FAIL);
        }
        String userId = isvRouteResult.getData().getUserId();
        taskDataBody.getContext().put("sendUserId", userId);
        return userId;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_CHAT_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (taskDataBody.getContext().containsKey("chatId")) {
            return (String) taskDataBody.getContext().get("chatId");
        }

        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("empty targetId", LogListUtil.newArrayList(context.getTaskId(), taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!Lists.newArrayList(ActivityTargetTypeEnum.WX_CHAT_ID).contains(activityTargetType)) {
            PlatformLogUtil.logFail("only accept chatId", LogListUtil.newArrayList(context.getTaskId(), taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        String chatId = taskDataBody.getTargetId();
        taskDataBody.getContext().put("chatId", chatId);
        return chatId;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.GROUP_WELCOME;
    }

    @Override
    public void updateAsyncExecuteResult(Long recordId, ScrmCallbackMsg scrmCallbackMsg) {
        sendTaskAsyncExecuteResult(scrmCallbackMsg);
        TaskExecuteRecordBO taskExecuteRecordBO = taskExecuteRecordService.queryByRecordId(recordId);
        if (Objects.isNull(taskExecuteRecordBO)) {
            PlatformLogUtil.logFail("任务执行明细，更新入群欢迎语任务异步执行结果，查询任务执行明细为空", LogListUtil.newArrayList(recordId));
            return;
        }

        boolean firstCallback = Lists.newArrayList(TaskSingleDataExecuteResultEnum.SUCCESS).contains(taskExecuteRecordBO.getStatusEnum());

        // 第一个回调，作为异步执行结果
        if (firstCallback) {
            taskExecuteRecordBO.setStatusEnum(scrmCallbackMsg.getResult() ? TaskSingleDataExecuteResultEnum.ASYNC_SUCCESS : TaskSingleDataExecuteResultEnum.ASYNC_FAIL);
            PlatformLogUtil.logInfo("任务执行明细，更新群欢迎语任务异步执行结果，首次更新异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        }

        JSONObject extraInfo = taskExecuteRecordBO.getExtraInfo();
        List<String> asyncExecuteResultList = extraInfo.containsKey("asyncExecuteResult") ? extraInfo.getObject("asyncExecuteResult", new TypeReference<List<String>>() {
        }) : new ArrayList<>();
        if (StringUtils.isNotBlank(scrmCallbackMsg.getContent())) {
            asyncExecuteResultList.add(scrmCallbackMsg.getContent());
            extraInfo.put("asyncExecuteResultList", asyncExecuteResultList);
        }
        if (StringUtils.isNotBlank(scrmCallbackMsg.getMessage())) {
            extraInfo.put("asyncExecuteResultMessage", scrmCallbackMsg.getMessage());
        }
        taskExecuteRecordBO.setExtraInfo(extraInfo);

        PlatformLogUtil.logInfo("任务执行明细，更新群欢迎语任务异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        if (firstCallback && NumberUtils.validLong(taskExecuteRecordBO.getSubTaskInstanceId())) {
            String key = scrmCallbackMsg.getResult() ? (TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_SUCCESS_COUNT_PREFIX + taskExecuteRecordBO.getSubTaskInstanceId()) : (TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_FAIL_COUNT_PREFIX + taskExecuteRecordBO.getSubTaskInstanceId());
            ldbTairManager.incr(key, 1, 0, 86400);
        }
        taskExecuteRecordService.upsert(taskExecuteRecordBO);
    }

    private void sendTaskAsyncExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        // 发送异步执行结果
        TaskAsyncExecuteResult taskAsyncExecuteResult = new TaskAsyncExecuteResult();
        taskAsyncExecuteResult.setRequestId(scrmCallbackMsg.getRequestId());
        taskAsyncExecuteResult.setResult(scrmCallbackMsg.getResult());
        taskAsyncExecuteResult.setMessage(scrmCallbackMsg.getMessage());
        JSONObject extraInfo = new JSONObject();
        JSONObject jsonContent = JSONObject.parseObject(scrmCallbackMsg.getContent());
        if (Objects.nonNull(jsonContent)) {
            extraInfo.put("msgNum", MapUtils.getInteger(jsonContent, "msgNum", null));
        }
        taskAsyncExecuteResult.setExtraInfo(extraInfo);
        metaqProducer.send(MQEnum.TASK_ASYNC_EXECUTE_RESULT, null, scrmCallbackMsg.getPlatformCorpId(), JSONObject.toJSONString(taskAsyncExecuteResult));
    }


}
