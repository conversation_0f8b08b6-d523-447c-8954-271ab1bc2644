package com.alibaba.tripscrm.service.synchronizer;

import com.ali.com.google.common.collect.Lists;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.domain.query.TagGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.TagInfoQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.dto.tag.WechatTagSynchronizerDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.api.service.TagService;
import com.alibaba.tripzoo.proxy.request.GetCorpTagListRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.TagInfoResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 企微标签同步器
 *
 * <AUTHOR>
 * @since 2025/01/02 10:46
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatTagSynchronizer extends BaseLockedSynchronizer<WechatTagSynchronizerDTO> {
    private final TagService tagService;
    private final TagInfoService tagInfoService;
    private final TagGroupService tagGroupService;

    @Override
    public void process(BaseSynchronizerContext<WechatTagSynchronizerDTO> context) {
        try {
            // 参数校验
            if (!paramVerify(context)) {
                PlatformLogUtil.logFail("参数校验失败", LogListUtil.newArrayList(context));
                return;
            }
            // 从企业微信获取标签列表
            GetCorpTagListRequest request = new GetCorpTagListRequest();
            request.setTagIdList(Lists.newArrayList(context.getTagId()));
            request.setGroupIdList(Lists.newArrayList(context.getTagGroupId()));
            request.setCorpId(context.getCorpId());
            ResultDO<List<TagInfoResponse>> resultDO = tagService.getCorpTagList(request);
            if (!resultDO.getSuccess()) {
                PlatformLogUtil.logFail("获取企业的标签列表失败", LogListUtil.newArrayList(request, resultDO));
                return;
            }
            List<TagInfoResponse> tagInfoResponseList = resultDO.getModel();
            if (CollectionUtils.isEmpty(tagInfoResponseList)) {
                PlatformLogUtil.logFail("从企业微信获取的企业标签列表为空", LogListUtil.newArrayList(request, resultDO));
                return;
            }
            TagInfoResponse tagInfoResponse = tagInfoResponseList.get(0);
            if (!StringUtils.hasLength(tagInfoResponse.getGroupId()) || Objects.isNull(tagInfoResponse.getDeleted())) {
                PlatformLogUtil.logFail("从企业微信获取的企业标签列表中的标签组id为空或标签组的删除状态为空", LogListUtil.newArrayList(request, resultDO));
                return;
            }
            PlatformLogUtil.logInfo("从企业微信查询企微标签信息", LogListUtil.newArrayList(request, resultDO));
            compareAndSave(tagInfoResponse);

        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.SYNCHRONIZER_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.SYNCHRONIZER_ERROR);
        }
    }

    /**
     * 对比并更新
     *
     * @param tagInfoResponse 从企微端查询标签信息的结果
     */
    private void compareAndSave(TagInfoResponse tagInfoResponse) {
        TagGroupDTO tagGroupDTO = tagGroupService.selectBySpaceIdAndTagTypeAndSourceWithCache(SwitchConfig.SCRM_BASE_SPACE_ID, TagTypeEnum.ENTERPRISE_WECHAT_TAG, tagInfoResponse.getGroupId());
        // 同步标签组
        // 企微侧标签组已被删除，db中的标签组未被删除，需要删除db中的标签组
        if (tagInfoResponse.getDeleted() && NumberUtils.isEqual(tagGroupDTO.getDeleted(), IsDeleteEnum.NO.getCode())) {
            PlatformLogUtil.logInfo("企微侧标签组已被删除，db中的标签组未被删除，需要删除db中的标签组", LogListUtil.newArrayList(tagInfoResponse, tagGroupDTO));

            Integer effectLines = tagGroupService.deleteById(tagGroupDTO.getId(), false);
            if (!NumberUtils.validInteger(effectLines) || effectLines <= 0) {
                PlatformLogUtil.logFail("删除标签组失败", LogListUtil.newArrayList(tagInfoResponse, tagGroupDTO, effectLines));
            }
            return;
        }
        // 如果DB中查不到标签组或已被删除，就创建新的标签组
        if (Objects.isNull(tagGroupDTO) || NumberUtils.isEqual(tagGroupDTO.getDeleted(), IsDeleteEnum.YES.getCode())) {
            PlatformLogUtil.logInfo("查询标签组不存在或已被删除,需要创建该标签组", LogListUtil.newArrayList(tagInfoResponse, tagGroupDTO));
            TagGroupDTO newTagGroupDTO = new TagGroupDTO();
            newTagGroupDTO.setName(tagInfoResponse.getGroupName());
            newTagGroupDTO.setSpaceId(SwitchConfig.SCRM_BASE_SPACE_ID);
            newTagGroupDTO.setTagType(TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode());
            newTagGroupDTO.setSource(tagInfoResponse.getGroupId());
            newTagGroupDTO.setSort(tagInfoResponse.getOrder());
            Integer effectLines = tagGroupService.insertSelective(newTagGroupDTO);
            if (effectLines <= 0) {
                PlatformLogUtil.logFail("创建标签组失败", LogListUtil.newArrayList(tagInfoResponse, tagGroupDTO));
                return;
            }
            tagGroupDTO = tagGroupService.selectBySpaceIdAndTagTypeAndSourceWithCache(SwitchConfig.SCRM_BASE_SPACE_ID, TagTypeEnum.ENTERPRISE_WECHAT_TAG, tagInfoResponse.getGroupId());
            if (Objects.isNull(tagGroupDTO) || tagGroupDTO.getDeleted() == 1 || !NumberUtils.validLong(tagGroupDTO.getId())) {
                PlatformLogUtil.logFail("创建标签组失败", LogListUtil.newArrayList(tagInfoResponse, newTagGroupDTO));
                return;
            }
        }
        // 如果DB中查到标签组并且标签组名与企微端不一致，就更新标签组名
        if (NumberUtils.isEqual(tagGroupDTO.getDeleted(), IsDeleteEnum.NO.getCode()) && !StringUtils.pathEquals(tagGroupDTO.getName(), tagInfoResponse.getGroupName())) {
            PlatformLogUtil.logInfo("查询标签组已存在,需要更新该标签组", LogListUtil.newArrayList(tagInfoResponse, tagGroupDTO));
            TagGroupDTO updateTagGroupDTO = new TagGroupDTO();
            updateTagGroupDTO.setName(tagInfoResponse.getGroupName());
            updateTagGroupDTO.setSort(tagInfoResponse.getOrder());
            TagGroupQuery query = new TagGroupQuery();
            query.setId(tagGroupDTO.getId());
            Integer effectLines = tagGroupService.
                    updateSelective(updateTagGroupDTO, query, false);
            if (effectLines <= 0) {
                PlatformLogUtil.logFail("更新标签组失败", LogListUtil.newArrayList(tagInfoResponse, tagGroupDTO));
            }
        }
        // 同步标签
        for (TagInfoResponse.Tag tag : tagInfoResponse.getTag()) {
            TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(tagGroupDTO.getId(), tag.getId());
            if ((Objects.nonNull(tagInfoDTO) && NumberUtils.isEqual(tagInfoDTO.getDeleted(), IsDeleteEnum.NO.getCode())) && tag.getDeleted()) {
                PlatformLogUtil.logInfo("查询DB标签不存在或DB标签未删除但企微测该标签已删除,需要从DB中删除该标签", LogListUtil.newArrayList(tagInfoResponse, tagInfoDTO, tag, tagGroupDTO));
                Integer effectLines = tagInfoService.deleteByIdList(Lists.newArrayList(tagInfoDTO.getId()), false);
                if (effectLines <= 0) {
                    PlatformLogUtil.logFail("删除标签失败", LogListUtil.newArrayList(tagInfoResponse, tagInfoDTO));
                }
                continue;
            }
            // 如果tagInfoDTO不存在或已被删除并且tag未被删除，就创建
            if ((Objects.isNull(tagInfoDTO) || tagInfoDTO.getDeleted() == 1) && !tag.getDeleted()) {
                PlatformLogUtil.logInfo("查询DB标签不存在或已被删除,需要创建该标签", LogListUtil.newArrayList(tagInfoResponse, tagInfoDTO, tag, tagGroupDTO));
                TagInfoDTO newTagInfoDTO = new TagInfoDTO();
                newTagInfoDTO.setName(tag.getName());
                newTagInfoDTO.setSpaceId(SwitchConfig.SCRM_BASE_SPACE_ID);
                newTagInfoDTO.setTagType(TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode());
                newTagInfoDTO.setSpaceId(SwitchConfig.SCRM_BASE_SPACE_ID);
                newTagInfoDTO.setSource(tag.getId());
                newTagInfoDTO.setGroupId(tagGroupDTO.getId());
                Integer effectLines = tagInfoService.insertSelective(newTagInfoDTO, false);
                if (effectLines <= 0) {
                    PlatformLogUtil.logFail("创建标签失败", LogListUtil.newArrayList(tagInfoResponse, newTagInfoDTO));
                }
                continue;
            }
            // 如果tagInfoDTO存在，就更新
            if ((Objects.nonNull(tagInfoDTO) && NumberUtils.isEqual(tagInfoDTO.getDeleted(), IsDeleteEnum.NO.getCode())) && !tag.getDeleted()) {
                PlatformLogUtil.logInfo("查询DB标签已存在,需要更新该标签", LogListUtil.newArrayList(tagInfoResponse, tagInfoDTO, tag, tagGroupDTO));
                TagInfoQuery condition = new TagInfoQuery();
                condition.setIdList(Lists.newArrayList(tagInfoDTO.getId()));
                tagInfoDTO.setName(tag.getName());
                tagInfoDTO.setGroupId(tagGroupDTO.getId());
                tagInfoDTO.setTagType(TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode());
                tagInfoDTO.setSpaceId(SwitchConfig.SCRM_BASE_SPACE_ID);
                tagInfoDTO.setSource(tag.getId());
                tagInfoDTO.setDeleted(tag.getDeleted() ? (byte) 1 : 0);
                Integer effectLines = tagInfoService.updateSelective(tagInfoDTO, condition, false);
                if (effectLines <= 0) {
                    PlatformLogUtil.logFail("创建标签失败", LogListUtil.newArrayList(tagInfoResponse, tagInfoDTO, condition));
                }
            }
        }
    }

    @Override
    public String getLockKey(BaseSynchronizerContext<WechatTagSynchronizerDTO> context) {
        return TairConstant.WECHAT_TAG_SYNCHRONIZER_LOCK_PREFIX + context.getTagId() + "_" + context.getData().getTagType().getDesc();
    }

    private Boolean paramVerify(BaseSynchronizerContext<WechatTagSynchronizerDTO> context) {
        if (Objects.isNull(context) || Objects.isNull(context.getData())) {
            PlatformLogUtil.logFail("企微标签同步器参数错误，上下文参数为空", LogListUtil.newArrayList(context));
            return false;
        }
        WechatTagSynchronizerDTO data = context.getData();
        if (Objects.isNull(data.getTagType())) {
            PlatformLogUtil.logFail("企微标签同步器参数错误，标签类型为空", LogListUtil.newArrayList(data));
            return false;
        }
        if (!StringUtils.hasLength(context.getCorpId())) {
            PlatformLogUtil.logFail("企微标签同步器参数错误，corpId为空", LogListUtil.newArrayList(data));
            return false;
        }
        return true;
    }

}
