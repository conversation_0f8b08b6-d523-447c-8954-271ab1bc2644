package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapReduceJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.domain.MemberInfoDTO;
import com.alibaba.tripscrm.domain.request.TripSCRMGroupSyncRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMGroupTemplateSyncRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMSyncGroupService;
import com.alibaba.tripscrm.service.convert.AlipayGroupConverter;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupSynchronizerDTO;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatContactMeActivitySopConfigVO;
import com.alibaba.tripscrm.service.synchronizer.GroupChatSynchronizer;
import com.alibaba.tripscrm.service.synchronizer.GroupTemplateSynchronizer;
import com.alibaba.tripscrm.service.synchronizer.SyncResult;
import com.alibaba.tripzoo.proxy.api.service.alipay.group.AlipayGroupService;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupDetailDTO;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupInstanceInfoDTO;
import com.alibaba.tripzoo.proxy.request.alipay.AlipayGroupInfoPageRequest;
import com.alibaba.tripzoo.proxy.request.alipay.GroupInstanceInfoPageRequest;
import com.alibaba.tripzoo.proxy.request.alipay.GroupInstanceInfoQueryRequest;
import com.alibaba.tripzoo.proxy.result.PageInfoDTO;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/18 13:54
 * @Filename：AlipayGroupSyncProcessor
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlipayGroupSyncProcessor extends MapReduceJobProcessor {
    private final TripSCRMSyncGroupService syncGroupService;
    private final AlipayGroupService alipayGroupService;
    private final AlipayGroupConverter alipayGroupConverter;
    private final GroupChatSynchronizer groupChatSynchronizer;
    private final GroupTemplateSynchronizer groupTemplateSynchronizer;
    private static final String SINGLE_GROUP_SYNC_TASK = "SINGLE_GROUP_SYNC_TASK";
    private static final String SINGLE_GROUP_CHAT_SYNC_TASK = "SINGLE_GROUP_CHAT_SYNC_TASK";


    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // map
        if (isRootTask(context)) {

            List<GroupDetailDTO> groupDetailDTOList = pageGroupDetailDTOAll();

            if(CollectionUtils.isEmpty(groupDetailDTOList)) {
                return new ProcessResult(true);
            }
            Boolean istest = true;
            if(true){
                List<GroupDetailDTO> testList = groupDetailDTOList.subList(0, 2);
                return map(testList, SINGLE_GROUP_SYNC_TASK);
            }
            return map(groupDetailDTOList, SINGLE_GROUP_SYNC_TASK);
        }
        //群组同步
        if (SINGLE_GROUP_SYNC_TASK.equals(context.getTaskName())) {
            executeSyncGroupTask(context);
        }
        //群组同步
        if (SINGLE_GROUP_CHAT_SYNC_TASK.equals(context.getTaskName())) {
            syncChatInfo(context);
        }
        return new ProcessResult(true);
    }

    private List<GroupDetailDTO> pageGroupDetailDTOAll() {

        List<GroupDetailDTO> groupDetailDTOList = new ArrayList<>();

        AlipayGroupInfoPageRequest request = new AlipayGroupInfoPageRequest();
        request.setPageNum(1L);
        request.setPageSize(50L);
        ResultDO<PageInfoDTO<GroupDetailDTO>> pageResultDO ;
        // 分页查询所有群组
        do {
            pageResultDO = alipayGroupService.pageQuery(request);
            if (!pageResultDO.getSuccess() || Objects.isNull(pageResultDO.getModel()) || pageResultDO.getModel().getList().isEmpty()) {
                PlatformLogUtil.logFail("查询支付宝群组信息失败", LogListUtil.newArrayList(request, pageResultDO));
                return groupDetailDTOList;
            }
            groupDetailDTOList.addAll(pageResultDO.getModel().getList());
            request.setPageNum(request.getPageNum() + 1);

        } while (Objects.nonNull(pageResultDO) && pageResultDO.getModel().getTotal() > groupDetailDTOList.size());
        return groupDetailDTOList;
    }


    //群组同步任务
    private void executeSyncGroupTask(JobContext context) {
        GroupDetailDTO groupDetailDTO = (GroupDetailDTO) context.getTask();
        if (Objects.isNull(groupDetailDTO)) {
            PlatformLogUtil.logFail("群组信息为空", LogListUtil.newArrayList(context.getTask()));
            return;
        }
        try {
            TripSCRMGroupTemplateSyncRequest request = alipayGroupConverter.convert2GroupTemplateBuildRequest(groupDetailDTO);
            TripSCRMResult<Boolean> result = syncGroupService.syncGroupTemplateInfo(request);
            if (!result.isSuccess() || !result.getData()) {
                PlatformLogUtil.logFail("同步群组信息失败", LogListUtil.newArrayList(groupDetailDTO, result));
                return;
            }
            // 分页查询所有群聊
            List<String> chatIdList = getChatIdList(groupDetailDTO);
            if(CollectionUtils.isEmpty(chatIdList)) {
                return;
            }
            Boolean isTest = true;
            if (isTest) {
                map(chatIdList.subList(0,1), SINGLE_GROUP_CHAT_SYNC_TASK);
            }

            map(chatIdList, SINGLE_GROUP_CHAT_SYNC_TASK);

        } catch (Exception e) {
            PlatformLogUtil.logException("同步群组信息失败", e.getMessage(), e, LogListUtil.newArrayList(groupDetailDTO, e));
        }
    }

    private  List<String> getChatIdList(GroupDetailDTO groupDetailDTO) {
        List<String> chatIdList = new ArrayList<>();

        GroupInstanceInfoPageRequest instanceInfoPageRequest = new GroupInstanceInfoPageRequest();
        instanceInfoPageRequest.setGroupId(groupDetailDTO.getGroupBaseInfoDTO().getGroupId());
        instanceInfoPageRequest.setPageNum(1L);
        instanceInfoPageRequest.setPageSize(50L);
        ResultDO<PageInfoDTO<GroupInstanceInfoDTO>> pageInfoDTOResultDO;
        do {
            pageInfoDTOResultDO = alipayGroupService.pageGroupInstanceInfo(instanceInfoPageRequest);
            if (!pageInfoDTOResultDO.getSuccess() || Objects.isNull(pageInfoDTOResultDO.getModel()) || pageInfoDTOResultDO.getModel().getList().isEmpty()) {
                PlatformLogUtil.logFail("查询支付宝群组信息失败", LogListUtil.newArrayList(instanceInfoPageRequest, pageInfoDTOResultDO));
                return chatIdList;
            }
            List<GroupInstanceInfoDTO> groupInstanceInfoDTOS = pageInfoDTOResultDO.getModel().getList();
            List<String> chatId = groupInstanceInfoDTOS.stream().map(GroupInstanceInfoDTO::getGroupInstanceId).collect(Collectors.toList());

            chatIdList.addAll(chatId);
            instanceInfoPageRequest.setPageNum(instanceInfoPageRequest.getPageNum() + 1);

        } while (Objects.nonNull(pageInfoDTOResultDO) && pageInfoDTOResultDO.getModel().getTotal() > chatIdList.size());


        return chatIdList;
    }

    /**
     * 群聊同步任务
     *
     * @param context
     */
    private void syncChatInfo(JobContext context) {
        String chatId = (String)context.getTask();

        try {
            BaseSynchronizerContext<WechatGroupSynchronizerDTO> syncContext = new BaseSynchronizerContext<>();
            WechatGroupSynchronizerDTO data = new WechatGroupSynchronizerDTO();
            data.setPlatformType(PlatformTypeEnum.ALI_PAY);
            data.setChatId(chatId);
            data.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);
            syncContext.setData(data);
            SyncResult<WechatGroupSynchronizerDTO> sync = groupChatSynchronizer.sync(syncContext);
        } catch (Exception e) {
            PlatformLogUtil.logException("同步群聊信息异常", e.getMessage(), e, LogListUtil.newArrayList(chatId, e));
        }

    }


    @Override
    public ProcessResult reduce(JobContext jobContext) throws Exception {
        return new ProcessResult(true);
    }


}
