package com.alibaba.tripscrm.service.service.risk;

import com.alibaba.tripscrm.dal.model.domain.base.PageInfo;
import com.alibaba.tripscrm.service.model.domain.request.*;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionLimitProcess;
import com.alibaba.tripscrm.service.model.vo.risk.*;

import java.util.List;

/**
 * 风控面板 Manager
 *
 * <AUTHOR>
 * @date 2024/07/01
 */
public interface RiskPanelService {
    /**
     * 机器人大盘数据
     * @return return
     */
    AccountRiskDataVO getData();
    /**
     * 创建风控调用记录
     *
     * @param create 对象
     */
    void createRecord(RiskInvokeRecordCreateParam create);

    /**
     * 【风控配置】分页获取账号的风控配置
     *
     * @param searchKey searchKey
     * @return return
     */
    AccountRiskConfigInfoVO pageRiskConfig(String searchKey, Integer robotType, Integer pageSize, Integer pageNum);

    /**
     * 【风控配置】查询单个账号风控配置详情
     * @param riskId riskId
     * @return return
     */
    AccountRiskConfigDetailVO getRiskConfig(String riskId);

    /**
     * 【风控配置】设置/修改风控配置
     * @param param param
     */
    boolean setRiskConfig(AccountRiskConfigUpdateRequest param);

    /**
     * 【风控配置】重制风控配置至默认
     * @param param param
     */
    void resetConfig(AccountRiskResetConfigRequest param);

    /**
     * 【风控配置】账号挂起
     * @param param param
     */
    void holdAccount(AccountRiskHoldRequest param);

    /**
     * 【风控配置】账号脱离
     * @param param param
     */
    void escapeAccount(AccountRiskEscapeRequest param);

    /**
     * 【风控配置】账号可用
     * @param param param
     */
    void normalAccount(AccountRiskNormalRequest param);

    /**
     * 【账号情况】分页获取账号情况数据（状态及限流次数）
     *
     * @param searchKey searchKey
     * @return return
     */
    PageInfo<AccountRiskSituationVO> pageAccountRiskSituation(String searchKey, Integer robotType, Integer pageSize, Integer pageNum);

    /**
     * 【账号情况】分页获取调用记录
     *
     * @param corpId corpId
     * @param userId userId
     * @param actionCode actionCode
     * @param invokeStatus invokeStatus
     * @return return
     */
    PageInfo<RiskInvokeRecordVO> pageRecord(String corpId, String userId, String actionCode, Integer invokeStatus, Integer pageSize, Integer pageNum);

    /**
     * 【账号情况】查询账号行动项限流进度
     *
     * @param corpId corpId
     * @param userId userId
     * @return return
     */
    List<RiskActionLimitProcess> listActionLimitProcess(String corpId, String userId);

    /**
     * 【账号情况】重置统计调用次数
     * @param param param
     */
    void resetInvokeCount(AccountRiskResetInvokeCountRequest param);
}