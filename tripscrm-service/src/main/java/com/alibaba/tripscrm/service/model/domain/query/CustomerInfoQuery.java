package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/12 10:27
 * 客户信息查询入参
 */
@Data
public class CustomerInfoQuery implements Serializable {
    private static final long serialVersionUID = -7612332309840220798L;
    /**
     * 企微成员 userId
     */
    private String userId;

    /**
     * 分组求count字段
     */
    private List<String> countFields;

    /**
     * 好友关系状态
     */
    private Integer status;

    /**
     * 组织id
     */
    private String corpId;
}
