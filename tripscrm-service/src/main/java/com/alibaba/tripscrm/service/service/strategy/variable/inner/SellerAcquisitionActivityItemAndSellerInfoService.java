package com.alibaba.tripscrm.service.service.strategy.variable.inner;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.material.InnerServiceEnum;
import com.alibaba.tripscrm.service.manager.second.MmpManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.material.ItemVO;
import com.alibaba.tripscrm.service.model.dto.ItemInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/3/24 11:08
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerAcquisitionActivityItemAndSellerInfoService extends AbstractVariableDataInnerService {
    private final MmpManager mmpManager;
    private final MaterialService materialService;

    @Override
    public String doGetValue(Map<String, Object> paramMap) {
        int itemType = MapUtils.getIntValue(paramMap, "itemType", 0);
        String itemId = MapUtils.getString(paramMap, "itemId");
        String itemName = MapUtils.getString(paramMap, "itemName");
        String orderId = MapUtils.getString(paramMap, "orderId");

        // 订详进来的
        if (StringUtils.hasText(orderId) && !Objects.equals(orderId, "0")) {
            return String.format("关于您咨询的「订单号为%s」的商品，我将为您匹配商家，请稍等", orderId);
        }

        if (StringUtils.hasText(itemId) && !Lists.newArrayList(ItemTypeEnum.GOODS.getItemType(), ItemTypeEnum.HOTEL.getItemType()).contains(itemType)) {
            throw new TripscrmException(TripSCRMErrorCode.REPLACE_VARIABLE_BE_FILTER);
        }

        String channelId = MapUtils.getString(paramMap, "channelId");
        if (Objects.equals(SwitchConfig.sellerAcquisitionChannelIdForWechatVideoRoom, channelId)) {
            return String.format("关于您咨询的%s，我将为您匹配商家，请稍等", StringUtils.hasLength(itemName) ? "「" + itemName + "」" : "商品");
        }

        ItemTypeEnum itemTypeEnum = ItemTypeEnum.findByType(itemType);
        // 没传商品信息，直接透出商家信息
        if (!StringUtils.hasText(itemName) && !StringUtils.hasText(itemId)) {
            long sellerId = MapUtils.getLongValue(paramMap, "sellerId", 0L);
            if (!NumberUtils.validLong(sellerId)) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
            }

            TripSCRMResult<String> sellerNickResult = mmpManager.getDisplayNick(sellerId);
            if (!sellerNickResult.isSuccess() || !StringUtils.hasText(sellerNickResult.getData())) {
                throw new TripscrmException(TripSCRMErrorCode.SYSTEM_EXCEPTION);
            }
            return String.format("关于您咨询的%s，我将为您匹配商家，请稍等", "「" + sellerNickResult.getData() + "」");
        }

        if (!Lists.newArrayList(ItemTypeEnum.GOODS, ItemTypeEnum.HOTEL).contains(itemTypeEnum) || !StringUtils.hasText(itemId)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ItemInfoDTO itemInfoDTO = new ItemInfoDTO();
        itemInfoDTO.setItemType(itemType);
        itemInfoDTO.setItemId(itemId);
        ItemVO itemVO = materialService.queryItemById(itemInfoDTO);
        itemName = Optional.ofNullable(itemVO).map(ItemVO::getName).orElse("");
        return String.format("关于您咨询的%s，我将为您匹配商家，请稍等", StringUtils.hasLength(itemName) ? "「" + itemName + "」" : "商品");
    }

    @Override
    public InnerServiceEnum getInnerServiceEnum() {
        return InnerServiceEnum.SELLER_ACQUISITION_ACTIVITY_ITEM_AND_SELLER_INFO;
    }
}
