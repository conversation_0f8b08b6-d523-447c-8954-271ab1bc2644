package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.response.variable.TripSCRMVariableInfoResponse;
import com.alibaba.tripscrm.domain.request.variable.TripSCRMVariableInfoQueryRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMVariableInfoService;
import com.alibaba.tripscrm.service.model.domain.query.VariableInfoQuery;
import com.alibaba.tripscrm.service.model.dto.material.VariableInfoDTO;
import com.alibaba.tripscrm.service.service.material.VariableInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 变量信息查询 HSF 实现
 */
@HSFProvider(serviceInterface = TripSCRMVariableInfoService.class)
public class TripSCRMVariableInfoServiceImpl implements TripSCRMVariableInfoService {

    @Autowired
    private VariableInfoService variableInfoService;

    @Override
    public TripSCRMResult<List<TripSCRMVariableInfoResponse>> list(TripSCRMVariableInfoQueryRequest request) {
        if (Objects.isNull(request) || Objects.isNull(request.getIndustryType())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        VariableInfoQuery query = new VariableInfoQuery();
        query.setId(request.getId());
        query.setName(request.getName());
        query.setSceneId(request.getSceneId());
        query.setDataSourceType(request.getDataSourceType());
        query.setIndustryType(request.getIndustryType());

        List<VariableInfoDTO> dtos = variableInfoService.query(query);
        List<TripSCRMVariableInfoResponse> responses = new ArrayList<>();
        if (Objects.nonNull(dtos)) {
            for (VariableInfoDTO dto : dtos) {
                responses.add(toResponse(dto));
            }
        }
        return TripSCRMResult.success(responses);
    }

    @Override
    public TripSCRMResult<TripSCRMVariableInfoResponse> queryById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        VariableInfoDTO dto = variableInfoService.queryById(id);
        if (dto == null) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return TripSCRMResult.success(toResponse(dto));
    }

    private TripSCRMVariableInfoResponse toResponse(VariableInfoDTO dto) {
        TripSCRMVariableInfoResponse resp = new TripSCRMVariableInfoResponse();
        resp.setId(dto.getId());
        resp.setName(dto.getName());
        resp.setIntroduce(dto.getIntroduce());
        resp.setSceneId(dto.getSceneId());
        resp.setServiceId(dto.getServiceId());
        if (dto.getIndustryType() != null) {
            resp.setIndustry(dto.getIndustryType());
        }
        if (dto.getDataSourceTypeEnum() != null) {
            resp.setDataSource(dto.getDataSourceTypeEnum().getCode());
        }
        resp.setRule(dto.getRule());
        if (dto.getId() != null) {
            resp.setValue("#{" + dto.getId() + "}");
        }
        resp.setEnv(dto.getEnv());
        return resp;
    }
}