package com.alibaba.tripscrm.service.model.domain.query;


import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WechatGroupMsgTaskQuery implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 删除状态，0：正常，1：删除
     */
    private Byte deleted;

    /**
     * 企业Id
     */
    private String corpId;

    /**
     * 企业群发消息的id，可用于获取企业群发成员执行结果
     */
    private String msgId;

    /**
     * 企微成员userId
     */
    private String userId;

    /**
     * 发送状态：0-未发送 2-已发送
     */
    private Byte status;
}