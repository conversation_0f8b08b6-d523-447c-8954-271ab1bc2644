package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SubTaskInstanceQueryRequest implements Serializable {
    private static final long serialVersionUID = -4535749078388092426L;

    /** 主键（子任务实例）ID **/
    private Long id;

    /** 任务ID **/
    private Long taskId;

    /** 主任务执行的实例ID **/
    private Long mainTaskInstanceId;

    /** 逻辑删除标志 **/
    private Byte deleted;

    /** 执行状态 **/
    private TaskInstanceStatusEnum status;

}
