package com.alibaba.tripscrm.service.service.impl.moment;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MomentInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.MomentInfoQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.task.CommentSendTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.request.SendCommentRequest;
import com.alibaba.tripscrm.service.model.vo.wechat.MomentCommentVO;
import com.alibaba.tripscrm.service.model.vo.wechat.TairCommentVO;
import com.alibaba.tripscrm.service.service.factory.ThreadPoolExecutorFactory;
import com.alibaba.tripscrm.service.service.material.MaterialContentGetService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.moment.CommentService;
import com.alibaba.tripscrm.service.service.moment.MomentInfoService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.api.service.MomentService;
import com.alibaba.tripzoo.proxy.model.WechatMomentCommentBO;
import com.alibaba.tripzoo.proxy.request.DeleteMomentCommentsRequest;
import com.alibaba.tripzoo.proxy.request.SendMomentCommentsRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.common.utils.DateUtils;
import com.taobao.csp.courier.StringUtils;
import de.danielbechler.util.Collections;
import de.danielbechler.util.Strings;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @description：
 * @Author：wangrui
 * @create：2024/10/31 下午4:30
 * @Filename：CommentServiceImpl
 */
@Service
@AllArgsConstructor
public class CommentServiceImpl implements CommentService {
    private final MomentService momentService;
    private final MomentInfoService momentInfoService;
    private final MaterialContentGetService materialContentGetService;
    private final MaterialService materialService;
    private final LdbTairManager ldbTairManager;
    private final WechatUserService wechatUserService;

    @Override
    public Boolean sendComent(SendCommentRequest request) {
        if (!Strings.hasText(request.getUserIds()) || request.getCommentId() == null) {
            return false;
        }
        try {
            List<String> userIdList = Arrays.stream(request.getUserIds().split(","))
                    .filter(Strings::hasText)
                    .collect(Collectors.toList());
            List<CompletableFuture<Boolean>> futures = userIdList.stream()
                    .map(userId -> CompletableFuture.supplyAsync(
                            () -> sendAndCacheComment(request, userId),
                            ThreadPoolExecutorFactory.MOMENT_COMMENT_THREAD_POOL
                    ))
                    .collect(Collectors.toList());
//            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            PlatformLogUtil.logException("朋友圈评论发送失败", e.getMessage(), e, LogListUtil.newArrayList(request));
            return false;
        }
        return true;
    }

    private Boolean sendAndCacheComment(SendCommentRequest request, String userId) {

        SendMomentCommentsRequest sendMomentCommentsRequest = new SendMomentCommentsRequest();
        sendMomentCommentsRequest.setUserId(userId);
        sendMomentCommentsRequest.setParentCommentId(request.getCommentId());
        // 解析素材
        String commentContent = handleMaterial(request, userId);
        sendMomentCommentsRequest.setComment(commentContent);
        //通过任务id查找jobid
        String jobId = getJobId(request.getId(), userId);
        if (!StringUtils.hasText(jobId)) {
            PlatformLogUtil.logFail("朋友圈评论接口未查找到JobId", LogListUtil.newArrayList(request, userId));
            return false;
        }
        sendMomentCommentsRequest.setJobId(jobId);
        sendMomentCommentsRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<String> result = momentService.sendComment(sendMomentCommentsRequest);
        if (!result.getSuccess()) {
            PlatformLogUtil.logFail("朋友圈评论接口发送失败", LogListUtil.newArrayList(sendMomentCommentsRequest, result));
        }

        // TODO 存缓存
        TairCommentVO tairCommentVO = convertToTairCommentVO(sendMomentCommentsRequest, result);
        tairCommentVO.setParentCommentUserName(request.getCustomerName());
        String key = getKey(userId, jobId);
        cacheReachComment(tairCommentVO, key);
        return true;
    }


    @Override
    public Boolean deleteComment(Long commentId, String userId, Long taskId) {
        if (!Strings.hasText(userId) || !NumberUtils.validLong(commentId)) {
            return false;
        }
        try {
            DeleteMomentCommentsRequest deleteMomentCommentsRequest = new DeleteMomentCommentsRequest();
            deleteMomentCommentsRequest.setCommentId(commentId);
            deleteMomentCommentsRequest.setUserId(userId);
            String jobId = getJobId(taskId, userId);
            deleteMomentCommentsRequest.setJobId(jobId);
            deleteMomentCommentsRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
            ResultDO<String> stringResultDO = momentService.deleteComment(deleteMomentCommentsRequest);
        } catch (Exception e) {
            PlatformLogUtil.logException("朋友圈评论删除失败", e.getMessage(), e, LogListUtil.newArrayList(commentId));
            return false;
        }
        return true;
    }

    @Override
    public List<MomentCommentVO> listByTaskId(Long id, String userIds) {
        List<MomentCommentVO> resultList = new ArrayList<>();
        if (!NumberUtils.validLong(id) || !Strings.hasText(userIds)) {
            return resultList;
        }
        try {
            //通过 任务id 查询出任务下面的
            List<String> userIdList = Arrays.stream(userIds.split(","))
                    .filter(Strings::hasText)
                    .collect(Collectors.toList());
            List<WechatUserDTO> wechatUserDTOS = wechatUserService.listById(userIdList);
            if (Collections.isEmpty(wechatUserDTOS)) {
                return resultList;
            }

            for (WechatUserDTO user : wechatUserDTOS) {
                MomentCommentVO momentCommentVO = getMomentCommentVO(id, user);
                if(Objects.nonNull(momentCommentVO)){
                    resultList.add(momentCommentVO);
                }
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("朋友圈评论列表查询失败", e.getMessage(), e, LogListUtil.newArrayList(id, userIds));
            return resultList;
        }

        return resultList;
    }

    private MomentCommentVO getMomentCommentVO(Long id, WechatUserDTO user) {
        String jobId = getJobId(id, user.getUserId());
        if (!StringUtils.hasText(jobId)) {
            return null;
        }
        MomentCommentVO momentCommentVO = new MomentCommentVO();
        momentCommentVO.setUserId(user.getUserId());
        momentCommentVO.setUserName(user.getName());
        List<WechatMomentCommentBO> wechatMomentCommentBOS = momentService.queryByJobId(jobId);
        momentCommentVO.setCommentList(wechatMomentCommentBOS);
        // 查询缓存获得未发送的评论
        List<TairCommentVO> tairCommentVOS = queryNotReachComment(user.getUserId(), jobId);
        momentCommentVO.setNotReachCommentList(tairCommentVOS);
        momentCommentVO.setCustomersCommentsNumber(wechatMomentCommentBOS.size());
        momentCommentVO.setRobotType(user.getRobotType());
        return momentCommentVO;
    }

    private List<TairCommentVO> queryNotReachComment(String userId, String jobId) {
        String key = getKey(userId, jobId);
        return ldbTairManager.get(key) == null ? new ArrayList<>() : JSONObject.parseArray((String) ldbTairManager.get(key), TairCommentVO.class);
    }


    private void cacheReachComment(TairCommentVO tairCommentVO, String key) {
        // 从缓存中获取对象
        Object cachedObject = ldbTairManager.get(key);
        List<TairCommentVO> tairCommentVOList;
        if (Objects.isNull(cachedObject)) {
            // 缓存中没有对象，新增缓存
            tairCommentVOList = new ArrayList<>();
            tairCommentVOList.add(tairCommentVO);
        } else {
            // 缓存中有对象，取出并解析成 List<TairCommentVO>
            String cachedJsonString = (String) cachedObject;
            tairCommentVOList = JSONObject.parseArray(cachedJsonString, TairCommentVO.class);
            tairCommentVOList.add(tairCommentVO);
        }
        ldbTairManager.put(key, JSONObject.toJSONString(tairCommentVOList), 3 * 24 * 60 * 60);
    }

    private String getJobId(Long taskId, String userId) {
        MomentInfoQuery query = new MomentInfoQuery();
        query.setTaskId(taskId);
        query.setUserIdList(Arrays.asList(userId));

        List<MomentInfoDO> momentInfoDOS = momentInfoService.queryByParam(query);
        if (Collections.isEmpty(momentInfoDOS)) {
            PlatformLogUtil.logFail("查询不到朋友圈信息", LogListUtil.newArrayList(taskId, taskId));
            return null;
        }
        return momentInfoDOS.get(0).getJobId();
    }

    private String handleMaterial(SendCommentRequest request, String userId) {
        if (Strings.hasText(request.getComment())) {
            return request.getComment();
        }
        if (Strings.hasText(request.getMaterialId())) {
            MaterailInfoDO materailInfoDO = materialService.queryById(Long.valueOf(request.getMaterialId()));
            return materialContentGetService.getMessageContent(materailInfoDO, userId, MaterialSendSceneTypeConstant.PENGYQ_PINGL);
        }

        return null;
    }

    /**
     * 获取朋友圈评论缓存key
     *
     * @param userId 企微用户id
     * @param jobId  朋友圈任务id
     * @return 朋友圈缓存key
     */
    private String getKey(String userId, String jobId) {
        PlatformLogUtil.logFail("获取朋友圈评论缓存key", LogListUtil.newArrayList(TairConstant.MOMENT_COMMENT_INFO_PREFIX + userId + "_" + jobId));
        return TairConstant.MOMENT_COMMENT_INFO_PREFIX + userId + "_" + jobId;
    }


    private TairCommentVO convertToTairCommentVO(SendMomentCommentsRequest sendMomentCommentsRequest, ResultDO<String> result) {
        TairCommentVO tairCommentVO = new TairCommentVO();
        tairCommentVO.setParentCommentId(sendMomentCommentsRequest.getParentCommentId());
        tairCommentVO.setComment(sendMomentCommentsRequest.getComment());
        tairCommentVO.setCommentTime(DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        CommentSendTypeEnum sendTypeEnum = result.getSuccess() ? CommentSendTypeEnum.SENDING : CommentSendTypeEnum.FAIL;
        tairCommentVO.setSendStatus(sendTypeEnum.getCode());
        if (!result.getSuccess()) {
            tairCommentVO.setErrorMsg(result.getResultMessage());
        }
        return tairCommentVO;

    }

}
