package com.alibaba.tripscrm.service.service;

import com.alibaba.tripscrm.service.model.domain.bo.OneIdMappingBO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OneIdMappingService {

    /**
     * 批量插入数据
     * @param oneIdMappingBOList
     * @return
     */
    int batchUpsert(List<OneIdMappingBO> oneIdMappingBOList);

    /**
     * 删除数据
     * @param oneIdMappingBO
     * @return
     */
    int delete(OneIdMappingBO oneIdMappingBO);

    /**
     * 批量删除数据
     * @param oneIdMappingBOList
     * @return
     */
    int batchDelete(List<OneIdMappingBO> oneIdMappingBOList);
}
