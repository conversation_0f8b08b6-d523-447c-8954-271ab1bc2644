package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.WechatJoinGroupDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatJoinGroupQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-01 14:03:45
 */
public interface WechatJoinGroupService {
    /**
     * 新增群活码
     *
     * @param chatIdList 群聊Id列表
     * @param state      自定义参数
     * @return WechatJoinGroupDO
     */
    WechatJoinGroupDO create(List<String> chatIdList, String state, Long spaceId, String corpId);

    /**
     * 更新群活码
     *
     * @param id         主键
     * @param chatIdList 群聊Id列表
     * @return 影响行数
     */
    Integer update(Long id, List<String> chatIdList);

    /**
     * 更新群活码
     *
     * @param id               主键
     * @param addChatIdList    新增群聊Id列表
     * @param deleteChatIdList 删除群聊Id列表
     * @return 影响行数
     */
    Integer modifyChatIdList(Long id, List<String> addChatIdList, List<String> deleteChatIdList);

    /**
     * 更新个人活码
     *
     * @param id 主键
     * @return 影响行数
     */
    Integer delete(Long id);

    /**
     * 根据id查询群活码
     *
     * @param id 主键
     * @return WechatJoinGroupDO
     */
    WechatJoinGroupDO getById(Long id);

    /**
     * 根据state查询群活码
     *
     * @param state 自定义参数
     * @return WechatJoinGroupDO
     */
    WechatJoinGroupDO getByState(String state);

    /**
     * 根据state查询群活码
     *
     * @param configId 自定义参数
     * @return WechatJoinGroupDO
     */
    WechatJoinGroupDO selectByConfigId(String configId);

    /**
     * 根据条件查询群活码
     *
     * @param query 查询条件
     * @return List<WechatJoinGroupDO>
     */
    List<WechatJoinGroupDO> getByCondition(WechatJoinGroupQuery query);
}