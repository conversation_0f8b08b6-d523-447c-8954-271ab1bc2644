package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.alibaba.tripscrm.domain.WxContentItemDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

/**
 * 微信成员信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WxCommunityContentQuery extends BasePageRequest {
    /**
     * 主键
     */
    private Long id;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * 创建人工号
     */
    private String creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 最近一次更新工号
     */
    private String lastOperatorId;

    /**
     * 最近一次更新名称
     */
    private String lastOperatorName;

    /**
     * 管理员工号，多个逗号间隔
     */
    private String managerIds;

    /**
     * 管理员名称，多个逗号间隔
     */
    private String managerNames;
    /**
     * 标题
     */
    private String title;
    /**
     * 文案
     */
    private String summary;

    /**
     * 图片
     */
    private List<String> pics;
    /**
     * 发布者名称
     */
    private String userName;
    /**
     * 发布者头像
     */
    private String userAvatar;
    /**
     * 发布者用户id
     */
    private String userId;
    /**
     * cpp内容状态
     */
    private Integer status;
    /**
     * 安审状态
     */
    private Integer operateSecurityStatus;

    /**
     * 不通过原因
     */
    private Integer operateSecurityReason;

    /**
     * cpp内容id
     */
    private Long cppContentId;
    /**
     * 标签列表
     */
    private List<String> tagIds;

    /**
     * 微信公众号文章主键值id
     */
    private Long wxArticlePkId;
    /**
     *
     */
    private String wxArticleTitle;
    /**
     * 微信公众号文章id
     */
    private String wxArticleId;
    /**
     * 微信公众号文章url
     */
    private String wxArticleUrl;
    /**
     * 月份标签
     */
    private List<String> monthLabels;
    /**
     * 挂载的商品信息
     */
    private List<WxContentItemDTO> relatedItems;
    /**
     * 活码活动id
     */
    private Long qrCodeActivityId;
    /**
     * 活码state参数，类似活码id
     */
    private String qrCodeState;
    /**
     * 活码类型
     */
    private Integer qrCodeType;
    /**
     * 活码引导语标题
     */
    private String qrCodeTitle;
    /**
     * 活码引导语内容
     */
    private String qrCodeSummary;

    /**
     * 查询时间戳
     */
    private Long gmtCreateStart;

    private Long gmtCreateEnd;

    private Long gmtModifiedStart;

    private Long gmtModifiedEnd;

    /**
     * 自定义内容状态
     */
    private Integer fliggywxStatus;
    /**
     * 搜索词
     * */
    private String searchWord;
    /**
     * 标签
     * */
    private String label;

    /**
     * myself 我的内容 online 线上全部内容  test 测试内容
     */
    private String tabType;
}
