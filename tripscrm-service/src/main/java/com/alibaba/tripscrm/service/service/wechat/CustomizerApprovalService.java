package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.CustomizerApprovalDO;
import com.alibaba.tripscrm.service.enums.system.CustomizerApprovalStatusEnum;
import com.alibaba.tripscrm.service.model.vo.customizer.CustomizerApprovalVO;
import com.alibaba.tripscrm.service.model.domain.query.CustomizerApplyQuery;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/21
 */
public interface CustomizerApprovalService {

    /**
     *
     * 申请定制师
     * @param query {@link CustomizerApplyQuery}
     * @return {@link Long}
     */
    Long apply(CustomizerApplyQuery query);

    /**
     *
     * 重新申请定制师
     * @param query {@link CustomizerApplyQuery}
     * @param exist {@link CustomizerApprovalDO}
     * @return {@link Long}
     */
    Long reApply(CustomizerApplyQuery query, CustomizerApprovalDO exist);

    /**
     *
     * 通过wechatUserId集合查找定制师申请记录
     * @param wechatUserIds {@link List}
     * @return {@link Map< Long, CustomizerApprovalDO>}
     */
    Map<Long, CustomizerApprovalDO> queryByWechatUserId(List<Long> wechatUserIds);

    /**
     *
     * 修改申请定制师状态
     * @param id {@link Long}
     * @param statusEnum {@link CustomizerApprovalStatusEnum}
     * @return {@link Boolean}
     */
    Boolean updateCustomizerApprovalStatus(Long id, CustomizerApprovalStatusEnum statusEnum);

    /**
     *
     * 通过wechatUserId查询定制师申请记录
     * @param userId {@link Long}
     * @return {@link CustomizerApprovalDO}
     */
    CustomizerApprovalDO selectByWechatUserId(Long userId);

    /**
     *
     * 实体类转换为vo
     * @param customizerApprovalDO {@link CustomizerApprovalDO}
     * @return {@link CustomizerApprovalVO}
     */
    CustomizerApprovalVO do2Vo(CustomizerApprovalDO customizerApprovalDO);

    /**
     * 发送私聊消息
     *
     * @param wechatUserId {@link String}
     * @param taoId {@link String}
     * @param state {@link String}
     * @param cropId {@link String}
     */
    void sendSingleChatMessage(String wechatUserId, String taoId, String state, String cropId);

    /**
     * 发送欢迎语消息
     *
     * @param wechatUserId {@link String}
     * @param externalUserId {@link String}
     * @param taoId {@link String}
     * @param state {@link String}
     * @param cropId {@link String}
     * @param welcomeCode {@link String}
     */
    void sendWelcomeMessage(String wechatUserId, String externalUserId, String taoId, String state, String cropId, String welcomeCode);

    /**
     *
     * 更新缓存
     */
    void updateCustomizerTair();
}
