package com.alibaba.tripscrm.service.service.impl;


import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.MonitorRecordMapper;
import com.alibaba.tripscrm.dal.model.domain.data.MonitorRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.MonitorRecordParam;
import com.alibaba.tripscrm.service.model.domain.query.MonitorRecordQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.MonitorRecordService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/21 10:35
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MonitorRecordServiceImpl implements MonitorRecordService {
    private final MonitorRecordMapper monitorRecordMapper;

    @Override
    public Integer insert(MonitorRecordDO record) {
        if (Objects.isNull(record)) {
            return 0;
        }
        MonitorRecordQuery query = new MonitorRecordQuery();
        query.setTargetId(record.getTargetId());
        query.setTargetType(record.getTargetType());
        query.setStatus((byte) 1);
        query.setDeleted((byte) 0);
        List<MonitorRecordDO> monitorRecordDOS = selectByCondition(query);
        // 如果该用户存在已确认未删除的记录，则创建失败
        if (!monitorRecordDOS.isEmpty()) {
            PlatformLogUtil.logFail("MonitorRecordServiceImpl.insert", TripSCRMErrorCode.MONITOR_RECORD_IS_CONFIRMED.getCode(), LogListUtil.newArrayList(query));
            throw new TripscrmException(TripSCRMErrorCode.MONITOR_RECORD_IS_CONFIRMED);
        }
        return monitorRecordMapper.insertSelective(record);
    }

    @Override
    public Integer updateByPrimaryKey(MonitorRecordDO record) {
        if (Objects.isNull(record) || Objects.isNull(record.getId())) {
            return 0;
        }
        return monitorRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public PageInfo<MonitorRecordDO> pageQuery(MonitorRecordQuery query) {
        try {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());

            MonitorRecordParam param = buildParam(query);

            param.appendOrderByClause(MonitorRecordParam.OrderCondition.GMTCREATE, MonitorRecordParam.SortType.DESC);

            List<MonitorRecordDO> list = monitorRecordMapper.selectByParamWithBLOBs(param);

            // 结果处理
            if (Objects.isNull(list)) {
                return null;
            }

            return new PageInfo<>(list);


        } catch (Exception e) {
            PlatformLogUtil.logException("pageQuery执行异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }

    }

    @Override
    @AteyeInvoker(description = "删除规则记录", paraDesc = "id")
    public Integer delete(Long id) {
        return monitorRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<MonitorRecordDO> selectByCondition(MonitorRecordQuery query) {
        if (Objects.isNull(query)) {
            return new ArrayList<>();
        }
        MonitorRecordParam param = buildParam(query);
        param.appendOrderByClause(MonitorRecordParam.OrderCondition.ID, MonitorRecordParam.SortType.ASC);

        return monitorRecordMapper.selectByParamWithBLOBs(param);

    }

    @Override
    public MonitorRecordDO selectByPrimaryKey(Long id) {
        return monitorRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public Integer upsert(MonitorRecordDO record) {
        return monitorRecordMapper.upsertSelective(record);
    }

    public MonitorRecordParam buildParam(MonitorRecordQuery query) {
        MonitorRecordParam param = new MonitorRecordParam();
        MonitorRecordParam.Criteria criteria = param.or();
        if (NumberUtils.biggerThanZero(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }

        if (StringUtils.hasLength(query.getTargetId())) {
            criteria.andTargetIdEqualTo(query.getTargetId());
        }

        if (Objects.nonNull(query.getTargetType())) {
            criteria.andTargetTypeEqualTo(query.getTargetType());
        }

        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }

        if (StringUtils.hasLength(query.getCreatorId())) {
            criteria.andCreatorIdEqualTo(query.getCreatorId());
        }

        if (StringUtils.hasLength(query.getLastOperatorId())) {
            criteria.andLastOperatorIdEqualTo(query.getLastOperatorId());
        }

        if (Objects.nonNull(query.getEntryMethod())) {
            criteria.andEntryMethodEqualTo(query.getEntryMethod());
        }


        if (StringUtils.hasLength(query.getBizId())) {
            criteria.andBizIdLike("%" + query.getBizId() + "%");
        }

        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }

        if (Objects.nonNull(query.getSpaceId())) {
            criteria.andSpaceIdEqualTo(query.getSpaceId());
        }

        if (Objects.nonNull(query.getIndustry())) {
            criteria.andIndustryEqualTo(query.getIndustry());
        }

        if (Objects.nonNull(query.getScene())) {
            criteria.andSceneEqualTo(query.getScene());
        }

        if (Objects.nonNull(query.getCreateStartTime())) {
            criteria.andGmtCreateGreaterThanOrEqualTo(query.getCreateStartTime());
        }

        if (Objects.nonNull(query.getCreateEndTime())) {
            criteria.andGmtCreateLessThanOrEqualTo(query.getCreateEndTime());
        }

        return param;
    }
}
