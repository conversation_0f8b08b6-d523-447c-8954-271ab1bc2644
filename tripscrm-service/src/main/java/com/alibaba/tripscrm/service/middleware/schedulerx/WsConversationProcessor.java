package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 定时任务 用于每日凌晨自动将有未读数的会话移到会话前面
 *
 * <AUTHOR>
 * @date 2023/11/24
 */
@Slf4j(topic = "FusionChatLog")
@Service
public class WsConversationProcessor extends JavaProcessor {
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private ChatConversationRepository chatConversationRepository;
    @Resource
    private LdbTairManager ldbTairManager;

    @Override
    public ProcessResult process(JobContext jobContext) throws Exception {
//        List<WechatUserDTO> wechatUsers = wechatUserService.listAll();
//        int allConversationCount = 0;
//        log.info("【未读红点会话前置】开始置顶会话...");
//        for (WechatUserDTO wechatUser : wechatUsers) {
//            List<ChatConversationDO> conversations = chatConversationRepository.listByUserId(wechatUser.getCorpId(), wechatUser.getUserId(), null);
//            Collections.reverse(conversations);
//            int conversationCount = 0;
//            int userUnreadCount = 0;
//            long currentTime = System.currentTimeMillis();
//            conversations = conversations.stream().sorted(Comparator.comparing(ChatConversationDO::getLastMessageCreateTime)).collect(Collectors.toList());
//            for (ChatConversationDO conversation : conversations) {
//                if (conversation.getUnreadCount() != 0) {
////                    conversation.setUpdateTimestamp(currentTime+conversationCount);
////                    chatConversationRepository.update(conversation);
//                    log.info("【未读红点会话前置】置顶会话顺序:{}", conversation.getChatId());
//                    conversationCount++;
//                    allConversationCount++;
//                    userUnreadCount += conversation.getUnreadCount();
//                }
//            }
//            ldbTairManager.setCount(WECHAT_USER_UNREAD_COUNT + wechatUser.getCorpId() + "|" + wechatUser.getUserId(), userUnreadCount, 0);
//            log.info("【未读红点会话前置】账号:{}共计置顶{}个会话，重新设置企微号未读数:{}", wechatUser.getName(), conversationCount, userUnreadCount);
//        }
//        log.info("【未读红点会话前置】企微号{}个，共计置顶会话{}个", wechatUsers.size(), allConversationCount);
        return new ProcessResult(true);
    }
}
