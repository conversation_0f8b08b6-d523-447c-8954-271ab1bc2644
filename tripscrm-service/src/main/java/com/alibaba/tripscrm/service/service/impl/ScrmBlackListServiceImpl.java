package com.alibaba.tripscrm.service.service.impl;

import com.alibaba.tripscrm.dal.mapper.tddl.ScrmBlackListMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmBlackListDO;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmBlackListParam;
import com.alibaba.tripscrm.service.model.domain.query.ScrmBlackListQuery;
import com.alibaba.tripscrm.service.service.ScrmBlackListService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/5 17:38
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ScrmBlackListServiceImpl implements ScrmBlackListService {
    private final ScrmBlackListMapper scrmBlackListMapper;

    @Override
    public List<ScrmBlackListDO> selectByCondition(ScrmBlackListQuery query) {
        ScrmBlackListParam param = new ScrmBlackListParam();
        ScrmBlackListParam.Criteria criteria = param.or();
        if (NumberUtils.biggerThanZero(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (!CollectionUtils.isEmpty(query.getTargetIdList())) {
            criteria.andTargetIdIn(query.getTargetIdList().stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }
        if (Objects.nonNull(query.getTargetType())) {
            criteria.andTargetTypeEqualTo(query.getTargetType().getCode().byteValue());
        }
        if (Objects.nonNull(query.getType())) {
            criteria.andTypeEqualTo(query.getType().getCode());
        }
        criteria.andDeletedEqualTo((byte) 0);
        return Optional.ofNullable(scrmBlackListMapper.selectByParam(param)).orElse(new ArrayList<>());
    }

    @AteyeInvoker(description = "新增scrm黑名单", paraDesc = "targetType&targetId&reason&type")
    public Integer insert(Byte targetType, String targetId, String reason, Byte type) {
        ScrmBlackListDO record = new ScrmBlackListDO();
        record.setTargetType(targetType);
        record.setTargetId(targetId);
        record.setReason(reason);
        record.setType(type);
        return insert(record);
    }

    @Override
    public Integer insert(ScrmBlackListDO record) {
        return scrmBlackListMapper.insertSelective(record);
    }

    @Override
    public Integer upsert(ScrmBlackListDO record) {
        record.setReason(Optional.ofNullable(record.getReason()).orElse(""));
        return scrmBlackListMapper.upsert(record);
    }

    @Override
    public Integer updateById(ScrmBlackListDO record) {
        return scrmBlackListMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    @AteyeInvoker(description = "删除scrm黑名单", paraDesc = "id")
    public Integer deleteById(Long id) {
        return scrmBlackListMapper.deleteByPrimaryKey(id);
    }

    @Override
    public Integer updateByTargetIdAndTargetTypeAndType(ScrmBlackListDO record) {
        ScrmBlackListParam param = new ScrmBlackListParam();
        param.or()
                .andTargetIdEqualTo(record.getTargetId())
                .andTargetTypeEqualTo(record.getTargetType())
                .andTypeEqualTo(record.getType());
        return scrmBlackListMapper.updateByParamSelective(record, param);
    }
}
