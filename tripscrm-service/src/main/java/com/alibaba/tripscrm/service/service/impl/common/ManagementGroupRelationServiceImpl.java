package com.alibaba.tripscrm.service.service.impl.common;

import com.alibaba.tripscrm.dal.model.domain.data.ManagementGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.query.GroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.vo.manage.ManageGroupRelationVO;
import com.alibaba.tripscrm.service.model.vo.manage.ManagementGroupVO;
import com.alibaba.tripscrm.service.model.dto.ResourceRelationDTO;
import com.alibaba.tripscrm.service.service.common.ManagementGroupRelationService;
import com.alibaba.tripscrm.service.service.common.ManagementGroupService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/5 16:48
 **/
@Service
public class ManagementGroupRelationServiceImpl implements ManagementGroupRelationService {

    @Resource
    private ManagementGroupService managementGroupService;
    @Resource
    private ResourceRelationService resourceRelationService;
    @Resource
    private WechatUserService wechatUserService;

    @Override
    public Boolean add(ManageGroupRelationVO manageGroupRelationVO) {
        // 新增管理组
        ManagementGroupDO managementGroupDO = new ManagementGroupDO();
        managementGroupDO.setGmtCreate(new Date());
        managementGroupDO.setGmtModified(new Date());
        managementGroupDO.setSpaceId(manageGroupRelationVO.getSpaceId());
        managementGroupDO.setCreatorId(manageGroupRelationVO.getOperatorId());
        managementGroupDO.setType(manageGroupRelationVO.getType());
        managementGroupDO.setName(manageGroupRelationVO.getName());
        managementGroupDO.setAdminList(manageGroupRelationVO.getOperatorId());
        managementGroupDO.setLastOperatorId(manageGroupRelationVO.getOperatorId());
        managementGroupDO.setIsDelete(IsDeleteEnum.NO.getCode());
        boolean managementGroupResult = managementGroupService.add(managementGroupDO);
        if (!managementGroupResult) {
            return false;
        }
        // 新增成员
        if (StringUtils.isBlank(manageGroupRelationVO.getMembers())) {
            return true;
        }
        ArrayList<ResourceRelationDO> resourceRelationList = new ArrayList<>();
        List<String> targetIds = Arrays.stream(manageGroupRelationVO.getMembers().split(",")).collect(Collectors.toList());
        for (String targetId : targetIds) {
            resourceRelationList.add(buildManagementGroupRelationDO(managementGroupDO, manageGroupRelationVO.getMemberType(), targetId, manageGroupRelationVO.getRelationType()));
        }
        return resourceRelationService.batchAdd(resourceRelationList);
    }

    @Override
    public Boolean updateByManagementGroupId(ManageGroupRelationVO manageGroupRelationVO) {
        // 更新管理组
        ManagementGroupDO managementGroupDO = managementGroupService.queryById(manageGroupRelationVO.getId());
        if (managementGroupDO == null) {
            return false;
        }
        managementGroupDO.setLastOperatorId(manageGroupRelationVO.getOperatorId());
        managementGroupDO.setGmtModified(new Date());
        managementGroupDO.setName(manageGroupRelationVO.getName());
        managementGroupDO.setAdminList(manageGroupRelationVO.getMembers());
        boolean managementGroupResult = managementGroupService.updateById(managementGroupDO);
        if (!managementGroupResult) {
            return false;
        }
        // 更新关系
        if (StringUtils.isBlank(manageGroupRelationVO.getMembers())) {
            return true;
        }
        ArrayList<ResourceRelationDO> resourceRelationList = new ArrayList<>();
        List<String> targetIds = Arrays.stream(manageGroupRelationVO.getMembers().split(",")).collect(Collectors.toList());
        for (String targetId : targetIds) {
            resourceRelationList.add(buildManagementGroupRelationDO(managementGroupDO, manageGroupRelationVO.getMemberType(), targetId, manageGroupRelationVO.getRelationType()));
        }
        ResourceRelationDTO resourceRelationDTO = new ResourceRelationDTO();
        resourceRelationDTO.setResourceType(ResourceTypeEnum.MANAGEMENT_GROUP.getCode().byteValue());
        resourceRelationDTO.setResourceId(String.valueOf(managementGroupDO.getId()));
        resourceRelationDTO.setRelationType(managementGroupDO.getType());
        resourceRelationDTO.setResourceRelationDOList(resourceRelationList);
        return resourceRelationService.updateResourceRelation(resourceRelationDTO);
    }

    /**
     * 构建管理组对应的资源关系对象
     * @param managementGroupDO 管理组
     * @param targetType 目标类型
     * @param targetId 目标Id
     * @param relationType 资源关系类型
     * @return 资源关系
     */
    private ResourceRelationDO buildManagementGroupRelationDO(ManagementGroupDO managementGroupDO, Byte targetType, String targetId, Byte relationType) {
        ResourceRelationDO resourceRelationDO = new ResourceRelationDO();
        resourceRelationDO.setGmtCreate(new Date());
        resourceRelationDO.setGmtModified(new Date());
        resourceRelationDO.setRelationType(relationType);
        resourceRelationDO.setSourceType(ResourceTypeEnum.MANAGEMENT_GROUP.getCode().byteValue());
        resourceRelationDO.setSourceId(String.valueOf(managementGroupDO.getId()));
        resourceRelationDO.setTargetType(targetType);
        resourceRelationDO.setTargetId(targetId);
        return resourceRelationDO;
    }

    @Override
    public Boolean deleteById(Long id, String operatorUserId) {
        // 校验删除条件
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setTargetType(ResourceTypeEnum.MANAGEMENT_GROUP.getCode().byteValue());
        query.setTargetId(String.valueOf(id));
        List<ResourceRelationDO> usageList = resourceRelationService.query(query);
        if (CollectionUtils.isNotEmpty(usageList)) {
            return false;
        }
        // 删除
        ManagementGroupDO managementGroupDO = new ManagementGroupDO();
        managementGroupDO.setIsDelete(IsDeleteEnum.YES.getCode());
        managementGroupDO.setLastOperatorId(operatorUserId);
        managementGroupDO.setId(id);
        return managementGroupService.updateById(managementGroupDO);
    }

    @Override
    public PageResultVO<ManagementGroupVO> list(GroupQuery query) {
        ArrayList<ManagementGroupVO> result = new ArrayList<>();
        Long sumCount = managementGroupService.count(query);
        // 管理组信息
        List<ManagementGroupDO> managementGroupDOS = managementGroupService.queryByPage(query);
        if (CollectionUtils.isEmpty(managementGroupDOS)) {
            return PageResultVO.successResult(Lists.newArrayList(), sumCount.intValue(), query.getPageNum(), query.getPageSize());
        }
        // 组内资源
        for (ManagementGroupDO managementGroupDO : managementGroupDOS) {
            List<ResourceRelationDO> resourceRelationDOList = resourceRelationService
                    .query(buildManagementGroupRelationQuery(String.valueOf(managementGroupDO.getId())));
            result.add(fillUserInfo(buildManagementGroupVO(managementGroupDO, resourceRelationDOList)));
        }
        return PageResultVO.successResult(result, sumCount.intValue(), query.getPageNum(), query.getPageSize());
    }

    @Override
    public ManagementGroupVO getById(Long id) {
        // 管理组查询
        if (!NumberUtils.validLong(id)) {
            return null;
        }
        ManagementGroupDO managementGroupDO = managementGroupService.queryById(id);
        if (managementGroupDO == null) {
            return null;
        }
        // 组成员查询
        List<ResourceRelationDO> resourceRelationDOList = resourceRelationService.query(buildManagementGroupRelationQuery(String.valueOf(id)));
        // 对象构建
        return fillUserInfo(buildManagementGroupVO(managementGroupDO, resourceRelationDOList));
    }

    /**
     * 填充管理组成员信息
     * @param managementGroupVO 视图层对象
     * @return 视图层对象
     */
    private ManagementGroupVO fillUserInfo(ManagementGroupVO managementGroupVO) {
        if (managementGroupVO == null || CollectionUtils.isEmpty(managementGroupVO.getInfoList())) {
            return managementGroupVO;
        }
        List<String> userIdList = managementGroupVO.getInfoList().stream().map(ManagementGroupVO.Info::getTargetId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return managementGroupVO;
        }
        List<WechatUserDTO> wechatUserDTOS = wechatUserService.listById(userIdList);
        if (CollectionUtils.isEmpty(wechatUserDTOS)) {
            return managementGroupVO;
        }
        Map<String, WechatUserDTO> userInfoMap = wechatUserDTOS.stream().collect(Collectors.toMap(WechatUserDTO::getUserId, wechatUserDTO -> wechatUserDTO));
        for (ManagementGroupVO.Info info : managementGroupVO.getInfoList()) {
            info.setTargetName(Optional.ofNullable(userInfoMap.get(info.getTargetId())).orElse(new WechatUserDTO()).getName());
        }
        return managementGroupVO;
    }

    /**
     * 构建群管理组关系查询条件
     * @param managementGroupId 群管理组id
     * @return 资源关系查询条件
     */
    private ResourceRelationQuery buildManagementGroupRelationQuery(String managementGroupId) {
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.GROUP_MANAGE.getCode());
        query.setSourceType(ResourceTypeEnum.MANAGEMENT_GROUP.getCode().byteValue());
        query.setSourceId(managementGroupId);
        return query;
    }

    /**
     * 构建管理组视图对象
     * @param managementGroupDO 管理组
     * @param resourceRelationDOList 管理组成员
     * @return 管理组视图对象
     */
    private ManagementGroupVO buildManagementGroupVO(ManagementGroupDO managementGroupDO, List<ResourceRelationDO> resourceRelationDOList) {
        ManagementGroupVO managementGroupVO = new ManagementGroupVO();
        managementGroupVO.setId(managementGroupDO.getId());
        managementGroupVO.setName(managementGroupDO.getName());
        if (CollectionUtils.isEmpty(resourceRelationDOList)) {
            return managementGroupVO;
        }
        managementGroupVO.setInfoList(ManagementGroupVO.buildManagementInfoList(resourceRelationDOList));
        return managementGroupVO;
    }

}
