package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.ali.unit.rule.util.lang.CollectionUtils;
import com.ali.unit.rule.util.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.mapper.tddl.WxSyncJobInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WxEnterpriseConsumerGroupInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WxSyncJobInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WxSyncJobInfoParam;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.model.domain.query.ConsumerGroupQuery;
import com.alibaba.tripscrm.service.model.vo.wechat.GroupListVO;
import com.alibaba.tripscrm.service.service.wechat.WxGroupService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 同步微信客户群定时任务
 *
 * <AUTHOR>
 * @date 2023/4/4
 */
@Slf4j
@Component
public class WxEnterpriseGroupSyncProcessor extends JavaProcessor {

    @Resource
    private WxGroupService wxGroupService;
    @Resource
    private WxSyncJobInfoMapper wxSyncJobInfoMapper;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        JSONObject jobParamJs = JSONObject.parseObject(context.getJobParameters());
        Integer fixSecond = jobParamJs.getInteger("fixSecond");
        Integer limit = jobParamJs.getInteger("limit");
        Long jobLabel = System.currentTimeMillis() / (1000L * fixSecond);
        //查询是否有同步相同label的同步任务
        WxSyncJobInfoParam wxSyncJobInfoParam = new WxSyncJobInfoParam();
        wxSyncJobInfoParam.createCriteria()
                .andBizTypeEqualTo(WxConstants.wxEnterpriseConsumerGroupBizType)
                .andJobLabelEqualTo(jobLabel);
        List<WxSyncJobInfoDO> wxSyncJobInfoDOS = wxSyncJobInfoMapper.selectByParam(wxSyncJobInfoParam);
        WxSyncJobInfoDO syncJobInfoDO;
        if (CollectionUtils.isEmpty(wxSyncJobInfoDOS)) {
            syncJobInfoDO = new WxSyncJobInfoDO();
            syncJobInfoDO.setFixSecond(fixSecond);
            syncJobInfoDO.setJobLabel(jobLabel);
            syncJobInfoDO.setBizType(WxConstants.wxEnterpriseConsumerGroupBizType);
            syncJobInfoDO.setStauts(1);
            if (wxSyncJobInfoMapper.insert(syncJobInfoDO) <= 0) {
                PlatformLogUtil.logFail("同步微信客户群定时任务，任务落库失败", LogListUtil.newArrayList(syncJobInfoDO));
            }
        } else {
            syncJobInfoDO = wxSyncJobInfoDOS.get(0);
            // 已经有执行成功的任务，直接返回
            if (syncJobInfoDO.getStauts() == 2) {
                PlatformLogUtil.logFail("同步微信客户群定时任务，已存在执行成功的任务，直接返回", LogListUtil.newArrayList(syncJobInfoDO));
                return new ProcessResult(true);
            }
        }
        boolean first = true;
        //拉取群数据
        while (first || StringUtils.isNotBlank(syncJobInfoDO.getNextCursor())){
            first = false;
            ConsumerGroupQuery consumerGroupQuery = new ConsumerGroupQuery();
            consumerGroupQuery.setCursor(syncJobInfoDO.getNextCursor());
            consumerGroupQuery.setLimit(limit);
            GroupListVO groupListVO = wxGroupService.listWxEnterpriseConsumerGroup(consumerGroupQuery);
            if (Objects.isNull(groupListVO)){
                PlatformLogUtil.logFail("同步微信客户群定时任务，获取用户群失败", LogListUtil.newArrayList(consumerGroupQuery));
                return new ProcessResult(false);
            }
            syncJobInfoDO.setNextCursor(groupListVO.getNextCursor());
            if (wxSyncJobInfoMapper.updateByPrimaryKeySelective(syncJobInfoDO) <= 0) {
                PlatformLogUtil.logFail("同步微信客户群定时任务，根据主键更新任务失败", LogListUtil.newArrayList(syncJobInfoDO));
                return new ProcessResult(false);
            }
            // 填充群信息
            for (String chatId : groupListVO.getGroupChatList()) {
                WxEnterpriseConsumerGroupInfoDO groupInfoDO = wxGroupService.getWxEnterpriseConsumerGroupInfo(chatId);
                if (Objects.isNull(groupInfoDO)) {
                    PlatformLogUtil.logFail("同步微信客户群定时任务，获取用户组详情失败", LogListUtil.newArrayList(chatId, groupInfoDO));
                    continue;
                }
                groupInfoDO.setSyncTaskId(String.valueOf(jobLabel));
                // 保存DB，新的新增，老的覆盖
                Long ret = wxGroupService.recoverGroupInfo(groupInfoDO);
                if (!NumberUtils.validLong(ret)) {
                    PlatformLogUtil.logFail("同步微信客户群定时任务，覆盖保存微信群信息失败", LogListUtil.newArrayList(chatId, groupInfoDO));
                }else {
                    PlatformLogUtil.logFail("同步微信客户群定时任务，覆盖保存微信群信息成功", LogListUtil.newArrayList(chatId, groupInfoDO));
                }
            }
        }
        // 删除同步id不同的数据，废弃的群
        int delNum = wxGroupService.delSyncByJobLabel(syncJobInfoDO.getJobLabel());
        PlatformLogUtil.logFail("同步微信客户群定时任务，删除同步id不同的数据(废弃的群)成功", LogListUtil.newArrayList(jobLabel, delNum));
        syncJobInfoDO.setStauts(2);
        if(wxSyncJobInfoMapper.updateByPrimaryKeySelective(syncJobInfoDO) <= 0){
            PlatformLogUtil.logFail("同步微信客户群定时任务，根据主键更新任务状态失败", LogListUtil.newArrayList(jobLabel, syncJobInfoDO));
        }
        return new ProcessResult(true);
    }

}
