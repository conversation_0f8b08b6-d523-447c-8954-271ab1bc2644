package com.alibaba.tripscrm.service.service.strategy.websocket.param.request;

import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/25 16:22
 **/
@Data
public class SupportChatListRequest {

    /**
     * 聊天类型
     * @see ChatTypeEnum
     */
    private List<Integer> chatType;

    /**
     * 聊天名称
     */
    private String chatName;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 100;
}
