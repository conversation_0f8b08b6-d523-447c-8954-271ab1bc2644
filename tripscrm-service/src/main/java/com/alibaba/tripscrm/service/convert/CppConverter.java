package com.alibaba.tripscrm.service.convert;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.*;
import com.google.common.collect.Lists;
import com.taobao.contentplatform.article.base.read.resp.ArticleDTO;
import com.taobao.contentplatform.content.base.element.JsonArrayElement;
import com.taobao.contentplatform.content.base.element.PicElement;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/20 22:38
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CppConverter {

    public WxCommunityContentDTO convertFromArticleDTO(ArticleDTO articleDTO) {
        WxCommunityContentDTO communityContentDTO = new WxCommunityContentDTO();

        communityContentDTO.setCppContentId(articleDTO.getId());
        communityContentDTO.setTitle(articleDTO.getTitle());
        communityContentDTO.setSummary(articleDTO.getSummary());

        communityContentDTO.setGmtCreate(new Date(articleDTO.getGmtCreate()));
        communityContentDTO.setGmtModified(new Date(articleDTO.getGmtModified()));

        // 关联商品
        JsonArrayElement fliggywxRelatedItems = articleDTO.getFliggywxRelatedItems();
        if (Objects.nonNull(fliggywxRelatedItems)) {
            List<WxContentItemDTO> relatedItems = fliggywxRelatedItems.getValue().stream().map(e -> JSONObject.parseObject(JSON.toJSONString(e), WxContentItemDTO.class)).collect(Collectors.toList());
            communityContentDTO.setRelatedItems(relatedItems);
        }

        // 图片
        List<PicElement> pics = articleDTO.getPics();
        if (CollectionUtils.isNotEmpty(pics)) {
            List<FileDTO> fileDTOList = pics.stream().map(e -> FileDTO.builder().url(e.getUrl()).height(e.getHeight()).width(e.getWidth()).build()).collect(Collectors.toList());
            communityContentDTO.setPics(fileDTOList);
        }

        // 标签
        communityContentDTO.setTagIds(articleDTO.getLabels());

        // 应季标签
        communityContentDTO.setMonthLabels(articleDTO.getFliggywxMonthLabels());

        // 活码
        communityContentDTO.setQrCodeState(articleDTO.getFliggywxQrCodeState());
        communityContentDTO.setQrCodeType(articleDTO.getFliggywxQrCodeType());
        communityContentDTO.setQrCodeTitle(articleDTO.getFliggywxQrCodeTitle());
        communityContentDTO.setQrCodeSummary(articleDTO.getFliggywxQrCodeSummary());
        communityContentDTO.setQrCodeActivityId(articleDTO.getFliggywxQrCodeActivityId());

        // 微信文章
        communityContentDTO.setWxArticleId(articleDTO.getFliggywxWxArticleId());
        communityContentDTO.setWxArticleUrl(articleDTO.getFliggywxWxArticleUrl());
        communityContentDTO.setWxArticlePkId(articleDTO.getFliggywxWxArticlePkId());

        // 安审状态
        communityContentDTO.setOperateSecurityStatus(articleDTO.getOperateSecurityStatus());
        communityContentDTO.setStatus(articleDTO.getFliggywxStatus());

        // 用户信息
        communityContentDTO.setUserAvatar(articleDTO.getUserAvatar());
        communityContentDTO.setUserId(articleDTO.getUserId());
        communityContentDTO.setUserName(articleDTO.getUserName());

        // 编辑者信息
        communityContentDTO.setCreatorName(articleDTO.getFliggywxCreator());
        communityContentDTO.setManagerNames(articleDTO.getFliggywxOwner());
        communityContentDTO.setLastOperatorName(articleDTO.getFliggywxUpdater());

        // 环境
        communityContentDTO.setEnv(articleDTO.getFliggywxEnv());

        return communityContentDTO;
    }

    public WxCommunityContentDTO convertFromMap(JSONObject jsonObject) {
        if (MapUtils.isEmpty(jsonObject)) {
            return null;
        }
        try {
            WxCommunityContentDTO communityContentDTO = new WxCommunityContentDTO();

            communityContentDTO.setCppContentId(MapUtils.getLong(jsonObject, "id"));
            communityContentDTO.setTitle(MapUtils.getString(jsonObject, "title"));
            communityContentDTO.setSummary(MapUtils.getString(jsonObject, "fliggywx_summary"));

            Long gmtCreate = MapUtils.getLong(jsonObject, "gmt_create");
            if (Objects.nonNull(gmtCreate)) {
                communityContentDTO.setGmtCreate(new Date(gmtCreate));
            }
            Long gmtModified = MapUtils.getLong(jsonObject, "gmt_modified");
            if (Objects.nonNull(gmtModified)) {
                communityContentDTO.setGmtModified(new Date(gmtModified));
            }

            // 关联商品
            String fliggywxRelatedItems = MapUtils.getString(jsonObject, "fliggywx_related_items");
            if (StringUtils.isNotBlank(fliggywxRelatedItems)) {
                JSONObject relatedItemJsonObject = JSONObject.parseObject(fliggywxRelatedItems);
                JSONArray relatedItemJsonArray = relatedItemJsonObject.getJSONArray("value/J");
                if (CollectionUtils.isEmpty(relatedItemJsonArray)) {
                    relatedItemJsonArray = relatedItemJsonObject.getJSONArray("value");
                }
                if (CollectionUtils.isNotEmpty(relatedItemJsonArray)) {
                    List<WxContentItemDTO> relatedItems = relatedItemJsonArray.stream().map(e -> JSONObject.parseObject(JSON.toJSONString(e), WxContentItemDTO.class)).collect(Collectors.toList());
                    communityContentDTO.setRelatedItems(relatedItems);
                }
            }

            // 图片
            String pics = MapUtils.getString(jsonObject, "pics");
            List<FileDTO> picList = Lists.newArrayList();
            if (StringUtils.isNotEmpty(pics)) {
                JSONArray picJsonArray = JSONArray.parseArray(pics);
                for (int i = 0; i < picJsonArray.size(); i++) {
                    JSONObject picJsonObject = picJsonArray.getJSONObject(i);
                    String url = MapUtils.getString(picJsonObject, "url/s");
                    Long height = MapUtils.getLong(picJsonObject, "height/s");
                    Long width = MapUtils.getLong(picJsonObject, "width/s");
                    if (StringUtils.isBlank(url)) {
                        url = MapUtils.getString(picJsonObject, "url");
                    }
                    if (Objects.isNull(height)) {
                        height = MapUtils.getLong(picJsonObject, "height");
                    }
                    if (Objects.isNull(width)) {
                        width = MapUtils.getLong(picJsonObject, "width");
                    }
                    picList.add(FileDTO.builder().height(height).width(width).url(url).build());
                }
                communityContentDTO.setPics(picList);
            }

            // 标签
            String labels = MapUtils.getString(jsonObject, "labels");
            if (StringUtils.isNotBlank(labels)) {
                communityContentDTO.setTagIds(Arrays.asList(labels.split("\t")));
            }

            // 应季标签
            String monthLabels = MapUtils.getString(jsonObject, "fliggywx_month_labels");
            if (StringUtils.isNotBlank(monthLabels)) {
                communityContentDTO.setMonthLabels(Arrays.asList(monthLabels.split("\t")));
            }

            // 活码
            communityContentDTO.setQrCodeState(MapUtils.getString(jsonObject, "fliggywx_qr_code_state"));
            communityContentDTO.setQrCodeType(MapUtils.getInteger(jsonObject, "fliggywx_qr_code_type"));
            communityContentDTO.setQrCodeTitle(MapUtils.getString(jsonObject, "fliggywx_qr_code_title"));
            communityContentDTO.setQrCodeSummary(MapUtils.getString(jsonObject, "fliggywx_qr_code_summary"));
            communityContentDTO.setQrCodeActivityId(MapUtils.getLong(jsonObject, "fliggywx_qr_code_activity_id"));

            // 微信文章
            communityContentDTO.setWxArticleId(MapUtils.getString(jsonObject, "fliggywx_wx_article_id"));
            communityContentDTO.setWxArticleUrl(MapUtils.getString(jsonObject, "fliggywx_wx_article_url"));
            communityContentDTO.setWxArticlePkId(MapUtils.getLong(jsonObject, "fliggywx_wx_article_pk_id"));

            // 安审状态
            communityContentDTO.setOperateSecurityStatus(MapUtils.getInteger(jsonObject, "operate_security_status"));
            communityContentDTO.setOperateSecurityReason(MapUtils.getString(jsonObject, "operate_security_reason"));
            communityContentDTO.setStatus(MapUtils.getInteger(jsonObject, "fliggywx_status"));

            // 用户信息
            communityContentDTO.setUserAvatar(MapUtils.getString(jsonObject, "user_avatar"));
            communityContentDTO.setUserId(MapUtils.getString(jsonObject, "user_id"));
            communityContentDTO.setUserName(MapUtils.getString(jsonObject, "user_name"));

            // 编辑者信息
            communityContentDTO.setCreatorName(MapUtils.getString(jsonObject, "fliggywx_creator"));
            communityContentDTO.setManagerNames(MapUtils.getString(jsonObject, "fliggywx_owner"));
            communityContentDTO.setLastOperatorName(MapUtils.getString(jsonObject, "fliggywx_updater"));

            // 环境
            communityContentDTO.setEnv(MapUtils.getString(jsonObject, "fliggywx_env"));

            return communityContentDTO;
        } catch (Exception e) {
            PlatformLogUtil.logException("convertFromMap，执行异常", e.getMessage(), e, LogListUtil.newArrayList(jsonObject));
        }
        return null;
    }


    public WxArticleDTO convertWxArticleFromMap(JSONObject reulstMap) {
        if (MapUtils.isEmpty(reulstMap)) {
            return null;
        }
        try {
            WxArticleDTO wxArticleDTO = new WxArticleDTO();

            wxArticleDTO.setId(MapUtils.getLong(reulstMap, "id"));
            wxArticleDTO.setTitle(MapUtils.getString(reulstMap, "title"));
            wxArticleDTO.setArticleId(MapUtils.getString(reulstMap, "article_id"));
            wxArticleDTO.setAuthor(MapUtils.getString(reulstMap, "author"));
            wxArticleDTO.setUrl(MapUtils.getString(reulstMap, "url"));
            wxArticleDTO.setContentSourceUrl(MapUtils.getString(reulstMap, "content_source_url"));
            wxArticleDTO.setDigest(MapUtils.getString(reulstMap, "digest"));

            return wxArticleDTO;
        } catch (Exception e) {
            PlatformLogUtil.logException("convertWxArticleFromMap，执行异常", e.getMessage(), e, LogListUtil.newArrayList(reulstMap));
        }
        return null;
    }

    public WxCommunityTagDTO convertTagFromMap(JSONObject jsonObject) {
        WxCommunityTagDTO wxCommunityTagDTO = new WxCommunityTagDTO();
        wxCommunityTagDTO.setId(MapUtils.getLong(jsonObject, "id"));
        wxCommunityTagDTO.setName(MapUtils.getString(jsonObject, "name"));

        return wxCommunityTagDTO;
    }
}
