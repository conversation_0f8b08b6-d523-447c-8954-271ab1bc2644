package com.alibaba.tripscrm.service.middleware.metaq.consumer.system;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.model.domain.request.RiskInvokeRecordCreateParam;
import com.alibaba.tripscrm.service.service.risk.RiskPanelService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 风控埋点记录 metaQ消费者
 *
 * <AUTHOR>
 * @date 2024/07/02
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service(value = "riskInvokeRecordConsumer")
public class RiskInvokeRecordConsumer implements MessageListenerConcurrently {
    @Resource
    private RiskPanelService riskPanelService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            String msgBody = new String(msg.getBody());
            RiskInvokeRecordCreateParam createParam = JSONObject.parseObject(msgBody, RiskInvokeRecordCreateParam.class);
            riskPanelService.createRecord(createParam);
            PlatformLogUtil.logInfo("风控记录-消费风控埋点", LogListUtil.newArrayList(msgBody));
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
