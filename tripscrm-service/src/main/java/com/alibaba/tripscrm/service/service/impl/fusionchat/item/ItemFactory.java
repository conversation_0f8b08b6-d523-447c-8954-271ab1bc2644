package com.alibaba.tripscrm.service.service.impl.fusionchat.item;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.enums.material.PageTypeEnum;
import com.alibaba.tripscrm.service.model.domain.fusionchat.dto.ItemSearchDTO;
import com.alibaba.tripscrm.service.model.domain.query.ItemQuery;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.service.fusionchat.item.ItemService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/6/28 18:05
 **/
@Component
@AllArgsConstructor
public class ItemFactory<T>{

    private final List<ItemService<T>> itemServiceList;

    private final Map<ItemTypeEnum, ItemService<T>> itemServiceMap;

    @PostConstruct
    void init() {
        for (ItemService<T> itemService : itemServiceList) {
            itemServiceMap.put(itemService.getType(), itemService);
        }
    }

    /**
     * 获取物料处理服务
     * @param itemTypeEnum 物料类型
     * @return 物料服务
     */
    private ItemService<T> getItemService(ItemTypeEnum itemTypeEnum) {
        if (itemTypeEnum == null) {
            return null;
        }
        return itemServiceMap.get(itemTypeEnum);
    }

    /**
     * 查询物料列表
     * @param query 查询条件
     * @return 物料列表
     */
    public ItemSearchDTO<T> queryItemList(ItemQuery query) {
        if (query == null || query.getItemTypeEnum() == null) {
            PlatformLogUtil.logFail("查询物料列表参数缺失", LogListUtil.newArrayList(query));
            return null;
        }
        ItemService<T> itemService = getItemService(query.getItemTypeEnum());
        if (itemService == null) {
            PlatformLogUtil.logFail("查询物料列表找不到对应的服务", LogListUtil.newArrayList(query));
            return null;
        }
        return itemService.queryItem(query);
    }

    /**
     * 获取物料访问链接
     * @param pageTypeEnum 页面类型枚举
     * @param targetTypeEnum 目标链接类型
     * @param itemId 物料ID
     * @return 物料访问链接
     */
    public String queryItemLink(PageTypeEnum pageTypeEnum, LinkTypeEnum targetTypeEnum, String itemId, MaterialTrackRelationDTO trackRelationDTO) {
        if (pageTypeEnum == null || StringUtils.isBlank(itemId)) {
            PlatformLogUtil.logFail("获取物料访问链接参数缺失", LogListUtil.newArrayList(pageTypeEnum, itemId));
            return null;
        }
        ItemService<T> itemService = getItemService(pageTypeEnum.getItemTypeEnum());
        if (itemService == null) {
            PlatformLogUtil.logFail("获取物料访问链接找不到对应的服务", LogListUtil.newArrayList(pageTypeEnum, itemId));
            return null;
        }
        return itemService.queryLink(pageTypeEnum.getItemTypeEnum(), itemId, targetTypeEnum, false, trackRelationDTO);
    }

}
