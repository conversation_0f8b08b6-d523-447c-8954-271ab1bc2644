package com.alibaba.tripscrm.service.constant;

/**
 * <AUTHOR>
 * @Date 2024/3/11 23:50
 **/
public class OdpsSqlConstant {

    /**
     * 去重客户数量
     */
    public static final String QUERY_MOMENT_TASK_CUSTOMER_COUNT = "SELECT COUNT(DISTINCT(external_user_id)) FROM trip_ods.s_customer_relation_tripscrm_app WHERE ds = MAX_PT('trip_ods.s_customer_relation_tripscrm_app') AND corp_id = '%s' AND user_id IN (%s) AND status = 1;";
    /**
     * 标签覆盖的客户
     */
    public static final String TAG_COVER_CUSTOMER_INFO = "SELECT union_id, user_id, external_user_id, COLLECT_SET(follow_user_id)[CAST(rand(UNIX_TIMESTAMP()) * COUNT(*) AS BIGINT)] AS follow_user_id, COALESCE(scrm_tag_ids,'') AS tag_ids, COALESCE(tag_name, '') AS wechat_tag_names FROM alitrip_wireless.scrm_wechat_customer_relation WHERE ds = MAX_PT('alitrip_wireless.scrm_wechat_customer_relation') AND corp_id = '%s' AND SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(scrm_tag_ids,''),','),ARRAY(%s))) > 0 GROUP BY union_id, user_id, COALESCE(scrm_tag_ids,''),COALESCE(tag_name, ''), external_user_id;";
    /**
     * 标签覆盖的群聊
     */
    public static final String TAG_COVER_GROUP_INFO = "SELECT chat_id, corp_id, owner_user, admin_user, tags, notice FROM trip_ods.trip_scrm_wechat_group_hh WHERE ds = MAX_PT('trip_ods.trip_scrm_wechat_group_hh') AND corp_id = '%s' AND SIZE(ARRAY_INTERSECT(SPLIT(tags,','),ARRAY(%s))) > 0;";

}
