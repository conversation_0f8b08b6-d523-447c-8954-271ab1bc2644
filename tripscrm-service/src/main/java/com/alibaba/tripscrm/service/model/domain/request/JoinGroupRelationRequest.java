package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/6 10:43
 **/
@Data
public class JoinGroupRelationRequest {

    /**
     * 添加方式
     */
    private Byte way;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 活动id
     */
    private String activityId;

    /**
     * 新增的群聊id列表
     */
    private List<String> addTargetIdList;

    /**
     * 移除的关系id列表
     */
    private List<Long> removeIdList;

    /**
     * 移除的群聊id列表
     */
    private List<String> removeTargetIdList;

    /**
     * state
     */
    private String state;


}
