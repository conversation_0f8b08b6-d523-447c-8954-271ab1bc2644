package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.domain.task.TaskAsyncExecuteResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.ScrmConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.request.SendMsgToGroupRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import com.taobao.unifiedsession.core.commons.limiter.RateLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupChatTaskExecutor extends AbstractTaskExecutor {
    private final EnterpriseWechatManager enterpriseWechatManager;
    private final WechatUserService wechatUserService;
    private final TaskExecuteRecordService taskExecuteRecordService;
    private final LdbTairManager ldbTairManager;
    private final IsvRouteService isvRouteService;
    private final MetaqProducer metaqProducer;

    @Switch(description = "每分钟最大消息触达数", name = "sendMessageCountPerMinute")
    public static Integer sendMessageCountPerMinute = 60;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String chatId = getFinalTargetId(context, taskDataBody);

        // 素材
        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("material get fail", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MATERIAL_IN_TASK);
        }

        String userId = getSendUserId(context, todoTaskVO);
        SendMsgToGroupRequest sendMsgToGroupRequest = buildSendMsgToGroupRequest(context, todoTaskVO, userId, chatId, materailInfoDO);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> enterpriseWechatManager.asyncSendMessageToGroup(sendMsgToGroupRequest));
        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        JSONObject data = new JSONObject();
        data.put("chatId", chatId);
        data.put("wechatUserId", userId);
        data.put("extraInfo", context.getExtInfo());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    private SendMsgToGroupRequest buildSendMsgToGroupRequest(TaskExecuteContext context, TodoTaskVO todoTaskVO, String userId, String chatId, MaterailInfoDO materailInfoDO) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.QUNLIAO_RW);
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setWxUserId(userId);
        materialTrackRelationDTO.setSendChatId(chatId);

        SendMsgToGroupRequest sendMsgToGroupRequest = new SendMsgToGroupRequest();
        // 指定requestId
        if (Objects.nonNull(context.getExtInfo()) && context.getExtInfo().containsKey("requestId")
                && Objects.nonNull(context.getExtInfo().get("requestId"))
                && context.getExtInfo().get("requestId") instanceof String) {
            sendMsgToGroupRequest.setRequestId(context.getExtInfo().get("requestId").toString());
        }

        sendMsgToGroupRequest.setChatId(chatId);
        sendMsgToGroupRequest.setUserId(userId);
        sendMsgToGroupRequest.setMainTaskInstanceId(todoTaskVO.getMainTaskInstanceId());
        sendMsgToGroupRequest.setAbTestBucketId(getAbTestBucketId(todoTaskVO));
        sendMsgToGroupRequest.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        sendMsgToGroupRequest.setMaterailInfoDO(materailInfoDO);
        sendMsgToGroupRequest.setTaskType(getTaskType());

        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setWechatUserId(userId);
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        if (StringUtils.isNotBlank(todoTaskVO.getData().get(0).getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo()));
        }
        materialContentConvertContext.setImageUrlList(context.getImageUrlList());
        materialContentConvertContext.setOriginContent(materailInfoDO.getContent());
        sendMsgToGroupRequest.setMaterialContentConvertContext(materialContentConvertContext);
        return sendMsgToGroupRequest;
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            return (String) taskDataBody.getContext().get("sendUserId");
        }

        // 先取事件源或接口指定的发送者id
        Map<String, Object> extInfo = context.getExtInfo();
        if (extInfo != null && extInfo.containsKey("sendUserId")) {
            String sendUserId = (String) extInfo.get("sendUserId");
            PlatformLogUtil.logInfo("群聊消息发送过程中从上下文扩展获取sendUserId", LogListUtil.newArrayList(taskDataBody, sendUserId));
            checkSendUserOnline(taskDataBody, sendUserId);
            return sendUserId;
        }

        // 指定了发送者，需要判断发送者在不在线
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        if (extInfoJson.containsKey("sendUserId")) {
            String sendUserId = extInfoJson.getString("sendUserId");
            PlatformLogUtil.logInfo("群聊消息发送过程中从任务配置中获取sendUserId", LogListUtil.newArrayList(taskDataBody, sendUserId));
            checkSendUserOnline(taskDataBody, sendUserId);
            return sendUserId;
        }

        String chatId = getFinalTargetId(context, taskDataBody);
        // 根据群账号选取策略获取发送人
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        isvRouteContext.setRiskActionEnum(RiskActionEnum.GROUP_SEND_MESSAGE);
        isvRouteContext.setChatId(chatId);
        isvRouteContext.addExtraInfo(ScrmConstant.NEED_OWNER_OR_ADMIN_PERMISSION, true);
        TripSCRMResult<WechatUserDTO> isvRouteResult = isvRouteService.matchWechatUser(isvRouteContext);
        if (!isvRouteResult.isSuccess() || Objects.isNull(isvRouteResult.getData()) || StringUtils.isBlank(isvRouteResult.getData().getUserId())) {
            PlatformLogUtil.logFail("群聊任务获取发送人失败，根据群账号选取策略获取发送人失败", LogListUtil.newArrayList(chatId, taskDataBody, context));
            throw new TripscrmException(TripSCRMErrorCode.MATCH_WECHAT_USER_BY_ISV_ROUTE_STRATEGY_FAIL);
        }
        String userId = isvRouteResult.getData().getUserId();
        taskDataBody.getContext().put("sendUserId", userId);
        return isvRouteResult.getData().getUserId();
    }

    private void checkSendUserOnline(TaskDataVO.DataBodyVO taskDataBody, String sendUserId) {
        // 判断指定的发送者id是否在线
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(sendUserId));
        if (CollectionUtils.isNotEmpty(wechatUserList) && Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
            return;
        }

        PlatformLogUtil.logFail("群聊消息发送过程中指定的发送者id不在线", LogListUtil.newArrayList(taskDataBody, sendUserId));
        throw new TripscrmException(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE);
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_CHAT_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (taskDataBody.getContext().containsKey("chatId")) {
            return (String) taskDataBody.getContext().get("chatId");
        }

        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("empty targetId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!Lists.newArrayList(ActivityTargetTypeEnum.WX_CHAT_ID).contains(activityTargetType)) {
            PlatformLogUtil.logFail("only accept chatId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        String chatId = taskDataBody.getTargetId();
        taskDataBody.getContext().put("chatId", chatId);
        return chatId;
    }

    @Override
    public RateLimiter getRateLimiter(TaskExecuteContext context) {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        if (isStreamTask) {
            PlatformLogUtil.logFail("isStreamTask");
            return null;
        }

        double qps = sendMessageCountPerMinute / 60d;
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(qps));
        return RateLimiter.create(sendMessageCountPerMinute / 60d);
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.GROUP_CHAT_MESSAGE;
    }

    @Override
    public boolean supportRetry(TripSCRMErrorCode errorCodeEnum) {
        return Lists.newArrayList(TripSCRMErrorCode.BIZ_RATE_LIMIT, TripSCRMErrorCode.BIZ_ACTION_DEGRADE_LIMIT, TripSCRMErrorCode.GATE_WAY_RATE_LIMIT, TripSCRMErrorCode.ISV_RATE_LIMIT).contains(errorCodeEnum);
    }

    @Override
    public void updateAsyncExecuteResult(Long recordId, ScrmCallbackMsg scrmCallbackMsg) {
        sendTaskAsyncExecuteResult(scrmCallbackMsg);
        TaskExecuteRecordBO taskExecuteRecordBO = taskExecuteRecordService.queryByRecordId(recordId);
        if (Objects.isNull(taskExecuteRecordBO)) {
            PlatformLogUtil.logFail("任务执行明细，更新群聊任务异步执行结果，查询任务执行明细为空", LogListUtil.newArrayList(recordId));
            return;
        }

        boolean firstCallback = Lists.newArrayList(TaskSingleDataExecuteResultEnum.SUCCESS).contains(taskExecuteRecordBO.getStatusEnum());

        // 第一个回调，作为异步执行结果
        if (firstCallback) {
            taskExecuteRecordBO.setStatusEnum(scrmCallbackMsg.getResult() ? TaskSingleDataExecuteResultEnum.ASYNC_SUCCESS : TaskSingleDataExecuteResultEnum.ASYNC_FAIL);
            PlatformLogUtil.logInfo("任务执行明细，更新群聊任务异步执行结果，首次更新异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        }

        JSONObject extraInfo = taskExecuteRecordBO.getExtraInfo();
        List<String> asyncExecuteResultList = extraInfo.containsKey("asyncExecuteResult") ? extraInfo.getObject("asyncExecuteResult", new TypeReference<List<String>>() {
        }) : new ArrayList<>();
        if (StringUtils.isNotBlank(scrmCallbackMsg.getContent())) {
            asyncExecuteResultList.add(scrmCallbackMsg.getContent());
            extraInfo.put("asyncExecuteResultList", asyncExecuteResultList);
        }
        if (StringUtils.isNotBlank(scrmCallbackMsg.getMessage())) {
            extraInfo.put("asyncExecuteResultMessage", scrmCallbackMsg.getMessage());
        }
        taskExecuteRecordBO.setExtraInfo(extraInfo);

        PlatformLogUtil.logInfo("任务执行明细，更新群聊任务异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        if (firstCallback && NumberUtils.validLong(taskExecuteRecordBO.getSubTaskInstanceId())) {
            String key = scrmCallbackMsg.getResult() ? (TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_SUCCESS_COUNT_PREFIX + taskExecuteRecordBO.getSubTaskInstanceId()) : (TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_FAIL_COUNT_PREFIX + taskExecuteRecordBO.getSubTaskInstanceId());
            ldbTairManager.incr(key, 1, 0, 86400);
        }
        taskExecuteRecordService.upsert(taskExecuteRecordBO);
    }

    private void sendTaskAsyncExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        // 发送异步执行结果
        TaskAsyncExecuteResult taskAsyncExecuteResult = new TaskAsyncExecuteResult();
        taskAsyncExecuteResult.setRequestId(scrmCallbackMsg.getRequestId());
        taskAsyncExecuteResult.setResult(scrmCallbackMsg.getResult());
        taskAsyncExecuteResult.setMessage(scrmCallbackMsg.getMessage());
        JSONObject extraInfo = new JSONObject();
        JSONObject jsonContent = JSONObject.parseObject(scrmCallbackMsg.getContent());
        if (Objects.nonNull(jsonContent)) {
            extraInfo.put("msgNum", MapUtils.getInteger(jsonContent, "msgNum", null));
        }
        taskAsyncExecuteResult.setExtraInfo(extraInfo);
        metaqProducer.send(MQEnum.TASK_ASYNC_EXECUTE_RESULT, null, scrmCallbackMsg.getPlatformCorpId(), JSONObject.toJSONString(taskAsyncExecuteResult));
    }
}
