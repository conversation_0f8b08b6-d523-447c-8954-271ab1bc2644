package com.alibaba.tripscrm.service.service.impl.fusionchat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatCustomerDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.dal.model.domain.result.GroupMemberCountResult;
import com.alibaba.tripscrm.dal.repository.CustomerRelationRepository;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.dal.repository.WechatCustomerRepository;
import com.alibaba.tripscrm.dal.repository.WechatUserRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.WxCommunityContentDTO;
import com.alibaba.tripscrm.domain.WxCommunityTagDTO;
import com.alibaba.tripscrm.domain.request.WxCommunityContentRequest;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatReceiveTypeEnum;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.task.UserRelationStatusEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.MemberTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.WechatUserOpenSearchManager;
import com.alibaba.tripscrm.service.manager.second.MemberManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.fusionchat.AccountFusionChatConfigBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.dto.ItemSearchDTO;
import com.alibaba.tripscrm.service.model.domain.fusionchat.vo.ItemSearchVO;
import com.alibaba.tripscrm.service.model.domain.fusionchat.vo.ItemVO;
import com.alibaba.tripscrm.service.model.domain.query.ItemQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerListQuery;
import com.alibaba.tripscrm.service.model.domain.request.GetItemListRequest;
import com.alibaba.tripscrm.service.model.domain.request.MaterialToMessageRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.fusionchat.FusionChatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.fusionchat.FusionChatGroupVO;
import com.alibaba.tripscrm.service.model.vo.wechat.*;
import com.alibaba.tripscrm.service.service.fusionchat.AccountConfigService;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.fusionchat.FusionChatService;
import com.alibaba.tripscrm.service.service.fusionchat.ProfileService;
import com.alibaba.tripscrm.service.service.impl.fusionchat.item.ItemFactory;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.ManagerWechatUser;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.GetUserConversationListProcessor;
import com.alibaba.tripscrm.service.service.wechat.GroupRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripscrm.service.util.system.BeanCopyUtils;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripscrm.service.wechat.WxCommunityContentService;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WechatGroupCancelWorkRequest;
import com.alibaba.tripzoo.proxy.request.WechatGroupOpenWorkRequest;
import com.alibaba.tripzoo.proxy.request.WechatGroupWorkListRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台账号配置 Manager实现类
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class FusionChatServiceImpl implements FusionChatService {
    private final WechatUserService wechatUserService;
    private final WechatUserOpenSearchManager wechatUserOpenSearchManager;
    private final AccountConfigService accountConfigService;
    private final WechatCustomerRepository wechatCustomerRepository;
    private final WechatUserRepository wechatUserRepository;
    private final GroupRelationService groupRelationService;
    private final GroupRelationRepository groupRelationRepository;
    private final WechatGroupService wechatGroupService;
    private final GroupService groupService;
    private final GetUserConversationListProcessor getUserConversationListProcessor;
    private final ChatConversationService chatConversationService;
    private final UicUtils uicUtils;
    private final WechatCustomerService wechatCustomerService;
    private final MemberManager memberManager;
    private final ProfileService profileService;
    private final CustomerRelationRepository customerRelationRepository;
    private final LdbTairManager ldbTairManager;
    private final WxCommunityContentService wxCommunityContentService;
    private final MaterialService materialService;
    private final ItemFactory<ItemVO> itemFactory;

    @Override
    public List<WechatUserDTO> listManagerWechatUserDTOList(String accountId, Long spaceId) {
        List<String> userIds = wechatUserService.listUserIdWithPermission(accountId, spaceId);
        if (userIds.isEmpty()) {
            return Collections.emptyList();
        }
        WechatUserQuery wechatUserQuery = new WechatUserQuery();
        wechatUserQuery.setUserIdList(userIds);
        wechatUserQuery.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wechatUserQuery.setQueryLockUser(true);
        return wechatUserService.listByCondition(wechatUserQuery);
    }

    @Override
    public ManagerWechatUser getManagerUser(String userId, String accountId, Long spaceId) {
        if (!wechatUserService.checkHasManagerPermission(userId, accountId, spaceId)) {
            return null;
        }

        WechatUserQuery wechatUserQuery = new WechatUserQuery();
        wechatUserQuery.setUserIdList(Lists.newArrayList(userId));
        wechatUserQuery.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wechatUserQuery.setQueryLockUser(true);
        List<WechatUserDTO> wechatUserDTOS = wechatUserService.listByCondition(wechatUserQuery);
        if (CollectionUtils.isEmpty(wechatUserDTOS)) {
            return null;
        }

        ManagerWechatUser managerWechatUser = BeanCopyUtils.copy(wechatUserDTOS.get(0), WechatUserDTO.class, ManagerWechatUser.class);
        managerWechatUser.setManagerUserList(Lists.newArrayList(wechatUserDTOS.get(0).getMemberList(), wechatUserDTOS.get(0).getAdminList()).stream().flatMap(List::stream).distinct().collect(Collectors.toList()));
        return managerWechatUser;
    }

    @Override
    public List<ManagerWechatUser> listManagerUsers(String accountId, Long spaceId) {
        List<ManagerWechatUser> result = new ArrayList<>();
        List<WechatUserDTO> managerUsers = listManagerWechatUserDTOList(accountId, spaceId);
        Map<String, AccountFusionChatConfigBody> fusionChatMap = accountConfigService.queryFusionChatConfig(accountId);
        for (WechatUserDTO wechatUser : managerUsers) {
            ManagerWechatUser one = BeanCopyUtils.copy(wechatUser, WechatUserDTO.class, ManagerWechatUser.class);
            AccountFusionChatConfigBody body = fusionChatMap.get(wechatUser.getUserId());
            // 设置置顶配置
            one.setTopNo(body == null ? 0 : body.getTopNo());
            // 设置锁定平台账号
            one.setLockUser(wechatUser.getLockUser());
            // 设置未读数
            one.setUnreadCount(chatConversationService.getWechatUserUnreadCount(wechatUser.getCorpId(), wechatUser.getUserId()));
            result.add(one);
        }
        return result;
    }

    @Override
    public List<ManagerWechatUser> listLockUsers(String accountId, Long spaceId) {
        List<ManagerWechatUser> managerUsers = listManagerUsers(accountId, spaceId);
        return managerUsers.stream().filter(x -> accountId.equals(x.getLockUser())).collect(Collectors.toList());
    }

    @Override
    public void checkGroupName(String userId, String groupName) {
        List<GroupRelationDO> groupRelations = groupRelationRepository.listByShardingKeyAndName(groupRelationRepository.userId2ShardingKey(userId), groupName, SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (!groupRelations.isEmpty()) {
            throw new TripscrmException("群聊名称已存在");
        }
    }

    @Override
    public void lockUser(User account, Long spaceId, String userId, Boolean lock) {
        try {
            if (lock) {
                wechatUserService.lock(account, userId, account.getUserId(), spaceId, true);
            } else {
                wechatUserService.unlock(account, userId, account.getUserId(), spaceId, true);
                chatConversationService.cleanUserDoingChat(account, userId);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("修改企微账号锁定状态异常", e.getMessage(), e, LogListUtil.newArrayList(lock));
            throw new TripscrmException(e.getMessage());
        }
    }

    @Override
    public WechatGroupInfoVO getGroupChatInfo(String chatId, String searchKey, String userId) {
        WechatGroupInfoVO result = new WechatGroupInfoVO();
        List<WechatGroupInfoMemberVO> members = groupRelationService.listByChatId(chatId, SpaceInfoThreadLocalUtils.getCorpId());
        result.setMemberCount(members.size());
        if (!StringUtils.isBlank(searchKey)) {
            members = members.stream().filter(x -> x.getName().contains(searchKey)).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(userId)) {
            members = members.stream().map(wechatGroupInfoMemberVO -> {
                wechatGroupInfoMemberVO.setStatus(getCustomerRelationStatus(userId, wechatGroupInfoMemberVO.getUserId()));
                return wechatGroupInfoMemberVO;
            }).collect(Collectors.toList());
        }
        List<WechatGroupInfoMemberGroupVO> memberGroups = new ArrayList<>();
        Map<String, List<WechatGroupInfoMemberVO>> memberGroupMap = members.stream().collect(Collectors.groupingBy(WechatGroupInfoMemberVO::getCorpName));
        for (Map.Entry<String, List<WechatGroupInfoMemberVO>> entry : memberGroupMap.entrySet()) {
            WechatGroupInfoMemberGroupVO memberGroup = new WechatGroupInfoMemberGroupVO();
            memberGroup.setCorpName(entry.getKey());
            memberGroup.setMembers(entry.getValue());
            memberGroups.add(memberGroup);
        }
        result.setMemberGroup(memberGroups);
        return result;
    }

    @Override
    public WechatSingleInfoVO getSingleChatInfo(String chatId, Integer chatType) {
        WechatSingleInfoVO result = new WechatSingleInfoVO();
        String corpId = SpaceInfoThreadLocalUtils.getCorpId();
        if (chatType.equals(ChatTypeEnum.SINGLE_FOR_CUSTOMER.getValue())) {
            WechatCustomerDO wechatCustomer = wechatCustomerRepository.getByExternalUserIdAndCorpId(chatId, corpId);
            result.setName(wechatCustomer.getName());
            result.setAvatar(wechatCustomer.getAvatar());
            result.setUserId(chatId);
            result.setUserType(GroupUserTypeEnum.CUSTOMER.getCode().byteValue());
            result.setCorpName(wechatCustomer.getCorpName());
        }
        if (chatType.equals(ChatTypeEnum.SINGLE_FOR_USER.getValue())) {
            WechatUserDO wechatUser = wechatUserRepository.getByUserIdAndCorpId(chatId, corpId);
            result.setName(wechatUser.getName());
            result.setAvatar(wechatUser.getAvatar());
            result.setUserId(chatId);
            result.setUserType(GroupUserTypeEnum.USER.getCode().byteValue());
        }
        return result;
    }

    /**
     * 获取当前企微账号所在的群聊列表（返回关注的标记）
     *
     * @param userId         userId
     * @param searchKey      searchKey
     * @param externalUserId externalUserId
     * @return return
     */
    @Override
    public List<WechatGroupWithWorkVO> listGroupByUserId(String userId, String searchKey, String externalUserId) {
        List<WechatGroupVO> wechatGroups = wechatGroupService.listByUserId(userId);
        List<WechatGroupWithWorkVO> resultList = new ArrayList<>();
        if (!StringUtils.isBlank(searchKey)) {
            wechatGroups = wechatGroups.stream().filter(x -> x.getName().contains(searchKey)).collect(Collectors.toList());
        }
        for (WechatGroupVO wechatGroup : wechatGroups) {
            WechatGroupWithWorkVO wechatGroupWithWork = BeanCopyUtils.copy(wechatGroup, WechatGroupVO.class, WechatGroupWithWorkVO.class);
            if (StringUtils.isBlank(wechatGroupWithWork.getName())) {
                wechatGroupWithWork.setName("未命名群聊");
            }
            if (StringUtils.isNotBlank(externalUserId)) {
                wechatGroupWithWork.setStatus(getUserInGroupStatus(wechatGroup.getChatId(), externalUserId));
            }
            List<GroupMemberCountResult> groupMemberCountResults = groupRelationRepository.countByShardingKeyList(Lists.newArrayList(groupRelationRepository.chatId2ShardingKey(wechatGroup.getChatId())), Lists.newArrayList(MemberTypeEnum.EnterpriseMember.getType(), MemberTypeEnum.ExternalContact.getType()), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
            wechatGroupWithWork.setUserCount(groupMemberCountResults.get(0).getCount());
            wechatGroupWithWork.setWork(false);
            resultList.add(wechatGroupWithWork);
        }
        // 获取关注的群聊列表
        WechatGroupWorkListRequest wechatGroupWorkListRequest = new WechatGroupWorkListRequest();
        wechatGroupWorkListRequest.setUserId(userId);
        wechatGroupWorkListRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<List<String>> result = groupService.listWorkGroupId(wechatGroupWorkListRequest);
        if (result.getSuccess()) {
            PlatformLogUtil.logFail("获取关注群聊列表成功", LogListUtil.newArrayList(wechatGroupWorkListRequest, result, userId));

            if (result.getModel() != null) {
                for (WechatGroupWithWorkVO wechatGroupWithWork : resultList) {
                    wechatGroupWithWork.setWork(result.getModel().contains(wechatGroupWithWork.getChatId()));
                }
            }
        } else {
            PlatformLogUtil.logFail("获取关注群聊列表失败", LogListUtil.newArrayList(wechatGroupWorkListRequest, result, userId));
        }

        return resultList.stream().sorted(Comparator.comparing(WechatGroupWithWorkVO::getCreateTime).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取用户在群状态
     *
     * @param chatId 群聊ID
     * @param userId 用户ID
     */
    private Integer getUserInGroupStatus(String chatId, String userId) {
        List<GroupRelationDO> relations = groupRelationRepository.listByShardingKey(groupRelationRepository.chatId2ShardingKey(chatId), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        List<GroupRelationDO> existList = relations.stream().filter(relationDO -> userId.equals(relationDO.getUserId())).collect(Collectors.toList());
        Object o = null;
        if (SwitchConfig.CACHE_IS_OPEN) {
            o = ldbTairManager.get(TairConstant.APPLY_TO_JOIN_GROUP_PREFIX + chatId + "_" + userId);
        }

        if (CollectionUtils.isNotEmpty(existList)) {
            return UserRelationStatusEnum.ADDED.getCode();
        }

        if (Objects.nonNull(o)) {
            return UserRelationStatusEnum.TO_BE_ACCEPTED.getCode();
        }

        return UserRelationStatusEnum.NOT_ADDED.getCode();
    }

    @Override
    public void openWorkGroup(String userId, String chatId) {
        // 校验关注群聊是否已达上线
        WechatGroupWorkListRequest wechatGroupWorkListRequest = new WechatGroupWorkListRequest();
        wechatGroupWorkListRequest.setUserId(userId);
        wechatGroupWorkListRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<List<String>> workResult = groupService.listWorkGroupId(wechatGroupWorkListRequest);
        if (workResult.getSuccess()) {
            PlatformLogUtil.logFail("获取关注群聊列表成功", LogListUtil.newArrayList(wechatGroupWorkListRequest, workResult, userId));

            if (workResult.getModel().size() >= 30) {
                PlatformLogUtil.logFail("关注群聊数量已到上限", LogListUtil.newArrayList(wechatGroupWorkListRequest, workResult, userId));
                throw new TripscrmException("关注群聊失败，关注群聊已达上限30个，请先取消关注部分群聊再来关注");
            }
        } else {
            PlatformLogUtil.logFail("获取关注群聊失败", LogListUtil.newArrayList(wechatGroupWorkListRequest, workResult, userId));
        }
        WechatGroupOpenWorkRequest wechatGroupOpenWorkRequest = new WechatGroupOpenWorkRequest();
        wechatGroupOpenWorkRequest.setUserId(userId);
        wechatGroupOpenWorkRequest.setChatId(chatId);
        wechatGroupOpenWorkRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<Void> result = groupService.openWorkGroup(wechatGroupOpenWorkRequest);
        if (!result.getSuccess()) {
            PlatformLogUtil.logFail("关注群聊失败", LogListUtil.newArrayList(wechatGroupOpenWorkRequest, result, userId));

            if (result.getResultMessage().contains("群聊信息不存在")) {
                throw new TripscrmException("关注群聊失败：请先在客户端内将此群保存到通讯录，并于1小时后再来关注");
            }

            if (result.getResultMessage().contains("机器人不在群内")) {
                throw new TripscrmException("关注群聊失败：请先在客户端群聊内收到至少1条消息，再来关注");
            }

            throw new TripscrmException("关注群聊失败：" + result.getResultMessage());
        }

        PlatformLogUtil.logFail("关注群聊成功", LogListUtil.newArrayList(wechatGroupOpenWorkRequest, result, userId));

        // 推送到websocket
        getUserConversationListProcessor.pushMessageByDistributed(userId);
    }

    @Override
    public void cancelWorkGroup(String userId, String chatId) {
        WechatGroupCancelWorkRequest wechatGroupCancelWorkRequest = new WechatGroupCancelWorkRequest();
        wechatGroupCancelWorkRequest.setUserId(userId);
        wechatGroupCancelWorkRequest.setChatId(chatId);
        wechatGroupCancelWorkRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<Void> result = groupService.cancelWorkGroup(wechatGroupCancelWorkRequest);
        if (!result.getSuccess()) {
            PlatformLogUtil.logFail("取消关注群聊失败", LogListUtil.newArrayList(wechatGroupCancelWorkRequest, result, userId));
            throw new TripscrmException("取消关注群聊失败：" + result.getResultMessage());
        }

        PlatformLogUtil.logFail("取消关注群聊成功", LogListUtil.newArrayList(wechatGroupCancelWorkRequest, result, userId));
        // 推送到websocket
        getUserConversationListProcessor.pushMessageByDistributed(userId);
    }

    @Override
    public FusionChatCustomerVO getCustomerInfo(String userId, String externalUserId) {
        FusionChatCustomerVO fusionChatCustomerVO = new FusionChatCustomerVO();
        // 微信信息
        fillUserBaseInfo(fusionChatCustomerVO, userId, externalUserId);
        // 飞猪域信息
        List<WechatCustomerVO> wechatCustomerVOS = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(externalUserId));
        if (CollectionUtils.isNotEmpty(wechatCustomerVOS)) {
            fillUserFliggyInfo(fusionChatCustomerVO, uicUtils.getUidByUnionId(wechatCustomerVOS.get(0).getUnionId()));
        }
        // 群聊信息
        fillUserChatInfo(fusionChatCustomerVO, externalUserId);
        return fusionChatCustomerVO;
    }

    /**
     * 补充用户基本信息
     *
     * @param fusionChatCustomerVO 用户信息
     * @param userId               员工id
     * @param externalUserId       客户id
     */
    private void fillUserBaseInfo(FusionChatCustomerVO fusionChatCustomerVO, String userId, String externalUserId) {
        WechatUserDTO inviteUserInfo = getInviteUserInfo(userId, externalUserId);
        // 填充关系
        fusionChatCustomerVO.setStatus(getCustomerRelationStatus(userId, externalUserId));
        fusionChatCustomerVO.setInviteUser(Objects.isNull(inviteUserInfo) ? null : inviteUserInfo.getName());
        // 客户信息查询
        WechatCustomerVO wechatCustomer = queryCustomerInfo(userId, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(), externalUserId);
        if (Objects.isNull(wechatCustomer)) {
            WechatCustomerVO wechatCustomerVO = queryCustomerInfo(null, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(), externalUserId);
            fusionChatCustomerVO.setWxNick(Objects.nonNull(wechatCustomerVO) ? wechatCustomerVO.getName() : null);
            return;
        }
        // 填充结果集
        fusionChatCustomerVO.setWxNick(wechatCustomer.getName());
        String remark = CollectionUtils.isEmpty(wechatCustomer.getFollowUserList()) ? null : wechatCustomer.getFollowUserList().get(0).getRemark();
        fusionChatCustomerVO.setRemark(remark);
        if (CollectionUtils.isNotEmpty(wechatCustomer.getTagList())) {
            fusionChatCustomerVO.setTags(wechatCustomer.getTagList());
        }
    }

    /**
     * 查询用户信息
     *
     * @param userId        企微成员Id
     * @param spaceId       空间Id
     * @param externalUsrId 外部拓展用户Id
     * @return WechatCustomerVO
     */
    private WechatCustomerVO queryCustomerInfo(String userId, Long spaceId, String externalUsrId) {
        WechatCustomerListQuery query = new WechatCustomerListQuery();
        if (!StringUtils.isBlank(userId)) {
            query.setUserId(userId);
        }
        if (Objects.nonNull(spaceId)) {
            query.setSpaceId(spaceId);
        }
        if (!StringUtils.isBlank(externalUsrId)) {
            query.setExternalUserId(externalUsrId);
        }
        query.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode()));
        PageInfoDTO<WechatCustomerVO> wechatCustomerVOPageInfoDTO = wechatCustomerService.listPageInfo(query);
        if (Objects.isNull(wechatCustomerVOPageInfoDTO) || CollectionUtils.isEmpty(wechatCustomerVOPageInfoDTO.getList())) {
            return null;
        }
        return wechatCustomerVOPageInfoDTO.getList().get(0);
    }

    /**
     * 获取客户的关系壮体啊
     * @param userId 用户id
     * @param externalUserId 外部用户id
     * @return 状态值
     */
    private Integer getCustomerRelationStatus(String userId, String externalUserId) {
        List<CustomerRelationDO> customerRelationDOS = customerRelationRepository.listByExternalUserIds(userId, Lists.newArrayList(externalUserId), SpaceInfoThreadLocalUtils.getCorpId());
        Object o = null;
        if (SwitchConfig.CACHE_IS_OPEN) {
            o = ldbTairManager.get(TairConstant.APPLY_TO_BE_FRIEND_PREFIX + userId + "_" + externalUserId);

        }
        if (!CollectionUtils.isEmpty(customerRelationDOS) && CustomerRelationStatusEnum.FRIEND.getCode().equals(customerRelationDOS.get(0).getStatus().intValue())) {
            return UserRelationStatusEnum.ADDED.getCode();
        } else if (Objects.nonNull(o)){
            return UserRelationStatusEnum.TO_BE_ACCEPTED.getCode();
        } else {
            return UserRelationStatusEnum.NOT_ADDED.getCode();
        }
    }

    /**
     * 获取邀请人信息
     * @param userId 企微成员Id
     * @param externalUserId 外部拓展用户Id
     * @return 邀请人信息
     */
    private WechatUserDTO getInviteUserInfo(String userId, String externalUserId) {
        Object o = ldbTairManager.get(TairConstant.APPLY_TO_BE_FRIEND_PREFIX + userId + "_" + externalUserId);
        if (Objects.isNull(o)) {
            return null;
        }
        WechatUserDTO wechatUserDTO = wechatUserOpenSearchManager.getByUserId(userId, SpaceInfoThreadLocalUtils.getCorpId());
        if (Objects.isNull(wechatUserDTO)) {
            return null;
        }
        return wechatUserDTO;
    }

    /**
     * 填充用户飞猪域信息
     * @param fusionChatCustomerVO 用户信息
     * @param userId 淘系用户id
     */
    private void fillUserFliggyInfo(FusionChatCustomerVO fusionChatCustomerVO, String userId) {
        if (StringUtils.isBlank(userId) || !StringUtils.isNumeric(userId)) {
            return ;
        }
        // 会员信息查询
        MemberDTO memberDTO = memberManager.queryMemberInfo(Long.valueOf(userId));
        if (memberDTO == null) {
            return ;
        }
        // 结果集填充
        fusionChatCustomerVO.setMemberLevel(memberDTO.getLevelName());
        fusionChatCustomerVO.setMileage(memberDTO.getPoint());
        Map<String, String> userStayCity = profileService.getUserStayCity(userId);
        if (MapUtils.isEmpty(userStayCity)) {
            return ;
        }
        fusionChatCustomerVO.setCityName(userStayCity.get(ProfileServiceImpl.STAY_CITY_NAME));
        fusionChatCustomerVO.setCityCode(userStayCity.get(ProfileServiceImpl.STAY_CITY_CODE));
    }

    /**
     * 填充用户群聊信息
     * @param fusionChatCustomerVO 用户信息
     */
    private void fillUserChatInfo(FusionChatCustomerVO fusionChatCustomerVO, String externalUserId) {
        List<WechatGroupWithWorkVO> wechatGroupVOList = listGroupByUserId(externalUserId, null, null);
        if (CollectionUtils.isNotEmpty(wechatGroupVOList)) {
            wechatGroupVOList.stream().map(wechatGroupWithWorkVO -> {
                wechatGroupWithWorkVO.setStatus(UserRelationStatusEnum.ADDED.getCode());
                return wechatGroupWithWorkVO;
            }).collect(Collectors.toList());
        }
        fusionChatCustomerVO.setGroupVOList(wechatGroupVOList);
    }

    @Override
    public FusionChatGroupVO getGroupInfo(String chatId, String userId) {
        FusionChatGroupVO fusionChatGroupVO = new FusionChatGroupVO();
        List<WechatGroupVO> wechatGroupVOList = wechatGroupService.listByChatIdList(Lists.newArrayList(chatId), false);

        if (CollectionUtils.isEmpty(wechatGroupVOList)) {
            return fusionChatGroupVO;
        }
        WechatGroupVO wechatGroupVO = wechatGroupVOList.get(0);
        fusionChatGroupVO.setName(wechatGroupVO.getName());
        fusionChatGroupVO.setTags(wechatGroupVO.getTagList());
        fusionChatGroupVO.setAnnouncement(wechatGroupVO.getNotice());

        List<GroupMemberCountResult> groupMemberCountResults = groupRelationRepository.countByShardingKeyList(Lists.newArrayList(groupRelationRepository.chatId2ShardingKey(chatId)), Lists.newArrayList(MemberTypeEnum.ExternalContact.getType()), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isNotEmpty(groupMemberCountResults)) {
            fusionChatGroupVO.setUserCount(groupMemberCountResults.get(0).getCount());
        }

        List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(chatId), Lists.newArrayList(userId), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isNotEmpty(groupRelationList)) {
            fusionChatGroupVO.setRemark(groupRelationList.get(0).getRemark());
            fusionChatGroupVO.setLastUpdateTime(groupRelationList.get(0).getLastUpdateRemarkData());
        }
        return fusionChatGroupVO;
    }

    @Override
    public Object getWxCommunityContentList(String title, Integer pageNum, Integer pageSize) {
        WxCommunityContentRequest request = new WxCommunityContentRequest();
        request.setSearchWord(title);
        request.setPageSize(pageSize);
        request.setPageNum(pageNum);
        TripSCRMResult<PageInfoDTO<WxCommunityContentDTO>> result = wxCommunityContentService.listByKeyWord(request);
        if (Objects.isNull(result)) {
            return null;
        }
        if (!result.isSuccess()) {
            throw new RuntimeException(result.getMsg());
        }

        PageInfoDTO<WxCommunityContentDTO> pageInfoDTO = result.getData();
        List<WxCommunityContentDTO> wxCommunityContentDTOList = pageInfoDTO.getList();
        for (WxCommunityContentDTO wxCommunityContentDTO : wxCommunityContentDTOList) {
            List<String> tagIds = wxCommunityContentDTO.getTagIds();
            TripSCRMResult<List<WxCommunityTagDTO>> tagResult = wxCommunityContentService.listTagByIds(tagIds);
            if (Objects.isNull(tagResult) || !tagResult.isSuccess() || CollectionUtils.isEmpty(tagResult.getData())) {
                continue;
            }
            List<String> tagNameList = tagResult.getData().stream().map(WxCommunityTagDTO::getName).collect(Collectors.toList());
            wxCommunityContentDTO.setTagNames(tagNameList);
        }
        return result.getData();
    }

    @Override
    public List<FusionChatMessageBody> toMaterialMessage(MaterialToMessageRequest request) {
        // 素材消息
        List<MessageBO> materialMessageList = materialService
                .getMaterialMessageList(request.getMaterialId(), build(request), Maps.newHashMap());
        if (CollectionUtils.isEmpty(materialMessageList)) {
            return Lists.newArrayList();
        }
        // 聚合聊天消息
        return materialMessageList.stream().map(MessageUtils::sendMessage2FusionChatMessage).collect(Collectors.toList());
    }

    @Override
    public ItemSearchVO<ItemVO> searchItem(GetItemListRequest request) {
        ItemSearchVO<ItemVO> searchVO = new ItemSearchVO<>();
        searchVO.setPageNum(request.getPageNum());
        searchVO.setPageSize(request.getPageSize());
        // 请求体构建
        ItemQuery itemQuery = new ItemQuery();
        itemQuery.setPageNum(request.getPageNum());
        itemQuery.setPageSize(request.getPageSize());
        itemQuery.setSellerId(request.getSellerId());
        itemQuery.setSearchKey(request.getSearchKey());
        itemQuery.setItemTypeEnum(ItemTypeEnum.findByType(request.getItemType()));
        // 请求物料列表
        ItemSearchDTO<ItemVO> itemSearchDTO = itemFactory.queryItemList(itemQuery);
        BeanUtils.copyProperties(itemSearchDTO, searchVO);
        return searchVO;
    }

    /**
     * 构建素材关系对象
     *
     * @param request 请求
     * @return MaterialTrackRelationDTO
     */
    private MaterialTrackRelationDTO build(MaterialToMessageRequest request) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setTaskId(-1L);
        materialTrackRelationDTO.setTaskInsId(-1L);
        materialTrackRelationDTO.setAbBucketId("-1");
        materialTrackRelationDTO.setMaterialId(request.getMaterialId());
        materialTrackRelationDTO.setWxUserId(request.getSendUserId());
        materialTrackRelationDTO.setOperatorId(request.getOperatorId());
        if (FusionChatReceiveTypeEnum.GROUP.getValue().equals(request.getChatType())) {
            materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.JUHE_QUNLIAO);
            materialTrackRelationDTO.setSendChatId(request.getTargetId());
        } else if (FusionChatReceiveTypeEnum.SINGLE.getValue().equals(request.getChatType())) {
            materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.JUHE_SILIAO);
            materialTrackRelationDTO.setSendUnionId(request.getTargetId());
        }
        return materialTrackRelationDTO;
    }

}