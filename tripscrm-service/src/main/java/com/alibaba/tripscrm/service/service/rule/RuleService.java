package com.alibaba.tripscrm.service.service.rule;

import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.service.model.domain.query.RuleQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 规则相关接口
 *
 * <AUTHOR>
 * @since 2024/6/6 19:33
 */
public interface RuleService {
    /**
     * 根据id查询规则
     */
    RuleDO selectById(Long id);

    /**
     * 根据id查询规则
     */
    RuleDO selectByTargetId(Byte type, Byte targetType, String targetId);

    /**
     * 根据id查询规则
     */
    List<RuleDO> listByTargetIds(Byte type, Byte targetType, List<String> targetIds);

    /**
     * 根据条件查询规则
     */
    List<RuleDO> selectByCondition(RuleQuery query);

    /**
     * 根据条件查询规则
     */
    PageInfo<RuleDO> pageQuery(RuleQuery query);

    /**
     * 创建规则
     */
    int create(RuleDO ruleDO);

    /**
     * 更新规则
     */
    int update(RuleDO ruleDO, boolean doPost);

    /**
     * 删除规则
     */
    int delete(Long id);

    /**
     * 删除规则
     */
    int deleteByTargetId(String targetId, Integer targetType);

    /**
     * 上线规则
     */
    boolean online(Long id);

    /**
     * 下线规则
     */
    boolean offline(Long id);

    /**
     * 根据type和deleted查询(读本地缓存）
     */
    public List<RuleDO> selectByTypeAndDeletedWithCache(Byte types, Byte deleted, Long groupId);
}
