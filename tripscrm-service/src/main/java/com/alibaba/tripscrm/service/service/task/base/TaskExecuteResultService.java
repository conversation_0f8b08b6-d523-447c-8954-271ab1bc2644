package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteResultVO;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-11-05 22:59:56
 */
public interface TaskExecuteResultService {
    /**
     * 统计任务执行结果
     *
     * @param taskId    任务 Id
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 任务执行结果
     */
    TaskExecuteResultVO getTaskExecuteResult(Long taskId, Date beginDate, Date endDate);
}
