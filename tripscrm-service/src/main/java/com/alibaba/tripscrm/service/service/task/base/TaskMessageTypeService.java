package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.vo.task.TaskMessageTypeVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-19 17:42:18
 */
public interface TaskMessageTypeService {
    /**
     * 根据id查询
     *
     * @param id 主键
     * @return TaskMessageTypeVO
     */
    TaskMessageTypeVO getById(Long id);

    /**
     * 根据任务类型查询
     *
     * @param taskType 任务类型
     * @return List<TaskMessageTypeVO>
     */
    List<TaskMessageTypeVO> listByTaskType(TaskType taskType);

    /**
     * 根据任务类型查询（读本地缓存）
     * @param taskType 任务类型
     * @return List<TaskMessageTypeVO>
     */
    List<TaskMessageTypeVO> listCacheByTaskType(TaskType taskType);

    /**
     * 批量upsert
     *
     * @param list 新增的数据
     * @return 影响行数
     */
    Integer batchUpsert(List<TaskMessageTypeVO> list);

    /**
     * 修改数据
     *
     * @param taskMessageType 新的数据
     * @return 影响行数
     */
    Integer updateById(TaskMessageTypeVO taskMessageType);

    /**
     * 根据主键删除
     *
     * @param id
     * @return
     */
    Integer deleteById(Long id);
}
