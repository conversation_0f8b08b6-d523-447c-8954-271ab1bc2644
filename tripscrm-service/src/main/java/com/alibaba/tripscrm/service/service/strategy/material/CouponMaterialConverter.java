package com.alibaba.tripscrm.service.service.strategy.material;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.CouponMaterialDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.message.CouponMsgDTO;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Component
@AllArgsConstructor
public class CouponMaterialConverter extends AbstractMaterialConverter<CouponMaterialDTO> {

    @Override
    public CouponMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        return JSONObject.parseObject(content, CouponMaterialDTO.class);
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        return new ArrayList<>();
    }

    @Override
    public List<MessageBO> buildMessageBO(CouponMaterialDTO materialDTO, MaterialContentConvertContext context, String sceneType) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgType(MessageTypeEnum.ALIPAY_MARKETING_ACTIVITY);
        messageBO.setMsgNum(materialDTO.getIndex());
        CouponMsgDTO couponMsgDTO = new CouponMsgDTO();
        couponMsgDTO.setLongIntroduceText(materialDTO.getLongIntroduceText());
        couponMsgDTO.setMultiCoupon(materialDTO.getMultiCoupon());
        couponMsgDTO.setShortIntroduceTextList(materialDTO.getShortIntroduceTextList());
        couponMsgDTO.setImageId(materialDTO.getImageId());
        couponMsgDTO.setTitle(materialDTO.getTitle());
        if (materialDTO.getMultiCoupon()) {
            couponMsgDTO.setActivityIdList(new ArrayList<>(materialDTO.getActivityMap().keySet()));
        } else {
            couponMsgDTO.setActivityId(materialDTO.getActivityMap().keySet().iterator().next());
        }
        messageBO.setExtFields(JSONObject.toJSONString(couponMsgDTO));
        return Lists.newArrayList(messageBO);
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(CouponMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        return null;
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.COUPON;
    }

    @Override
    public String buildSubscribeMsgTemplateContent(String content, String paramType) {
        return "";
    }
}
