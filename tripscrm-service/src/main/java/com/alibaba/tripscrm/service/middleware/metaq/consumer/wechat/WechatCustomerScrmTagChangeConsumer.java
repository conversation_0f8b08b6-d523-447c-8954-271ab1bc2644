package com.alibaba.tripscrm.service.middleware.metaq.consumer.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.RuleConstant;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.RuleExecuteContext;
import com.alibaba.tripscrm.service.service.strategy.rule.AbstractRuleGroupStrategy;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * topic: scrm_wechat_customer_scrm_tag_change
 * consumerId: CID_tripscrm_wechat_customer_scrm_tag_change
 * <p>
 * 消息格式为：${corp_id}\t${external_user_id}\t${tagIdList}
 *
 * <AUTHOR>
 * @since 2024/6/6 10:46
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerScrmTagChangeConsumer implements MessageListenerConcurrently {
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo(("接收到客户标签变更消息"), LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("处理客户标签变更消息失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("客户标签变更消息解析后结果", LogListUtil.newArrayList(message));
            if (!StringUtils.hasLength(message)) {
                PlatformLogUtil.logFail("客户标签变更消息内容解析为空");
                return false;
            }

            List<String> contentList = Arrays.stream(message.split("\t")).filter(StringUtils::hasLength).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contentList) || contentList.size() != 3) {
                PlatformLogUtil.logFail("客户标签变更消息内容非法");
                return false;
            }

            // 客户新增的系统标签Id
            String corpId = contentList.get(0);
            String externalUserId = contentList.get(1);
            SpaceInfoThreadLocalUtils.setCorpId(corpId);
            List<String> newTagIdList = Arrays.stream(Optional.ofNullable(contentList.get(2)).orElse("").split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());

            if (!StringUtils.hasLength(corpId) || !StringUtils.hasLength(externalUserId) || CollectionUtils.isEmpty(newTagIdList)) {
                PlatformLogUtil.logFail("消息内容非法");
                return false;
            }

            // 从规则配置获取需要调度的任务
            RuleExecuteContext context = new RuleExecuteContext();
            JSONObject params = new JSONObject();
            params.put(RuleConstant.NEW_TAG_ID_LIST, newTagIdList);
            params.put(RuleConstant.CORP_ID, corpId);
            params.put(RuleConstant.EXTERNAL_USER_ID, externalUserId);
            context.setParams(params);
            AbstractRuleGroupStrategy ruleGroupStrategy = AbstractRuleGroupStrategy.getStrategyByType(RuleTypeEnum.TASK);
            if (Objects.isNull(ruleGroupStrategy)) {
                PlatformLogUtil.logFail("获取规则组执行策略失败，本次处理结束");
                return false;
            }
            TripSCRMResult<Void> result = ruleGroupStrategy.run(context);
            if (!result.isSuccess()) {
                PlatformLogUtil.logFail("规则组策略执行失败", LogListUtil.newArrayList(result));
                return false;
            }

            PlatformLogUtil.logFail("规则组策略执行成功", LogListUtil.newArrayList(result));
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }
}
