package com.alibaba.tripscrm.service.service.impl.seller;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.SellerWorkScheduleInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SellerWorkScheduleInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.SellerWorkScheduleInfoParam;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.convert.SellerWorkScheduleInfoConverter;
import com.alibaba.tripscrm.service.model.domain.query.seller.SellerWorkScheduleInfoQuery;
import com.alibaba.tripscrm.service.model.dto.seller.SellerWorkScheduleInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.seller.SellerWorkScheduleInfoService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerWorkScheduleInfoServiceImpl implements SellerWorkScheduleInfoService {
    private static final int[] DAY_OF_WEEK_MAP = new int[]{0, 7, 1, 2, 3, 4, 5, 6};
    private final SellerWorkScheduleInfoMapper sellerWorkScheduleInfoMapper;
    private final SellerWorkScheduleInfoConverter sellerWorkScheduleInfoConverter;

    /**
     * 根据参数查询
     *
     * @param query
     */
    @Override
    public SellerWorkScheduleInfoDTO find(SellerWorkScheduleInfoQuery query) {
        List<SellerWorkScheduleInfoDTO> list = list(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    /**
     * 列表查询
     *
     * @param query
     */
    @Override
    public List<SellerWorkScheduleInfoDTO> list(SellerWorkScheduleInfoQuery query) {
        SellerWorkScheduleInfoParam sellerWorkScheduleInfoParam = buildParam(query);
        List<SellerWorkScheduleInfoDO> list = sellerWorkScheduleInfoMapper.selectByParam(sellerWorkScheduleInfoParam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<SellerWorkScheduleInfoDTO> result = new ArrayList<>();
        for (SellerWorkScheduleInfoDO record : list) {
            SellerWorkScheduleInfoDTO sellerWorkScheduleInfoDTO = sellerWorkScheduleInfoConverter.convertFromDO(record);
            result.add(sellerWorkScheduleInfoDTO);
        }
        return result;
    }

    /**
     * 创建
     *
     * @param query
     */
    @Override
    public int createSelective(SellerWorkScheduleInfoDTO query) {
        SellerWorkScheduleInfoDO record = sellerWorkScheduleInfoConverter.convertFromDTO(query);
        return sellerWorkScheduleInfoMapper.insertSelective(record);
    }

    /**
     * 修改
     *
     * @param dto
     * @param query
     */
    @Override
    public int updateSelective(SellerWorkScheduleInfoDTO dto, SellerWorkScheduleInfoQuery query) {
        //  参数隔离一下，避免修改到关键字段
        SellerWorkScheduleInfoDTO infoDTO = new SellerWorkScheduleInfoDTO();
        infoDTO.setBeginTime(dto.getBeginTime());
        infoDTO.setEndTime(dto.getEndTime());
        infoDTO.setDayOfWeek(dto.getDayOfWeek());
        infoDTO.setWechatUserIds(dto.getWechatUserIds());
        SellerWorkScheduleInfoDO record = sellerWorkScheduleInfoConverter.convertFromDTO(dto);
        SellerWorkScheduleInfoParam param = buildParam(query);
        return sellerWorkScheduleInfoMapper.updateByParamSelective(record, param);
    }

    /**
     * 根据ID删除
     *
     * @param condition infoDO
     */
    @Override
    public int delete(SellerWorkScheduleInfoQuery condition) {
        if (Objects.isNull(condition)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        SellerWorkScheduleInfoParam sellerWorkScheduleInfoParam = buildParam(condition);
        return sellerWorkScheduleInfoMapper.deleteByParam(sellerWorkScheduleInfoParam);
    }

    /**
     * 带事务的更新或是新增
     *
     * @param dto
     * @param query
     */
    @Override
    @Transactional
    public int createOrUpdateSelective(SellerWorkScheduleInfoDTO dto, SellerWorkScheduleInfoQuery query) {
        List<SellerWorkScheduleInfoDTO> queryList = list(query);
        if (CollectionUtils.isEmpty(queryList)) {
            return createSelective(dto);
        } else {
            queryList.forEach(infoDTO -> {
                sellerWorkScheduleInfoMapper.deleteByPrimaryKey(infoDTO.getId());
            });
            return createSelective(dto);
        }
    }

    @AteyeInvoker(description = "查询指定时间段内的排班人员", paraDesc = "sellerId&userIdStr&timestamp")
    public TripSCRMResult<List<String>> queryScheduleWechatUserIdList(String sellerId, String userIdStr, Long timestamp) {
        return queryScheduleWechatUserIdList(sellerId, !StringUtils.hasText(userIdStr) ? null : Arrays.stream(userIdStr.split(",")).collect(Collectors.toList()), new Date(timestamp));
    }

    @Override
    public TripSCRMResult<List<String>> queryScheduleWechatUserIdList(String sellerId, List<String> userIdList, Date date) {
        SellerWorkScheduleInfoQuery sellerWorkScheduleInfoQuery = new SellerWorkScheduleInfoQuery();
        sellerWorkScheduleInfoQuery.setSellerId(Long.parseLong(sellerId));
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        byte dayOfWeek = (byte) DAY_OF_WEEK_MAP[cal.get(Calendar.DAY_OF_WEEK)];
        sellerWorkScheduleInfoQuery.setDayOfWeekList(Lists.newArrayList((byte) 0, dayOfWeek));
        sellerWorkScheduleInfoQuery.setTime(DateUtils.getHourMiniteShowString2(new Date()));

        List<SellerWorkScheduleInfoDTO> sellerWorkScheduleInfoList = list(sellerWorkScheduleInfoQuery);
        if (CollectionUtils.isEmpty(sellerWorkScheduleInfoList)) {
            PlatformLogUtil.logFail("查询商家值班账号，商家当前没有值班账号", TripSCRMErrorCode.SELLER_NO_WORK_WECHAT_USER.getCode(), LogListUtil.newArrayList(sellerWorkScheduleInfoQuery));
            return TripSCRMResult.fail(TripSCRMErrorCode.SELLER_NO_WORK_WECHAT_USER);
        }

        List<String> wechatUserIdList = Optional.ofNullable(userIdList).orElse(new ArrayList<>()).stream().filter(StringUtils::hasText).collect(Collectors.toList());
        List<String> sellerWechatUserIdList = Lists.newArrayList();

        // 兜底账号
        String wechatUserId1 = sellerWorkScheduleInfoList.stream()
                .filter(sellerWorkScheduleInfoDTO -> Objects.equals(sellerWorkScheduleInfoDTO.getDayOfWeek(), (byte) 0))
                .flatMap(sellerWorkScheduleInfoDTO -> Arrays.stream(sellerWorkScheduleInfoDTO.getWechatUserIds().split(",")))
                .findFirst().orElse(null);
        if (StringUtils.hasText(wechatUserId1)) {
            sellerWechatUserIdList.add(wechatUserId1);
        }

        // 值班账号
        List<String> workWechatUserIdList = sellerWorkScheduleInfoList.stream()
                .map(sellerWorkScheduleInfoDTO ->
                        Arrays.stream(sellerWorkScheduleInfoDTO.getWechatUserIds().split(",")).filter(StringUtils::hasText)
                                .filter(wechatUserId ->
                                        !Objects.equals(sellerWorkScheduleInfoDTO.getDayOfWeek(), (byte) 0)
                                                && (CollectionUtils.isEmpty(wechatUserIdList) || wechatUserIdList.contains(wechatUserId))
                                                && !Objects.equals(wechatUserId1, wechatUserId))
                                .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        String wechatUserId2 = CollectionUtils.isEmpty(workWechatUserIdList) ? null : workWechatUserIdList.get(ThreadLocalRandom.current().nextInt(workWechatUserIdList.size()));
        if (StringUtils.hasText(wechatUserId2)) {
            sellerWechatUserIdList.add(wechatUserId2);
        }


        PlatformLogUtil.logInfo("查询商家值班账号", LogListUtil.newArrayList(sellerId, wechatUserIdList, sellerWechatUserIdList));
        return TripSCRMResult.success(sellerWechatUserIdList);
    }

    /**
     * 删除根据星期和商家ID
     *
     * @param sellerId
     * @param weekDate
     */
    @Override
    public void deleteByWeekDate(String sellerId, Integer weekDate) {
        Objects.requireNonNull(sellerId);
        Objects.requireNonNull(weekDate);
        SellerWorkScheduleInfoQuery infoQuery = new SellerWorkScheduleInfoQuery();
        infoQuery.setSellerId(Long.valueOf(sellerId));
        infoQuery.setDayOfWeekList(Lists.newArrayList(weekDate.byteValue()));

        List<SellerWorkScheduleInfoDTO> list = list(infoQuery);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(infoDTO -> {
            sellerWorkScheduleInfoMapper.deleteByPrimaryKey(infoDTO.getId());
        });
    }

    private SellerWorkScheduleInfoParam buildParam(SellerWorkScheduleInfoQuery query) {
        SellerWorkScheduleInfoParam param = new SellerWorkScheduleInfoParam();
        SellerWorkScheduleInfoParam.Criteria criteria = param.createCriteria();
        if (NumberUtils.biggerThanZero(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (NumberUtils.biggerThanZero(query.getSellerId())) {
            criteria.andSellerIdEqualTo(query.getSellerId());
        }
        if (NumberUtils.biggerThanZero(query.getSpaceId())) {
            criteria.andSpaceIdEqualTo(query.getSpaceId());
        }
        if (!CollectionUtils.isEmpty(query.getDayOfWeekList())) {
            criteria.andDayOfWeekIn(query.getDayOfWeekList());
        }
        if (StringUtils.hasText(query.getTime())) {
            criteria.andBeginTimeLessThanOrEqualTo(query.getTime());
            criteria.andEndTimeGreaterThanOrEqualTo(query.getTime());
        }
        if (StringUtils.hasText(query.getBeginTime())) {
            criteria.andBeginTimeEqualTo(query.getBeginTime());
        }
        if (StringUtils.hasText(query.getEndTime())) {
            criteria.andEndTimeEqualTo(query.getEndTime());
        }

        return param;
    }
}