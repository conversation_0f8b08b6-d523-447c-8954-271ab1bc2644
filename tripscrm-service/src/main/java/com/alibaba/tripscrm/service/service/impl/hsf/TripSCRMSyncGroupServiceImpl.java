package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.domain.request.TripSCRMGroupSyncRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMGroupTemplateSyncRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMSyncGroupService;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.dto.group.GroupTemplateInfoDTO;
import com.alibaba.tripscrm.service.model.dto.group.ChatSynchronizerDTO;
import com.alibaba.tripscrm.service.synchronizer.GroupChatSynchronizer;
import com.alibaba.tripscrm.service.synchronizer.GroupTemplateSynchronizer;
import com.alibaba.tripscrm.service.synchronizer.SyncResult;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/19 16:04
 * @Filename：TripSCRMSyncGroupServiceImpl
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@HSFProvider(serviceInterface = TripSCRMSyncGroupService.class)
public class TripSCRMSyncGroupServiceImpl implements TripSCRMSyncGroupService {
    private final GroupChatSynchronizer groupChatSynchronizer;
    private final GroupTemplateSynchronizer groupTemplateSynchronizer;
    /**
     * 同步群模版(组)信息
     *
     * @param request
     * @return
     */
    @Override
    public TripSCRMResult<Boolean> syncGroupTemplateInfo(TripSCRMGroupTemplateSyncRequest request) {
        BaseSynchronizerContext<GroupTemplateInfoDTO> context = new BaseSynchronizerContext<>();
        GroupTemplateInfoDTO data = new GroupTemplateInfoDTO();
        data.setAdminInfo(request.getAdminInfo());
        data.setCorpId(request.getCorpId());
        data.setGroupName(request.getGroupName());
        data.setGroupTemplateId(request.getGroupTemplateId());
        data.setPlatformType(PlatformTypeEnum.valueOf(request.getPlatformType()));
        data.setDeleted(request.getDeleted());
        data.setExtraInfo(request.getExtraInfo());
        context.setData(data);
        // 同步
        SyncResult<GroupTemplateInfoDTO> sync = groupTemplateSynchronizer.sync(context);
        if(!sync.getSuccess()){
            return TripSCRMResult.fail(sync.getMessage());
        }
        return TripSCRMResult.success(true);
    }

    /**
     * 同步增群信息
     *
     * @param request
     * @return
     */
    @Override
    public TripSCRMResult<Boolean> syncGroup(TripSCRMGroupSyncRequest request) {
        BaseSynchronizerContext<ChatSynchronizerDTO> context = new BaseSynchronizerContext<>();
        ChatSynchronizerDTO data = new ChatSynchronizerDTO();
        data.setPlatformType(PlatformTypeEnum.valueOf(request.getPlatformType()));
        data.setChatId(request.getChatId());
        data.setCorpId(request.getCorpId());
        context.setData(data);
        SyncResult<ChatSynchronizerDTO> sync = groupChatSynchronizer.sync(context);
        if(!sync.getSuccess()){
            return TripSCRMResult.fail(sync.getMessage());
        }
        return TripSCRMResult.success(true);
    }
}
