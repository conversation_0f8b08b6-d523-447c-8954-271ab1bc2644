package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.factory.ThreadPoolExecutorFactory;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerAddTagTaskExecutor extends AbstractTaskExecutor {
    private final WechatCustomerService wechatCustomerService;
    private final TagRelationService tagRelationService;
    private final LdbTairManager ldbTairManager;
    private final TagInfoService tagInfoService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        if (checkDuplicate(context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            return;
        }

        String externalUserId = getFinalTargetId(context, taskDataBody);
        String userId = getSendUserId(context, todoTaskVO);
        List<String> tagList = getTagList(context, todoTaskVO);
        CompletableFuture.supplyAsync(() -> {
            upsertTagRelation(tagList, externalUserId, userId);
            return true;
            }, ThreadPoolExecutorFactory.ITEM_TAG_RELATION_INSERT_THREAD_POOL);
        JSONObject data = new JSONObject();
        data.put("externalUserId", externalUserId);
        data.put("tagList", tagList);
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (taskDataBody.getContext().containsKey("externalUserId")) {
            return (String) taskDataBody.getContext().get("externalUserId");
        }

        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("empty targetId", LogListUtil.newArrayList(context.getTaskId(), taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!Lists.newArrayList(ActivityTargetTypeEnum.WX_EXTERNAL_USERID, ActivityTargetTypeEnum.WX_UNION_ID).contains(activityTargetType)) {
            PlatformLogUtil.logFail("only accept externalUserId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        String externalUserId = taskDataBody.getTargetId();
        if (activityTargetType == ActivityTargetTypeEnum.WX_UNION_ID) {
            externalUserId = wechatCustomerService.getExternalUserIdByUnionId(taskDataBody.getTargetId());
        }

        if (StringUtils.isBlank(externalUserId)) {
            PlatformLogUtil.logFail("externalUserId is empty", LogListUtil.newArrayList(context.getTaskId(), taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.EMPTY_EXTERNAL_USER_ID);
        }

        taskDataBody.getContext().put("externalUserId", externalUserId);
        return externalUserId;
    }

    private boolean checkDuplicate(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        Long taskId = todoTaskVO.getTaskId();
        if (ldbTairManager.incr(TairConstant.TASK_EXECUTE_DUPLICATE_CHECK_PREFIX + taskId + "_" + getFinalTargetId(context, taskDataBody), 1, 0, 10) > 1) {
            PlatformLogUtil.logFail("duplicate execute", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            return true;
        }

        return false;
    }

    private List<String> getTagList(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 先取事件源或接口指定的发送者id
        Map<String, Object> extInfo = context.getExtInfo();
        if (Objects.nonNull(extInfo) && extInfo.containsKey("tagIds")) {
            String tagIds = (String) extInfo.get("tagIds");
            if (StringUtils.isNotBlank(tagIds)) {
                List<String> tagIdList = Arrays.stream(tagIds.split(",")).collect(Collectors.toList());
                PlatformLogUtil.logInfo("客户打标任务，从数据源中获取到tagIds", LogListUtil.newArrayList(tagIdList));
                return tagIdList;
            }
        }

        String taskExtInfoStr = context.getTaskInfoDOSnapshot().getExtInfo();
        if (StringUtils.isEmpty(taskExtInfoStr)) {
            return new ArrayList<>();
        }

        JSONObject taskExtInfo = JSONObject.parseObject(taskExtInfoStr);
        return taskExtInfo.getObject("tagList", new TypeReference<ArrayList<String>>() {
        });
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        Map<String, Object> extInfo = context.getExtInfo();
        return MapUtils.getString(extInfo, "userId", null);
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_EXTERNAL_USERID;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.WECHAT_CUSTOMER_ADD_TAG;
    }


    private void upsertTagRelation(List<String> tagList, String externalUserId, String userId) {
        List<ItemTagRelationDTO> addList =
                tagList.stream()
                        .map(tagId -> {
                            TagInfoDTO tagInfoDTO = tagInfoService.queryByIdWithCache(tagId);
                            if (Objects.isNull(tagInfoDTO) || Objects.isNull(TagTypeEnum.of(tagInfoDTO.getTagType()))) {
                                return null;
                            }
                            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                            itemTagRelationDTO.setItemId(externalUserId + "_" + SpaceInfoThreadLocalUtils.getCorpId());
                            itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_CUSTOMER.getCode());
                            itemTagRelationDTO.setTag(tagId);
                            itemTagRelationDTO.setRelationId(userId);
                            itemTagRelationDTO.setDeleted((byte) 0);
                            if (Objects.equals(TagTypeEnum.of(tagInfoDTO.getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG)) {
                                itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_CUSTOMER_RELATION.getCode());
                            }
                            return itemTagRelationDTO;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
        tagRelationService.batchUpsertSelective(addList);
    }
}
