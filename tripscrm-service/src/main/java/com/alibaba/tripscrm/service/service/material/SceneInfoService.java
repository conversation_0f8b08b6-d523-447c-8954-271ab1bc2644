package com.alibaba.tripscrm.service.service.material;

import com.alibaba.tripscrm.dal.model.domain.data.SceneInfoDO;
import com.alibaba.tripscrm.service.model.domain.query.SceneInfoQuery;
import com.alibaba.tripscrm.service.model.vo.material.SceneTreeVO;

import java.util.List;

public interface SceneInfoService {

    /**
     * 新增场景
     * @param sceneInfoDO 场景信息
     * @return 处理结果
     */
    Boolean add(SceneInfoDO sceneInfoDO);

    /**
     * 移除场景
     * @param id 主键
     * @return 处理结果
     */
    Boolean delete(Long id);

    /**
     * 更新场景信息
     * @param sceneInfoDO 场景信息
     * @return 处理结果
     */
    Boolean updateById(SceneInfoDO sceneInfoDO);

    /**
     * 列表查询
     * @param query 查询条件
     * @return 场景列表
     */
    List<SceneInfoDO> list(SceneInfoQuery query);

    /**
     * 主键查询
     * @param id 主键
     * @return 场景对象
     */
    SceneInfoDO queryById(Long id);

    /**
     * 获取场景树结构
     * @return 场景树结构
     */
    List<SceneTreeVO> tree();

}
