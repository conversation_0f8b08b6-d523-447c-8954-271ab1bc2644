package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.vo.task.TaskAsyncRequestDataVO;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.callback.TaskAsyncRequestCallbackProcessor;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 通过群聊主动添加好友请求发送结果【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AddMemberByGroupRequestSendResultCallbackProcessor implements ProxyCallbackProcessor {
    private final LdbTairManager ldbTairManager;
    private final TaskAsyncRequestCallbackProcessor taskAsyncRequestCallbackProcessor;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.ADD_MEMBER_BY_GROUP_SEND_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        processStatistic(scrmCallbackMsg);
        processTaskExecuteResult(scrmCallbackMsg);
        return true;
    }

    private void processTaskExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        taskAsyncRequestCallbackProcessor.process(scrmCallbackMsg);
    }

    private void processStatistic(ScrmCallbackMsg scrmCallbackMsg) {
        String requestId = scrmCallbackMsg.getRequestId();
        if (!StringUtils.hasText(requestId)) {
            return;
        }

        Object o = ldbTairManager.get(TairConstant.LDB_PROXY_REQUEST_DATA_PREFIX + scrmCallbackMsg.getRequestId());
        if (Objects.isNull(o)) {
            return;
        }

        TaskAsyncRequestDataVO taskAsyncRequestDataVO = JSONObject.parseObject(ldbTairManager.get(TairConstant.LDB_PROXY_REQUEST_DATA_PREFIX + scrmCallbackMsg.getRequestId()).toString(), new TypeReference<TaskAsyncRequestDataVO>() {
        });
        if (NumberUtils.validLong(taskAsyncRequestDataVO.getTaskId())) {
            // 记录发送群内添加好友请求成功数据
            ldbTairManager.incr(TairConstant.ADD_WECHAT_CUSTOMER_BY_GROUP_REQUEST_SUCCESS_COUNT_PREFIX + taskAsyncRequestDataVO.getTaskId(), 1, 0, 86400 * 365 * 10);
        }
    }
}
