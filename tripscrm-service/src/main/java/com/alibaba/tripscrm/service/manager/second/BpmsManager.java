package com.alibaba.tripscrm.service.manager.second;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.alipmc.api.ProcessInstanceService;
import com.alibaba.alipmc.api.model.bpm.ProcessInstance;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.second.BpmsTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.context.BpmsContext;
import com.alibaba.tripscrm.service.model.vo.SpaceVO;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;


/**
 * <AUTHOR>
 * @date 2023-08-21 21:27:01
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BpmsManager {
    /**
     * 审批默认 7 天过期
     */
    private final Integer expireSecond = 7 * 24 * 60 * 60;

    @Switch(description = "BPMS审核 authKey", name = "bpmsAuthKey")
    private static String bpmsAuthKey = "1$tripscrm$e1730eb6-3e8f-4836-825d-d671331556f9";

    private final LdbTairManager ldbTairManager;
    private final SpaceService spaceService;
    private final ProcessInstanceService processInstanceService;

    private Map<BpmsTypeEnum, Function<BpmsContext, Boolean>> functionMap;

    @PostConstruct
    public void init() {
        functionMap = new HashMap<>();
        functionMap.put(BpmsTypeEnum.CREATE_SPACE, this::createSpace);
        functionMap.put(BpmsTypeEnum.UPDATE_SPACE, this::updateSpace);
    }

    /**
     * 保存上下文
     *
     * @param bpmsContext
     * @return
     */
    private boolean saveContext(BpmsContext bpmsContext) {
        return ldbTairManager.put(TairConstant.LDB_BPMS_CONTEXT_PREFIX + bpmsContext.getId(), JSONObject.toJSONString(bpmsContext), expireSecond);
    }

    /**
     * 查询上下文
     *
     * @param contextId 上下文 Id
     * @return BpmsContext
     */
    private BpmsContext getContext(String contextId) {
        Object tairObj = ldbTairManager.get(TairConstant.LDB_BPMS_CONTEXT_PREFIX + contextId);
        return JSONObject.parseObject(tairObj.toString(), new TypeReference<BpmsContext>() {
        });
    }

    public boolean startProcess(BpmsContext context) {
        if (Objects.isNull(context.getCreatorId())) {
            return false;
        }

        if (!saveContext(context)) {
            PlatformLogUtil.logFail("保存上下文失败", LogListUtil.newArrayList(context));
            return false;
        }

        ProcessInstance processInstance = processInstanceService.startProcessInstance(
                context.getBpmsTypeEnum().getDesc(),
                context.getTitle(),
                context.getCreatorId(),
                context.getInitData(),
                bpmsAuthKey
        );

        PlatformLogUtil.logFail("保存上下文成功", LogListUtil.newArrayList(context, processInstance));
        return true;
    }

    @AteyeInvoker(description = "测试BPMS回调", paraDesc = "contextId")
    public boolean testProcessCallback(String contextId) {
        return processCallback(contextId);
    }

    public boolean processCallback(String contextId) {
        BpmsContext context = getContext(contextId);
        if (Objects.isNull(context)) {
            PlatformLogUtil.logFail("获取上下文失败", LogListUtil.newArrayList(context));
            return false;
        }

        if (!functionMap.containsKey(context.getBpmsTypeEnum())) {
            PlatformLogUtil.logFail("获取函数map失败", LogListUtil.newArrayList(context));
        }

        return functionMap.get(context.getBpmsTypeEnum()).apply(context);
    }

    private boolean createSpace(BpmsContext bpmsContext) {
        if (Objects.isNull(bpmsContext.getContent())) {
            return false;
        }

        SpaceVO spaceVO = bpmsContext.getContent().toJavaObject(new TypeReference<SpaceVO>() {
        });
        SpaceInfoThreadLocalUtils.setCorpId(spaceVO.getCorpId());
        Long spaceId = spaceService.create(spaceVO);
        SpaceInfoThreadLocalUtils.remove();
        return NumberUtils.validLong(spaceId);
    }

    private boolean updateSpace(BpmsContext bpmsContext) {
        if (Objects.isNull(bpmsContext.getContent())) {
            return false;
        }

        SpaceVO spaceVO = bpmsContext.getContent().toJavaObject(new TypeReference<SpaceVO>() {
        });

        return spaceService.update(spaceVO) > 0;
    }
}
