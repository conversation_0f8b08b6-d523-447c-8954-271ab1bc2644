package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/2 16:24
 **/
@Data
public class TagRequest {

    /**
     * 空间Id
     */
    private Long spaceId;

    /**
     * 标签组Id
     */
    private Long groupId;

    /**
     * 标签组名称
     */
    private String groupName;

    /**
     * 标签组描述
     */
    private String description;

    /**
     * 标签名称列表
     */
    private List<String> tagNameList;

    /**
     * 标签类型
     */
    private Integer tagType;

    /**
     * 创建人工号
     */
    private String creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 最后操作人工号
     */
    private String lastOperatorId;

    /**
     * 最后操作人名称
     */
    private String lastOperatorName;

    /**
     * 推送企微
     */
    private Byte push;

}
