package com.alibaba.tripscrm.service.service.impl.second;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fliggy.furl.common.ResultDTO;
import com.alibaba.fliggy.furl.model.FurlCreateModel;
import com.alibaba.fliggy.furl.model.FurlModel;
import com.alibaba.fliggy.furl.service.FurlService;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.AppConstant;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.second.ShortLinkService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-10-26 20:40:11
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ShortLinkServiceImpl implements ShortLinkService {

    // 支付宝短链接相关常量
    private static final String ALIPAY_APP_ID = "200651030429006628";
    private static final String ALIPAY_APP_NAME = "fl-flian";
    private static final String ALIPAY_METHOD_NAME = "materialHandler-getMaterial";
    private static final String ALIPAY_BASE_PARAM_ID = "26";
    private static final String ALIPAY_WORK_ID = "**********";
    private static final String ALIPAY_USER_NAME = "王瑞";

    // JSON字段常量
    private static final String FIELD_ALIPAY = "alipay";
    private static final String FIELD_SHORT_LINK = "shortLink";

    private final FurlService furlService;
    private final com.aliyun.fc.open.api.hsf.HsfGatewayService gatewayService;
    private final AccountService accountService;

    @Override
    @AteyeInvoker(description = "生成微信太阳码scene", paraDesc = "targetUrl")
    public String getUnlimitQrCodeScene(String targetUrl) {
        JSONObject data = new JSONObject();
        data.put("targetUrl", targetUrl);
        Object result = gatewayService.invoke("201703019888404473", "fl-wx-middle", "qrcode-scene-create", data);
        if (Objects.isNull(result)) {
            PlatformLogUtil.logFail("生成微信太阳码scene失败", LogListUtil.newArrayList(targetUrl, result));
            throw new TripscrmException(TripSCRMErrorCode.GENERATE_WECHAT_UN_LIMIT_QR_CODE_FAIL);
        }

        JSONObject jsonResult = JSONObject.parseObject(JSON.toJSONString(result));
        if (!jsonResult.containsKey("data")) {
            PlatformLogUtil.logFail("生成微信太阳码scene失败", LogListUtil.newArrayList(targetUrl, result));
            throw new TripscrmException(TripSCRMErrorCode.GENERATE_WECHAT_UN_LIMIT_QR_CODE_FAIL);
        }
        JSONObject resultData = jsonResult.getJSONObject("data");
        if (Objects.isNull(resultData) || !resultData.containsKey("scene")) {
            PlatformLogUtil.logFail("生成微信太阳码scene失败", LogListUtil.newArrayList(targetUrl, result));
            throw new TripscrmException(TripSCRMErrorCode.GENERATE_WECHAT_UN_LIMIT_QR_CODE_FAIL);
        }

        PlatformLogUtil.logInfo("生成微信太阳码scene", LogListUtil.newArrayList(targetUrl, resultData));
        return resultData.getString("scene");
    }

    /**
     * 获取支付宝短链接
     *
     * @param url      目标URL
     * @param params   参数类型
     * @param pageType 页面类型
     * @return 支付宝短链接
     */
    @Override
    @AteyeInvoker(description = "获取支付宝短链接", paraDesc = "url&params&pageType")
    public String getAlipayShortUrl(String url, String params, String pageType) {
        JSONObject requestData = buildAlipayRequestData(url, params, pageType);
        Object result = invokeAlipayService(requestData);
        return parseAlipayShortLink(result, url);
    }

    /**
     * 构建支付宝请求数据
     */
    private JSONObject buildAlipayRequestData(String url, String params, String pageType) {
        JSONObject formValue = new JSONObject();
        formValue.put("url", url);
        formValue.put("params", params);
        formValue.put("pageType", pageType);

        JSONObject data = new JSONObject();
        data.put("baseParamId", ALIPAY_BASE_PARAM_ID);
        data.put("formValue", formValue);
        User user = accountService.getUserInWebThread();
        data.put("workid",Objects.isNull(user) ? ALIPAY_WORK_ID : user.getUserId());
        data.put("name", Objects.isNull(user) ? ALIPAY_USER_NAME : user.getUserName());
        data.put("genRecord", false);
        data.put("description", "");
        data.put("extraValue", new JSONObject());
        data.put("isPreview", false);
        data.put("avatarUrl", "");

        return data;
    }

    /**
     * 调用支付宝服务
     */
    private Object invokeAlipayService(JSONObject requestData) {
        Object result = gatewayService.invoke(ALIPAY_APP_ID, ALIPAY_APP_NAME, ALIPAY_METHOD_NAME, requestData);
        if (Objects.isNull(result)) {
            PlatformLogUtil.logFail("获取支付宝短链接失败" , LogListUtil.newArrayList(requestData));
            throw new TripscrmException(TripSCRMErrorCode.GENERATE_ALIPAY_SHORT_LINK_FAIL);
        }
        return result;
    }

    /**
     * 解析支付宝短链接响应
     */
    private String parseAlipayShortLink(Object result, String originalUrl) {
        JSONObject jsonResult = JSONObject.parseObject(JSON.toJSONString(result));

        if (Objects.isNull(jsonResult) || !jsonResult.containsKey(FIELD_ALIPAY)) {
            PlatformLogUtil.logFail("获取支付宝短链接失败,响应格式错误或缺少alipay字段", LogListUtil.newArrayList( originalUrl, result));
            throw new TripscrmException(TripSCRMErrorCode.GENERATE_ALIPAY_SHORT_LINK_FAIL);
        }
        JSONObject alipay = jsonResult.getJSONObject(FIELD_ALIPAY);
        if (Objects.isNull(alipay) || !alipay.containsKey(FIELD_SHORT_LINK)) {
            PlatformLogUtil.logFail("获取支付宝短链接失败,alipay对象为空或缺少shortLink字段", LogListUtil.newArrayList(originalUrl, result));
            throw new TripscrmException(TripSCRMErrorCode.GENERATE_ALIPAY_SHORT_LINK_FAIL);
        }
        return alipay.getString(FIELD_SHORT_LINK);
    }


    @Override
    @AteyeInvoker(description = "短链接转换", paraDesc = "originUrl")
    public String convertToShortUrl(String originUrl) {
        if (EnvUtils.isDaily()) {
            return originUrl;
        }

        FurlCreateModel furlCreateModel = new FurlCreateModel();
        furlCreateModel.setLongLink(originUrl);
        furlCreateModel.setRequestAppName(AppConstant.APP_NAME);
        ResultDTO<FurlModel> resultDTO = furlService.createFurl(furlCreateModel);
        if (Objects.isNull(resultDTO) || !resultDTO.isSuccess()) {
            PlatformLogUtil.logFail("短链接转换失败", LogListUtil.newArrayList(originUrl, resultDTO));
            throw new TripscrmException(TripSCRMErrorCode.GENERATE_SHORT_LINK_FAIL);
        }

        String shortLink = resultDTO.getData().getShortKey();
        PlatformLogUtil.logFail("短链接转换成功", LogListUtil.newArrayList(originUrl, shortLink));
        return shortLink;
    }

}