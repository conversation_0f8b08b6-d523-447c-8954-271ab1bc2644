package com.alibaba.tripscrm.service.service.impl.rule;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.RuleMapper;
import com.alibaba.tripscrm.dal.model.domain.data.MonitorRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.model.domain.data.RuleParam;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleStatusEnum;
import com.alibaba.tripscrm.service.model.domain.query.MonitorRecordQuery;
import com.alibaba.tripscrm.service.model.domain.query.RuleQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.MonitorRecordService;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.alibaba.tripscrm.service.service.strategy.rule.AbstractRuleStrategy;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:37
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RuleServiceImpl implements RuleService {
    private final RuleMapper ruleMapper;
    private final MonitorRecordService monitorRecordService;

    @Override
    public RuleDO selectById(Long id) {
        if (!NumberUtils.validLong(id)) {
            return null;
        }

        return ruleMapper.selectByPrimaryKey(id);
    }

    @Override
    public RuleDO selectByTargetId(Byte type, Byte targetType, String targetId) {
        RuleParam param = new RuleParam();
        RuleParam.Criteria criteria = param.or();
        if (Objects.nonNull(type)) {
            criteria.andTypeEqualTo(type);
        }

        if (StringUtils.hasLength(targetId)) {
            criteria.andTargetIdEqualTo(targetId);
        }

        if (Objects.nonNull(targetType)) {
            criteria.andTargetTypeEqualTo(targetType);
        }
        List<RuleDO> rules = ruleMapper.selectByParam(param);
        if (CollectionUtils.isEmpty(rules)) {
            return null;
        }

        return rules.get(0);
    }

    @Override
    public List<RuleDO> listByTargetIds(Byte type, Byte targetType, List<String> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return Collections.emptyList();
        }
        return ruleMapper.listByTargetId(type, targetType, targetIds);
    }

    @Override
    public List<RuleDO> selectByCondition(RuleQuery query) {
        if (Objects.isNull(query)) {
            return new ArrayList<>();
        }

        RuleParam param = new RuleParam();
        RuleParam.Criteria criteria = param.or();
        if (NumberUtils.validLong(query.getGroupId())) {
            criteria.andGroupIdEqualTo(query.getGroupId());
        }

        if (Objects.nonNull(query.getType())) {
            criteria.andTypeEqualTo(query.getType());
        }

        if (StringUtils.hasLength(query.getName())) {
            criteria.andNameEqualTo(query.getName());
        }

        if (StringUtils.hasLength(query.getTargetId())) {
            criteria.andTargetIdEqualTo(query.getTargetId());
        }

        if (Objects.nonNull(query.getTargetType())) {
            criteria.andTargetTypeEqualTo(query.getTargetType());
        }

        if (Objects.nonNull(query.getEnv())) {
            criteria.andEnvEqualTo(query.getEnv());
        }

        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }

        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }

        return ruleMapper.selectByParam(param);
    }

    @Override
    public PageInfo<RuleDO> pageQuery(RuleQuery query) {
        try {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());

            RuleParam param = new RuleParam();
            RuleParam.Criteria criteria = param.or();
            if (NumberUtils.validLong(query.getId())) {
                criteria.andIdEqualTo(query.getId());
            }

            if (NumberUtils.validLong(query.getGroupId())) {
                criteria.andGroupIdEqualTo(query.getGroupId());
            }

            if (Objects.nonNull(query.getType())) {
                criteria.andTypeEqualTo(query.getType());
            }

            if (StringUtils.hasLength(query.getName())) {
                criteria.andNameLike("%" + query.getName() + "%");
            }

            if (StringUtils.hasLength(query.getTargetId())) {
                criteria.andTargetIdEqualTo(query.getTargetId());
            }

            if (Objects.nonNull(query.getTargetType())) {
                criteria.andTargetTypeEqualTo(query.getTargetType());
            }

            if (Objects.nonNull(query.getCreatorId())) {
                criteria.andCreatorIdEqualTo(query.getCreatorId());
            }

            if (Objects.nonNull(query.getMemberIds())) {
                criteria.andMemberIdsLike("%" + query.getMemberIds() + "%");
            }

            if (Objects.nonNull(query.getCreateStartTime())) {
                criteria.andGmtCreateGreaterThanOrEqualTo(query.getCreateStartTime());
            }

            if (Objects.nonNull(query.getCreateEndTime())) {
                criteria.andGmtCreateLessThanOrEqualTo(query.getCreateEndTime());
            }

            if (Objects.nonNull(query.getEnv())) {
                criteria.andEnvEqualTo(query.getEnv());
            }

            if (Objects.nonNull(query.getTypeList())) {
                criteria.andTypeIn(query.getTypeList());
            }

            criteria.andDeletedEqualTo((byte) 0);
            param.appendOrderByClause(RuleParam.OrderCondition.PRIORITY, RuleParam.SortType.ASC);
            param.appendOrderByClause(RuleParam.OrderCondition.GMTCREATE, RuleParam.SortType.DESC);
            List<RuleDO> list = ruleMapper.selectByParam(param);
            // 结果处理
            if (Objects.isNull(list)) {
                return null;
            }

            return new PageInfo<>(list);
        } catch (Exception e) {
            PlatformLogUtil.logException("分页查询规则数据出错", e.getMessage(), e, LogListUtil.newArrayList(query));
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int create(RuleDO ruleDO) {
        if (Objects.isNull(ruleDO)) {
            return 0;
        }

        RuleQuery query = new RuleQuery();
        query.setName(ruleDO.getName());
        if (RuleTypeEnum.isGroupMonitorType(Integer.valueOf(ruleDO.getType()))) {
            query.setGroupId(ruleDO.getGroupId());
            query.setDeleted(IsDeleteEnum.NO.getCode());
        }
        List<RuleDO> ruleDOS = selectByCondition(query);
        if (!CollectionUtils.isEmpty(ruleDOS)) {
            PlatformLogUtil.logFail("规则创建失败，规则已存在", LogListUtil.newArrayList(ruleDO));
            throw new TripscrmException(TripSCRMErrorCode.RULE_IS_EXIST);
        }

        int effectLines = ruleMapper.insertSelective(ruleDO);
        if (effectLines == 0) {
            return 0;
        }

        AbstractRuleStrategy ruleStrategy = AbstractRuleStrategy.getStrategyByType(ruleDO.getType());
        if (ruleStrategy != null) {
            ruleStrategy.postCreate(ruleDO);
        }
        return effectLines;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(RuleDO ruleDO, boolean doPost) {
        if (Objects.isNull(ruleDO)) {
            return 0;
        }

        if (!NumberUtils.validLong(ruleDO.getId())) {
            return 0;
        }

        if (!doPost) {
            return ruleMapper.updateByPrimaryKeySelective(ruleDO);
        }

        RuleDO oldRuleDO = selectById(ruleDO.getId());

        try {
            int effectLines = ruleMapper.updateByPrimaryKeySelective(ruleDO);
            RuleDO newRuleDO = selectById(ruleDO.getId());
            AbstractRuleStrategy ruleStrategy = AbstractRuleStrategy.getStrategyByType(oldRuleDO.getType());
            if (ruleStrategy != null) {
                ruleStrategy.postUpdate(oldRuleDO, newRuleDO);
            }
            return effectLines;
        } catch (Exception e) {
            PlatformLogUtil.logException("规则表数据更新出错", e.getMessage(), e, LogListUtil.newArrayList(ruleDO, doPost));
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        if (!NumberUtils.validLong(id)) {
            return 0;
        }

        RuleDO ruleDO = selectById(id);
        if (Objects.isNull(ruleDO)) {
            return 0;
        }

        // 如果如果type是群监控则
        if (RuleTypeEnum.isGroupMonitorType(Integer.valueOf(ruleDO.getType()))) {
            // 检查id是否存在monitor_record的bizId中
            MonitorRecordQuery monitorRecordQuery = new MonitorRecordQuery();
            monitorRecordQuery.setBizId(String.valueOf(ruleDO.getId()));
            monitorRecordQuery.setDeleted(IsDeleteEnum.NO.getCode());
            List<MonitorRecordDO> monitorRecordDOList = monitorRecordService.selectByCondition(monitorRecordQuery);

            // 如果存在说明关联黑名单，无法删除
            boolean isAssociatedWithBlacklist = monitorRecordDOList.stream()
                    .flatMap(monitorRecordDO -> Arrays.stream(monitorRecordDO.getBizId().split(",")))
                    .anyMatch(bizId -> Objects.equals(bizId, String.valueOf(ruleDO.getId())));

            if (isAssociatedWithBlacklist) {
                throw new TripscrmException(TripSCRMErrorCode.RULE_ASSOCIATION_MONITOR_RECORD);
            }
        }
        // 只有编辑中才可以删除
        if (!Objects.equals(RuleStatusEnum.EDITING, RuleStatusEnum.getByStatus(ruleDO.getStatus()))) {
            throw new TripscrmException(TripSCRMErrorCode.RULE_STATUS_NOT_EDITING);
        }

        int effectLines = ruleMapper.deleteByPrimaryKey(id);
        if (effectLines == 0) {
            return 0;
        }
        if (!RuleTypeEnum.isGroupMonitorType(Integer.valueOf(ruleDO.getType()))) {
            AbstractRuleStrategy.getStrategyByType(ruleDO.getType()).postDelete(ruleDO);
        }
        return effectLines;
    }

    @AteyeInvoker(description = "删除规则（谨慎）", paraDesc = "id")
    public int deleteForce(Long id) {
        if (!NumberUtils.validLong(id)) {
            return 0;
        }
        return ruleMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteByTargetId(String targetId, Integer targetType) {
        RuleParam ruleParam = new RuleParam();
        ruleParam.or().andTargetIdEqualTo(targetId).andTargetTypeEqualTo(targetType.byteValue());
        return ruleMapper.deleteByParam(ruleParam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean online(Long id) {
        if (!NumberUtils.validLong(id)) {
            return false;
        }

        RuleDO ruleDO = selectById(id);
        if (Objects.isNull(ruleDO)) {
            return false;
        }

        // 只有编辑中才可以上线
        if (!Objects.equals(RuleStatusEnum.EDITING, RuleStatusEnum.getByStatus(ruleDO.getStatus()))) {
            throw new TripscrmException(TripSCRMErrorCode.RULE_STATUS_NOT_EDITING);
        }

        RuleDO newRuleDO = new RuleDO();
        newRuleDO.setId(id);
        newRuleDO.setStatus(RuleStatusEnum.ONLINE.getStatus());
        int effectLines = update(newRuleDO, false);
        if (effectLines <= 0) {
            return false;
        }

        AbstractRuleStrategy.getStrategyByType(ruleDO.getType()).postOnline(ruleDO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean offline(Long id) {
        if (!NumberUtils.validLong(id)) {
            return false;
        }

        RuleDO ruleDO = selectById(id);
        if (Objects.isNull(ruleDO)) {
            return false;
        }

        // 只有已上线才可以上线
        if (!Objects.equals(RuleStatusEnum.ONLINE, RuleStatusEnum.getByStatus(ruleDO.getStatus()))) {
            throw new TripscrmException(TripSCRMErrorCode.RULE_STATUS_NOT_ONLINE);
        }

        RuleDO newRuleDO = new RuleDO();
        newRuleDO.setId(id);
        newRuleDO.setStatus(RuleStatusEnum.EDITING.getStatus());
        int effectLines = update(newRuleDO, false);
        if (effectLines <= 0) {
            return false;
        }

        AbstractRuleStrategy.getStrategyByType(ruleDO.getType()).postOffline(ruleDO);
        return true;
    }

    @Override
    @Cacheable(key = "'MonitorRecord_' + 'type:' + #type + ',deleted:' + #deleted + ',groupId:' + #groupId", value = "oneMinutesAnd128MaximumSizeCacheManager")
    public List<RuleDO> selectByTypeAndDeletedWithCache(Byte type, Byte deleted, Long groupId) {

        RuleParam param = new RuleParam();
        RuleParam.Criteria criteria = param.or();

        // deleted = 0;
        if (Objects.nonNull(type)) {
            criteria.andTypeEqualTo(type);
        }

        if (Objects.nonNull(deleted)) {
            criteria.andDeletedEqualTo(deleted);
        }

        if (Objects.nonNull(groupId)) {
            criteria.andGroupIdEqualTo(groupId);
        }
        return ruleMapper.selectByParam(param);
    }

}
