package com.alibaba.tripscrm.service.service.material;

import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.subscribemsg.SubscribeMsgTemplateDTO;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/26 15:17
 */
public interface MaterialTransferService {
    /**
     * 素材构建成微信消息BO对象
     *
     * @param materailInfoDO           素材对象
     * @param materialTrackRelationDTO 素材-trackId关系
     * @param context                  素材上下文
     * @param taskType                 调用任务类型
     * @return 微信消息BO对象
     */
    List<WxMessageBO> buildWxMessages(MaterailInfoDO materailInfoDO, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, String taskType);

    /**
     * 构建比邻消息BO对象
     *
     * @param materailInfoDO           素材对象
     * @param materialTrackRelationDTO 素材-trackId关系
     * @param context                  素材上下文
     * @param taskType                 调用任务类型
     * @return 比邻消息BO对象
     */
    List<MessageBO> buildMessages(MaterailInfoDO materailInfoDO, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, String taskType);

    /**
     * 获取素材供给列表
     * @param materialContent 素材内容
     * @return
     */
    List<MaterialSupplyDTO> extractMaterialSupply(String materialContent);

    /**
     * 构建小程序订阅消息对象
     *
     * @param materialId               素材id
     * @param materialTrackRelationDTO 素材-trackId关系
     * @param context                  素材上下文
     * @return
     */
    SubscribeMsgTemplateDTO buildSubscribeMsg(Long materialId, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context);
}
