package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.alipay.AliPayMsgManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.alipay.msg.AliPayGroupMsgRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.csp.courier.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 支付宝群发消息任务执行器
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@Service
public class AliPayGroupMsgExecutor extends AbstractTaskExecutor {
    @Autowired
    protected AliPayMsgManager aliPayMsgManager;
    @Autowired
    protected LdbTairManager ldbTairManager;
    @Autowired
    protected MetaqProducer metaqProducer;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        // 从todoTaskVO中取出任务配置
        List<String> alipayGroupTemplateList;
        Date pushTime;
        String taskName;
        try {
            alipayGroupTemplateList = Arrays.asList(getFinalTargetId(context, taskDataBody).split(","));
            JSONObject extInfo = JSONObject.parseObject(taskDataBody.getExtInfo());
            taskName = taskInfoDO.getName();
            pushTime = getPushTime(taskInfoDO, extInfo.getString(TaskConstant.ALIPAY_TRIGGER_TIME));
        } catch (Exception e) {
            PlatformLogUtil.logException("支付宝群发消息任务执行器执行出错，从todoTaskVO中取出任务配置出错", e.getMessage(), e, LogListUtil.newArrayList(taskDataBody, taskInfoDO.getId()));
            throw new TripscrmException(TripSCRMErrorCode.ALIPAY_MSG_EXT_INFO_INVALID);
        }

        // 素材
        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("支付宝群发消息任务执行器执行失败，素材获取失败", LogListUtil.newArrayList(taskDataBody, taskInfoDO.getId()));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MATERIAL_IN_TASK);
        }


        MaterialContentConvertContext materialContentConvertContext = buildMaterialContentConvertContext(context, todoTaskVO, taskDataBody, materailInfoDO, alipayGroupTemplateList);
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(context, todoTaskVO);

        AliPayGroupMsgRequest request = buildAliPayGroupMsgRequest(context, todoTaskVO, materailInfoDO, materialTrackRelationDTO, materialContentConvertContext, alipayGroupTemplateList, pushTime, taskName);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> aliPayMsgManager.asyncSendGroupMessage(request));
        if (!result.isSuccess()) {
            PlatformLogUtil.logFail("支付宝群发消息任务执行器执行失败，调用支付宝接口失败", LogListUtil.newArrayList(request, result, taskInfoDO.getId()));
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        JSONObject data = new JSONObject();
        data.put("extraInfo", materialContentConvertContext.getExtraInfo());
        data.put("materialId", materailInfoDO.getId());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return "";
    }

    private AliPayGroupMsgRequest buildAliPayGroupMsgRequest(TaskExecuteContext context
            , TodoTaskVO todoTaskVO
            , MaterailInfoDO materailInfoDO
            , MaterialTrackRelationDTO materialTrackRelationDTO
            , MaterialContentConvertContext materialContentConvertContext
            , List<String> alipayGroupTemplateIdList
            , Date pushTime
            , String taskName) {
        AliPayGroupMsgRequest request = new AliPayGroupMsgRequest();
        request.setAlipayGroupTemplateIdList(alipayGroupTemplateIdList);
        request.setAssistantMsgName(taskName);
        request.setPublishTime(pushTime);
        request.setMainTaskInstanceId(todoTaskVO.getMainTaskInstanceId());
        request.setAbTestBucketId(getAbTestBucketId(todoTaskVO));
        request.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        request.setMaterailInfoDO(materailInfoDO);
        request.setTaskType(getTaskType());
        request.setMaterialContentConvertContext(materialContentConvertContext);

        // 指定requestId
        if (Objects.nonNull(context.getExtInfo()) && context.getExtInfo().containsKey("requestId")
                && Objects.nonNull(context.getExtInfo().get("requestId"))
                && context.getExtInfo().get("requestId") instanceof String) {
            request.setRequestId(context.getExtInfo().get("requestId").toString());
        }
        return request;
    }

    private MaterialTrackRelationDTO buildMaterialTrackRelationDTO(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(getScene());
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setTargetId(getFinalTargetId(context, todoTaskVO.getData().get(0)));
        return materialTrackRelationDTO;
    }

    private MaterialContentConvertContext buildMaterialContentConvertContext(TaskExecuteContext context, TodoTaskVO todoTaskVO, TaskDataVO.DataBodyVO taskDataBody, MaterailInfoDO materailInfoDO, List<String> alipayGroupTemplateIdList) {
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setAlipayGroupTemplateIdList(alipayGroupTemplateIdList);
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        if (StringUtils.hasLength(taskDataBody.getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(taskDataBody.getExtInfo()));
        }
        materialContentConvertContext.setImageUrlList(context.getImageUrlList());
        materialContentConvertContext.setOriginContent(materailInfoDO.getContent());
        return materialContentConvertContext;
    }


    protected String getScene() {
        return MaterialSendSceneTypeConstant.ALIPAY_GROUP_MSG;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        if (Objects.isNull(context)
                || Objects.isNull(context.getTaskDataVO())
                || CollectionUtils.isEmpty(context.getTaskDataVO().getData())
                || Objects.isNull(context.getTaskDataVO().getData().get(0))) {
            PlatformLogUtil.logFail("获取支付宝群发消息目标id失败，context或taskDataBody为空", LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        TaskDataVO.DataBodyVO taskDataBody = context.getTaskDataVO().getData().get(0);
        String groupTemplateListStr = taskDataBody.getTargetId();
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!StringUtils.hasLength(groupTemplateListStr) || !Objects.equals(targetTypeEnum, ActivityTargetTypeEnum.ALIPAY_GROUP_TEMPLATE_ID_LIST)) {
            PlatformLogUtil.logFail("获取支付宝群发消息目标id失败，targetId或targetType非法", LogListUtil.newArrayList(context, taskDataBody, groupTemplateListStr, targetTypeEnum));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        return targetTypeEnum;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (Objects.isNull(context) || Objects.isNull(taskDataBody)) {
            PlatformLogUtil.logFail("获取支付宝群发消息目标id失败，context或taskDataBody为空", LogListUtil.newArrayList(context, taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        String groupTemplateListStr = taskDataBody.getTargetId();
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!StringUtils.hasLength(groupTemplateListStr) || !Objects.equals(targetTypeEnum, ActivityTargetTypeEnum.ALIPAY_GROUP_TEMPLATE_ID_LIST)) {
            PlatformLogUtil.logFail("获取支付宝群发消息目标id失败，targetId或targetType非法", LogListUtil.newArrayList(context, taskDataBody, groupTemplateListStr, targetTypeEnum));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        return groupTemplateListStr;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.ALIPAY_GROUP_MSG;
    }

    /**
     * 获取支付宝群发消息推送时间戳
     * @param taskInfoDO             任务信息
     * @param aliPayTriggerTimeValue 支付宝群发消息推送时间信息
     * @return 推送时间
     */
    private Date getPushTime(TaskInfoDO taskInfoDO, String aliPayTriggerTimeValue) {
        if (Objects.isNull(taskInfoDO) || !StringUtils.hasLength(aliPayTriggerTimeValue)) {
            PlatformLogUtil.logFail("获取支付宝群发消息推送时间戳失败，taskInfoDO或aliPayTriggerTimeValue非法", LogListUtil.newArrayList(taskInfoDO, aliPayTriggerTimeValue));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TRIGGER_TIME);
        }
        TaskTriggerTypeEnum taskTriggerTypeEnum = TaskTriggerTypeEnum.getByCode(taskInfoDO.getTriggerType());
        if (Objects.isNull(taskTriggerTypeEnum)) {
            PlatformLogUtil.logFail("获取支付宝群发消息推送时间戳失败，triggerType非法", LogListUtil.newArrayList(taskInfoDO, aliPayTriggerTimeValue, taskInfoDO.getTriggerType()));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TRIGGER_TIME);
        }
        Date pushTime;
        switch (taskTriggerTypeEnum) {
            case SINGLE_EXECUTE:
                try {
                    pushTime = new Date(Long.parseLong(aliPayTriggerTimeValue));
                    return pushTime;
                } catch (Exception e) {
                    PlatformLogUtil.logFail("获取支付宝群发消息推送时间戳失败，aliPayTriggerTimeValue非法", LogListUtil.newArrayList(taskInfoDO, aliPayTriggerTimeValue, taskInfoDO.getTriggerType()));
                    throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TRIGGER_TIME);
                }
            case EXECUTION_PLAN:
                pushTime = new Date(convertTimeToTimestamp(aliPayTriggerTimeValue));
                return pushTime;
            default:
                PlatformLogUtil.logFail("获取支付宝群发消息推送时间戳失败，triggerType非法", LogListUtil.newArrayList(taskInfoDO, aliPayTriggerTimeValue, taskInfoDO.getTriggerType()));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TRIGGER_TIME);
        }
    }

    /**
     * 将时分秒格式的字符串转换为毫秒级时间戳（以今天的日期为基础）
     *
     * @param timeStr 时分秒格式的字符串，例如 "06:00:06"
     * @return 毫秒级时间戳
     */
    private Long convertTimeToTimestamp(String timeStr) {
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss");

        try {
            // 解析时间部分
            Date timePart = timeFormat.parse(timeStr);

            // 获取今天的日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date());

            // 清除时间部分并设置时间为解析的时间
            calendar.clear(Calendar.HOUR_OF_DAY);
            calendar.clear(Calendar.MINUTE);
            calendar.clear(Calendar.SECOND);
            calendar.clear(Calendar.MILLISECOND);

            calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timeStr.substring(0, 2)));
            calendar.set(Calendar.MINUTE, Integer.parseInt(timeStr.substring(3, 5)));
            calendar.set(Calendar.SECOND, Integer.parseInt(timeStr.substring(6, 8)));

            // 获取最终的时间戳
            return calendar.getTimeInMillis();
        } catch (ParseException e) {
            PlatformLogUtil.logException("支付宝群发消息执行器执行失败，周期任务推送时间格式非法", e.getMessage(), e, LogListUtil.newArrayList(timeStr));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TRIGGER_TIME);
        }
    }
}
