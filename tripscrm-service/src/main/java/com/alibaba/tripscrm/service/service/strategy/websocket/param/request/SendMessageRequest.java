package com.alibaba.tripscrm.service.service.strategy.websocket.param.request;

import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import lombok.Data;

/**
 * 发消息 请求体
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class SendMessageRequest {
    /**
     * 会话id 1:群聊会话id，2:私聊时，企微客户的userId，3:私聊时，企微成员的userId
     */
    private String chatId;
    /**
     * 会话类型 1:群聊，2:与企微客户的私聊，3:与企微成员的私聊
     * @see ChatTypeEnum
     */
    private Integer chatType;
    /**
     * 消息类型
     * @see FusionChatMessageTypeEnum
     */
    private String msgType;
    /**
     * 消息内容
     */
    private String msgContent;
    /**
     * 素材id
     */
    private Long materialId;

    public FusionChatMessageBody build() {
        return new FusionChatMessageBody(msgType, msgContent);
    }

}
