package com.alibaba.tripscrm.service.service.impl.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.tddl.ScrmWechatCustomerRecallMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmWechatCustomerRecallDO;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmWechatCustomerRecallParam;
import com.alibaba.tripscrm.service.enums.wechat.RescallStatusTypeEnum;
import com.alibaba.tripscrm.service.model.dto.wechat.ScrmWechatCustomerRecallDTO;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerRecallService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/1/7 下午8:27
 * @Filename：WechatCustomerRecallServiceImpl
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerRecallServiceImpl implements WechatCustomerRecallService {
    private final ScrmWechatCustomerRecallMapper scrmWechatCustomerRecallMapper;

    @Override
    @AteyeInvoker(description = "查询召回好友用户信息", paraDesc = "userId&days&limit")
    public List<ScrmWechatCustomerRecallDTO> selectByUserIdAndDeletTime(String userId, int days, int limit) {
        if (!StringUtils.hasText(userId) || !NumberUtils.validInteger(days) || !NumberUtils.validInteger(limit)) {
            PlatformLogUtil.logFail("查询用户信息参数错误", LogListUtil.newArrayList(userId, days, limit));
            return new ArrayList<>();
        }
        ScrmWechatCustomerRecallParam scrmWechatCustomerRecallParam = new ScrmWechatCustomerRecallParam();
        scrmWechatCustomerRecallParam.createCriteria().andUserIdEqualTo(userId).andDeleteTimeLessThanOrEqualTo(DateUtils.addDays(new Date(), -days));
        scrmWechatCustomerRecallParam.setPage(true);
        scrmWechatCustomerRecallParam.setPageSize(limit);
        scrmWechatCustomerRecallParam.setPageStart(1);
        List<ScrmWechatCustomerRecallDO> scrmWechatCustomerRecallDOS = scrmWechatCustomerRecallMapper.selectByParam(scrmWechatCustomerRecallParam);
        return scrmWechatCustomerRecallDOS.stream().map(this::convert2DTO).collect(Collectors.toList());
    }

    @Override
    public int updateByExternalUserId(String externalUserId, ScrmWechatCustomerRecallDO record) {
        try {
            if (StringUtils.hasText(externalUserId) && Objects.nonNull(record)) {
                ScrmWechatCustomerRecallParam scrmWechatCustomerRecallParam = new ScrmWechatCustomerRecallParam();
                scrmWechatCustomerRecallParam.createCriteria().andExternalUserIdEqualTo(externalUserId);
                return scrmWechatCustomerRecallMapper.updateByParamSelective(record, scrmWechatCustomerRecallParam);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("更新用户信息失败", e.getMessage(), e, LogListUtil.newArrayList(externalUserId, record));
            throw new RuntimeException(e);
        }
        return -1;
    }


    @Override
    public int updateByPrimaryKey(ScrmWechatCustomerRecallDO record) {
        try {
            if (Objects.nonNull(record)) {
                return scrmWechatCustomerRecallMapper.updateByPrimaryKeySelective(record);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("更新用户信息失败", e.getMessage(), e, LogListUtil.newArrayList(record));
            throw new RuntimeException(e);
        }
        return -1;
    }

    @Override
    public List<ScrmWechatCustomerRecallDTO> selectByStatus(RescallStatusTypeEnum status, int pageNum, int pageSize,Long minId) {
        ScrmWechatCustomerRecallParam scrmWechatCustomerRecallParam = new ScrmWechatCustomerRecallParam();
        scrmWechatCustomerRecallParam.appendOrderByClause(ScrmWechatCustomerRecallParam.OrderCondition.ID, ScrmWechatCustomerRecallParam.SortType.ASC);
        scrmWechatCustomerRecallParam.setPage(true);
        scrmWechatCustomerRecallParam.setPageSize(pageSize);
        scrmWechatCustomerRecallParam.setPageStart(pageNum);
        scrmWechatCustomerRecallParam.createCriteria().andStatusEqualTo(status.getType()).andIdGreaterThan(minId);
        List<ScrmWechatCustomerRecallDO> scrmWechatCustomerRecallDOS = scrmWechatCustomerRecallMapper.selectByParam(scrmWechatCustomerRecallParam);
        return scrmWechatCustomerRecallDOS.stream().map(this::convert2DTO).collect(Collectors.toList());
    }

    public ScrmWechatCustomerRecallDTO convert2DTO(ScrmWechatCustomerRecallDO record) {
        ScrmWechatCustomerRecallDTO dto = new ScrmWechatCustomerRecallDTO();
        BeanUtils.copyProperties(record, dto);
        return dto;
    }

    public ScrmWechatCustomerRecallDO convert2DO(ScrmWechatCustomerRecallDTO dto) {
        ScrmWechatCustomerRecallDO record = new ScrmWechatCustomerRecallDO();
        BeanUtils.copyProperties(dto, record);
        return record;
    }


}
