package com.alibaba.tripscrm.service.service.strategy.websocket.param.response;

import lombok.Data;

/**
 * 发起群聊 推送体
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class CreateGroupChatResponse {
    /**
     * 会话id 1:群聊会话id，2:私聊时，企微客户的userId，3:私聊时，企微成员的userId
     */
    private String chatId;
    /**
     * 会话类型 1:群聊，2:与企微客户的私聊，3:与企微成员的私聊
     * @see ChatTypeEnum
     */
    private Integer chatType;
    /**
     * 会话名称
     */
    private String chatName;
    /**
     * 所属组织名称
     */
    private String corpName;
    /**
     * 置顶
     */
    private Integer topNo;
    /**
     * 会话头像
     */
    private String chatAvatar;
    /**
     * 会话更新时间戳（用于会话排序）
     */
    private Long updateTimestamp;
    /**
     * 该群聊是否关注
     */
    private Boolean groupWork;
}
