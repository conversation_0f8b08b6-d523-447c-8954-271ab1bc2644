package com.alibaba.tripscrm.service.service.impl.wechat;

import com.alibaba.tripscrm.dal.mapper.tddl.WechatCustomerAcquisitionLinkMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatCustomerAcquisitionLinkDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatCustomerAcquisitionListQuery;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.convert.WechatUserConverter;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerAcquisitionLinkVO;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationCoverRequest;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.second.ShortLinkService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerAcquisitionService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.CustomerAcquisitionService;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.alibaba.tripzoo.proxy.model.CustomerAcquisitionLinkBO;
import com.alibaba.tripzoo.proxy.request.CreateLinkRequest;
import com.alibaba.tripzoo.proxy.request.DeleteLinkRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-25 20:47:12
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerAcquisitionServiceImpl implements WechatCustomerAcquisitionService {
    private final WechatCustomerAcquisitionLinkMapper wechatCustomerAcquisitionLinkMapper;
    private final ShortLinkService shortLinkService;
    private final WechatUserConverter wechatUserConverter;
    private final CustomerAcquisitionService customerAcquisitionService;
    private final TagRelationService tagRelationService;
    private final WechatUserService wechatUserService;
    private final LdbTairManager tairManager;
    private final AccountService accountService;
    private final SpaceService spaceService;

    @Override
    public List<WechatCustomerAcquisitionLinkDO> selectAll() {
        List<WechatCustomerAcquisitionLinkDO> res = new ArrayList<>();
        for (long minId = 0; ; ) {
            List<WechatCustomerAcquisitionLinkDO> selectResult = wechatCustomerAcquisitionLinkMapper.selectByMinIdLimit(minId, 300);
            if (CollectionUtils.isEmpty(selectResult)) {
                break;
            }

            minId = selectResult.stream().map(WechatCustomerAcquisitionLinkDO::getId).max(Long::compareTo).orElse(0L);
            res.addAll(selectResult);
        }

        return res;
    }

    @Override
    public PageInfo<WechatCustomerAcquisitionLinkVO> pageQuery(WechatCustomerAcquisitionListQuery query) {
        if (StringUtils.isNotBlank(query.getTagId())) {
            ItemTagRelationQuery relationQuery = new ItemTagRelationQuery();
            if (StringUtils.isNumeric(query.getTagId())) {
                relationQuery.setTagId(Long.parseLong(query.getTagId()));
            } else {
                String[] split = query.getTagId().split("_");
                relationQuery.setTagId(Long.parseLong(split[0]));
                relationQuery.setSubCodeList(Lists.newArrayList(split[1]));
            }
            relationQuery.setItemType(BizTypeEnum.CUSTOMER_ACQUISITION_LINK.getCode());
            relationQuery.setPageNum(1);
            relationQuery.setPageSize(100);
            PageInfo<ItemTagRelationDTO> relationPageInfo = tagRelationService.pageQuery(relationQuery);
            List<String> itemIdList = relationPageInfo.getList().stream().map(ItemTagRelationDTO::getItemId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemIdList)) {
                return new PageInfo<>(new ArrayList<>());
            }
            query.setIdList(itemIdList.stream().map(Long::valueOf).collect(Collectors.toList()));
        }

        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<WechatCustomerAcquisitionLinkDO> selectResult = wechatCustomerAcquisitionLinkMapper.select(query);
        if (CollectionUtils.isEmpty(selectResult)) {
            return new PageInfo<>(new ArrayList<>());
        }

        return new PageInfo<>(selectResult.stream().map(this::do2vo).collect(Collectors.toList()));
    }

    @Override
    public Long create(WechatCustomerAcquisitionLinkVO wechatCustomerAcquisitionLinkVO) {
        WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO = vo2do(wechatCustomerAcquisitionLinkVO);
        WechatCustomerAcquisitionLinkDO sameNameInDb = wechatCustomerAcquisitionLinkMapper.selectBySpaceIdAndName(wechatCustomerAcquisitionLinkDO.getSpaceId(), wechatCustomerAcquisitionLinkDO.getName());
        if (Objects.nonNull(sameNameInDb)) {
            throw new RuntimeException("获客链接名称不可重复！");
        }
        CreateLinkRequest request = new CreateLinkRequest();
        String corpId = spaceService.getCorpIdBySpaceId(wechatCustomerAcquisitionLinkVO.getSpaceId());
        request.setCorpId(corpId);
        request.setName(wechatCustomerAcquisitionLinkDO.getName());
        request.setUserIdList(Arrays.stream(wechatCustomerAcquisitionLinkDO.getUserIdList().split(",")).collect(Collectors.toList()));
        ResultDO<CustomerAcquisitionLinkBO> resultDO = customerAcquisitionService.createLink(request);
        if (!resultDO.getSuccess() || Objects.isNull(resultDO.getModel())) {
            PlatformLogUtil.logFail("customerAcquisitionService createLink failed", LogListUtil.newArrayList(wechatCustomerAcquisitionLinkVO, wechatCustomerAcquisitionLinkDO, resultDO));
            throw new RuntimeException("获客链接创建失败！");
        }

        CustomerAcquisitionLinkBO customerAcquisitionLinkBO = resultDO.getModel();
        wechatCustomerAcquisitionLinkDO.setCreateTime(customerAcquisitionLinkBO.getCreateTime());
        wechatCustomerAcquisitionLinkDO.setLinkId(customerAcquisitionLinkBO.getId());
        wechatCustomerAcquisitionLinkDO.setUrl(customerAcquisitionLinkBO.getUrl());
        wechatCustomerAcquisitionLinkDO.setUserIdList(String.join(",", customerAcquisitionLinkBO.getUserIdList()));
        wechatCustomerAcquisitionLinkMapper.insert(wechatCustomerAcquisitionLinkDO);

        if (!NumberUtils.validLong(wechatCustomerAcquisitionLinkDO.getId())) {
            throw new RuntimeException("获客链接数据写入失败！");
        }

        List<ItemTagRelationDTO> itemTagRelationList = convert2TagRelationList(String.valueOf(wechatCustomerAcquisitionLinkDO.getId()), wechatCustomerAcquisitionLinkVO.getTagIdList());
        tagRelationService.batchUpsertSelective(itemTagRelationList);
        return wechatCustomerAcquisitionLinkDO.getId();
    }

    @Override
    public Integer update(WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO, Long spaceId) {
        WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDOInDb = wechatCustomerAcquisitionLinkMapper.selectByPrimaryKey(wechatCustomerAcquisitionLinkDO.getId());
        if (Objects.isNull(wechatCustomerAcquisitionLinkDOInDb) || !Objects.equals(wechatCustomerAcquisitionLinkDOInDb.getSpaceId(), spaceId)) {
            PlatformLogUtil.logFail("wechatCustomerAcquisitionLinkDOInDb is null", LogListUtil.newArrayList(wechatCustomerAcquisitionLinkDO, spaceId));
            return 0;
        }

        CustomerAcquisitionLinkBO customerAcquisitionLinkBO = new CustomerAcquisitionLinkBO();
        customerAcquisitionLinkBO.setId(wechatCustomerAcquisitionLinkDOInDb.getLinkId());
        if (StringUtils.isNotBlank(wechatCustomerAcquisitionLinkDO.getName())) {
            customerAcquisitionLinkBO.setName(wechatCustomerAcquisitionLinkDO.getName());
        }

        if (StringUtils.isNotBlank(wechatCustomerAcquisitionLinkDO.getUserIdList())) {
            customerAcquisitionLinkBO.setUserIdList(Arrays.stream(wechatCustomerAcquisitionLinkDO.getUserIdList().split(",")).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(customerAcquisitionLinkBO.getUserIdList()) || !StringUtils.isEmpty(customerAcquisitionLinkBO.getName())) {
            String corpId = spaceService.getCorpIdBySpaceId(spaceId);
            customerAcquisitionLinkBO.setCorpId(corpId);
            ResultDO<CustomerAcquisitionLinkBO> resultDO = customerAcquisitionService.updateLink(customerAcquisitionLinkBO);
            if (!resultDO.getSuccess() || Objects.isNull(resultDO.getModel())) {
                PlatformLogUtil.logFail("customerAcquisitionService updateLink failed", LogListUtil.newArrayList(wechatCustomerAcquisitionLinkDO, resultDO));
                throw new RuntimeException("获客链接更新失败！");
            }
        }

        return wechatCustomerAcquisitionLinkMapper.updateByPrimaryKey(wechatCustomerAcquisitionLinkDO);
    }

    @Override
    public Integer batchUpdate(List<WechatCustomerAcquisitionLinkVO> wechatCustomerAcquisitionLinkVOList) {
        Integer res = 0;
        for (WechatCustomerAcquisitionLinkVO wechatCustomerAcquisitionLinkVO : wechatCustomerAcquisitionLinkVOList) {
            WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO = new WechatCustomerAcquisitionLinkDO();
            wechatCustomerAcquisitionLinkDO.setId(wechatCustomerAcquisitionLinkVO.getId());
            wechatCustomerAcquisitionLinkDO.setSpaceId(wechatCustomerAcquisitionLinkVO.getSpaceId());
            wechatCustomerAcquisitionLinkDO.setName(wechatCustomerAcquisitionLinkVO.getName());
            wechatCustomerAcquisitionLinkDO.setUserIdList(wechatCustomerAcquisitionLinkVO.getUserIdList());
            wechatCustomerAcquisitionLinkDO.setLastOperatorId(wechatCustomerAcquisitionLinkVO.getLastOperatorId());
            wechatCustomerAcquisitionLinkDO.setIsDeleted((byte) 0);

            Integer lines = update(wechatCustomerAcquisitionLinkDO, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
            res += lines;
            if (lines <= 0) {
                PlatformLogUtil.logFail("updateLink effect line 0", LogListUtil.newArrayList(wechatCustomerAcquisitionLinkDO));
            }

            if (Objects.isNull(wechatCustomerAcquisitionLinkVO.getTagIdList())) {
                continue;
            }

            ItemTagRelationCoverRequest request = new ItemTagRelationCoverRequest();
            request.setItemId(String.valueOf(wechatCustomerAcquisitionLinkDO.getId()));
            request.setItemType(BizTypeEnum.CUSTOMER_ACQUISITION_LINK.getCode());
            request.setTagIdList(wechatCustomerAcquisitionLinkVO.getTagIdList());
            request.setCreatorId(wechatCustomerAcquisitionLinkVO.getLastOperatorId());
            request.setCreatorId(accountService.getUserInWebThread().getUserId());
            request.setCreatorName(accountService.getUserInWebThread().getUserName());
            tagRelationService.cover(request);
        }

        return res;
    }

    @Override
    public WechatCustomerAcquisitionLinkVO getById(Long id, Long spaceId) {
        WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO = wechatCustomerAcquisitionLinkMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wechatCustomerAcquisitionLinkDO) || !Objects.equals(wechatCustomerAcquisitionLinkDO.getSpaceId(), spaceId)) {
            return null;
        }

        return do2vo(wechatCustomerAcquisitionLinkDO);
    }

    @AteyeInvoker(description = "强制删除获客链接数据", paraDesc = "id&spaceId")
    public Integer deleteByIdForce(Long id, Long spaceId) {
        WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO = wechatCustomerAcquisitionLinkMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wechatCustomerAcquisitionLinkDO) || !Objects.equals(wechatCustomerAcquisitionLinkDO.getSpaceId(), spaceId)) {
            PlatformLogUtil.logFail("wechatCustomerAcquisitionLinkDO is null", LogListUtil.newArrayList(id, spaceId));
            return 0;
        }
        DeleteLinkRequest request = new DeleteLinkRequest();
        request.setLinkId(wechatCustomerAcquisitionLinkDO.getLinkId());
        String corpId = spaceService.getCorpIdBySpaceId(spaceId);
        request.setCorpId(corpId);
        ResultDO<Boolean> resultDO = customerAcquisitionService.deleteLink(request);
        if (!resultDO.getSuccess() || Objects.isNull(resultDO.getModel())) {
            PlatformLogUtil.logFail("customerAcquisitionService deleteLink failed", LogListUtil.newArrayList(id, spaceId));
        }

        int updateLines = wechatCustomerAcquisitionLinkMapper.deleteByPrimaryKey(id);
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(id, spaceId, updateLines));
        if (updateLines <= 0) {
            return 0;
        }

        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setDeleted((byte)1);
        ItemTagRelationQuery condition = new ItemTagRelationQuery();
        condition.setItemType(BizTypeEnum.CUSTOMER_ACQUISITION_LINK.getCode());
        condition.setItemId(String.valueOf(id));
        int relationUpdateLines = tagRelationService.updateSelective(itemTagRelationDTO, condition);
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(id, spaceId, relationUpdateLines));
        return updateLines;
    }

    @Override
    public Integer deleteById(Long id, Long spaceId) {
        WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO = wechatCustomerAcquisitionLinkMapper.selectByPrimaryKey(id);
        if (Objects.isNull(wechatCustomerAcquisitionLinkDO) || !Objects.equals(wechatCustomerAcquisitionLinkDO.getSpaceId(), spaceId)) {
            PlatformLogUtil.logFail("wechatCustomerAcquisitionLinkDO is null", LogListUtil.newArrayList(id, spaceId));
            return 0;
        }
        DeleteLinkRequest request = new DeleteLinkRequest();
        request.setLinkId(wechatCustomerAcquisitionLinkDO.getLinkId());
        String corpId = spaceService.getCorpIdBySpaceId(spaceId);
        request.setCorpId(corpId);
        ResultDO<Boolean> resultDO = customerAcquisitionService.deleteLink(request);
        if (!resultDO.getSuccess() || Objects.isNull(resultDO.getModel())) {
            PlatformLogUtil.logFail("customerAcquisitionService deleteLink failed", LogListUtil.newArrayList(id, spaceId));
            throw new RuntimeException("获客链接删除失败！");
        }

        int updateLines = wechatCustomerAcquisitionLinkMapper.deleteByPrimaryKey(id);
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(id, spaceId, updateLines));
        if (updateLines <= 0) {
            return 0;
        }

        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setDeleted((byte)1);
        ItemTagRelationQuery condition = new ItemTagRelationQuery();
        condition.setItemType(BizTypeEnum.CUSTOMER_ACQUISITION_LINK.getCode());
        condition.setItemId(String.valueOf(id));
        int relationUpdateLines = tagRelationService.updateSelective(itemTagRelationDTO, condition);
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(id, spaceId, relationUpdateLines));
        return updateLines;
    }

    @Override
    @AteyeInvoker(description = "获客链接追加自定义参数并转短链接", paraDesc = "originUrl&customerChannel")
    public String convertToShortCustomerUrl(String originUrl, String customerChannel) {
        try {
            String originLink = String.format("%s?customer_channel=%s", originUrl, customerChannel);
            String originLongLink = String.format("weixin://biz/ww/profile/%s", URLEncoder.encode(originLink, StandardCharsets.UTF_8.name()));
            String shortUrl = shortLinkService.convertToShortUrl(originLongLink);
            PlatformLogUtil.logFail("success", LogListUtil.newArrayList(originUrl, customerChannel, shortUrl));
            return shortUrl;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(originUrl, customerChannel));
            return originUrl;
        }
    }

    private List<ItemTagRelationDTO> convert2TagRelationList(String itemId, List<String> tagIdList) {
        if (CollectionUtils.isEmpty(tagIdList)) {
            return new ArrayList<>();
        }

        return tagIdList.stream().map(tagId -> {
            //保存标签关联关系
            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            if (StringUtils.isNumeric(tagId)) {
                itemTagRelationDTO.setTagId(Long.parseLong(tagId));
            } else {
                String[] split = tagId.split("_");
                itemTagRelationDTO.setTagId(Long.parseLong(split[0]));
                itemTagRelationDTO.setSubCode(split[1]);
            }
            itemTagRelationDTO.setItemType(BizTypeEnum.CUSTOMER_ACQUISITION_LINK.getCode());
            itemTagRelationDTO.setItemId(itemId);
            itemTagRelationDTO.setCreatorId(accountService.getUserInWebThread().getUserId());
            itemTagRelationDTO.setCreatorName(accountService.getUserInWebThread().getUserName());
            return itemTagRelationDTO;
        }).collect(Collectors.toList());
    }

    private WechatCustomerAcquisitionLinkVO do2vo(WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO) {
        WechatCustomerAcquisitionLinkVO wechatCustomerAcquisitionLinkVO = new WechatCustomerAcquisitionLinkVO();
        BeanUtils.copyProperties(wechatCustomerAcquisitionLinkDO, wechatCustomerAcquisitionLinkVO);
        List<String> userIdList = Arrays.stream(wechatCustomerAcquisitionLinkDO.getUserIdList().split(",")).collect(Collectors.toList());
        wechatCustomerAcquisitionLinkVO.setUserList(wechatUserService.listByIdAndStatus(userIdList, WechatUserStatusEnum.ACTIVE).stream().map(wechatUserConverter::convert2VO).collect(Collectors.toList()));
        wechatCustomerAcquisitionLinkVO.setCreatorName(accountService.getUserAndAccountIdByAccountId(wechatCustomerAcquisitionLinkDO.getCreatorId()).getNameAndEmpId());
        ItemTagRelationQuery itemTagRelationQuery = new ItemTagRelationQuery();
        itemTagRelationQuery.setItemId(String.valueOf(wechatCustomerAcquisitionLinkDO.getId()));
        itemTagRelationQuery.setItemType(BizTypeEnum.CUSTOMER_ACQUISITION_LINK.getCode());
        List<TagInfoDTO> tagInfoList = tagRelationService.selectTagInfoByCondition(itemTagRelationQuery);
        wechatCustomerAcquisitionLinkVO.setTagIdList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
        wechatCustomerAcquisitionLinkVO.setTagNameList(tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        wechatCustomerAcquisitionLinkVO.setAddCount(Objects.isNull(tairManager.get(TairConstant.LINK_ADD_CLIENT_PREFIX_.concat(wechatCustomerAcquisitionLinkDO.getLinkId()))) ? 0 : (int) tairManager.get("addCount"));
        return wechatCustomerAcquisitionLinkVO;
    }

    private WechatCustomerAcquisitionLinkDO vo2do(WechatCustomerAcquisitionLinkVO wechatCustomerAcquisitionLinkVO) {
        WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO = new WechatCustomerAcquisitionLinkDO();
        BeanUtils.copyProperties(wechatCustomerAcquisitionLinkVO, wechatCustomerAcquisitionLinkDO);
        return wechatCustomerAcquisitionLinkDO;
    }
}
