package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class WechatCustomerRemarkRequest implements Serializable {
    /**
     * 外部成员 externalUserId
     */
    @NotEmpty(message = "客户Id不可为空")
    private List<String> externalUserId;

    /**
     * 企微成员 userId
     */
    @NotBlank(message = "userId不可为空")
    private String userId;

    /**
     * 备注
     */
    @NotBlank(message = "备注不可为空")
    private String remark;
}
