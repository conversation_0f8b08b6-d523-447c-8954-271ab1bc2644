package com.alibaba.tripscrm.service.convert;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TagInfoDO;
import com.alibaba.tripscrm.domain.enums.TagBizTypeEnum;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.tag.TagInfoVO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * Tag相关数据防腐层
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TagConverter {
    /**
     * TagInfoVO 转 TagInfoDTO
     */
    public TagInfoDTO convert2DTO(TagInfoVO tagInfoVO) {
        if (Objects.isNull(tagInfoVO)) {
            return null;
        }
        TagInfoDTO tagInfoDTO = new TagInfoDTO();
        if (StringUtils.hasText(tagInfoVO.getId())) {
            if (tagInfoVO.getId().contains("_")) {
                String[] split = tagInfoVO.getId().split("_");
                tagInfoDTO.setId(Long.valueOf(split[0]));
                tagInfoDTO.setSubCode(split[1]);
            } else {
                tagInfoDTO.setId(Long.valueOf(tagInfoVO.getId()));
            }
        }

        tagInfoDTO.setName(tagInfoVO.getName());
        tagInfoDTO.setDescription(tagInfoVO.getDescription());
        tagInfoDTO.setSpaceId(tagInfoVO.getSpaceId());
        tagInfoDTO.setTagType(tagInfoVO.getTagType());
        tagInfoDTO.setSource(tagInfoVO.getSource());
        tagInfoDTO.setCreatorId(tagInfoVO.getCreatorId());
        tagInfoDTO.setCreatorName(tagInfoVO.getCreatorName());
        tagInfoDTO.setLastOperatorId(tagInfoVO.getLastOperatorId());
        tagInfoDTO.setLastOperatorName(tagInfoVO.getLastOperatorName());
        tagInfoDTO.setGroupId(tagInfoVO.getGroupId());
        tagInfoDTO.setParentId(tagInfoVO.getParentId());
        return tagInfoDTO;
    }

    /**
     * TagInfoDTO 转 TagInfoVO
     */
    public TagInfoVO convert2VO(TagInfoDTO tagInfoDTO) {
        if (Objects.isNull(tagInfoDTO)) {
            return null;
        }
        TagInfoVO tagInfoVO = new TagInfoVO();
        if (StringUtils.hasText(tagInfoDTO.getSubCode())) {
            tagInfoVO.setId(tagInfoDTO.getId() + "_" + tagInfoDTO.getSubCode());
        } else {
            tagInfoVO.setId(String.valueOf(tagInfoDTO.getId()));
        }
        tagInfoVO.setName(tagInfoDTO.getName());
        tagInfoVO.setDescription(tagInfoDTO.getDescription());
        tagInfoVO.setSpaceId(tagInfoDTO.getSpaceId());
        tagInfoVO.setTagType(tagInfoDTO.getTagType());
        tagInfoVO.setSource(tagInfoDTO.getSource());
        tagInfoVO.setCreatorId(tagInfoDTO.getCreatorId());
        tagInfoVO.setCreatorName(tagInfoDTO.getCreatorName());
        tagInfoVO.setLastOperatorId(tagInfoDTO.getLastOperatorId());
        tagInfoVO.setLastOperatorName(tagInfoDTO.getLastOperatorName());
        tagInfoVO.setGroupId(tagInfoDTO.getGroupId());
        tagInfoVO.setParentId(tagInfoDTO.getParentId());
        tagInfoVO.setGmtCreate(tagInfoDTO.getGmtCreate());
        tagInfoVO.setGmtModified(tagInfoDTO.getGmtModified());
        tagInfoVO.setTagBizTypeCode(tagInfoDTO.getTagBizTypeEnum().getCode());
        return tagInfoVO;
    }

    /**
     * TagInfoDTO 转 TagInfoDO
     */
    public TagInfoDO convert2DO(TagInfoDTO tagInfoDTO) {
        if (Objects.isNull(tagInfoDTO)) {
            return null;
        }
        TagInfoDO tagInfoDO = new TagInfoDO();
        tagInfoDO.setId(tagInfoDTO.getId());
        tagInfoDO.setDeleted(tagInfoDTO.getDeleted());
        tagInfoDO.setGmtCreate(tagInfoDTO.getGmtCreate());
        tagInfoDO.setGmtModified(tagInfoDTO.getGmtModified());
        tagInfoDO.setName(tagInfoDTO.getName());
        tagInfoDO.setDescription(tagInfoDTO.getDescription());
        tagInfoDO.setSpaceId(tagInfoDTO.getSpaceId());
        tagInfoDO.setTagType(tagInfoDTO.getTagType());
        tagInfoDO.setSource(tagInfoDTO.getSource());
        tagInfoDO.setCreatorId(tagInfoDTO.getCreatorId());
        tagInfoDO.setCreatorName(tagInfoDTO.getCreatorName());
        tagInfoDO.setLastOperatorId(tagInfoDTO.getLastOperatorId());
        tagInfoDO.setLastOperatorName(tagInfoDTO.getLastOperatorName());
        tagInfoDO.setGroupId(tagInfoDTO.getGroupId());
        tagInfoDO.setParentId(tagInfoDTO.getParentId());
        return tagInfoDO;
    }

    /**
     * TagInfoDO 转 TagInfoDTO
     */
    public TagInfoDTO convert2DTO(TagInfoDO tagInfoDO) {
        if (Objects.isNull(tagInfoDO)) {
            return null;
        }

        TagInfoDTO tagInfoDTO = new TagInfoDTO();
        tagInfoDTO.setId(tagInfoDO.getId());
        tagInfoDTO.setDeleted(tagInfoDO.getDeleted());
        tagInfoDTO.setGmtCreate(tagInfoDO.getGmtCreate());
        tagInfoDTO.setGmtModified(tagInfoDO.getGmtModified());
        tagInfoDTO.setName(tagInfoDO.getName());
        tagInfoDTO.setDescription(tagInfoDO.getDescription());
        tagInfoDTO.setSpaceId(tagInfoDO.getSpaceId());
        tagInfoDTO.setTagType(tagInfoDO.getTagType());
        tagInfoDTO.setSource(tagInfoDO.getSource());
        tagInfoDTO.setCreatorId(tagInfoDO.getCreatorId());
        tagInfoDTO.setCreatorName(tagInfoDO.getCreatorName());
        tagInfoDTO.setLastOperatorId(tagInfoDO.getLastOperatorId());
        tagInfoDTO.setLastOperatorName(tagInfoDO.getLastOperatorName());
        tagInfoDTO.setGroupId(tagInfoDO.getGroupId());
        tagInfoDTO.setParentId(tagInfoDO.getParentId());

        TagTypeEnum tagTypeEnum = TagTypeEnum.of(tagInfoDO.getTagType());

        switch (tagTypeEnum) {
            case CUSTOMER:
                if (Objects.equals(SwitchConfig.SCRM_BASE_SPACE_ID, tagInfoDO.getSpaceId())) {
                    tagInfoDTO.setTagBizTypeEnum(TagBizTypeEnum.SYSTEM);
                    break;
                }
                tagInfoDTO.setTagBizTypeEnum(TagBizTypeEnum.CUSTOMER);
                break;
            case DIVISION_CODE:
                tagInfoDTO.setTagBizTypeEnum(TagBizTypeEnum.SYSTEM);
                break;
            case CROWD_TAG:
                tagInfoDTO.setTagBizTypeEnum(TagBizTypeEnum.CROWD_TAG);
                break;
            case ENTERPRISE_WECHAT_TAG:
                tagInfoDTO.setTagBizTypeEnum(TagBizTypeEnum.ENTERPRISE_WECHAT_TAG);
                break;
            default:
                PlatformLogUtil.logFail("标签业务类型转换失败", LogListUtil.newArrayList(tagTypeEnum));
                throw new TripscrmException(TripSCRMErrorCode.DB_FAILED);
        }
        return tagInfoDTO;
    }
}
