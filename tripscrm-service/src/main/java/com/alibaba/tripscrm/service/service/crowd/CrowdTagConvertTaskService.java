package com.alibaba.tripscrm.service.service.crowd;

import com.alibaba.tripscrm.service.model.domain.query.CrowdTagConvertTaskQuery;
import com.alibaba.tripscrm.service.model.dto.crowd.CrowdTagConvertTaskDTO;
import com.alibaba.tripscrm.service.model.request.crowd.CrowdTagConvertRequest;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 人群标签转换任务服务接口
 *
 * <AUTHOR>
 * @create 2025/10/11 17:41
 */
public interface CrowdTagConvertTaskService {

    /**
     * 创建人群标签转换任务
     *
     * @param request 创建请求
     * @return 任务信息
     */
    CrowdTagConvertTaskDTO createTask(CrowdTagConvertRequest request);

    /**
     * 查询人群标签转换任务
     *
     * @param query 查询条件
     * @return 任务列表
     */
    List<CrowdTagConvertTaskDTO> queryTasks(CrowdTagConvertTaskQuery query);

    /**
     * 分页查询人群标签转换任务
     *
     * @param query 查询条件（包含分页参数）
     * @return 分页结果
     */
    PageInfo<CrowdTagConvertTaskDTO> queryTasksWithPage(CrowdTagConvertTaskQuery query);

    /**
     * 根据ID查询人群标签转换任务
     *
     * @param id 任务ID
     * @return 任务信息
     */
    CrowdTagConvertTaskDTO getTaskById(Long id);

    /**
     * 更新任务状态
     *
     * @return 是否更新成功
     */
    Boolean updateTaskStatus(CrowdTagConvertTaskDTO updateDTO);

    /**
     * 统计人群标签转换任务数量
     *
     * @param query 查询条件
     * @return 任务数量
     */
    Long countTasks(CrowdTagConvertTaskQuery query);

    /**
     * 根据ID删除人群标签转换任务
     *
     * @param id 任务ID
     * @return 任务信息
     */
    Integer deleteTaskById(Long id);
}