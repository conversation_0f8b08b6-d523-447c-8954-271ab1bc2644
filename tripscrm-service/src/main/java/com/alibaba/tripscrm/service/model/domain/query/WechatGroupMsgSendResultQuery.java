package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WechatGroupMsgSendResultQuery implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 删除状态，0：正常，1：删除
     */
    private Byte deleted;

    /**
     * 企业Id
     */
    private String corpId;

    /**
     * 企业群发消息的id，可用于获取企业群发成员执行结果
     */
    private String msgId;

    /**
     * 群发任务的目标类型：0-客户，1-客户群
     */
    private Byte targetType;

    /**
     * 群发任务的目标Id：0-客户externalUserId，1-客户群chatId
     */
    private String targetId;

    /**
     * 企微成员userId
     */
    private String userId;

    /**
     * 发送状态：0-未发送 1-已发送 2-因客户不是好友导致发送失败 3-因客户已经收到其他群发消息导致发送失败
     */
    private Byte status;
}