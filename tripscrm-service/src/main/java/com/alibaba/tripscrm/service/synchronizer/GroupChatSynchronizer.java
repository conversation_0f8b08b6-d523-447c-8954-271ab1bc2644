package com.alibaba.tripscrm.service.synchronizer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.MemberInfoDTO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.convert.AlipayGroupConverter;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.domain.query.GroupRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupNewQuery;
import com.alibaba.tripscrm.service.model.dto.group.GroupRelationDTO;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupDTO;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupSynchronizerDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.group.GroupNewService;
import com.alibaba.tripscrm.service.service.group.GroupRelationNewService;
import com.alibaba.tripzoo.proxy.api.service.alipay.group.AlipayGroupService;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupInstanceInfoDTO;
import com.alibaba.tripzoo.proxy.request.alipay.GroupInstanceInfoQueryRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.common.utils.DateUtils;
import com.taobao.uic.common.service.userinfo.client.UicPaymentAccountReadServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 群聊信息同步器
 * 继承BaseLockedSynchronizer，实现群聊信息和群成员信息的同步
 *
 * <AUTHOR>
 * @since 2025/1/13
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupChatSynchronizer extends BaseLockedSynchronizer<WechatGroupSynchronizerDTO> {
    private final GroupRelationNewService groupRelationNewService;
    private final GroupNewService groupNewService;
    private final AlipayGroupService alipayGroupService;
    private final AlipayGroupConverter alipayGroupConverter;


    @Override
    public void process(BaseSynchronizerContext<WechatGroupSynchronizerDTO> context) {
        try {
            // 参数校验
            if (!paramVerify(context)) {
                PlatformLogUtil.logFail("群聊信息同步器参数校验失败", LogListUtil.newArrayList(context));
                return;
            }
            WechatGroupSynchronizerDTO data = context.getData();

            GroupInstanceInfoQueryRequest queryRequest = new GroupInstanceInfoQueryRequest();
            queryRequest.setGroupInstanceId(data.getChatId());
            queryRequest.setNeedMember(true);
            ResultDO<GroupInstanceInfoDTO> groupInstanceInfoDTOResultDO = alipayGroupService.queryGroupInstanceInfo(queryRequest);
            if (!groupInstanceInfoDTOResultDO.getSuccess()) {
                PlatformLogUtil.logFail("查询群聊信息失败", LogListUtil.newArrayList(queryRequest, groupInstanceInfoDTOResultDO));
                return;
            }
            WechatGroupDTO newValue = alipayGroupConverter.convert2WechatGroupDTO(groupInstanceInfoDTOResultDO.getModel());
            // 同步群聊信息
            syncChatInfo(newValue);
            data.setGroupName(newValue.getName());

            if (groupInstanceInfoDTOResultDO.getSuccess() && Objects.nonNull(groupInstanceInfoDTOResultDO.getModel()) && !CollectionUtils.isEmpty(groupInstanceInfoDTOResultDO.getModel().getMemberList())) {
                // 同步群聊成员信息
                List<MemberInfoDTO> memberInfoList = groupInstanceInfoDTOResultDO.getModel().getMemberList().stream().map(alipayGroupConverter::convert2MemberInfoDTO).collect(Collectors.toList());
                syncChatMembers(data,memberInfoList);
            }



        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.SYNCHRONIZER_ERROR.getDescCn(),
                    e.getMessage(), e, LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.SYNCHRONIZER_ERROR);
        }
    }

    /**
     * 同步群聊信息
     */
    private void syncChatInfo(WechatGroupDTO newValue) {

        try {
            PlatformLogUtil.logInfo("开始同步群聊信息", LogListUtil.newArrayList(newValue));

            WechatGroupNewQuery query = new WechatGroupNewQuery();
            query.setChatId(newValue.getChatId());
            query.setCorpId(newValue.getCorpId());
            query.setPlatformType(newValue.getPlatformType());
            WechatGroupDTO oldValue = Optional.ofNullable(groupNewService.select(query))
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0)).orElse(null);
            //群信息不存在 走新增逻辑
            if (Objects.isNull(oldValue)) {
                PlatformLogUtil.logInfo("开始同步群组信息-新增", LogListUtil.newArrayList(newValue));

                if (groupNewService.insertSelective(newValue) <= 0) {
                    PlatformLogUtil.logFail("群组信息新增失败", LogListUtil.newArrayList(newValue));
                }
                return;
            }
            //群组信息更新 如果群组信息没有改变,直接返回
            Boolean isChange = newValue.isDiff(oldValue);
            if (!isChange) {
                return;
            }
            newValue.setId(oldValue.getId());
            PlatformLogUtil.logInfo("开始同步群组信息-更新", LogListUtil.newArrayList(newValue));

            if (groupNewService.updateSelective(newValue) <= 0) {
                PlatformLogUtil.logFail("群组信息更新失败", LogListUtil.newArrayList(newValue));
            }

        } catch (Exception e) {
            PlatformLogUtil.logException("同步群聊信息异常", e.getMessage(), e, LogListUtil.newArrayList(newValue));
        }
    }

    /**
     * 同步群成员信息
     */
    private void syncChatMembers(WechatGroupSynchronizerDTO data, List<MemberInfoDTO> memberList) {
        //1. 查询DB群成员信息
        GroupRelationQuery query = new GroupRelationQuery();
        query.setShardingKey("2_" + data.getChatId());
        query.setChatId(data.getChatId());
        query.setCorpId(data.getCorpId());
        query.setPlatformType(data.getPlatformType());
        List<GroupRelationDTO> select = groupRelationNewService.select(query);

        //2.对比userId 分出新增成员
        List<MemberInfoDTO> newMembers = getNewMembers(select, memberList);

        //3.新增成员
        if (CollectionUtils.isEmpty(newMembers)) {
           return;
        }
        batchInsertNewMembers(data, newMembers);

    }


    /**
     * 获取新增成员列表
     *
     * @param dbMembers 数据库中的成员列表
     * @param remoteMembers 远程获取的成员列表
     * @return 新增的成员列表
     */
    private List<MemberInfoDTO> getNewMembers(List<GroupRelationDTO> dbMembers, List<MemberInfoDTO> remoteMembers) {

        // 如果数据库中没有成员，则所有远程成员都是新增的
        if (CollectionUtils.isEmpty(dbMembers)) {
            return new ArrayList<>(remoteMembers);
        }

        // 构建数据库成员ID集合
        Set<String> dbMemberIds = dbMembers.stream()
                .map(GroupRelationDTO::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 筛选出新增的成员
        return remoteMembers.stream()
                .filter(member -> Objects.nonNull(member.getUserId()))
                .filter(member -> !dbMemberIds.contains(member.getUserId()))
                .collect(Collectors.toList());
    }


    /**
     * 批量插入新增成员
     *
     * @param data 群组同步数据
     * @param newMembers 新增成员列表
     */
    private void batchInsertNewMembers(WechatGroupSynchronizerDTO data, List<MemberInfoDTO> newMembers) {
        PlatformLogUtil.logInfo("开始批量插入新增成员", LogListUtil.newArrayList(
                "chatId", data.getChatId(),
                "newMemberCount", newMembers.size()
        ));

        try {
            List<GroupRelationDTO> insertList = newMembers.stream()
                    .map(member -> convertToGroupRelationDTO(data, member))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            //再存一份ShardingKey为1_userId
            List<GroupRelationDTO> insertList2 = newMembers.stream()
                    .map(member ->{
                        GroupRelationDTO groupRelationDTO = convertToGroupRelationDTO(data, member);
                        groupRelationDTO.setShardingKey("1_"+ groupRelationDTO.getUserId());
                        return groupRelationDTO;
                    } )
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            insertList.addAll(insertList2);

            if (!CollectionUtils.isEmpty(insertList)) {
                // 批量插入
                Integer insertedCount = groupRelationNewService.batchInsert(insertList);
                PlatformLogUtil.logInfo("批量插入新增成员完成", LogListUtil.newArrayList(
                        "chatId", data.getChatId(),
                        "attemptInsertCount", insertList.size(),
                        "actualInsertCount", insertedCount
                ));
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("批量插入新增成员异常", e.getMessage(), e, LogListUtil.newArrayList(
                    "chatId", data.getChatId(),
                    "newMemberCount", newMembers.size()
            ));
        }
    }

    /**
     * 将MemberInfoDTO转换为GroupRelationDTO
     *
     * @param data 群组同步数据
     * @param member 成员信息
     * @return GroupRelationDTO
     */
    private GroupRelationDTO convertToGroupRelationDTO(WechatGroupSynchronizerDTO data, MemberInfoDTO member) {
        if (Objects.isNull(member)) {
            return null;
        }

        GroupRelationDTO dto = new GroupRelationDTO();
        dto.setShardingKey("2_" + data.getChatId());
        dto.setChatId(data.getChatId());
        dto.setCorpId(data.getCorpId());
        dto.setPlatformType(data.getPlatformType());
        dto.setUserId(member.getUserId());
        dto.setName(member.getUserName());
        dto.setNickname(member.getUserName());
        Date joinTime = DateUtils.parseDateTime(member.getJoinTime());
        dto.setJoinTime(joinTime);
        dto.setJoinScene(member.getJoinScene().byteValue());
        dto.setInvitor(member.getInviteId());
        dto.setGroupName(data.getGroupName());
        // 默认为活跃状态
        dto.setStatus((byte)1);
        // 默认为未删除
        dto.setIsDeleted(( byte)0);
        dto.setExtraInfo(member.getExtraInfo());
        return dto;
    }

    /**
     * 参数校验
     */
    private boolean paramVerify(BaseSynchronizerContext<WechatGroupSynchronizerDTO> context) {
        if (Objects.isNull(context) || Objects.isNull(context.getData())) {
            PlatformLogUtil.logFail("群聊信息同步器上下文为空", LogListUtil.newArrayList(context));
            return false;
        }

        WechatGroupSynchronizerDTO data = context.getData();

        if (!StringUtils.hasText(data.getCorpId()) || !StringUtils.hasText(data.getChatId()) || Objects.isNull(data.getPlatformType())) {
            PlatformLogUtil.logFail("群聊信息同步器参数校验失败", LogListUtil.newArrayList(context));
            return false;
        }

        return true;
    }

    @Override
    public String getLockKey(BaseSynchronizerContext<WechatGroupSynchronizerDTO> context) {
        return TairConstant.CHAT_SYNCHRONIZER_LOCK_PREFIX + context.getData().getPlatformType().getCode() + "_" + context.getData().getChatId();

    }
}
