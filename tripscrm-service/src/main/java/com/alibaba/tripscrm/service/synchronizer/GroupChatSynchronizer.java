package com.alibaba.tripscrm.service.synchronizer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.MemberInfoDTO;
import com.alibaba.tripscrm.service.constant.AlipayConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.convert.AlipayGroupConverter;
import com.alibaba.tripscrm.service.enums.alipay.AlipayGroupChangeTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.domain.query.GroupRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupNewQuery;
import com.alibaba.tripscrm.service.model.dto.group.GroupRelationDTO;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupDTO;
import com.alibaba.tripscrm.service.model.dto.group.ChatSynchronizerDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.group.GroupNewService;
import com.alibaba.tripscrm.service.service.group.GroupRelationNewService;
import com.alibaba.tripzoo.proxy.api.service.alipay.group.AlipayGroupService;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupInstanceInfoDTO;
import com.alibaba.tripzoo.proxy.request.alipay.GroupInstanceInfoQueryRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 群聊信息同步器
 * 继承BaseLockedSynchronizer，实现群聊信息和群成员信息的同步
 *
 * <AUTHOR>
 * @since 2025/1/13
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupChatSynchronizer extends BaseLockedSynchronizer<ChatSynchronizerDTO> {
    private final GroupRelationNewService groupRelationNewService;
    private final GroupNewService groupNewService;
    private final AlipayGroupService alipayGroupService;
    private final AlipayGroupConverter alipayGroupConverter;


    @Override
    public void process(BaseSynchronizerContext<ChatSynchronizerDTO> context) {
        try {
            // 参数校验
            if (!paramVerify(context)) {
                PlatformLogUtil.logFail("群聊信息同步器参数校验失败", LogListUtil.newArrayList(context));
                return;
            }
            ChatSynchronizerDTO data = context.getData();
            //如果是解散群聊的变更,就不能再查支付宝的信息了
            if (AlipayGroupChangeTypeEnum.DEL_GROUP.equals(data.getChangeType())) {
                //删除群聊
                deleteSyncChat(data);
            }
            GroupInstanceInfoQueryRequest queryRequest = new GroupInstanceInfoQueryRequest();
            queryRequest.setGroupInstanceId(data.getChatId());
            queryRequest.setNeedMember(true);
            ResultDO<GroupInstanceInfoDTO> groupInstanceInfoDTOResultDO = alipayGroupService.queryGroupInstanceInfo(queryRequest);
            if (!groupInstanceInfoDTOResultDO.getSuccess()) {
                PlatformLogUtil.logFail("查询群聊信息失败", LogListUtil.newArrayList(queryRequest, groupInstanceInfoDTOResultDO));
                return;
            }
            WechatGroupDTO newValue = alipayGroupConverter.convert2WechatGroupDTO(groupInstanceInfoDTOResultDO.getModel());

            // 同步群聊信息
            Boolean isNewChat = syncChatInfo(newValue);
            data.setGroupName(newValue.getName());

            //退群成员
            if (StringUtils.hasText(context.getUserId()) && !CollectionUtils.isEmpty(context.getExtraInfo()) && Objects.equals(AlipayGroupChangeTypeEnum.DEL_MEMBER.getCode(), context.getExtraInfo().get(AlipayConstant.CHANGE_TYPE))) {
                PlatformLogUtil.logInfo("退群成员", LogListUtil.newArrayList(context));
                leaveSyncChatMember(context);
                return;
            }

            if (groupInstanceInfoDTOResultDO.getSuccess() && Objects.nonNull(groupInstanceInfoDTOResultDO.getModel()) && !CollectionUtils.isEmpty(groupInstanceInfoDTOResultDO.getModel().getMemberList())) {
                // 同步群聊成员信息
                List<MemberInfoDTO> memberInfoList = Optional.ofNullable(context.getUserId())
                        .filter(StringUtils::hasText)
                        //如果新群,就算是加好友变更,也需要同步所有群成员
                        .filter(userId -> !isNewChat)
                        .map(userId -> groupInstanceInfoDTOResultDO.getModel().getMemberList().stream()
                                .filter(member -> member.getUserId().equals(userId))
                                .findFirst()
                                .map(alipayGroupConverter::convert2MemberInfoDTO)
                                .orElse(null))
                        .map(Collections::singletonList)
                        .orElse(groupInstanceInfoDTOResultDO.getModel().getMemberList().stream()
                                .map(alipayGroupConverter::convert2MemberInfoDTO)
                                .collect(Collectors.toList()));
                List<GroupRelationDTO> memberDtoList = memberInfoList.stream()
                        .map(member -> convertToGroupRelationDTO(data, member))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                syncChatMembers(data, memberDtoList, Objects.nonNull(context.getUserId()));
            }


        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.SYNCHRONIZER_ERROR.getDescCn(),
                    e.getMessage(), e, LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.SYNCHRONIZER_ERROR);
        }
    }

    private void deleteSyncChat(ChatSynchronizerDTO data) {

        try {
            PlatformLogUtil.logInfo("开始解散群聊信息", LogListUtil.newArrayList(data));

            WechatGroupNewQuery query = new WechatGroupNewQuery();
            query.setChatId(data.getChatId());
            query.setCorpId(data.getCorpId());
            query.setPlatformType(data.getPlatformType());
            WechatGroupDTO oldValue = Optional.ofNullable(groupNewService.select(query))
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0)).orElse(null);
            //群信息不存在
            if (Objects.isNull(oldValue)) {
                PlatformLogUtil.logInfo("开始解散群聊信息未查到群聊", LogListUtil.newArrayList(query));
                return;
            }
            oldValue.setIsDeleted(IsDeleteEnum.YES.getCode());
            PlatformLogUtil.logInfo("解散群聊-更新", LogListUtil.newArrayList(oldValue));

            if (groupNewService.updateSelective(oldValue) <= 0) {
                PlatformLogUtil.logFail("解散群聊-更新", LogListUtil.newArrayList(oldValue));
            }

            GroupRelationQuery relationQuery = new GroupRelationQuery();
            relationQuery.setShardingKey(chatId2ShardingKey(data.getChatId()));
            relationQuery.setChatId(data.getChatId());
            relationQuery.setCorpId(data.getCorpId());
            relationQuery.setPlatformType(data.getPlatformType());
            List<GroupRelationDTO> oldMemberList = groupRelationNewService.select(relationQuery);
            if (CollectionUtils.isEmpty(oldMemberList)) {
                return;
            }
            oldMemberList.stream().forEach(member -> {
                member.setStatus(GroupRelationStatusEnum.NOT_MEMBER.getCode().byteValue());
                member.setLeaveTime(new Date());
                member.setIsDeleted(IsDeleteEnum.YES.getCode());
            });
            batchUpdateMembers(oldMemberList);
        } catch (Exception e) {
            PlatformLogUtil.logException("同步群聊信息异常", e.getMessage(), e, LogListUtil.newArrayList(data));
        }


    }

    private void leaveSyncChatMember(BaseSynchronizerContext<ChatSynchronizerDTO> context) {
        Date changeTime = DateUtils.parse(context.getExtraInfo().get(AlipayConstant.CHANGE_TIME));
        ChatSynchronizerDTO data = context.getData();
        //1. 查询DB群成员信息
        GroupRelationQuery query = new GroupRelationQuery();
        query.setShardingKey(chatId2ShardingKey(data.getChatId()));
        query.setChatId(data.getChatId());
        query.setCorpId(data.getCorpId());
        query.setUserId(context.getUserId());
        query.setPlatformType(data.getPlatformType());
        List<GroupRelationDTO> oldMemberList = groupRelationNewService.select(query);
        GroupRelationDTO member = Optional.ofNullable(oldMemberList).orElse(Collections.emptyList()).stream().filter(item -> item.getUserId().equals(context.getUserId())).findFirst().orElse(null);
        member.setStatus(GroupRelationStatusEnum.NOT_MEMBER.getCode().byteValue());
        member.setLeaveTime(changeTime);
        batchUpdateMembers(Collections.singletonList(member));
    }

    /**
     * 同步群聊信息
     */
    private Boolean syncChatInfo(WechatGroupDTO newValue) {

        try {
            PlatformLogUtil.logInfo("开始同步群聊信息", LogListUtil.newArrayList(newValue));

            WechatGroupNewQuery query = new WechatGroupNewQuery();
            query.setChatId(newValue.getChatId());
            query.setCorpId(newValue.getCorpId());
            query.setPlatformType(newValue.getPlatformType());
            WechatGroupDTO oldValue = Optional.ofNullable(groupNewService.select(query))
                    .filter(list -> !list.isEmpty())
                    .map(list -> list.get(0)).orElse(null);
            //群信息不存在 走新增逻辑
            if (Objects.isNull(oldValue)) {
                PlatformLogUtil.logInfo("开始同步群组信息-新增", LogListUtil.newArrayList(newValue));

                if (groupNewService.insertSelective(newValue) <= 0) {
                    PlatformLogUtil.logFail("群组信息新增失败", LogListUtil.newArrayList(newValue));
                }
                return true;
            }

            //群组信息更新 如果群组信息没有改变,直接返回
            Boolean isChange = newValue.isDiff(oldValue);
            if (!isChange) {
                return false;
            }
            newValue.setId(oldValue.getId());
            PlatformLogUtil.logInfo("开始同步群组信息-更新", LogListUtil.newArrayList(newValue));

            if (groupNewService.updateSelective(newValue) <= 0) {
                PlatformLogUtil.logFail("群组信息更新失败", LogListUtil.newArrayList(newValue));
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("同步群聊信息异常", e.getMessage(), e, LogListUtil.newArrayList(newValue));
        }
        return false;
    }

    /**
     * 同步群成员信息
     */
    private void syncChatMembers(ChatSynchronizerDTO data, List<GroupRelationDTO> memberList, Boolean isAddMember) {
        //1. 查询DB群成员信息
        GroupRelationQuery query = new GroupRelationQuery();
        query.setShardingKey(chatId2ShardingKey(data.getChatId()));
        query.setChatId(data.getChatId());
        query.setCorpId(data.getCorpId());
        query.setPlatformType(data.getPlatformType());
        List<GroupRelationDTO> oldMemberList = groupRelationNewService.select(query);

        //2.对比userId 分出新增成员 删除成员
        List<GroupRelationDTO> newMembers = getNewMembers(oldMemberList, memberList);

        List<GroupRelationDTO> updateMembers = getUpdateMembers(oldMemberList, memberList);
        if (!isAddMember) {
            List<GroupRelationDTO> deleteMembers = getDeleteMembers(oldMemberList, memberList);
            updateMembers.addAll(deleteMembers);
        }

        //3.新增成员
        if (!CollectionUtils.isEmpty(newMembers)) {
            batchInsertNewMembers(newMembers);
        }
        //4.更新成员
        if (!CollectionUtils.isEmpty(updateMembers)) {
            batchUpdateMembers(updateMembers);
        }

    }

    private void batchUpdateMembers(List<GroupRelationDTO> updateMembers) {


        try {

            if (CollectionUtils.isEmpty(updateMembers)) {
                return;
            }
            // 批量更新
            for (GroupRelationDTO member : updateMembers) {
                GroupRelationQuery query = new GroupRelationQuery();
                query.setChatId(member.getChatId());
                query.setUserId(member.getUserId());
                query.setCorpId(member.getCorpId());
                query.setPlatformType(member.getPlatformType());
                query.setUserType(member.getUserType());
                List<String> shardingKeys = new ArrayList<>();
                shardingKeys.add(chatId2ShardingKey(member.getChatId()));
                //跟新需要把另一个userId分片也更新掉
                shardingKeys.add(userId2ShardingKey(member.getUserId()));
                query.setShardingKeyList(shardingKeys);
                Integer i = groupRelationNewService.updateSelective(member, query);
                if (i <= 0) {
                    PlatformLogUtil.logFail("群成员信息更新失败", LogListUtil.newArrayList(member));
                }
            }

        } catch (Exception e) {
            PlatformLogUtil.logException("批量插入新增成员异常", e.getMessage(), e, LogListUtil.newArrayList(updateMembers
            ));
        }

    }

    private List<GroupRelationDTO> getUpdateMembers(List<GroupRelationDTO> oldMemberList, List<GroupRelationDTO> memberList) {
        List<GroupRelationDTO> updateMembers = new ArrayList<>();
        //1.获取旧成员ID
        Map<String, GroupRelationDTO> oldMembers = oldMemberList.stream().collect(Collectors.toMap(GroupRelationDTO::getUserId, item -> item));

        updateMembers = memberList.stream()
                .filter(member -> Objects.nonNull(member.getUserId()))
                .filter(member -> (
                        oldMembers.containsKey(member.getUserId()) && oldMembers.get(member.getUserId()).isDiff(member)
                )).map(member -> {
                    member.setId(oldMembers.get(member.getUserId()).getId());
                    return member;
                })
                .collect(Collectors.toList());
        return updateMembers;
    }


    private List<GroupRelationDTO> getDeleteMembers(List<GroupRelationDTO> oldMemberList, List<GroupRelationDTO> memberList) {
        List<GroupRelationDTO> deleteMembers = new ArrayList<>();
        Set<String> newMemberIds = memberList.stream()
                .map(GroupRelationDTO::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        deleteMembers = oldMemberList.stream()
                .filter(oldMember -> !newMemberIds.contains(oldMember.getUserId()))
                .filter(oldMember -> !Objects.equals(GroupRelationStatusEnum.NOT_MEMBER.getCode().byteValue(), oldMember.getStatus()))
                .map(oldMember -> {
                    oldMember.setStatus(Byte.valueOf("2"));
                    oldMember.setLeaveTime(new Date());
                    return oldMember;
                }).collect(Collectors.toList());
        return deleteMembers;
    }


    /**
     * 获取新增成员列表
     *
     * @param dbMembers     数据库中的成员列表
     * @param remoteMembers 远程获取的成员列表
     * @return 新增的成员列表
     */
    private List<GroupRelationDTO> getNewMembers(List<GroupRelationDTO> dbMembers, List<GroupRelationDTO> remoteMembers) {

        // 如果数据库中没有成员，则所有远程成员都是新增的
        if (CollectionUtils.isEmpty(dbMembers)) {
            return new ArrayList<>(remoteMembers);
        }

        // 构建数据库成员ID集合
        Set<String> dbMemberIds = dbMembers.stream()
                .map(GroupRelationDTO::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 筛选出新增的成员
        return remoteMembers.stream()
                .filter(member -> Objects.nonNull(member.getUserId()))
                .filter(member -> !dbMemberIds.contains(member.getUserId()))
                .collect(Collectors.toList());
    }


    /**
     * 批量插入新增成员
     *
     * @param newMembers 新增成员列表
     */
    private void batchInsertNewMembers(List<GroupRelationDTO> newMembers) {

        try {
            if (CollectionUtils.isEmpty(newMembers)) {
                return;
            }
            // 批量插入
            Integer insertedCount = groupRelationNewService.batchInsert(newMembers);
            PlatformLogUtil.logInfo("批量插入新增成员完成", LogListUtil.newArrayList(newMembers.size(), insertedCount));
        } catch (Exception e) {
            PlatformLogUtil.logException("批量插入新增成员异常", e.getMessage(), e, LogListUtil.newArrayList(
                    newMembers.size()
            ));
        }
    }

    /**
     * 将MemberInfoDTO转换为GroupRelationDTO
     *
     * @param data   群组同步数据
     * @param member 成员信息
     * @return GroupRelationDTO
     */
    private GroupRelationDTO convertToGroupRelationDTO(ChatSynchronizerDTO data, MemberInfoDTO member) {
        if (Objects.isNull(member)) {
            return null;
        }

        GroupRelationDTO dto = new GroupRelationDTO();
        dto.setShardingKey(chatId2ShardingKey(data.getChatId()));
        dto.setChatId(data.getChatId());
        dto.setCorpId(data.getCorpId());
        dto.setPlatformType(data.getPlatformType());
        dto.setUserId(member.getUserId());
        dto.setName(member.getUserName());
        dto.setNickname(member.getUserName());
        dto.setUserType(GroupUserTypeEnum.ALIPAY_USER.getCode().byteValue());
        Date joinTime = DateUtils.parseDateTime(member.getJoinTime());
        dto.setJoinTime(joinTime);
        dto.setJoinScene(member.getJoinScene().byteValue());
        dto.setInvitor(member.getInviteId());
        dto.setGroupName(data.getGroupName());
        // 默认为活跃状态
        dto.setStatus(GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        // 默认为未删除
        dto.setIsDeleted(IsDeleteEnum.NO.getCode());
        dto.setExtraInfo(member.getExtraInfo());
        return dto;
    }

    private String userId2ShardingKey(String userId) {
        return "1_" + userId;
    }

    private String chatId2ShardingKey(String chatId) {
        return "2_" + chatId;
    }

    /**
     * 参数校验
     */
    private boolean paramVerify(BaseSynchronizerContext<ChatSynchronizerDTO> context) {
        if (Objects.isNull(context) || Objects.isNull(context.getData())) {
            PlatformLogUtil.logFail("群聊信息同步器上下文为空", LogListUtil.newArrayList(context));
            return false;
        }

        ChatSynchronizerDTO data = context.getData();

        if (!StringUtils.hasText(data.getCorpId()) || !StringUtils.hasText(data.getChatId()) || Objects.isNull(data.getPlatformType())) {
            PlatformLogUtil.logFail("群聊信息同步器参数校验失败", LogListUtil.newArrayList(context));
            return false;
        }

        return true;
    }

    @Override
    public String getLockKey(BaseSynchronizerContext<ChatSynchronizerDTO> context) {
        return TairConstant.CHAT_SYNCHRONIZER_LOCK_PREFIX + context.getData().getPlatformType().getCode() + "_" + context.getData().getChatId();

    }
}