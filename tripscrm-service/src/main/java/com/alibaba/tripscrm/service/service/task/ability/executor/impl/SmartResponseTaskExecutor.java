package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.KnowledgeDocumentDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.enums.task.SmartResponseResultType;
import com.alibaba.tripscrm.service.enums.task.SmartResponseTaskParamKey;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.dto.MessageRelationInfoDTO;
import com.alibaba.tripscrm.service.model.domain.fusionchat.message.TextMessage;
import com.alibaba.tripscrm.service.model.dto.knowledge.KnowledgeDocumentDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.task.SmartResponseTaskExtInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO.DataBodyVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.knowledge.KnowledgeDocumentService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxCustomerSendMsgRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.testng.collections.Lists;

/**
 * 智能回复任务执行器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SmartResponseTaskExecutor extends AbstractTaskExecutor {
    private final WechatUserService wechatUserService;
    private final CustomerService customerService;
    private final MessageUtils messageUtils;
    private final MaterialService materialService;
    private final KnowledgeDocumentService knowledgeDocumentService;
    private final MaterialTransferService materialTransferService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<DataBodyVO> dataBodyList = todoTaskVO.getData();
        if (CollectionUtils.isEmpty(dataBodyList)) {
            PlatformLogUtil.logFail("智能回复数据为空", LogListUtil.newArrayList(todoTaskVO.getTaskId(), todoTaskVO));
            return;
        }
        // 参数校验
        if (!paramVerify(dataBodyList.get(0), context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            return;
        }
        // 是否跳过
        if (skipProcess(dataBodyList.get(0), context, todoTaskVO)) {
            todoTaskVO.setSuccess(true);
            todoTaskVO.setFailReason("不符合执行条件");
            return;
        }
        // 埋点日志
        PlatformLogUtil.logFail("智能回复匹配成功，开始执行发送数据", LogListUtil.newArrayList(context.getExtInfo(), todoTaskVO));
        // 业务处理
        businessProcess(context, todoTaskVO);
    }

    private boolean paramVerify(TaskDataVO.DataBodyVO dataBodyVO, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 目标信息需要存在（群/客户）
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(dataBodyVO.getTargetType());
        if (targetTypeEnum == null) {
            PlatformLogUtil.logFail("智能回复目标类型不存在", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            return false;
        }
        if (StringUtils.isBlank(dataBodyVO.getTargetId())) {
            PlatformLogUtil.logFail("智能回复目标ID不存在", LogListUtil.newArrayList(context.getTaskId(), dataBodyVO));
            return false;
        }
        // 任务空间校验
        Long taskBelongSpace = getTaskBelongSpace(context, todoTaskVO);
        if (taskBelongSpace == null) {
            PlatformLogUtil.logFail("智能回复找不到任务对应的空间", LogListUtil.newArrayList(context.getTaskId(), context, dataBodyVO));
            return false;
        }
        // 目标空间校验
        Long targetBelongSpace = wechatUserService.getSpaceId(getSendUserId(context, todoTaskVO), SpaceInfoThreadLocalUtils.getCorpId());
        if (targetBelongSpace == null) {
            PlatformLogUtil.logFail("智能回复找不到目标对应的空间", LogListUtil.newArrayList(context.getTaskId(), context, dataBodyVO));
            return false;
        }
        // 任务空间和成员空间统一性判断
        if (!taskBelongSpace.equals(targetBelongSpace)) {
            PlatformLogUtil.logFail("智能回复，空间不一致", LogListUtil.newArrayList(context.getTaskId(), context, dataBodyVO));
            return false;
        }
        // 发送人校验
        if (StringUtils.isBlank(getSendUserId(context, todoTaskVO))) {
            PlatformLogUtil.logFail("智能回复发送人为空", LogListUtil.newArrayList(context.getTaskId(), context, dataBodyVO));
            return false;
        }
        // 获取任务结果参数
        Map<String, Object> taskResultParam = getTaskResultParam(context.getExtInfo());
        if (MapUtils.isEmpty(taskResultParam)) {
            PlatformLogUtil.logFail("智能回复任务结果参数为空", LogListUtil.newArrayList(context.getTaskId(), context, dataBodyVO));
            return false;
        }
        // 获取任务结果回答类型
        String replyType = MapUtils.getString(taskResultParam, "type");
        if (StringUtils.isBlank(replyType)) {
            PlatformLogUtil.logFail("智能回复任务结果回答类型为空", LogListUtil.newArrayList(context.getTaskId(), context, dataBodyVO));
            return false;
        }
        // 获取任务结果回答内容
        String replyContent = MapUtils.getString(taskResultParam, "content");
        if (StringUtils.isBlank(replyContent)) {
            PlatformLogUtil.logFail("智能回复任务结果回答内容为空", LogListUtil.newArrayList(context.getTaskId(), context, dataBodyVO));
            return false;
        }
        return true;
    }

    /**
     * 获取任务的归属空间
     *
     * @param todoTaskVO 任务数据
     * @return 归属空间ID
     */
    private Long getTaskBelongSpace(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (todoTaskVO == null || context == null || context.getTaskInfoDOSnapshot() == null) {
            return null;
        }
        return context.getTaskInfoDOSnapshot().getSpaceId();
    }

    /**
     * 通过任务拓展参数，获取到AI任务结果参数
     *
     * @param extInfo 拓展参数
     * @return 任务结果参数
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getTaskResultParam(Map<String, Object> extInfo) {
        return (Map<String, Object>)MapUtils.getMap(extInfo, SmartResponseTaskParamKey.RESULT_TASK_PARAM.getCode());
    }

    private boolean skipProcess(TaskDataVO.DataBodyVO dataBodyVO, TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 获取智能回复任务配置
        SmartResponseTaskExtInfoDTO smartResponseTaskExt = getSmartResponseConfig(context, todoTaskVO);
        if (smartResponseTaskExt == null) {
            PlatformLogUtil.logFail("智能回复规则为空", LogListUtil.newArrayList(context.getTaskId(), todoTaskVO));
            return true;
        }
        // 判断企微成员是否在生效范围, 如果不是全部生效下，判断企微成员是否在生效范围内
        if (BooleanUtils.isNotTrue(smartResponseTaskExt.getApplyAllUserId()) && !ListUtils.emptyIfNull(smartResponseTaskExt.getApplyUserList()).contains(getSendUserId(context, todoTaskVO))) {
            PlatformLogUtil.logFail("智能回复过滤规则【智能回复任务指定企微成员未匹配】", LogListUtil.newArrayList(smartResponseTaskExt, getSendUserId(context, todoTaskVO)));
            return true;
        }
        return false;
    }

    /**
     * 获取智能回复任务拓展参数
     *
     * @param context    任务执行上下文
     * @param todoTaskVO 任务数据
     * @return 智能回复任务拓展参数
     */
    private SmartResponseTaskExtInfoDTO getSmartResponseConfig(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (todoTaskVO == null || context == null || context.getTaskInfoDOSnapshot() == null) {
            PlatformLogUtil.logFail("智能回复任务数据获取不到", LogListUtil.newArrayList(todoTaskVO));
            return null;
        }
        String extInfo = context.getTaskInfoDOSnapshot().getExtInfo();
        if (StringUtils.isBlank(extInfo)) {
            PlatformLogUtil.logFail("智能回复任务配置获取不到", LogListUtil.newArrayList(todoTaskVO));
            return null;
        }
        return JSONObject.parseObject(extInfo, SmartResponseTaskExtInfoDTO.class);
    }

    /**
     * 业务处理
     *
     * @param context    任务执行上下文
     * @param todoTaskVO 任务数据
     */
    private void businessProcess(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 1. 构造消息发送对象列表
        List<MessageBO> messageList = buildSendMessageBOList(context, todoTaskVO);
        // 1.1 有效数据校验
        if (CollectionUtils.isEmpty(messageList)) {
            PlatformLogUtil.logFail("智能回复消息发送对象列表为空,不自动回复", LogListUtil.newArrayList(todoTaskVO));
            return;
        }
        // 2. 处理消息发送
        asyncSendMessage(context, todoTaskVO, messageList);
    }

    /**
     * 构造消息发送对象列表
     *
     * @param context    任务执行上下文
     * @param todoTaskVO 任务数据
     * @return 消息发送对象列表
     */
    private List<MessageBO> buildSendMessageBOList(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 获取任务结果参数
        Map<String, Object> taskResultParam = getTaskResultParam(context.getExtInfo());
        // 获取任务结果回答类型
        String replyType = MapUtils.getString(taskResultParam, "type");
        String replyContent = MapUtils.getString(taskResultParam, "content");
        // 获取回答类型枚举
        SmartResponseResultType resultType = SmartResponseResultType.findByCode(replyType);
        if (Objects.isNull(resultType)) {
            PlatformLogUtil.logFail("智能回复任务结果回答类型不存在", LogListUtil.newArrayList(todoTaskVO));
            throw new TripscrmException(ErrorCodeEnum.SMART_RESPONSE_TASK_RESULT_REPLY_TYPE_MISSED);
        }
        switch (resultType) {
            case USER_RE_CONFIRM:
            case USER_FINISH:
                return Collections.singletonList(buildCommonTextMessageBO(context, todoTaskVO, replyContent));
            case LAST_REPLY:
                return buildLastReplyMessageBOList(context, todoTaskVO, replyContent);
            case REJECT:
                // 配置拒绝策略时，按照拒绝策略回复
                Long spaceId = getTaskBelongSpace(context, todoTaskVO);
                if (Objects.nonNull(spaceId) && SwitchConfig.SMART_RESPONSE_REJECT_STRATEGY_CONTENT.containsKey(spaceId)) {
                    return Collections.singletonList(buildCommonTextMessageBO(context, todoTaskVO, SwitchConfig.SMART_RESPONSE_REJECT_STRATEGY_CONTENT.get(spaceId)));
                }
                // 没有配置拒绝策略时，不回复消息
                return Collections.emptyList();
            default:
                throw new TripscrmException(ErrorCodeEnum.SMART_RESPONSE_TASK_RESULT_REPLY_TYPE_UN_SUPPORT);
        }
    }

    /**
     * 构造普通消息发送对象列表
     *
     * @param context      任务执行上下文
     * @param todoTaskVO   任务数据
     * @param replyContent 回答内容
     * @return 消息发送对象列表
     */
    private MessageBO buildCommonTextMessageBO(TaskExecuteContext context, TodoTaskVO todoTaskVO, String replyContent) {
        // 1. 借助聚合聊天工具处理
        // 1.1 构建消息基础对象
        FusionChatMessageBody fusionChatMessageBody = new FusionChatMessageBody();
        fusionChatMessageBody.setMsgType(FusionChatMessageTypeEnum.TEXT.getValue());
        TextMessage textMessage = new TextMessage();
        textMessage.setContent(replyContent);
        fusionChatMessageBody.setMsgContent(JSONObject.toJSONString(textMessage));
        // 1.2 构建消息关系对象
        MessageRelationInfoDTO messageRelationInfoDTO = new MessageRelationInfoDTO(ChatTypeEnum.SINGLE_FOR_CUSTOMER, getFinalTargetId(context, todoTaskVO.getData().get(0)),
            getSendUserId(context, todoTaskVO), null);
        // 2. 构建消息发送对象
        return messageUtils.fusionChatMessage2SendMessage(fusionChatMessageBody, messageRelationInfoDTO);
    }

    /**
     * 异步发送消息
     *
     * @param context     任务执行上下文
     * @param todoTaskVO  任务数据
     * @param messageList 消息发送对象列表
     */
    private void asyncSendMessage(TaskExecuteContext context, TodoTaskVO todoTaskVO, List<MessageBO> messageList) {
        switch (getFinalTargetType(context)) {
            case WX_CHAT_ID:
                // 智能回复群聊待拓展
                break;
            case WX_EXTERNAL_USERID:
                sendMessageToCustomer(context, todoTaskVO, messageList);
                break;
            default:
                break;
        }

    }

    /**
     * 发送消息给客户
     *
     * @param context     任务执行上下文
     * @param todoTaskVO  任务数据
     * @param messageList 消息发送对象列表
     */
    private void sendMessageToCustomer(TaskExecuteContext context, TodoTaskVO todoTaskVO, List<MessageBO> messageList) {
        // 1. 请求构建
        WxCustomerSendMsgRequest wxCustomerSendMsgRequest = buildWxCustomerSendMsgRequest(context, todoTaskVO, messageList);
        // 2. 发起请求
        ResultDO<String> resultDO = customerService.asyncSendMessage(wxCustomerSendMsgRequest);
        // 3. 日志留存
        if (resultDO.getSuccess()) {
            PlatformLogUtil.logInfo("智能回复发送消息调用成功", LogListUtil.newArrayList(wxCustomerSendMsgRequest, resultDO, context, messageList));
        } else {
            PlatformLogUtil.logFail("智能回复发送消息调用失败", LogListUtil.newArrayList(wxCustomerSendMsgRequest, resultDO, context, messageList));
            throw new TripscrmException("智能回复消息发送失败：" + resultDO.getResultMessage());
        }
    }

    /**
     * 构建企微发送消息请求
     *
     * @param context     任务执行上下文
     * @param todoTaskVO  任务数据
     * @param messageList 消息发送对象列表
     * @return 企微发送消息请求
     */
    private WxCustomerSendMsgRequest buildWxCustomerSendMsgRequest(TaskExecuteContext context, TodoTaskVO todoTaskVO, List<MessageBO> messageList) {
        // 发送参数处理
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        // 发送目标对象
        String finalTargetId = getFinalTargetId(context, taskDataBody);
        // 企微成员
        String sendUserId = getSendUserId(context, todoTaskVO);

        WxCustomerSendMsgRequest wxCustomerSendMsgRequest = new WxCustomerSendMsgRequest();
        wxCustomerSendMsgRequest.setUserId(sendUserId);
        wxCustomerSendMsgRequest.setExternalUserId(finalTargetId);
        wxCustomerSendMsgRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wxCustomerSendMsgRequest.setRequestId(UUID.randomUUID().toString());
        wxCustomerSendMsgRequest.setMessageList(messageList);
        return wxCustomerSendMsgRequest;
    }

    /**
     * 构建明确知识文档的回答消息
     *
     * @param context      任务执行上下文
     * @param todoTaskVO   任务数据
     * @param replyContent 回答内容
     * @return 消息发送对象列表
     */
    private List<MessageBO> buildLastReplyMessageBOList(TaskExecuteContext context, TodoTaskVO todoTaskVO, String replyContent) {
        // 获取任务回复数据
        JSONObject knowledgeDocumentJson = JSONObject.parseObject(replyContent);
        if (Objects.isNull(knowledgeDocumentJson)) {
            PlatformLogUtil.logFail("智能回复任务结果回答内容获取不到", LogListUtil.newArrayList(context, todoTaskVO));
            throw new TripscrmException(ErrorCodeEnum.SMART_RESPONSE_TASK_RESULT_REPLY_CONTENT_ERROR);
        }
        // 获取知识文档id
        Long knowledgeDocumentId = knowledgeDocumentJson.getLong("biz_id");
        if (Objects.isNull(knowledgeDocumentId)) {
            PlatformLogUtil.logFail("智能回复任务结果回答内容id获取不到", LogListUtil.newArrayList(context, todoTaskVO));
            throw new TripscrmException(ErrorCodeEnum.SMART_RESPONSE_TASK_RESULT_REPLY_CONTENT_ERROR);
        }
        // 获取知识文档
        KnowledgeDocumentDTO documentDTO = knowledgeDocumentService.selectById(knowledgeDocumentId);
        if (Objects.isNull(documentDTO)) {
            PlatformLogUtil.logFail("智能回复任务结果回答内容id不存在", LogListUtil.newArrayList(context, todoTaskVO));
            throw new TripscrmException(ErrorCodeEnum.SMART_RESPONSE_TASK_RESULT_REPLY_CONTENT_ERROR);
        }
        List<MessageBO> messageBOList = Lists.newArrayList();
        // 处理普通文案回复消息
        if (StringUtils.isNotBlank(documentDTO.getAnswer())) {
            messageBOList.add(buildCommonTextMessageBO(context, todoTaskVO, documentDTO.getAnswer()));
        }
        // 处理素材回复消息
        if (Objects.nonNull(documentDTO.getMaterialId())) {
            messageBOList.addAll(buildMaterialMessageBOList(context, todoTaskVO, documentDTO.getMaterialId()));
        }
        // 重置消息编号
        for (int i = 0; i < messageBOList.size(); i++) {
            messageBOList.get(i).setMsgNum(i + 1);
        }
        return messageBOList;
    }

    /**
     * 构建素材回复消息
     *
     * @param context    任务执行上下文
     * @param todoTaskVO 任务数据
     * @param materialId 素材id
     * @return 消息发送对象列表
     */
    private List<MessageBO> buildMaterialMessageBOList(TaskExecuteContext context, TodoTaskVO todoTaskVO, Long materialId) {
        // 1. 查询素材对象
        MaterailInfoDO materailInfoDO = materialService.queryById(materialId);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("智能回复任务结果回答素材id不存在", LogListUtil.newArrayList(context, todoTaskVO));
            throw new TripscrmException(ErrorCodeEnum.SMART_RESPONSE_TASK_RESULT_MATERIAL_ERROR);
        }
        // 2. 获取发送对象及目标对象
        String sendUserId = getSendUserId(context, todoTaskVO);
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String chatId = getFinalTargetId(context, taskDataBody);

        // 关系构建
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(context, todoTaskVO, MaterialSendSceneTypeConstant.SMART_RESPONSE_SILIAO, sendUserId, chatId, null);
        // 素材上下文
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setWechatUserId(sendUserId);
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        if (StringUtils.isNotBlank(todoTaskVO.getData().get(0).getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo()));
        }
        materialContentConvertContext.setOriginContent(materailInfoDO.getContent());
        return materialTransferService.buildMessages(materailInfoDO, materialTrackRelationDTO, materialContentConvertContext, getTaskType().getCode());
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return MapUtils.getString(context.getExtInfo(), SmartResponseTaskParamKey.SEND_USER_ID.getCode());
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        Integer targetType = MapUtils.getInteger(context.getExtInfo(), SmartResponseTaskParamKey.TARGET_TYPE.getCode());
        return ActivityTargetTypeEnum.codeOf(targetType);
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, DataBodyVO taskDataBody) {
        return MapUtils.getString(context.getExtInfo(), SmartResponseTaskParamKey.TARGET_ID.getCode());
    }

    @Override
    public boolean checkActivityContextValid(TaskExecuteContext context, DataBodyVO taskDataBody) {
        return true;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.SMART_RESPONSE;
    }
}
