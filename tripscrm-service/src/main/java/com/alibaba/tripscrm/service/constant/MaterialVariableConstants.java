package com.alibaba.tripscrm.service.constant;

/**
 * <AUTHOR>
 * @since 2025/3/13 17:29
 */
public interface MaterialVariableConstants {
    /**
     * 国内机票出发日期（yyyy-MM-dd）
     * 机票出发时间
     */
    String DMESTIC_FLIGHT_LEAVE_DATE = "dmesticFlightLeaveDate";

    /**
     * 国内机票出发地（三字码）
     * 机票出发地三字码
     */
    String DMESTIC_FLIGHT_DEP_CITY_CODE3 = "dmesticFlightDepCityCode3";

    /**
     * 国内机票到达地（三字码）
     * 机票到达地三字码
     */
    String DMESTIC_FLIGHT_ARR_CITY_CODE3 = "dmesticFlightArrCityCode3";

    /**
     * 国内酒店目的地城市divisionId（例如30010）
     * 酒店入住城市/机票到达城市/火车票到达城市
     */
    String NON_PACKAGE_HOTEL_DIVISION_ID = "nonPackageHotelDivisionId";

    /**
     * 国内酒店入住时间（yyyy-MM-dd）
     * 酒店入住时间/机票出发时间/火车票出发时间
     */
    String NON_PACKAGE_HOTEL_CHECK_IN = "nonPackageHotelCheckIn";

    /**
     * 国内酒店离店时间（yyyy-MM-dd）
     * 酒店离店时间/机票出发时间+1d/火车票出发时间+1d
     */
    String NON_PACKAGE_HOTEL_CHECK_OUT = "nonPackageHotelCheckOut";

    /**
     * 火车票出发站点
     */
    String TRAIN_TICKET_DEP_LOCATION = "trainTicketDepLocation";

    /**
     * 火车票到达站点
     */
    String TRAIN_TICKET_ARR_LOCATION = "trainTicketArrLocation";

    /**
     * 火车票出发日期
     */
    String TRAIN_TICKET_DEP_DATE = "trainTicketDepDate";

    /**
     * 目的地城市名称
     */
    String ARR_CITY_NAME = "arrCityName";

    /**
     * 入住时间
     */
    String CHECK_IN_DATE = "checkinDate";

    /**
     * 离店时间
     */
    String CHECK_OUT_DATE = "checkoutDate";

    /**
     * 酒店名称
     */
    String HOTEL_NAME = "hotelName";

    /**
     * 酒店地址
     */
    String HOTEL_ADDRESS = "hotelAddress";

    /**
     * 出行人名称
     */
    String PASSENGER_NAME = "passengerName";

    /**
     * 商品名称
     */
    String ITEM_NAME = "itemName";

    /**
     * 数量
     */
    String AMOUNT = "amount";

    /**
     * 出行日期
     */
    String SERVICE_START_TIME = "serviceStartTime";
}
