package com.alibaba.tripscrm.service.service.ma;

import com.alibaba.tripscrm.service.model.vo.wechat.WxGroupInfoVO;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.query.WxGroupQuery;

/**
 * 微信群码服务
 * <AUTHOR>
 * @date 2023/4/10
 */
public interface WxGroupMaService {

    /**
     * 保存群码
     * @param wxGroupInfoVO
     * @return
     */
    PageResultVO<Long> saveGroupMa(WxGroupInfoVO wxGroupInfoVO, User user);

    /**
     * 更新群码
     * @param wxGroupInfoVO
     * @return
     */
    PageResultVO<Long> updateGroupMa(WxGroupInfoVO wxGroupInfoVO, User user);

    /**
     * 删除群码
     * @param wxGroupInfoVO
     * @return
     */
    PageResultVO<Long> delGroupMa(WxGroupInfoVO wxGroupInfoVO, User user);

    /**
     * 群码列表
     * @param query
     * @return
     */
    PageResultVO<WxGroupInfoVO> listGroupMa(WxGroupQuery query);


    /**
     * 通过id获取群码
     * @param id
     * @return
     */
    WxGroupInfoVO queryWxGroupMaInfoByIdAndSpaceId(Long id, Long spaceId);

    /**
     * 通过id获取群码
     * @param id
     * @return
     */
    WxGroupInfoVO queryWxGroupMaInfoById(Long id);

    /**
     * DB物理删除活码
     * @param wxGroupInfoVO
     * @return
     */
    PageResultVO<Long> delGroupMaDB(WxGroupInfoVO wxGroupInfoVO);

    /**
     * 查找第一个未满的群
     */
    WxGroupInfoVO findFirstNotFullGroupMaBySpaceId(String tagId, Long spaceId);

    /**
     * 查找第一个未满的群
     */
    WxGroupInfoVO findFirstNotFullGroupMa(String tagId);

    /**
     * 更新群最大数量
     * @param id
     * @param spaceId
     * @return
     */
    Integer updateGroupMaxNum(Long id, Long spaceId);
}
