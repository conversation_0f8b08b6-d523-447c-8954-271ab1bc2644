package com.alibaba.tripscrm.service.service.strategy.message;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialJsonKeyConstant;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.message.MessageTransferDTO;
import com.alibaba.tripscrm.service.model.dto.message.VideoMessageInfoDTO;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.WxMediaUtils;
import com.alibaba.tripzoo.proxy.enums.AttachmentSceneTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-04 23:32:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class VideoMessageConverter extends AbstractMessageConverter<VideoMessageInfoDTO> {

    private final WxMediaUtils wxMediaUtils;

    @Override
    public VideoMessageInfoDTO convert(MessageTransferDTO messageTransferDTO) {
        VideoMessageInfoDTO videoMessageInfoDTO = new VideoMessageInfoDTO();
        JSONObject messageContent = messageTransferDTO.getContent();
        if (!messageContent.containsKey(MaterialJsonKeyConstant.videoContent)) {
            return videoMessageInfoDTO;
        }
        List<String> urlList = messageContent.getObject(MaterialJsonKeyConstant.videoContent, new TypeReference<List<String>>() {
        });
        videoMessageInfoDTO.setUrlList(urlList);
        if (!messageContent.containsKey(MaterialJsonKeyConstant.videoDuration)) {
            return videoMessageInfoDTO;
        }
        List<Integer> videoDuration = messageContent.getObject(MaterialJsonKeyConstant.videoDuration, new TypeReference<List<Integer>>() {
        });
        videoMessageInfoDTO.setVideoDuration(videoDuration);
        if (!messageContent.containsKey(MaterialJsonKeyConstant.videoPicture)) {
            return videoMessageInfoDTO;
        }
        List<String> picture = messageContent.getObject(MaterialJsonKeyConstant.videoPicture, new TypeReference<List<String>>() {
        });
        videoMessageInfoDTO.setCoverPicture(picture);
        return videoMessageInfoDTO;
    }

    @Override
    public List<MessageBO> buildMessageBO(VideoMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, Integer messageIndex) {
        ArrayList<MessageBO> messageBOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(messageInfoDTO.getUrlList())) {
            return messageBOList;
        }
        for (int i = 0; i < messageInfoDTO.getUrlList().size(); i++) {
            MessageBO messageBO = new MessageBO();
            messageBO.setMsgType(messageInfoDTO.getMessageType());
            messageBO.setMsgNum(messageIndex++);
            messageBO.setHref(messageInfoDTO.getUrlList().get(i));
            if (CollectionUtils.isNotEmpty(messageInfoDTO.getCoverPicture()) && messageInfoDTO.getCoverPicture().size() > i) {
                messageBO.setMsgContent(messageInfoDTO.getCoverPicture().get(i));
            }
            if (CollectionUtils.isNotEmpty(messageInfoDTO.getVideoDuration())) {
                // ISV接口需要时间参数/官方不需要
                messageBO.setVoiceTime(messageInfoDTO.getVideoDuration().get(i));
            }
            messageBOList.add(messageBO);
        }
        return messageBOList;
    }

    @Override
    List<WxMessageBO> buildWxMessageBO(VideoMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, TaskType taskType) {
        ArrayList<WxMessageBO> wxMessageList = new ArrayList<>();
        for (String url : messageInfoDTO.getUrlList()) {
            WxMessageBO wxMessageBO3 = new WxMessageBO();
            wxMessageBO3.setMsgType(WxAttachmentTypeEnum.VIDEO);
            wxMessageBO3.setMediaId(wxMediaUtils.getMediaId(WxAttachmentTypeEnum.VIDEO, TaskType.SEND_TO_MOMENTS.equals(taskType) ? AttachmentSceneTypeEnum.MOMENT : null, url, SpaceInfoThreadLocalUtils.getCorpId(), false));
            wxMessageList.add(wxMessageBO3);
        }
        return wxMessageList;
    }

    @Override
    public MessageTypeEnum getMsgType() {
        return MessageTypeEnum.VIDEO;
    }
}
