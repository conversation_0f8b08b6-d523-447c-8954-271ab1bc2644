package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2023/7/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SendMsgToGroupRequest extends BaseSendWechatMsgRequest {
    private static final long serialVersionUID = -568849163749341420L;
    private String userId;
    private String chatId;
    private MaterailInfoDO materailInfoDO;
    private MaterialTrackRelationDTO materialTrackRelationDTO;
    private MaterialContentConvertContext materialContentConvertContext;
    private TaskType taskType;
    private String requestId;
}
