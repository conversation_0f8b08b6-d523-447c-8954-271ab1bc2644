package com.alibaba.tripscrm.service.service.group;

import com.alibaba.tripscrm.service.model.domain.query.GroupTemplateQuery;
import com.alibaba.tripscrm.service.model.dto.group.GroupTemplateInfoDTO;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;

import java.util.List;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/13 18:56
 * @Filename：GroupTService
 */
public interface GroupTemplateInfoService {

    /**
     * 新增群组模板
     * @param groupTemplateInfoDTO
     * @return
     */
    Integer insertSelective(GroupTemplateInfoDTO groupTemplateInfoDTO);

    /**
     * 查询群组模板
     * @param query
     * @return
     */
    List<GroupTemplateInfoDTO> select(GroupTemplateQuery query);

    /**
     * 分页查询群组模板
     * @param query
     * @return
     */
    PageInfoDTO<GroupTemplateInfoDTO> pageQuery(GroupTemplateQuery query);

    /**
     * 更新群组模板
     * @param groupTemplateInfoDTO
     * @return
     */
    Integer updateSelective(GroupTemplateInfoDTO groupTemplateInfoDTO);

    /**
     * 删除群组模板
     * @param query
     * @return
     */
    Integer delete(GroupTemplateQuery query);
}
