package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.tag.TagSyncTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.dto.tag.WechatTagSynchronizerDTO;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.synchronizer.WechatTagSynchronizer;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 企微标签信息变更【微信回调】
 *
 * <AUTHOR>
 * @date 2024/12/12
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatTagChangeCallbackProcessor implements ProxyCallbackProcessor {
    private final WechatTagSynchronizer wechatTagSynchronizer;
    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.WECHAT_TAG_CHANGE;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (!paramVerify(scrmCallbackMsg)) {
                PlatformLogUtil.logFail("企微标签变更回调参数校验失败", LogListUtil.newArrayList(scrmCallbackMsg));
                return false;
            }
            JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
            BaseSynchronizerContext<WechatTagSynchronizerDTO> context = new BaseSynchronizerContext<>();
            context.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            String tagId = content.getString(CallbackConstant.TAG_ID);
            WechatTagSynchronizerDTO data = new WechatTagSynchronizerDTO();
            data.setTagType(TagSyncTypeEnum.codeOf(content.getString(CallbackConstant.TAG_TYPE)));
            TagSyncTypeEnum tagSyncTypeEnum = TagSyncTypeEnum.codeOf(content.getString(CallbackConstant.TAG_TYPE));
            if (Objects.isNull(tagSyncTypeEnum)) {
                PlatformLogUtil.logFail("企微标签变更回调参数tagType不匹配", LogListUtil.newArrayList(scrmCallbackMsg));
                return false;
            }
            switch (tagSyncTypeEnum) {
                case TAG:
                    context.setTagId(tagId);
                    break;
                case TAG_GROUP:
                    context.setTagGroupId(tagId);
                    break;
                default:
                    PlatformLogUtil.logFail("企微标签变更回调参数tagType不匹配", LogListUtil.newArrayList(scrmCallbackMsg));
                    return false;
            }
            data.setChangeType(content.getString(CallbackConstant.CHANGE_TYPE));
            context.setData(data);
            wechatTagSynchronizer.sync(context);
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.PROXY_CALLBACK_PROCESSOR_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        return true;
    }

    /**
     * 企微标签变更回调参数校验
     *
     * @param scrmCallbackMsg
     * @return
     */
    private Boolean paramVerify(ScrmCallbackMsg scrmCallbackMsg) {
        if (Objects.isNull(scrmCallbackMsg.getContent())) {
            PlatformLogUtil.logFail("企微标签变更回调参数为空", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }

        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());

        if (!content.containsKey(CallbackConstant.CHANGE_TYPE) || !content.containsKey(CallbackConstant.TAG_ID)  || !content.containsKey(CallbackConstant.TAG_TYPE) || !content.containsKey(CallbackConstant.CORP_ID)) {
            PlatformLogUtil.logFail("企微标签变更回调参数缺失", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }

        if (!StringUtils.pathEquals(SwitchConfig.ADD_FRIENDS_CALL_USER_ID_CORP_ID, content.getString(CallbackConstant.CORP_ID))) {
            PlatformLogUtil.logFail("企微标签回调参数corpId不匹配", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }

        return true;
    }
}
