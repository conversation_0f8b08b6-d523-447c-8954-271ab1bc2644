package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @date 2023-07-17 15:50:16
 */
@Data
public class TagInfoSearchQuery {
    /**
     * 标签类型，1普通，2行政区划
     */
    @NotNull(message = "标签类型不可为空")
    @Min(value = 2, message = "标签类型非法")
    private Integer tagType;

    /**
     * 虚拟标签-子标签搜索关键词
     */
    @NotBlank(message = "关键词不可为空")
    private String keyword;
}
