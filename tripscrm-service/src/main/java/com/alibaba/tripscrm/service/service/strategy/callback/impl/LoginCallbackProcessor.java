package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.request.RiskOnlineTimeRequest;
import com.alibaba.tripscrm.service.service.risk.controller.RiskRobotOnlineTimeController;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.alibaba.tripscrm.service.enums.system.MQEnum.WECHAT_USER_STATUS;

/**
 * 机器人登录
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class LoginCallbackProcessor implements ProxyCallbackProcessor {
    private final RiskRobotOnlineTimeController riskRobotOnlineTimeController;

    @Resource
    private MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.LOGIN_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("result failed");
            return true;
        }
        PlatformLogUtil.logFail("机器人上线回调");
        String userId = scrmCallbackMsg.getContent();
        String corpId = scrmCallbackMsg.getPlatformCorpId();
        // 风控上线
        JSONObject message = new JSONObject();
        message.put("userId", userId);
        message.put("corpId", corpId);
        message.put("robotStatus", RobotStatusEnum.ONLINE.getCode());
        metaqProducer.send(WECHAT_USER_STATUS,null,corpId, JSON.toJSONString(message));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("wechatUserId", userId);
        metaqProducer.send(MQEnum.SCRM_SELLER_CORP_VALID_PLATFORM_WECHAT_USER_SYNC, null, null, jsonObject.toJSONString());

        riskRobotOnlineTimeController.judgeIntoProtect(new RiskOnlineTimeRequest(corpId, userId, null));
        return true;
    }
}
