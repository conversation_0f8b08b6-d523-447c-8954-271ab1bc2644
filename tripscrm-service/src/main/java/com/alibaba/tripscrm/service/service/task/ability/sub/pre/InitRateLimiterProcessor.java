package com.alibaba.tripscrm.service.service.task.ability.sub.pre;

import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 任务执行_子任务_设置限流器
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class InitRateLimiterProcessor implements ExecuteProcessor {
    @Override
    @TaskExecuteLog("任务执行_子任务_设置限流器")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        // 设置任务执行限流器
        initRateLimiter(context);
        return TripSCRMResult.success(null);
    }

    private void initRateLimiter(TaskExecuteContext context) {
        AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(TaskType.getByCode(context.getTaskInfoDOSnapshot().getType()));
        context.setRateLimiter(taskExecutor.getRateLimiter(context));
        PlatformLogUtil.logInfo("子任务执行-设置限流器", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
    }
}
