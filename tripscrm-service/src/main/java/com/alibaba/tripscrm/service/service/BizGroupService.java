package com.alibaba.tripscrm.service.service;

import com.alibaba.tripscrm.dal.model.domain.data.BizGroupInfoDO;
import com.alibaba.tripscrm.service.model.vo.BizGroupVO;

import java.util.List;

/**
 * 业务分组管理服务
 * <AUTHOR>
 * @date 2023/3/31
 */
public interface BizGroupService {
    /**
     * 全部分组
     * @return
     */
    List<BizGroupVO> listBizGroup();

    /**
     * 添加分组
     * @param bizGroupInfoDO
     * @return
     */
    int addBizGroup(BizGroupInfoDO bizGroupInfoDO);


    /**
     * 修改分组
     * @param bizGroupInfoDO
     * @return
     */
    int updateBizGroup(BizGroupInfoDO bizGroupInfoDO);

    /**
     * 删除分组
     * @param id
     * @return
     */
    int delBizGroup(Long id);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    BizGroupInfoDO queryBizGroupById(Long id);

    /**
     * 根据name查询
     * @param name
     * @return
     */
    BizGroupInfoDO queryBizGroupByName(String name);

}
