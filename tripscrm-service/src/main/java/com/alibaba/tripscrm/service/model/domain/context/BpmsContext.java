package com.alibaba.tripscrm.service.model.domain.context;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.tripscrm.service.enums.second.BpmsTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023-08-21 21:37:40
 */
@Slf4j
@Data
public class BpmsContext {
    /**
     * UUID
     */
    private String id = UUID.randomUUID().toString();

    /**
     * 创建者员工号
     */
    private String creatorId;

    /**
     * SCRM 业务空间申请
     */
    private transient String title;

    /**
     * 审批类型
     */
    private BpmsTypeEnum bpmsTypeEnum;

    /**
     * 数据内容
     */
    private JSONObject content;

    /**
     * 初始化数据
     */
    @JSONField(serialize = false)
    private transient Map<String, String> initData = new HashMap<>();
}
