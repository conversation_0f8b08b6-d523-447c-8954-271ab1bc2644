package com.alibaba.tripscrm.service.service.strategy.rule.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.request.TripSCRMCreateTaskRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMTaskService;
import com.alibaba.tripscrm.service.constant.RuleConstant;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.RuleExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.RuleQuery;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.alibaba.tripscrm.service.service.strategy.rule.AbstractRuleStrategy;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/6 20:59
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskRuleStrategy extends AbstractRuleStrategy {
    private final TripSCRMTaskService tripSCRMTaskService;
    private final TaskService taskService;
    private final RuleService ruleService;

    @Switch(name = "taskExecuteMaxDelaySeconds", description = "规则任务执行最长延迟时间")
    public static Long taskExecuteMaxDelaySeconds = 3600L * 24 * 3;

    @Override
    public void postCreate(RuleDO ruleDO) {
        if (Objects.isNull(ruleDO) || Objects.isNull(ruleDO.getConfig())) {
            return;
        }

        // 1. 创建私聊任务
        createTask(ruleDO);
        // 2. 调整规则组下其他规则优先级
        updateOtherRulePriorityByCreate(ruleDO);
    }

    @Override
    public void postUpdate(RuleDO oldRuleDO, RuleDO newRuleDO) {
        if (Objects.isNull(oldRuleDO) || Objects.isNull(oldRuleDO.getConfig())) {
            return;
        }

        if (Objects.isNull(newRuleDO) || Objects.isNull(newRuleDO.getConfig())) {
            return;
        }

        // 1. 创建私聊任务
        updateTask(oldRuleDO, newRuleDO);
        // 2. 调整规则组下其他规则优先级
        updateOtherRulePriorityByUpdate(oldRuleDO, newRuleDO);
    }

    @Override
    public void postDelete(RuleDO ruleDO) {
        if (Objects.isNull(ruleDO) || !StringUtils.hasLength(ruleDO.getConfig())) {
            return;
        }

        // 1. 删除私聊任务
        deleteTask(ruleDO);
        // 2. 调整规则组下其他规则优先级
        updateOtherRulePriorityByDelete(ruleDO);
    }

    @Override
    public void postOnline(RuleDO ruleDO) {
        if (Objects.isNull(ruleDO) || !StringUtils.hasLength(ruleDO.getConfig())) {
            return;
        }

        JSONObject dynamicConfigJson = JSONObject.parseObject(ruleDO.getDynamicConfig());
        Map<Long, Long> materialIdToTaskIdMap = dynamicConfigJson.getObject(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING, new TypeReference<Map<Long, Long>>() {
        });

        for (Long taskId : materialIdToTaskIdMap.values()) {
            taskService.online(taskId, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        }
    }

    @Override
    public void postOffline(RuleDO ruleDO) {
        if (Objects.isNull(ruleDO) || !StringUtils.hasLength(ruleDO.getConfig())) {
            return;
        }

        JSONObject dynamicConfigJson = JSONObject.parseObject(ruleDO.getDynamicConfig());
        Map<Long, Long> materialIdToTaskIdMap = dynamicConfigJson.getObject(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING, new TypeReference<Map<Long, Long>>() {
        });

        for (Long taskId : materialIdToTaskIdMap.values()) {
            taskService.offline(taskId, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        }
    }

    private void updateTask(RuleDO oldRuleDO, RuleDO newRuleDO) {
        JSONObject configJson = JSONObject.parseObject(newRuleDO.getConfig());
        JSONObject dynamicConfigJson = JSONObject.parseObject(oldRuleDO.getDynamicConfig());
        Map<String, Long> oldMaterialIdToTaskIdMap = dynamicConfigJson.getObject(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING, new TypeReference<Map<String, Long>>() {
        });

        Set<Long> oldMaterialIdSet = oldMaterialIdToTaskIdMap.keySet().stream().map(Long::parseLong).collect(Collectors.toSet());
        Set<Long> newMaterialIdSet = configJson.getObject(RuleConstant.MATERIAL_ID_LIST, new TypeReference<Set<Long>>() {
        });

        List<Long> addMaterialIdList = newMaterialIdSet.stream().filter(materialId -> !oldMaterialIdSet.contains(materialId)).collect(Collectors.toList());
        List<Long> deleteMaterialIdList = oldMaterialIdSet.stream().filter(materialId -> !newMaterialIdSet.contains(materialId)).collect(Collectors.toList());

        Map<String, Long> newMaterialIdToTaskIdMap = oldMaterialIdToTaskIdMap.entrySet()
                .stream()
                .filter(entry -> newMaterialIdSet.contains(Long.parseLong(entry.getKey())))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        for (Long materialId : addMaterialIdList) {
            Long taskId = doCreateTask(materialId, getTaskName(newRuleDO));
            newMaterialIdToTaskIdMap.put(String.valueOf(materialId), taskId);
        }

        for (Long materialId : deleteMaterialIdList) {
            taskService.delete(oldMaterialIdToTaskIdMap.get(String.valueOf(materialId)), SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(),true);
        }

        // 绑定任务
        dynamicConfigJson.put(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING, newMaterialIdToTaskIdMap);
        newRuleDO.setDynamicConfig(dynamicConfigJson.toJSONString());
        newRuleDO.setConfig(configJson.toJSONString());
        int effectLines = ruleService.update(newRuleDO, false);
        if (effectLines == 0) {
            throw new RuntimeException("规则绑定任务失败！");
        }
    }

    private void deleteTask(RuleDO ruleDO) {
        JSONObject dynamicConfigJson = JSONObject.parseObject(ruleDO.getDynamicConfig());
        Map<Long, Long> materialIdToTaskIdMap = dynamicConfigJson.getObject(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING, new TypeReference<Map<Long, Long>>() {
        });

        for (Long taskId : materialIdToTaskIdMap.values()) {
            Integer effectLines = taskService.delete(taskId, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(),true);
            if (!NumberUtils.biggerThanZero(effectLines)) {
                throw new RuntimeException("删除私聊任务失败！");
            }
        }
    }

    private void createTask(RuleDO ruleDO) {
        JSONObject configJson = JSONObject.parseObject(ruleDO.getConfig());
        List<Long> materialIdList = configJson.getObject(RuleConstant.MATERIAL_ID_LIST, new TypeReference<List<Long>>() {
        });

        // 创建任务
        Map<String, Long> materialIdToTaskIdMap = new HashMap<>();
        for (Long materialId : materialIdList) {
            Long taskId = doCreateTask(materialId, getTaskName(ruleDO));
            materialIdToTaskIdMap.put(String.valueOf(materialId), taskId);
        }

        // 绑定任务
        JSONObject dynamicConfigJson = new JSONObject();
        dynamicConfigJson.put(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING, materialIdToTaskIdMap);
        ruleDO.setDynamicConfig(dynamicConfigJson.toJSONString());
        int effectLines = ruleService.update(ruleDO, false);
        if (effectLines == 0) {
            throw new RuntimeException("规则绑定任务失败！");
        }
    }

    private Long doCreateTask(Long materialId, String taskName) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.ROBOT_CHAT_MESSAGE_FOR_RULE).getInitConfig(SwitchConfig.publicActivityId, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(), taskName);
        taskInfoDO.setEffectStartTime(new Date());
        Date effectEndTime = new Date(LocalDate.of(2040, 12, 30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        taskInfoDO.setEffectEndTime(effectEndTime);
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        taskInfoDO.setTriggerType(Integer.parseInt(TaskTriggerTypeEnum.INTERFACE.getCode()));
        taskInfoDO.setTriggerCrowd(3);
        taskInfoDO.setMaterialId(materialId);

        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建私聊任务失败！");
        }

        return taskInfoDO.getId();
    }

    // 大于 Priority 的都降一级
    private void updateOtherRulePriorityByDelete(RuleDO ruleDO) {
        RuleQuery query = new RuleQuery();
        query.setGroupId(ruleDO.getGroupId());
        List<RuleDO> ruleListInGroup = ruleService.selectByCondition(query);
        for (RuleDO rule : ruleListInGroup) {
            if (Objects.equals(rule.getId(), ruleDO.getId())) {
                continue;
            }

            if (rule.getPriority() <= ruleDO.getPriority()) {
                continue;
            }

            RuleDO newRule = new RuleDO();
            newRule.setId(rule.getId());
            newRule.setPriority(rule.getPriority() - 1);
            int effectLines = ruleService.update(newRule, false);
            if (effectLines == 0) {
                throw new RuntimeException("修改规则组下其他规则的优先级失败！");
            }
        }
    }

    // (1, 2, 3, 4, 5, 6) -> (1, 5, 3, 4, 5, 6)
    // 改大，大于 oldPriority 且 小于等于 newPriority 的都降一级

    // (1, 2, 3, 4, 5, 6) -> (1, 2, 3, 4, 2, 6)
    // 改小，小于 oldPriority 且 大于等于 newPriority 的都升一级
    private void updateOtherRulePriorityByUpdate(RuleDO oldRuleDO, RuleDO newRuleDO) {
        if (Objects.equals(oldRuleDO.getPriority(), newRuleDO.getPriority())) {
            return;
        }

        RuleQuery query = new RuleQuery();
        query.setGroupId(newRuleDO.getGroupId());
        List<RuleDO> ruleListInGroup = ruleService.selectByCondition(query);

        boolean isDown = newRuleDO.getPriority() < oldRuleDO.getPriority();

        for (RuleDO rule : ruleListInGroup) {
            if (Objects.equals(rule.getId(), newRuleDO.getId())) {
                continue;
            }

            if (isDown) {
                if (rule.getPriority() >= oldRuleDO.getPriority()) {
                    continue;
                }
                if (rule.getPriority() < newRuleDO.getPriority()) {
                    continue;
                }

                rule.setPriority(rule.getPriority() + 1);
            } else {
                if (rule.getPriority() <= oldRuleDO.getPriority()) {
                    continue;
                }
                if (rule.getPriority() > newRuleDO.getPriority()) {
                    continue;
                }
                rule.setPriority(rule.getPriority() - 1);
            }

            RuleDO newRule = new RuleDO();
            newRule.setId(rule.getId());
            newRule.setPriority(rule.getPriority());
            int effectLines = ruleService.update(newRule, false);
            if (effectLines == 0) {
                throw new RuntimeException("修改规则组下其他规则的优先级失败！");
            }
        }
    }

    // 大于等于 Priority 的都升一级
    private void updateOtherRulePriorityByCreate(RuleDO ruleDO) {
        RuleQuery query = new RuleQuery();
        query.setGroupId(ruleDO.getGroupId());
        List<RuleDO> ruleListInGroup = ruleService.selectByCondition(query);
        for (RuleDO rule : ruleListInGroup) {
            if (Objects.equals(rule.getId(), ruleDO.getId())) {
                continue;
            }

            if (rule.getPriority() < ruleDO.getPriority()) {
                continue;
            }

            RuleDO newRule = new RuleDO();
            newRule.setId(rule.getId());
            newRule.setPriority(rule.getPriority() + 1);
            int effectLines = ruleService.update(newRule, false);
            if (effectLines == 0) {
                throw new RuntimeException("修改规则组下其他规则的优先级失败！");
            }
        }
    }

    @Override
    public TripSCRMResult<Void> run(RuleDO ruleDO, RuleExecuteContext context) {
        if (Objects.isNull(ruleDO)) {
            PlatformLogUtil.logFail("任务规则执行，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        JSONObject params = context.getParams();
        if (CollectionUtils.isEmpty(params) ||
                !params.containsKey(RuleConstant.NEW_TAG_ID_LIST) ||
                !params.containsKey(RuleConstant.CORP_ID) ||
                !params.containsKey(RuleConstant.EXTERNAL_USER_ID)
        ) {
            PlatformLogUtil.logFail("任务规则执行，参数非法", LogListUtil.newArrayList(ruleDO, context));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        // 企业Id
        String corpId = params.getString(RuleConstant.CORP_ID);
        // 外部联系人Id
        String externalUserId = params.getString(RuleConstant.EXTERNAL_USER_ID);
        if (!StringUtils.hasLength(corpId) || !StringUtils.hasLength(externalUserId)) {
            PlatformLogUtil.logFail("任务规则执行，参数非法", LogListUtil.newArrayList(ruleDO, context));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!match(ruleDO, context)) {
            PlatformLogUtil.logFail("任务规则执行，规则执行条件匹配结果为失败", LogListUtil.newArrayList(ruleDO, context));
            return TripSCRMResult.fail(TripSCRMErrorCode.RULE_NOT_MATCH_EXECUTE_CONDITION);
        }

        // 执行后续操作
        JSONObject configJson = JSONObject.parseObject(ruleDO.getConfig());
        JSONObject dynamicConfigJson = JSONObject.parseObject(ruleDO.getDynamicConfig());
        if (!dynamicConfigJson.containsKey(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING)) {
            PlatformLogUtil.logFail("任务规则执行，获取规则上绑定的任务失败", LogListUtil.newArrayList(ruleDO, context));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_RULE_CONFIG);
        }

        // 没有配置触发时间段
        if (!configJson.containsKey(RuleConstant.EXECUTE_TIME)) {
            PlatformLogUtil.logFail("任务规则执行，同步执行任务", LogListUtil.newArrayList(ruleDO, context));
            return createSyncTask(dynamicConfigJson, externalUserId);
        }

        PlatformLogUtil.logFail("任务规则执行，异步执行任务", LogListUtil.newArrayList(ruleDO, context));
        return createAsyncTask(configJson, dynamicConfigJson, externalUserId);
    }

    private TripSCRMResult<Void> createSyncTask(JSONObject dynamicConfigJson, String externalUserId) {
        Map<Long, Long> materialIdToTaskIdMap = dynamicConfigJson.getObject(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING, new TypeReference<Map<Long, Long>>() {
        });

        for (Long taskId : materialIdToTaskIdMap.values()) {
            TripSCRMCreateTaskRequest request = new TripSCRMCreateTaskRequest();
            request.setTaskId(taskId);
            request.setTargetId(externalUserId);
            request.setTargetType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
            TripSCRMResult<String> result = tripSCRMTaskService.createSyncTask(request);
            PlatformLogUtil.logFail("任务规则执行，同步执行任务", LogListUtil.newArrayList(externalUserId, taskId, request));
            if (!result.isSuccess()) {
                PlatformLogUtil.logFail("任务规则执行，同步任务执行失败", LogListUtil.newArrayList(externalUserId, taskId, request));
                return TripSCRMResult.fail(TripSCRMErrorCode.TASK_EXECUTE_FAIL);
            }
        }

        return TripSCRMResult.success(null);
    }

    private TripSCRMResult<Void> createAsyncTask(JSONObject configJson, JSONObject dynamicConfigJson, String externalUserId) {
        String executeTime = configJson.getString(RuleConstant.EXECUTE_TIME);
        if (!StringUtils.hasLength(executeTime)) {
            PlatformLogUtil.logFail("任务规则执行，异步任务执行失败，规则配置执行时间为空");
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_RULE_CONFIG);
        }

        List<String> executeTimeList = Arrays.stream(executeTime.split(";")).filter(StringUtils::hasLength).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(executeTimeList) || executeTimeList.size() != 2) {
            PlatformLogUtil.logFail("任务规则执行，异步任务执行失败，规则配置执行时间非法");
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_RULE_CONFIG);
        }

        Map<Long, Long> materialIdToTaskIdMap = dynamicConfigJson.getObject(RuleConstant.MATERIAL_ID_TO_TASK_ID_MAPPING, new TypeReference<Map<Long, Long>>() {
        });

        for (Long taskId : materialIdToTaskIdMap.values()) {
            TripSCRMCreateTaskRequest request = new TripSCRMCreateTaskRequest();
            request.setTaskId(taskId);
            request.setTargetId(externalUserId);
            request.setTargetType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
            request.setSecondDelayLevel(true);
            request.setTriggerTimeType(TriggerTimeEnum.CUSTOM.getCode());
            // 计算执行时间
            LocalDateTime localDateTime = calculateExecuteTime(executeTimeList);
            request.setTriggerTime(localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            PlatformLogUtil.logFail("任务规则执行，异步执行任务", LogListUtil.newArrayList(externalUserId, taskId, request));
            TripSCRMResult<String> result = tripSCRMTaskService.createAsyncTask(request);
            if (!result.isSuccess()) {
                PlatformLogUtil.logFail("任务规则执行，异步执行任务失败", LogListUtil.newArrayList(externalUserId, taskId, request));
                return TripSCRMResult.fail(TripSCRMErrorCode.TASK_EXECUTE_FAIL);
            }
        }

        return TripSCRMResult.success(null);
    }

    public static void main(String[] args) {
        for (int i = 0; i <= 200; i++) {
            System.out.println(calculateExecuteTime(Arrays.asList("06:00", "23:00")));
        }
    }

    private static LocalDateTime calculateExecuteTime(List<String> executeTime) {
        LocalDateTime now = LocalDateTime.now();
        LocalTime beginTime = LocalTime.parse(executeTime.get(0));
        LocalTime endTime = LocalTime.parse(executeTime.get(1));

        // 全局开始结束时间
        LocalDateTime beginDateTime = LocalDateTime.from(now);
        LocalDateTime endDateTime = now.plusSeconds(taskExecuteMaxDelaySeconds);

        if (now.toLocalTime().isBefore(beginTime)) {
            beginDateTime = beginDateTime.with(beginTime);
            endDateTime = endDateTime.minusDays(1).with(endTime);
        }

        if (now.toLocalTime().isAfter(endTime)) {
            beginDateTime = now.plusDays(1).with(beginTime);
            endDateTime = endDateTime.with(endTime);
        }

        long allDaySecondsSum = 0L;
        List<Long> allDayRemainSeconds = new ArrayList<>();
        LocalDateTime localDateTime = LocalDateTime.from(now);

        // 当天已经没有可用时间了
        if (localDateTime.toLocalTime().isAfter(endTime)) {
            allDayRemainSeconds.add(null);
            localDateTime = localDateTime.plusDays(1).with(beginTime);
        }

        // 早于当天执行时间
        if (localDateTime.toLocalTime().isBefore(beginTime)) {
            localDateTime = localDateTime.with(beginTime);
        }

        // 从第一天开始计算，每天可用秒数
        while (!localDateTime.isAfter(endDateTime)) {
            long seconds;
            if (endDateTime.isBefore(localDateTime.with(endTime))) {
                seconds = ChronoUnit.SECONDS.between(localDateTime.with(beginTime), localDateTime);
            } else {
                seconds = ChronoUnit.SECONDS.between(localDateTime, localDateTime.with(endTime));
            }

            allDayRemainSeconds.add(seconds);
            allDaySecondsSum += seconds;
            localDateTime = localDateTime.plusDays(1);
        }

        long delaySeconds = ThreadLocalRandom.current().nextLong(allDaySecondsSum);
        localDateTime = LocalDateTime.from(beginDateTime);
        for (Long remainSecond : allDayRemainSeconds) {
            if (remainSecond == null) {
                continue;
            }

            if (delaySeconds <= remainSecond) {
                return localDateTime.plusSeconds(delaySeconds);
            }
            delaySeconds -= remainSecond;
            localDateTime = localDateTime.plusDays(1).with(beginTime);
        }

        return localDateTime;
    }

    private boolean match(RuleDO ruleDO, RuleExecuteContext context) {
        JSONObject configJson = JSONObject.parseObject(ruleDO.getConfig());
        if (!configJson.containsKey(RuleConstant.TAG_ID_LIST_GROUP)) {
            return false;
        }

        List<List<String>> tagIdListGroup = configJson.getObject(RuleConstant.TAG_ID_LIST_GROUP, new TypeReference<List<List<String>>>() {
        });

        // 新增标签数据
        List<String> customerNewTagIdList = context.getParams().getObject(RuleConstant.NEW_TAG_ID_LIST, new TypeReference<List<String>>() {
        });

        tagIdListGroup = Optional.ofNullable(tagIdListGroup).orElse(new ArrayList<>());
        Set<String> customerNewTagIdSet = new HashSet<>(Optional.ofNullable(customerNewTagIdList).orElse(new ArrayList<>()));

        for (List<String> tagIdList : tagIdListGroup) {
            if (CollectionUtils.isEmpty(tagIdList)) {
                continue;
            }

            // 组内是且，组与组之间是或
            if (customerNewTagIdSet.containsAll(tagIdList)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 任务名称
     *
     * @return 任务名称
     */
    protected String getTaskName(RuleDO ruleDO) {
        return ruleDO.getName() + "_" + UUID.randomUUID().toString().substring(0, 10);
    }

    @Override
    public RuleTypeEnum getRuleType() {
        return RuleTypeEnum.TASK;
    }
}
