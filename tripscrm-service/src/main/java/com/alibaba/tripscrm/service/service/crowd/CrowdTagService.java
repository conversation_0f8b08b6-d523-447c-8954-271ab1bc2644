package com.alibaba.tripscrm.service.service.crowd;

import com.alibaba.tripscrm.service.model.dto.crowd.CrowdTagConvertTaskDTO;
import com.alibaba.tripscrm.service.model.request.crowd.CrowdTagConvertRequest;
import com.alibaba.tripscrm.service.model.response.crowd.CrowdTagConvertResponse;

/**
 * 人群打标服务接口
 *
 * <AUTHOR>
 * @date 2024/10/13
 */
public interface CrowdTagService {

    /**
     * 创建人群打标任务
     *
     * @param request 人群打标请求
     * @return 人群打标响应
     */
    CrowdTagConvertResponse createCrowdTagTask(CrowdTagConvertRequest request);

    /**
     * 查询人群打标任务状态
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    CrowdTagConvertTaskDTO getTaskStatus(Long taskId);

    /**
     * 取消人群打标任务
     *
     * @param taskId 任务ID
     * @return 是否成功
     */
    Boolean cancelTask(Long taskId);
}