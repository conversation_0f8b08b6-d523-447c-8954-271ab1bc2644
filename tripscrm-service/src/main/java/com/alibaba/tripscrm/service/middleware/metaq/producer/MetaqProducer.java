package com.alibaba.tripscrm.service.middleware.metaq.producer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.rocketmq.client.producer.MessageQueueSelector;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.client.producer.SendStatus;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.taobao.metaq.client.MetaProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

/**
 * metaq消息发送
 *
 * <AUTHOR>
 * @date 2023/4/18
 */
@Slf4j
@Service
public class MetaqProducer {
    MetaProducer producer = null;

    @PostConstruct
    public void init() throws Exception {
        this.producer = new MetaProducer("wx_ep_callback_sync");
        this.producer.start();
        PlatformLogUtil.logFail("already start");
    }

    public boolean send(MQEnum mqEnum, String key, String msgBody, MessageQueueSelector selector) {
        try {
            Message msg = new Message(
                    // topic
                    mqEnum.getTopic(),
                    // tag
                    "",
                    // key，消息的Key字段是为了唯一标识消息的，方便运维排查问题。如果不设置Key，则无法定位消息丢失原因。
                    key,
                    // body
                    msgBody.getBytes(StandardCharsets.UTF_8));
            SendResult sendResult = producer.send(msg, selector, null);
            if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                PlatformLogUtil.logFail("指定选择器发送metaq消息失败", LogListUtil.newArrayList(mqEnum.getTopic(), key, msgBody, sendResult));
                return false;
            }
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("指定选择器发送metaq消息异常", e.getMessage(), e, LogListUtil.newArrayList(mqEnum.getTopic(), key, msgBody));
            return false;
        }
    }

    public boolean send(MQEnum mqEnum, String key, String msgBody) {
        return send(mqEnum, key, "", msgBody, null);
    }

    public boolean send(MQEnum mqEnum, String key, String tag, String msgBody) {
        return send(mqEnum, key, tag, msgBody, null);
    }

//    public boolean send(MQEnum mqEnum, String tag, String key, String msgBody) {
//        return send(mqEnum, key, msgBody, null);
//    }

    public boolean send(MQEnum mqEnum, String key, String tag, String msgBody, Integer delayTimeLevel) {
        try {
            Message msg = new Message(
                    // topic
                    mqEnum.getTopic(),
                    // tag
                    tag,
                    // key，消息的Key字段是为了唯一标识消息的，方便运维排查问题。如果不设置Key，则无法定位消息丢失原因。
                    key,
                    // body
                    msgBody.getBytes(StandardCharsets.UTF_8));
            if (delayTimeLevel != null) {
                msg.setDelayTimeLevel(delayTimeLevel);
            }
            SendResult sendResult = producer.send(msg);
            if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
                PlatformLogUtil.logFail("发送metaq消息失败", LogListUtil.newArrayList(mqEnum.getTopic(), tag, key, msgBody, sendResult));
                return false;
            }
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("发送metaq消息异常", e.getMessage(), e, LogListUtil.newArrayList(mqEnum.getTopic(), tag, key, msgBody));
            return false;
        }
    }
}
