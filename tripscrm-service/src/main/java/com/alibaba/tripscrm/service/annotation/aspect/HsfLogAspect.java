package com.alibaba.tripscrm.service.annotation.aspect;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.util.log.monitor.ServiceMonitorLoggerUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;


/**
 * <AUTHOR>
 * @date 2023-10-30 15:01:57
 */
@Aspect
@Component
public class HsfLogAspect {
    @Around(value = "@annotation(log)")
    public Object logAspect(ProceedingJoinPoint point, ServiceLog log) {
        long startTime = System.currentTimeMillis();
        String className = null;
        String methodName = null;
        String mark = null;
        Object request = null;
        Object result = null;
        try {
            mark = StringUtils.hasText(log.value()) ? log.value() : "未知接口调用";
            className = ClassUtils.getShortName(point.getTarget().getClass().getName());
            methodName = point.getSignature().getName();
            if (point.getArgs() != null && point.getArgs().length == 1) {
                request = point.getArgs()[0];
            } else {
                request = point.getArgs();
            }
            result = point.proceed();
            if (result != null) {
                // 其他类型暂时无法处理
                if (!(result instanceof TripSCRMResult)) {
                    return result;
                }

                TripSCRMResult<?> response = (TripSCRMResult<?>) result;
                ServiceMonitorLoggerUtils.record(mark, className, methodName, TripSCRMErrorCode.codeOf(response.getCode()), System.currentTimeMillis() - startTime, response.getMsg());
                PlatformLogUtil.logInfo(mark, LogListUtil.newArrayList(className, methodName, request, result));
                return result;
            }

            ServiceMonitorLoggerUtils.record(mark, className, methodName, TripSCRMErrorCode.UNKNOWN, System.currentTimeMillis() - startTime, null);
            PlatformLogUtil.logFail(mark, TripSCRMErrorCode.UNKNOWN.getCode(), LogListUtil.newArrayList(className, methodName, request, result));
            return result;
        } catch (TripscrmException e) {
            ServiceMonitorLoggerUtils.record(mark, className, methodName, e.getErrorCodeEnum(), System.currentTimeMillis() - startTime, e.getMessage());
            PlatformLogUtil.logException(mark + ", 执行异常", e.getErrorCodeEnum().getCode(), e, LogListUtil.newArrayList(className, methodName, request, result));
            return TripSCRMResult.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Throwable e) {
            ServiceMonitorLoggerUtils.record(mark, className, methodName, TripSCRMErrorCode.SYSTEM_EXCEPTION, System.currentTimeMillis() - startTime, e.getMessage());
            PlatformLogUtil.logException(mark + ", 处理异常", TripSCRMErrorCode.SYSTEM_EXCEPTION.getCode(), e, LogListUtil.newArrayList(className, methodName, request, result));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION, e.getMessage());
        }
    }
}
