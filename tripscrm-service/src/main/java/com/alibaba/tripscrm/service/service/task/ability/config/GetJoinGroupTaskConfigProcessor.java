package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatJoinGroupDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatJoinGroupService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.INTERFACE;

/**
 * 获取群活码任务配置处理器
 *
 * <AUTHOR>
 * @date 2023-12-30 15:26:28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GetJoinGroupTaskConfigProcessor extends AbstractTaskConfigProcessor {
    private final TaskService taskService;
    private final WechatJoinGroupService wechatJoinGroupService;
    private final WechatGroupService wechatGroupService;
    private final ActivityContextService activityContextService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        JSONObject extraInfoJson = super.getExtraInfo(taskInfoDO);
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        if (!configJson.containsKey("wechatJoinGroupId")) {
            return extraInfoJson;
        }

        Long wechatJoinGroupId = configJson.getLong("wechatJoinGroupId");
        String state = configJson.getString("contextId");
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getById(wechatJoinGroupId);
        extraInfoJson.put("qrCodeUrl", wechatJoinGroupDO.getQrCode());
        extraInfoJson.put("state", state);
        extraInfoJson.put("chatIdList", wechatJoinGroupDO.getChatIdList());

        Long contextId = Long.valueOf(wechatJoinGroupDO.getState());
        ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        activityTaskInfoBO.getExtraJson().putIfAbsent("allChatIdList", new ArrayList<>());
        List<String> allChatIdList = activityTaskInfoBO.getExtraJson().getObject("allChatIdList", new TypeReference<List<String>>() {
        });
        extraInfoJson.put("groupCount", allChatIdList.size());
        extraInfoJson.put("joinCount", wechatGroupService.getGroupCustomerCount(allChatIdList, state));
        extraInfoJson.put("joinWithFollowUserCount", wechatGroupService.getGroupCustomerWithFollowUserCount(allChatIdList, state));
        extraInfoJson.put("leaveCount", wechatGroupService.getGroupLeaveCustomerCount(allChatIdList, state));
        extraInfoJson.put("leaveWithFollowUserCount", wechatGroupService.getGroupLeaveCustomerWithFollowUserCount(allChatIdList, state));
        return extraInfoJson;
    }

    @Override
    public TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName) {
        TaskInfoDO taskInfoDO = super.getInitConfig(activityId, spaceId, taskName);
        taskInfoDO.setTriggerType(Integer.parseInt(INTERFACE.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        Date effectEndTime = new Date(LocalDate.of(2040, 12, 30).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        taskInfoDO.setEffectEndTime(effectEndTime);
        return taskInfoDO;
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
        JSONObject extInfoJson = JSONObject.parseObject(taskInfoDO.getExtInfo());
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());

        // 默认配置
        extInfoJson.putIfAbsent("chatIdList", new ArrayList<>());

        // 处理群活码数据
        WechatJoinGroupDO wechatJoinGroupDO = processWechatJoinGroup(extInfoJson, configJson, taskInfoDO.getSpaceId());
        // 处理活动上下文数据
        processActivityContext(taskInfoDO, wechatJoinGroupDO);

        // 更新任务配置信息
        configJson.put("wechatJoinGroupId", wechatJoinGroupDO.getId());
        configJson.put("contextId", Long.parseLong(wechatJoinGroupDO.getState()));
        configJson.put("qrCodeUrl", wechatJoinGroupDO.getQrCode());
        taskInfoDO.setExtInfo(extInfoJson.toJSONString());
        taskInfoDO.setConfig(configJson.toJSONString());
        taskService.updateTaskInfoWithoutPost(taskInfoDO);
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
        postCreate(taskInfoDO);
    }

    private WechatJoinGroupDO processWechatJoinGroup(JSONObject extInfoJson, JSONObject configJson, Long spaceId) {
        List<String> chatIdList = extInfoJson.getObject("chatIdList", new TypeReference<List<String>>() {
        });
        // 创建群活码
        if (!configJson.containsKey("wechatJoinGroupId")) {
            return wechatJoinGroupService.create(chatIdList, activityContextService.generateContextId().toString(), spaceId, null);
        }

        Long wechatJoinGroupId = configJson.getLong("wechatJoinGroupId");
        return wechatJoinGroupService.getById(wechatJoinGroupId);
    }

    private void processActivityContext(TaskInfoDO taskInfoDO, WechatJoinGroupDO wechatJoinGroupDO) {
        Long contextId = NumberUtils.toLong(wechatJoinGroupDO.getState());
        List<String> newChatIdList = Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).collect(Collectors.toList());
        ActivityTaskInfoBO oldActivityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        List<String> oldChatIdList = new ArrayList<>();
        if (Objects.nonNull(oldActivityTaskInfoBO)) {
            oldChatIdList = oldActivityTaskInfoBO.getExtraJson().getObject("allChatIdList", new TypeReference<List<String>>() {
            });
        }
        List<String> allChatIdList = Lists.newArrayList(newChatIdList, oldChatIdList).stream().flatMap((Function<List<String>, Stream<String>>) Collection::stream).distinct().collect(Collectors.toList());

        // =====================================================================================
        // 插入/更新活动上下文信息（targetType = 群活码类型）
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(contextId);
        activityTaskInfoBO.setActivityId(taskInfoDO.getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_JOIN_GROUP_ID);
        activityTaskInfoBO.setTargetId(String.valueOf(wechatJoinGroupDO.getId()));
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("wechatJoinGroupId", wechatJoinGroupDO.getId());
        extraJson.put("qrCodeUrl", wechatJoinGroupDO.getQrCode());
        extraJson.put("chatIdList", newChatIdList);
        extraJson.put("allChatIdList", allChatIdList);
        extraJson.put("dataTime", System.currentTimeMillis());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);
        PlatformLogUtil.logFail("upsert", LogListUtil.newArrayList(activityTaskInfoBO));


        // =====================================================================================
        // 插入/更新活动上下文信息（targetType = 客户群类型）
        for (String chatId : newChatIdList) {
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setActivityId(taskInfoDO.getActivityId());
            query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            query.setTargetId(chatId);
            List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
            Long targetActivityContextId;
            if (!CollectionUtils.isEmpty(activityTaskInfoBOS)) {
                targetActivityContextId = ActivityContextService.getNewestBo(activityTaskInfoBOS).getContextId();
            } else {
                targetActivityContextId = activityContextService.generateContextId();
            }

            ActivityTaskInfoBO targetActivityTaskInfoBO = new ActivityTaskInfoBO();
            targetActivityTaskInfoBO.setContextId(targetActivityContextId);
            targetActivityTaskInfoBO.setActivityId(taskInfoDO.getActivityId());
            targetActivityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            targetActivityTaskInfoBO.setTargetId(chatId);
            targetActivityTaskInfoBO.setTagIdList(new ArrayList<>());
            extraJson = new JSONObject();
            extraJson.put("contextId", contextId);
            extraJson.put("dataTime", System.currentTimeMillis());
            targetActivityTaskInfoBO.setExtraJson(extraJson);
            activityContextService.upsert(targetActivityTaskInfoBO);
            PlatformLogUtil.logFail("upsert", LogListUtil.newArrayList(targetActivityTaskInfoBO));
        }
    }

    private void deleteActivityContext(TaskInfoDO taskInfoDO, WechatJoinGroupDO wechatJoinGroupDO) {
        Long contextId = NumberUtils.toLong(wechatJoinGroupDO.getState());
        // 查询上下文信息
        ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        if (Objects.isNull(activityTaskInfoBO)) {
            return;
        }

        // 删除群活码上下文
        activityContextService.deleteByTaskId(contextId);
        PlatformLogUtil.logFail("deleteByTaskId", LogListUtil.newArrayList(activityTaskInfoBO));

        // ====================================================================
        // 删除群聊上下文
        if (!activityTaskInfoBO.getExtraJson().containsKey("chatIdList")) {
            return;
        }
        List<String> chatIdList = activityTaskInfoBO.getExtraJson().getObject("chatIdList", new TypeReference<List<String>>() {
        });
        List<ActivityTaskInfoBO> queryList = new ArrayList<>();
        for (String chatId : chatIdList) {
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setActivityId(taskInfoDO.getActivityId());
            query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            query.setTargetId(chatId);
            queryList.add(query);
        }

        List<ActivityTaskInfoBO> removeActivityTaskInfoList = activityContextService.batchQueryByActivityAndTarget(queryList);
        activityContextService.batchDeleteByTaskId(removeActivityTaskInfoList.stream().map(ActivityTaskInfoBO::getContextId).collect(Collectors.toList()));
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(removeActivityTaskInfoList));
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {
        String config = taskInfoDO.getConfig();
        JSONObject configJson = JSONObject.parseObject(config);
        Long wechatJoinGroupId = configJson.getLong("wechatJoinGroupId");
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getById(wechatJoinGroupId);

        // 删除群码信息
        try {
            wechatJoinGroupService.delete(wechatJoinGroupId);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(taskInfoDO));
        }
        // 删除上下文信息
        deleteActivityContext(taskInfoDO, wechatJoinGroupDO);
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.GET_WECHAT_JOIN_GROUP;
    }
}
