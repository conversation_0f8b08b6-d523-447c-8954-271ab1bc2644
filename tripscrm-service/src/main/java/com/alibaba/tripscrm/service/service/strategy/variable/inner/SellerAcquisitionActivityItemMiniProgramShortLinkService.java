package com.alibaba.tripscrm.service.service.strategy.variable.inner;

import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.enums.material.InnerServiceEnum;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.dto.ItemInfoDTO;
import com.alibaba.tripscrm.service.model.vo.material.ItemVO;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.second.ShortLinkService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/24 11:08
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerAcquisitionActivityItemMiniProgramShortLinkService extends AbstractVariableDataInnerService {
    private final MaterialService materialService;
    private final MaterialLinkService materialLinkService;
    private final ShortLinkService shortLinkService;

    @Override
    public String doGetValue(Map<String, Object> paramMap) {
        if (!paramMap.containsKey("itemType") || !paramMap.containsKey("itemId")) {
            return "";
        }

        int itemType = MapUtils.getIntValue(paramMap, "itemType", 0);
        String itemId = MapUtils.getString(paramMap, "itemId");
        String itemName = MapUtils.getString(paramMap, "itemName", "");
        String channelId = MapUtils.getString(paramMap, "channelId");
        ItemTypeEnum itemTypeEnum = ItemTypeEnum.findByType(itemType);

        if (Objects.isNull(itemTypeEnum) || !Lists.newArrayList(ItemTypeEnum.GOODS, ItemTypeEnum.HOTEL).contains(itemTypeEnum) || !StringUtils.hasText(itemId)) {
            return "";
        }

        if (Objects.equals(SwitchConfig.sellerAcquisitionChannelIdForWechatVideoRoom, channelId)) {
            return StringUtils.hasText(itemName) ? "咨询的商品是：" + itemName : "";
        }

        ItemInfoDTO itemInfoDTO = new ItemInfoDTO();
        itemInfoDTO.setItemType(itemType);
        itemInfoDTO.setItemId(itemId);
        ItemVO itemVO = materialService.queryItemById(itemInfoDTO);
        if (Objects.isNull(itemVO) || !StringUtils.hasText(itemVO.getName())) {
            return "";
        }

        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setTargetLinkType(LinkTypeEnum.MINI_PROGRAM_LIKE);
        request.setConvertShortLink(true);
        request.setTitle(itemVO.getName());
        request.setUseInWechat(true);

        switch (itemTypeEnum) {
            case GOODS:
                request.setOriginal(MaterialConstant.GOODS_MINI_PROGRAM_URL_PRE + itemId);
                String h5ShortUrl = shortLinkService.convertToShortUrl(MaterialConstant.GOODS_PC_URL + itemId);
                return "咨询的商品是：" + materialLinkService.convertLink(request) + "，" + h5ShortUrl;
            case HOTEL:
                request.setOriginal(MaterialConstant.HOTEL_H5_PRE + itemId);
                return "咨询的商品是：" + materialLinkService.convertLink(request);
        }

        return "";
    }

    @Override
    public InnerServiceEnum getInnerServiceEnum() {
        return InnerServiceEnum.SELLER_ACQUISITION_ACTIVITY_ITEM_MINI_PROGRAM_SHORT_LINK;
    }
}
