package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.service.model.dto.group.GroupRelationDTO;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description：群组关系转换器
 * @Author：wangrui
 * @create：2025/8/15 16:07
 * @Filename：GroupRelationConverter
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupRelationConverter {

    /**
     * DO转DTO
     * @param groupRelationDO
     * @return
     */
    public static GroupRelationDTO convert2DTO(GroupRelationDO groupRelationDO) {
        if (groupRelationDO == null) {
            return null;
        }

        GroupRelationDTO groupRelationDTO = new GroupRelationDTO();
        groupRelationDTO.setId(groupRelationDO.getId());
        groupRelationDTO.setChatId(groupRelationDO.getChatId());
        groupRelationDTO.setUserId(groupRelationDO.getUserId());
        groupRelationDTO.setCorpId(groupRelationDO.getCorpId());
        groupRelationDTO.setUserType(groupRelationDO.getUserType());
        groupRelationDTO.setGroupName(groupRelationDO.getGroupName());
        groupRelationDTO.setName(groupRelationDO.getName());
        groupRelationDTO.setNickname(groupRelationDO.getNickname());
        groupRelationDTO.setJoinTime(groupRelationDO.getJoinTime());
        groupRelationDTO.setJoinScene(groupRelationDO.getJoinScene());
        groupRelationDTO.setInvitor(groupRelationDO.getInvitor());
        groupRelationDTO.setMemberType(groupRelationDO.getMemberType());
        groupRelationDTO.setStatus(groupRelationDO.getStatus());
        groupRelationDTO.setIsDeleted(groupRelationDO.getIsDeleted());
        groupRelationDTO.setGmtCreate(groupRelationDO.getGmtCreate());
        groupRelationDTO.setGmtModified(groupRelationDO.getGmtModified());
        groupRelationDTO.setPlatformType(PlatformTypeEnum.valueOf(groupRelationDO.getPlatformType()));

        return groupRelationDTO;
    }

    /**
     * DTO转DO
     * @param groupRelationDTO
     * @return
     */
    public static GroupRelationDO convert2DO(GroupRelationDTO groupRelationDTO) {
        if (groupRelationDTO == null) {
            return null;
        }

        GroupRelationDO groupRelationDO = new GroupRelationDO();
        groupRelationDO.setId(groupRelationDTO.getId());
        groupRelationDO.setChatId(groupRelationDTO.getChatId());
        groupRelationDO.setUserId(groupRelationDTO.getUserId());
        groupRelationDO.setCorpId(groupRelationDTO.getCorpId());
        groupRelationDO.setUserType(groupRelationDTO.getUserType());
        groupRelationDO.setGroupName(groupRelationDTO.getGroupName());
        groupRelationDO.setName(groupRelationDTO.getName());
        groupRelationDO.setNickname(groupRelationDTO.getNickname());
        groupRelationDO.setJoinTime(groupRelationDTO.getJoinTime());
        groupRelationDO.setJoinScene(groupRelationDTO.getJoinScene());
        groupRelationDO.setInvitor(groupRelationDTO.getInvitor());
        groupRelationDO.setMemberType(groupRelationDTO.getMemberType());
        groupRelationDO.setStatus(groupRelationDTO.getStatus());
        groupRelationDO.setIsDeleted(groupRelationDTO.getIsDeleted());
        groupRelationDO.setPlatformType(groupRelationDTO.getPlatformType().getCode().byteValue());
        groupRelationDO.setShardingKey(groupRelationDTO.getShardingKey());
        groupRelationDO.setUserType(( byte)0);
        groupRelationDO.setExtraInfo(groupRelationDTO.getExtraInfo());

        return groupRelationDO;
    }

    /**
     * DO列表转DTO列表
     * @param groupRelationDOS
     * @return
     */
    public static List<GroupRelationDTO> convert2DTOs(List<GroupRelationDO> groupRelationDOS) {
        if (groupRelationDOS == null) {
            return null;
        }

        List<GroupRelationDTO> groupRelationDTOS = new ArrayList<>();
        for (GroupRelationDO groupRelationDO : groupRelationDOS) {
            groupRelationDTOS.add(convert2DTO(groupRelationDO));
        }
        return groupRelationDTOS;
    }

    /**
     * DTO列表转DO列表
     * @param groupRelationDTOS
     * @return
     */
    public static List<GroupRelationDO> convert2DOs(List<GroupRelationDTO> groupRelationDTOS) {
        if (groupRelationDTOS == null) {
            return null;
        }

        List<GroupRelationDO> groupRelationDOS = new ArrayList<>();
        for (GroupRelationDTO groupRelationDTO : groupRelationDTOS) {
            groupRelationDOS.add(convert2DO(groupRelationDTO));
        }
        return groupRelationDOS;
    }
}
