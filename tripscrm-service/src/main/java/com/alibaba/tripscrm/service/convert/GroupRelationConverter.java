package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.mapper.tddl.GroupRelationNewMapper;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.service.model.dto.group.GroupRelationDTO;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import com.taobao.tddl.client.sequence.Sequence;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description：群组关系转换器
 * @Author：wangrui
 * @create：2025/8/15 16:07
 * @Filename：GroupRelationConverter
 */
@Component
public class GroupRelationConverter {
    private final Sequence sequence;

    public GroupRelationConverter(@Qualifier("groupRelationIdSequence") Sequence sequence) {

        this.sequence = sequence;
    }

    /**
     * DO转DTO
     *
     * @param groupRelationDO
     * @return
     */
    public static GroupRelationDTO convert2DTO(GroupRelationDO groupRelationDO) {
        if (groupRelationDO == null) {
            return null;
        }

        GroupRelationDTO groupRelationDTO = new GroupRelationDTO();
        groupRelationDTO.setId(groupRelationDO.getId());
        groupRelationDTO.setChatId(groupRelationDO.getChatId());
        groupRelationDTO.setUserId(groupRelationDO.getUserId());
        groupRelationDTO.setCorpId(groupRelationDO.getCorpId());
        groupRelationDTO.setUserType(groupRelationDO.getUserType());
        groupRelationDTO.setGroupName(groupRelationDO.getGroupName());
        groupRelationDTO.setName(groupRelationDO.getName());
        groupRelationDTO.setNickname(groupRelationDO.getNickname());
        groupRelationDTO.setJoinTime(groupRelationDO.getJoinTime());
        groupRelationDTO.setJoinScene(groupRelationDO.getJoinScene());
        groupRelationDTO.setInvitor(groupRelationDO.getInvitor());
        groupRelationDTO.setMemberType(groupRelationDO.getMemberType());
        groupRelationDTO.setStatus(groupRelationDO.getStatus());
        groupRelationDTO.setIsDeleted(groupRelationDO.getIsDeleted());
        groupRelationDTO.setGmtCreate(groupRelationDO.getGmtCreate());
        groupRelationDTO.setGmtModified(groupRelationDO.getGmtModified());
        groupRelationDTO.setPlatformType(PlatformTypeEnum.valueOf(groupRelationDO.getPlatformType()));
        groupRelationDTO.setExtraInfo(groupRelationDO.getExtraInfo());
        groupRelationDTO.setLeaveTime(groupRelationDO.getLeaveTime());
        groupRelationDTO.setShardingKey(groupRelationDO.getShardingKey());

        return groupRelationDTO;
    }

    /**
     * DTO转DO
     *
     * @param groupRelationDTO
     * @return
     */
    public static GroupRelationDO convert2DO(GroupRelationDTO groupRelationDTO) {
        if (groupRelationDTO == null) {
            return null;
        }
        GroupRelationDO groupRelationDO = new GroupRelationDO();

        // 基础字段
        groupRelationDO.setId(groupRelationDTO.getId());
        groupRelationDO.setChatId(groupRelationDTO.getChatId());
        groupRelationDO.setUserId(groupRelationDTO.getUserId());
        groupRelationDO.setCorpId(groupRelationDTO.getCorpId());
        groupRelationDO.setPlatformType(groupRelationDTO.getPlatformType().getCode().byteValue());
        groupRelationDO.setJoinTime(groupRelationDTO.getJoinTime());
        groupRelationDO.setLeaveTime(groupRelationDTO.getLeaveTime());

        // 数值类型字段 - 设置默认值
        groupRelationDO.setUserType(groupRelationDTO.getUserType() != null ? groupRelationDTO.getUserType() : (byte) 0);
        groupRelationDO.setJoinScene(groupRelationDTO.getJoinScene() != null ? groupRelationDTO.getJoinScene() : (byte) 0);
        groupRelationDO.setMemberType(groupRelationDTO.getMemberType() != null ? groupRelationDTO.getMemberType() : (byte) 0);
        groupRelationDO.setStatus(groupRelationDTO.getStatus() != null ? groupRelationDTO.getStatus() : (byte) 1);
        groupRelationDO.setIsDeleted(groupRelationDTO.getIsDeleted() != null ? groupRelationDTO.getIsDeleted() : (byte) 0);

        // 字符串字段 - 为空时设置空字符串
        groupRelationDO.setGroupName(groupRelationDTO.getGroupName() != null ? groupRelationDTO.getGroupName() : "");
        groupRelationDO.setName(groupRelationDTO.getName() != null ? groupRelationDTO.getName() : "");
        groupRelationDO.setNickname(groupRelationDTO.getNickname() != null ? groupRelationDTO.getNickname() : "");
        groupRelationDO.setInvitor(groupRelationDTO.getInvitor() != null ? groupRelationDTO.getInvitor() : "");
        groupRelationDO.setState(groupRelationDTO.getState() != null ? groupRelationDTO.getState() : "");
        groupRelationDO.setUnionId(groupRelationDTO.getUnionId() != null ? groupRelationDTO.getUnionId() : "");
        groupRelationDO.setRemark(groupRelationDTO.getRemark() != null ? groupRelationDTO.getRemark() : "");
        groupRelationDO.setExtraInfo(groupRelationDTO.getExtraInfo() != null ? groupRelationDTO.getExtraInfo() : "");


        return groupRelationDO;
    }

    public List<GroupRelationDO> convertWithId(GroupRelationDTO groupRelationDTO) {
        List<GroupRelationDO> list = new ArrayList<>();
        GroupRelationDO groupRelationDO = convert2DO(groupRelationDTO);
        groupRelationDO.setShardingKey(userId2ShardingKey(groupRelationDO.getUserId()));
        groupRelationDO.setId(sequence.nextValue());
        groupRelationDO.setGmtCreate(new Date());
        groupRelationDO.setGmtModified(new Date());
        list.add(groupRelationDO);
        GroupRelationDO groupRelationDO2 = convert2DO(groupRelationDTO);
        groupRelationDO2.setShardingKey(chatId2ShardingKey(groupRelationDTO.getChatId()));
        groupRelationDO2.setId(sequence.nextValue());
        groupRelationDO2.setGmtCreate(new Date());
        groupRelationDO2.setGmtModified(new Date());
        list.add(groupRelationDO2);
        return list;
    }


    /**
     * DO列表转DTO列表
     *
     * @param groupRelationDOS
     * @return
     */
    public static List<GroupRelationDTO> convert2DTOs(List<GroupRelationDO> groupRelationDOS) {
        if (groupRelationDOS == null) {
            return null;
        }

        List<GroupRelationDTO> groupRelationDTOS = new ArrayList<>();
        for (GroupRelationDO groupRelationDO : groupRelationDOS) {
            groupRelationDTOS.add(convert2DTO(groupRelationDO));
        }
        return groupRelationDTOS;
    }

    /**
     * DTO列表转DO列表
     *
     * @param groupRelationDTOS
     * @return
     */
    public static List<GroupRelationDO> convert2DOs(List<GroupRelationDTO> groupRelationDTOS) {
        if (groupRelationDTOS == null) {
            return null;
        }

        List<GroupRelationDO> groupRelationDOS = new ArrayList<>();
        for (GroupRelationDTO groupRelationDTO : groupRelationDTOS) {
            groupRelationDOS.add(convert2DO(groupRelationDTO));
        }
        return groupRelationDOS;
    }


    public String userId2ShardingKey(String userId) {
        return "1_" + userId;
    }

    public String chatId2ShardingKey(String chatId) {
        return "2_" + chatId;
    }
}