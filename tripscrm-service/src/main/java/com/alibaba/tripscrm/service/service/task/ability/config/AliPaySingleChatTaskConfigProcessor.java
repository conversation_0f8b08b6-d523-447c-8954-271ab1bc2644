package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 支付宝商家小助手定向消息任务配置
 *
 * <AUTHOR>
 * @since 2025-09-15
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AliPaySingleChatTaskConfigProcessor extends AbstractTaskConfigProcessor {

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {

    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {

    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {

    }

    @Override
    public TaskType getTaskType() {
        return TaskType.ALIPAY_DIRECT_MSG;
    }
}
