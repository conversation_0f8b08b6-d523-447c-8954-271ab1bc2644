package com.alibaba.tripscrm.service.service.task;

import com.alibaba.tripscrm.dal.model.domain.data.EventSourceDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.EventSourceQuery;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-01 10:27:44
 */
public interface EventSourceService {
    /**
     * 插入单条记录
     *
     * @param record 插入的记录
     * @return id
     */
    Long insert(EventSourceDO record);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return EventSourceDO
     */
    EventSourceDO selectById(Long id);

    /**
     * 根据条件查询（分页）
     *
     * @param query 查询条件
     * @return PageInfo<EventSourceDO>
     */
    PageInfo<EventSourceDO> pageQuery(EventSourceQuery query);

    /**
     * 根据条件查询
     *
     * @param query 查询条件
     * @return List<EventSourceDO>
     */
    List<EventSourceDO> selectByCondition(EventSourceQuery query);

    /**
     * 全量查询
     *
     * @return List<EventSourceDO>
     */
    List<EventSourceDO> selectAll();

    /**
     * 根据主键更新
     *
     * @param record 新的记录（只更新非空字段）
     * @return 影响行数
     */
    Integer updateById(EventSourceDO record);

    /**
     * 事件源上线
     *
     * @param id 主键
     * @return 影响行数
     */
    Integer online(Long id);

    /**
     * 事件源下线
     *
     * @param id 主键
     * @return 影像行数
     */
    Integer offline(Long id);
}
