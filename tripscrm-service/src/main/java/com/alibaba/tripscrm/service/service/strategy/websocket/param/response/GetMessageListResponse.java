package com.alibaba.tripscrm.service.service.strategy.websocket.param.response;

import lombok.Data;

import java.util.List;

/**
 * 获取消息列表 推送体
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class GetMessageListResponse {
    /**
     * 会话id 1:群聊会话id，2:私聊时，企微客户的userId，3:私聊时，企微成员的userId
     */
    private String chatId;
    /**
     * 会话类型 1:群聊，2:与企微客户的私聊，3:与企微成员的私聊
     * @see ChatTypeEnum
     */
    private Integer chatType;
    /**
     * 消息列表
     */
    private List<GetMessageListResponseBody> messageList;
}
