package com.alibaba.tripscrm.service.service.strategy.websocket.param.response;

import lombok.Data;

/**
 * 更新用户会话的配置 推送体
 *
 * <AUTHOR>
 * @date 2023/10/09
 */
@Data
public class RecallMessageResponse {
    /**
     * 会话id 1:群聊会话id，2:私聊时，企微客户的userId，3:私聊时，企微成员的userId
     */
    private String chatId;
    /**
     * 会话类型 1:群聊，2:与企微客户的私聊，3:与企微成员的私聊
     * @see ChatTypeEnum
     */
    private Integer chatType;
    /**
     * 消息id
     */
    private String messageId;
}
