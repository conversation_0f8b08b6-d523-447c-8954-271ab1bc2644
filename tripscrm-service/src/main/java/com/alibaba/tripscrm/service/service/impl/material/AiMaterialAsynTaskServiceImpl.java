package com.alibaba.tripscrm.service.service.impl.material;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.tddl.AiMaterialAsynTaskMapper;
import com.alibaba.tripscrm.dal.model.domain.data.AiMaterialAsynTaskDO;
import com.alibaba.tripscrm.dal.model.domain.data.AiMaterialAsynTaskParam;
import com.alibaba.tripscrm.service.enums.material.AiMaterialAsynTaskStatusEnum;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.model.common.ResultDO;
import com.alibaba.tripscrm.service.model.domain.query.AiMaterialAsynTaskQuery;
import com.alibaba.tripscrm.service.model.dto.material.AiMaterialAsynTaskDTO;
import com.alibaba.tripscrm.service.service.material.AiMaterialAsynTaskService;
import com.taobao.util.CollectionUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @DECLARATION: THE AUTHORITY OF CODE BELONGS TO ENTERPRISE TECHNOLOGY GROUP OF FEIZHU
 * @NOTE:
 * @AUTHOR: benjamin.zhm
 * @DATE: 2025/7/9
 **/
@Service
public class AiMaterialAsynTaskServiceImpl implements AiMaterialAsynTaskService {

    @Resource
    private AiMaterialAsynTaskMapper aiMaterialAsynTaskMapper;

    @Override
    public ResultDO<List<AiMaterialAsynTaskDTO>> getAiMaterialAsynTask(AiMaterialAsynTaskQuery query) {
        if (query == null) {
            PlatformLogUtil.logFail("AI素材异步任务查询入参非法", LogListUtil.newArrayList(query));
            return ResultDO.falseInstance(ErrorCodeEnum.PARAM_INVALID);
        }
        if (query.getId() != null) {
            return getListResultDOById(query);
        }
        if (query.getTaskId() == null && query.getMaterialId() == null && query.getOutBizId() == null && query.getRequestId() == null && query.getTargetId() == null) {
            PlatformLogUtil.logFail("AI素材异步任务查询入参非法", LogListUtil.newArrayList(query));
            return ResultDO.falseInstance(ErrorCodeEnum.PARAM_INVALID);
        }
        return getListResultDOByParam(query);
    }

    @Override
    public ResultDO<AiMaterialAsynTaskDTO> upsertAiMaterialAsynTask(AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO) {
        if (aiMaterialAsynTaskDTO == null) {
            PlatformLogUtil.logFail("AI素材异步任务操作入参非法", LogListUtil.newArrayList(aiMaterialAsynTaskDTO));
            return ResultDO.falseInstance(ErrorCodeEnum.PARAM_INVALID);
        }
        if (aiMaterialAsynTaskDTO.getId() != null) {
            return getUpdateResultDO(aiMaterialAsynTaskDTO);
        }
        return getInsertResultDO(aiMaterialAsynTaskDTO);
    }

    private @NotNull ResultDO<List<AiMaterialAsynTaskDTO>> getListResultDOByParam(AiMaterialAsynTaskQuery query) {
        AiMaterialAsynTaskParam taskParam = new AiMaterialAsynTaskParam();
        taskParam.appendOrderByClause(AiMaterialAsynTaskParam.OrderCondition.GMTMODIFIED, AiMaterialAsynTaskParam.SortType.DESC);
        AiMaterialAsynTaskParam.Criteria criteria = taskParam.createCriteria();
        appendCriteria(query, criteria);
        List<AiMaterialAsynTaskDO> taskDOS = aiMaterialAsynTaskMapper.selectByParam(taskParam);
        if (CollectionUtil.isEmpty(taskDOS)) {
            PlatformLogUtil.logFail("AI素材异步任务查询为空", LogListUtil.newArrayList(query));
            return ResultDO.falseInstance(ErrorCodeEnum.AI_MATERIAL_ASYN_TASK_NOT_EXIST);
        }
        List<AiMaterialAsynTaskDTO> taskDTOS = new ArrayList<>();
        taskDOS.forEach(taskDO -> taskDTOS.add(getAiMaterialAsynTaskDTO(taskDO)));
        return ResultDO.successInstance(taskDTOS);
    }

    private static void appendCriteria(AiMaterialAsynTaskQuery query, AiMaterialAsynTaskParam.Criteria criteria) {
        if (query.getTaskId() != null) {
            criteria.andTaskIdEqualTo(query.getTaskId());
        }
        if (query.getMaterialId() != null) {
            criteria.andMaterialIdEqualTo(query.getMaterialId());
        }
        if (query.getOutBizId() != null) {
            criteria.andOutBizIdEqualTo(query.getOutBizId());
        }
        if (query.getRequestId() != null) {
            criteria.andRequestIdEqualTo(query.getRequestId());
        }
        if(query.getTargetId() != null){
            criteria.andTargetIdEqualTo(query.getTargetId());
        }
        if(query.getDeleted() != null){
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        if(query.getStatus() != null){
            criteria.andStatusEqualTo(query.getStatus());
        }
    }

    private @NotNull ResultDO<List<AiMaterialAsynTaskDTO>> getListResultDOById(AiMaterialAsynTaskQuery query) {
        AiMaterialAsynTaskDO taskDO = aiMaterialAsynTaskMapper.selectByPrimaryKey(query.getId());
        if (taskDO == null) {
            PlatformLogUtil.logFail("AI素材异步任务查询失败", LogListUtil.newArrayList(query));
            return ResultDO.falseInstance(ErrorCodeEnum.AI_MATERIAL_ASYN_TASK_NOT_EXIST);
        }
        AiMaterialAsynTaskDTO taskDTO = getAiMaterialAsynTaskDTO(taskDO);
        return ResultDO.successInstance(LogListUtil.newArrayList(taskDTO));
    }

    private static @NotNull AiMaterialAsynTaskDTO getAiMaterialAsynTaskDTO(AiMaterialAsynTaskDO taskDO) {
        AiMaterialAsynTaskDTO taskDTO = new AiMaterialAsynTaskDTO();
        taskDTO.setId(taskDO.getId());
        taskDTO.setGmtCreate(taskDO.getGmtCreate());
        taskDTO.setGmtModified(taskDO.getGmtModified());
        taskDTO.setStatus(taskDO.getStatus());
        taskDTO.setDeleted(taskDO.getDeleted());
        taskDTO.setTaskId(taskDO.getTaskId());
        taskDTO.setMaterialId(taskDO.getMaterialId());
        taskDTO.setOutBizId(taskDO.getOutBizId());
        taskDTO.setExtInfo(taskDO.getExtInfo());
        taskDTO.setTargetId(taskDO.getTargetId());
        taskDTO.setTargetType(taskDO.getTargetType());
        taskDTO.setRequestId(taskDO.getRequestId());
        taskDTO.setResult(taskDO.getResult());
        return taskDTO;
    }

    private @NotNull ResultDO<AiMaterialAsynTaskDTO> getInsertResultDO(AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO) {
        AiMaterialAsynTaskDO taskDO = getAiMaterialAsynTaskDO(aiMaterialAsynTaskDTO);
        int effect = aiMaterialAsynTaskMapper.insert(taskDO);
        if (effect <= 0 || taskDO.getId() == null) {
            PlatformLogUtil.logFail("插入AI素材异步任务失败", LogListUtil.newArrayList(aiMaterialAsynTaskDTO));
            return ResultDO.falseInstance(ErrorCodeEnum.AI_MATERIAL_ASYN_TASK_UPSERT_DB_FAIL);
        }
        AiMaterialAsynTaskDO asyncTaskDO = aiMaterialAsynTaskMapper.selectByPrimaryKey(taskDO.getId());
        return ResultDO.successInstance(getAiMaterialAsynTaskDTO(asyncTaskDO));
    }

    private @NotNull ResultDO<AiMaterialAsynTaskDTO> getUpdateResultDO(AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO) {
        AiMaterialAsynTaskDO aiMaterialAsynTaskDO = new AiMaterialAsynTaskDO();
        aiMaterialAsynTaskDO.setOutBizId(aiMaterialAsynTaskDTO.getOutBizId());
        aiMaterialAsynTaskDO.setExtInfo(aiMaterialAsynTaskDTO.getExtInfo());
        aiMaterialAsynTaskDO.setStatus(aiMaterialAsynTaskDTO.getStatus());
        aiMaterialAsynTaskDO.setDeleted(aiMaterialAsynTaskDTO.getDeleted());
        aiMaterialAsynTaskDO.setResult(aiMaterialAsynTaskDTO.getResult());
        aiMaterialAsynTaskDO.setGmtModified(new Date());
        aiMaterialAsynTaskDO.setId(aiMaterialAsynTaskDTO.getId());
        int effect = aiMaterialAsynTaskMapper.updateByPrimaryKeySelective(aiMaterialAsynTaskDO);
        if (effect <= 0) {
            PlatformLogUtil.logFail("更新AI素材异步任务失败", LogListUtil.newArrayList(aiMaterialAsynTaskDTO));
            return ResultDO.falseInstance(ErrorCodeEnum.AI_MATERIAL_ASYN_TASK_UPSERT_DB_FAIL);
        }
        AiMaterialAsynTaskDO asynTaskDO = aiMaterialAsynTaskMapper.selectByPrimaryKey(aiMaterialAsynTaskDO.getId());
        return ResultDO.successInstance(getAiMaterialAsynTaskDTO(asynTaskDO));
    }

    private static @NotNull AiMaterialAsynTaskDO getAiMaterialAsynTaskDO(AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO) {
        AiMaterialAsynTaskDO aiMaterialAsynTaskDO = new AiMaterialAsynTaskDO();
        aiMaterialAsynTaskDO.setId(aiMaterialAsynTaskDTO.getId());
        aiMaterialAsynTaskDO.setTaskId(aiMaterialAsynTaskDTO.getTaskId());
        aiMaterialAsynTaskDO.setMaterialId(aiMaterialAsynTaskDTO.getMaterialId());
        aiMaterialAsynTaskDO.setOutBizId(aiMaterialAsynTaskDTO.getOutBizId());
        aiMaterialAsynTaskDO.setExtInfo(aiMaterialAsynTaskDTO.getExtInfo());
        aiMaterialAsynTaskDO.setTargetId(aiMaterialAsynTaskDTO.getTargetId());
        aiMaterialAsynTaskDO.setTargetType(aiMaterialAsynTaskDTO.getTargetType());
        aiMaterialAsynTaskDO.setStatus(AiMaterialAsynTaskStatusEnum.INIT.getCode());
        aiMaterialAsynTaskDO.setDeleted(IsDeleteEnum.NO.getCode());
        aiMaterialAsynTaskDO.setGmtCreate(new Date());
        aiMaterialAsynTaskDO.setGmtModified(new Date());
        aiMaterialAsynTaskDO.setRequestId(aiMaterialAsynTaskDTO.getRequestId());
        aiMaterialAsynTaskDO.setResult(aiMaterialAsynTaskDTO.getResult());
        return aiMaterialAsynTaskDO;
    }
}
