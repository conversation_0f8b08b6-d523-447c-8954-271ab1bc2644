package com.alibaba.tripscrm.service.service.strategy.account;

import com.alibaba.buc.sso.client.util.SimpleUserUtil;
import com.alibaba.buc.sso.client.vo.BucSSOUser;
import com.alibaba.ihr.amdplatform.common.util.AmdpResponseUtil;
import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import com.alibaba.ihr.amdplatform.service.dto.QueryResultDTO;
import com.alibaba.ihr.amdplatform.service.dto.ResultDTO;
import com.alibaba.ihr.amdplatform.service.param.AuthParam;
import com.alibaba.ihr.amdplatform.service.param.DataQueryParam;
import com.alibaba.ihr.amdplatform.service.param.FilterField;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.AMDPCombine1784;
import com.alibaba.tripscrm.service.model.domain.AMDPCombine2132;
import com.alibaba.tripscrm.service.model.domain.AMDPCombine2132.EmpEmployee;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.UserInfoVO;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class BucManager implements AccountManager {
    private final static Logger logger = LoggerFactory.getLogger(BucManager.class);

    private final static String KEY_HAS_ERROR = "hasError";
    private final static String KEY_CONTENT = "content";

    @Value("${amdp.app-key}")
    private String amdpAppKey;

    @Value("${amdp.app-secret}")
    private String amdpAppSecret;

    @Value("${amdp.combine-id}")
    private Long amdpCombineId;

    @Value("${amdp.search-combine-id}")
    private Long amdpSearchCombineId;

    private static Cache<String, EmpEmployee> empId2EmployeeCache = CacheBuilder.newBuilder().
            maximumSize(256).
            recordStats()
            .build();

    @Resource
    private AmdpDataQueryService amdpDataQueryService;
    @Resource
    @Lazy
    private BucManager bucManager;

    @Override
    public boolean support(String serverName) {
        return SwitchConfig.BUC_ACCOUNT_SERVER_NAME_SUPPORT.contains(serverName);
    }

    @Override
    public boolean supportQuery(String serverName) {
        return !SwitchConfig.TAO_BAO_ACCOUNT_SERVER_NAME_SUPPORT.contains(serverName);
    }

    /**
     * empId相等判断，会去除0前缀
     *
     * @param empId1 工号1
     * @param empId2 工号2
     * @return 相等返回true, 不相等或任一empId为空返回false
     */
    public static boolean empIdEquals(String empId1, String empId2) {

        empId1 = formatEmpId(empId1);
        empId2 = formatEmpId(empId2);
        if (StringUtils.isBlank(empId1) || StringUtils.isBlank(empId2)) {
            return false;
        }
        return empId1.equals(empId2);
    }

    /**
     * 获取用户信息，只能在web主线程中使用
     *
     * @return 用户信息
     */
    @Override
    public User getUserInWebThread() {
        return convertToUser(getBucUserInWebThread());
    }

    /**
     * 获取用户姓名-工号
     *
     * @return 用户信息
     */
    @Override
    public String getUserNameAndEmpId() {
        BucSSOUser ssoUser = getBucUserInWebThread();
        if (Objects.isNull(ssoUser)) {
            return null;
        }
        if (StringUtils.isNotEmpty(ssoUser.getNickNameCn())) {
            return ssoUser.getNickNameCn() + "-" + ssoUser.getEmpId();
        }
        return ssoUser.getLastName() + "-" + ssoUser.getEmpId();
    }

    /**
     * 获取用户信息，只能在web主线程中调用
     *
     * @return Buc用户信息
     */
    public BucSSOUser getBucUserInWebThread() {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes())
                    .getRequest();
            BucSSOUser user = SimpleUserUtil.getBucSSOUser(request);
            return user;
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 从当前线程中获取用户信息
     * @return UserInfoVO
     */
    @Override
    public UserInfoVO getUserVoInWebThread() {
        User user = getUserInWebThread();
        BucSSOUser ssoUser = getBucUserInWebThread();
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setEmpId(user.getUserId());
        userInfoVO.setUserName(user.getUserName());
        userInfoVO.setNickNameCn(ssoUser.getNickNameCn());
        userInfoVO.setBucId(String.valueOf(ssoUser.getId()));
        userInfoVO.setAccount(ssoUser.getAccount());
        userInfoVO.setLastName(ssoUser.getLastName());
        userInfoVO.setNameAndEmpId(getUserNameAndEmpId());
        userInfoVO.setIsInnerStaff(true);
        return userInfoVO;
    }

    /**
     * 获取用户信息，可以在任意线程调用
     *
     * @param request web请求
     * @return 用户信息
     */
    public BucSSOUser getBucUser(HttpServletRequest request) {
        try {
            BucSSOUser user = SimpleUserUtil.getBucSSOUser(request);
            return user;
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据工号获取用户信息
     *
     * @param empId 工号
     * @return 用户信息
     */
    @Override
    public User getUserByAccountId(String empId) {
        String formatId = formatEmpId(empId);

        EmpEmployee empEmployee = bucManager.getUserInfoByEmpId(formatId);
        if (Objects.isNull(empEmployee)) {
            return null;
        }

        String userName = getUserName(empEmployee.getName(), empEmployee.getNickName());
        return new User(empId, userName, true);
    }


    /**
     * 根据工号获取用户信息
     *
     * @param empId 工号
     * @return 用户信息
     */
    @Override
    public UserInfoVO getUserAndAccountIdByAccountId(String empId) {
        String formatId = formatEmpId(empId);

        EmpEmployee empEmployee = bucManager.getUserInfoByEmpId(formatId);
        if (Objects.isNull(empEmployee)) {
            return null;
        }
        UserInfoVO userInfoVO = new UserInfoVO();
        StringBuilder nameAndEmpId = new StringBuilder();
        if (StringUtils.isNotEmpty(empEmployee.getNickName())) {
            nameAndEmpId.append(empEmployee.getNickName());
        } else {
            nameAndEmpId.append(empEmployee.getName());
        }
        nameAndEmpId.append("-");
        nameAndEmpId.append(empEmployee.getWorkNo());
        userInfoVO.setNameAndEmpId(nameAndEmpId.toString());
        userInfoVO.setEmpId(empEmployee.getWorkNo());
        userInfoVO.setNickNameCn(empEmployee.getNickName());
        userInfoVO.setIsInnerStaff(true);
        userInfoVO.setLastName(empEmployee.getName());
        return userInfoVO;
    }

    /**
     * 批量根据工号获取用户信息
     *
     * @param empIds 工号列表
     * @return 用户信息列表
     */
    @Override
    public List<UserInfoVO> batchGetUserByEmpId(List<String> empIds) {
        List<UserInfoVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(empIds)) {
            return result;
        }

        List<EmpEmployee> empEmployees = batchGetUserInfoByEmpId(empIds);
        return batchConvertToUser(empEmployees);
    }

    /**
     * 根据工号获取用户原始信息
     *
     * @param empId 工号
     * @return 用户信息
     */
    @Cacheable("getUserInfoByEmpId")
    public EmpEmployee getUserInfoByEmpId(String empId) {
        if (StringUtils.isBlank(empId)) {
            return null;
        }

        List<EmpEmployee> list = batchGetUserInfoByEmpId(Lists.newArrayList(empId));
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    /**
     * 根据工号列表批量获取用户信息
     *
     * @param empIdList 工号列表
     * @return 用户信息列表
     */
    public List<EmpEmployee> batchGetUserInfoByEmpId(List<String> empIdList) {
        if (CollectionUtils.isEmpty(empIdList)) {
            return null;
        }

        List<EmpEmployee> result = Lists.newArrayList();
        // 优先使用缓存
        List<String> noCacheEmpIdList = Lists.newArrayList();
        for (String empId : empIdList) {
            String formatEmpId = formatEmpId(empId);
            EmpEmployee cacheResult = empId2EmployeeCache.getIfPresent(formatEmpId);
            if (Objects.nonNull(cacheResult)) {
                result.add(cacheResult);
            } else {
                noCacheEmpIdList.add(empId);
            }
        }
        if (CollectionUtils.isEmpty(noCacheEmpIdList)) {
            return result;
        }

        try {
            // 批量请求
            AuthParam authParam = new AuthParam(amdpAppKey, amdpAppSecret);
            DataQueryParam dataQueryParam = new DataQueryParam();
            dataQueryParam.setCombineId(amdpCombineId);

            List<String> workNoList = noCacheEmpIdList.stream().map(BucManager::formatEmpId).collect(Collectors.toList());
            List<FilterField> filterFields = Lists.newArrayList();
            filterFields.add(new FilterField("workNoList", workNoList));
            dataQueryParam.setFilterFieldList(filterFields);
            ResultDTO<QueryResultDTO> resultDTO = amdpDataQueryService.queryDataSet(authParam, dataQueryParam);
            PlatformLogUtil.logFail("getUserInfoByEmpIdList queryDataSet", LogListUtil.newArrayList(empIdList, authParam, dataQueryParam, filterFields, resultDTO));
            List<AMDPCombine2132> combine2132s = AmdpResponseUtil.convertCombineResultToBeanList(resultDTO, AMDPCombine2132.class);
            if (CollectionUtils.isNotEmpty(combine2132s)) {
                List<EmpEmployee> empEmployees = combine2132s.stream()
                        .map(AMDPCombine2132::getEmpEmployee)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                for (EmpEmployee empEmployee : empEmployees) {
                    result.add(empEmployee);
                    empId2EmployeeCache.put(formatEmpId(empEmployee.getWorkNo()), empEmployee);
                }
            }

            return result;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(empIdList));
        }

        return null;
    }

    /**
     * 标准化工号，数字类型的工号，去掉前面的0
     *
     * @param empId 工号
     * @return 标准化工号
     */
    public static String formatEmpId(String empId) {
        if (StringUtils.isNumeric(empId)) {
            return String.valueOf(Long.valueOf(empId));
        }

        if (StringUtils.isNotBlank(empId)) {
            return empId.toUpperCase();
        } else {
            return empId;
        }
    }

    private String getUserName(String realName, String nickNamecn) {
        String result = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(realName)) {
            result = realName;
            if (StringUtils.isNotBlank(nickNamecn)) {
                result += "(" + nickNamecn + ")";
            }
        } else {
            if (StringUtils.isNotBlank(nickNamecn)) {
                result = nickNamecn;
            }
        }

        return result;
    }

    /**
     * 转为User
     *
     * @param ssoUser buc用户信息
     * @return User
     */
    private User convertToUser(BucSSOUser ssoUser) {
        if (Objects.isNull(ssoUser)) {
            return null;
        }

        String userName = getUserName(ssoUser.getLastName(), ssoUser.getNickNameCn());
        return new User(ssoUser.getEmpId(), userName, true);
    }

    /**
     * 转为User
     *
     * @param empEmployee 员工信息
     * @return User
     */
    private User convertToUser(EmpEmployee empEmployee) {
        if (Objects.isNull(empEmployee)) {
            return null;
        }

        String userName = getUserName(empEmployee.getName(), empEmployee.getNickName());
        return new User(empEmployee.getWorkNo(), userName, true);
    }

    /**
     * 批量将员工信息转为user
     *
     * @param empEmployees 员工信息列表
     * @return user列表
     */
    private List<UserInfoVO> batchConvertToUser(List<EmpEmployee> empEmployees) {
        List<UserInfoVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(empEmployees)) {
            return result;
        }

        for (EmpEmployee empEmployee : empEmployees) {
            UserInfoVO userInfoVO = new UserInfoVO();
            StringBuilder nameAndEmpId = new StringBuilder();
            if (StringUtils.isNotEmpty(empEmployee.getNickName())) {
                nameAndEmpId.append(empEmployee.getNickName());
            } else {
                nameAndEmpId.append(empEmployee.getName());
            }
            nameAndEmpId.append("-");
            nameAndEmpId.append(empEmployee.getWorkNo());
            userInfoVO.setNameAndEmpId(nameAndEmpId.toString());
            userInfoVO.setEmpId(empEmployee.getWorkNo());
            userInfoVO.setNickNameCn(empEmployee.getNickName());
            userInfoVO.setLastName(empEmployee.getName());
            userInfoVO.setIcon(empEmployee.getPersonalPhotoUrl());
            userInfoVO.setIsInnerStaff(true);
            result.add(userInfoVO);
        }

        return result;
    }

    /**
     * 批量将员工信息转为user
     *
     * @param empEmployees 员工信息列表
     * @return user列表
     */
    private List<UserInfoVO> batchConvert1784ToUser(List<AMDPCombine1784.EmpEmployee> empEmployees) {
        List<UserInfoVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(empEmployees)) {
            return result;
        }

        for (AMDPCombine1784.EmpEmployee empEmployee : empEmployees) {
            UserInfoVO userInfoVO = new UserInfoVO();
            StringBuilder nameAndEmpId = new StringBuilder();
            if (StringUtils.isNotEmpty(empEmployee.getNickName())) {
                nameAndEmpId.append(empEmployee.getNickName());
            } else {
                nameAndEmpId.append(empEmployee.getName());
            }
            nameAndEmpId.append("-");
            nameAndEmpId.append(empEmployee.getWorkNo());
            userInfoVO.setNameAndEmpId(nameAndEmpId.toString());
            userInfoVO.setEmpId(empEmployee.getWorkNo());
            userInfoVO.setNickNameCn(empEmployee.getNickName());
            userInfoVO.setLastName(empEmployee.getName());
            userInfoVO.setIsInnerStaff(true);
            userInfoVO.setIcon(empEmployee.getPersonalPhotoUrl());
            result.add(userInfoVO);
        }

        return result;
    }

    /**
     * 搜索员工信息
     *
     * @param keyword 关键字
     * @return 用户信息列表
     */
    @Override
    public List<UserInfoVO> searchUser(String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return new ArrayList<>();
        }

        List<AMDPCombine1784.EmpEmployee> empEmployees = searchUserInfo(keyword);
        return batchConvert1784ToUser(empEmployees);
    }

    private List<AMDPCombine1784.EmpEmployee> searchUserInfo(String keyword) {
        List<AMDPCombine1784.EmpEmployee> result = Lists.newArrayList();

        try {
            // 批量请求
            AuthParam authParam = new AuthParam(amdpAppKey, amdpAppSecret);
            DataQueryParam dataQueryParam = new DataQueryParam();
            dataQueryParam.setCombineId(amdpSearchCombineId);

            List<FilterField> filterFields = Lists.newArrayList();
            filterFields.add(new FilterField("searchKey", keyword));
            filterFields.add(new FilterField("workStatus", "A"));
            filterFields.add(new FilterField("pageSize", 20));
            dataQueryParam.setFilterFieldList(filterFields);
            ResultDTO<QueryResultDTO> resultDTO = amdpDataQueryService.queryDataSet(authParam, dataQueryParam);
            logger.info("searchUserInfo queryDataSet, authParam:{}, dataQueryParam:{}, filterFields:{}, resultDTO:{}", authParam, dataQueryParam, filterFields, resultDTO);
            List<AMDPCombine1784> combine1784s = AmdpResponseUtil.convertCombineResultToBeanList(resultDTO, AMDPCombine1784.class);
            if (CollectionUtils.isNotEmpty(combine1784s)) {
                result = combine1784s.stream()
                        .map(AMDPCombine1784::getEmpEmployee)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }

            return result;
        } catch (Exception e) {
            logger.error("searchUserInfo error, keyword:{}", keyword, e);
        }

        return result;
    }
}
