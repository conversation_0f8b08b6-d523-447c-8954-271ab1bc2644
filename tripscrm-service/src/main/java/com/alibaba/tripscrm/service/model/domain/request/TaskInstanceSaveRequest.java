package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TaskInstanceSaveRequest implements Serializable {

    private static final long serialVersionUID = -4535749078388092426L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 执行开始时间
     */
    private Date startTime;

    /**
     * 执行结束时间
     */
    private Date endTime;

    /**
     * 执行状态
     */
    private String status;

    /**
     * 扩展字段
     */
    private String extInfo;

    /**
     * 任务快照，执行时只取这里面的配置数据
     */
    private String taskConfig;

    /**
     * 素材快照，执行时只取这里面的配置数据
     */
    private String materialInfo;

    /**
     * ab分桶快照，执行时只取这里面的配置数据
     */
    private String abBucketList;

}
