package com.alibaba.tripscrm.service.service.impl.hsf;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.dal.repository.WechatGroupRepository;
import com.alibaba.tripscrm.domain.WechatGroupInfo;
import com.alibaba.tripscrm.domain.WechatGroupMemberDTO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.request.ListGroupByOwnerRequest;
import com.alibaba.tripscrm.domain.request.SetOrUpdateGroupAdminRequest;
import com.alibaba.tripscrm.domain.request.TransferGroupOwnerRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMEnterpriseWechatGroupAsyncCreateRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMEnterpriseWechatGroupAsyncInviteRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMEnterpriseWechatGroupChatRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMGroupPublicNoticeRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMRemoveGroupMembersRequest;
import com.alibaba.tripscrm.domain.response.wechat.group.TripSCRMEnterpriseWechatAdminsUpdateResponse;
import com.alibaba.tripscrm.domain.response.wechat.group.TripSCRMEnterpriseWechatGroupAsyncCreateResponse;
import com.alibaba.tripscrm.domain.response.wechat.group.TripSCRMEnterpriseWechatGroupAsyncInviteResponse;
import com.alibaba.tripscrm.domain.response.wechat.group.TripSCRMEnterpriseWechatGroupUpdateOwnerResponse;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.wechat.EnterpriseWechatGroupChatService;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.ErrorCodeEnum;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.request.GroupCreateRequest;
import com.alibaba.tripzoo.proxy.request.GroupInviteJoinRequest;
import com.alibaba.tripzoo.proxy.request.RemoveGroupMembersRequest;
import com.alibaba.tripzoo.proxy.request.SingleGroupUpdateRequest;
import com.alibaba.tripzoo.proxy.request.WechatGroupPublishNoticeRequest;
import com.alibaba.tripzoo.proxy.result.GroupInviteJoinResponse;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.OperatorResult;
import com.alibaba.tripzoo.proxy.result.response.OperatorResult.Detail;

import com.ali.com.google.common.collect.Lists;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/5/14 下午3:54
 * @Filename：EnterpriseWechatGroupChatServiceImpl
 */
@Slf4j
@Service
@HSFProvider(serviceInterface = EnterpriseWechatGroupChatService.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class EnterpriseWechatGroupChatServiceImpl implements EnterpriseWechatGroupChatService {

    private final GroupRelationRepository groupRelationRepository;
    private final WechatGroupRepository wechatGroupRepository;
    private final GroupService groupService;
    private final WechatUserService wechatUserService;
    private final WechatGroupService wechatGroupService;

    /**
     * 获取群内成员
     *
     * @param request
     * @return
     */
    @Override
    @ServiceLog("企微客户群-获取群内成员")
    public TripSCRMResult<List<WechatGroupMemberDTO>> listGroupMember(TripSCRMEnterpriseWechatGroupChatRequest request) {
        if (Objects.isNull(request) || !StringUtils.hasText(request.getChatId()) || !StringUtils.hasText(request.getCorpId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserType(groupRelationRepository.chatId2ShardingKey(request.getChatId()), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue(), request.getUserType());
        if (CollectionUtils.isEmpty(groupRelationList)) {
            return TripSCRMResult.success(new ArrayList<>());
        }
        List<WechatGroupMemberDTO> wechatGroupMemberDTOList = groupRelationList.stream().map(groupRelationDO -> {
            WechatGroupMemberDTO wechatGroupMemberDTO = new WechatGroupMemberDTO();
            BeanUtils.copyProperties(groupRelationDO, wechatGroupMemberDTO);
            return wechatGroupMemberDTO;
        }).collect(Collectors.toList());
        return TripSCRMResult.success(wechatGroupMemberDTOList);
    }

    @Override
    @ServiceLog("企微客户群-获取群聊信息")
    public TripSCRMResult<WechatGroupInfo> listGroupInfo(TripSCRMEnterpriseWechatGroupChatRequest request) {
        if (Objects.isNull(request) || !StringUtils.hasText(request.getChatId()) || !StringUtils.hasText(request.getCorpId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatGroupDO wechatGroupDO = wechatGroupRepository.getByChatIdAndCorpId(request.getChatId(), request.getCorpId());
        if (Objects.isNull(wechatGroupDO)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NOT_EXIST_GROUP);
        }
        WechatGroupInfo wechatGroupInfo = new WechatGroupInfo();
        wechatGroupInfo.setName(wechatGroupDO.getName());
        wechatGroupInfo.setCreateTime(wechatGroupDO.getCreateTime());
        wechatGroupInfo.setOwnerUserId(wechatGroupDO.getOwnerUser());
        wechatGroupInfo.setChatId(wechatGroupDO.getChatId());
        wechatGroupInfo.setIsDeleted(Optional.ofNullable(wechatGroupDO.getIsDeleted()).orElse((byte)0) == 1);
        return TripSCRMResult.success(wechatGroupInfo);
    }

    @Override
    @ServiceLog("企微客户群-异步创建群聊")
    public TripSCRMResult<TripSCRMEnterpriseWechatGroupAsyncCreateResponse> asyncCreate(TripSCRMEnterpriseWechatGroupAsyncCreateRequest request) {
        if (Objects.isNull(request) || !StringUtils.hasText(request.getGroupName()) || !StringUtils.hasText(request.getUserId()) || !StringUtils.hasText(request.getCorpId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS.getCode(), TripSCRMErrorCode.INVALID_PARAMS.getDescCn());
        }

        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(request.getUserId()));
        if (CollectionUtils.isEmpty(wechatUserList) || !Objects.equals(wechatUserList.get(0).getOnlineStatus(), RobotStatusEnum.ONLINE.getCode())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE.getCode(), TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE.getDescCn());
        }

        GroupCreateRequest groupCreateRequest = new GroupCreateRequest();
        groupCreateRequest.setGroupName(request.getGroupName());
        groupCreateRequest.setUserId(request.getUserId());
        groupCreateRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<String> resultDO = groupService.asyncCreate(groupCreateRequest);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
            PlatformLogUtil.logFail("异步创建群聊接口，调用网关层接口失败", LogListUtil.newArrayList(resultDO));

            ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
            switch (errorCodeEnum) {
                case WECHAT_GROUP_DATA_ERROR:
                case ROBOT_NOT_IN_GROUP:
                case WECHAT_USER_NOT_ONLINE:
                    return TripSCRMResult.fail(TripSCRMErrorCode.SEND_MESSAGE_FAIL);
                case RATE_LIMIT_FILTER:
                    return TripSCRMResult.fail(TripSCRMErrorCode.GATE_WAY_RATE_LIMIT);
                case ISV_RATE_LIMIT:
                    return TripSCRMResult.fail(TripSCRMErrorCode.ISV_RATE_LIMIT);
                case BIZ_ACTION_RISK_LIMIT:
                    return TripSCRMResult.fail(TripSCRMErrorCode.BIZ_ACTION_DEGRADE_LIMIT);
                case BIZ_RATE_LIMIT:
                    return TripSCRMResult.fail(TripSCRMErrorCode.BIZ_RATE_LIMIT);
                default:
                    return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
            }
        }
        TripSCRMEnterpriseWechatGroupAsyncCreateResponse response = new TripSCRMEnterpriseWechatGroupAsyncCreateResponse();
        response.setRequestId(resultDO.getModel());
        return TripSCRMResult.success(response);
    }

    @Override
    @ServiceLog("企微客户群-异步邀请入群")
    public TripSCRMResult<TripSCRMEnterpriseWechatGroupAsyncInviteResponse> asyncInvite(TripSCRMEnterpriseWechatGroupAsyncInviteRequest request) {
        if (Objects.isNull(request)
                || !StringUtils.hasText(request.getChatId())
                || !StringUtils.hasText(request.getInviteUserId())
                || (CollectionUtils.isEmpty(request.getUserIdList()) && CollectionUtils.isEmpty(request.getExternalUserIdList()))) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS.getCode(), TripSCRMErrorCode.INVALID_PARAMS.getDescCn());
        }

        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(request.getInviteUserId()));
        if (CollectionUtils.isEmpty(wechatUserList) || !Objects.equals(wechatUserList.get(0).getOnlineStatus(), RobotStatusEnum.ONLINE.getCode())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE.getCode(), TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE.getDescCn());
        }

        GroupInviteJoinRequest groupInviteJoinRequest = new GroupInviteJoinRequest();
        groupInviteJoinRequest.setUserId(request.getInviteUserId());
        groupInviteJoinRequest.setCorpId(request.getCorpId());
        groupInviteJoinRequest.setChatId(request.getChatId());
        groupInviteJoinRequest.setExternalUserIdList(request.getExternalUserIdList());
        groupInviteJoinRequest.setUserIdList(request.getUserIdList());
        ResultDO<GroupInviteJoinResponse> resultDO = groupService.asyncInviteJoinGroup(groupInviteJoinRequest);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess() || Objects.isNull(resultDO.getModel())) {
            PlatformLogUtil.logFail("邀请入群接口，调用网关层接口失败", LogListUtil.newArrayList(resultDO));

            ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
            switch (errorCodeEnum) {
                case WECHAT_GROUP_DATA_ERROR:
                case ROBOT_NOT_IN_GROUP:
                case WECHAT_USER_NOT_ONLINE:
                    return TripSCRMResult.fail(TripSCRMErrorCode.SEND_MESSAGE_FAIL);
                case RATE_LIMIT_FILTER:
                    return TripSCRMResult.fail(TripSCRMErrorCode.GATE_WAY_RATE_LIMIT);
                case ISV_RATE_LIMIT:
                    return TripSCRMResult.fail(TripSCRMErrorCode.ISV_RATE_LIMIT);
                case BIZ_ACTION_RISK_LIMIT:
                    return TripSCRMResult.fail(TripSCRMErrorCode.BIZ_ACTION_DEGRADE_LIMIT);
                case BIZ_RATE_LIMIT:
                    return TripSCRMResult.fail(TripSCRMErrorCode.BIZ_RATE_LIMIT);
                default:
                    return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
            }
        }

        TripSCRMEnterpriseWechatGroupAsyncInviteResponse response = new TripSCRMEnterpriseWechatGroupAsyncInviteResponse();
        response.setRequestId(resultDO.getModel().getRequestId());
        return TripSCRMResult.success(response);
    }

    @Override
    @ServiceLog("企微客户群-转让群主")
    public TripSCRMResult<TripSCRMEnterpriseWechatGroupUpdateOwnerResponse> transferGroupOwner(TransferGroupOwnerRequest request) {
        if (request == null || !StringUtils.hasText(request.getChatId()) || !StringUtils.hasText(request.getCorpId()) || !StringUtils.hasText(
            request.getNewOwnerUserId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        SingleGroupUpdateRequest updateRequest = new SingleGroupUpdateRequest();
        updateRequest.setChatId(request.getChatId());
        updateRequest.setOwnerUserId(request.getNewOwnerUserId());
        updateRequest.setCorpId(request.getCorpId());
        ResultDO<OperatorResult> resultDO = groupService.asyncSingleUpdate(updateRequest);
        if (resultDO == null || !resultDO.getSuccess() || resultDO.getModel() == null) {
            return TripSCRMResult.fail(resultDO.getResultCode(),resultDO.getResultMessage());
        }
        // 取第一个requestId
        String requestId = resultDO.getModel().getDetailList().stream().map(Detail::getRequestId).findFirst().orElse(null);
        return TripSCRMResult.success(new TripSCRMEnterpriseWechatGroupUpdateOwnerResponse(requestId));
    }

    @Override
    @ServiceLog("企微客户群-设置/修改管理员")
    public TripSCRMResult<TripSCRMEnterpriseWechatAdminsUpdateResponse> setOrUpdateGroupAdmin(SetOrUpdateGroupAdminRequest request) {
        if (request == null || !StringUtils.hasText(request.getChatId()) || !StringUtils.hasText(request.getCorpId()) || CollectionUtils.isEmpty(
            request.getAdminUserIdList())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        SingleGroupUpdateRequest updateRequest = new SingleGroupUpdateRequest();
        updateRequest.setChatId(request.getChatId());
        updateRequest.setAdminUserIdList(request.getAdminUserIdList());
        updateRequest.setCorpId(request.getCorpId());
        ResultDO<OperatorResult> resultDO = groupService.asyncSingleUpdate(updateRequest);
        if (resultDO == null || !resultDO.getSuccess() || resultDO.getModel() == null) {
            return TripSCRMResult.fail(resultDO.getResultCode(),resultDO.getResultMessage());
        }
        // 取第一个requestId
        String requestId = resultDO.getModel().getDetailList().stream().map(Detail::getRequestId).findFirst().orElse(null);
        return TripSCRMResult.success(new TripSCRMEnterpriseWechatAdminsUpdateResponse(requestId));
    }

    @Override
    @ServiceLog("企微客户群-查询用户所管理的群列表")
    public TripSCRMResult<PageInfoDTO<WechatGroupInfo>> listManagedGroups(ListGroupByOwnerRequest listGroupByOwnerRequest) {
        if (!StringUtils.hasText(listGroupByOwnerRequest.getOwnerUserId()) || !StringUtils.hasText(listGroupByOwnerRequest.getCorpId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        PageInfoDTO pageInfoDTO = new PageInfoDTO();
        List<WechatGroupInfo> result = new ArrayList<>();
        PageInfoDTO<WechatGroupDO> page = wechatGroupService.listByOwnerUserPage(listGroupByOwnerRequest.getOwnerUserId(), listGroupByOwnerRequest.getCorpId(),listGroupByOwnerRequest.getPageNum(),listGroupByOwnerRequest.getPageSize());
        if (!CollectionUtils.isEmpty(page.getList())) {
            for (WechatGroupDO groupDO : page.getList()) {
                WechatGroupInfo info = new WechatGroupInfo();
                info.setOwnerUserId(groupDO.getOwnerUser());
                info.setCreateTime(groupDO.getCreateTime());
                info.setName(groupDO.getName());
                info.setChatId(groupDO.getChatId());
                info.setIsDeleted(groupDO.getIsDeleted() != null && groupDO.getIsDeleted() == 1);
                result.add(info);
            }
        }
        pageInfoDTO.setList(result);
        pageInfoDTO.setTotal(page.getTotal());
        return TripSCRMResult.success(pageInfoDTO);
    }

    @Override
    @ServiceLog("企微客户群-移除群成员")
    public TripSCRMResult<String> removeGroupMembers(TripSCRMRemoveGroupMembersRequest request) {
        if (request == null || !StringUtils.hasText(request.getChatId())
            || !StringUtils.hasText(request.getUserId())
            || !StringUtils.hasText(request.getCorpId())
            || (CollectionUtils.isEmpty(request.getExternalIdList()) && CollectionUtils.isEmpty(request.getUserIdList())) ) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        RemoveGroupMembersRequest updateRequest = new RemoveGroupMembersRequest();
        updateRequest.setChatId(request.getChatId());
        updateRequest.setExternalIdList(request.getExternalIdList());
        updateRequest.setUserIdList(request.getUserIdList());
        updateRequest.setCorpId(request.getCorpId());
        updateRequest.setUserId(request.getUserId());
        ResultDO<String> resultDO = groupService.asyncRemoveGroupMembers(updateRequest);
        if (resultDO == null || !resultDO.getSuccess() || resultDO.getModel() == null) {
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
        return TripSCRMResult.success(resultDO.getModel());
    }


    @Override
    @ServiceLog("企微客户群-发布群公告")
    public TripSCRMResult<String> publishNotice(TripSCRMGroupPublicNoticeRequest request) {
        if (request == null || !StringUtils.hasText(request.getChatId())
            || !StringUtils.hasText(request.getUserId())
            || !StringUtils.hasText(request.getCorpId())
            || !StringUtils.hasText(request.getChatId())
            || !StringUtils.hasText(request.getNotice())
        ) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatGroupPublishNoticeRequest publishNoticeRequest = new WechatGroupPublishNoticeRequest();
        publishNoticeRequest.setChatId(request.getChatId());
        publishNoticeRequest.setNotice(request.getNotice());
        publishNoticeRequest.setCorpId(request.getCorpId());
        publishNoticeRequest.setUserId(request.getUserId());
        ResultDO<String> resultDO = groupService.asyncPublishNotice(publishNoticeRequest);
        if (resultDO == null || !resultDO.getSuccess()) {
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }
        return TripSCRMResult.success(resultDO.getModel());
    }


}
