package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SellerAcquisitionLinkRequest implements Serializable {
    /**
     * 活动Id
     */
    @Min(value = 1L, message = "activityId非法")
    @NotNull(message = "activityId不可为空")
    private Long activityId;

    /**
     * 链接类型，1：h5，2：小程序太阳码链接
     */
    @Min(value = 1L, message = "linkType非法")
    @Max(value = 2L, message = "linkType非法")
    private Integer linkType = 1;
}
