package com.alibaba.tripscrm.service.service.impl.activity;

import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.activity.ActivityInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.service.convert.ActivitySopConverter;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityStatusEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.request.SopActivityQueryRequest;
import com.alibaba.tripscrm.service.model.vo.activity.AbstractActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.service.activity.ActivitySopService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.strategy.sop.factory.ActivitySopFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.github.pagehelper.PageInfo;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-01-09 17:51:08
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ActivitySopServiceImpl<T extends AbstractActivitySopConfigVO> implements ActivitySopService<T> {
    private final ActivitySopFactory<T> activitySopFactory;
    private final TaskService taskService;
    private final ActivityInfoService activityInfoService;
    private final AccountService accountService;

    @Override
    public Long create(ActivitySopVO<T> activitySopVO) {
        return activitySopFactory.create(activitySopVO);
    }

    @Override
    public void update(ActivitySopVO<T> activitySopVO) {
        User user = accountService.getUserInWebThread();
        if (user != null) {
            activitySopVO.setOperatorId(user.getUserId());
            activitySopVO.setOperatorName(user.getUserName());
        }
        activitySopFactory.update(activitySopVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Boolean online(Long id) {
        ActivityInfoDO activityInfoDO = activityInfoService.queryInfoById(id);
        if (Objects.isNull(activityInfoDO)) {
            PlatformLogUtil.logFail("activityInfo not exists");
            throw new RuntimeException("活动信息不存在");
        }

        if (!Objects.equals(ActivityStatusEnum.getByStatus(activityInfoDO.getStatus()), ActivityStatusEnum.EDITING)) {
            PlatformLogUtil.logFail("activityInfo status not editing");
            throw new RuntimeException("只有编辑中的活动可执行上线操作");
        }

        User user = accountService.getUserInWebThread();
        if (user != null) {
        }

        int upRet = activityInfoService.compareAndUpdateStatus(id, ActivityStatusEnum.EDITING, ActivityStatusEnum.ONLINE);
        if (!NumberUtils.validInteger(upRet) || upRet < 1) {
            PlatformLogUtil.logFail("compareAndUpdateStatus fail");
            throw new RuntimeException("活动上线失败");
        }

        taskService.onlineActivity(id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Boolean offline(Long id) {
        ActivityInfoDO activityInfoDO = activityInfoService.queryInfoById(id);
        if (Objects.isNull(activityInfoDO)) {
            PlatformLogUtil.logFail("activityInfo not exists");
            throw new RuntimeException("活动信息不存在");
        }

        if (!Objects.equals(ActivityStatusEnum.getByStatus(activityInfoDO.getStatus()), ActivityStatusEnum.ONLINE)) {
            PlatformLogUtil.logFail("activityInfo status not online");
            throw new RuntimeException("只有已上线的活动可执行下线操作");
        }

        int upRet = activityInfoService.compareAndUpdateStatus(id, ActivityStatusEnum.ONLINE, ActivityStatusEnum.OFFLINE_IN_PROGRESS);
        if (!NumberUtils.validInteger(upRet) || upRet < 1) {
            PlatformLogUtil.logFail("compareAndUpdateStatus fail");
            throw new RuntimeException("活动下线失败");
        }

        boolean existsOfflineInProcess = taskService.offlineActivity(id);
        if (existsOfflineInProcess) {
            return true;
        }

        activityInfoService.compareAndUpdateStatus(id, ActivityStatusEnum.OFFLINE_IN_PROGRESS, ActivityStatusEnum.EDITING);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    @AteyeInvoker(description = "删除SOP活动以及下面对应的任务，谨慎操作！！！", paraDesc = "activityId")
    public Boolean delete(Long id) {
        ActivityInfoDO activityInfoDO = activityInfoService.queryInfoById(id);
        if (Objects.isNull(activityInfoDO)) {
            PlatformLogUtil.logFail("activityInfo not exists");
            throw new RuntimeException("活动信息不存在");
        }

        if (!Objects.equals(ActivityStatusEnum.getByStatus(activityInfoDO.getStatus()), ActivityStatusEnum.EDITING)) {
            PlatformLogUtil.logFail("activityInfo status not editing");
            throw new RuntimeException("只有已下线的活动可执行删除操作");
        }

        int upRet = taskService.deleteByActivityId(id);
        if (!NumberUtils.validInteger(upRet) || upRet < 1) {
            PlatformLogUtil.logFail("task delete fail");
            throw new RuntimeException("活动删除失败");
        }

        upRet = activityInfoService.deleteById(id);
        if (!NumberUtils.validInteger(upRet) || upRet < 1) {
            PlatformLogUtil.logFail("activityInfoService delete fail");
            throw new RuntimeException("活动删除失败");
        }

        return true;
    }

    @Override
    public ActivitySopVO<T> queryById(Long activityId) {
        return convert(activityInfoService.queryInfoById(activityId));
    }

    @Override
    public PageInfo<ActivitySopVO<T>> pageList(SopActivityQueryRequest request) {
        ActivityInfoQuery query = ActivitySopConverter.queryRequest2Do(request);
        PageInfo<ActivityInfoDO> list = activityInfoService.list(query);
        return PageUtils.getPageInfo(list, this::convert);
    }

    @Override
    public ActivitySopVO<T> convert(ActivityInfoDO activityInfoDO) {
        if (Objects.isNull(activityInfoDO)) {
            return null;
        }

        if (StringUtils.hasText(activityInfoDO.getMemberIds())) {
            String[] memberIdList = activityInfoDO.getMemberIds().split(",");
            String memberNameList = Arrays.stream(memberIdList).map(accountService::getUserByAccountId).filter(Objects::nonNull).map(User::getUserName).collect(Collectors.joining(","));
            activityInfoDO.setMemberNames(memberNameList);
        }

        if (StringUtils.hasText(activityInfoDO.getLastOperatorId())) {
            User user = accountService.getUserByAccountId(activityInfoDO.getLastOperatorId());
            if (Objects.nonNull(user)) {
                activityInfoDO.setLastOperatorName(user.getUserName());
            }
        }

        ActivitySopVO<T> activitySopVO = ActivitySopConverter.do2Vo(activityInfoDO);
        activitySopFactory.fillInfo(activitySopVO);
        fillTaskInfo(activitySopVO);
        return activitySopVO;
    }

    /**
     * 填充任务信息
     * @param activitySopVO 活动SOP对象
     */
    private void fillTaskInfo(ActivitySopVO activitySopVO) {
        if (!NumberUtils.validLong(activitySopVO.getActivityId()) || SopTypeEnum.GROUP_SOP.getCode().equals(activitySopVO.getSopType())) {
            return ;
        }
        TaskQuery query = new TaskQuery();
        query.setActivityId(activitySopVO.getActivityId());
        List<TaskInfoDO> taskInfoList = taskService.query(query);
        if (CollectionUtils.isEmpty(taskInfoList)) {
            return ;
        }
        Map<String, Long> taskInfoMap = taskInfoList.stream().collect(Collectors.toMap(TaskInfoDO::getType, TaskInfoDO::getId));
        activitySopVO.setTaskMap(taskInfoMap);
    }

}
