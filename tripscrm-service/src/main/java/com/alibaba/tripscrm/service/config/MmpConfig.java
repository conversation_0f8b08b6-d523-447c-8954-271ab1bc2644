package com.alibaba.tripscrm.service.config;

import com.alibaba.tripscrm.service.constant.AppConstant;
import com.taobao.mmp.client.MmpServiceContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/3/31 20:48
 */
@Configuration
public class MmpConfig {
    @Bean
    public MmpServiceContext mmpServiceContext() {
        MmpServiceContext mmpServiceContext = new MmpServiceContext();
        mmpServiceContext.setAppName(AppConstant.APP_NAME);
        return mmpServiceContext;
    }
}
