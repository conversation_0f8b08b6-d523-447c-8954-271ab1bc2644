package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 微信群组信息查询条件
 */
@Data
public class WechatGroupNewQuery extends BasePageRequest {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 主键ID列表
     */
    private List<Long> idList;

    /**
     * 群聊chatId
     */
    private String chatId;

    /**
     * 群聊chatId列表
     */
    private List<String> chatIdList;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 企业ID列表
     */
    private List<String> corpIdList;

    /**
     * 群聊名称
     */
    private String name;

    /**
     * 群聊名称模糊查询
     */
    private String nameLike;

    /**
     * 群主UserId
     */
    private String ownerUser;

    /**
     * 群主UserId列表
     */
    private List<String> ownerUserList;

    /**
     * 是否删除 0未删除，1删除
     */
    private Byte isDeleted;

    /**
     * 群组模板ID
     */
    private String groupTemplateId;

    /**
     * 群组模板ID列表
     */
    private List<String> groupTemplateIdList;

    /**
     * 平台类型
     */
    private PlatformTypeEnum platformType;

    /**
     * 创建时间开始
     */
    private Date createTimeStart;

    /**
     * 创建时间结束
     */
    private Date createTimeEnd;
}
