package com.alibaba.tripscrm.service.manager.old;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.micro.front.domain.MicroFrontInfoVO;
import com.alibaba.micro.front.domain.ResultDTO;
import com.alibaba.micro.front.util.MicroFrontClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 微前端服务控制
 *
 * <AUTHOR>
 * @date 2023/3/23
 */
@Slf4j
@Service
public class MicroFrontManager {
    public static final String diamondId = "trip__wx-operation__3hs031lfj54bck";

    public MicroFrontInfoVO getFrameworkConfig(String userId) {
        ResultDTO<MicroFrontInfoVO> frontInfoVOResultDTO = MicroFrontClientUtil.getFrameworkConfig(diamondId, userId);
        if (!frontInfoVOResultDTO.getSuccess()) {
            PlatformLogUtil.logFail("微前端服务控制，getFrameworkConfig失败", LogListUtil.newArrayList(userId));
        }
        return frontInfoVOResultDTO.getObject();
    }


    public MicroFrontInfoVO getMicroConfig(String userId) {
        ResultDTO<MicroFrontInfoVO> frontInfoVOResultDTO = MicroFrontClientUtil.getMicroAssets(diamondId, userId);
        if (!frontInfoVOResultDTO.getSuccess()) {
            PlatformLogUtil.logFail("微前端服务控制，getMicroConfig失败", LogListUtil.newArrayList(userId));
        }
        return frontInfoVOResultDTO.getObject();
    }
}
