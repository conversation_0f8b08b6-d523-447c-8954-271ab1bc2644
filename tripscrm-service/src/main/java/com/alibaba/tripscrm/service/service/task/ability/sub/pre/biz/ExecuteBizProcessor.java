package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.constant.TaskAttributionConstant;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.task.FatigueManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.Objects;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.INTERFACE;

/**
 * 单条任务数据处理_原子能力
 *
 * <AUTHOR>
 * @since 2024/4/22 15:22
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ExecuteBizProcessor implements BizProcessor {
    private final FatigueManager fatigueManager;

    @Override
    @TaskExecuteLog("单条任务数据处理_原子能力")
    public TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception {
        Long taskId = todoTaskVO.getTaskId();
        Long instanceId = todoTaskVO.getInstanceId();
        // 记录任务实例下每个targetId的处理进度
        if (recordCursor(context, todoTaskVO)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.TASK_EXECUTE_SKIP_TARGET);
        }

        if (skip(context, todoTaskVO)) {
            return TripSCRMResult.success(null);
        }

        try {
            doBizWithFatigue(context, todoTaskVO);
        } catch (TripscrmException e) {
            todoTaskVO.setSuccess(false);
            todoTaskVO.setAttribution(e.getErrorCodeEnum().getCode());
            todoTaskVO.setFailReason(e.getErrorCodeEnum().getDescCn());
            PlatformLogUtil.logException("单条任务数据处理-原子能力，处理失败", e.getErrorCodeEnum().getCode(), e, Lists.newArrayList(taskId, instanceId, context));
            throw e;
        } catch (Throwable e) {
            todoTaskVO.setSuccess(false);
            todoTaskVO.setAttribution(TaskAttributionConstant.EXCEPTION);
            todoTaskVO.setFailReason(e.getMessage());
            PlatformLogUtil.logException("单条任务数据处理-原子能力，系统异常", e.getMessage(), e, Lists.newArrayList(taskId, instanceId, context));
            throw e;
        }

        return TripSCRMResult.success(null);
    }

    private boolean recordCursor(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        if (context.getRetry()) {
            return false;
        }

        // 接口或者单次触发，不会进行跳过逻辑
        TaskTriggerTypeEnum triggerType = context.getTriggerType();
        if (Lists.newArrayList(INTERFACE, EVENT).contains(triggerType)) {
            return false;
        }

        AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(TaskType.getByCode(context.getTaskInfoDOSnapshot().getType()));
        return taskExecutor.recordCursor(context, todoTaskVO);
    }

    private void doBizWithFatigue(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        Date date = new Date();
        TaskInfoDO taskInfoDOSnapshot = context.getTaskInfoDOSnapshot();
        String taskType = taskInfoDOSnapshot.getType();
        AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(TaskType.getByCode(taskType));
        Integer messageCount = AbstractTaskExecutor.getMessageCount(context, todoTaskVO);
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);

        try {
            // 疲劳度控制
            if (!doFatigue(context, taskDataBody, taskExecutor, date, messageCount)) {
                PlatformLogUtil.logFail("单条任务数据处理-原子能力，超出疲劳度限制", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), taskDataBody, context));
                throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_OVER_FATIGUE_LIMIT);
            }

            taskExecutor.doBiz(context, todoTaskVO);
        } catch (Throwable e) {
            // 疲劳度回退
            undoFatigue(context, taskDataBody, taskExecutor, date, messageCount);
            throw e;
        }
    }

    /**
     * 是否跳过执行
     */
    private boolean skip(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // AB&空桶才跳过
        Long materialId = AbstractTaskExecutor.getMaterialId(context, todoTaskVO);

        if (!Objects.equals(materialId, -1L)) {
            return false;
        }

        // 小程序订阅消息类型任务，素材信息存在ab桶的素材id列表中，素材id列表为空则跳过
        TaskInfoDO taskInfoDOSnapshot = context.getTaskInfoDOSnapshot();
        TaskType taskType = TaskType.getByCode(taskInfoDOSnapshot.getType());
        AbTestBucketVO abTestBucketVO = todoTaskVO.getAbTestBucketVO();
        if (Objects.equals(taskType, TaskType.MINI_PROGRAM_SUBSCRIBE_MSG) && Objects.nonNull(abTestBucketVO) && !CollectionUtils.isEmpty(abTestBucketVO.getMaterialIdList())) {
            return false;
        }

        Long abTestBucketId = AbstractTaskExecutor.getAbTestBucketId(todoTaskVO);
        if (Objects.equals(abTestBucketId, -1L)) {
            return false;
        }

        // 空素材无需执行原子能力，也无需计入疲劳度
        JSONObject data = new JSONObject();
        data.put("extraInfo", context.getExtInfo());
        data.put("abTestBucketName", todoTaskVO.getAbTestBucketVO().getBucketName());
        data.put("result", "命中空素材桶，自动跳过发送流程");
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        todoTaskVO.setAttribution(TaskAttributionConstant.EMPTY_MATERIAL);
        return true;
    }

    /**
     * 疲劳度控制
     */
    private boolean doFatigue(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody,
        AbstractTaskExecutor taskExecutor, Date date, Integer messageCount) {
        if (context.getTestFlag()) {
            return true;
        }

        return fatigueManager.acquire(taskExecutor.getFinalTargetType(context),
            taskExecutor.getFinalTargetId(context, taskDataBody), context.getTaskInfoDOSnapshot(), messageCount, date);
    }

    /**
     * 疲劳度控制（发送数据回退）
     */
    private void undoFatigue(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody,
        AbstractTaskExecutor taskExecutor, Date date, Integer messageCount) {
        if (context.getTestFlag()) {
            return;
        }

        fatigueManager.release(taskExecutor.getFinalTargetType(context),
            taskExecutor.getFinalTargetId(context, taskDataBody), context.getTaskInfoDOSnapshot(), messageCount, date);
    }
}