package com.alibaba.tripscrm.service.service.task.ability.executor;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.subscribemsg.SubscribeMsgTaskStrategyEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskAsyncRequestDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.taobao.unifiedsession.core.commons.limiter.RateLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Supplier;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public abstract class AbstractTaskExecutor {
    @Resource
    private ActivityContextService activityContextService;
    @Resource
    private LdbTairManager ldbTairManager;

    /**
     * 子类实现的业务方法
     */
    public abstract void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO);


    public RateLimiter getRateLimiter(TaskExecuteContext context) {
        return null;
    }

    /**
     * 发送消息的userId
     */
    protected abstract String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO);

    /**
     * 最终执行时使用的 targetType
     */
    public abstract ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context);

    /**
     * 最终执行时使用的 targetId
     */
    public abstract String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody);

    /**
     * 获取素材-trackId关系对象
     *
     * @param todoTaskVO  任务信息
     * @param sceneType   场景类型
     * @param sendUserId  发送用户id
     * @param chatId      群聊id
     * @param sendUnionId 接收人的unionId
     * @return 素材-trackId关系对象
     */
    protected MaterialTrackRelationDTO buildMaterialTrackRelationDTO(TaskExecuteContext context, TodoTaskVO todoTaskVO, String sceneType, String sendUserId, String chatId, String sendUnionId) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setSceneType(sceneType);
        materialTrackRelationDTO.setWxUserId(sendUserId);
        materialTrackRelationDTO.setSendChatId(chatId);
        materialTrackRelationDTO.setSendUnionId(sendUnionId);
        return materialTrackRelationDTO;
    }

    /**
     * 获取素材中包含的消息数量
     */
    public static Integer getMessageCount(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskType taskType = TaskType.getByCode(context.getTaskInfoDOSnapshot().getType());
        switch (taskType) {
            case MINI_PROGRAM_SUBSCRIBE_MSG:
                return getSubscribeMsgTaskMsgCount(context, todoTaskVO);
            default:
                return MaterialService.getMessageCount(getMaterialInfo(context, todoTaskVO));
        }
    }

    /**
     * 获取订阅消息类型任务的消息数量
     *
     * @param context    任务上下文
     * @param todoTaskVO 待执行的任务
     * @return 消息数量
     */
    private static int getSubscribeMsgTaskMsgCount(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        try {
            JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
            Integer executeStrategyCode = extInfo.getInteger(TaskConstant.SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
            SubscribeMsgTaskStrategyEnum subscribeMsgTaskStrategyEnum = SubscribeMsgTaskStrategyEnum.of(executeStrategyCode);
            switch (subscribeMsgTaskStrategyEnum) {
                case SEQUENTIAL:
                    return 1;
                case CONCURRENT:
                    return todoTaskVO.getAbTestBucketVO().getMaterialIdList().size();
                default:
                    PlatformLogUtil.logFail("获取小程序订阅消息任务消息数失败，不支持的执行策略类型",  LogListUtil.newArrayList(context, todoTaskVO));
                    throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("获取小程序订阅消息任务消息数失败，获取任务执行策略出错", e.getMessage(), e, LogListUtil.newArrayList(context, todoTaskVO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
        }
    }

    public static Long getAbTestBucketId(TodoTaskVO todoTaskVO) {
        if (Objects.nonNull(todoTaskVO.getAbTestBucketVO())) {
            return todoTaskVO.getAbTestBucketVO().getId();
        }

        return -1L;
    }

    public static Long getMaterialId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        MaterailInfoDO materialInfo = getMaterialInfo(context, todoTaskVO);
        return Objects.isNull(materialInfo) ? -1L : materialInfo.getId();
    }

    public static MaterailInfoDO getMaterialInfo(TaskExecuteContext context, TodoTaskVO todoTaskVO) {

        if (Objects.nonNull(todoTaskVO.getAbTestBucketVO())) {
            return todoTaskVO.getAbTestBucketVO().getMaterialInfo();
        }

        return context.getMaterialSnapshot();
    }

    public TripSCRMResult<Void> checkValid(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return TripSCRMResult.success(null);
    }

    public boolean checkActivityContextValid(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (Objects.equals(SwitchConfig.publicActivityId, context.getTaskInfoDOSnapshot().getActivityId())) {
            return true;
        }
        return Objects.nonNull(getActivityContext(context, taskDataBody));
    }

    protected ActivityTaskInfoBO getTargetActivityContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        Long activityId = context.getTaskInfoDOSnapshot().getActivityId();
        // 根据 targetId + targetType + activityId 查询上下文
        String targetId = getFinalTargetId(context, taskDataBody);
        ActivityTargetTypeEnum targetType = getFinalTargetType(context);
        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(activityId);
        query.setTargetTypeEnum(targetType);
        query.setTargetId(targetId);
        List<ActivityTaskInfoBO> targetActivityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
        if (CollectionUtils.isEmpty(targetActivityTaskInfoBOS)) {
            PlatformLogUtil.logFail("任务基础调度执行器，根据 targetId + targetType + activityId 查询上下文为空", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        ActivityTaskInfoBO activityTaskInfoBO = ActivityContextService.getNewestBo(targetActivityTaskInfoBOS);
        if (Objects.isNull(activityTaskInfoBO) || org.springframework.util.CollectionUtils.isEmpty(activityTaskInfoBO.getExtraJson()) || !activityTaskInfoBO.getExtraJson().containsKey("dataTime")) {
            return activityTaskInfoBO;
        }

        // 活动上下文过期时间判断
        Long dataTime = MapUtils.getLong(activityTaskInfoBO.getExtraJson(), "dataTime", 0L);
        if (Math.abs(System.currentTimeMillis() - dataTime) > SwitchConfig.targetActivityContextExpireSeconds * 1000L) {
            PlatformLogUtil.logFail("任务基础调度执行器，上下文已过期", LogListUtil.newArrayList(taskDataBody, activityTaskInfoBO));
            return null;
        }

        return activityTaskInfoBO;
    }

    /**
     * 获取活动上下文
     * 先尝试根据传入的contextId进行查询
     * 如果查询不到，再根据 targetId + targetType + activityId 查询
     */
    protected ActivityTaskInfoBO getActivityContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        Map<String, Object> extInfo = context.getExtInfo();

        Long contextId = -1L;
        if (extInfo.containsKey(TaskConstant.CONTEXT_ID) && StringUtils.isNotBlank(String.valueOf(extInfo.getOrDefault(TaskConstant.CONTEXT_ID, "")))) {
            contextId = NumberUtils.toLong(String.valueOf(extInfo.get(TaskConstant.CONTEXT_ID)), -1L);
            // contextId非法
            if (contextId < 0L) {
                PlatformLogUtil.logFail("任务基础调度执行器，contextId非法", LogListUtil.newArrayList(taskDataBody));
                return null;
            }
        }

        Long activityId = context.getTaskInfoDOSnapshot().getActivityId();
        // 根据 targetId + targetType + activityId 查询上下文
        if (contextId < 0) {
            ActivityTaskInfoBO targetActivityTaskInfoBO = getTargetActivityContext(context, taskDataBody);
            if (Objects.isNull(targetActivityTaskInfoBO)) {
                PlatformLogUtil.logFail("任务基础调度执行器，根据目标信息查询活动上下文为空", LogListUtil.newArrayList(taskDataBody));
                return null;
            }

            JSONObject extraJson = Optional.ofNullable(targetActivityTaskInfoBO.getExtraJson()).orElse(new JSONObject());
            if (!extraJson.containsKey(TaskConstant.CONTEXT_ID)) {
                PlatformLogUtil.logFail("任务基础调度执行器，上下文id跟活动不匹配", LogListUtil.newArrayList(taskDataBody));
                return null;
            }

            contextId = NumberUtils.toLong(extraJson.getString(TaskConstant.CONTEXT_ID), -1L);
        }

        ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        // 上下文不存在
        if (Objects.isNull(activityTaskInfoBO)) {
            PlatformLogUtil.logFail("任务基础调度执行器，上下文id在lindorm不存在", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        // 非当前活动
        if (!Objects.equals(activityTaskInfoBO.getActivityId(), activityId)) {
            PlatformLogUtil.logFail("任务基础调度执行器，非当前活动", LogListUtil.newArrayList(taskDataBody));
            return null;
        }
        return activityTaskInfoBO;
    }

    public static void main(String[] args) {
        Long dataTime = 1725610357931L;
        System.out.println(System.currentTimeMillis());
//        System.out.println(Math.abs(System.currentTimeMillis() - dataTime) > 86400 * 1000 * 7);
    }

    /**
     * 发送异步请求，并记录当前任务执行数据
     */
    protected TripSCRMResult<String> sendAsyncRequest(TaskExecuteContext context, TodoTaskVO todoTaskVO, Supplier<TripSCRMResult<String>> asyncRequestSupplier) {
        // 只处理第一条
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);

        // 记录请求proxy数据
        TaskAsyncRequestDataVO tairData = new TaskAsyncRequestDataVO();
        tairData.setTaskId(context.getTaskId());
        tairData.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
        tairData.setIsSubTask(context.getIsSub());
        tairData.setMainTaskInstanceId(context.getMainTaskInstanceId());
        tairData.setSubTaskInstanceId(context.getInstanceId());
        tairData.setTargetId(taskDataBody.getTargetId());
        tairData.setTargetType(taskDataBody.getTargetType());
        tairData.setTaskType(getTaskType().getCode());
        tairData.setRecordId(taskDataBody.getRecordId());
        tairData.setExtraInfo(getTaskAsyncExecuteExtraInfo(context, todoTaskVO));

        TripSCRMResult<String> result = asyncRequestSupplier.get();
        if (Objects.nonNull(result) && result.isSuccess()) {
            ldbTairManager.put(TairConstant.LDB_PROXY_REQUEST_DATA_PREFIX + result.getData(), JSONObject.toJSONString(tairData), 30 * 60);
        }
        return result;
    }

    protected Map<String, String> getTaskAsyncExecuteExtraInfo(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return new HashMap<>();
    }

    /**
     * 任务类型
     */
    protected abstract TaskType getTaskType();

    @PostConstruct
    public void init0() {
        TaskExecutorFactory.registry(getTaskType(), this);
    }

    protected String getUniqueKey(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return TairConstant.LDB_TASK_FINISH_TARGET_DATA_PREFIX + context.getInstanceId() + "_" + todoTaskVO.getData().get(0).getTargetId();
    }

    /**
     * 检查是否跳过任务
     */
    public boolean checkSkip(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 判断targetId是否已经执行过
        return Long.parseLong(String.valueOf(Optional.ofNullable(ldbTairManager.get(getUniqueKey(context, todoTaskVO))).orElse(0))) > 0;
    }

    /**
     * 记录任务实例下每个targetId的处理进度
     */
    public boolean recordCursor(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 记录任务实例下每个targetId的处理进度
        return ldbTairManager.incr(getUniqueKey(context, todoTaskVO), 1, 0, 12 * 60 * 60) > 1;
    }

    /**
     * 是否支持重试
     */
    public boolean supportRetry(TripSCRMErrorCode errorCodeEnum) {
        return false;
    }

    /**
     * 处理异步执行结果
     */
    public void updateAsyncExecuteResult(Long recordId, ScrmCallbackMsg scrmCallbackMsg) {
    }
}
