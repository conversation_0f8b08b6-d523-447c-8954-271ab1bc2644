package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.wechat.BaiYeWeComUpOrderStatusEnum;
import com.alibaba.tripscrm.service.enums.wechat.CreateWeComRecordStatusEnum;
import com.alibaba.tripscrm.service.enums.wechat.WechatUserApplyStatusEnum;
import com.alibaba.tripscrm.service.model.domain.query.CreateWeComRecordQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.CreateWeComRecordDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatUserApplyRecordDTO;
import com.alibaba.tripscrm.service.service.CreateWeComRecordService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserApplyRecordService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripzoo.proxy.api.service.RobotService;
import com.alibaba.tripzoo.proxy.request.BaiyeQueryWeComUpDetailRequest;
import com.alibaba.tripzoo.proxy.result.BaiyeQueryWeComUpDetailBO;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 百业上号记录同步
 * <AUTHOR>
 * @since 2025/01/23 04:47
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BaiYeApplyWeComRecordSyncProcessor extends JavaProcessor {
    private final CreateWeComRecordService createWeComRecordService;
    private final RobotService robotService;
    private final WechatUserService wechatUserService;
    private final WechatUserApplyRecordService wechatUserApplyRecordService;

    private static final String ORDER_ID = "orderId";
    private static final String SUCCESS_COUNT = "successCount";
    private static final String APPLY_SUCCESS_TIME = "applySuccessTime";
    private static final String ROBOT_NAME = "robotName";
    private static final String APPLY_NAME = "applyName";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        CreateWeComRecordQuery query = new CreateWeComRecordQuery();
        query.setCreateStartTime(new Date(System.currentTimeMillis() -  24L * 3 * 60 * 60 * 1000));
        query.setCreateEndTime(new Date());
        List<CreateWeComRecordDTO> createWeComRecordDTOS = createWeComRecordService.selectByCondition(query);
        if (CollectionUtils.isEmpty(createWeComRecordDTOS)) {
            PlatformLogUtil.logFail("DB中未查询到上号申请记录", LogListUtil.newArrayList(query));
            return null;
        }
        for (CreateWeComRecordDTO createWeComRecordDTO : createWeComRecordDTOS) {
            JSONObject extInfo = JSONObject.parseObject(createWeComRecordDTO.getExtInfo());
            String orderId = extInfo.getString(ORDER_ID);
            BaiyeQueryWeComUpDetailRequest request = new BaiyeQueryWeComUpDetailRequest();
            request.setOrderId(orderId);
            request.setAccountDetail(true);
            ResultDO<BaiyeQueryWeComUpDetailBO> resultDO =  robotService.baiyeQueryWeComUpDetail(request);
            if (!resultDO.getSuccess()) {
                PlatformLogUtil.logFail("未查询到上号记录", LogListUtil.newArrayList(createWeComRecordDTO));
                continue;
            }
            BaiyeQueryWeComUpDetailBO queryWeComUpDetailBO = resultDO.getModel();
            CreateWeComRecordDTO newRecord = new CreateWeComRecordDTO();
            newRecord.setId(createWeComRecordDTO.getId());
            JSONObject newRecordExtInfo = new JSONObject();
            newRecordExtInfo.put(SUCCESS_COUNT, queryWeComUpDetailBO.getData().getUpSuccessCount());
            if (Objects.equals(BaiYeWeComUpOrderStatusEnum.COMPLETED, BaiYeWeComUpOrderStatusEnum.valueOf((queryWeComUpDetailBO.getData().getStatus())))) {
                newRecord.setStatus(CreateWeComRecordStatusEnum.APPLY_SUCCESS.getCode());
            }
            if (queryWeComUpDetailBO.getData().getUpSuccessCount() == 0) {
                newRecord.setStatus(CreateWeComRecordStatusEnum.APPLY_FAILED.getCode());
            }
            newRecord.setExtInfo(newRecordExtInfo.toJSONString());
            Integer effectLines = createWeComRecordService.updateByIdSelective(newRecord);
            if (Objects.isNull(effectLines) || effectLines <= 0) {
                PlatformLogUtil.logFail("更新上号记录申请状态失败", LogListUtil.newArrayList(newRecord));
                continue;
            }
            List<WechatUserDTO> wechatUserDTOS = wechatUserService.listById(Lists.newArrayList(createWeComRecordDTO.getCreatorId()));
            String userName = "";
            if (!CollectionUtils.isEmpty(wechatUserDTOS)) {
                userName = wechatUserDTOS.get(0).getName();
            }
            for (BaiyeQueryWeComUpDetailBO.UpSuccessInfo info : queryWeComUpDetailBO.getData().getUpSuccessInfos()) {
                WechatUserApplyRecordDTO wechatUserApplyRecordDTO = new WechatUserApplyRecordDTO();
                wechatUserApplyRecordDTO.setCreateWeComRecordId(createWeComRecordDTO.getId());
                wechatUserApplyRecordDTO.setIsvType(createWeComRecordDTO.getIsvType());
                wechatUserApplyRecordDTO.setIsvId(info.getWxId());
                wechatUserApplyRecordDTO.setStatus(WechatUserApplyStatusEnum.SUCCESS.getCode());
                JSONObject wechatUserApplyRecordExtInfo = new JSONObject();
                wechatUserApplyRecordExtInfo.put(APPLY_SUCCESS_TIME, new Date());
                wechatUserApplyRecordExtInfo.put(ROBOT_NAME, info.getWxNick());
                wechatUserApplyRecordExtInfo.put(APPLY_NAME, userName);
                wechatUserApplyRecordDTO.setExtInfo(wechatUserApplyRecordExtInfo.toJSONString());
                Integer wechatUserApplyRecordEffectLines = wechatUserApplyRecordService.upsertSelective(wechatUserApplyRecordDTO);
                if (Objects.isNull(wechatUserApplyRecordEffectLines) || wechatUserApplyRecordEffectLines <= 0) {
                    PlatformLogUtil.logFail("上号记录写入失败", LogListUtil.newArrayList(wechatUserApplyRecordDTO));
                }
            }

        }
        return new ProcessResult(true);
    }
}
