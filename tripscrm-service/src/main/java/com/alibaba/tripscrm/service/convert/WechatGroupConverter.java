package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupDTO;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @description：微信群组转换器
 * @Author：wangrui
 * @create：2025/8/15 16:07
 * @Filename：WechatGroupConverter
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatGroupConverter {

    /**
     * DO转DTO
     * @param wechatGroupDO
     * @return
     */
    public WechatGroupDTO convert2DTO(WechatGroupDO wechatGroupDO) {
        if (wechatGroupDO == null) {
            return null;
        }

        WechatGroupDTO wechatGroupDTO = new WechatGroupDTO();
        wechatGroupDTO.setId(wechatGroupDO.getId());
        wechatGroupDTO.setChatId(wechatGroupDO.getChatId());
        wechatGroupDTO.setCorpId(wechatGroupDO.getCorpId());
        wechatGroupDTO.setName(wechatGroupDO.getName());
        wechatGroupDTO.setCreateTime(wechatGroupDO.getCreateTime());
        wechatGroupDTO.setOwnerUser(wechatGroupDO.getOwnerUser());
        wechatGroupDTO.setAdminUser(wechatGroupDO.getAdminUser());
        wechatGroupDTO.setIsDeleted(wechatGroupDO.getIsDeleted());
        wechatGroupDTO.setGmtCreate(wechatGroupDO.getGmtCreate());
        wechatGroupDTO.setGmtModified(wechatGroupDO.getGmtModified());
        wechatGroupDTO.setTags(wechatGroupDO.getTags());
        wechatGroupDTO.setQrcode(wechatGroupDO.getQrcode());
        wechatGroupDTO.setNotice(wechatGroupDO.getNotice());
        wechatGroupDTO.setGroupTemplateId(wechatGroupDO.getGroupTemplateId());
        wechatGroupDTO.setPlatformType(PlatformTypeEnum.valueOf(wechatGroupDO.getPlatformType()));
        wechatGroupDTO.setExtraInfo(wechatGroupDO.getExtraInfo());

        return wechatGroupDTO;
    }

    /**
     * DTO转DO
     * @param wechatGroupDTO
     * @return
     */
    public WechatGroupDO convert2DO(WechatGroupDTO wechatGroupDTO) {
        if (wechatGroupDTO == null) {
            return null;
        }

        WechatGroupDO wechatGroupDO = new WechatGroupDO();
        wechatGroupDO.setId(wechatGroupDTO.getId());
        wechatGroupDO.setChatId(wechatGroupDTO.getChatId());
        wechatGroupDO.setCorpId(wechatGroupDTO.getCorpId());
        wechatGroupDO.setName(wechatGroupDTO.getName());
        wechatGroupDO.setCreateTime(wechatGroupDTO.getCreateTime());
        wechatGroupDO.setOwnerUser(wechatGroupDTO.getOwnerUser());
        wechatGroupDO.setAdminUser(wechatGroupDTO.getAdminUser());
        wechatGroupDO.setIsDeleted(wechatGroupDTO.getIsDeleted());
        wechatGroupDO.setGmtCreate(wechatGroupDTO.getGmtCreate());
        wechatGroupDO.setGmtModified(wechatGroupDTO.getGmtModified());
        wechatGroupDO.setTags(wechatGroupDTO.getTags());
        wechatGroupDO.setQrcode(wechatGroupDTO.getQrcode());
        wechatGroupDO.setNotice(wechatGroupDTO.getNotice());
        wechatGroupDO.setGroupTemplateId(wechatGroupDTO.getGroupTemplateId());
        wechatGroupDO.setPlatformType(wechatGroupDTO.getPlatformType().getCode().byteValue());
        wechatGroupDO.setExtraInfo(wechatGroupDTO.getExtraInfo());

        return wechatGroupDO;
    }

    /**
     * DO列表转DTO列表
     * @param wechatGroupDOS
     * @return
     */
    public  List<WechatGroupDTO> convert2DTOs(List<WechatGroupDO> wechatGroupDOS) {
        if (wechatGroupDOS == null) {
            return null;
        }

        List<WechatGroupDTO> wechatGroupDTOS = new ArrayList<>();
        for (WechatGroupDO wechatGroupDO : wechatGroupDOS) {
            wechatGroupDTOS.add(convert2DTO(wechatGroupDO));
        }
        return wechatGroupDTOS;
    }

    /**
     * DTO列表转DO列表
     * @param wechatGroupDTOS
     * @return
     */
    public  List<WechatGroupDO> convert2DOs(List<WechatGroupDTO> wechatGroupDTOS) {
        if (wechatGroupDTOS == null) {
            return null;
        }

        List<WechatGroupDO> wechatGroupDOS = new ArrayList<>();
        for (WechatGroupDTO wechatGroupDTO : wechatGroupDTOS) {
            wechatGroupDOS.add(convert2DO(wechatGroupDTO));
        }
        return wechatGroupDOS;
    }
}
