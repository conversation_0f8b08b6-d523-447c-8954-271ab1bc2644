package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2024/6/6 19:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RuleGroupQuery extends BasePageRequest {
    /**
     * 规则组名称
     */
    private String name;

    /**
     * 业务空间id
     */
    private Long spaceId;

    /**
     * 规则组类型
     */
    private Byte type;

    /**
     * 创建者
     */
    private String creatorId;

    /**
     * 管理员
     */
    private String memberIds;

    /**
     * 创建时间（开始时间）
     */
    private Date createStartTime;

    /**
     * 创建时间（结束时间）
     */
    private Date createEndTime;

    /**
     * 环境
     */
    private String env;
}
