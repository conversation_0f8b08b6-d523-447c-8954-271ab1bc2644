package com.alibaba.tripscrm.service.service.strategy.rule;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.RuleGroupDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.RuleExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.RuleGroupQuery;
import com.alibaba.tripscrm.service.service.rule.RuleGroupService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2024/6/6 20:15
 */
@Slf4j
@Component
public abstract class AbstractRuleGroupStrategy implements RuleGroupStrategy {
    public static final Map<RuleTypeEnum, AbstractRuleGroupStrategy> RULE_GROUP_STRATEGY_MAP = new ConcurrentHashMap<>();

    @Resource
    private RuleGroupService ruleGroupService;

    /**
     * 注册
     */
    public static void registry(RuleTypeEnum ruleTypeEnum, AbstractRuleGroupStrategy ruleStrategy) {
        RULE_GROUP_STRATEGY_MAP.put(ruleTypeEnum, ruleStrategy);
    }

    public static AbstractRuleGroupStrategy getStrategyByType(Byte ruleType) {
        RuleTypeEnum ruleTypeEnum = RuleTypeEnum.getByCode(ruleType.intValue());
        return getStrategyByType(ruleTypeEnum);
    }

    public static AbstractRuleGroupStrategy getStrategyByType(RuleTypeEnum ruleTypeEnum) {
        if (Objects.isNull(ruleTypeEnum)) {
            return null;
        }

        return RULE_GROUP_STRATEGY_MAP.get(ruleTypeEnum);
    }

    /**
     * 执行规则
     */
    @Override
    public TripSCRMResult<Void> run(Long id, RuleExecuteContext context) {
        RuleGroupDO ruleGroupDO = ruleGroupService.selectById(id);
        if (Objects.isNull(ruleGroupDO)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.QUERY_RULE_GROUP_INFO_FAIL);
        }

        AbstractRuleGroupStrategy strategy = getStrategyByType(ruleGroupDO.getType());
        if (Objects.isNull(strategy)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NOT_SUPPORT_RULE_TYPE);
        }

        return strategy.run(ruleGroupDO, context);
    }

    /**
     * 执行规则
     */
    @Override
    public TripSCRMResult<Void> run(RuleExecuteContext context) {
        RuleGroupQuery query = new RuleGroupQuery();
        query.setType(getRuleType().getCode().byteValue());
        query.setEnv(EnvUtils.getEnvironment());
        List<RuleGroupDO> ruleGroupList = ruleGroupService.selectByCondition(query);
        if (CollectionUtils.isEmpty(ruleGroupList)) {
            PlatformLogUtil.logFail("规则组查询结果为空", LogListUtil.newArrayList(query, getRuleType()));
            return TripSCRMResult.success(null);
        }

        for (RuleGroupDO ruleGroupDO : ruleGroupList) {
            TripSCRMResult<Void> result = run(ruleGroupDO, context);
            if (!result.isSuccess()) {
                PlatformLogUtil.logFail("规则组执行失败", LogListUtil.newArrayList(ruleGroupDO, result));
                return TripSCRMResult.fail(result.getCode(), result.getMsg());
            }
        }

        return TripSCRMResult.success(null);
    }

    /**
     * 规则真正执行的逻辑
     */
    protected abstract TripSCRMResult<Void> run(RuleGroupDO ruleGroupDO, RuleExecuteContext context);

    @PostConstruct
    public void init() {
        registry(getRuleType(), this);
    }
}
