package com.alibaba.tripscrm.service.service.strategy.variable.inner;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.material.InnerServiceEnum;
import com.alibaba.tripscrm.service.manager.second.MemberManager;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.fliggy.ffa.touch.prod.customize.client.common.param.MemberDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 会员里程到账-获取里程余额
 *
 * <AUTHOR>
 * @since 2025/3/24 11:08
 */
@Component
public class TripTradeWaitingCollectPointGetBalanceVariableDataInnerService extends AbstractVariableDataInnerService {

    @Resource
    private MemberManager memberManager;

    @Override
    public String doGetValue(Map<String, Object> paramMap) {
        if (!paramMap.containsKey(TaskConstant.TARGET_ID)) {
            PlatformLogUtil.logFail("会员里程到账-获取里程余额服务，获取变量失败，缺少目标id", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        Long userId = (Long) paramMap.get(TaskConstant.TARGET_ID);
        MemberDTO memberDTO = memberManager.queryMemberPoint(userId);
        if (Objects.isNull(memberDTO)) {
            PlatformLogUtil.logFail("会员里程到账-获取里程余额服务，获取变量失败，会员信息为空", LogListUtil.newArrayList(paramMap));
            throw new TripscrmException(TripSCRMErrorCode.GET_MEMBER_POINT_FAIL);
        }
        return memberDTO.getPoint().toString();
    }

    @Override
    public InnerServiceEnum getInnerServiceEnum() {
        return InnerServiceEnum.TRIP_TRADE_WAITING_COLLECT_POINT_GET_BALANCE_POINT;
    }
}
