package com.alibaba.tripscrm.service.service.strategy.message;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialJsonKeyConstant;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.message.ImageMessageInfoDTO;
import com.alibaba.tripscrm.service.model.dto.message.MessageTransferDTO;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.WxMediaUtils;
import com.alibaba.tripzoo.proxy.enums.AttachmentSceneTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-04 23:32:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ImageMessageConverter extends AbstractMessageConverter<ImageMessageInfoDTO> {

    private final WxMediaUtils wxMediaUtils;

    @Override
    public ImageMessageInfoDTO convert(MessageTransferDTO messageTransferDTO) {
        ImageMessageInfoDTO imageMessageInfoDTO = new ImageMessageInfoDTO();
        JSONObject messageContent = messageTransferDTO.getContent();
        if (!messageContent.containsKey(MaterialJsonKeyConstant.pictureContent)) {
            return imageMessageInfoDTO;
        }
        List<String> urlList = messageContent.getObject(MaterialJsonKeyConstant.pictureContent, new TypeReference<List<String>>() {
        });
        imageMessageInfoDTO.setUrlList(urlList);
        return imageMessageInfoDTO;
    }

    @Override
    public List<MessageBO> buildMessageBO(ImageMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, Integer messageIndex) {
        ArrayList<MessageBO> messageBOList = new ArrayList<>();
        List<String> imageUrlList = messageInfoDTO.getUrlList();
        if (CollectionUtils.isEmpty(imageUrlList)) {
            return messageBOList;
        }
        for (String imageUrl : imageUrlList) {
            MessageBO messageBO = new MessageBO();
            messageBO.setMsgNum(messageIndex++);
            messageBO.setMsgType(messageInfoDTO.getMessageType());
            messageBO.setMsgContent(imageUrl);
            messageBOList.add(messageBO);
        }
        return messageBOList;
    }

    @Override
    List<WxMessageBO> buildWxMessageBO(ImageMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, TaskType taskType) {
        ArrayList<WxMessageBO> wxMessageList = new ArrayList<>();
        List<String> imageUrlList = messageInfoDTO.getUrlList();
        if (CollectionUtils.isEmpty(imageUrlList)) {
            return wxMessageList;
        }

        for (String url : imageUrlList) {
            WxMessageBO wxMessageBO2 = new WxMessageBO();
            wxMessageBO2.setMsgType(WxAttachmentTypeEnum.IMAGE);
            wxMessageBO2.setMediaId(wxMediaUtils.getMediaId(WxAttachmentTypeEnum.IMAGE, TaskType.SEND_TO_MOMENTS.equals(taskType) ? AttachmentSceneTypeEnum.MOMENT : null, url, SpaceInfoThreadLocalUtils.getCorpId(), false));
            wxMessageList.add(wxMessageBO2);
        }
        return wxMessageList;
    }

    @Override
    public MessageTypeEnum getMsgType() {
        return MessageTypeEnum.IMAGE;
    }
}
