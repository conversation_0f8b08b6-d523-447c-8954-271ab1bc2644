package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.fastjson.JSONPath;
import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageUpdateParam;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.RecallMessageRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.AsyncRecallMessageProcessor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.alibaba.tripscrm.service.constant.TairConstant.MESSAGE_ID_MAPPING_PREFIX;

/**
 * 消息撤回【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class RecallMessageCallbackProcessor implements ProxyCallbackProcessor {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private AsyncRecallMessageProcessor asyncRecallMessageProcessor;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private ChatConversationRepository chatConversationRepository;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.RECALL_MESSAGE_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (!scrmCallbackMsg.getResult()) {
            return true;
        }

        String msgId = (String) JSONPath.read(scrmCallbackMsg.getContent(), "$.msgId");
        String messageId = (String) ldbTairManager.get(MESSAGE_ID_MAPPING_PREFIX + msgId);
        // 执行主动发送消息的后置动作
        WsEvent wsEvent = asyncRecallMessageProcessor.afterCreate(scrmCallbackMsg.getRequestId());
        if (wsEvent == null) {
            PlatformLogUtil.logFail("消息撤回回调处理，wsEvent为空", LogListUtil.newArrayList(scrmCallbackMsg));
            return true;
        }
        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("消息撤回回调处理，回调结果为失败", LogListUtil.newArrayList(scrmCallbackMsg));
            wsEvent.setSuccess(false);
            wsEvent.setErrorMessage(scrmCallbackMsg.getMessage() + "_" + scrmCallbackMsg.getContent());
            webSocketFactory.pushMessageByDistributed(wsEvent);
            return true;
        } else {
            PlatformLogUtil.logFail("消息撤回回调处理，回调结果为成功", LogListUtil.newArrayList(scrmCallbackMsg));
        }
        RecallMessageRequest recallMessageRequest = wsEvent.getData().toJavaObject(RecallMessageRequest.class);
        String receiveId = chatMessageService.chatId2ReceiveId(wsEvent.getUserId(), recallMessageRequest.getChatId(), recallMessageRequest.getChatType());
        // 更新消息状态
        ChatMessageUpdateParam param = new ChatMessageUpdateParam();
        param.setReceiveId(receiveId);
        param.setMessageId(messageId);
        param.setTimestamp(recallMessageRequest.getTimestamp());
        param.setStatus(1);
        chatMessageService.updateChatMessage(param);
        // 推送ws
        asyncRecallMessageProcessor.pushMessageByDistributed(wsEvent, recallMessageRequest.getChatId(), recallMessageRequest.getChatType(), messageId);
        // 若撤回的是最新一条消息，则更新lastMessage
        ChatConversationDO chatConversation = chatConversationRepository.getByUniqueKey(wsEvent.getUserId(), recallMessageRequest.getChatId(), recallMessageRequest.getChatType());
        if ((recallMessageRequest.getTimestamp() / 1000) == (chatConversation.getLastMessageCreateTime().getTime() / 1000)) {
            chatConversation.setLastMessageContent("你撤回了一条消息");
            chatConversationRepository.update(chatConversation);
        }
        return true;
    }
}
