package com.alibaba.tripscrm.service.middleware.metaq.consumer.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 子任务触发执行，内部消费
 *
 * <AUTHOR>
 * @date 2023-08-30 17:26:58
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service(value = "innerSubTaskTriggerConsumer")
public class InnerSubTaskTriggerConsumer implements MessageListenerConcurrently {
    private final TaskService taskService;
    private final TaskExecuteStrategy taskExecuteStrategy;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logFail("receive", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("receiveMsg", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("receive message",  LogListUtil.newArrayList(message));
            TaskExecuteParam param = JSONObject.parseObject(message, new TypeReference<TaskExecuteParam>() {});
            // 执行任务方法
            taskExecuteStrategy.runSub(param);
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("子任务实例执行出错", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
    }
}
