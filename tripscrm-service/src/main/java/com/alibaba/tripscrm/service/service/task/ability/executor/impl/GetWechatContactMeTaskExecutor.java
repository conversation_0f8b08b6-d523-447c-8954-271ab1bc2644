package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.WechatContactMeDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatContactMeService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GetWechatContactMeTaskExecutor extends AbstractTaskExecutor {
    private final WechatContactMeService wechatContactMeService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String wechatContactMeId = getFinalTargetId(context, taskDataBody);
        WechatContactMeDO wechatContactMeDO = wechatContactMeService.getById(Long.parseLong(wechatContactMeId));
        if (Objects.isNull(wechatContactMeDO)) {
            PlatformLogUtil.logFail("invalid wechatContactMeId", LogListUtil.newArrayList(wechatContactMeId));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        JSONObject data = new JSONObject();
        data.put("qrCodeUrl", wechatContactMeDO.getQrCode());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return null;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_CONTACT_ME_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        String configStr = context.getTaskInfoDOSnapshot().getConfig();
        if (StringUtils.isEmpty(configStr)) {
            return null;
        }

        JSONObject configJson = JSONObject.parseObject(configStr);
        if (!configJson.containsKey("wechatContactMeId")) {
            PlatformLogUtil.logFail("empty wechatContactMeId", LogListUtil.newArrayList(context.getTaskInfoDOSnapshot().getId()));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        return String.valueOf(configJson.getLong("wechatContactMeId"));
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.GET_WECHAT_CONTACT_ME;
    }
}
