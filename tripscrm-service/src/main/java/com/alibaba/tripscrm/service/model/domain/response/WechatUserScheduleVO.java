package com.alibaba.tripscrm.service.model.domain.response;

import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.model.dto.seller.SellerWorkScheduleInfoDTO;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.*;

@Data
public class WechatUserScheduleVO implements Serializable {

    /**
     * 商家Id
     */
    private Long sellerId;
    /**
     * 业务空间Id
     */
    private Long spaceId;

    /**
     * 企微成员排班信息列表
     */
    private List<WechatUserScheduleInfo> scheduleInfoList;

    // 构造函数
    public WechatUserScheduleVO() {
        this.scheduleInfoList = new ArrayList<>();
    }

    // 从DTO列表构建包装类的静态方法
    public WechatUserScheduleVO build(List<SellerWorkScheduleInfoDTO> dtoList, WechatUserService wechatUserService) {
        if (dtoList == null || dtoList.isEmpty()) {
            return new WechatUserScheduleVO();
        }

        WechatUserScheduleVO wrapper = new WechatUserScheduleVO();

        // 设置商家ID和空间ID（所有DTO具有相同的商家ID和空间ID）
        SellerWorkScheduleInfoDTO firstDto = dtoList.get(0);
        wrapper.setSellerId(firstDto.getSellerId());
        wrapper.setSpaceId(firstDto.getSpaceId());

        //用于存储每个企微成员ID对应的排班信息
        Map<String, WechatUserScheduleInfo> userScheduleMap = new HashMap<>();

        // 处理每个排班记录
        for (SellerWorkScheduleInfoDTO dto : dtoList) {
            String wechatUserIdsStr = dto.getWechatUserIds();
            if (wechatUserIdsStr != null && !wechatUserIdsStr.isEmpty()) {
                // 解析企微成员ID字符串
                String[] userIds = wechatUserIdsStr.split(",");
                for (String userIdStr : userIds) {
                    try {
                        //获取或创建该用户的排班信息对象
                        WechatUserScheduleInfo userSchedule = userScheduleMap.getOrDefault(userIdStr, new WechatUserScheduleInfo(userIdStr));        // 添加排班详情
                        userSchedule.addSchedule(new ScheduleDetail(
                                dto.getDayOfWeek(),
                                dto.getBeginTime(),
                                dto.getEndTime()
                        ));
                        List<WechatUserDTO> userDTOList = wechatUserService.listById(Collections.singletonList(userIdStr));
                        if (CollectionUtils.isNotEmpty(userDTOList)){
                            userSchedule.setWechatUserName(userDTOList.get(0).getName());
                        }
                        // 更新映射
                        userScheduleMap.put(userIdStr, userSchedule);
                    } catch (Exception e) {
                        // 处理无效的用户ID
                        continue;
                    }
                }
            }
        }

        //将映射转换为列表
        wrapper.setScheduleInfoList(new ArrayList<>(userScheduleMap.values()));

        return wrapper;
    }

    /**
     * 企微成员个人排班信息
     */
    @Setter
    @Getter
    public static class WechatUserScheduleInfo implements Serializable {

        // getter和setter方法
        /*** 企微成员ID
         */
        private String wechatUserId;

        /**
         * 企微成员名称
         */
        private String wechatUserName;

        /**
         * 该成员的排班详情列表
         */
        private List<ScheduleDetail> scheduleDetails;

        // 构造函数
        public WechatUserScheduleInfo(String wechatUserId) {
            this.wechatUserId = wechatUserId;
            this.scheduleDetails = new ArrayList<>();
        }

        // 添加排班详情
        public void addSchedule(ScheduleDetail detail) {
            this.scheduleDetails.add(detail);
        }

    }

    /**
     * 排班详情
     */
    @Setter
    @Getter
    public static class ScheduleDetail implements Serializable {

        /**
         * 星期几，0表示兜底
         */
        private Byte dayOfWeek;

        /**
         * 开始时间
         */
        private String beginTime;
        /**
         * 结束时间
         */
        private String endTime;

        // 构造函数
        public ScheduleDetail(Byte dayOfWeek, String beginTime, String endTime) {
            this.dayOfWeek = dayOfWeek;
            this.beginTime = beginTime;
            this.endTime = endTime;
        }

    }
}
