package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.data.WechatOfficialAccountArticleDO;
import com.alibaba.tripscrm.service.service.wechat.WechatOfficialAccountArticleService;
import com.alibaba.tripzoo.proxy.model.WechatOfficialFreePublishBO;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 微信公众号文章同步任务
 *
 * <AUTHOR>
 * @date 2023-08-28 16:12:48
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatOfficialAccountArticleSyncProcessor extends JavaProcessor {
    private final WechatOfficialAccountArticleService wechatOfficialAccountArticleService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        List<WechatOfficialFreePublishBO.Item> itemList = new ArrayList<>();
        for (int offset = 0; ; ) {
            WechatOfficialFreePublishBO wechatOfficialFreePublishBO = wechatOfficialAccountArticleService.fetchFreePublishList(offset);
            if (Objects.isNull(wechatOfficialFreePublishBO) || !NumberUtils.biggerThanZero(wechatOfficialFreePublishBO.getItemCount())) {
                break;
            }

            itemList.addAll(wechatOfficialFreePublishBO.getItems());
            offset += wechatOfficialFreePublishBO.getItemCount();
        }

        for (WechatOfficialFreePublishBO.Item item : itemList) {
            List<WechatOfficialAccountArticleDO> oldWechatOfficialAccountArticleDOList = wechatOfficialAccountArticleService.listByArticleId(item.getArticleId());
            List<WechatOfficialAccountArticleDO> newWechatOfficialAccountArticleDOList = new ArrayList<>();
            for (WechatOfficialFreePublishBO.Content.NewsItem newsItem : item.getContent().getNewsItems()) {
                WechatOfficialAccountArticleDO wechatOfficialAccountArticleDO = new WechatOfficialAccountArticleDO();
                wechatOfficialAccountArticleDO.setDeleted(Optional.ofNullable(newsItem.getIsDeleted()).orElse(false) ? 1 : 0);
                wechatOfficialAccountArticleDO.setArticleId(item.getArticleId());
                wechatOfficialAccountArticleDO.setTitle(newsItem.getTitle());
                wechatOfficialAccountArticleDO.setAuthor(newsItem.getAuthor());
                wechatOfficialAccountArticleDO.setContentSourceUrl(Optional.ofNullable(newsItem.getContentSourceUrl()).orElse(""));
                wechatOfficialAccountArticleDO.setThumbMediaId(Optional.ofNullable(newsItem.getThumbMediaId()).orElse(""));
                wechatOfficialAccountArticleDO.setShowCoverPic(Optional.ofNullable(newsItem.getShowCoverPic()).orElse(0));
                wechatOfficialAccountArticleDO.setNeedOpenComment(Optional.ofNullable(newsItem.getNeedOpenComment()).orElse(0));
                wechatOfficialAccountArticleDO.setOnlyFansCanComment(Optional.ofNullable(newsItem.getOnlyFansCanComment()).orElse(0));
                wechatOfficialAccountArticleDO.setUrl(newsItem.getUrl());
                wechatOfficialAccountArticleDO.setUpdateTime(new Date(Long.parseLong(item.getUpdateTime()) * 1000));
                wechatOfficialAccountArticleDO.setDigest(newsItem.getDigest());
                wechatOfficialAccountArticleDO.setContent(newsItem.getContent());
                newWechatOfficialAccountArticleDOList.add(wechatOfficialAccountArticleDO);
            }

            Map<String, WechatOfficialAccountArticleDO> url2NewDo = newWechatOfficialAccountArticleDOList.stream()
                    .collect(Collectors.toMap(WechatOfficialAccountArticleDO::getUrl, x -> x));
            Map<String, WechatOfficialAccountArticleDO> url2OldDo = oldWechatOfficialAccountArticleDOList.stream()
                    .collect(Collectors.toMap(WechatOfficialAccountArticleDO::getUrl, x -> x));

            List<WechatOfficialAccountArticleDO> insertList = newWechatOfficialAccountArticleDOList.stream().filter(x -> !url2OldDo.containsKey(x.getUrl())).collect(Collectors.toList());
            List<WechatOfficialAccountArticleDO> updateList = newWechatOfficialAccountArticleDOList.stream().filter(x -> url2OldDo.containsKey(x.getUrl())).collect(Collectors.toList());

            for (WechatOfficialAccountArticleDO wechatOfficialAccountArticleDO : insertList) {
                wechatOfficialAccountArticleService.insert(wechatOfficialAccountArticleDO);
            }

            for (WechatOfficialAccountArticleDO wechatOfficialAccountArticleDO : updateList) {
                wechatOfficialAccountArticleService.updateByArticleIdAndUrl(wechatOfficialAccountArticleDO);
            }
        }

        return new ProcessResult(true);
    }
}
