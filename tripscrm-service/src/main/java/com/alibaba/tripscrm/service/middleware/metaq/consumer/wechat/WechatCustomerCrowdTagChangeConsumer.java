package com.alibaba.tripscrm.service.middleware.metaq.consumer.wechat;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.factory.DelayScheduleMethodFactory;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.dto.tag.CrowdTagSynchronizerDTO;
import com.alibaba.tripscrm.service.synchronizer.CrowdTagSynchronizer;
import com.alibaba.tripscrm.service.synchronizer.SyncResult;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 诸葛标签变更消费
 * topic: scrm_wechat_customer_crowd_tag_diff
 * consumerId: CID_scrm_wechat_customer_crowd_tag_diff
 * <p>
 * 消息格式为：${corp_id}\t${union_id}\t${external_user_id}
 * ${corp_id}必传
 * ${union_id}和${external_user_id}至少传一个
 *
 * <AUTHOR>
 * @since 2024/12/12
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerCrowdTagChangeConsumer implements MessageListenerConcurrently {
    private final MetaqProducer metaqProducer;
    private final CrowdTagSynchronizer crowdTagSynchronizer;

    @Switch(description = "获取uid失败后重试等待时间", name = "delayRetrySeconds")
    public static Integer delayRetrySeconds = 60 * 2;

    private volatile Set<String> systemTagSyncLbsPermanentCitySet;

    private volatile Set<String> systemTagSyncLbsPermanentProvinceSet;

    @PostConstruct
    public void init() {
        DelayScheduleMethodFactory.register(DelayScheduleMethodEnum.SEND_WECHAT_CUSTOMER_CROWD_TAG_CHANGE_MESSAGE, this::sendRetryMessage);
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("接收到客户诸葛标签变更消息", LogListUtil.newArrayList(msg, context));
            String receivedMsg = new String(msg.getBody());
            String tags = msg.getTags();
            boolean isRetry = StringUtils.hasLength(tags) && tags.contains("retry");
            if (!dealWithMessage(receivedMsg, isRetry)) {
                PlatformLogUtil.logFail("处理客户诸葛标签变更消息失败", LogListUtil.newArrayList(receivedMsg, isRetry));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 消息处理
     *
     * @param message 消息
     * @return Boolean
     */
    private Boolean dealWithMessage(String message, boolean isRetry) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("客户诸葛标签变更消息解析前结果", LogListUtil.newArrayList(message, isRetry));
            if (!StringUtils.hasLength(message)) {
                PlatformLogUtil.logFail("客户诸葛标签变更消息内容解析为空", LogListUtil.newArrayList(message, isRetry));
                return false;
            }

            List<String> contentList = Arrays.stream(message.split("\t")).filter(StringUtils::hasLength).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contentList) || contentList.size() < 2 || contentList.size() > 3) {
                PlatformLogUtil.logFail("客户诸葛标签变更消息内容解析为空", LogListUtil.newArrayList(message, isRetry));
                return false;
            }

            String corpId = contentList.get(0);
            String unionId = contentList.get(1);
            String externalUserId = contentList.size() == 3 ? contentList.get(2) : null;
            SpaceInfoThreadLocalUtils.setCorpId(corpId);

            if (!StringUtils.hasLength(unionId) && !StringUtils.hasLength(externalUserId)) {
                PlatformLogUtil.logFail("unionId和externalUserId不可同时为空");
                return false;
            }

            // 同步客户信息 & 成员-客户关系
            BaseSynchronizerContext<CrowdTagSynchronizerDTO> context = new BaseSynchronizerContext<>();
            CrowdTagSynchronizerDTO crowdTagSynchronizerDTO = new CrowdTagSynchronizerDTO();
            if (!StringUtils.hasLength(externalUserId)) {
                externalUserId = unionId;
            }
            context.setExternalUserId(externalUserId);
            context.setCorpId(corpId);
            context.setUnionId(unionId);
            crowdTagSynchronizerDTO.setIsRetry(isRetry);
            context.setData(crowdTagSynchronizerDTO);
            SyncResult<CrowdTagSynchronizerDTO> syncResult = crowdTagSynchronizer.sync(context);
            if (!syncResult.getSuccess()) {
                PlatformLogUtil.logFail("同步客户诸葛标签信息失败", syncResult.getMessage(), LogListUtil.newArrayList(context, syncResult));
                return false;
            }
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    public void sendRetryMessage(String param) {
        JSONObject jsonParam = JSONObject.parseObject(param);
        metaqProducer.send(MQEnum.SCRM_WECHAT_CUSTOMER_CROWD_TAG_DIFF, "", "retry", String.format("%s\t%s\t%s", jsonParam.getString("corpId"), jsonParam.getString("unionId"), jsonParam.getString("externalUserId")));
    }

    private Set<String> getSystemTagSyncLbsPermanentCitySet() {
        if (Objects.nonNull(systemTagSyncLbsPermanentCitySet)) {
            return systemTagSyncLbsPermanentCitySet;
        }

        synchronized (this) {
            if (Objects.nonNull(systemTagSyncLbsPermanentCitySet)) {
                return systemTagSyncLbsPermanentCitySet;
            }

            systemTagSyncLbsPermanentCitySet = Arrays.stream(SwitchConfig.SYSTEM_TAG_SYNC_LBS_PERMANENT_CITY_LIST_STR.split(",")).filter(StringUtils::hasLength).collect(Collectors.toSet());
        }

        return systemTagSyncLbsPermanentCitySet;
    }

    private Set<String> getSystemTagSyncLbsPermanentProvinceSet() {
        if (Objects.nonNull(systemTagSyncLbsPermanentProvinceSet)) {
            return systemTagSyncLbsPermanentProvinceSet;
        }

        synchronized (this) {
            if (Objects.nonNull(systemTagSyncLbsPermanentProvinceSet)) {
                return systemTagSyncLbsPermanentProvinceSet;
            }

            systemTagSyncLbsPermanentProvinceSet = Arrays.stream(SwitchConfig.SYSTEM_TAG_SYNC_LBS_PERMANENT_PROVINCE_LIST_STR.split(",")).filter(StringUtils::hasLength).collect(Collectors.toSet());
        }

        return systemTagSyncLbsPermanentProvinceSet;
    }
}
