package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.com.google.common.collect.Lists;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.tag.TagSyncTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.domain.query.TagGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.TagInfoQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.dto.tag.WechatTagSynchronizerDTO;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.synchronizer.WechatTagSynchronizer;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.TagService;
import com.alibaba.tripzoo.proxy.request.GetCorpTagListRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.TagInfoResponse;
import com.taobao.csp.courier.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 企微标签同步定时任务
 *
 * <AUTHOR>
 * @since 2024/12/16
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatTagChangeSyncProcessor extends JavaProcessor {
    private final TagInfoService tagInfoService;
    private final TagGroupService tagGroupService;
    private final WechatTagSynchronizer wechatTagSynchronizer;
    private final TagService tagService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // 查询DB中所有的企微标签和企微标签组
        TagInfoQuery tagInfoQuery = new TagInfoQuery();
        tagInfoQuery.setTagType(Lists.newArrayList(TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode()));
        tagInfoQuery.setDeleted(IsDeleteEnum.NO.getCode());
        List<TagInfoDTO> tagInfoDTOS = tagInfoService.selectByCondition(tagInfoQuery);
        TagGroupQuery tagGroupQuery = new TagGroupQuery();
        tagGroupQuery.setTagType(Lists.newArrayList(TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode()));
        tagGroupQuery.setDeleted(IsDeleteEnum.NO.getCode());
        List<TagGroupDTO> tagGroupDTOS = tagGroupService.selectByCondition(tagGroupQuery);
        String corpId = WxConstants.DEFAULT_ENTERPRISE_CORP_ID;
        try {
            SpaceInfoThreadLocalUtils.setCorpId(corpId);
            // 从DB中查询所有企微标签和企微标签组，与微信端同步
            if (!CollectionUtils.isEmpty(tagInfoDTOS)) {
                for (TagInfoDTO tagInfoDTO : tagInfoDTOS) {
                    BaseSynchronizerContext<WechatTagSynchronizerDTO> wechatTagSynchronizerContext = new BaseSynchronizerContext<>();
                    WechatTagSynchronizerDTO data = new WechatTagSynchronizerDTO();
                    data.setTagType(TagSyncTypeEnum.TAG);
                    wechatTagSynchronizerContext.setTagId(tagInfoDTO.getSource());
                    wechatTagSynchronizerContext.setData(data);
                    wechatTagSynchronizerContext.setCorpId(corpId);
                    wechatTagSynchronizer.sync(wechatTagSynchronizerContext);
                }
            }
            if (!CollectionUtils.isEmpty(tagGroupDTOS)) {
                for (TagGroupDTO tagGroupDTO : tagGroupDTOS) {
                    BaseSynchronizerContext<WechatTagSynchronizerDTO> wechatTagSynchronizerContext = new BaseSynchronizerContext<>();
                    WechatTagSynchronizerDTO data = new WechatTagSynchronizerDTO();
                    data.setTagType(TagSyncTypeEnum.TAG_GROUP);
                    wechatTagSynchronizerContext.setTagGroupId(tagGroupDTO.getSource());
                    wechatTagSynchronizerContext.setData(data);
                    wechatTagSynchronizerContext.setCorpId(corpId);
                    wechatTagSynchronizer.sync(wechatTagSynchronizerContext);
                }
            }
            // 从企微端查询所有标签和标签组，与DB同步
            GetCorpTagListRequest getCorpTagListRequest = new GetCorpTagListRequest();
            getCorpTagListRequest.setGroupIdList(Lists.newArrayList());
            getCorpTagListRequest.setTagIdList(Lists.newArrayList());
            getCorpTagListRequest.setCorpId(corpId);
            ResultDO<List<TagInfoResponse>> resultDO = tagService.getCorpTagList(getCorpTagListRequest);
            if (!resultDO.getSuccess() || CollectionUtils.isEmpty(resultDO.getModel())) {
                PlatformLogUtil.logFail("从企业微信获取全量企业标签列表失败", LogListUtil.newArrayList(getCorpTagListRequest, resultDO));
                return new ProcessResult(false);
            }
            List<TagInfoResponse> tagInfoResponseList = resultDO.getModel();
            for (TagInfoResponse tagInfoResponse : tagInfoResponseList) {
                if (!StringUtils.hasLength(tagInfoResponse.getGroupId())) {
                    PlatformLogUtil.logFail("从企业微信获取的企业标签列表中的标签组id为空", LogListUtil.newArrayList(getCorpTagListRequest, resultDO));
                    continue;
                }
                BaseSynchronizerContext<WechatTagSynchronizerDTO> wechatTagSynchronizerContext = new BaseSynchronizerContext<>();
                WechatTagSynchronizerDTO data = new WechatTagSynchronizerDTO();
                data.setTagType(TagSyncTypeEnum.TAG_GROUP);
                wechatTagSynchronizerContext.setTagGroupId(tagInfoResponse.getGroupId());
                wechatTagSynchronizerContext.setData(data);
                wechatTagSynchronizerContext.setCorpId(corpId);
                wechatTagSynchronizer.sync(wechatTagSynchronizerContext);
            }

        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.SCHEDULE_PROCESSOR_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(tagInfoQuery));
            return new ProcessResult(false);
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
        return new ProcessResult(true);
    }
}
