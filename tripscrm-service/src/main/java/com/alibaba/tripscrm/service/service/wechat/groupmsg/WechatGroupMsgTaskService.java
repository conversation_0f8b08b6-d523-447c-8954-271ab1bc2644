package com.alibaba.tripscrm.service.service.wechat.groupmsg;

import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgTaskDO;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupMsgTaskQuery;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WechatGroupMsgTaskService {
    /**
     * 根据参数统计总数
     *
     * @param query
     */
    long count(WechatGroupMsgTaskQuery query);

    /**
     * 根据参数查询
     *
     * @param query
     */
    WechatGroupMsgTaskDO find(WechatGroupMsgTaskQuery query);

    /**
     * 列表查询
     *
     * @param query
     */
    List<WechatGroupMsgTaskDO> list(WechatGroupMsgTaskQuery query);

    /**
     * 创建
     *
     * @param record
     */
    int insertSelective(WechatGroupMsgTaskDO record);

    /**
     * 选择性修改
     *
     * @param record
     * @param condition
     */
    int updateSelective(WechatGroupMsgTaskDO record, WechatGroupMsgTaskQuery condition);

    /**
     * 唯一键冲突时更新
     *
     * @param record 数据
     */
    int upsertSelective(WechatGroupMsgTaskDO record);
}