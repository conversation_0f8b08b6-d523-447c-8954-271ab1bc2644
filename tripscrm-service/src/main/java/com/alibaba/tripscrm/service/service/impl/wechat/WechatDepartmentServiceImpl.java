package com.alibaba.tripscrm.service.service.impl.wechat;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceDO;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.space.SpaceResourceService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.wechat.WechatDepartmentService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.DepartmentService;
import com.alibaba.tripzoo.proxy.model.DepartmentBO;
import com.alibaba.tripzoo.proxy.request.WechatDepartmentTreeGetRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023-08-17 14:39:01
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatDepartmentServiceImpl implements WechatDepartmentService {
    private final DepartmentService departmentService;
    private final SpaceResourceService spaceResourceService;
    private final SpaceService spaceService;

    @Override
    @Cacheable(key = "'department_list_' + #corpId", value = "departmentCacheManager")
    public DepartmentBO list(String corpId) {
        WechatDepartmentTreeGetRequest request = new WechatDepartmentTreeGetRequest();
        request.setCorpId(corpId);
        ResultDO<DepartmentBO> resultDO = departmentService.list(request);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {

            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED);
        }
        return resultDO.getModel();
    }

    @Override
    public List<Integer> queryBindDepartmentIdListBySpaceId(Long spaceId) {
        if (!NumberUtils.biggerThanZero(spaceId)) {
            return null;
        }

        List<Integer> departmentIdList = spaceResourceService.listBySpaceIdAndResourceType(spaceId, ResourceTypeEnum.DEPARTMENT).stream().map(SpaceResourceDO::getTargetId).map(Integer::parseInt).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentIdList)) {
            return new ArrayList<>();
        }

        return departmentIdList;
    }

    @Override
    @Cacheable(key = "'queryBindDepartmentIdListBySpaceIdWithCache_spaceId:' + #spaceId", value = "oneMinutesAnd128MaximumSizeCacheManager", unless = "#result == null || #result.size() == 0")
    public List<Integer> queryBindDepartmentIdListBySpaceIdWithCache(Long spaceId) {
        return queryBindDepartmentIdListBySpaceId(spaceId);
    }

    @Override
    public Long getSpaceIdByDepartmentId(Integer departmentId) {
        if (Objects.isNull(departmentId)) {
            return null;
        }

        List<SpaceResourceDO> spaceResourceList = spaceResourceService.listByResourceTypeAndResourceId(ResourceTypeEnum.DEPARTMENT, String.valueOf(departmentId));
        if (CollectionUtils.isEmpty(spaceResourceList)) {
            return null;
        }

        if (!SwitchConfig.QUERY_SPACE_ID_BY_DEPARTMENT_ID_CHECK_CORP_ID_SWITCH) {
            return spaceResourceList.get(0).getSpaceId();
        }
        List<SpaceDO> spaceDOS = spaceService.list(
                spaceResourceList
                        .stream()
                        .map(SpaceResourceDO::getSpaceId)
                        .filter(Objects::nonNull)  // 过滤掉 getSpaceId() 为空的元素
                        .collect(Collectors.toList())
        );

        String currentCorpId = SpaceInfoThreadLocalUtils.getCorpId();

        spaceDOS = spaceDOS
                .stream()
                .filter(spaceDO -> Objects.equals(spaceDO.getCorpId(), currentCorpId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spaceDOS) || spaceDOS.size() != 1) {
            return null;
        }

        return spaceDOS.get(0).getId();
    }
}
