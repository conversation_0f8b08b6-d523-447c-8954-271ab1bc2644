package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatJoinGroupDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatJoinGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkRobotSingleMessageUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @title: WechatJoinGroupCheckProcessor
 * @projectName tripscrm
 * @description: 群活码新建群聊巡检
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatJoinGroupCheckProcessor extends JavaProcessor {
    private final TaskService taskService;
    private final TaskExecuteStrategy taskExecuteStrategy;
    private final WechatJoinGroupService wechatJoinGroupService;
    private  final DingTalkApi dingTalkApi;
    private static final String ADD_MEMBER_UPDATE_DETAIL = "add_member";
    private static final String UPDATE_DETAIL = "updateDetail";
    private static final String AUTO_CREATE_GROUP = "autoCreateGroup";
    public static String dingTalkAccessToken = "124b41d43a2cae3f29f3fab8df33fed506242e73254ae404917c5a38bb4b9956";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        TaskQuery taskQuery = new TaskQuery();
        taskQuery.setType(TaskType.MANAGER_WECHAT_JOIN_GROUP.getCode());
        taskQuery.setDeleted(0);
        taskQuery.setStatus(TaskStatusEnum.ONLINE.getStatus());
        taskQuery.setEnv(EnvUtils.getEnvironment());
        taskQuery.setSpaceId(10L);
        List<TaskInfoDO> taskList = taskService.query(taskQuery);
        if (CollectionUtils.isEmpty(taskList)) {
            return new ProcessResult(true);
        }

        List<TaskInfoDO> updateTaskList = taskList.stream().filter(taskInfoDO -> {
            JSONObject taskExtInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
            boolean autoCreateGroup = taskExtInfo.containsKey(AUTO_CREATE_GROUP) && taskExtInfo.getBoolean(AUTO_CREATE_GROUP);
            return autoCreateGroup;
        }).collect(Collectors.toList());
        updateTaskList.forEach(taskInfoDO -> {
            simulateJoinGroup(taskInfoDO);

        });

        return new ProcessResult(true);
    }

    /**
     * 模拟人群进群
     */
    @AteyeInvoker(description = "模拟人群进群", paraDesc = "taskId")
    private  void simulateJoinGroup(TaskInfoDO taskInfoDO) {
        //查询任务
        TaskQuery taskQuery = new TaskQuery();
        taskQuery.setType(TaskType.GET_WECHAT_JOIN_GROUP.getCode());
        taskQuery.setDeleted(0);
        taskQuery.setStatus(TaskStatusEnum.ONLINE.getStatus());
        taskQuery.setEnv(EnvUtils.getEnvironment());
        taskQuery.setActivityId(taskInfoDO.getActivityId());
        List<TaskInfoDO> taskList = taskService.query(taskQuery);
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        //获得群活码
        JSONObject configJson = JSONObject.parseObject(taskList.get(0).getConfig());
        if (Objects.isNull(configJson) || !configJson.containsKey(TaskConstant.CONTEXT_ID)) {
            return;
        }
        Long contextId = configJson.getLong(TaskConstant.CONTEXT_ID);
        //获得群id
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getByState(String.valueOf(contextId));
        List<String> chatIdList = Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).collect(Collectors.toList());

        Map<String, Object> extraInfo = new HashMap<>();
        extraInfo.put(UPDATE_DETAIL, ADD_MEMBER_UPDATE_DETAIL);
        extraInfo.put(TaskConstant.CORP_ID, SpaceInfoThreadLocalUtils.getCorpId());
        extraInfo.put(TaskConstant.CHAT_ID, chatIdList.get(0));
        extraInfo.put(TaskConstant.CONTEXT_ID, contextId);
        extraInfo.put(TaskConstant.IS_PATROL, true);
        TaskExecuteParam param = TaskExecuteParam.buildParam(new Date(), taskInfoDO.getId(), TaskTriggerTypeEnum.INTERFACE, ActivityTargetTypeEnum.WX_CHAT_ID.getCode(), chatIdList.get(0), true, extraInfo, null);
        //调用任务执行
        TripSCRMResult<String> stringTripSCRMResult = taskExecuteStrategy.runMain(param);
        PlatformLogUtil.logInfo("群活码新建群聊巡检，处理结果", LogListUtil.newArrayList(stringTripSCRMResult, taskInfoDO));
        //告警
        if (!stringTripSCRMResult.isSuccess()) {
            dingTalkApi.sendMessageToDingTalkGroup(dingTalkAccessToken, "群码可用群不足预警信息：【飞猪福利管家】-【用户运营】群码" +
                    "\n活动id:" + taskInfoDO.getActivityId() +
                    "\n群码:" + wechatJoinGroupDO.getQrCode() +
                    "\n当前无可用群，系统自动重试也未成功，请手工维护该群码。如果该群码已经不再使用，请下线群码，下线后将不再巡检，你也将不会再收到该信息", "**********,322980");

        }
    }
}
