package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgSendResultDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgTaskDO;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.groupmsg.WechatGroupMsgSendResultService;
import com.alibaba.tripscrm.service.service.wechat.groupmsg.WechatGroupMsgTaskService;
import com.alibaba.tripzoo.proxy.api.service.EnterpriseWechatMsgService;
import com.alibaba.tripzoo.proxy.enums.WechatMsgTemplateChatTypeEnum;
import com.alibaba.tripzoo.proxy.request.WechatGetGroupMsgSendResultRequest;
import com.alibaba.tripzoo.proxy.request.WechatGetGroupMsgTaskRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.WechatGetGroupMsgSendResultResponse;
import com.alibaba.tripzoo.proxy.result.WechatGetGroupMsgTaskResponse;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 企业微信群发任务数据同步
 *
 * <AUTHOR>
 * @since 2024/9/4 18:46
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class EnterpriseWechatGroupMsgDataSyncProcessor extends JavaProcessor {
    private final EnterpriseWechatMsgService enterpriseWechatMsgService;
    private final WechatGroupMsgTaskService wechatGroupMsgTaskService;
    private final WechatGroupMsgSendResultService wechatGroupMsgSendResultService;
    private final ResourceRelationService resourceRelationService;
    private final TaskInstanceService taskInstanceService;
    private final TaskService taskService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            Map<String, String> msgId2CorpId = queryMsgId2CorpId();
            PlatformLogUtil.logInfo("查询企微群发任务数据成功", LogListUtil.newArrayList(msgId2CorpId.size()));
            for (String msgId : msgId2CorpId.keySet()) {
                String corpId = msgId2CorpId.get(msgId);
                syncWechatGroupMsgTask(msgId, corpId);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("企业微信群发任务数据同步失败", e.getMessage(), e, LogListUtil.newArrayList());
        }
        return new ProcessResult(true);
    }

    private void syncWechatGroupMsgTask(String msgId, String corpId) {
        WechatGetGroupMsgTaskRequest wechatGetGroupMsgTaskRequest = new WechatGetGroupMsgTaskRequest();
        wechatGetGroupMsgTaskRequest.setCorpId(corpId);
        wechatGetGroupMsgTaskRequest.setMsgId(msgId);
        ResultDO<WechatGetGroupMsgTaskResponse> resultDO = enterpriseWechatMsgService.getGroupMsgTask(wechatGetGroupMsgTaskRequest);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess() || Objects.isNull(resultDO.getModel())) {
            PlatformLogUtil.logFail("从企微端获取群发成员发送任务列表失败", LogListUtil.newArrayList(wechatGetGroupMsgTaskRequest, msgId, corpId));
            return;
        }
        List<WechatGetGroupMsgTaskResponse.Task> taskList = resultDO.getModel().getTaskList();
        PlatformLogUtil.logInfo("从企微端获取群发成员发送任务列表成功", LogListUtil.newArrayList(taskList.size(), msgId, corpId));
        for (WechatGetGroupMsgTaskResponse.Task task : taskList) {
            WechatGroupMsgTaskDO wechatGroupMsgTaskDO = new WechatGroupMsgTaskDO();
            wechatGroupMsgTaskDO.setCorpId(corpId);
            wechatGroupMsgTaskDO.setMsgId(msgId);
            wechatGroupMsgTaskDO.setSendTime(task.getSendTime());
            wechatGroupMsgTaskDO.setUserId(task.getUserId());
            if (Objects.nonNull(task.getStatus())) {
                wechatGroupMsgTaskDO.setStatus(task.getStatus().byteValue());
            }
            int effectLines = wechatGroupMsgTaskService.upsertSelective(wechatGroupMsgTaskDO);
            if (effectLines < 1) {
                PlatformLogUtil.logFail("群发成员发送任务数据同步失败", LogListUtil.newArrayList(wechatGroupMsgTaskDO, msgId, corpId));
            }
            // 同步群发成员执行结果数据
            syncWechatGroupMsgSendResult(msgId, task.getUserId(), corpId);
        }
    }

    private void syncWechatGroupMsgSendResult(String msgId, String userId, String corpId) {
        WechatGetGroupMsgSendResultRequest wechatGetGroupMsgSendResultRequest = new WechatGetGroupMsgSendResultRequest();
        wechatGetGroupMsgSendResultRequest.setCorpId(corpId);
        wechatGetGroupMsgSendResultRequest.setMsgId(msgId);
        wechatGetGroupMsgSendResultRequest.setUserId(userId);
        int count =0;
        for (String cursor = null; ;) {

            wechatGetGroupMsgSendResultRequest.setCursor(cursor);
            ResultDO<WechatGetGroupMsgSendResultResponse> resultDO = enterpriseWechatMsgService.pageQueryGroupMsgSendResult(wechatGetGroupMsgSendResultRequest);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess() || Objects.isNull(resultDO.getModel())) {
                PlatformLogUtil.logFail("获取企业微信群发任务发送结果失败", LogListUtil.newArrayList(wechatGetGroupMsgSendResultRequest, msgId, userId, resultDO));
                break;
            }

            List<WechatGetGroupMsgSendResultResponse.SendResult> sendResultList = resultDO.getModel().getSendResultList();
            if (CollectionUtils.isEmpty(sendResultList)) {
                break;
            }
            count+=sendResultList.size();

            cursor = resultDO.getModel().getNextCursor();
            for (WechatGetGroupMsgSendResultResponse.SendResult sendResult : sendResultList) {
                WechatGroupMsgSendResultDO wechatGroupMsgSendResultDO = new WechatGroupMsgSendResultDO();
                wechatGroupMsgSendResultDO.setCorpId(corpId);
                wechatGroupMsgSendResultDO.setMsgId(msgId);
                wechatGroupMsgSendResultDO.setUserId(userId);
                wechatGroupMsgSendResultDO.setTargetId(sendResult.getTargetId());
                wechatGroupMsgSendResultDO.setTargetType(Objects.equals(WechatMsgTemplateChatTypeEnum.SINGLE, sendResult.getChatType()) ? (byte) 1 : (byte) 0);
                wechatGroupMsgSendResultDO.setSendTime(sendResult.getSendTime());
                if (Objects.nonNull(sendResult.getStatus())) {
                    wechatGroupMsgSendResultDO.setStatus(sendResult.getStatus().byteValue());
                }
                int effectLines = wechatGroupMsgSendResultService.upsertSelective(wechatGroupMsgSendResultDO);
                if (effectLines < 1) {
                    PlatformLogUtil.logFail("群发成员执行结果数据同步失败", LogListUtil.newArrayList(wechatGroupMsgSendResultDO, msgId, userId, corpId));
                }
            }

            if (!StringUtils.hasText(cursor)) {
                break;
            }
        }
        PlatformLogUtil.logInfo("从企微端获取群发成员执行结果成功", LogListUtil.newArrayList(count, msgId, userId, corpId));
    }


    @AteyeInvoker(description = "同步指定任务实例的发送数据", paraDesc = "taskInstanceId")
    public void sync(Long taskInstanceId) {
        try {
            List<ResourceRelationDO> resourceRelationList = new ArrayList<>();
            for (long minId = 1L; ; ) {
                ResourceRelationQuery resourceRelationQuery = new ResourceRelationQuery();
                resourceRelationQuery.setPage(true);
                resourceRelationQuery.setPageSize(500);
                resourceRelationQuery.setSourceId(String.valueOf(taskInstanceId));
                resourceRelationQuery.setStartCreateTime(getSyncTime());
                resourceRelationQuery.setType(ResourceRelationTypeEnum.SCRM_TASK_INSTANCE_WECHAT_GROUP_MSG_RELATION.getCode());
                resourceRelationQuery.setMinId(minId);
                List<ResourceRelationDO> list = resourceRelationService.query(resourceRelationQuery);
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }

                resourceRelationList.addAll(list);
                minId = list.stream().mapToLong(ResourceRelationDO::getId).max().orElse(0L) + 1L;
            }

            Map<String, String> msgId2CorpId = resourceRelationList.stream().collect(
                    Collectors.toMap(
                            ResourceRelationDO::getTargetId,
                            resourceRelationDO -> taskService.getCorpIdByTaskId(taskInstanceService.getTaskIdByTaskInstanceId(Long.parseLong(resourceRelationDO.getSourceId())))
                    ));

            for (String msgId : msgId2CorpId.keySet()) {
                String corpId = msgId2CorpId.get(msgId);
                syncWechatGroupMsgTask(msgId, corpId);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("企业微信群发任务数据手动同步失败", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    private Map<String, String> queryMsgId2CorpId() {
        List<ResourceRelationDO> resourceRelationList = new ArrayList<>();
        for (long minId = 1L; ; ) {
            ResourceRelationQuery resourceRelationQuery = new ResourceRelationQuery();
            resourceRelationQuery.setPage(true);
            resourceRelationQuery.setPageSize(500);
            resourceRelationQuery.setStartCreateTime(getMinSyncTime());
            resourceRelationQuery.setType(ResourceRelationTypeEnum.SCRM_TASK_INSTANCE_WECHAT_GROUP_MSG_RELATION.getCode());
            resourceRelationQuery.setMinId(minId);
            List<ResourceRelationDO> list = resourceRelationService.query(resourceRelationQuery);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }

            resourceRelationList.addAll(list);
            minId = list.stream().mapToLong(ResourceRelationDO::getId).max().orElse(0L) + 1L;
            PlatformLogUtil.logInfo("从资源关系表获取企业微信群发任务数据成功", LogListUtil.newArrayList(resourceRelationList.size(), minId));
        }

        return resourceRelationList.stream().collect(
                Collectors.toMap(
                        ResourceRelationDO::getTargetId,
                        resourceRelationDO -> taskService.getCorpIdByTaskId(taskInstanceService.getTaskIdByTaskInstanceId(Long.parseLong(resourceRelationDO.getSourceId())))
                ));
    }

    /**
     * 获取同步的数据最早的插入时间（昨天0点）
     */
    private Date getMinSyncTime() {
        LocalDate today = LocalDate.now();
        LocalDate previousDay = today.minusDays(1);
        LocalDateTime previousDayAtStartOfDay = previousDay.atStartOfDay();
        return Date.from(previousDayAtStartOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 手动获取同步的数据最早的插入时间（一周）
     */
    private Date getSyncTime() {
        LocalDate today = LocalDate.now();
        LocalDate previousDay = today.minusDays(7);
        LocalDateTime previousDayAtStartOfDay = previousDay.atStartOfDay();
        return Date.from(previousDayAtStartOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
}
