package com.alibaba.tripscrm.service.service.strategy.material;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.enums.material.AiMaterialAsynTaskStatusEnum;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.query.AiMaterialAsynTaskQuery;
import com.alibaba.tripscrm.service.model.dto.material.AiMaterialAsynTaskDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.material.TextMaterialDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripscrm.service.service.material.AiMaterialAsynTaskService;
import com.alibaba.tripscrm.service.service.material.MaterialVariableInfoService;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class TextMaterialConverter extends AbstractMaterialConverter<TextMaterialDTO> {

    @Resource
    private MaterialLinkService materialLinkService;
    @Resource
    private MaterialVariableInfoService materialVariableInfoService;
    @Resource
    private AiMaterialAsynTaskService aiMaterialAsynTaskService;

    @Override
    public TextMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        TextMaterialDTO textMaterialDTO = JSONObject.parseObject(content, TextMaterialDTO.class);
        if (textMaterialDTO == null || CollectionUtils.isEmpty(textMaterialDTO.getMessageParagraphList())) {
            return textMaterialDTO;
        }
        StringBuilder text = new StringBuilder();
        // 段落解析和信息追加
        appendParagraphContent(materialTrackRelationDTO, context, textMaterialDTO, text);
        textMaterialDTO.setContent(text.toString());
        return textMaterialDTO;
    }

    private void appendParagraphContent(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, TextMaterialDTO textMaterialDTO, StringBuilder text) {
        int paragraphIndex = 0;
        // 段落处理
        for (TextMaterialDTO.Paragraph paragraph : textMaterialDTO.getMessageParagraphList()) {
            // AI素材
            MaterialSupplyTypeEnum supplyTypeEnum = MaterialSupplyTypeEnum.getSupplyType(paragraph.getSupplyType());
            if (MaterialSupplyTypeEnum.DOMESTIC_HOTEL_TOP_AI.equals(supplyTypeEnum)
                    || MaterialSupplyTypeEnum.DOMESTIC_FLIGHT_TOP_AI.equals(supplyTypeEnum)
                    || MaterialSupplyTypeEnum.INTERNATIONAL_FLIGHT_TOP_AI.equals(supplyTypeEnum)) {
                settleAiMaterialAppendContent(materialTrackRelationDTO, text, paragraph, paragraphIndex, supplyTypeEnum);
                paragraphIndex = materialTrackRelationDTO.getMsgParagraphId();
                continue;
            }
            // 普通素材
            materialTrackRelationDTO.setMsgParagraphId(paragraphIndex++);
            String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
            if (StringUtils.isNotBlank(paragraph.getTextScript())) {
                // 变量替换
                String paragrmText = materialVariableInfoService.replaceVariable(paragraph.getTextScript(), context.getExtraInfo(), materialTrackRelationDTO);
                text.append(paragrmText).append("\n");
            }
            String originUrl = buildPathUrl(paragraph, context.getExtraInfo(), scrmTrackId);
            PlatformLogUtil.logInfo("文本消息素材转换器，获得文本链接", originUrl, materialTrackRelationDTO, paragraph);
            appendUrlShortLink(materialTrackRelationDTO, context, paragraph, originUrl, scrmTrackId, text);
        }
        if (text.length() > 0 && text.charAt(text.length() - 1) == '\n') {
            text.deleteCharAt(text.length() - 1);
        }
    }

    private void settleAiMaterialAppendContent(MaterialTrackRelationDTO materialTrackRelationDTO, StringBuilder text, TextMaterialDTO.Paragraph paragraph, int paragraphIndex, MaterialSupplyTypeEnum supplyTypeEnum) {
        AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO = getAiMaterialAsynTaskDTO(materialTrackRelationDTO);
        if (StringUtils.isBlank(aiMaterialAsynTaskDTO.getResult())) {
            PlatformLogUtil.logInfo("文本消息转换器，素材生成任务结果为空", materialTrackRelationDTO, aiMaterialAsynTaskDTO);
            throw new RuntimeException("AI素材获取异步生成的素材结果为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(aiMaterialAsynTaskDTO.getResult());
        String recommendJsonString = jsonObject.getString("recommendJsonString");
        if (StringUtils.isBlank(recommendJsonString)) {
            PlatformLogUtil.logInfo("文本消息转换器，素材生成任务结果信息不足", materialTrackRelationDTO, aiMaterialAsynTaskDTO);
            throw new RuntimeException("AI素材获取异步生成的素材结果信息不足");
        }
        JSONArray jsonArray = JSONArray.parseArray(recommendJsonString);
        for (Object object : jsonArray) {
            settleSingleAiPragrah(materialTrackRelationDTO, text, paragraph, paragraphIndex, supplyTypeEnum, object, aiMaterialAsynTaskDTO);
            paragraphIndex = materialTrackRelationDTO.getMsgParagraphId();
        }
    }

    private void settleSingleAiPragrah(MaterialTrackRelationDTO materialTrackRelationDTO, StringBuilder text, TextMaterialDTO.Paragraph paragraph, int paragraphIndex, MaterialSupplyTypeEnum supplyTypeEnum, Object object, AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO) {
        JSONObject recommend = JSONObject.parseObject(object.toString());
        String recommendText = recommend.getString("recommendText");
        String itemBody = recommend.getString("oriItemBody");
        if (StringUtils.isBlank(recommendText) || StringUtils.isBlank(itemBody)) {
            PlatformLogUtil.logInfo("文本消息转换器，素材生成任务结果信息不全", materialTrackRelationDTO, aiMaterialAsynTaskDTO);
            return;
        }
        AiMaterialMsgInfo aiMaterialMsgInfo = getSingleAiMaterialMsgInfo(supplyTypeEnum, recommendText, itemBody);
        if (StringUtils.isBlank(aiMaterialMsgInfo.getContent())
                || StringUtils.isBlank(aiMaterialMsgInfo.getTitle())
                || StringUtils.isBlank(aiMaterialMsgInfo.getOriginUrl())) {
            PlatformLogUtil.logFail("文本消息素材转换器，获得AI素材结果失败", LogListUtil.newArrayList(materialTrackRelationDTO, aiMaterialMsgInfo));
            return;
        }
        PlatformLogUtil.logInfo("文本消息素材转换器，获得AI素材结果", LogListUtil.newArrayList(materialTrackRelationDTO, aiMaterialMsgInfo));
        materialTrackRelationDTO.setMsgParagraphId(paragraphIndex + 1);
        String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
        String originUrl = getUrlShortLink(materialTrackRelationDTO, paragraph, aiMaterialMsgInfo, scrmTrackId);
        if (StringUtils.isBlank(originUrl)) {
            PlatformLogUtil.logFail("文本消息素材转换器，获得AI素材供给短链失败", LogListUtil.newArrayList(materialTrackRelationDTO, aiMaterialMsgInfo));
            return;
        }
        text.append(aiMaterialMsgInfo.getContent());
        text.append(originUrl).append("\n").append("\n");
    }

    private AiMaterialMsgInfo getSingleAiMaterialMsgInfo(MaterialSupplyTypeEnum supplyTypeEnum, String recommendText, String itemBody) {
        AiMaterialMsgInfo aiMaterialMsgInfo = new AiMaterialMsgInfo();
        aiMaterialMsgInfo.setContent(recommendText);
        JSONObject oriItemBody = JSONObject.parseObject(itemBody);
        switch (supplyTypeEnum) {
            case DOMESTIC_HOTEL_TOP_AI:
                String itemTitle = oriItemBody.getString("title");
                String shid = oriItemBody.getString("shid");
                aiMaterialMsgInfo.setTitle(itemTitle);
                aiMaterialMsgInfo.setOriginUrl(MaterialConstant.HOTEL_H5_PRE + shid);
                break;
            case DOMESTIC_FLIGHT_TOP_AI:
                break;
            case INTERNATIONAL_FLIGHT_TOP_AI:
                break;
            default:
                break;
        }
        return aiMaterialMsgInfo;
    }

    private String getUrlShortLink(MaterialTrackRelationDTO materialTrackRelationDTO, TextMaterialDTO.Paragraph paragraph, AiMaterialMsgInfo aiMaterialMsgInfo, String scrmTrackId) {
        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(aiMaterialMsgInfo.getOriginUrl());
        request.setTargetLinkType(getTargetLinkType(paragraph));
        request.setConvertShortLink(true);
        request.setTitle(Optional.ofNullable(aiMaterialMsgInfo.getTitle()).orElse(UUID.randomUUID().toString()));
        request.setUseInWechat(true);
        request.setScrmTrackId(scrmTrackId);
        request.setMaterialId(materialTrackRelationDTO.getMaterialId());
        // 红包需要设置默认参数
        if (Objects.equals(paragraph.getSupplyType(), MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY.getType())) {
            request.getCustomizeParamMap().put("navBg", "%23FFE226");
            request.getCustomizeParamMap().put("navColor", "%23000000");
        }
        return materialLinkService.convertLink(request);
    }

    private void appendUrlShortLink(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, TextMaterialDTO.Paragraph paragraph, String originUrl, String scrmTrackId, StringBuilder text) {
        if (StringUtils.isNotBlank(originUrl)) {
            String shortChainIntroduction = paragraph.getShortChainIntroduction();
            if (StringUtils.isNotBlank(shortChainIntroduction)) {
                shortChainIntroduction = materialVariableInfoService.replaceVariable(shortChainIntroduction, context.getExtraInfo(), materialTrackRelationDTO);
            }
            MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
            request.setOriginal(originUrl);
            request.setTargetLinkType(getTargetLinkType(paragraph));
            request.setConvertShortLink(true);
            request.setTitle(Optional.ofNullable(shortChainIntroduction).orElse(UUID.randomUUID().toString()));
            request.setUseInWechat(true);
            request.setCustomizeParamMap(MaterialUtils.parseCustomizeParam(paragraph.getCustomize()));
            if (Objects.equals(MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY, MaterialSupplyTypeEnum.getSupplyType(paragraph.getSupplyType()))) {
                request.getCustomizeParamMap().put("navBg", "%23FFE226");
                request.getCustomizeParamMap().put("navColor", "%23000000");
            }
            request.setScrmTrackId(scrmTrackId);
            request.setMaterialId(materialTrackRelationDTO.getMaterialId());
            originUrl = materialLinkService.convertLink(request);
            text.append(originUrl).append("\n");
        }
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        TextMaterialDTO textMaterialDTO = JSONObject.parseObject(content, TextMaterialDTO.class);
        if (textMaterialDTO == null || CollectionUtils.isEmpty(textMaterialDTO.getMessageParagraphList())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }

        List<MaterialSupplyDTO> materialSupplyList = Lists.newArrayList();
        for (TextMaterialDTO.Paragraph paragraph : textMaterialDTO.getMessageParagraphList()) {
            // 普通供给
            if (StringUtils.isNotBlank(paragraph.getSupplyType()) && StringUtils.isNotBlank(paragraph.getSupplyId())) {
                materialSupplyList.add(new MaterialSupplyDTO(paragraph.getSupplyType(), paragraph.getSupplyId(), paragraph.getParamMap()));
            }
            // AI生成的供给
            if (StringUtils.isNotBlank(paragraph.getSupplyType()) && StringUtils.isBlank(paragraph.getSupplyId()) &&
                    (MaterialSupplyTypeEnum.DOMESTIC_HOTEL_TOP_AI.getType().equals(paragraph.getSupplyType())
                            || MaterialSupplyTypeEnum.DOMESTIC_FLIGHT_TOP_AI.getType().equals(paragraph.getSupplyType())
                            || MaterialSupplyTypeEnum.INTERNATIONAL_FLIGHT_TOP_AI.getType().equals(paragraph.getSupplyType()))) {
                materialSupplyList.add(new MaterialSupplyDTO(paragraph.getSupplyType(), paragraph.getSupplyId(), paragraph.getParamMap()));
            }
        }
        return materialSupplyList;
    }

    /**
     * 获取目标链接类型
     *
     * @param paragraph 段落
     * @return 目标链接类型
     */
    private LinkTypeEnum getTargetLinkType(TextMaterialDTO.Paragraph paragraph) {
        if (MaterialSupplyTypeEnum.H5.getType().equals(paragraph.getPageType())) {
            return LinkTypeEnum.H5_LINK;
        }
        return LinkTypeEnum.MINI_PROGRAM_LIKE;
    }

    /**
     * 构建跳转链接
     *
     * @param paragraph 文本素材段落内容
     * @return 跳转链接
     */
    private String buildPathUrl(TextMaterialDTO.Paragraph paragraph, Map<String, Object> carryMap, String scrmTrackId) {
        if (MaterialSupplyTypeEnum.H5.getType().equals(paragraph.getPageType())) {
            return paragraph.getPageUrl();
        }

        Map<String, String> paramMap = paragraph.getParamMap();
        if (!CollectionUtils.isEmpty(paramMap)) {
            paramMap = paramMap.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> materialVariableInfoService.replaceVariable(entry.getValue(), carryMap)
            ));
        }
        return buildPathUrl(new MaterialSupplyDTO(paragraph.getSupplyType(), paragraph.getSupplyId(), paramMap), paramMap, paragraph.getPagePath(), scrmTrackId);
    }

    @Override
    public List<MessageBO> buildMessageBO(TextMaterialDTO materialDTO, MaterialContentConvertContext context) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgNum(materialDTO.getIndex());
        messageBO.setMsgType(MessageTypeEnum.TEXT);
        messageBO.setMsgContent(materialDTO.getContent());
        if (materialDTO.getAtAll()) {
            // 在开始处，艾特所有人
            messageBO.setAtLocation(0);
            messageBO.setAt(1);
        }
        return Lists.newArrayList(messageBO);
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(TextMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        WxMessageBO wxMessageBO = new WxMessageBO();
        wxMessageBO.setMsgType(WxAttachmentTypeEnum.TEXT);
        wxMessageBO.setMsgContent(materialDTO.getContent());
        return Lists.newArrayList(wxMessageBO);
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.TEXT;
    }

    private static @Nullable TextMaterialConverter.AiMaterialMsgInfo fillAiMaterialMsgInfoOfCategory(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialSupplyTypeEnum supplyTypeEnum, JSONObject oriItemBody, AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO, TextMaterialConverter.AiMaterialMsgInfo result) {
        switch (supplyTypeEnum) {
            case DOMESTIC_HOTEL_TOP_AI:
                String itemTitle = oriItemBody.getString("title");
                String shid = oriItemBody.getString("shid");
                if (StringUtils.isBlank(itemTitle) || StringUtils.isBlank(shid)) {
                    PlatformLogUtil.logInfo("文本消息转换器，素材生成任务结果缺少酒店相关信息", materialTrackRelationDTO, aiMaterialAsynTaskDTO);
                    throw new RuntimeException("AI素材获取异步生成的素材结果缺少酒店相关信息");
                }
                result.setTitle(itemTitle);
                result.setOriginUrl(MaterialConstant.HOTEL_H5_PRE + shid);
                return result;
            case DOMESTIC_FLIGHT_TOP_AI:
                break;
            case INTERNATIONAL_FLIGHT_TOP_AI:
                break;
            default:
                break;
        }
        return null;
    }

    private AiMaterialAsynTaskDTO getAiMaterialAsynTaskDTO(MaterialTrackRelationDTO materialTrackRelationDTO) {
        Long taskId = materialTrackRelationDTO.getTaskId();
        String targetId = materialTrackRelationDTO.getTargetId();
        Long materialId = materialTrackRelationDTO.getMaterialId();

        AiMaterialAsynTaskQuery query = new AiMaterialAsynTaskQuery();
        query.setTaskId(taskId);
        query.setMaterialId(materialId);
        query.setTargetId(targetId);
        query.setDeleted(IsDeleteEnum.NO.getCode());
        query.setStatus(AiMaterialAsynTaskStatusEnum.EXECUTE_SUCCESS.getCode());

        com.alibaba.tripscrm.service.model.common.ResultDO<List<AiMaterialAsynTaskDTO>> resultDO = aiMaterialAsynTaskService.getAiMaterialAsynTask(query);
        if (resultDO == null || !resultDO.isSuccess() || resultDO.getModel() == null || resultDO.getModel().size() == 0) {
            PlatformLogUtil.logInfo("文本消息转换器，获取素材生成任务失败", materialTrackRelationDTO, resultDO);
            throw new RuntimeException("AI素材获取异步生成的素材失败");
        }
        AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO = resultDO.getModel().get(0);
        return aiMaterialAsynTaskDTO;
    }

    @Data
    private class AiMaterialMsgInfo {
        /**
         * 消息主体
         */
        public String content;
        /**
         * 地址
         */
        public String originUrl;
        /**
         * 标题
         */
        public String title;
    }
}
