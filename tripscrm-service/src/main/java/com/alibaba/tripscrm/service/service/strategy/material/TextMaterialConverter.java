package com.alibaba.tripscrm.service.service.strategy.material;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.enums.material.AiMaterialAsynTaskStatusEnum;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.query.AiMaterialAsynTaskQuery;
import com.alibaba.tripscrm.service.model.dto.material.AiMaterialAsynTaskDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.material.TextMaterialDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripscrm.service.service.material.AiMaterialAsynTaskService;
import com.alibaba.tripscrm.service.service.material.MaterialVariableInfoService;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class TextMaterialConverter extends AbstractMaterialConverter<TextMaterialDTO> {

    @Resource
    private MaterialLinkService materialLinkService;
    @Resource
    private MaterialVariableInfoService materialVariableInfoService;
    @Resource
    private AiMaterialAsynTaskService aiMaterialAsynTaskService;

    @Override
    public TextMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        TextMaterialDTO textMaterialDTO = JSONObject.parseObject(content, TextMaterialDTO.class);
        if (textMaterialDTO == null || CollectionUtils.isEmpty(textMaterialDTO.getMessageParagraphList())) {
            return textMaterialDTO;
        }
        StringBuilder text = new StringBuilder();
        // 段落解析和信息追加
        appendParagraphContent(materialTrackRelationDTO, context, textMaterialDTO, text);
        textMaterialDTO.setContent(text.toString());
        return textMaterialDTO;
    }

    private void appendParagraphContent(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, TextMaterialDTO textMaterialDTO, StringBuilder text) {
        int paragraphIndex = 0;
        // 段落处理
        for (TextMaterialDTO.Paragraph paragraph : textMaterialDTO.getMessageParagraphList()) {
            // AI素材
            MaterialSupplyTypeEnum supplyTypeEnum = MaterialSupplyTypeEnum.getSupplyType(paragraph.getSupplyType());
            if (MaterialSupplyTypeEnum.DOMESTIC_HOTEL_TOP_AI.equals(supplyTypeEnum)
                    || MaterialSupplyTypeEnum.DOMESTIC_FLIGHT_TOP_AI.equals(supplyTypeEnum)
                    || MaterialSupplyTypeEnum.INTERNATIONAL_FLIGHT_TOP_AI.equals(supplyTypeEnum)) {
                settleAiMaterialAppendContent(materialTrackRelationDTO, context, text, paragraph, paragraphIndex, supplyTypeEnum);
                paragraphIndex = materialTrackRelationDTO.getMsgParagraphId();
                continue;
            }
            // 普通素材
            materialTrackRelationDTO.setMsgParagraphId(paragraphIndex++);
            String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
            if (StringUtils.isNotBlank(paragraph.getTextScript())) {
                // 变量替换
                String paragrmText = materialVariableInfoService.replaceVariable(paragraph.getTextScript(), context.getExtraInfo(), materialTrackRelationDTO);
                text.append(paragrmText).append("\n");
            }

            String originUrl = buildPathUrl(paragraph, context.getExtraInfo(), scrmTrackId,materialTrackRelationDTO.getSceneType());
            PlatformLogUtil.logInfo("文本消息素材转换器，获得文本链接", originUrl, materialTrackRelationDTO, paragraph);
            appendUrlShortLink(materialTrackRelationDTO, context, paragraph, originUrl, scrmTrackId, text);
        }
        if (text.length() > 0 && text.charAt(text.length() - 1) == '\n') {
            text.deleteCharAt(text.length() - 1);
        }
    }

    private void settleAiMaterialAppendContent(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, StringBuilder text, TextMaterialDTO.Paragraph paragraph, int paragraphIndex, MaterialSupplyTypeEnum supplyTypeEnum) {
        AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO = getAiMaterialAsynTaskDTO(materialTrackRelationDTO, context);
        if (StringUtils.isBlank(aiMaterialAsynTaskDTO.getResult())) {
            PlatformLogUtil.logInfo("文本消息转换器，素材生成任务结果为空", materialTrackRelationDTO, aiMaterialAsynTaskDTO);
            throw new RuntimeException("AI素材获取异步生成的素材结果为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(aiMaterialAsynTaskDTO.getResult());
        String recommendJsonString = jsonObject.getString("recommendJsonString");
        if (StringUtils.isBlank(recommendJsonString)) {
            PlatformLogUtil.logInfo("文本消息转换器，素材生成任务结果信息不足", materialTrackRelationDTO, aiMaterialAsynTaskDTO);
            throw new RuntimeException("AI素材获取异步生成的素材结果信息不足");
        }
        JSONArray jsonArray = JSONArray.parseArray(recommendJsonString);
        for (Object object : jsonArray) {
            settleSingleAiPragrah(materialTrackRelationDTO, text, paragraph, paragraphIndex, supplyTypeEnum, object, aiMaterialAsynTaskDTO);
            paragraphIndex = materialTrackRelationDTO.getMsgParagraphId();
        }
    }

    private void settleSingleAiPragrah(MaterialTrackRelationDTO materialTrackRelationDTO, StringBuilder text, TextMaterialDTO.Paragraph paragraph, int paragraphIndex, MaterialSupplyTypeEnum supplyTypeEnum, Object object, AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO) {
        JSONObject recommend = JSONObject.parseObject(object.toString());
        String recommendText = recommend.getString("recommendText");
        String itemBody = recommend.getString("oriItemBody");
        if (StringUtils.isBlank(recommendText) || StringUtils.isBlank(itemBody)) {
            PlatformLogUtil.logInfo("文本消息转换器，素材生成任务结果信息不全", materialTrackRelationDTO, aiMaterialAsynTaskDTO);
            return;
        }
        AiMaterialMsgInfo aiMaterialMsgInfo = getSingleAiMaterialMsgInfo(supplyTypeEnum, recommendText, itemBody);
        if (StringUtils.isBlank(aiMaterialMsgInfo.getContent())
                || StringUtils.isBlank(aiMaterialMsgInfo.getTitle())
                || StringUtils.isBlank(aiMaterialMsgInfo.getOriginUrl())) {
            PlatformLogUtil.logFail("文本消息素材转换器，获得AI素材结果失败", LogListUtil.newArrayList(materialTrackRelationDTO, aiMaterialMsgInfo));
            return;
        }
        PlatformLogUtil.logInfo("文本消息素材转换器，获得AI素材结果", LogListUtil.newArrayList(materialTrackRelationDTO, aiMaterialMsgInfo));
        materialTrackRelationDTO.setMsgParagraphId(paragraphIndex + 1);
        String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
        String originUrl = getUrlShortLink(materialTrackRelationDTO, paragraph, aiMaterialMsgInfo, scrmTrackId);
        if (StringUtils.isBlank(originUrl)) {
            PlatformLogUtil.logFail("文本消息素材转换器，获得AI素材供给短链失败", LogListUtil.newArrayList(materialTrackRelationDTO, aiMaterialMsgInfo));
            return;
        }
        text.append(aiMaterialMsgInfo.getContent());
        text.append(originUrl).append("\n").append("\n");
    }

    private AiMaterialMsgInfo getSingleAiMaterialMsgInfo(MaterialSupplyTypeEnum supplyTypeEnum, String recommendText, String itemBody) {
        AiMaterialMsgInfo aiMaterialMsgInfo = new AiMaterialMsgInfo();
        aiMaterialMsgInfo.setContent(recommendText);
        JSONObject oriItemBody = JSONObject.parseObject(itemBody);
        switch (supplyTypeEnum) {
            case DOMESTIC_HOTEL_TOP_AI:
                String itemTitle = oriItemBody.getString("title");
                String shid = oriItemBody.getString("shid");
                aiMaterialMsgInfo.setTitle(itemTitle);
                aiMaterialMsgInfo.setOriginUrl(MaterialConstant.HOTEL_H5_PRE + shid);
                break;
            case DOMESTIC_FLIGHT_TOP_AI:
                aiMaterialMsgInfo.setTitle(buildFlightItemTitle(oriItemBody));
                aiMaterialMsgInfo.setOriginUrl(buildDomesticFlightItemUrl(oriItemBody));
                break;
            case INTERNATIONAL_FLIGHT_TOP_AI:
                aiMaterialMsgInfo.setTitle(buildFlightItemTitle(oriItemBody));
                aiMaterialMsgInfo.setOriginUrl(buildInternationalFlightItemUrl(oriItemBody));
                break;
            default:
                break;
        }
        return aiMaterialMsgInfo;
    }

    /**
     * 根据商品推荐数据，构建机票的商品标题
     *
     * @param oriItemBody 商品推荐数据
     * @return 机票的商品标题
     */
    private String buildFlightItemTitle(JSONObject oriItemBody) {
        StringBuilder itemTitleBuilder = new StringBuilder();
        // 航班号
        String marketingFlightNo = oriItemBody.getString("marketingFlightNo");
        // 出发机场
        String depAirportName = oriItemBody.getString("depAirportName");
        // 到达机场
        String arrAirportName = oriItemBody.getString("arrAirportName");
        // 设置航班号
        if (StringUtils.isNotEmpty(marketingFlightNo)) {
            itemTitleBuilder.append(marketingFlightNo);
        }
        // 设置出发机场和到达机场
        if (StringUtils.isNotEmpty(depAirportName) && StringUtils.isNotEmpty(arrAirportName)) {
            itemTitleBuilder.append(depAirportName).append("-").append(arrAirportName);
        }
        return itemTitleBuilder.toString();
    }

    /**
     * 根据商品推荐数据，构建国内机票OTA页地址
     * 语雀地址：<a href="https://aliyuque.antfin.com/fliggy/guide/vmgrzb57f12vlgzr?singleDoc#">《国内机票-OTA》</a>
     *
     * @param oriItemBody 商品推荐数据
     * @return 国内机票OTA页地址
     */
    private String buildDomesticFlightItemUrl(JSONObject oriItemBody) {
        String depCityCode = oriItemBody.getString("depCityCode");
        String arrCityCode = oriItemBody.getString("arrCityCode");
        Long depDateTime = oriItemBody.getLong("depDateTime");
        String marketingFlightNo = oriItemBody.getString("marketingFlightNo");
        if (StringUtils.isEmpty(depCityCode) || StringUtils.isEmpty(arrCityCode) || Objects.isNull(depDateTime) || StringUtils.isEmpty(marketingFlightNo)) {
            PlatformLogUtil.logFail("根据商品推荐数据，构建国内机票OTA页地址失败", LogListUtil.newArrayList(oriItemBody));
            throw new TripscrmException(ErrorCodeEnum.AI_MATERIAL_BUILD_FLIGHT_ITEM_URL_ERROR);
        }
        return MaterialConstant.DMESTIC_FLIGHT_OTA_H5_PRE
            // 出发城市三字码
            + "depCityCode=" + depCityCode
            // 到达城市三字码
            + "&arrCityCode=" + arrCityCode
            // 出发时间
            + "&leaveDate=" + DateUtils.getOtherSimpleDayDateString(new Date(depDateTime))
            // 到达时间
            + "&leaveFlightNo=" + marketingFlightNo;
    }

    /**
     * 根据商品推荐数据，构建国际机票OTA页地址
     * 语雀地址：<a href="https://aliyuque.antfin.com/fliggy/guide/gsz1hl?singleDoc#">《国际机票-OTA》</a>
     *
     * @param oriItemBody 商品推荐数据
     * @return 国际机票OTA页地址
     */
    private String buildInternationalFlightItemUrl(JSONObject oriItemBody) {
        String depCityCode = oriItemBody.getString("depCityCode");
        String arrCityCode = oriItemBody.getString("arrCityCode");
        Long depDateTime = oriItemBody.getLong("depDateTime");
        String marketingFlightNo = oriItemBody.getString("marketingFlightNo");
        if (StringUtils.isEmpty(depCityCode) || StringUtils.isEmpty(arrCityCode) || Objects.isNull(depDateTime) || StringUtils.isEmpty(marketingFlightNo)) {
            PlatformLogUtil.logFail("根据商品推荐数据，构建国际机票OTA页地址失败", LogListUtil.newArrayList(oriItemBody));
            throw new TripscrmException(ErrorCodeEnum.AI_MATERIAL_BUILD_FLIGHT_ITEM_URL_ERROR);
        }
        // 获取出发时间
        Date depDate = new Date(depDateTime);

        // 构造搜索航线数据
        JSONObject searchSegment = new JSONObject();
        searchSegment.put("depDate", DateUtils.getOtherSimpleDayDateString(depDate));
        searchSegment.put("depCityCode", depCityCode);
        searchSegment.put("depCityName", oriItemBody.getString("depCityName"));
        searchSegment.put("arrCityCode", arrCityCode);
        searchSegment.put("arrCityName", oriItemBody.getString("arrCityName"));

        // 构造已选航线数据
        JSONObject flightSegment = new JSONObject();
        flightSegment.put("depDate", DateUtils.getDetailDateString(depDate));
        flightSegment.put("marketingFlightNo", marketingFlightNo);
        flightSegment.put("journeySeq", "1");

        //构建返回地址url
        return MaterialConstant.INTERNATIONAL_FLIGHT_OTA_H5_PRE
            // 默认单程
            + "tripType=1"
            // 搜索航线数据
            + "&searchSegments=" + JSON.toJSONString(Collections.singletonList(searchSegment))
            // 已选航线数据
            + "&selectedSegments=" + JSON.toJSONString(Collections.singletonList(flightSegment));
    }

    private String getUrlShortLink(MaterialTrackRelationDTO materialTrackRelationDTO, TextMaterialDTO.Paragraph paragraph, AiMaterialMsgInfo aiMaterialMsgInfo, String scrmTrackId) {
        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(aiMaterialMsgInfo.getOriginUrl());
        request.setTargetLinkType(getTargetLinkType(paragraph));
        request.setConvertShortLink(true);
        request.setTitle(Optional.ofNullable(aiMaterialMsgInfo.getTitle()).orElse(UUID.randomUUID().toString()));
        request.setUseInWechat(true);
        request.setScrmTrackId(scrmTrackId);
        request.setMaterialId(materialTrackRelationDTO.getMaterialId());
        // 红包需要设置默认参数
        if (Objects.equals(paragraph.getSupplyType(), MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY.getType())) {
            request.getCustomizeParamMap().put("navBg", "%23FFE226");
            request.getCustomizeParamMap().put("navColor", "%23000000");
        }
        return materialLinkService.convertLink(request);
    }

    private void appendUrlShortLink(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, TextMaterialDTO.Paragraph paragraph, String originUrl, String scrmTrackId, StringBuilder text) {
        if (StringUtils.isNotBlank(originUrl)) {
            String shortChainIntroduction = paragraph.getShortChainIntroduction();
            if (StringUtils.isNotBlank(shortChainIntroduction)) {
                shortChainIntroduction = materialVariableInfoService.replaceVariable(shortChainIntroduction, context.getExtraInfo(), materialTrackRelationDTO);
            }
            MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
            request.setSceneType(materialTrackRelationDTO.getSceneType());
            request.setOriginal(originUrl);
            request.setTargetLinkType(getTargetLinkType(paragraph));
            request.setConvertShortLink(true);
            request.setTitle(Optional.ofNullable(shortChainIntroduction).orElse(UUID.randomUUID().toString()));
            request.setUseInWechat(true);
            request.setCustomizeParamMap(MaterialUtils.parseCustomizeParam(paragraph.getCustomize()));
            if (Objects.equals(MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY, MaterialSupplyTypeEnum.getSupplyType(paragraph.getSupplyType()))) {
                request.getCustomizeParamMap().put("navBg", "%23FFE226");
                request.getCustomizeParamMap().put("navColor", "%23000000");
            }
            request.setScrmTrackId(scrmTrackId);
            request.setMaterialId(materialTrackRelationDTO.getMaterialId());
            originUrl = materialLinkService.convertLink(request);
            text.append(originUrl).append("\n");
        }
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        TextMaterialDTO textMaterialDTO = JSONObject.parseObject(content, TextMaterialDTO.class);
        if (textMaterialDTO == null || CollectionUtils.isEmpty(textMaterialDTO.getMessageParagraphList())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }

        List<MaterialSupplyDTO> materialSupplyList = Lists.newArrayList();
        for (TextMaterialDTO.Paragraph paragraph : textMaterialDTO.getMessageParagraphList()) {
            // 普通供给
            if (StringUtils.isNotBlank(paragraph.getSupplyType()) && StringUtils.isNotBlank(paragraph.getSupplyId())) {
                materialSupplyList.add(new MaterialSupplyDTO(paragraph.getSupplyType(), paragraph.getSupplyId(), paragraph.getParamMap()));
            }
            // AI生成的供给
            if (StringUtils.isNotBlank(paragraph.getSupplyType()) && StringUtils.isBlank(paragraph.getSupplyId()) &&
                    (MaterialSupplyTypeEnum.DOMESTIC_HOTEL_TOP_AI.getType().equals(paragraph.getSupplyType())
                            || MaterialSupplyTypeEnum.DOMESTIC_FLIGHT_TOP_AI.getType().equals(paragraph.getSupplyType())
                            || MaterialSupplyTypeEnum.INTERNATIONAL_FLIGHT_TOP_AI.getType().equals(paragraph.getSupplyType()))) {
                materialSupplyList.add(new MaterialSupplyDTO(paragraph.getSupplyType(), paragraph.getSupplyId(), paragraph.getParamMap()));
            }
        }
        return materialSupplyList;
    }

    /**
     * 获取目标链接类型
     *
     * @param paragraph 段落
     * @return 目标链接类型
     */
    private LinkTypeEnum getTargetLinkType(TextMaterialDTO.Paragraph paragraph) {
        if (MaterialSupplyTypeEnum.H5.getType().equals(paragraph.getPageType())) {
            return LinkTypeEnum.H5_LINK;
        }
        if(MaterialSupplyTypeEnum.ALIPAY_MINI_PROGRAM.getType().equals(paragraph.getPageType())){
            return LinkTypeEnum.ALIPAY_LINK;
        }
        return LinkTypeEnum.MINI_PROGRAM_LIKE;
    }

    /**
     * 构建跳转链接
     *
     * @param paragraph 文本素材段落内容
     * @return 跳转链接
     */
    private String buildPathUrl(TextMaterialDTO.Paragraph paragraph, Map<String, Object> carryMap, String scrmTrackId,String sceneType) {
        if (MaterialSupplyTypeEnum.H5.getType().equals(paragraph.getPageType())) {
            return paragraph.getPageUrl();
        }

        Map<String, String> paramMap = paragraph.getParamMap();
        if (!CollectionUtils.isEmpty(paramMap)) {
            paramMap = paramMap.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> materialVariableInfoService.replaceVariable(entry.getValue(), carryMap)
            ));
        }
        return buildPathUrl(new MaterialSupplyDTO(paragraph.getSupplyType(), paragraph.getSupplyId(), paramMap), paramMap, paragraph.getPagePath(), scrmTrackId, sceneType);
    }

    @Override
    public List<MessageBO> buildMessageBO(TextMaterialDTO materialDTO, MaterialContentConvertContext context, String sceneType) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgNum(materialDTO.getIndex());
        messageBO.setMsgType(MessageTypeEnum.TEXT);
        messageBO.setMsgContent(materialDTO.getContent());
        if (materialDTO.getAtAll()) {
            // 在开始处，艾特所有人
            messageBO.setAtLocation(0);
            messageBO.setAt(1);
        }
        return Lists.newArrayList(messageBO);
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(TextMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        WxMessageBO wxMessageBO = new WxMessageBO();
        wxMessageBO.setMsgType(WxAttachmentTypeEnum.TEXT);
        wxMessageBO.setMsgContent(materialDTO.getContent());
        return Lists.newArrayList(wxMessageBO);
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.TEXT;
    }

    @Override
    public String buildSubscribeMsgTemplateContent(String content, String paramType) {
        return "";
    }

    private static @Nullable TextMaterialConverter.AiMaterialMsgInfo fillAiMaterialMsgInfoOfCategory(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialSupplyTypeEnum supplyTypeEnum, JSONObject oriItemBody, AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO, TextMaterialConverter.AiMaterialMsgInfo result) {
        switch (supplyTypeEnum) {
            case DOMESTIC_HOTEL_TOP_AI:
                String itemTitle = oriItemBody.getString("title");
                String shid = oriItemBody.getString("shid");
                if (StringUtils.isBlank(itemTitle) || StringUtils.isBlank(shid)) {
                    PlatformLogUtil.logInfo("文本消息转换器，素材生成任务结果缺少酒店相关信息", materialTrackRelationDTO, aiMaterialAsynTaskDTO);
                    throw new RuntimeException("AI素材获取异步生成的素材结果缺少酒店相关信息");
                }
                result.setTitle(itemTitle);
                result.setOriginUrl(MaterialConstant.HOTEL_H5_PRE + shid);
                return result;
            case DOMESTIC_FLIGHT_TOP_AI:
                break;
            case INTERNATIONAL_FLIGHT_TOP_AI:
                break;
            default:
                break;
        }
        return null;
    }

    private AiMaterialAsynTaskDTO getAiMaterialAsynTaskDTO(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        AiMaterialAsynTaskQuery query = new AiMaterialAsynTaskQuery();
        query.setTaskId(materialTrackRelationDTO.getTaskId());
        query.setMaterialId(materialTrackRelationDTO.getMaterialId());
        query.setTargetId(context.getExternalUserId());
        query.setDeleted(IsDeleteEnum.NO.getCode());
        query.setStatus(AiMaterialAsynTaskStatusEnum.EXECUTE_SUCCESS.getCode());

        com.alibaba.tripscrm.service.model.common.ResultDO<List<AiMaterialAsynTaskDTO>> resultDO = aiMaterialAsynTaskService.getAiMaterialAsynTask(query);
        if (resultDO == null || !resultDO.isSuccess() || resultDO.getModel() == null || resultDO.getModel().isEmpty()) {
            PlatformLogUtil.logInfo("文本消息转换器，获取素材生成任务失败", materialTrackRelationDTO, resultDO);
            throw new RuntimeException("AI素材获取异步生成的素材失败");
        }
        AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO = resultDO.getModel().get(0);
        return aiMaterialAsynTaskDTO;
    }

    @Data
    private class AiMaterialMsgInfo {
        /**
         * 消息主体
         */
        public String content;
        /**
         * 地址
         */
        public String originUrl;
        /**
         * 标题
         */
        public String title;
    }
}
