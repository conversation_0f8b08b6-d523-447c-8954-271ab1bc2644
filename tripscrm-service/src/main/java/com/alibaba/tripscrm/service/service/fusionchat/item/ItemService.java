package com.alibaba.tripscrm.service.service.fusionchat.item;

import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.model.domain.query.ItemQuery;
import com.alibaba.tripscrm.service.model.domain.fusionchat.dto.ItemSearchDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;


/**
 * <AUTHOR>
 * @Date 2024/6/28 16:55
 **/
public interface ItemService<T> {

    /**
     * 获取物料类型
     * @return 物料类型
     */
    ItemTypeEnum getType();

    /**
     * 查询物料列表
     * @param query 查询条件
     * @return 物料列表
     */
    ItemSearchDTO<T> queryItem(ItemQuery query);

    /**
     * 获取访问链接
     * @param itemTypeEnum 物料类型枚举
     * @param itemId 物料ID
     * @param targetTypeEnum 目标链接类型
     * @param convertShortLink 是否转化短链
     * @param trackRelationDTO 埋点信息
     * @return 访问链接
     */
    String queryLink(ItemTypeEnum itemTypeEnum, String itemId, LinkTypeEnum targetTypeEnum, Boolean convertShortLink, MaterialTrackRelationDTO trackRelationDTO);
}
