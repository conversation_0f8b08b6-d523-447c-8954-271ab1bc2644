package com.alibaba.tripscrm.service.service.group;

import com.alibaba.tripscrm.service.model.domain.query.GroupRelationQuery;
import com.alibaba.tripscrm.service.model.dto.group.GroupRelationDTO;

import java.util.List;

/**
 * @description：群组关系服务接口
 * @Author：wangrui
 * @create：2025/8/15 16:07
 * @Filename：GroupRelationService
 */
public interface GroupRelationNewService {

    /**
     * 查询群组关系
     *
     * @param query
     * @return
     */
    List<GroupRelationDTO> select(GroupRelationQuery query);

    /**
     * 更新群组关系
     *
     * @param groupRelationDTO
     * @return
     */
    Integer updateSelective(GroupRelationDTO groupRelationDTO, GroupRelationQuery query);

    /**
     * 删除群组关系
     *
     * @param query
     * @return
     */
    Integer delete(GroupRelationQuery query);

    /**
     * 批量插入群组关系
     *
     * @param insertList
     * @return
     */
    Integer batchInsert(List<GroupRelationDTO> insertList);
}
