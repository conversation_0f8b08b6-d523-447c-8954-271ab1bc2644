package com.alibaba.tripscrm.service.service.impl.material;

import com.ali.com.google.common.collect.Lists;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.MaterailInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialJsonKeyConstant;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialTypeMappingEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.subscribemsg.SubscribeMsgTemplateDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.service.material.MaterialVariableInfoService;
import com.alibaba.tripscrm.service.service.subscribemsg.SubscribeMsgTemplateService;
import com.alibaba.tripscrm.service.service.strategy.material.AbstractMaterialConverter;
import com.alibaba.tripscrm.service.service.strategy.material.factory.MaterialConvertFactory;
import com.alibaba.tripscrm.service.service.strategy.message.factory.MessageConverterFactory;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.admin.client.service.subscribemsg.SubscribeMsgService;
import com.alibaba.tripzoo.admin.enums.subscribemsg.MiniProgramPlatformEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2024/1/26 15:20
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MaterialTransferServiceImpl implements MaterialTransferService {
    private final MessageConverterFactory<?> messageConverterFactory;
    private final MaterailInfoMapper materailInfoMapper;
    private final LdbTairManager ldbTairManager;
    private final MaterialConvertFactory<?> materialConvertFactory;
    private final SubscribeMsgService subscribeMsgService;
    private final SubscribeMsgTemplateService subscribeMsgTemplateService;
    private final MaterialLinkService materialLinkService;
    private final MaterialVariableInfoService materialVariableInfoService;

    @Override
    public List<WxMessageBO> buildWxMessages(MaterailInfoDO materailInfoDO, MaterialTrackRelationDTO materialTrackRelationDTO,
                                             MaterialContentConvertContext context, String taskType) {
        try {
            // 标识素材使用状态
            markMaterialUsed(materailInfoDO.getId());
            // 获取消息对象
            if (materailInfoDO.getSceneId() == null) {
                return convertMaterialToWxMessageBO(materailInfoDO, materialTrackRelationDTO, context, TaskType.getByCode(taskType));
            }
            return convertMaterialToWxMessageBO(materailInfoDO, materialTrackRelationDTO, context, !TaskType.getByCode(taskType).equals(TaskType.SEND_TO_MOMENTS));
        } catch (Exception e) {
            PlatformLogUtil.logException("根据素材构建微信消息，执行异常", e.getMessage(), e, LogListUtil.newArrayList(materailInfoDO, taskType));
            throw e;
        }
    }


    @Override
    public List<MessageBO> buildMessages(MaterailInfoDO materailInfoDO, MaterialTrackRelationDTO materialTrackRelationDTO,
                                         MaterialContentConvertContext context, String taskType) {
        try {
            // 标识素材使用状态
            markMaterialUsed(materailInfoDO.getId());
            // 获取消息对象
            if (materailInfoDO.getSceneId() == null) {
                return convertMaterialToMessageBO(materailInfoDO, materialTrackRelationDTO, context, TaskType.getByCode(taskType));
            }
            return convertMaterialToMessageBO(materailInfoDO, materialTrackRelationDTO, context);
        } catch (Exception e) {
            PlatformLogUtil.logException("根据素材构建消息，执行异常", e.getMessage(), e, LogListUtil.newArrayList(materailInfoDO, taskType));
            throw e;
        }
    }

    /**
     * 素材转化为消息BO对象【新】
     *
     * @param materailInfoDO           素材对象
     * @param materialTrackRelationDTO 素材-trackId关系
     * @param context                  素材上下文
     * @return 消息BO对象
     */
    private List<MessageBO> convertMaterialToMessageBO(MaterailInfoDO materailInfoDO
            , MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        List<MessageBO> messageList = new ArrayList<>();
        JSONArray jsonArray = JSONObject.parseObject(materailInfoDO.getContent())
                .getJSONArray(MaterialJsonKeyConstant.messageList);
        int msgIndex = 0;
        for (int i = 0; i < jsonArray.size(); i++) {
            materialTrackRelationDTO.setMsgIndexId(msgIndex);
            // 处理器获取
            String contentType = jsonArray.getJSONObject(i).getString(MaterialJsonKeyConstant.messageType);
            AbstractMaterialConverter<?> materialContentConverter = materialConvertFactory
                    .getMaterialContentConverter(MaterialContentTypeEnum.codeOf(contentType));
            if (materialContentConverter == null) {
                PlatformLogUtil.logFail("根据素材构建消息，没有找到对应的内容处理器", LogListUtil.newArrayList(materailInfoDO, jsonArray));
                continue;
            }
            // 格式处理
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            jsonObject.put("index", msgIndex);
            materialTrackRelationDTO.setMaterialId(materailInfoDO.getId());
            List<MessageBO> messageBOS = materialContentConverter.buildMessageBO(
                    jsonObject.toJSONString(), materialTrackRelationDTO, context);
            if (CollectionUtils.isEmpty(messageBOS)) {
                PlatformLogUtil.logFail("根据素材构建消息，内容没有获取到消息信息", LogListUtil.newArrayList(materailInfoDO));
                continue;
            }
            msgIndex = messageBOS.stream().map(MessageBO::getMsgNum).max(Integer::compareTo).orElse(msgIndex) + 1;
            messageList.addAll(messageBOS);
        }
        return messageList;
    }

    /**
     * 素材转化为消息BO对象【新】
     *
     * @param materailInfoDO           素材对象
     * @param materialTrackRelationDTO 素材-trackId关系
     * @param context                  素材上下文
     * @return 消息BO对象
     */
    private List<WxMessageBO> convertMaterialToWxMessageBO(MaterailInfoDO materailInfoDO
            , MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        List<WxMessageBO> wxMessageList = new ArrayList<>();
        JSONArray jsonArray = JSONObject.parseObject(materailInfoDO.getContent())
                .getJSONArray(MaterialJsonKeyConstant.messageList);
        for (int i = 0; i < jsonArray.size(); i++) {
            materialTrackRelationDTO.setMsgIndexId(i);
            // 处理器获取
            String contentType = jsonArray.getJSONObject(i).getString(MaterialJsonKeyConstant.messageType);
            AbstractMaterialConverter<?> materialContentConverter = materialConvertFactory
                    .getMaterialContentConverter(MaterialContentTypeEnum.codeOf(contentType));
            if (materialContentConverter == null) {
                PlatformLogUtil.logFail("根据素材构建微信消息，没有找到对应的内容处理器", LogListUtil.newArrayList(materailInfoDO, jsonArray));
                continue;
            }
            materialTrackRelationDTO.setMaterialId(materailInfoDO.getId());
            // 格式处理
            List<WxMessageBO> wxMessageBOS = materialContentConverter.buildWxMessageBO(
                    jsonArray.getJSONObject(i).toJSONString(), materialTrackRelationDTO, context, sendMessage);
            if (CollectionUtils.isEmpty(wxMessageBOS)) {
                PlatformLogUtil.logFail("根据素材构建微信消息，内容没有获取到消息信息", LogListUtil.newArrayList(materailInfoDO, jsonArray));
                continue;
            }
            wxMessageList.addAll(wxMessageBOS);
        }
        return wxMessageList;
    }

    /**
     * 获取素材供给
     */
    public List<MaterialSupplyDTO> extractMaterialSupply(String materialContent) {
        if (!org.springframework.util.StringUtils.hasText(materialContent)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }

        List<MaterialSupplyDTO> supplyDTOList = new ArrayList<>();
        JSONObject jsonContent = JSONObject.parseObject(materialContent);
        if (MapUtils.isEmpty(jsonContent) || !jsonContent.containsKey(MaterialJsonKeyConstant.messageList)) {
            return supplyDTOList;
        }

        JSONArray jsonArray = jsonContent.getJSONArray(MaterialJsonKeyConstant.messageList);
        for (int i = 0; i < jsonArray.size(); i++) {
            String contentType = jsonArray.getJSONObject(i).getString(MaterialJsonKeyConstant.messageType);
            AbstractMaterialConverter<?> materialContentConverter = materialConvertFactory.getMaterialContentConverter(MaterialContentTypeEnum.codeOf(contentType));
            if (Objects.isNull(materialContentConverter)) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
            }
            supplyDTOList.addAll(materialContentConverter.getMaterialSupplyList(jsonArray.getJSONObject(i).toJSONString()));
        }
        return supplyDTOList;
    }

    @Override
    public SubscribeMsgTemplateDTO buildSubscribeMsg(Long materialId, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        if (!NumberUtils.validLong(materialId) || Objects.isNull(materialTrackRelationDTO) || Objects.isNull(context)) {
            PlatformLogUtil.logFail("构建小程序订阅消息对象失败，参数非法", LogListUtil.newArrayList(materialId, materialTrackRelationDTO, context));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        // 根据素材DO查询模版信息
        List<SubscribeMsgTemplateDTO> subscribeMsgTemplateDTOS = subscribeMsgTemplateService.list(Collections.singletonList(materialId));
        if (CollectionUtils.isEmpty(subscribeMsgTemplateDTOS)) {
            PlatformLogUtil.logFail("构建小程序订阅消息对象失败，模版信息不存在", LogListUtil.newArrayList(materialId));
            throw new TripscrmException(TripSCRMErrorCode.SUBSCRIBE_MSG_TEMPLATE_IS_NOT_EXIST);
        }
        SubscribeMsgTemplateDTO templateDTO = subscribeMsgTemplateDTOS.get(0);
        // 处理模版参数内容
        processTemplateContent(templateDTO, context);
        // 埋点
        templateBuildPoint(templateDTO, materialTrackRelationDTO);
        // 返回结果
        return templateDTO;
    }

    /**
     * 处理模版DTO
     *
     * @param templateDTO 订阅消息模板
     * @param context     素材上下文
     */
    private void processTemplateContent(SubscribeMsgTemplateDTO templateDTO, MaterialContentConvertContext context) {
        if (Objects.isNull(templateDTO) || Objects.isNull(context)) {
            PlatformLogUtil.logFail("埋点、参数内容变量替换失败，参数非法", LogListUtil.newArrayList(templateDTO, context));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }
        for (SubscribeMsgTemplateDTO.TemplateParam templateParam : templateDTO.getParamList()) {
            if (Objects.isNull(templateParam) || StringUtils.isBlank(templateParam.getParamContent())) {
                continue;
            }
            MaterialSupplyTypeEnum materialSupplyTypeEnum = MaterialSupplyTypeEnum.getSupplyType(templateParam.getParamType());
            if (Objects.isNull(materialSupplyTypeEnum)) {
                continue;
            }
            templateParam.setParamContent(materialVariableInfoService.replaceVariable(templateParam.getParamContent(), context.getExtraInfo()));
            AbstractMaterialConverter<?> materialContentConverter = materialConvertFactory.getMaterialContentConverter(MaterialContentTypeEnum.SUBSCRIBE_MSG);
            if (Objects.isNull(materialContentConverter)) {
                continue;
            }
            templateParam.setParamContent(materialContentConverter.buildSubscribeMsgTemplateContent(templateParam.getParamContent(), templateParam.getParamType()));
        }

    }

    /**
     * 订阅消息模板埋点逻辑处理
     *
     * @param templateDTO              订阅消息模板数据传输对象（非空）
     * @param materialTrackRelationDTO 素材与埋点关系数据传输对象（非空）
     */
    private void templateBuildPoint(SubscribeMsgTemplateDTO templateDTO, MaterialTrackRelationDTO materialTrackRelationDTO) {
        // 参数校验 → 非空检查
        if (Objects.isNull(templateDTO) || Objects.isNull(materialTrackRelationDTO)) {
            PlatformLogUtil.logFail("埋点失败，参数非法", LogListUtil.newArrayList(templateDTO, materialTrackRelationDTO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        // 处理跳转链接埋点 → 更新 templateDTO.getPageUrl()
        processPageUrl(templateDTO, materialTrackRelationDTO);

        // 处理页面参数埋点 → 更新 templateDTO.getParamList() 中的参数内容
        processTemplateParams(templateDTO, materialTrackRelationDTO);
    }

    /**
     * 处理跳转链接埋点逻辑
     *
     * @param templateDTO              订阅消息模板数据传输对象
     * @param materialTrackRelationDTO 素材与埋点关系数据传输对象
     */
    private void processPageUrl(SubscribeMsgTemplateDTO templateDTO, MaterialTrackRelationDTO materialTrackRelationDTO) {
        materialTrackRelationDTO.setMsgParagraphId(1);
        String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
        MaterialLinkConvertRequest request = buildConvertRequest(templateDTO, materialTrackRelationDTO, scrmTrackId, templateDTO.getPageUrl());
        templateDTO.setPageUrl(materialLinkService.convertLink(request));
    }

    /**
     * 构建通用的链接转换请求
     *
     * @param templateDTO              订阅消息模板数据传输对象
     * @param materialTrackRelationDTO 素材与埋点关系数据传输对象
     * @param scrmTrackId              埋点ID（非空）
     * @return 构建完成的链接转换请求对象
     */
    private MaterialLinkConvertRequest buildConvertRequest(SubscribeMsgTemplateDTO templateDTO, MaterialTrackRelationDTO materialTrackRelationDTO, String scrmTrackId, String originUrl) {
        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(originUrl);
        request.setScrmTrackId(scrmTrackId);
        MiniProgramPlatformEnum platformType = MiniProgramPlatformEnum.valueOf(templateDTO.getPlatformType());
        switch (platformType) {
            case WEIXIN:
                request.setTargetLinkType(LinkTypeEnum.MINI_PROGRAM_LIKE);
                break;
            case ALIPAY:
                request.setTargetLinkType(LinkTypeEnum.ALIPAY_LINK);
                break;
            default:
                PlatformLogUtil.logFail("构建通用的链接转换请求失败，不支持的平台类型", LogListUtil.newArrayList(platformType));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        request.setMaterialId(materialTrackRelationDTO.getMaterialId());
        request.setConvertShortLink(false);
        request.setUseInWechat(Objects.equals(MiniProgramPlatformEnum.valueOf(templateDTO.getPlatformType()), MiniProgramPlatformEnum.WEIXIN));
        request.setSceneType(materialTrackRelationDTO.getSceneType());
        return request;
    }

    /**
     * 处理订阅消息模板的参数列表
     *
     * @param templateDTO              订阅消息模板数据传输对象
     * @param materialTrackRelationDTO 素材与埋点关系数据传输对象
     */
    private void processTemplateParams(SubscribeMsgTemplateDTO templateDTO, MaterialTrackRelationDTO materialTrackRelationDTO) {
        List<SubscribeMsgTemplateDTO.TemplateParam> paramList = templateDTO.getParamList();
        if (CollectionUtils.isEmpty(paramList)) return;

        for (int i = 2; i <= paramList.size() + 1; i++) {
            SubscribeMsgTemplateDTO.TemplateParam templateParam = paramList.get(i - 2);
            if (isInvalidParam(templateParam)) continue;

            MaterialSupplyTypeEnum supplyType = MaterialSupplyTypeEnum.getSupplyType(templateParam.getParamType());
            if (!isSupportedSupplyType(supplyType)) continue;

            processSingleParam(templateDTO, materialTrackRelationDTO, templateParam, i, templateParam.getParamContent());
        }
    }

    /**
     * 判断参数是否无效（空对象或空内容）
     *
     * @param templateParam 订阅消息模板参数对象
     * @return 是否无效
     */
    private boolean isInvalidParam(SubscribeMsgTemplateDTO.TemplateParam templateParam) {
        return Objects.isNull(templateParam) || StringUtils.isBlank(templateParam.getParamContent());
    }

    /**
     * 判断是否支持的供给类型
     *
     * @param supplyType 供给类型枚举
     * @return 是否支持
     */
    private boolean isSupportedSupplyType(MaterialSupplyTypeEnum supplyType) {
        return Lists.newArrayList(
                MaterialSupplyTypeEnum.NON_PACKAGE_HOTEL_LISTING,
                MaterialSupplyTypeEnum.DMESTIC_FLIGHT_LISTING,
                MaterialSupplyTypeEnum.INTERNATIONAL_FLIGHT_LISTING
        ).contains(supplyType);
    }

    /**
     * 处理单个模板参数的埋点逻辑
     *
     * @param templateDTO              订阅消息模板数据传输对象
     * @param materialTrackRelationDTO 素材与埋点关系数据传输对象
     * @param templateParam            单个模板参数对象
     * @param index                    参数位置索引
     */
    private void processSingleParam(SubscribeMsgTemplateDTO templateDTO, MaterialTrackRelationDTO materialTrackRelationDTO, SubscribeMsgTemplateDTO.TemplateParam templateParam, int index, String originUrl) {
        materialTrackRelationDTO.setMsgParagraphId(index);
        String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
        MaterialLinkConvertRequest request = buildConvertRequest(templateDTO, materialTrackRelationDTO, scrmTrackId, originUrl);
        templateParam.setParamContent(materialLinkService.convertLink(request));
    }


    /**
     * 素材转化为消息BO对象【旧】
     *
     * @param materailInfoDO           素材对象
     * @param materialTrackRelationDTO 素材-trackId关系
     * @param context                  素材上下文
     * @param taskType                 任务类型
     * @return 消息BO对象
     */
    private List<MessageBO> convertMaterialToMessageBO(MaterailInfoDO materailInfoDO, MaterialTrackRelationDTO materialTrackRelationDTO,
                                                       MaterialContentConvertContext context, TaskType taskType) {
        ArrayList<MessageBO> messageBOList = new ArrayList<>();
        if (materailInfoDO == null || StringUtils.isBlank(materailInfoDO.getContent())) {
            PlatformLogUtil.logFail("素材转化为消息BO对象【旧】，参数非法", LogListUtil.newArrayList(materailInfoDO));
            return messageBOList;
        }
        // JSON中的消息列表
        JSONObject materialContentJson = JSONObject.parseObject(materailInfoDO.getContent());
        JSONArray messageJsonArray = Optional.ofNullable(materialContentJson.getJSONArray(MaterialJsonKeyConstant.messageList)).orElse(new JSONArray());
        int msgIndex = 0;
        for (int i = 0; i < messageJsonArray.size(); i++) {
            List<MessageBO> messageList = messageConverterFactory.convertMessageBO(messageJsonArray.getJSONObject(i), materialTrackRelationDTO, context, taskType, getMessageType(messageJsonArray.getJSONObject(i), MaterialJsonKeyConstant.messageType), msgIndex);
            if (CollectionUtils.isNotEmpty(messageList)) {
                messageBOList.addAll(messageList);
                msgIndex = messageList.stream().map(MessageBO::getMsgNum).max(Integer::compareTo).orElse(msgIndex) + 1;
            }
        }
        return messageBOList;
    }

    /**
     * 素材转化为消息BO对象【旧】
     *
     * @param materailInfoDO           素材对象
     * @param materialTrackRelationDTO 素材-trackId关系
     * @param context                  素材上下文
     * @param taskType                 任务类型
     * @return 消息BO对象
     */
    private List<WxMessageBO> convertMaterialToWxMessageBO(MaterailInfoDO materailInfoDO, MaterialTrackRelationDTO materialTrackRelationDTO,
                                                           MaterialContentConvertContext context, TaskType taskType) {
        ArrayList<WxMessageBO> messageBOList = new ArrayList<>();
        if (materailInfoDO == null || StringUtils.isBlank(materailInfoDO.getContent())) {
            PlatformLogUtil.logFail("素材转化为消息BO对象【旧】，参数非法", LogListUtil.newArrayList(materailInfoDO));
            return messageBOList;
        }
        // JSON中的消息列表
        JSONObject materialContentJson = JSONObject.parseObject(materailInfoDO.getContent());
        JSONArray messageJsonArray = Optional.ofNullable(materialContentJson.getJSONArray(MaterialJsonKeyConstant.messageList)).orElse(new JSONArray());
        for (int i = 0; i < messageJsonArray.size(); i++) {
            List<WxMessageBO> messageList = messageConverterFactory.convertWxMessageBO(messageJsonArray.getJSONObject(i), materialTrackRelationDTO, context, taskType, getMessageType(messageJsonArray.getJSONObject(i), MaterialJsonKeyConstant.messageType), i);
            if (CollectionUtils.isNotEmpty(messageList)) {
                messageBOList.addAll(messageList);
            }
        }
        // 朋友圈模版字段取数和其他不一致
        if (!materialContentJson.containsKey(MaterialJsonKeyConstant.momentContent)) {
            return messageBOList;
        }
        JSONObject momentContentJson = materialContentJson.getJSONObject(MaterialJsonKeyConstant.momentContent);
        List<WxMessageBO> momentImageVideoList = messageConverterFactory.convertWxMessageBO(momentContentJson, materialTrackRelationDTO, context, taskType, getMessageType(momentContentJson, MaterialJsonKeyConstant.contentType), null);
        if (CollectionUtils.isNotEmpty(momentImageVideoList)) {
            messageBOList.addAll(momentImageVideoList);
        }
        return messageBOList;
    }

    /**
     * 从JSON中获取消息类型
     *
     * @param jsonObject jsonObject
     * @return 消息类型
     */
    private MessageTypeEnum getMessageType(JSONObject jsonObject, String key) {
        String messageType = jsonObject.getString(key);
        if (StringUtils.isBlank(messageType)) {
            PlatformLogUtil.logFail("从JSON中获取消息类型，消息类型为空", LogListUtil.newArrayList(jsonObject, key));
            return null;
        }
        MaterialTypeMappingEnum materialTypeMappingEnum = MaterialTypeMappingEnum.codeOf(messageType);
        if (materialTypeMappingEnum == null) {
            PlatformLogUtil.logFail("从JSON中获取消息类型，素材类型mapping失败", LogListUtil.newArrayList(jsonObject, key));
            return null;
        }
        return materialTypeMappingEnum.getMessageTypeEnum();
    }


    /**
     * 标识素材的使用状态
     *
     * @param materialId 素材id
     */
    private void markMaterialUsed(Long materialId) {
        if (!NumberUtils.validLong(materialId)) {
            return;
        }
        ldbTairManager.put(MaterialServiceImpl.MATERIAL_PRE, 1, 60);
        MaterailInfoDO materailInfoDO = new MaterailInfoDO();
        materailInfoDO.setId(materialId);
        materailInfoDO.setUsed((byte) 1);
        materailInfoMapper.updateByPrimaryKey(materailInfoDO);
    }
}
