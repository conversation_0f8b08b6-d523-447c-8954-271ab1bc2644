package com.alibaba.tripscrm.service.service.impl;

import com.alibaba.tripscrm.dal.mapper.lindorm.OneIdMappingDAO;
import com.alibaba.tripscrm.dal.model.lindorm.OneIdMappingDO;
import com.alibaba.tripscrm.service.model.domain.bo.OneIdMappingBO;
import com.alibaba.tripscrm.service.service.OneIdMappingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OneIdMappingServiceImpl implements OneIdMappingService {

    @Resource
    private OneIdMappingDAO oneIdMappingDAO;

    @Override
    public int batchUpsert(List<OneIdMappingBO> oneIdMappingBOList) {
        List<OneIdMappingDO> oneIdMappingDOList = oneIdMappingBOList.stream().map(OneIdMappingBO::convertOneIdMappingDO).collect(Collectors.toList());
        return oneIdMappingDAO.batchUpsert(oneIdMappingDOList);
    }

    @Override
    public int delete(OneIdMappingBO oneIdMappingBO) {
        return oneIdMappingDAO.delete(oneIdMappingBO.convertOneIdMappingDO());
    }

    @Override
    public int batchDelete(List<OneIdMappingBO> oneIdMappingBOList) {
        AtomicInteger cnt = new AtomicInteger();
        oneIdMappingBOList.forEach(oneIdMappingBO -> cnt.addAndGet(oneIdMappingDAO.delete(oneIdMappingBO.convertOneIdMappingDO())));
        return cnt.get();
    }
}
