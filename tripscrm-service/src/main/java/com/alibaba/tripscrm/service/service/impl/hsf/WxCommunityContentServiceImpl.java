package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.activity.ActivityInfoQuery;
import com.alibaba.tripscrm.domain.*;
import com.alibaba.tripscrm.domain.request.TripSCRMActivityRequest;
import com.alibaba.tripscrm.domain.request.WxCommunityContentRequest;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.manager.opensearch.WxContentOpenSearchManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.WxCommunityContentQuery;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSelfTestDTO;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.service.activity.ActivitySopService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.wechat.WxCommunityContentService;
import com.fliggy.travelsummary.domain.SummarySearchItem;
import com.fliggy.travelsummary.domain.query.TravelSummaryQuery;
import com.fliggy.travelsummary.service.SummarySearchService;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.mortbay.util.ajax.JSON;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import com.google.common.collect.Lists;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 微信内容社区服务
 * @author: huiyi
 * @create: 2024-04-18 15:37
 **/
@Slf4j
@HSFProvider(serviceInterface = WxCommunityContentService.class)
public class WxCommunityContentServiceImpl implements WxCommunityContentService {
    @Resource
    private WxContentOpenSearchManager wxContentOpenSearchManager;
    @Resource
    private SummarySearchService summarySearchService;
    @Resource
    private MaterialService materialService;
    @Resource
    private ActivitySopService activitySopService;
    @Resource
    private ActivityInfoService activityInfoService;


    @Override
    @ServiceLog("微信小程序社区内容-自测预览")
    public TripSCRMResult<Boolean> preview(WxCommunityContentRequest request) {

        Long cppContentId = request.getCppContentId();
        WxCommunityContentQuery query = new WxCommunityContentQuery();
        query.setCppContentId(cppContentId);
        WxCommunityContentDTO communityContentDTO = wxContentOpenSearchManager.query(query);
        Assert.notNull(communityContentDTO, "内容失效不可用");

        MaterialSelfTestDTO materialSelfTestDTO = new MaterialSelfTestDTO();
        materialSelfTestDTO.setUid(request.getUserId());

        String materialTemplate = "{\"messageList\":[{\"messageType\":\"text\",\"messageParagraphList\":[{\"textScript\":\"%s\",\"shortChainIntroduction\":\"%s\",\"pageType\":\"fliggyMiniprogram\",\"supplyType\":\"miniprogram\",\"pagePath\":\"%s\"}],\"messageLength\":22,\"messageRowLength\":1}]}";
        String contentDetailUrl = String.format(org.apache.commons.collections4.MapUtils.getString(SwitchConfig.COMMUNITY_CONTENT_DETAIL_URL_MAP, EnvUtils.getEnvironment(), org.apache.commons.collections4.MapUtils.getString(SwitchConfig.COMMUNITY_CONTENT_DETAIL_URL_MAP, "online")), cppContentId);
        String content = String.format(materialTemplate, communityContentDTO.getTitle(), communityContentDTO.getTitle(), contentDetailUrl);

        materialSelfTestDTO.setContent(content);
        Boolean res = materialService.selfTest(materialSelfTestDTO);
        return TripSCRMResult.success(res);
    }

    @Override
    @ServiceLog("微信小程序社区内容-分页查询")
    public TripSCRMResult<PageInfoDTO<WxCommunityContentDTO>> listByPage(WxCommunityContentRequest request) {
        PageInfoDTO<WxCommunityContentDTO> pageInfoDTO = new PageInfoDTO<>();

        WxCommunityContentQuery query = new WxCommunityContentQuery();
        BeanUtils.copyProperties(request, query);
        PageInfo<WxCommunityContentDTO> pageInfo = wxContentOpenSearchManager.listByPage(query);
        if (Objects.isNull(pageInfo)) {
            return TripSCRMResult.success(pageInfoDTO);
        }
        pageInfoDTO.setTotal(pageInfo.getTotal());
        pageInfoDTO.setPageNum(pageInfo.getPageNum());
        pageInfoDTO.setPageSize(pageInfo.getPageSize());
        pageInfoDTO.setList(pageInfo.getList());

        return TripSCRMResult.success(pageInfoDTO);
    }

    @Override
    @ServiceLog("微信小程序社区内容-查找指定")
    public TripSCRMResult<WxCommunityContentDTO> find(WxCommunityContentRequest request) {
        // 内容召回
        WxCommunityContentQuery query = new WxCommunityContentQuery();
        query.setCppContentId(request.getCppContentId());
        WxCommunityContentDTO communityContentDTO = wxContentOpenSearchManager.query(query);

        // 微信文章补全
        Long wxArticlePkId = communityContentDTO.getWxArticlePkId();
        if (Objects.nonNull(communityContentDTO) && Objects.nonNull(wxArticlePkId) && wxArticlePkId > 0L) {
            List<Long> wxArticlePkIds = Lists.newArrayList(wxArticlePkId);
            List<WxArticleDTO> wxArticleDTOS = wxContentOpenSearchManager.listWxArticleList(wxArticlePkIds);
            if (CollectionUtils.isNotEmpty(wxArticleDTOS)) {
                WxArticleDTO articleDTO = wxArticleDTOS.get(0);

                communityContentDTO.setWxArticleAuthor(articleDTO.getAuthor());
                communityContentDTO.setWxArticleTitle(articleDTO.getTitle());
                communityContentDTO.setWxArticleUrl(articleDTO.getUrl());
            }
        }

        // 商品信息补全
        List<WxContentItemDTO> relatedItems = communityContentDTO.getRelatedItems();
        if (CollectionUtils.isNotEmpty(relatedItems)) {
            Set<String> itemIdSet = relatedItems.stream().filter(e -> Objects.nonNull(e) && StringUtils.isNotBlank(e.getItemId()))
                    .filter(e -> Arrays.asList("hotel", "poi", "bnbroom", "vacation").contains(e.getSupplType()))
                    .map(e -> String.format("%s:%s", e.getSupplType(), e.getItemId()))
                    .collect(Collectors.toSet());

            TravelSummaryQuery travelSummaryQuery = new TravelSummaryQuery();
            travelSummaryQuery.setIds(itemIdSet);
            travelSummaryQuery.setAppName("tripscrm");
            travelSummaryQuery.setTtid(request.getTtid());
            if (Objects.nonNull(request.getTbUserId())) {
                HashMap<String, Object> extraParams = Maps.newHashMap();
                extraParams.put("userId", request.getTbUserId());
                travelSummaryQuery.setExtraParams(extraParams);
            }

            Map<String, SummarySearchItem> summarySearchItemMap = summarySearchService.searchItemMap(travelSummaryQuery, null);

            for (WxContentItemDTO relatedItem : relatedItems) {
                String itemId = String.format("%s:%s", relatedItem.getSupplType(), relatedItem.getItemId());
                SummarySearchItem searchItem = summarySearchItemMap.get(itemId);
                if (Objects.nonNull(searchItem)) {
                    relatedItem.setH5Url(searchItem.getH5Url());
                    relatedItem.setTitle(searchItem.getTitle());
                    relatedItem.setPicUrl(searchItem.getPicUrl());
                    relatedItem.setDiscountPriceText(searchItem.getDiscountPriceText());
                    relatedItem.setShortTitle(searchItem.getShortTitle());
                    relatedItem.setStatus(searchItem.getStatus());
                    if (MapUtils.isNotEmpty(searchItem.getAtmosphereMap())) {
                        relatedItem.setAtmosphereMap(JSON.toString(searchItem.getAtmosphereMap()));
                    }
                    if (CollectionUtils.isNotEmpty(searchItem.getLights())) {
                        relatedItem.setLights(JSON.toString(searchItem.getLights()));
                    }
                }
            }
        }

        return TripSCRMResult.success(communityContentDTO);
    }

    @Override
    @ServiceLog("微信小程序社区内容-删除")
    public TripSCRMResult<Boolean> delete(WxCommunityContentRequest request) {
        WxCommunityContentQuery query = new WxCommunityContentQuery();
        query.setCppContentId(request.getCppContentId());
        return TripSCRMResult.success(wxContentOpenSearchManager.delete(query));
    }

    @Override
    @ServiceLog("微信小程序社区内容-按关键词查询")
    public TripSCRMResult<PageInfoDTO<WxCommunityContentDTO>> listByKeyWord(WxCommunityContentRequest request) {
        PageInfoDTO<WxCommunityContentDTO> pageInfoDTO = new PageInfoDTO<>();

        WxCommunityContentQuery query = new WxCommunityContentQuery();
        BeanUtils.copyProperties(request, query);
        PageInfo<WxCommunityContentDTO> pageInfo = wxContentOpenSearchManager.listByKeyWord(query);
        if (Objects.isNull(pageInfo)) {
            return TripSCRMResult.success(pageInfoDTO);
        }
        pageInfoDTO.setTotal(pageInfo.getTotal());
        pageInfoDTO.setPageNum(pageInfo.getPageNum());
        pageInfoDTO.setPageSize(pageInfo.getPageSize());

        List<WxCommunityContentDTO> wxContentDTOList = pageInfo.getList();
        pageInfoDTO.setList(wxContentDTOList);

        // 微信文章补全
//        if (CollectionUtils.isNotEmpty(wxContentDTOList)) {
//            List<Long> wxArticlePkIds = wxContentDTOList.stream().map(WxCommunityContentDTO::getWxArticlePkId).filter(Objects::nonNull).filter(e -> e > 0L).distinct().collect(Collectors.toList());
//            List<WxArticleDTO> wxArticleDTOS = wxContentOpenSearchManager.listWxArticleList(wxArticlePkIds);
//            if (CollectionUtils.isNotEmpty(wxArticleDTOS)) {
//                Map<Long, WxArticleDTO> wxArticleDTOMap = wxArticleDTOS.stream().collect(Collectors.toMap(WxArticleDTO::getId, e -> e));
//                wxContentDTOList.stream().filter(e -> Objects.nonNull(e.getWxArticlePkId()) && e.getWxArticlePkId() > 0L).forEach(e -> {
//                    WxArticleDTO wxArticleDTO = wxArticleDTOMap.get(e.getWxArticlePkId());
//                    if (Objects.nonNull(wxArticleDTO)) {
//                        e.setWxArticleAuthor(wxArticleDTO.getAuthor());
//                        e.setWxArticleTitle(wxArticleDTO.getTitle());
//                        e.setWxArticleUrl(wxArticleDTO.getUrl());
//                    }
//                });
//            }
//        }

        return TripSCRMResult.success(pageInfoDTO);
    }

    @Override
    @ServiceLog("微信小程序社区内容-查询标签")
    public TripSCRMResult<List<WxCommunityTagDTO>> listTagByIds(List<String> ids) {
        List<WxCommunityTagDTO> wxCommunityTagDTOS = wxContentOpenSearchManager.listTagByIds(ids);
        return TripSCRMResult.success(wxCommunityTagDTOS);
    }

    @Override
    @ServiceLog("微信小程序社区内容-查询活动")
    public TripSCRMResult<PageInfoDTO<TripSCRMActivityVO>> listQrCodeInfo(TripSCRMActivityRequest request) {
        ActivityInfoQuery query = new ActivityInfoQuery();
        BeanUtils.copyProperties(request, query);
        query.setId(request.getActivityId());
        query.setName(request.getActivityName());
        query.setManager(request.getMemberIds());
        query.setSpaceId(request.getSpaceId());
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());

        PageInfo<ActivityInfoDO> list = activityInfoService.list(query);
        if (Objects.isNull(list) || CollectionUtils.isEmpty(list.getList())) {
            return TripSCRMResult.success(new PageInfoDTO());
        }

        PageInfo<ActivitySopVO> pageInfo = PageUtils.getPageInfo(list, activitySopService::convert);
        if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
            return TripSCRMResult.success(new PageInfoDTO());
        }

        List<ActivitySopVO> activitySopVOS = pageInfo.getList();

        PageInfoDTO pageInfoDTO = new PageInfoDTO();
        pageInfoDTO.setPageNum(pageInfo.getPageNum());
        pageInfoDTO.setPageSize(pageInfo.getPageSize());
        pageInfoDTO.setTotal(pageInfo.getTotal());
        if (CollectionUtils.isEmpty(activitySopVOS)) {
            pageInfoDTO.setList(null);
            return TripSCRMResult.success(pageInfoDTO);
        }

        List<TripSCRMActivityVO> tripSCRMActivityVOList = activitySopVOS.stream().map(e -> {
            TripSCRMActivityVO activityVO = new TripSCRMActivityVO();
            BeanUtils.copyProperties(e, activityVO);
            activityVO.setConfig(e.getConfig());
            return activityVO;
        }).collect(Collectors.toList());

        pageInfoDTO.setList(tripSCRMActivityVOList);
        return TripSCRMResult.success(pageInfoDTO);
    }
}
