package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fusionchat.AccountConfigService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.UpdateWechatUserConfigRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.UpdateWechatUserConfigResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.List;

/**
 * 更新企微成员配置 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class UpdateWechatUserConfigProcessor implements WsEventProcessor {
    @Resource
    private AccountConfigService accountConfigService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private WechatUserService wechatUserService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.UPDATE_WECHAT_USER_CONFIG;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        User account = webSocketFactory.getWsContextInfo(session).getAccount();
        UpdateWechatUserConfigRequest request = wsEvent.getData().toJavaObject(UpdateWechatUserConfigRequest.class);
        if (wsEvent.getUserId() == null) {
            throw new TripscrmException("缺少必要的参数 userId");
        }
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        if (!wechatUserService.checkHasLock(wsEvent.getUserId(), wsContextInfo.getAccount().getUserId(), wsContextInfo.getSpaceId())) {
            throw new TripscrmException("未锁定当前企微账号，无法执行该操作");
        }
        accountConfigService.saveFusionChatConfig(account.getUserId(), wsEvent.getUserId(), request.getTop());
        // 推送结果
        UpdateWechatUserConfigResponse response = new UpdateWechatUserConfigResponse();
        response.setTopNo(request.getTop() ? 1 : 0);
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));

        Long spaceId = wsContextInfo.getSpaceId();
        // 寻找当前企微号有关系的account列表，推送到websocket
        List<String> accountIds = wechatUserService.listAccountIdWithPermission(wsEvent.getUserId(), spaceId);
        webSocketFactory.pushMessageByDistributedWithAccount(wsEvent, accountIds);
    }
}
