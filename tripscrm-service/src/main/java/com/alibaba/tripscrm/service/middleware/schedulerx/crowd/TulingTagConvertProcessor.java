package com.alibaba.tripscrm.service.middleware.schedulerx.crowd;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.MapJobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.lindorm.UserTagRecordDO;
import com.alibaba.tripscrm.dal.repository.UserTagRecordRepository;
import com.alibaba.tripscrm.service.enums.tag.TagOperationEnum;
import com.alibaba.tripscrm.service.enums.tag.UserTagRecordStatusEnum;
import com.alibaba.tripscrm.service.enums.task.CrowdTagConvertExecuteTypeEnum;
import com.alibaba.tripscrm.service.enums.task.CrowdTagConvertTaskStatusEnum;
import com.alibaba.tripscrm.service.manager.middleware.OssClientManager;
import com.alibaba.tripscrm.service.manager.second.CrowdInfoManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.query.CrowdTagConvertTaskQuery;
import com.alibaba.tripscrm.service.model.dto.crowd.CrowdTagConvertTaskDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.crowd.CrowdTagConvertTaskService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.api.service.alipay.util.AlipayTagService;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import com.alibaba.tripzoo.proxy.request.alipay.AlipayTagRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.crowd.service.TripCrowdCommonService;
import com.fliggy.crowd.service.domain.CrowdDTO;
import com.fliggy.pokemon.common.enums.TimeUnitEnum;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.trip.platform.protocol.TripCommonPlatformResult;
import com.taobao.uic.common.domain.BasePaymentAccountDO;
import com.taobao.uic.common.service.userinfo.client.UicPaymentAccountReadServiceClient;
import com.taobao.uic.common.util.AlipayCardNOUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.ArrayList;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig.*;

/**
 * 图灵标签转换处理器 - MapReduce 模式
 * <p>
 * 主要功能：
 * 1. 从数据库中查询满足条件的任务（源数据平台是诸葛，目标平台是图灵）
 * 2. 根据诸葛id查询诸葛人群
 * 3. 先查缓存，用户最近是否打过这个标签，未命中再查lindrom记录表
 * 4. 需要打标的用户满10000就放到一个分片中，单个分片存一个缓存
 * 5. 分片子任务打标（MapReduce 分布式处理）
 * <p>
 * MapReduce 模式：
 * - Map 阶段：将用户数据分片分发到不同的处理节点
 * - Reduce 阶段：汇总各个分片的处理结果
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TulingTagConvertProcessor extends MapJobProcessor {


    private final CrowdTagConvertTaskService crowdTagConvertTaskService;
    private final CrowdInfoManager crowdInfoManager;
    private final LdbTairManager ldbTairManager;
    private final UserTagRecordRepository userTagRecordRepository;
    private final UicPaymentAccountReadServiceClient uicPaymentAccountReadServiceClient;
    private final AlipayTagService alipayTagService;
    private final OssClientManager ossClientManager;
    private final TripCrowdCommonService tripCrowdCommonService;

    // 缓存相关常量
    private static final String TULING_TAG_SHARD_DATA_CACHE = "tuling_tag_shard_data_";
    private static final String USER_TAG_CACHE_PREFIX = "zhuge_to_tuling_user_tag_";
    private static final String TASK_RESULT_CACHE_PREFIX = "task_result_shard_";
    private static final String TASK_TOTAL_SHARDS = "task_total_shards_";
    private static final String TASK_STATUS_RETRY_PREFIX = "tuling_tag_retry_task_";
    private static final String SCRM_TAO_TO_ALIPAY_ID_CACHE_PREFIX = "scrm_tao_map_alipay_";


    // 缓存过期时间（秒）
    private static final int CACHE_EXPIRE_TIME = 3600 * 24 * 3;
    private static final int TAG_CACHE_EXPIRE_TIME = 3600 * 24 * 7;
    private static final int SCRM_TAO_TO_ALIPAY_ID_CACHE_EXPIRE_TIME = 3600 * 24 * 30;

    // MapReduce 任务名称常量
    private static final String MAP_ADD_TASK_NAME = "TULING_ADD_TAG_MAP_TASK";
    private static final String MAP_REMOVE_TASK_NAME = "TULING_REMOVE_TAG_MAP_TASK";

    @Override
    public ProcessResult process(JobContext context) throws Exception {

        try {
            if (isRootTask(context)) {
                // 根任务：执行 Map 阶段
                return executeMapPhase(context);
            } else if (MAP_ADD_TASK_NAME.equals(context.getTaskName())) {
                // 加标
                return executeMapTask(context);
            } else if (MAP_REMOVE_TASK_NAME.equals(context.getTaskName())) {
                // 去标
                return executeRemoveMapTask(context);
            }

            return new ProcessResult(true);

        } catch (Exception e) {
            PlatformLogUtil.logException("图灵标签转换任务执行失败", e.getMessage(), e, LogListUtil.newArrayList(context));
            return new ProcessResult(false, e.getMessage());
        }
    }

    /**
     * Map 阶段：分发任务和数据分片
     */
    private ProcessResult executeMapPhase(JobContext context) {
        PlatformLogUtil.logInfo("开始执行 Map 阶段", LogListUtil.newArrayList(context.getJobId()));

        // 步骤1: 从数据库查询满足条件的任务
        CrowdTagConvertTaskDTO task = queryEligibleTasks();
        if (Objects.isNull(task)) {
            PlatformLogUtil.logInfo("没有找到需要处理的任务", LogListUtil.newArrayList());
            return new ProcessResult(true);
        }
        try {
            AtomicInteger totalShards = new AtomicInteger(0);
            //加标分片 - 如果抛出异常则表示失败
            processTaskInMapPhase(task, totalShards);

            // 加标分片成功，继续执行去标分片
            PlatformLogUtil.logInfo("加标分片成功，开始执行去标分片", LogListUtil.newArrayList(task));
            processRemoveTagInBatches(task, totalShards);
        } catch (TripscrmException e) {
            PlatformLogUtil.logException("加标分片失败，任务终止", e.getErrorMsg(), e, LogListUtil.newArrayList(task));
            // 加标分片失败，将任务状态设为失败，不执行去标分片
            updateTaskStatus(task.getId(), CrowdTagConvertTaskStatusEnum.FAILED, null, null);
            return new ProcessResult(false, "加标分片失败: " + e.getMessage());
        }
        return new ProcessResult(true);
    }

    /**
     * 加标分片任务
     */
    private void processTaskInMapPhase(CrowdTagConvertTaskDTO task, AtomicInteger totalShards) {
        PlatformLogUtil.logInfo("Map阶段开始处理任务", LogListUtil.newArrayList(task.getId(), task.getSourceCrowdId()));

        // 更新任务状态为处理中
        // 再查一遍诸葛人群数据，防止数据不一致
        TripCommonPlatformResult<CrowdDTO> crowdInfoResult = tripCrowdCommonService.getCrowdInfo(Long.valueOf(task.getSourceCrowdId()));
        if (crowdInfoResult == null || !crowdInfoResult.isSuccess() || crowdInfoResult.getData() == null) {
            throw new TripscrmException(TripSCRMErrorCode.ZHUGE_CROWD_NOT_EXIST_OR_NO_PERMISSION, "诸葛平台人群不存在或无权限访问");
        }
        CrowdDTO crowdDTO = crowdInfoResult.getData();

        if (!NumberUtils.biggerThanZero(crowdDTO.getCrowdAmount())) {
            throw new TripscrmException(TripSCRMErrorCode.ZHUGE_CROWD_NOT_EXIST_OR_NO_PERMISSION, "诸葛平台人群不存在或无权限访问");
        }
        // 校验人群是否在有效期内
        Date expiredDate = crowdDTO.getExpiredDate();
        if (new Date().after(expiredDate)) {
            throw new TripscrmException(TripSCRMErrorCode.CROWD_EXPIRED, "人群已过期，不允许转换");
        }
        if (!Objects.equals(task.getExecuteType(), CrowdTagConvertExecuteTypeEnum.SOURCE_CROWD_VALID_TIME)) {
            expiredDate = null;
        }
        // 更新任务状态为处理中
        updateTaskStatus(task.getId(), CrowdTagConvertTaskStatusEnum.PROCESSING, expiredDate, crowdDTO.getCrowdAmount());

        //流式处理诸葛人群文件数据，边读边分区
        processCrowdFileDataInBatches(task, totalShards);
    }

    /**
     * 流式处理诸葛人群文件数据，边读边分区
     */
    private void processCrowdFileDataInBatches(CrowdTagConvertTaskDTO task, AtomicInteger totalShards) {

        Long crowdId = Long.parseLong(task.getSourceCrowdId());

        // 获取诸葛人群文件链接列表
        List<String> fileUrls = crowdInfoManager.getCrowdFile(crowdId);
        if (CollectionUtils.isEmpty(fileUrls)) {
            PlatformLogUtil.logFail("诸葛人群文件链接为空", LogListUtil.newArrayList(task.getId(), task.getSourceCrowdId()));
            updateTaskStatus(task.getId(), CrowdTagConvertTaskStatusEnum.FAILED, null, null);
            return;
        }
        List<String> currentBatch = new ArrayList<>();
        // 逐个文件且分片
        for (String fileUrl : fileUrls) {

            readCrowdFileANDShardTask(fileUrl, task, currentBatch, totalShards);
        }
        // 处理最后一个不满分片大小的批次
        if (!CollectionUtils.isEmpty(currentBatch)) {
            totalShards.incrementAndGet();
            createShardTask(task, currentBatch, totalShards.get());
            PlatformLogUtil.logInfo("创建最后分片任务", LogListUtil.newArrayList(task.getId(), totalShards.get()));
        }

    }


    public void readCrowdFileANDShardTask(String fileName, CrowdTagConvertTaskDTO task, List<String> currentBatch, AtomicInteger totalShards) {
        if (!StringUtils.hasText(fileName)) {
            PlatformLogUtil.logInfo("读取圈人文件，文件地址为空");
            return;
        }
        BufferedReader reader = null;
        try {
            InputStream inputStream = ossClientManager.getZhugeOssContent(fileName);
            reader = new BufferedReader(new InputStreamReader(inputStream));
            int lineNum = 0;

            String userId;
            while (Objects.nonNull(userId = reader.readLine()) && lineNum < ZHUGE_TO_TULING_CROWD_MAX_SIZE) {
                lineNum++;
                userId = userId.trim();
                if (!StringUtils.hasText(userId)) {
                    // 跳过空行，继续处理下一行
                    continue;
                }
                // 检查用户是否需要打标
               if(TULING_TASK_RESET_SWITCH  || !hasUserTagAndUpsertRecord(userId, task)) {
                    currentBatch.add(userId);
                }
                // 当前批次达到分片大小时，创建分片任务
                if (currentBatch.size() >= ZHUGE_TO_TULING_SHARD_SIZE) {
                    totalShards.incrementAndGet();
                    createShardTask(task, currentBatch, totalShards.get());
                    currentBatch.clear();
                }
            }
            PlatformLogUtil.logInfo("读取圈人文件完成", fileName);
        } catch (Exception e) {
            PlatformLogUtil.logException("读取圈人文件出现异常", e.getMessage(), e, LogListUtil.newArrayList(fileName));
        } finally {
            if (Objects.nonNull(reader)) {
                try {
                    reader.close();
                } catch (IOException e) {
                    PlatformLogUtil.logException("读取圈人文件关闭数据流异常", e.getMessage(), e, LogListUtil.newArrayList(fileName));
                }
            }
        }
    }

    /**
     * 创建分片任务
     */
    private void createShardTask(CrowdTagConvertTaskDTO task, List<String> userShard, int shardIndex) {
        try {
            // 缓存分片数据
            String date = DateUtils.getSimpleDayDateString(new Date());
            String shardCacheKey = TULING_TAG_SHARD_DATA_CACHE + task.getId() + "_" + shardIndex + "_" + date;
            boolean put = ldbTairManager.put(shardCacheKey, JSONObject.toJSONString(userShard), CACHE_EXPIRE_TIME);
            if (!put) {
                PlatformLogUtil.logFail("缓存分片数据失败", LogListUtil.newArrayList(task.getId(), shardIndex, userShard.size()));
                ldbTairManager.put(shardCacheKey, JSONObject.toJSONString(userShard), CACHE_EXPIRE_TIME);
            }
            // 创建并分发Map子任务 totalShards在最后确定
            MapTaskData mapTaskData = new MapTaskData(task.getId(), shardIndex, -1, task.getSourceCrowdId());
            map(Collections.singletonList(mapTaskData), MAP_ADD_TASK_NAME);

        } catch (Exception e) {
            PlatformLogUtil.logException("创建分片任务失败", e.getMessage(), e,
                    LogListUtil.newArrayList(task.getId(), shardIndex, userShard.size()));
        }
    }


    /**
     * 执行 Map 子任务：处理单个分片
     */
    private ProcessResult executeMapTask(JobContext context) throws Exception {
        MapTaskData mapTaskData = (MapTaskData) context.getTask();
        Long taskId = mapTaskData.getTaskId();
        int shardIndex = mapTaskData.getShardIndex();

        PlatformLogUtil.logInfo("开始执行Map子任务", LogListUtil.newArrayList(taskId, shardIndex));

        try {
            // 从缓存获取任务信息和分片数据
            CrowdTagConvertTaskDTO task = crowdTagConvertTaskService.getTaskById(taskId);
            List<String> shard = getShardFromCache(taskId, shardIndex);

            if (task == null || CollectionUtils.isEmpty(shard)) {
                PlatformLogUtil.logFail("Map子任务获取缓存数据失败", LogListUtil.newArrayList(taskId, shardIndex));
                return new ProcessResult(false, "缓存数据获取失败");
            }

            // 执行分片打标
            int successCount = executeShardTagging(task, shard, shardIndex, false);

            // 缓存分片处理结果
            cacheShardResult(taskId, shardIndex, successCount, shard.size());

            PlatformLogUtil.logInfo("Map子任务处理完成", LogListUtil.newArrayList(taskId, shardIndex, successCount, shard.size()));

            // 每个分片完成后立即检查是否所有分片都完成了
            String shardTotalKey = TASK_TOTAL_SHARDS + task.getId() + "_" + task.getTargetPlatform().getCode() + "_" + DateUtils.getSimpleDayDateString(new Date());
            Object totalShards = ldbTairManager.get(shardTotalKey);
            if (Objects.nonNull(totalShards)) {
                checkAndCompleteTaskIfAllShardsFinished(task, (Integer) totalShards);
            }

            return new ProcessResult(true);

        } catch (Exception e) {
            PlatformLogUtil.logException("Map子任务执行失败", e.getMessage(), e, LogListUtil.newArrayList(taskId, shardIndex));
            return new ProcessResult(false, e.getMessage());
        }
    }

    /**
     * 从缓存获取分片数据
     */
    @SuppressWarnings("unchecked")
    private List<String> getShardFromCache(Long taskId, int shardIndex) {
        try {
            String shardCacheKey = TULING_TAG_SHARD_DATA_CACHE + taskId + "_" + shardIndex + "_" + DateUtils.getSimpleDayDateString(new Date());
            Object cached = ldbTairManager.get(shardCacheKey);
            return cached != null ? JSONObject.parseArray((String) cached, String.class) : null;
        } catch (Exception e) {
            PlatformLogUtil.logException("获取分片缓存失败", e.getMessage(), e, LogListUtil.newArrayList(taskId, shardIndex));
            return null;
        }
    }

    /**
     * 检查并在所有分片完成时立即完成任务
     * 优化：避免在 Reduce 任务中长时间等待
     */
    private void checkAndCompleteTaskIfAllShardsFinished(CrowdTagConvertTaskDTO task, Integer totalShards) {
        // 检查所有分片的处理结果
        int totalProcessedCount = 0;
        int completedShards = 0;

        for (int i = 1; i <= totalShards; i++) {
            ShardResult shardResult = getShardResultFromCache(task.getId(), i);
            if (shardResult != null) {
                totalProcessedCount += shardResult.getTotalCount();
                completedShards++;
            }
        }

        // 检查是否所有分片都已完成
        if (completedShards >= totalShards) {
            PlatformLogUtil.logInfo("所有分片已完成，立即执行任务汇总",
                    LogListUtil.newArrayList(task, completedShards, totalShards, totalProcessedCount));

            // 所有分片处理完成，更新任务状态和结果
            //查询数据库成功人数
            Long totalSuccessCount = userTagRecordRepository.countByTaskIdAndStatusAndOperation(String.valueOf(task.getId()), UserTagRecordStatusEnum.SUCCESS.getCode(), TagOperationEnum.ADD.getCode());
            task.setConvertCount(totalSuccessCount);
            task.setLastExecuteTime(new Date());
            task.setStatus(CrowdTagConvertTaskStatusEnum.SUCCESS);
            task.setLastExecuteTime(new Date());
            if (!crowdTagConvertTaskService.updateTaskStatus(task)) {
                PlatformLogUtil.logFail("任务完成,更新任务失败", LogListUtil.newArrayList(task));
            }
            // 清理缓存
            cleanupTaskCache(task.getId(), totalShards);
        } else {
            PlatformLogUtil.logInfo("分片进度检查",
                    LogListUtil.newArrayList(task, "已完成", completedShards, "总数", totalShards));
        }
    }

    /**
     * 缓存分片处理结果
     */
    private void cacheShardResult(Long taskId, int shardIndex, int successCount, int totalCount) {
        String resultCacheKey = TASK_RESULT_CACHE_PREFIX + taskId + "_" + shardIndex;
        ShardResult result = new ShardResult(shardIndex, successCount, totalCount);
        boolean put = ldbTairManager.put(resultCacheKey, JSONObject.toJSONString(result), CACHE_EXPIRE_TIME);
        if (!put) {
            PlatformLogUtil.logFail("缓存分片处理结果失败", LogListUtil.newArrayList(taskId, shardIndex, successCount, totalCount));
            ldbTairManager.put(resultCacheKey, JSONObject.toJSONString(result), CACHE_EXPIRE_TIME);
        }

        PlatformLogUtil.logInfo("缓存分片处理结果", LogListUtil.newArrayList(taskId, shardIndex, successCount, totalCount));
    }

    /**
     * 从缓存获取分片处理结果
     */
    private ShardResult getShardResultFromCache(Long taskId, int shardIndex) {
        String resultCacheKey = TASK_RESULT_CACHE_PREFIX + taskId + "_" + shardIndex;
        Object cached = ldbTairManager.get(resultCacheKey);
        PlatformLogUtil.logInfo("从缓存获取分片处理结果", LogListUtil.newArrayList(resultCacheKey, cached));
        return cached != null ? JSONObject.parseObject((String) cached, ShardResult.class) : null;
    }

    /**
     * 清理任务相关缓存
     */
    private void cleanupTaskCache(Long taskId, int totalShards) {

        // 清理分片数据缓存
        for (int i = 1; i <= totalShards; i++) {
            String shardCacheKey = TULING_TAG_SHARD_DATA_CACHE + taskId + "_" + i + "_" + DateUtils.getSimpleDayDateString(new Date());
            String resultCacheKey = TASK_RESULT_CACHE_PREFIX + taskId + "_" + i;
            ldbTairManager.delete(shardCacheKey);
            ldbTairManager.delete(resultCacheKey);
        }

        PlatformLogUtil.logInfo("清理任务缓存完成", LogListUtil.newArrayList(taskId, totalShards));
    }

    /**
     * 步骤1: 查询满足条件的任务
     * 条件：源数据平台是诸葛，目标平台是图灵
     */
    private CrowdTagConvertTaskDTO queryEligibleTasks() {
        CrowdTagConvertTaskQuery query = new CrowdTagConvertTaskQuery();
        query.setSourcePlatform(PlatformTypeEnum.ZHU_GE.getCode().byteValue());
        query.setTargetPlatform(PlatformTypeEnum.TU_LING.getCode().byteValue());
        //处理中的更新为失败状态
        processingTask(query);

        // 查询待处理状态的任务
        query.setStatus(CrowdTagConvertTaskStatusEnum.PENDING.getCode());
        List<CrowdTagConvertTaskDTO> pendingTasks = crowdTagConvertTaskService.queryTasks(query);
        if (!pendingTasks.isEmpty()) {
            return pendingTasks.get(0);
        }

        // 查询周期任务（在同步时间内的）
        CrowdTagConvertTaskDTO cycleTask = getCycleTaskDTOS(query);
        if (Objects.nonNull(cycleTask)) {
            return cycleTask;
        }
        // 查询失败状态的任务
        CrowdTagConvertTaskDTO failedTask = getFiledTaskDTOS();
        if (Objects.nonNull(failedTask)) {
            return failedTask;
        }
        return null;
    }

    private CrowdTagConvertTaskDTO getCycleTaskDTOS(CrowdTagConvertTaskQuery query) {
        Date now = new Date();
        query.setStatus(CrowdTagConvertTaskStatusEnum.SUCCESS.getCode());
        query.setExecuteTypeList(Arrays.asList(CrowdTagConvertExecuteTypeEnum.PERIODIC.getCode(), CrowdTagConvertExecuteTypeEnum.SOURCE_CROWD_VALID_TIME.getCode()));
        query.setEndDateEnd(now);
        List<CrowdTagConvertTaskDTO> periodicTasks = crowdTagConvertTaskService.queryTasks(query);
        //过滤掉最后执行时时间是今天的任务
        periodicTasks = periodicTasks.stream().filter(task -> {
            return !Objects.equals(DateUtils.getSimpleDayDateString(task.getLastExecuteTime()), DateUtils.getSimpleDayDateString(now));
        }).collect(Collectors.toList());
        if (periodicTasks.isEmpty()) {
            return null;
        }
        return periodicTasks.get(0);
    }

    private CrowdTagConvertTaskDTO getFiledTaskDTOS() {
        CrowdTagConvertTaskQuery query = new CrowdTagConvertTaskQuery();
        query.setSourcePlatform(PlatformTypeEnum.ZHU_GE.getCode().byteValue());
        query.setTargetPlatform(PlatformTypeEnum.TU_LING.getCode().byteValue());
        query.setStatus(CrowdTagConvertTaskStatusEnum.FAILED.getCode());
        List<CrowdTagConvertTaskDTO> failedTasks = crowdTagConvertTaskService.queryTasks(query);
        if (failedTasks.isEmpty()) {
            return null;
        }
        for (CrowdTagConvertTaskDTO task : failedTasks) {
            task.setStatus(CrowdTagConvertTaskStatusEnum.PENDING);
            String taskRetry = TASK_STATUS_RETRY_PREFIX + task.getId();
            Object totalShards = ldbTairManager.get(taskRetry);
            if (Objects.isNull(totalShards)) {
                ldbTairManager.put(taskRetry, 1, 3600 * 24);
                return task;
            }
            if (Integer.parseInt(totalShards.toString()) < 2) {
                ldbTairManager.put(taskRetry, Integer.parseInt(totalShards.toString()) + 1, 3600 * 24);
                return task;
            }
        }
        return null;
    }

    private void processingTask(CrowdTagConvertTaskQuery query) {
        query.setStatus(CrowdTagConvertTaskStatusEnum.PROCESSING.getCode());
        List<CrowdTagConvertTaskDTO> processingTasks = crowdTagConvertTaskService.queryTasks(query);
        if (processingTasks.isEmpty()) {
            return;
        }
        for (CrowdTagConvertTaskDTO task : processingTasks) {
            try {
                //任务执行时间超过5小时，更新为失败状态
                if (com.fliggy.pokemon.common.utils.DateUtils.dateSubtraction(task.getGmtModified(), new Date(), TimeUnitEnum.HOUR) < ZHUGE_TO_TULING_TASK_TIME_MAX) {
                    continue;
                }

                task.setStatus(CrowdTagConvertTaskStatusEnum.FAILED);
                if (!crowdTagConvertTaskService.updateTaskStatus(task)) {
                    PlatformLogUtil.logFail("超时修改任务状态失败", LogListUtil.newArrayList(task));
                }
            } catch (Exception e) {
                PlatformLogUtil.logException("处理中任务处理,任务更新异常", e.getMessage(), e, LogListUtil.newArrayList(task));
            }
        }

    }

    /**
     * 检查用户是否已有标签（先查缓存，再查lindrom记录表）
     */
    private boolean hasUserTagAndUpsertRecord(String userId, CrowdTagConvertTaskDTO task) {
        String cacheKey = USER_TAG_CACHE_PREFIX + userId + "_" + task.getSourceCrowdId();
        UserTagRecordDO updateRecord = new UserTagRecordDO();
        updateRecord.setUserId(userId);
        updateRecord.setTagId(task.getSourceCrowdId());
        updateRecord.setPlatform(task.getTargetPlatform().getCode());
        updateRecord.setConvertTaskId(String.valueOf(task.getId()));
        // 先查缓存
        Object cached = ldbTairManager.get(cacheKey);
        if (cached != null && Boolean.parseBoolean(cached.toString())) {
            // 缓存命中，用户已有标签 更新lindrom
            updateRecord.setOperationDate(DateUtils.getSimpleDayDateString(new Date()));
            UserTagRecordDO update = userTagRecordRepository.update(updateRecord);
            if (Objects.isNull(update)) {
                PlatformLogUtil.logFail("更新用户标签记录失败", LogListUtil.newArrayList(updateRecord));
            }
            return true;
        }

        // 缓存未命中，查询lindrom记录表
        UserTagRecordDO record = userTagRecordRepository.queryByPrimaryKey(
                String.valueOf(task.getId()),
                userId,
                task.getSourceCrowdId(),
                task.getTargetPlatform().getCode()
        );

        if (Objects.isNull(record)) {
            createUserTagRecord(task, userId, task.getSourceCrowdId());
            return false;
        }
        // 检查记录状态是否为成功
        boolean hasTag = Objects.equals(record.getStatus(), UserTagRecordStatusEnum.SUCCESS.getCode()) && Objects.equals(TagOperationEnum.ADD.getCode(), record.getTagOperation());

        if (hasTag) {
            record.setOperationDate(DateUtils.getSimpleDayDateString(new Date()));
            userTagRecordRepository.update(record);
            return true;
        }
        record.setTagOperation(TagOperationEnum.ADD.getCode());
        record.setStatus(UserTagRecordStatusEnum.PENDING.getCode());
        record.setOperationDate(DateUtils.getSimpleDayDateString(new Date()));

        userTagRecordRepository.update(record);
        return false;
    }

    /**
     * 步骤5: 执行分片打标
     */
    private int executeShardTagging(CrowdTagConvertTaskDTO task, List<String> userShard, int shardIndex, Boolean isRemoveTag) {
        AtomicInteger successCount = new AtomicInteger(0);


        // 调用支付宝打标API进行打标
        for (String userId : userShard) {
            String alipayUserId = getAlipayUserIdByTaobaoUserId(userId);
            if (StringUtils.isEmpty(alipayUserId)) {
                // 更新打标记录状态为失败
                updateSingleTagRecordStatus(task.getId(), userId, task.getFirstLevelTag(), alipayUserId, task.getTargetPlatform().getCode(), UserTagRecordStatusEnum.FAILED.getCode());
                continue;
            }
            try {
                boolean tagResult = callAlipayTagApi(task, alipayUserId, isRemoveTag);
                if (tagResult) {
                    // 更新打标记录状态为成功
                    updateSingleTagRecordStatus(task.getId(), userId, task.getSourceCrowdId(), alipayUserId, task.getTargetPlatform().getCode(), UserTagRecordStatusEnum.SUCCESS.getCode());
                    successCount.incrementAndGet();
                    // 更新缓存
                    updateUserTagCache(userId, task.getSourceCrowdId(), true, isRemoveTag);
                } else {
                    // 更新打标记录状态为失败
                    updateSingleTagRecordStatus(task.getId(), userId, task.getFirstLevelTag(), alipayUserId, task.getTargetPlatform().getCode(), UserTagRecordStatusEnum.FAILED.getCode());
                }

            } catch (Exception e) {
                PlatformLogUtil.logException("图灵打标单个用户失败", e.getMessage(), e, LogListUtil.newArrayList(task.getId(), userId));
                // 更新打标记录状态为失败
                updateSingleTagRecordStatus(task.getId(), userId, task.getFirstLevelTag(), alipayUserId, task.getTargetPlatform().getCode(), UserTagRecordStatusEnum.FAILED.getCode());
            }
        }
        return successCount.get();
    }

    /**
     * 创建用户打标记录
     */
    private UserTagRecordDO createUserTagRecord(CrowdTagConvertTaskDTO task, String userId, String tagId) {
        UserTagRecordDO record = new UserTagRecordDO();
        record.setConvertTaskId(String.valueOf(task.getId()));
        record.setUserId(userId);
        record.setTagId(tagId);
        record.setPlatform(PlatformTypeEnum.TU_LING.getCode());
        record.setTagOperation(TagOperationEnum.ADD.getCode());
        record.setStatus(UserTagRecordStatusEnum.PENDING.getCode());
        record.setCreatTime(DateUtils.getDetailDateString(new Date()));
        record.setOperationDate(DateUtils.getSimpleDayDateString(new Date()));
        return userTagRecordRepository.create(record);
    }

    /**
     * 更新单个标签记录状态
     */
    private void updateSingleTagRecordStatus(Long taskId, String userId, String tagId, String alipayUserId, Integer platform, Integer status) {

        UserTagRecordDO record = new UserTagRecordDO();
        record.setConvertTaskId(String.valueOf(taskId));
        record.setUserId(userId);
        record.setTagId(tagId);
        record.setStatus(status);
        record.setPlatform(platform);
        record.setOperationDate(DateUtils.getSimpleDayDateString(new Date()));
        record.setAlipayId(alipayUserId);
        if (Objects.isNull(userTagRecordRepository.update(record))) {
            PlatformLogUtil.logFail("更新用户标签记录失败", LogListUtil.newArrayList(record));
        }

    }

    /**
     * 调用支付宝打标API进行打标
     */
    private boolean callAlipayTagApi(CrowdTagConvertTaskDTO task, String alipayUserId, Boolean isRemoveTag) {
        // 构建支付宝打标请求
        AlipayTagRequest request = new AlipayTagRequest();
        request.setUserId(alipayUserId);
        // 图灵申请的标签字段
        request.setTagField(TULING_TAG_FIELD);
        request.setTagValueTwo(task.getFirstLevelTag());
        //1-打标 0-去标
        if (isRemoveTag) {
            request.setTagValueOne("0");
        } else {
            request.setTagValueOne("1");
        }
        // 调用支付宝打标API
        ResultDO<String> result = alipayTagService.label(request);

        if (result.getSuccess()) {
            return true;
        }
        PlatformLogUtil.logFail("支付宝打标API调用失败",
                LogListUtil.newArrayList(alipayUserId, result.getResultMessage()));
        return false;
    }

    /**
     * 更新用户标签缓存
     */
    private void updateUserTagCache(String userId, String tagId, boolean hasTag, boolean isRemoveTag) {
        if (StringUtils.hasText(tagId) && !isRemoveTag) {
            String cacheKey = USER_TAG_CACHE_PREFIX + userId + "_" + tagId;
            ldbTairManager.put(cacheKey, hasTag, TAG_CACHE_EXPIRE_TIME);
            return;
        }
        String cacheKey = USER_TAG_CACHE_PREFIX + userId + "_" + tagId;
        ldbTairManager.delete(cacheKey);

    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, CrowdTagConvertTaskStatusEnum status, Date expiredDate, Long crowdAmount) {


        CrowdTagConvertTaskDTO updateDTO = new CrowdTagConvertTaskDTO();
        updateDTO.setId(taskId);
        updateDTO.setStatus(status);
        updateDTO.setGmtModified(new Date());
        updateDTO.setLastExecuteTime(new Date());
        if (expiredDate != null) {
            updateDTO.setEndDate(expiredDate);
        }
        if (crowdAmount != null) {
            updateDTO.setTotalCount(crowdAmount);
        }
        if (!crowdTagConvertTaskService.updateTaskStatus(updateDTO)) {
            PlatformLogUtil.logFail("更新任务状态失败", LogListUtil.newArrayList(taskId, status.getDesc()));
        }
        PlatformLogUtil.logInfo("更新任务状态", LogListUtil.newArrayList(taskId, status.getDesc()));

    }

    /**
     * Map 任务数据
     */
    @Data
    private static class MapTaskData implements Serializable {
        private final Long taskId;
        private final int shardIndex;
        private final int totalShards;
        private final String tagId;

        public MapTaskData(Long taskId, int shardIndex, int totalShards, String tagId) {
            this.taskId = taskId;
            this.shardIndex = shardIndex;
            this.totalShards = totalShards;
            this.tagId = tagId;
        }
    }

    /**
     * 分片处理结果
     */
    private static class ShardResult implements Serializable {
        private final int shardIndex;
        private final int successCount;
        private final int totalCount;

        public ShardResult(int shardIndex, int successCount, int totalCount) {
            this.shardIndex = shardIndex;
            this.successCount = successCount;
            this.totalCount = totalCount;
        }

        public int getShardIndex() {
            return shardIndex;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getTotalCount() {
            return totalCount;
        }
    }

    /**
     * 批量处理去标用户并分片
     */
    private void processRemoveTagInBatches(CrowdTagConvertTaskDTO task, AtomicInteger totalShards) {
        PlatformLogUtil.logInfo("开始处理去标任务", LogListUtil.newArrayList(task.getId()));

        // 使用分页查询避免大数据量问题
        String date = DateUtils.getSimpleDayDateString(new Date());
        // 每页查询1000条记录
        int pageSize = 1000;
        int offset = 0;
        boolean hasMoreData = true;

        List<String> currentBatch = new ArrayList<>();
        int totalProcessed = 0;

        while (hasMoreData && totalProcessed < task.getTotalCount()) {
            // 分页查询需要去标的用户
            List<UserTagRecordDO> usersToRemove = userTagRecordRepository.listByPrimaryKeyWithPaging(
                    String.valueOf(task.getId()),
                    task.getSourceCrowdId(),
                    task.getTargetPlatform().getCode(),
                    date,
                    TagOperationEnum.ADD.getCode(),
                    offset,
                    pageSize
            );

            if (CollectionUtils.isEmpty(usersToRemove)) {
                hasMoreData = false;
                break;
            }

            // 如果返回的记录数小于页大小，说明这是最后一页
            if (usersToRemove.size() < pageSize) {
                hasMoreData = false;
            }

            PlatformLogUtil.logInfo("分页查询到需要去标的用户",
                    LogListUtil.newArrayList(task.getId(), "offset", offset, "size", usersToRemove.size()));

            // 处理当前页的数据
            for (UserTagRecordDO record : usersToRemove) {
                String userId = record.getUserId();
                String alipayUserId = record.getAlipayId();

                if (StringUtils.hasText(userId) && StringUtils.hasText(alipayUserId)) {
                    currentBatch.add(userId);
                    totalProcessed++;

                    // 更新记录为去标状态
                    updateUserTagRecordForRemove(record);

                    // 当前批次达到分片大小时，创建分片任务
                    if (currentBatch.size() >= ZHUGE_TO_TULING_SHARD_SIZE) {
                        totalShards.incrementAndGet();
                        createShardTask(task, currentBatch, totalShards.get());
                        currentBatch.clear();
                    }
                }
            }

            // 更新偏移量，准备查询下一页
            offset += pageSize;
        }

        // 处理剩余的用户（不足一个分片的）
        if (!currentBatch.isEmpty()) {
            totalShards.incrementAndGet();
            createShardTask(task, currentBatch, totalShards.get());
            PlatformLogUtil.logInfo("创建最后去标分片任务", LogListUtil.newArrayList(task.getId(), totalShards.get(), currentBatch.size()));
        }

        // 如果没有创建任何分片，说明没有需要去标的用户
        if (totalShards.get() == 0) {
            PlatformLogUtil.logInfo("没有需要去标的用户", LogListUtil.newArrayList(task.getId()));
            updateTaskStatus(task.getId(), CrowdTagConvertTaskStatusEnum.SUCCESS, null, null);
            return;
        }
        String shardTotalKey = TASK_TOTAL_SHARDS + task.getId() + "_" + task.getTargetPlatform().getCode() + "_" + date;
        boolean put = ldbTairManager.put(shardTotalKey, totalShards.get());
        if (!put) {
            PlatformLogUtil.logFail("去标任务分片失败", "创建分片任务失败",
                    LogListUtil.newArrayList(task.getId(), "totalShards", totalShards.get(), "totalProcessed", totalProcessed));
            ldbTairManager.put(shardTotalKey, totalShards.get());
        }
    }


    /**
     * 更新用户标签记录为去标状态
     */
    private void updateUserTagRecordForRemove(UserTagRecordDO record) {
        try {
            record.setTagOperation(TagOperationEnum.REMOVE.getCode());
            record.setStatus(UserTagRecordStatusEnum.PENDING.getCode());
            record.setOperationDate(DateUtils.getSimpleDayDateString(new Date()));
            UserTagRecordDO update = userTagRecordRepository.update(record);
            if (Objects.isNull(update)) {
                PlatformLogUtil.logFail("去标任务更新用户标签记录失败", "更新用户标签记录失败",
                        LogListUtil.newArrayList("userId", record.getUserId(), "tagId", record.getTagId()));
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("更新用户标签记录为去标状态失败", e.getMessage(), e,
                    LogListUtil.newArrayList("userId", record.getUserId(), "tagId", record.getTagId()));
        }
    }

    /**
     * 根据淘宝用户ID查询支付宝ID
     */
    private String getAlipayUserIdByTaobaoUserId(String taobaoUserId) {
        String cacheKey = SCRM_TAO_TO_ALIPAY_ID_CACHE_PREFIX + taobaoUserId;
        Object alipayId = ldbTairManager.get(cacheKey);
        if (Objects.nonNull(alipayId)) {
            return (String) alipayId;
        }
        Long userId = Long.parseLong(taobaoUserId);

        //查询lindorm
        if(ZHUGE_TO_TULING_TASK_QUERY_LINGDONG){
            String alipayIdByUserId = userTagRecordRepository.queryAlipayIdByUserId(taobaoUserId);
            if (StringUtils.hasText(alipayIdByUserId)) {
                ldbTairManager.put(cacheKey, alipayIdByUserId, SCRM_TAO_TO_ALIPAY_ID_CACHE_EXPIRE_TIME);
                return alipayIdByUserId;
            }
        }

        // 调用UIC服务查询支付宝用户ID
        com.taobao.uic.common.domain.ResultDO<BasePaymentAccountDO> result = uicPaymentAccountReadServiceClient.getAccountByUserId(userId);
        if (result.isSuccess() && result.getModule() != null) {
            BasePaymentAccountDO accountDO = result.getModule();
            String alipayUserId =  AlipayCardNOUtil.convertToAlipayStyle(accountDO.getAccountNo());
            if (StringUtils.hasText(alipayUserId)) {
                ldbTairManager.put(cacheKey, alipayUserId, SCRM_TAO_TO_ALIPAY_ID_CACHE_EXPIRE_TIME);
                return alipayUserId;
            }
        }
        PlatformLogUtil.logFail("查询支付宝用户ID失败",
                LogListUtil.newArrayList(taobaoUserId, result.getErrMsg()));
        return null;
    }

    private ProcessResult executeRemoveMapTask(JobContext context) {
        MapTaskData mapTaskData = (MapTaskData) context.getTask();
        Long taskId = mapTaskData.getTaskId();
        int shardIndex = mapTaskData.getShardIndex();

        PlatformLogUtil.logInfo("开始执行Map子任务", LogListUtil.newArrayList(taskId, shardIndex));

        // 从缓存获取任务信息和分片数据
        CrowdTagConvertTaskDTO task = crowdTagConvertTaskService.getTaskById(taskId);
        List<String> shard = getShardFromCache(taskId, shardIndex);

        if (task == null || CollectionUtils.isEmpty(shard)) {
            PlatformLogUtil.logFail("Map子任务获取缓存数据失败", LogListUtil.newArrayList(taskId, shardIndex));
            return new ProcessResult(false, "缓存数据获取失败");
        }
        // 执行去标
        int successCount = executeShardTagging(task, shard, shardIndex, true);

        // 缓存分片处理结果
        cacheShardResult(taskId, shardIndex, -successCount, shard.size());

        PlatformLogUtil.logInfo("Map子任务处理完成", LogListUtil.newArrayList(taskId, shardIndex, successCount, shard.size()));

        // 每个分片完成后立即检查是否所有分片都完成了
        String shardTotalKey = TASK_TOTAL_SHARDS + task.getId() + "_" + task.getTargetPlatform().getCode() + "_" + DateUtils.getSimpleDayDateString(new Date());
        Object totalShards = ldbTairManager.get(shardTotalKey);
        if (Objects.nonNull(totalShards)) {
            checkAndCompleteTaskIfAllShardsFinished(task, (Integer) totalShards);
        }
        return new ProcessResult(true);

    }
}