package com.alibaba.tripscrm.service.service.risk.config;

import com.alibaba.tripscrm.service.model.domain.request.AccountRiskConfigUpdateRequest;
import com.alibaba.tripscrm.service.model.domain.risk.*;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigDetailVO;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigVO;
import com.alibaba.tripscrm.service.model.exception.WarnException;
import com.alibaba.tripscrm.service.service.risk.RiskConfigHandler;
import org.springframework.stereotype.Component;

/**
 * 机器人在线时长 配置处理器
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class RiskOnlineTimeConfigHandler extends RiskConfigHandler {

    @Override
    public void buildListShow(UserRiskSchema schema, AccountRiskConfigVO riskConfig) {
        if (schema != null) {
            RiskOnlineProtect riskOnlineProtect = schema.getRiskOnlineProtect();
            riskConfig.setOnlineDurationMinute(riskOnlineProtect.getEnable() ?
                    "当在线时长小于" + riskOnlineProtect.getOnlineDurationMinute() + "分钟时处于保护状态" : "未启用");
        } else {
            riskConfig.setOnlineDurationMinute("遵循默认风控");
        }
    }

    @Override
    public void buildDetailShow(UserRiskSchema schema, AccountRiskConfigDetailVO result) {
        // 设置在线时长阈值
        RiskOnlineProtect riskOnlineProtect = new RiskOnlineProtect(null, false);
        if (schema != null) {
            riskOnlineProtect = schema.getRiskOnlineProtect();
        }
        result.setRiskOnlineProtect(riskOnlineProtect);
    }

    @Override
    public void updateConfig(UserRiskSchema schema, AccountRiskConfigUpdateRequest param, AccountRiskConfigVO.RiskIdInfo riskIdInfo) {
        // 设置在线时长阈值
        RiskOnlineProtect riskOnlineProtect = schema.getRiskOnlineProtect();
        if (param.getRiskOnlineProtect() != null) {
            riskOnlineProtect = param.getRiskOnlineProtect();
            if (riskOnlineProtect.getEnable() && (riskOnlineProtect.getOnlineDurationMinute() == null || riskOnlineProtect.getOnlineDurationMinute() == 0)) {
                throw new WarnException("请填写在线时长阈值");
            }
        }
        schema.setRiskOnlineProtect(riskOnlineProtect);
    }
}

