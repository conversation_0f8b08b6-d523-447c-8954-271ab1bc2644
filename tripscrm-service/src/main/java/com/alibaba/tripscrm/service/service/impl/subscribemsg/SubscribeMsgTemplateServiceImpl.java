package com.alibaba.tripscrm.service.service.impl.subscribemsg;

import com.ali.com.google.common.collect.Lists;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.dto.subscribemsg.SubscribeMsgTemplateDTO;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.subscribemsg.SubscribeMsgTemplateService;
import com.alibaba.tripzoo.admin.client.domain.model.subscribemsg.TemplateDTO;
import com.alibaba.tripzoo.admin.client.request.subscribemsg.TemplatePageQueryRequest;
import com.alibaba.tripzoo.admin.client.result.TripzooAdminResult;
import com.alibaba.tripzoo.admin.client.service.subscribemsg.SubscribeMsgService;
import com.alibaba.tripzoo.admin.enums.subscribemsg.MiniProgramPlatformEnum;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小程序订阅消息模板服务实现类
 *
 * <AUTHOR>
 * @date 2025/10/20
 */
@Slf4j
@Service
public class SubscribeMsgTemplateServiceImpl implements SubscribeMsgTemplateService {

    @Resource
    private SubscribeMsgService subscribeMsgService;

    @Resource
    private MaterialService materialService;

    private final Long SUBSCRIBE_MSG_SPACE_ID = 275L;
    private final Integer QUERY_TEMPLATE_MAX_SIZE = 10;

    @Override
    public List<SubscribeMsgTemplateDTO> list(List<Long> materialIdList) {
        try {
            List<MaterailInfoDO> materialList;
            if (CollectionUtils.isEmpty(materialIdList)) {
                materialList = queryMaterialList();
                if (CollectionUtils.isEmpty(materialList)) {
                    PlatformLogUtil.logFail("根据素材id查询模版失败，未查询到符合条件的素材列表");
                    return new ArrayList<>();
                }
            } else {
                materialList = materialService.listByIds(materialIdList);
                // 过滤掉materialList中type不为miniProgramSubscribeMsg的素材
                materialList = materialList.stream().filter(material -> TaskType.MINI_PROGRAM_SUBSCRIBE_MSG.getCode().equals(material.getType())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(materialList)) {
                    PlatformLogUtil.logFail("根据素材id查询模版失败，不存在类型为小程序订阅消息的素材");
                    return new ArrayList<>();
                }
            }

            // 2. 根据每个素材的content字段中的platformType整理出多个list
            Map<Integer, List<SubscribeMsgTemplateDTO>> platformTemplateMap = groupTemplatesByPlatform(materialList);

            // 3. 对每个list根据templateId去重，用去重后的模版id列表和platformType字段调用外部服务获取模版名称
            List<SubscribeMsgTemplateDTO> result = fillTemplateInfo(platformTemplateMap);
            return result;
        } catch (Exception e) {
            PlatformLogUtil.logException("全量查询小程序订阅消息模板失败", e.getMessage(), e, LogListUtil.newArrayList());
            return new ArrayList<>();
        }
    }

    /**
     * 根据模版id和平台类型查询并填充模版信息
     * @param templateIdList 模版id列表
     * @param platformType   平台类型
     * @param templateList   模板列表
     */
    private List<SubscribeMsgTemplateDTO> queryTemplateInfoByTemplateIdAndPlatform(List<String> templateIdList, Integer platformType, List<SubscribeMsgTemplateDTO> templateList) {
        if (CollectionUtils.isEmpty(templateIdList) || Objects.isNull(MiniProgramPlatformEnum.valueOf(platformType))) {
            PlatformLogUtil.logFail("查询小程序订阅消息模板名称失败，参数非法", LogListUtil.newArrayList(templateIdList, platformType));
            return new ArrayList<>();
        }
        List<SubscribeMsgTemplateDTO> result = new ArrayList<>();
        
        // 分批处理模板ID列表
        List<List<String>> partitionTemplateIds = Lists.partition(templateIdList, QUERY_TEMPLATE_MAX_SIZE);
        for (List<String> subTemplateIds : partitionTemplateIds) {
            result.addAll(processTemplateBatch(subTemplateIds, platformType, templateList));
        }
        return result;
    }

    /**
     * 处理模板查询的单个批次
     * @param subTemplateIds 批次中的模板ID列表
     * @param platformType   平台类型
     * @param templateList   模板列表
     */
    private List<SubscribeMsgTemplateDTO> processTemplateBatch(List<String> subTemplateIds, Integer platformType, List<SubscribeMsgTemplateDTO> templateList) {
        try {
            // 构造外部服务请求参数
            TemplatePageQueryRequest request = buildTemplateQueryRequest(subTemplateIds, platformType, subTemplateIds.size());

            // 调用外部服务查询模板
            TripzooAdminResult<com.alibaba.tripzoo.admin.client.response.PageInfoDTO<TemplateDTO>> result =
                    subscribeMsgService.queryTemplate(request);
            
            if (result == null || !result.isSuccess() || result.getData() == null || CollectionUtils.isEmpty(result.getData().getList())) {
                PlatformLogUtil.logFail("查询小程序订阅消息模板失败", LogListUtil.newArrayList(request, result));
                return new ArrayList<>();
            }

            // 处理查询结果并填充模板信息
            List<TemplateDTO> templateDTOList = result.getData().getList();
            Map<String, TemplateDTO> templateDTOMap = buildTemplateDTOMap(templateDTOList);
            List<SubscribeMsgTemplateDTO> TemplateDTOWithInfoList = fillTemplateInfo(templateList, templateDTOMap);
            return TemplateDTOWithInfoList;

        } catch (Exception e) {
            PlatformLogUtil.logException("查询小程序订阅消息模板失败", e.getMessage(), e, LogListUtil.newArrayList());
            return new ArrayList<>();
        }
    }

    /**
     * 构建模板查询请求
     * @param templateIdList 模板ID列表
     * @param platformType   平台类型
     * @param pageSize       页面大小
     * @return 模板查询请求对象
     */
    private TemplatePageQueryRequest buildTemplateQueryRequest(List<String> templateIdList, Integer platformType, int pageSize) {
        TemplatePageQueryRequest request = new TemplatePageQueryRequest();
        request.setTemplateIdList(templateIdList);
        request.setPlatformType(MiniProgramPlatformEnum.valueOf(platformType));
        request.setPageNum(1);
        request.setPageSize(pageSize);
        return request;
    }

    /**
     * 构建模板DTO映射
     * @param templateDTOList 模板DTO列表
     * @return 模板映射
     */
    private Map<String, TemplateDTO> buildTemplateDTOMap(List<TemplateDTO> templateDTOList) {
        return templateDTOList.stream()
                .collect(Collectors.toMap(
                        // 拼接 version + platformType + templateId 作为键
                        t -> t.getVersion() + "_" + t.getPlatformType() + "_" + t.getTemplateId(),
                        // 值为 TemplateDTO 对象本身
                        t -> t,
                        // 如果存在重复键，保留第一个遇到的元素
                        (t1, t2) -> t1
                ));
    }

    /**
     * 填充模板信息
     * @param templateList   模板列表
     * @param templateDTOMap 模板映射
     */
    private List<SubscribeMsgTemplateDTO> fillTemplateInfo(List<SubscribeMsgTemplateDTO> templateList, Map<String, TemplateDTO> templateDTOMap) {
        List<SubscribeMsgTemplateDTO> result = new ArrayList<>();
        templateList.forEach(templateInfo -> {
            TemplateDTO templateDTO = templateDTOMap.get(templateInfo.getVersion() + "_" + templateInfo.getPlatformType() + "_" + templateInfo.getTemplateId());
            if (Objects.isNull(templateDTO)) {
                // todo 上线前删除
                PlatformLogUtil.logFail("查询小程序订阅消息模板名称失败，未查询到模板", LogListUtil.newArrayList(templateInfo));
                return;
            }
            templateInfo.setTemplateName(templateDTO.getTemplateName());
            templateInfo.setParamList(templateDTO.getParamList().stream().map(this::getTemplateParam).collect(Collectors.toList()));
            templateInfo.setPageUrl(templateDTO.getPageUrl());
            result.add(templateInfo);
        });
        return result;
    }

    private SubscribeMsgTemplateDTO convertTemplateDTO(TemplateDTO templateDTO) {
        SubscribeMsgTemplateDTO subscribeMsgTemplateDTO = new SubscribeMsgTemplateDTO();
        subscribeMsgTemplateDTO.setTemplateId(templateDTO.getTemplateId());
        subscribeMsgTemplateDTO.setTemplateName(templateDTO.getTemplateName());
        subscribeMsgTemplateDTO.setPlatformType(Integer.valueOf(templateDTO.getPlatformType()));
        subscribeMsgTemplateDTO.setVersion(templateDTO.getVersion());
        List<SubscribeMsgTemplateDTO.TemplateParam> paramList = new ArrayList<>();
        for (TemplateDTO.TemplateParam templateParam : templateDTO.getParamList()) {
            SubscribeMsgTemplateDTO.TemplateParam templateParamDTO = getTemplateParam(templateParam);
            paramList.add(templateParamDTO);
        }
        subscribeMsgTemplateDTO.setParamList(paramList);
        return subscribeMsgTemplateDTO;
    }

    private SubscribeMsgTemplateDTO.TemplateParam getTemplateParam(TemplateDTO.TemplateParam templateParam) {
        SubscribeMsgTemplateDTO.TemplateParam templateParamDTO  = new SubscribeMsgTemplateDTO.TemplateParam();
        BeanUtils.copyProperties(templateParam, templateParamDTO);
        return templateParamDTO;
    }

    /**
     * 查询符合条件的素材列表
     *
     * @return 素材列表
     */
    private List<MaterailInfoDO> queryMaterialList() {
        try {
            List<MaterailInfoDO> materialList = materialService.listBySpaceIdAndType(SUBSCRIBE_MSG_SPACE_ID, TaskType.MINI_PROGRAM_SUBSCRIBE_MSG.getCode());
            return materialList;
        } catch (Exception e) {
            PlatformLogUtil.logException("查询素材列表失败", e.getMessage(), e, LogListUtil.newArrayList());
            return new ArrayList<>();
        }
    }

    /**
     * 根据platformType分组模板信息
     *
     * @param materialList 素材列表
     * @return 按platformType分组的模板信息
     */
    private Map<Integer, List<SubscribeMsgTemplateDTO>> groupTemplatesByPlatform(List<MaterailInfoDO> materialList) {
        Map<Integer, List<SubscribeMsgTemplateDTO>> platformTemplateMap = new HashMap<>();

        for (MaterailInfoDO material : materialList) {
            if (material == null || !StringUtils.hasText(material.getContent())) {
                continue;
            }

            try {
                // 解析content字段的JSON
                SubscribeMsgTemplateDTO subscribeMsgTemplateDTO = JSONObject.parseObject(material.getContent(), new TypeReference<SubscribeMsgTemplateDTO>() {});
                if (Objects.isNull(subscribeMsgTemplateDTO) || !StringUtils.hasText(subscribeMsgTemplateDTO.getTemplateId()) || Objects.isNull(MiniProgramPlatformEnum.valueOf(subscribeMsgTemplateDTO.getPlatformType()))) {
                    PlatformLogUtil.logFail("解析素材content字段失败，解析结果为空，跳过", LogListUtil.newArrayList(material, subscribeMsgTemplateDTO));
                    continue;
                }
                subscribeMsgTemplateDTO.setMaterialId(material.getId());
                platformTemplateMap.computeIfAbsent(subscribeMsgTemplateDTO.getPlatformType(), k -> new ArrayList<>()).add(subscribeMsgTemplateDTO);
            } catch (Exception e) {
                PlatformLogUtil.logException("解析素材content字段失败", e.getMessage(), e, LogListUtil.newArrayList(material.getId(), material.getContent()));
            }
        }

        return platformTemplateMap;
    }

    /**
     * 填充模板信息
     *
     * @param platformTemplateMap 按platformType分组的模板信息
     * @return 填充了模板信息的结果列表
     */
    private List<SubscribeMsgTemplateDTO> fillTemplateInfo(Map<Integer, List<SubscribeMsgTemplateDTO>> platformTemplateMap) {
        List<SubscribeMsgTemplateDTO> result = new ArrayList<>();

        for (Map.Entry<Integer, List<SubscribeMsgTemplateDTO>> entry : platformTemplateMap.entrySet()) {
            Integer platformType = entry.getKey();
            List<SubscribeMsgTemplateDTO> templateList = entry.getValue();

            if (CollectionUtils.isEmpty(templateList)) {
                continue;
            }

            // 对模板列表按templateId去重，用于查询模板名称（避免重复查询）
            List<SubscribeMsgTemplateDTO> distinctTemplates = new ArrayList<>(templateList.stream()
                    .collect(Collectors.toMap(SubscribeMsgTemplateDTO::getTemplateId, t -> t, (t1, t2) -> t1))
                    .values());

            // 获取去重后的模板id列表
            List<String> templateIds = distinctTemplates.stream()
                    .map(SubscribeMsgTemplateDTO::getTemplateId)
                    .collect(Collectors.toList());

            // 调用外部服务查询并填充模板信息
            List<SubscribeMsgTemplateDTO> subscribeMsgTemplateDTOWithInfoList = queryTemplateInfoByTemplateIdAndPlatform(templateIds, platformType, templateList);
            result.addAll(subscribeMsgTemplateDTOWithInfoList);
        }

        return result;
    }

    @AteyeInvoker(description = "根据素材id列表查询小程序订阅消息模板", paraDesc = "materialIdList")
    public List<SubscribeMsgTemplateDTO> list(String materialIdList) {
        List<Long> materialIdListParam = new ArrayList<>();
        if (StringUtils.hasText(materialIdList)) {
            materialIdListParam = Arrays.stream(materialIdList.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        List<SubscribeMsgTemplateDTO> templateList = list(materialIdListParam);
        return templateList;
    }
}