package com.alibaba.tripscrm.service.service.strategy.variable.inner;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.material.InnerServiceEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/24 11:08
 */
@Component
public class TripTradeWaitingCollectPointGetPointVariableDataInnerService extends AbstractVariableDataInnerService {
    @Override
    public String doGetValue(Map<String, Object> paramMap) {
        if (!paramMap.containsKey("pointSendSource") || !paramMap.containsKey("operateType")  || !paramMap.containsKey("totalPointNum")) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!Objects.equals(paramMap.get("operateType"), 0)) {
            throw new TripscrmException(TripSCRMErrorCode.REPLACE_VARIABLE_BE_FILTER);
        }

        return String.valueOf(paramMap.get("totalPointNum"));
    }

    @Override
    public InnerServiceEnum getInnerServiceEnum() {
        return InnerServiceEnum.TRIP_TRADE_WAITING_COLLECT_POINT_GET_POINT;
    }
}
