package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.enums.wechat.CreateWeComRecordStatusEnum;
import com.alibaba.tripscrm.service.model.dto.wechat.CreateWeComRecordDTO;
import com.alibaba.tripscrm.service.service.CreateWeComRecordService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.result.WeComRobotByLinkSummaryBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 机器人上号结果汇总【比邻回调】
 *
 * <AUTHOR>
 * @date 2025-01-21 23:57
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WeComRobotByLinkSummaryCallbackProcessor implements ProxyCallbackProcessor {

    private final CreateWeComRecordService createWeComRecordService;
    private final static String SUCCESS_COUNT = "successCount";

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.WE_COM_ROBOT_BY_LINK_SUMMARY;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        PlatformLogUtil.logInfo("机器人上号结果汇总回调", LogListUtil.newArrayList(scrmCallbackMsg));
        if (Objects.isNull(scrmCallbackMsg.getContent()) || !StringUtils.hasLength(scrmCallbackMsg.getRequestId()) || Objects.isNull(scrmCallbackMsg.getResult())) {
            PlatformLogUtil.logFail("机器人上号结果汇总，回调参数非法", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        // 根据requestId匹配上号记录
        CreateWeComRecordDTO oldRecord = createWeComRecordService.selectByDateAndOrderId(new Date(System.currentTimeMillis() -  24L * 3 * 60 * 60 * 1000), new Date(), scrmCallbackMsg.getRequestId());
        if (Objects.isNull(oldRecord) || !Objects.equals(IsvTypeEnum.valueOf(oldRecord.getIsvType()), IsvTypeEnum.BI_LIN)) {
            PlatformLogUtil.logFail("未查询到上号申请记录", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        WeComRobotByLinkSummaryBO weComRobotByLinkSummaryBO;
        try {
            weComRobotByLinkSummaryBO = JSONObject.parseObject(scrmCallbackMsg.getContent(), new TypeReference<WeComRobotByLinkSummaryBO>() {});
        } catch (Exception e) {
            PlatformLogUtil.logException("上号记录汇总数据格式转换失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        CreateWeComRecordDTO newRecord = new CreateWeComRecordDTO();
        newRecord.setId(oldRecord.getId());
        PlatformLogUtil.logInfo("上号状态", LogListUtil.newArrayList(scrmCallbackMsg.getResult(), weComRobotByLinkSummaryBO.getCount()));
        // 更新上号记录申请状态
        newRecord.setStatus(
                scrmCallbackMsg.getResult() && weComRobotByLinkSummaryBO.getCount() > 0 ? CreateWeComRecordStatusEnum.APPLY_SUCCESS.getCode()
                        : CreateWeComRecordStatusEnum.APPLY_FAILED.getCode()
        );
        JSONObject createWeComRecordExtInfo = JSONObject.parseObject(oldRecord.getExtInfo());
        createWeComRecordExtInfo.put(SUCCESS_COUNT, weComRobotByLinkSummaryBO.getCount());
        newRecord.setExtInfo(createWeComRecordExtInfo.toJSONString());
        Integer createWeComRecordEffectLines = createWeComRecordService.updateByIdSelective(newRecord);
        if (createWeComRecordEffectLines < 1) {
            PlatformLogUtil.logFail("更新上号记录申请状态失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }

        if (CollectionUtils.isEmpty(weComRobotByLinkSummaryBO.getRobotList())) {
            PlatformLogUtil.logFail("上号记录汇总数据为空", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        return true;
    }
}
