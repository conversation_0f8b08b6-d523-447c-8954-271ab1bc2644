package com.alibaba.tripscrm.service.service.impl.material;

import com.alibaba.tripscrm.dal.mapper.tddl.VariableInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SceneInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.VariableInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.VariableInfoParam;
import com.alibaba.tripscrm.service.model.domain.query.SceneInfoQuery;
import com.alibaba.tripscrm.service.model.domain.query.VariableInfoQuery;
import com.alibaba.tripscrm.service.model.dto.material.VariableInfoDTO;
import com.alibaba.tripscrm.service.service.material.SceneInfoService;
import com.alibaba.tripscrm.service.service.material.VariableInfoService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.material.ModelConvertUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class VariableInfoServiceImpl implements VariableInfoService {

    private final VariableInfoMapper variableInfoMapper;
    private final SceneInfoService sceneInfoService;

    @Override
    public Boolean add(VariableInfoDTO variableInfoDTO) {
        VariableInfoDO variableInfoDO = ModelConvertUtils.convertDO(variableInfoDTO);
        variableInfoDO.setGmtCreate(new Date());
        variableInfoDO.setGmtModified(new Date());
        if (variableInfoDO.getEnv() == null) {
            variableInfoDO.setEnv(EnvUtils.getEnvironment());
        }
        return variableInfoMapper.insert(variableInfoDO) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        return variableInfoMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public Boolean updateById(VariableInfoDTO variableInfoDTO) {
        return variableInfoMapper.updateByPrimaryKeySelective(ModelConvertUtils.convertDO(variableInfoDTO)) > 0;
    }

    @Override
    public List<VariableInfoDTO> query(VariableInfoQuery query) {
        if (query == null) {
            return Lists.newArrayList();
        }
        VariableInfoParam param = new VariableInfoParam();
        VariableInfoParam.Criteria criteria = param.createCriteria();
        if (query.getId() != null) {
            criteria.andIdEqualTo(query.getId());
        }
        if (query.getDataSourceType() != null) {
            criteria.andDataSourceEqualTo(query.getDataSourceType());
        }
        if (query.getSceneId() != null) {
            criteria.andSceneIdEqualTo(query.getSceneId());
        }
        if (StringUtils.isNotBlank(query.getName())) {
            criteria.andNameLike(query.getName());
        }
        if (query.getIndustryType() != null) {
            SceneInfoQuery sceneQuery = new SceneInfoQuery();
            sceneQuery.setIndustryType(query.getIndustryType());
            List<Long> sceneIdList = sceneInfoService.list(sceneQuery).stream().map(SceneInfoDO::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(sceneIdList)) {
                return Lists.newArrayList();
            }
            criteria.andSceneIdIn(sceneIdList);
        }
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());
        return Optional.ofNullable(variableInfoMapper.selectByParam(param))
                .orElse(new ArrayList<>()).stream().map(ModelConvertUtils::convertDTO).collect(Collectors.toList());
    }

    @Override
    public VariableInfoDTO queryById(Long id) {
        VariableInfoDTO variableInfoDTO = ModelConvertUtils.convertDTO(variableInfoMapper.selectByPrimaryKey(id));
        SceneInfoDO sceneInfoDO = sceneInfoService.queryById(variableInfoDTO.getSceneId());
        if (sceneInfoDO != null) {
            variableInfoDTO.setIndustryType(sceneInfoDO.getIndustry());
        }
        return variableInfoDTO;
    }
}
