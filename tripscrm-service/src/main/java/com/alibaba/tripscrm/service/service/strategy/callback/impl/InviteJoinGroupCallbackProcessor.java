package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.system.OperationLogStatusEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.log.BatchOperateLogContentBO;
import com.alibaba.tripscrm.service.model.dto.BatchOperateLogDTO;
import com.alibaba.tripscrm.service.service.log.BatchOperateLogInfoService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.callback.TaskAsyncRequestCallbackProcessor;
import com.alibaba.tripscrm.service.util.log.OperatorLogUtils;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.OperatorResult;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/6/6 14:33
 **/
@Component
@AllArgsConstructor
public class InviteJoinGroupCallbackProcessor implements ProxyCallbackProcessor {

    private final LdbTairManager ldbTairManager;
    private final BatchOperateLogInfoService batchOperateLogInfoService;
    private final TaskAsyncRequestCallbackProcessor taskAsyncRequestCallbackProcessor;
    private final MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.INVITER_JOIN_GROUP_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (Objects.isNull(scrmCallbackMsg) || StringUtils.isBlank(scrmCallbackMsg.getRequestId())) {
            PlatformLogUtil.logFail("邀请成员进群回调内容为空或者请求id为空");
            return false;
        }

        boolean dealResult = true;
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("邀请成员入群回调显示失败", LogListUtil.newArrayList(scrmCallbackMsg));
            dealResult = false;
        }

        processTaskExecuteResult(scrmCallbackMsg);
        // 邀请成员入群操作结果处理
        dealBatchOperate(scrmCallbackMsg.getRequestId(), dealResult);
        // 往事件源发送消息
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        sendOutMessage(scrmCallbackMsg, content);
        dealEventSource(content, scrmCallbackMsg.getPlatformCorpId());
        dealRefreshSellerCorpValidUser(content, scrmCallbackMsg);
        return true;
    }

    private void sendOutMessage(ScrmCallbackMsg scrmCallbackMsg, JSONObject content) {
        try {
            // 对外部提供的metaq
            JSONObject message1 = new JSONObject();
            message1.put("result", scrmCallbackMsg.getResult());
            message1.put("userId", content.getString(CallbackConstant.USER_ID));
            message1.put("corpId", scrmCallbackMsg.getPlatformCorpId());
            message1.put("requestId", scrmCallbackMsg.getRequestId());
            metaqProducer.send(MQEnum.ASYNC_INVITE_JOIN_GROUP_RESULT, "", message1.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("邀请进群回调，发送外部消息出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private void dealRefreshSellerCorpValidUser(JSONObject content, ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (!scrmCallbackMsg.getResult()) {
                return;
            }
            String userId = content.getString(CallbackConstant.USER_ID);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("wechatUserId", userId);
            metaqProducer.send(MQEnum.SCRM_SELLER_CORP_VALID_PLATFORM_WECHAT_USER_SYNC, null, null, jsonObject.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方群聊创建回调，处理群聊池刷新逻辑出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private void dealEventSource(JSONObject content, String platformCorpId) {
        try {
            JSONObject message = new JSONObject();
            if (!content.containsKey(CallbackConstant.SUCCESS_EXTERNALUSER_ID_LIST)) {
                return;
            }
            List<String> successExternalUserIdList = content.getObject(CallbackConstant.SUCCESS_EXTERNALUSER_ID_LIST, new TypeReference<List<String>>() {
            });
            if (CollectionUtils.isEmpty(successExternalUserIdList) || successExternalUserIdList.size() != 1) {
                return;
            }
            message.put("sendUserId", content.getString(CallbackConstant.USER_ID));
            message.put("externalUserId", successExternalUserIdList.get(0));
            message.put("corpId", platformCorpId);
            metaqProducer.send(MQEnum.JION_GROUP_REQUEST_SEND_SUCCESS, "", message.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方群聊创建回调，处理事件源逻辑出错", e.getMessage(), e, LogListUtil.newArrayList(content, platformCorpId));
        }
    }


    /**
     * 邀请成员入群操作结果处理
     *
     * @param requestId  请求id
     * @param dealResult 处理结果
     */
    private void dealBatchOperate(String requestId, boolean dealResult) {
        try {
            Object o = ldbTairManager.get(TairConstant.BATCH_GROUP_OPERATE_MAPPING + requestId);
            if (Objects.isNull(o)) {
                return;
            }
            BatchOperateLogDTO batchOperateLogDTO = batchOperateLogInfoService.queryById((Long) o);
            if (batchOperateLogDTO == null) {
                return;
            }
            batchOperateLogInfoService.updateById(getNewOperateLogDTO(requestId, dealResult, batchOperateLogDTO));
        } catch (Exception e) {
            PlatformLogUtil.logException("邀请成员入群操作结果处理", e.getMessage(), e, LogListUtil.newArrayList(requestId, dealResult));
        }
    }

    /**
     * 获取最新的操作详情
     *
     * @param requestId  邀请请求id
     * @param dealResult 邀请结果
     * @return 操作详情
     */
    private BatchOperateLogDTO getNewOperateLogDTO(String requestId, boolean dealResult, BatchOperateLogDTO batchOperateLogDTO) {
        List<BatchOperateLogContentBO> operateLogDetailList = new ArrayList<>();
        // 更新邀请结果
        operateLogDetailList = batchOperateLogDTO.getBatchOperateLogContentList();
        for (BatchOperateLogContentBO batchOperateLogContentBO : operateLogDetailList) {
            if (StringUtils.isNotBlank(batchOperateLogContentBO.getRequestId()) && batchOperateLogContentBO.getRequestId().equals(requestId)) {
                batchOperateLogContentBO.setOperateState(dealResult ? OperationLogStatusEnum.SUCCESS.getCode() : OperationLogStatusEnum.FAIL.getCode());
            }
        }
        // 串联操作结果
        Object resultDO = ldbTairManager.get(TairConstant.ASYNC_SERIES_OPERATE_RESULT + requestId);
        if (Objects.isNull(resultDO)) {
            return batchOperateLogDTO;
        }
        ResultDO<OperatorResult> result = JSONObject.parseObject((String) resultDO, new TypeReference<ResultDO<OperatorResult>>() {
        });
        operateLogDetailList.addAll(OperatorLogUtils.getOperateLogList(result));
        batchOperateLogDTO.setBatchOperateLogContentList(operateLogDetailList);
        return batchOperateLogDTO;
    }

    private void processTaskExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            taskAsyncRequestCallbackProcessor.process(scrmCallbackMsg);
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方群聊创建回调，处理任务异步执行结果出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

}
