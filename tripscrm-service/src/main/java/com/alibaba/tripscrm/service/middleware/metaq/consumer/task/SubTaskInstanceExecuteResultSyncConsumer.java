package com.alibaba.tripscrm.service.middleware.metaq.consumer.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * topic: sub_task_instance_execute_result_sync
 * consumerId: CID_sub_task_instance_execute_result_sync
 *
 * <AUTHOR>
 * @since 2024/6/6 10:46
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SubTaskInstanceExecuteResultSyncConsumer implements MessageListenerConcurrently {

    private final SubTaskInstanceService subTaskInstanceService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logInfo("接收到同步子任务实例执行结果消息，执行失败", LogListUtil.newArrayList(msg));
                continue;
            }

            PlatformLogUtil.logInfo("接收到同步子任务实例执行结果消息，执行完毕", LogListUtil.newArrayList(msg));
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private boolean dealWithMessage(String receivedMsg) {
        JSONObject jsonObject = JSONObject.parseObject(receivedMsg);
        Long subTaskInstanceId = jsonObject.getLong("subTaskInstanceId");
        SubTaskInstanceDO subTaskInstanceDO = subTaskInstanceService.queryById(subTaskInstanceId);
        JSONObject executeResultFromTair = subTaskInstanceService.getExecuteResultFromTair(subTaskInstanceId);
        String extraInfo = subTaskInstanceDO.getExtInfo();
        if (!StringUtils.hasText(extraInfo)) {
            extraInfo = "{}";
        }
        JSONObject extraInfoJson = JSONObject.parseObject(extraInfo);
        extraInfoJson.putAll(executeResultFromTair);
        subTaskInstanceDO.setExtInfo(extraInfoJson.toJSONString());
        return subTaskInstanceService.updateById(subTaskInstanceDO) > 0;
    }
}
