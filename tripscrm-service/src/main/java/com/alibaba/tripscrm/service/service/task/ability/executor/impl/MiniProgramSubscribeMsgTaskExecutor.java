package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskInstanceQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.subscribemsg.SubscribeMsgTaskStrategyEnum;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.manager.subscribemsg.SubscribeMsgManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.miniprogram.subscribemsg.SCRMSubscribeMsgSendRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.util.log.ttlog.TtEventTrackLogUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.csp.courier.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支付宝定向消息任务执行器
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@Service
public class MiniProgramSubscribeMsgTaskExecutor extends AbstractTaskExecutor {
    @Resource
    protected SubscribeMsgManager subscribeMsgManager;
    @Resource
    protected TaskExecuteRecordService taskExecuteRecordService;
    @Resource
    protected TaskInstanceService taskInstanceService;


    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();

        // 从上下文中获取targetId
        String targetId = getFinalTargetId(context, taskDataBody);
        ActivityTargetTypeEnum targetType = getFinalTargetType(context);

        // 根据执行策略构建请求
        SubscribeMsgTaskStrategyEnum strategy = getStrategy(taskInfoDO.getExtInfo());
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(context, todoTaskVO);
        MaterialContentConvertContext materialContentConvertContext = buildMaterialContentConvertContext(context, todoTaskVO.getData().get(0));
        List<SCRMSubscribeMsgSendRequest> sCRMSubscribeMsgSendRequests = buildSCRMSubscribeMsgSendRequests(materialTrackRelationDTO, materialContentConvertContext, todoTaskVO, strategy, targetId, targetType);
        List<Long> materialIdList = new ArrayList<>();
        int failCount = 0;
        for (SCRMSubscribeMsgSendRequest sendRequest : sCRMSubscribeMsgSendRequests) {
            TtEventTrackLogUtils.log(taskInfoDO.getEventSourceId(), taskInfoDO.getId(), sendRequest.getTargetId(), targetType.getCode(), getAbTestBucketId(todoTaskVO), sendRequest.getMaterialId());
            TripSCRMResult<Boolean> result = subscribeMsgManager.sendSubscribeMsg(sendRequest);
            if (Objects.isNull(result) || !result.isSuccess()) {
                PlatformLogUtil.logFail("小程序订阅消息管理，发送订阅消息，本条消息发送失败", LogListUtil.newArrayList(sendRequest, result));
                failCount++;
            }
            materialIdList.add(sendRequest.getMaterialId());
        }
        if (failCount == sCRMSubscribeMsgSendRequests.size()) {
            PlatformLogUtil.logFail("小程序订阅消息管理，发送订阅消息，所有消息发送失败", LogListUtil.newArrayList(sCRMSubscribeMsgSendRequests, context, todoTaskVO));
            throw new TripscrmException(TripSCRMErrorCode.SEND_SUBSCRIBE_MSG_FAIL);
        }

        JSONObject data = new JSONObject();
        data.put("extraInfo", materialContentConvertContext.getExtraInfo());
        data.put("materialIdList", materialIdList);
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    /**
     * 构建请求列表
     *
     * @param materialTrackRelationDTO      素材-trackId关系
     * @param materialContentConvertContext 素材上下文
     * @param todoTaskVO                    待执行任务
     * @param strategy                      任务执行策略
     * @return 请求列表
     */
    private List<SCRMSubscribeMsgSendRequest> buildSCRMSubscribeMsgSendRequests(MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext materialContentConvertContext, TodoTaskVO todoTaskVO, SubscribeMsgTaskStrategyEnum strategy, String targetId, ActivityTargetTypeEnum targetType) {
        List<SCRMSubscribeMsgSendRequest> SCRMSubscribeMsgSendRequests = new ArrayList<>();
        List<Long> materialIdList = todoTaskVO.getAbTestBucketVO().getMaterialIdList();
        switch (strategy) {
            case SEQUENTIAL:
                // 获取该分桶下的素材数量
                Integer materialCount = materialIdList.size();
                // 查询素材数量-1个素材id列表
                List<Long> sendMaterialIdList = queryRecentTaskExecuteRecord(todoTaskVO.getTaskId(), targetId, targetType, materialCount - 1);
                Long sequentialMaterialId;
                // 过滤发送过的素材，随机选一个剩余模版
                if (CollectionUtils.isEmpty(sendMaterialIdList) || sendMaterialIdList.size() >= materialCount) {
                    sequentialMaterialId = todoTaskVO.getAbTestBucketVO().getMaterialIdList().get(0);
                } else {
                    sequentialMaterialId = materialIdList.stream().filter(materialId1 -> !sendMaterialIdList.contains(materialId1)).findFirst().orElse(null);
                }
                SCRMSubscribeMsgSendRequest sequentialRequest = new SCRMSubscribeMsgSendRequest();
                sequentialRequest.setMaterialId(sequentialMaterialId);
                materialTrackRelationDTO.setMaterialId(sequentialMaterialId);
                sequentialRequest.setTargetId(targetId);
                sequentialRequest.setMaterialTrackRelationDTO(materialTrackRelationDTO);
                sequentialRequest.setMaterialContentConvertContext(materialContentConvertContext);
                SCRMSubscribeMsgSendRequests.add(sequentialRequest);
                break;
            case CONCURRENT:
                for (Long materialId : materialIdList) {
                    SCRMSubscribeMsgSendRequest concurrentRequest = new SCRMSubscribeMsgSendRequest();
                    concurrentRequest.setMaterialId(materialId);
                    concurrentRequest.setTargetId(targetId);
                    materialTrackRelationDTO.setMaterialId(materialId);
                    concurrentRequest.setMaterialTrackRelationDTO(materialTrackRelationDTO);
                    concurrentRequest.setMaterialContentConvertContext(materialContentConvertContext);
                    SCRMSubscribeMsgSendRequests.add(concurrentRequest);
                }
                break;
            default:
                PlatformLogUtil.logFail("订阅消息任务执行策略校验失败，任务执行策略非法", LogListUtil.newArrayList(strategy));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
        }
        return SCRMSubscribeMsgSendRequests;
    }

    /**
     * 查询最新的count条任务执行记录
     *
     * @param taskId     任务ID（必须为正整数）
     * @param targetId   目标ID（非空字符串）
     * @param targetType 目标类型（非空枚举）
     * @param count      查询数量
     * @return 最近发送的count个素材id
     */
    private List<Long> queryRecentTaskExecuteRecord(Long taskId, String targetId, ActivityTargetTypeEnum targetType, Integer count) {
        // 参数校验
        if (!NumberUtils.validLong(taskId) || !StringUtils.hasLength(targetId) ||
                Objects.isNull(targetType) || Objects.isNull(count)) {
            PlatformLogUtil.logFail("查询任务执行记录失败，参数非法", LogListUtil.newArrayList(taskId, targetId, targetType, count));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (count == 0) {
            return new ArrayList<>();
        }

        // 查询任务实例
        List<TaskInstanceDO> taskInstances = queryTaskInstances(taskId);
        if (CollectionUtils.isEmpty(taskInstances)) {
            return new ArrayList<>();
        }

        // 按创建时间降序排序
        List<TaskInstanceDO> sortedInstances = sortTaskInstances(taskInstances);

        // 查询并聚合执行记录
        return collectExecuteRecords(sortedInstances, targetId, taskId, targetType, count);
    }

    /**
     * 查询任务实例列表
     *
     * @param taskId 任务ID（必须为正整数）
     * @return 任务实例列表（可能为空列表）
     */
    private List<TaskInstanceDO> queryTaskInstances(Long taskId) {
        TaskInstanceQuery taskInstanceQuery = new TaskInstanceQuery();
        taskInstanceQuery.setTaskId(taskId);
        return taskInstanceService.list(taskInstanceQuery);
    }

    /**
     * 按创建时间降序排序任务实例
     *
     * @param taskInstances 待排序的任务实例列表（非空）
     * @return 按创建时间降序排列的新列表（不修改原列表）
     */
    private List<TaskInstanceDO> sortTaskInstances(List<TaskInstanceDO> taskInstances) {
        return taskInstances.stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(TaskInstanceDO::getGmtCreate).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 查询并聚合执行记录（按创建时间降序，最多返回count条）
     *
     * @param sortedInstances 已按创建时间降序排序的任务实例列表
     * @param targetId        目标ID（非空字符串）
     * @param taskId          任务ID（必须为正整数）
     * @param targetType      目标类型（非空枚举）
     * @param count           需要返回的最大记录数
     * @return 最近发送的count个素材id
     */
    private List<Long> collectExecuteRecords(List<TaskInstanceDO> sortedInstances, String targetId, Long taskId, ActivityTargetTypeEnum targetType, Integer count) {
        List<Long> result = new ArrayList<>();

        for (int i = 0; i < Math.min(sortedInstances.size(), SwitchConfig.SUBSCRIBE_MSG_TASK_EXECUTOR_QUERY_TASK_INSTANCE_MAX_COUNT); i++) {
            TaskInstanceDO instance = sortedInstances.get(i);
            TaskExecuteRecordBO query = buildQuery(targetId, taskId, instance.getId(), targetType);
            List<TaskExecuteRecordBO> records = taskExecuteRecordService.queryByTargetAndInstance(query);
            if (CollectionUtils.isEmpty(records)) {
                continue;
            }
            List<TaskExecuteRecordBO> sortRecords = sortTaskExecuteRecordBOByGmtCreate(records);
            for (TaskExecuteRecordBO record : sortRecords) {
                List<Long> recordMaterialIdList = getMaterialIdFromExecuteRecord(record);
                result.addAll(recordMaterialIdList);
                if (result.size() >= count) {
                    // 从result中移除多余的记录，使得result.size() = count
                    result = result.subList(0, count);
                    return result;
                }
            }
        }

        return result;
    }

    /**
     * 根据创建时间降序排序任务执行记录
     */
    private List<TaskExecuteRecordBO> sortTaskExecuteRecordBOByGmtCreate(List<TaskExecuteRecordBO> records) {
        return records.stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(TaskExecuteRecordBO::getGmtCreate).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 从任务执行记录中获取素材id列表
     * @param recordBO 任务执行记录
     * @return 素材id列表
     */
    private List<Long> getMaterialIdFromExecuteRecord(TaskExecuteRecordBO recordBO) {
        if (Objects.isNull(recordBO) || Objects.isNull(recordBO.getExecuteInfo())) {
            return new ArrayList<>();
        }
        try {
            JSONObject taskExecuteResult = JSONObject.parseObject(recordBO.getExtraInfo().get("taskExecuteResult").toString());
            if (Objects.isNull(taskExecuteResult)) {
                return new ArrayList<>();
            }
            return taskExecuteResult.getJSONArray("materialIdList").toJavaList(Long.class);
        } catch (Exception e) {
            PlatformLogUtil.logFail("小程序订阅消息任务执行器，获取任务执行记录中的素材id列表失败，executeInfo非法", LogListUtil.newArrayList(recordBO));
            return new ArrayList<>();
        }
    }

    /**
     * 构建任务执行记录查询参数
     *
     * @param targetId   目标ID（非空字符串）
     * @param taskId     任务ID（必须为正整数）
     * @param instanceId 任务实例ID（必须为正整数）
     * @param targetType 目标类型（非空枚举）
     * @return 构建完成的查询参数对象
     */
    private TaskExecuteRecordBO buildQuery(String targetId, Long taskId, Long instanceId, ActivityTargetTypeEnum targetType) {
        TaskExecuteRecordBO query = new TaskExecuteRecordBO();
        query.setTargetId(targetId);
        query.setTaskId(taskId);
        query.setMainTaskInstanceId(instanceId);
        query.setTargetTypeEnum(targetType);
        query.setStatusEnum(TaskSingleDataExecuteResultEnum.SUCCESS);
        return query;
    }

    /**
     * 将优先队列转换为按创建时间降序排列的列表
     *
     * @param queue 待转换的优先队列
     * @return 按创建时间降序排列的列表
     */
    private List<TaskExecuteRecordBO> convertToSortedList(PriorityQueue<TaskExecuteRecordBO> queue) {
        List<TaskExecuteRecordBO> sortedList = new ArrayList<>(queue);
        sortedList.sort((a, b) -> b.getGmtCreate().compareTo(a.getGmtCreate()));
        return sortedList;
    }

    private MaterialTrackRelationDTO buildMaterialTrackRelationDTO(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(getScene());
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setTargetId(getFinalTargetId(context, todoTaskVO.getData().get(0)));
        return materialTrackRelationDTO;
    }

    private MaterialContentConvertContext buildMaterialContentConvertContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        if (StringUtils.hasLength(taskDataBody.getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(taskDataBody.getExtInfo()));
        }
        return materialContentConvertContext;
    }


    protected String getScene() {
        return MaterialSendSceneTypeConstant.MINI_PROGRAM_SUBSCRIBE_MSG;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.TAOBAO_USER_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (Objects.isNull(context) || Objects.isNull(taskDataBody)) {
            PlatformLogUtil.logFail("获取支付宝定向消息目标id失败，context或taskDataBody为空", LogListUtil.newArrayList(context, taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        String uid = taskDataBody.getTargetId();
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!StringUtils.hasLength(uid) || !NumberUtils.validLong(Long.valueOf(uid)) || !Objects.equals(targetTypeEnum, ActivityTargetTypeEnum.TAOBAO_USER_ID)) {
            PlatformLogUtil.logFail("获取支付宝定向消息目标id失败，targetId或targetType非法", LogListUtil.newArrayList(context, taskDataBody, uid, targetTypeEnum));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_TARGET_ID);
        }
        return uid;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.MINI_PROGRAM_SUBSCRIBE_MSG;
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return "";
    }

    /**
     * 获取订阅消息任务执行策略
     *
     * @param extInfo 任务扩展信息
     * @return 订阅消息任务执行策略
     */
    private SubscribeMsgTaskStrategyEnum getStrategy(String extInfo) {
        if (!StringUtils.hasLength(extInfo)) {
            PlatformLogUtil.logFail("订阅消息任务执行策略校验失败，任务扩展信息为空", LogListUtil.newArrayList(extInfo));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
        }
        try {
            JSONObject jsonObject = JSONObject.parseObject(extInfo);
            Integer executeStrategyCode = jsonObject.getInteger(TaskConstant.SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
            if (!NumberUtils.validInteger(executeStrategyCode) || Objects.isNull(SubscribeMsgTaskStrategyEnum.of(executeStrategyCode))) {
                PlatformLogUtil.logFail("订阅消息任务执行策略校验失败，任务执行策略非法", LogListUtil.newArrayList(extInfo));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
            }
            return SubscribeMsgTaskStrategyEnum.of(executeStrategyCode);
        } catch (Exception e) {
            PlatformLogUtil.logException("订阅消息任务执行策略校验出错", e.getMessage(), e, LogListUtil.newArrayList(extInfo));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY);
        }

    }


}
