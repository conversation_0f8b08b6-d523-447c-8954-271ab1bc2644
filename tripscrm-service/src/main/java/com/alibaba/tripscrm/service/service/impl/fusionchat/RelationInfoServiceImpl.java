package com.alibaba.tripscrm.service.service.impl.fusionchat;

import com.alibaba.tripscrm.dal.mapper.tddl.RelationInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.RelationInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.RelationInfoParam;
import com.alibaba.tripscrm.service.enums.task.RelationInfoTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.service.common.RelationInfoService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.tddl.client.sequence.Sequence;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@AllArgsConstructor
public class RelationInfoServiceImpl implements RelationInfoService {

    private final RelationInfoMapper relationInfoMapper;
    private final Sequence resourceRelationSequence;

    @Override
    public Boolean addRelation(RelationInfoDO relationInfoDO) {
        return relationInfoMapper.batchInsert(convertWithId(relationInfoDO)) > 0;
    }

    @Override
    @AteyeInvoker(description = "删除关系", paraDesc = "id")
    public Boolean deleteRelationById(Long id) {
        return relationInfoMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public Boolean batchDelete(List<Long> ids) {
        for (Long id : ids) {
            deleteRelationById(id);
        }
        return true;
    }

    @Override
    public Boolean deleteByInfo(RelationInfoTypeEnum typeEnum, ResourceTypeEnum sourceType, String sourceId, ResourceTypeEnum targetType, String targetId) {
        // 分表源数据删除
        String sourceId2SharingKey = sourceId2SharingKey(sourceId);
        Boolean sourceResult = deleteByParam(typeEnum.getCode(), sourceId2SharingKey, sourceType.getCode(), sourceId, targetType.getCode(), targetId);
        // 分表目标数据删除
        String targetId2SharingKey = targetId2SharingKey(targetId);
        Boolean targetResult = deleteByParam(typeEnum.getCode(), targetId2SharingKey, sourceType.getCode(), sourceId, targetType.getCode(), targetId);
        // 结果
        return sourceResult && targetResult;
    }

    @AteyeInvoker(description = "删除关系", paraDesc = "type&sourceType&sourceId&targetType&targetId")
    public Boolean deleteByInfo(Byte type, Integer sourceType, String sourceId, Integer targetType, String targetId) {
        return deleteByInfo(RelationInfoTypeEnum.codeOf(type), ResourceTypeEnum.of(sourceType), sourceId, ResourceTypeEnum.of(targetType), targetId);
    }

    /**
     * 条件删除
     * @param typeEnum 关系类型
     * @param shardingKey 分表键
     * @param sourceType 资源类型
     * @param sourceId 资源ID
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 删除结果
     */
    private Boolean deleteByParam(Byte typeEnum, String shardingKey, Integer sourceType, String sourceId, Integer targetType, String targetId) {
        RelationInfoParam param = new RelationInfoParam();
        RelationInfoParam.Criteria criteria = param.createCriteria();
        if (StringUtils.isNotBlank(shardingKey)) {
            criteria.andShardingKeyEqualTo(shardingKey);
        }
        if (typeEnum != null) {
            criteria.andTypeEqualTo(typeEnum);
        }
        if (sourceType != null) {
            criteria.andSourceTypeEqualTo(sourceType.byteValue());
        }
        if (StringUtils.isNotBlank(sourceId)) {
            criteria.andSourceIdEqualTo(sourceId);
        }
        if (targetType != null) {
            criteria.andTargetTypeEqualTo(targetType.byteValue());
        }
        if (StringUtils.isNotBlank(targetId)) {
            criteria.andTargetIdEqualTo(targetId);
        }
        return relationInfoMapper.deleteByParam(param) > 0;
    }

    @Override
    public List<RelationInfoDO> queryByTypeAndSourceInfo(RelationInfoTypeEnum typeEnum, ResourceTypeEnum sourceType, String sourceId) {
        RelationInfoParam param = new RelationInfoParam();
        RelationInfoParam.Criteria criteria = param.createCriteria();
        criteria.andTypeEqualTo(typeEnum.getCode());
        criteria.andSourceTypeEqualTo(sourceType.getCode().byteValue());
        criteria.andSourceIdEqualTo(sourceId);
        criteria.andShardingKeyEqualTo(sourceId2SharingKey(sourceId));
        return relationInfoMapper.selectByParam(param);
    }

    @Override
    public List<RelationInfoDO> queryByTypeAndTargetInfo(RelationInfoTypeEnum typeEnum, ResourceTypeEnum targetType, String targetId) {
        RelationInfoParam param = new RelationInfoParam();
        RelationInfoParam.Criteria criteria = param.createCriteria();
        criteria.andTypeEqualTo(typeEnum.getCode());
        criteria.andTargetTypeEqualTo(targetType.getCode().byteValue());
        criteria.andShardingKeyEqualTo(targetId2SharingKey(targetId));
        return relationInfoMapper.selectByParam(param);
    }

    @Override
    public RelationInfoDO queryById(Long id) {
        return relationInfoMapper.selectByPrimaryKey(id);
    }

    private List<RelationInfoDO> convertWithId(RelationInfoDO relationInfoDO) {
        ArrayList<RelationInfoDO> relationInfoList = new ArrayList<>();
        if (relationInfoDO.getGmtCreate() == null) {
            relationInfoDO.setGmtCreate(new Date());
        }
        if (relationInfoDO.getGmtModified() == null) {
            relationInfoDO.setGmtModified(new Date());
        }
        RelationInfoDO relationInfoDO1 = new RelationInfoDO();
        BeanUtils.copyProperties(relationInfoDO, relationInfoDO1);

        relationInfoDO.setId(resourceRelationSequence.nextValue());
        relationInfoDO.setShardingKey(sourceId2SharingKey(relationInfoDO.getSourceId()));
        relationInfoList.add(relationInfoDO);

        relationInfoDO1.setId(resourceRelationSequence.nextValue());
        relationInfoDO1.setShardingKey(targetId2SharingKey(relationInfoDO1.getTargetId()));
        relationInfoList.add(relationInfoDO1);

        return relationInfoList;
    }

    public String sourceId2SharingKey(String sourceId) {
        return "1_" + sourceId;
    }

    private String targetId2SharingKey(String targetId) {
        return "2_" + targetId;
    }
}
