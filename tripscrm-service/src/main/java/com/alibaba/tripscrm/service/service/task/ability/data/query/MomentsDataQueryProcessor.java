package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskInstanceQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.TripSCRMErrorCode.TASK_QUERY_TASK_SEND_USER_FAIL;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:05:38
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MomentsDataQueryProcessor extends AbstractTaskDataProcessor {
    private final TaskService taskService;
    private final WechatUserService wechatUserService;
    private final TaskInstanceService taskInstanceService;
    private final WechatCustomerService wechatCustomerService;
    private final LdbTairManager ldbTairManager;

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.SEND_TO_MOMENTS);
    }

    @AteyeInvoker(description = "朋友圈群发数据查询器", paraDesc = "taskId")
    public TaskDataVO handleReadAllData(Long taskId) {
        TaskExecuteContext context = new TaskExecuteContext();
        context.setTaskInfoDOSnapshot(taskService.queryTaskById(taskId));
        context.setInstanceId(-1L);
        return handleReadAllData(context);
    }
    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        try {
            //获得发送用户
            String sendUserId = taskInfoDO.getSendUserId();
            if (StringUtils.isBlank(sendUserId)) {
                List<String> normalWechatUserIdList = wechatUserService.getAutoMatchUserIdList(taskInfoDO);
                if (CollectionUtils.isEmpty(normalWechatUserIdList)) {
                    return null;
                }
                sendUserId = String.join(",", normalWechatUserIdList);
            }
            // 组装任务数据
            List<String> sendUserIdList = Arrays.stream(sendUserId.split(",")).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            // 统计企微号覆盖的客户数并保存到任务实例中
            setUserIdListAndCoverCount(taskInfoDO, sendUserIdList);

            for (String userId : sendUserIdList){
                TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
                dataBodyVO.setTargetId(userId);
                dataBodyVO.setTargetType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
                JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
                extInfo.put("sendUserId", userId);
                dataBodyVO.setExtInfo(extInfo.toJSONString());
                dataVO.getData().add(dataBodyVO);
            }
            dataVO.setTotalCount((long) dataVO.getData().size());
            return dataVO;
        } catch (Exception e) {
            PlatformLogUtil.logException("【任务执行异常】查询任务数据异常", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】查询任务数据异常，任务ID:%s，实例ID:%s", context.getTaskDataVO(), context.getInstanceId()));
            throw new TripscrmException(TASK_QUERY_TASK_SEND_USER_FAIL);
        }
    }

    private void setUserIdListAndCoverCount(TaskInfoDO taskInfoDO, List<String> sendUserIdList) {
        TaskInstanceQuery query = new TaskInstanceQuery();
        query.setTaskId(taskInfoDO.getId());
        TaskInstanceDO taskInstanceDO = taskInstanceService.getNewest(query);
        Long customerTotalNumber = 0L;
        if (Objects.nonNull(taskInstanceDO)) {
            for (String userId : sendUserIdList) {
                if (StringUtils.isBlank(userId)) {
                    continue;
                }
                Long dayVersion = DateUtils.getDayVersion(new Date());
                String corpId = SpaceInfoThreadLocalUtils.getSpaceInfo().getCorpId();
                String key = TairConstant.WECHAT_USER_CUSTOMER_COUNT_PREFIX + dayVersion + "_" + corpId + "_" + userId + "_" + CustomerRelationStatusEnum.FRIEND.getDesc();
                Long customerCount = (Long) ldbTairManager.get(key);
                if (NumberUtils.validLong(customerCount)) {
                    customerTotalNumber += customerCount;
                    continue;
                }
                customerCount = wechatCustomerService.getCustomerCountWithCache(userId, false);
                if (NumberUtils.validLong(customerCount)) {
                    customerTotalNumber += customerCount;
                }
            }
            JSONObject extraInfo = JSONObject.parseObject(taskInstanceDO.getExtInfo());
            if (Objects.isNull(extraInfo)) {
                extraInfo = new JSONObject();
            }
            extraInfo.put("coverCount", customerTotalNumber);
            taskInstanceDO.setExtInfo(extraInfo.toJSONString());
            taskInstanceService.updateByPrimaryKey(taskInstanceDO);
        }
    }
}