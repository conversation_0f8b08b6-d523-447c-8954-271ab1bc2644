package com.alibaba.tripscrm.service.service.fusionchat;

import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.model.domain.query.fusionchat.ChatConversationListQuery;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationCreateParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateLastMessageParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateParam;
import com.alibaba.tripscrm.service.model.vo.fusionchat.ChatConversationVO;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetUserConversationResponseBody;

import java.util.List;

/**
 * 用户会话 Manager
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
public interface ChatConversationService {
    /**
     * 插入，唯一键冲突时更新
     * @param record
     * @return
     */
    Integer upsertChatConversation(ChatConversationDO record);

    /**
     * 保存用户会话
     *
     * @param create 对象
     * @return 结果
     */
    ChatConversationVO createChatConversation(ChatConversationCreateParam create);

    /**
     * 修改用户会话的配置
     *
     * @param update 对象
     * @return 结果
     */
    ChatConversationDO updateChatConversationConfig(ChatConversationUpdateParam update);

    /**
     * 修改用户会话最后一条消息
     *
     * @param update 对象
     */
    ChatConversationDO updateChatConversationLastMessage(ChatConversationUpdateLastMessageParam update);

    /**
     * 增加会话消息未读数
     * @param userId
     * @param senderId
     * @param chatId
     * @param corpId
     * @param content
     * @return
     */
    Integer addChatConversationUnreadCount(String userId, String senderId, String chatId, String corpId, String content);

    /**
     * 根据用户id获取用户会话列表
     *
     * @param userId 用户id
     * @return 用户会话列表
     */
    List<GetUserConversationResponseBody> listByUserId(String userId);

    /**
     * 条件查询(有则返回，无则创建)
     * @param query 查询条件
     * @return 用户会话列表
     */
    List<GetUserConversationResponseBody> listByParam(ChatConversationListQuery query);

    /**
     * 条件查询
     * @param query 查询条件
     * @return 会话列表数量
     */
    Long countByParam(ChatConversationListQuery query);

    /**
     * 获取企微号的消息未读数
     *
     * @param userId 企微号id
     */
    Integer getWechatUserUnreadCount(String corpId, String userId);

    /**
     * 获取企微号会话的消息未读数
     *
     * @param userId 企微号id
     */
    Integer getWechatUserConversationUnreadCount(String corpId, String userId, String chatId);

    /**
     * 清除该平台账号其他锁定企微号的进行中会话标记缓存
     *
     * @param account account
     * @param spaceId spaceId
     */
    void cleanUserDoingChat(User account, Long spaceId);

    /**
     * 清除该平台账号锁定企微号的进行中会话标记缓存
     *
     * @param account account
     */
    void cleanUserDoingChat(User account, String userId);
}