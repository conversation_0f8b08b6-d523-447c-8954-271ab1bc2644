package com.alibaba.tripscrm.service.service.task.ability.sub.pre;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskExecutorFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 任务执行_子任务_活动上下文有效性检查
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CheckActivityContextProcessor implements ExecuteProcessor {
    @Override
    @TaskExecuteLog("任务执行_子任务_活动上下文有效性检查")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        if (context.getTestFlag()) {
            return TripSCRMResult.success(null);
        }

        TaskTriggerTypeEnum triggerType = context.getTriggerType();
        if (!Objects.equals(TaskTriggerTypeEnum.EVENT, triggerType)) {
            return TripSCRMResult.success(null);
        }

        TaskInfoDO taskInfoDOSnapshot = context.getTaskInfoDOSnapshot();
        TaskType taskType = TaskType.getByCode(taskInfoDOSnapshot.getType());
        AbstractTaskExecutor taskExecutor = TaskExecutorFactory.getTaskBeanByType(taskType);
        boolean valid = taskExecutor.checkActivityContextValid(context, context.getTaskDataVO().getData().get(0));
        if (!valid) {
            return TripSCRMResult.fail(TripSCRMErrorCode.ACTIVITY_NOT_BELONG_TO_CURRENT_ACTIVITY);
        }
        return TripSCRMResult.success(null);
    }
}
