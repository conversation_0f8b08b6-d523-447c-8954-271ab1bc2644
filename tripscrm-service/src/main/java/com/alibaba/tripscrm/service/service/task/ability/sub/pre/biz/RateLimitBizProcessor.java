package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 单条任务数据处理_限流
 * <AUTHOR>
 * @since 2024/4/22 15:22
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RateLimitBizProcessor implements BizProcessor {
    @Override
    @TaskExecuteLog("单条任务数据处理_限流")
    public TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception {
        if (Objects.isNull(context.getRateLimiter())) {
            return TripSCRMResult.success(null);
        }

        Integer messageCount = AbstractTaskExecutor.getMessageCount(context, todoTaskVO);
        if (messageCount <= 0) {
            return TripSCRMResult.success(null);
        }

        PlatformLogUtil.logInfo("单条任务数据处理-限流，开始", LogListUtil.newArrayList(messageCount, todoTaskVO.getData().get(0)));
        context.getRateLimiter().acquire(messageCount);
        PlatformLogUtil.logInfo("单条任务数据处理-限流，结束", LogListUtil.newArrayList(messageCount, todoTaskVO.getData().get(0)));
        return TripSCRMResult.success(null);
    }
}