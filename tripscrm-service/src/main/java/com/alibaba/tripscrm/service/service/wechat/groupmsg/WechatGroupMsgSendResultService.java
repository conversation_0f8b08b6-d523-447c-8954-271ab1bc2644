package com.alibaba.tripscrm.service.service.wechat.groupmsg;


import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupMsgSendResultDO;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupMsgSendResultQuery;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WechatGroupMsgSendResultService {
    /**
     * 根据参数查询
     *
     * @param query
     */
    WechatGroupMsgSendResultDO find(WechatGroupMsgSendResultQuery query);

    /**
     * 列表查询
     *
     * @param query
     */
    List<WechatGroupMsgSendResultDO> list(WechatGroupMsgSendResultQuery query);

    /**
     * 创建
     *
     * @param record
     */
    int insertSelective(WechatGroupMsgSendResultDO record);

    /**
     * 选择性修改
     *
     * @param record
     * @param condition
     */
    int updateSelective(WechatGroupMsgSendResultDO record, WechatGroupMsgSendResultQuery condition);

    /**
     * 唯一键冲突时更新
     *
     * @param record
     * @return
     */
    int upsertSelective(WechatGroupMsgSendResultDO record);
}