package com.alibaba.tripscrm.service.service.impl.hsf.knowledge;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.domain.KnowledgeCategoryDTO;
import com.alibaba.tripscrm.domain.request.TripSCRMKnowledgeCategoryQueryRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.knowledge.TripSCRMKnowledgeCategoryService;
import com.alibaba.tripscrm.service.model.domain.request.knowledge.CategoryReq;
import com.alibaba.tripscrm.service.service.knowledge.KnowledgeCategoryService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/9/19
 */
@Slf4j
@HSFProvider(serviceInterface = TripSCRMKnowledgeCategoryService.class)
public class TripSCRMKnowledgeCategoryServiceImpl implements TripSCRMKnowledgeCategoryService {

    @Resource
    private KnowledgeCategoryService knowledgeCategoryService;

    @Override
    public TripSCRMResult<List<KnowledgeCategoryDTO>> listKnowledgeCategory(TripSCRMKnowledgeCategoryQueryRequest request) {
        CategoryReq query = new CategoryReq();
        if (Objects.nonNull(request) && Objects.nonNull(request.getSpaceId())) {
            query.setSpaceId(request.getSpaceId());
        }
        return TripSCRMResult.success(knowledgeCategoryService.selectByParam(query).stream()
                .map(e -> KnowledgeCategoryDTO.builder()
                        .name(e.getName())
                        .description(e.getDescription())
                        .build())
                .collect(Collectors.toList()));
    }
}
