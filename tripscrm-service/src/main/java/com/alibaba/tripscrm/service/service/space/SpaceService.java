package com.alibaba.tripscrm.service.service.space;

import com.alibaba.tripscrm.dal.model.domain.data.SpaceDO;
import com.alibaba.tripscrm.domain.enums.RoleTypeEnum;
import com.alibaba.tripscrm.service.model.vo.SpaceVO;
import com.alibaba.tripzoo.proxy.model.DepartmentBO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-15 16:50:27
 */
public interface SpaceService {
    /**
     * 新增
     *
     * @param spaceDO spaceDO
     * @return 影响行数
     */
    int insert(SpaceDO spaceDO);

    /**
     * 更新
     *
     * @param spaceDO spaceDO
     * @return 影响行数
     */
    int update(SpaceDO spaceDO);

    /**
     * 根据主键查询数据
     *
     * @param id 主键
     * @return 记录行
     */
    SpaceDO getById(Long id);

    /**
     * 根据业务空间名称查询数据
     *
     * @param name 业务空间名称
     * @return 记录行
     */
    SpaceDO getByName(String name);

    /**
     * 获取加入的业务空间列表
     *
     * @param userId 用户员工号
     * @return 业务空间列表
     */
    List<SpaceDO> list(String userId, String corpId);

    /**
     * 获取业务空间列表
     * @return 业务空间列表
     */
    List<SpaceDO> list(List<Long> idList);

    /**
     * 全量查询
     *
     * @return 所有的业务空间数据
     */
    List<SpaceDO> listByCorpId(String corpId);

    /**
     * 创建业务空间
     *
     * @param spaceVO 业务空间
     * @return Id
     */
    Long create(SpaceVO spaceVO);

    /**
     * 编辑业务空间
     *
     * @param spaceVO 业务空间数据
     * @return 影响行数
     */
    Integer update(SpaceVO spaceVO);

    /**
     * 检验绑定部门层级合法性
     *
     * @param departmentList 绑定部门列表
     * @return 是否合法
     */
    boolean checkDepartmentLevelGtSecond(String departmentList);

    /**
     * 检验绑定部门合法性
     *
     * @param departmentList 绑定部门列表
     * @param spaceId        业务空间 Id
     * @return 是否合法
     */
    List<DepartmentBO> getInvalidDepartmentList(String departmentList, Long spaceId);

    /**
     * 获取用户在业务空间的角色
     *
     * @param userId  员工号
     * @param spaceId 业务空间 Id
     * @return RoleTypeEnum
     */
    RoleTypeEnum getRoleType(String userId, Long spaceId);

    /**
     * 是否超级管理员
     *
     * @param empId 员工号
     * @return 是否超级管理员
     */
    boolean isSuperAdmin(String empId);

    /**
     * 获取部门绑定的空间
     *
     * @param corpId       企业Id
     * @param departmentId 部门Id
     * @return 空间Id
     */
    Long getSpaceId(String corpId, Integer departmentId);

    /**
     * 根据空间id获取corpId
     *
     * @return corpId
     */
    String getCorpIdBySpaceId(Long spaceId);

    /**
     * 获取商家ID
     * @param spaceId 空间ID
     * @return 商家ID
     */
    String getSellerIdBySpaceId(Long spaceId);
}
