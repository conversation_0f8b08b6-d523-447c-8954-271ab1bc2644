package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/11 19:40
 **/
@Data
public class TaskTagRequest {

    /**
     * 任务Id
     */
    @NotEmpty(message = "任务Id不可为空")
    private List<Long> taskIds;

    /**
     * 标签 Id
     */
    @NotEmpty(message = "标签Id不可为空")
    private List<String> tagIdList;

}
