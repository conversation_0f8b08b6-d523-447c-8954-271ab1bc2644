package com.alibaba.tripscrm.service.service.wechat;


import com.alibaba.tripscrm.dal.model.domain.data.WxEnterpriseConsumerGroupInfoDO;
import com.alibaba.tripscrm.service.model.domain.query.ConsumerGroupQuery;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.vo.wechat.GroupListVO;

/**
 * 企微群管理
 * <AUTHOR>
 * @date 2023/3/29
 */
public interface WxGroupService {

    /**
     * 获取用户群
     */
    GroupListVO listWxEnterpriseConsumerGroup(ConsumerGroupQuery consumerGroupQuery);

    /**
     * 获取用户组详情
     * @param chatId
     * @return
     */
    WxEnterpriseConsumerGroupInfoDO getWxEnterpriseConsumerGroupInfo(String chatId);

    /**
     * 覆盖保存微信群信息
     * @param wxEnterpriseConsumerGroupInfoDO
     * @return
     */
    Long recoverGroupInfo(WxEnterpriseConsumerGroupInfoDO wxEnterpriseConsumerGroupInfoDO);

    /**
     * 逻辑删除同步id过期的群，即为微信方删除的群
     * @param jobLabel
     * @return
     */
    int delSyncByJobLabel(Long jobLabel);

    /**
     * 获取用户群
     */
    PageResultVO<WxEnterpriseConsumerGroupInfoDO> listWxEnterpriseGroup(ConsumerGroupQuery consumerGroupQuery);

}
