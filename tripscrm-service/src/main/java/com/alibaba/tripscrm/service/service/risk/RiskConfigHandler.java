package com.alibaba.tripscrm.service.service.risk;

import com.alibaba.tripscrm.service.model.domain.request.AccountRiskConfigUpdateRequest;
import com.alibaba.tripscrm.service.model.domain.risk.UserRiskSchema;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigDetailVO;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigVO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 风控配置处理器抽象类，处理配置的列表页显示、详情页显示、配置更新
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public abstract class RiskConfigHandler {
    private static final List<RiskConfigHandler> riskConfigHandlers = new ArrayList<>();

    @PostConstruct
    public void init0() {
        riskConfigHandlers.add(this);
    }

    public static List<RiskConfigHandler> getConfigHandlers() {
        return riskConfigHandlers;
    }

    /**
     * 列表页显示
     */
    public abstract void buildListShow(UserRiskSchema schema, AccountRiskConfigVO riskConfig);

    /**
     * 详情页显示
     */
    public abstract void buildDetailShow(UserRiskSchema schema, AccountRiskConfigDetailVO result);

    /**
     * 配置更新
     */
    public abstract void updateConfig(UserRiskSchema schema, AccountRiskConfigUpdateRequest param, AccountRiskConfigVO.RiskIdInfo riskIdInfo);
}

