package com.alibaba.tripscrm.service.service.activity;

import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.service.model.domain.request.SopActivityQueryRequest;
import com.alibaba.tripscrm.service.model.vo.activity.AbstractActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @date 2024/1/10
 */
public interface ActivitySopService<T extends AbstractActivitySopConfigVO> {
    /**
     * 创建活动SOP
     */
    Long create(ActivitySopVO<T> activitySopVO);

    /**
     * 更新活动SOP
     */
    void update(ActivitySopVO<T> activitySopVO);

    /**
     * 上线SOP
     */
    Boolean online(Long id);

    /**
     * 下线SOP
     */
    Boolean offline(Long id);

    /**
     * 删除SOP
     */
    Boolean delete(Long id);

    /**
     * 查询活动SOP
     */
    ActivitySopVO<T> queryById(Long activityId);

    /**
     * 分页查询活动SOP列表
     */
    PageInfo<ActivitySopVO<T>> pageList(SopActivityQueryRequest request);


    ActivitySopVO<T> convert(ActivityInfoDO activityInfoDO);
}
