package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
public class ResignedTransferCustomerTaskExecutor extends TransferCustomerTaskExecutor {
    @Override
    protected String getTransferSuccessMsg(TaskExecuteContext context) {
       return null;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.RESIGNED_TRANSFER_CUSTOMER;
    }
}
