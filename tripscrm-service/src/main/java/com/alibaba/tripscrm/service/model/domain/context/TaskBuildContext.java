package com.alibaba.tripscrm.service.model.domain.context;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务执行上下文
 * <AUTHOR>
 * @date 2023/6/1
 */
@Data
public class TaskBuildContext {
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 目标类型 1 - 淘宝userId 2 - 手机号
     */
    private Integer targetType;

    /**
     * 目标id
     */
    private String targetId;
    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 虚拟标签子节点
     */
    private String subCode;

    /**
     * 业务标签
     */
    private Integer bizType;

    /**
     * 活动上下文内容
     */
    private String context;

    /**
     * 任务
     */
    private TaskInfoDO taskInfoDO;

    private Map<String, String> extraInfo = new HashMap<>();
}
