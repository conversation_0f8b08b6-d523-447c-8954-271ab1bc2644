package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 微信成员信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WxQrCodeQuery extends BasePageRequest {
    /**
     * 主键
     */
    private Long id;
    /**
     * 是否删除
     */
    private Boolean deleted;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 修改时间
     */
    private Date gmtModified;
    /**
     * state
     */
    private String state;

    private Integer pageSize = 10;

    private Integer pageNum = 1;
}
