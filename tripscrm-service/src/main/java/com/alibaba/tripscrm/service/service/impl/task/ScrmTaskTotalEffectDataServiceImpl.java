package com.alibaba.tripscrm.service.service.impl.task;

import com.alibaba.tripscrm.dal.mapper.tddl.ScrmTaskTotalEffectDataMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmTaskTotalEffectDataDO;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmTaskTotalEffectDataParam;
import com.alibaba.tripscrm.service.convert.ScrmTaskEffectDataConverter;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import com.alibaba.tripscrm.service.model.domain.query.ScrmTaskTotalEffectDataQuery;
import com.alibaba.tripscrm.service.model.dto.task.ScrmTaskTotalEffectDataDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.effectdata.ScrmTaskTotalEffectDataService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/6/26 17:21
 * @Filename：ScrmTaskTotalEffectDataServiceImpl
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ScrmTaskTotalEffectDataServiceImpl implements ScrmTaskTotalEffectDataService {
    private final ScrmTaskTotalEffectDataMapper scrmTaskTotalEffectDataMapper;
    private final ScrmTaskEffectDataConverter scrmTaskEffectDataConverter;

    @Override
    public List<ScrmTaskTotalEffectDataDTO> list(ScrmTaskTotalEffectDataQuery query) {
        ScrmTaskTotalEffectDataParam param = buildParam(query);
        List<ScrmTaskTotalEffectDataDO> list = scrmTaskTotalEffectDataMapper.selectByParam(param);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(scrmTaskEffectDataConverter::convert2TotalDTO).collect(Collectors.toList());
    }



    public ScrmTaskTotalEffectDataParam buildParam(ScrmTaskTotalEffectDataQuery query) {

        if (Objects.isNull(query) || !NumberUtils.biggerThanZero(query.getSpaceId())){
            throw new TripscrmException(ErrorCodeEnum.PARAM_INVALID);
        }
        ScrmTaskTotalEffectDataParam param = new ScrmTaskTotalEffectDataParam();
        ScrmTaskTotalEffectDataParam.Criteria criteria = param.or();
        criteria.andEnvEqualTo(EnvUtils.getEnvironment());
        criteria.andSpaceIdEqualTo(query.getSpaceId());

        if(Objects.nonNull(query.getDate())){
            criteria.andDateEqualTo(query.getDate());
        }
        if(Objects.nonNull(query.getCycleType())){
            criteria.andCycleTypeEqualTo(query.getCycleType().getCode().byteValue());
        }
        param.appendOrderByClause(ScrmTaskTotalEffectDataParam.OrderCondition.DATE, ScrmTaskTotalEffectDataParam.SortType.DESC);

        return param;
    }
}
