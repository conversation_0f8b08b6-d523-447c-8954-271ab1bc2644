package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.mapper.tddl.SpaceMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceDO;
import com.alibaba.tripscrm.domain.WeChatUserInfo;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 定时任务：查询space表中spaceId下的企业微信账号集合的信息
 * @date 2024/8/21
 */
@Slf4j
@Component
public class WxSpaceId2WxUserInfoProcessor extends JavaProcessor {

    @Resource
    private LdbTairManager ldbTairManager;

    @Resource
    private SpaceMapper spaceMapper;

    @Resource
    private WechatUserService wechatUserService;

    private static final String WX_SPACE_ID_2_WX_USER_INFO_PREFIX = "wx_space_id_2_wx_user_info_prefix";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        List<Long> spaceIds = spaceMapper.list().stream()
                .map(SpaceDO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<WeChatUserInfo>> spaceId2WxUserInfo = new HashMap<>(spaceIds.size());
        for (Long spaceId : spaceIds) {
            List<WechatUserDTO> wechatUserDTOS = wechatUserService.listBySpaceId(spaceId);
            if (CollectionUtils.isEmpty(wechatUserDTOS)) {
                continue;
            }
            List<WeChatUserInfo> validWechatUserList = wechatUserDTOS.stream()
                    // 过滤非正常的企微号
                    .filter(wechatUserDTO -> Objects.equals(WechatUserStatusEnum.ACTIVE, WechatUserStatusEnum.of(wechatUserDTO.getStatus())))
                    .map(wechatUserDTO -> {
                        WeChatUserInfo userInfo = new WeChatUserInfo();
                        BeanUtils.copyProperties(wechatUserDTO, userInfo);
                        return userInfo;
                    })
                    .collect(Collectors.toList());
            spaceId2WxUserInfo.put(spaceId, validWechatUserList);
        }
        // 过期时间为24小时
        ldbTairManager.put(WX_SPACE_ID_2_WX_USER_INFO_PREFIX, JSONObject.toJSONString(spaceId2WxUserInfo), 24 * 60 * 60);
        return new ProcessResult(true);
    }
}
