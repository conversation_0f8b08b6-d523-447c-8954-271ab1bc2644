package com.alibaba.tripscrm.service.middleware.metaq.consumer.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.risk.SourceTypeEnum;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 主任务触发，内部消费
 *
 * <AUTHOR>
 * @date 2023-08-30 17:26:58
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service(value = "innerMainTaskTriggerConsumer")
public class InnerMainTaskTriggerConsumer implements MessageListenerConcurrently {
    private final TaskExecuteStrategy taskExecuteStrategy;
    private final TaskService taskService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("内部主任务触发，消息处理", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("内部主任务触发，消息处理失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        TaskExecuteParam param;
        try {
            // 解析数据
            PlatformLogUtil.logInfo("内部主任务触发，消息处理开始", LogListUtil.newArrayList(message));
            param = JSONObject.parseObject(message, new TypeReference<TaskExecuteParam>() {
            });
            if (Objects.isNull(param)) {
                PlatformLogUtil.logFail("内部主任务触发，消息处理参数为空");
                return false;
            }
            // 设置租户
            String corpId = taskService.getCorpIdByTaskId(param.getTaskId());
            SpaceInfoThreadLocalUtils.setCorpId(corpId).setSourceId(param.getTaskId().toString()).setSourceType(SourceTypeEnum.TASK_OPT);
            // 运行主任务处理器
            TripSCRMResult<String> result = taskExecuteStrategy.runMain(param);
            // 处理完成，打印日志
            PlatformLogUtil.logInfo("内部主任务触发，消息处理结束", LogListUtil.newArrayList(param.getTaskId(), param.getInstanceId(), result));
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("内部主任务触发，消息处理异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }
}
