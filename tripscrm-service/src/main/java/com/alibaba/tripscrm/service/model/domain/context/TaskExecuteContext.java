package com.alibaba.tripscrm.service.model.domain.context;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.taobao.unifiedsession.core.commons.limiter.RateLimiter;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 任务执行上下文
 */
@Data
public class TaskExecuteContext {
    /**
     * 任务调度时间
     */
    private Date scheduleTime;

    /**
     * 任务开始执行时间
     */
    private Long startTime;

    /********************   入参对应   *********************/

    /**
     * 任务ID
     **/
    private Long taskId;

    /**
     * 是否是子任务实例
     **/
    private Boolean isSub;

    /**
     * 触发方式
     **/
    private TaskTriggerTypeEnum triggerType;

    /**
     * 任务执行扩展参数
     */
    private Map<String, Object> extInfo;

    /**
     * 要发送的图片列表
     */
    private List<String> imageUrlList;

    /********************   上下文设置   *********************/

    /**
     * 主任务实例ID
     **/
    private Long mainTaskInstanceId;

    /**
     * 任务实例ID（主or子）
     **/
    private Long instanceId;

    /**
     * 任务数据快照（保存在 主or子 任务实例中）
     */
    private TaskInfoDO taskInfoDOSnapshot;

    /**
     * AB分桶数据快照（保存在 主or子 任务实例中）
     */
    private List<AbTestBucketVO> abBucketListSnapshot;

    /**
     * 素材数据快照（保存在 主or子 任务实例中）
     */
    private MaterailInfoDO materialSnapshot;

    /**
     * 任务执行限流器
     */
    private RateLimiter rateLimiter;

    /**
     * 是否是重试链路
     **/
    private Boolean retry = Boolean.FALSE;

    @JSONField(serialize = false)
    /** 查询后的数据,存储主任务查询的统计数据，用来判断是否需要拆分和调度 **/
    private TaskDataVO taskDataVO;

    @JSONField(serialize = false)
    /** 拆分后的待办任务列表，存储每个子任务要执行的基础数据和数据 **/
    private List<TodoTaskVO> todoTaskVOList;

    /**
     * 要返回的信息body，主要用于外部触发
     **/
    private String result;

    /**
     * 是否是测试链路
     **/
    private Boolean testFlag = Boolean.FALSE;

    /**
     * 任务执行结果，默认为不成功
     **/
    private Boolean runTaskResult = Boolean.FALSE;

    /**
     * sequenceId
     */
    private Long asyncTaskSequenceId;
}
