package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.WechatOfficialAccountArticleDO;
import com.alibaba.tripscrm.domain.WechatArticleDTO;
import com.alibaba.tripscrm.domain.request.WechatArticleRequest;
import com.alibaba.tripscrm.domain.request.WechatOfficialAccountCheckSubscribeRequest;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.service.fusionchat.ProfileService;
import com.alibaba.tripscrm.service.service.wechat.WechatOfficialAccountArticleService;
import com.alibaba.tripscrm.service.wechat.WechatOfficialAcountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 微信公众号相关接口
 *
 * <AUTHOR>
 * @date 2023-08-10 18:16:22
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@HSFProvider(serviceInterface = WechatOfficialAcountService.class)
public class WechatOfficialAcountServiceImpl implements WechatOfficialAcountService {
    private final WechatOfficialAccountArticleService wechatOfficialAccountArticleService;
    private final ProfileService profileService;

    /**
     * 获取微信公众号文章接口
     *
     * @param request
     * @return
     */
    @Override
    @ServiceLog("查询微信公众号文章")
    public TripSCRMResult<PageInfoDTO<WechatArticleDTO>> getArticleListInWechatOfficial(WechatArticleRequest request) {
        PageInfoDTO<WechatArticleDTO> pageInfoDTO = new PageInfoDTO<>();

        String requestStr = JSON.toJSONString(request);
        Map<String, Object> paramMap = buildParamMap(request, requestStr);

        int total = wechatOfficialAccountArticleService.countByParam(paramMap);
        if (total < 1) {
            return TripSCRMResult.success(pageInfoDTO);
        }

        List<WechatOfficialAccountArticleDO> articleDOS = wechatOfficialAccountArticleService.listByParam(paramMap);
        if (CollectionUtils.isEmpty(articleDOS)) {
            return TripSCRMResult.success(pageInfoDTO);
        }

        List<WechatArticleDTO> articleDTOS = articleDOS.stream().map(e -> {
            String articleJsonStr = JSON.toJSONString(e);
            WechatArticleDTO wechatArticleDTO = JSONObject.parseObject(articleJsonStr, WechatArticleDTO.class);
            return wechatArticleDTO;
        }).collect(Collectors.toList());

        pageInfoDTO.setList(articleDTOS);
        pageInfoDTO.setTotal((long) total);
        pageInfoDTO.setPageNum(request.getPageNum());
        pageInfoDTO.setPageSize(request.getPageSize());

        return TripSCRMResult.success(pageInfoDTO);
    }

    @Override
    @ServiceLog("查询指定Id的微信公众号文章")
    public TripSCRMResult<WechatArticleDTO> getArticleById(Long id) {
        WechatOfficialAccountArticleDO wechatArticleDO = wechatOfficialAccountArticleService.getById(id);
        if (Objects.isNull(wechatArticleDO)) {
            return null;
        }
        WechatArticleDTO wechatArticleDTO = JSONObject.parseObject(JSON.toJSONString(wechatArticleDO), WechatArticleDTO.class);
        return TripSCRMResult.success(wechatArticleDTO);
    }

    @Override
    @ServiceLog("判断用户是否订阅公众号")
    public TripSCRMResult<Boolean> hasSubscribed(WechatOfficialAccountCheckSubscribeRequest request) {
        if (request == null || !StringUtils.hasLength(request.getUid())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TripSCRMResult<String> result = profileService.getProfileValue(request.getUid(), "rt_fliggy_wx_official_account_focus");
        if (result == null) {
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (!result.isSuccess()) {
            return TripSCRMResult.fail(TripSCRMErrorCode.codeOf(result.getCode()));
        }

        return TripSCRMResult.success(Objects.equals("1", result.getData()));
    }

    @NotNull
    private Map<String, Object> buildParamMap(WechatArticleRequest request, String requestStr) {
        Map<String, Object> paramMap = JSONObject.parseObject(requestStr, Map.class);
        paramMap.put("pageIndex", request.getPageIndex());
        return paramMap;
    }
}