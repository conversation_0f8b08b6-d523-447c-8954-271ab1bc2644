package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.RobotStatusEnum;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityStatusEnum;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.request.SopActivityQueryRequest;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatContactMeActivitySopConfigVO;
import com.alibaba.tripscrm.service.service.activity.ActivitySopService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig.WECHAT_CONTACT_ME_USER_REPLACE_COUNT_MAX;

/**
 * 个人活码不可用账号更换
 *
 * <AUTHOR>
 * @Date 2024/4/23 14:48
 **/
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatContactMeUpdateProcessor extends JavaProcessor {

    private final WechatUserService wechatUserService;
    private final WechatCustomerService wechatCustomerService;
    private final LdbTairManager ldbTairManager;
    private final ActivitySopService<WechatContactMeActivitySopConfigVO> activitySopService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        Map<String, Integer> usedUserMap = new HashMap<>();
        List<String> disabledUserIdList = new ArrayList<>();
        // 客户数过滤
        customerCountFilter(usedUserMap, disabledUserIdList);
        // 查询和更新活码信息
        getAndUpdateActivitySopList(usedUserMap, disabledUserIdList);

        return new ProcessResult(true);
    }

    private void getAndUpdateActivitySopList(Map<String, Integer> usedUserMap, List<String> disabledUserIdList) {
        SopActivityQueryRequest request = new SopActivityQueryRequest();
        request.setSopType(SopTypeEnum.WECHAT_CONTACT_ME.getCode());
        request.setSpaceId(10L);
        request.setPageSize(100);
        request.setPageNum(1);
        request.setStatus(ActivityStatusEnum.ONLINE.getStatus());
        PageInfo<ActivitySopVO<WechatContactMeActivitySopConfigVO>> activitySopVOPageInfo;
        do {
            activitySopVOPageInfo = activitySopService.pageList(request);
            List<ActivitySopVO<WechatContactMeActivitySopConfigVO>> activitySopList = activitySopVOPageInfo.getList();
            if (CollectionUtils.isEmpty(activitySopList)) {
                return;
            }
            updateSop(usedUserMap, disabledUserIdList, activitySopList);
            request.setPageNum(request.getPageNum() + 1);

        } while (Objects.nonNull(activitySopVOPageInfo) && activitySopVOPageInfo.isHasNextPage());

    }

    private void updateSop(Map<String, Integer> usedUserMap, List<String> disabledUserIdList, List<ActivitySopVO<WechatContactMeActivitySopConfigVO>> activitySopList) {
        for (ActivitySopVO<WechatContactMeActivitySopConfigVO> activitySopVO : activitySopList) {
            try {
                List<String> needReplaceUserIds = new ArrayList<>();

                activitySopVO.getConfig().getUserIdList().forEach(userId -> {
                    if (disabledUserIdList.contains(userId)) {
                        needReplaceUserIds.add(userId);
                    }
                });
                if (needReplaceUserIds.isEmpty()) {
                    continue;
                }
                Set<String> newUserIds = replaceDisabledUsers(activitySopVO.getConfig().getUserIdList(), needReplaceUserIds, usedUserMap);
                if (Objects.isNull(newUserIds) || newUserIds.isEmpty()) {
                    continue;
                }
                PlatformLogUtil.logInfo("个人活码替换账号", LogListUtil.newArrayList(activitySopVO.getActivityId(), activitySopVO.getActivityName(), activitySopVO.getConfig().getUserIdList(), newUserIds, needReplaceUserIds));
                activitySopVO.getConfig().setUserIdList(newUserIds.stream().collect(Collectors.toList()));
                //更新活动
                activitySopService.update(activitySopVO);
                PlatformLogUtil.logInfo("个人活码替换账号成功", LogListUtil.newArrayList(activitySopVO.getActivityId(), activitySopVO.getActivityName(), newUserIds, needReplaceUserIds));

            } catch (Exception e) {
                PlatformLogUtil.logException("更新活码信息失败", e.getMessage(), e, LogListUtil.newArrayList(activitySopVO));
            }

        }
    }


    private void customerCountFilter(Map<String, Integer> usedUserMap, List<String> disabledUserIdList) {

        WechatUserQuery query = new WechatUserQuery();
        query.setSpaceId(10L);
        List<WechatUserDTO> wechatUserList = wechatUserService.listByCondition(query);
        Long dayVersion = DateUtils.getDayVersion(new Date());
        for (WechatUserDTO wechatUserDTO : wechatUserList) {
            if (!wechatUserDTO.getStatus().equals(WechatUserStatusEnum.ACTIVE.getCode())) {
                disabledUserIdList.add(wechatUserDTO.getUserId());
                continue;
            }
            String corpId = wechatUserDTO.getCorpId();
            String key = TairConstant.WECHAT_USER_CUSTOMER_COUNT_PREFIX + dayVersion + "_" + corpId + "_" + wechatUserDTO.getUserId();
            Long customerCount = Optional.ofNullable(ldbTairManager.get(key))
                    .map(o -> (Long) o).orElse(-1L);
            if (customerCount < 0) {
                // 没有在缓存中，就走DB
                customerCount = wechatCustomerService.getCustomerCount(wechatUserDTO.getUserId(), false);
            }

            // 大于19000不可用账号
            if (customerCount >= SwitchConfig.WECHAT_CONTACT_ME_USER_MAX_CUSTOMER_COUNT) {
                disabledUserIdList.add(wechatUserDTO.getUserId());
                continue;
            }

            // 小于15000可替换账号
            if (customerCount < SwitchConfig.WECHAT_CONTACT_ME_USER_REPLACE_CUSTOMER_COUNT_THRESHOLD && Objects.equals(RobotStatusEnum.ONLINE.getCode(), wechatUserDTO.getOnlineStatus())) {
                usedUserMap.put(wechatUserDTO.getUserId(), WECHAT_CONTACT_ME_USER_REPLACE_COUNT_MAX);
            }

        }
        PlatformLogUtil.logInfo("个人活码可用账号/不可用账号", LogListUtil.newArrayList(usedUserMap, disabledUserIdList));
    }

    /**
     * 替换不可用账号 如果返回空则表示没有可替换账号 活动不用更新
     *
     * @param currentUserIds     当前配置的账号ID列表
     * @param needReplaceUserIds 需要替换的账号ID列表
     */
    private Set<String> replaceDisabledUsers(List<String> currentUserIds, List<String> needReplaceUserIds, Map<String, Integer> usedUserMap) {

        // 获取当前配置中的所有有效账号（排除需要替换的）
        Set<String> existingUserIds = new HashSet<>(currentUserIds);
        existingUserIds.removeAll(needReplaceUserIds);

        // 获取可用账号，排除已存在的账号
        List<String> availableUserIds = getAvailableUserIds(needReplaceUserIds.size(), existingUserIds, usedUserMap);
        if (availableUserIds.isEmpty()) {
            PlatformLogUtil.logFail("个人活码无可替换账号", LogListUtil.newArrayList(needReplaceUserIds.size(), needReplaceUserIds));
            return null;
        }

        existingUserIds.addAll(availableUserIds);

        if (availableUserIds.size() < needReplaceUserIds.size()) {
            List<String> lastElements = needReplaceUserIds.subList(needReplaceUserIds.size() - availableUserIds.size(), needReplaceUserIds.size());

            existingUserIds.addAll(lastElements);
            PlatformLogUtil.logFail("个人活码可用账号不足", LogListUtil.newArrayList(needReplaceUserIds.size(), availableUserIds.size(), currentUserIds, needReplaceUserIds));
        }

        return existingUserIds;
    }

    /**
     * 获取指定数量的可用账号ID，确保不与已存在账号重复
     *
     * @param count          需要的账号数量
     * @param excludeUserIds 需要排除的账号ID集合
     * @return 可用账号ID列表
     */
    private List<String> getAvailableUserIds(int count, Set<String> excludeUserIds, Map<String, Integer> usedUserMap) {
        List<String> result = new ArrayList<>();

        // 获取所有可用账号（使用次数小于10且不在排除列表中）
        List<String> candidateUserIds = usedUserMap.entrySet().stream()
                .map(Map.Entry::getKey)
                .filter(userId -> !excludeUserIds.contains(userId))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(candidateUserIds)) {
            PlatformLogUtil.logFail("没有可用的候选账号", LogListUtil.newArrayList(count, excludeUserIds, usedUserMap));
            return result;
        }

        // 随机打乱候选列表
        Collections.shuffle(candidateUserIds);

        // 选择指定数量的账号
        int selectCount = Math.min(count, candidateUserIds.size());
        for (int i = 0; i < selectCount; i++) {
            String selectedUserId = candidateUserIds.get(i);
            result.add(selectedUserId);
            // 更新使用计数
            usedUserMap.put(selectedUserId, usedUserMap.getOrDefault(selectedUserId, 0) - 1);
            if (usedUserMap.get(selectedUserId) <= 0) {
                usedUserMap.remove(selectedUserId);
            }
        }

        PlatformLogUtil.logInfo("获取可用账号", LogListUtil.newArrayList(count, result, candidateUserIds.size()));

        return result;
    }

}
