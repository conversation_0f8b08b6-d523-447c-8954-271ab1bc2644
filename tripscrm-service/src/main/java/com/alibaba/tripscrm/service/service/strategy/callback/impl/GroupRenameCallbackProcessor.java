package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.callback.TaskAsyncRequestCallbackProcessor;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;


/**
 * 修改群名回调【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupRenameCallbackProcessor implements ProxyCallbackProcessor {
    private final TaskAsyncRequestCallbackProcessor taskAsyncRequestCallbackProcessor;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.GROUP_RENAME_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        processTaskExecuteResult(scrmCallbackMsg);
        return true;
    }

    private void processTaskExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            taskAsyncRequestCallbackProcessor.process(scrmCallbackMsg);
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方修改群名回调，处理任务异步执行结果出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }
}
