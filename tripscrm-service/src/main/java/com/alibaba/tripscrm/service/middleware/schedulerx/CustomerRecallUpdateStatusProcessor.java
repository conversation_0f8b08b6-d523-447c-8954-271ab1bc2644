package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmWechatCustomerRecallDO;
import com.alibaba.tripscrm.dal.repository.CustomerRelationRepository;
import com.alibaba.tripscrm.service.enums.wechat.RescallStatusTypeEnum;
import com.alibaba.tripscrm.service.model.dto.wechat.ScrmWechatCustomerRecallDTO;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerRecallService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 好友召回状态更新定时任务
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerRecallUpdateStatusProcessor extends JavaProcessor {
    private final WechatCustomerRecallService wechatCustomerRecallService;
    private final CustomerRelationRepository customerRelationRepository;


    @Override
    public ProcessResult process(JobContext context) throws Exception {
        int pageNum = 1;
        int pageSize = 500;
        for(Long minId = 0L;;){
            List<ScrmWechatCustomerRecallDTO> scrmWechatCustomerRecallDTOS = wechatCustomerRecallService.selectByStatus(RescallStatusTypeEnum.IN_PROGRESS_RECALL, pageNum, pageSize, minId);
            // 处理当前页的数据 更新七天内好友召回的状态 默认七天 1,状态在关系表中变为好友,就视为为召回成功 2,7天后未改变就视为为召回失败
            for (ScrmWechatCustomerRecallDTO scrmWechatCustomerRecallDTO : scrmWechatCustomerRecallDTOS) {
                try {
                    extracted(scrmWechatCustomerRecallDTO);
                } catch (Exception e) {
                    PlatformLogUtil.logException("好友召回状态更新定时任务异常", e.getMessage(), e, LogListUtil.newArrayList(scrmWechatCustomerRecallDTO.getExternalUserId(), scrmWechatCustomerRecallDTO.getUserId()));
                }
            }
            // 检查是否还有下一页数据
            if (scrmWechatCustomerRecallDTOS.size()< pageSize || CollectionUtils.isEmpty(scrmWechatCustomerRecallDTOS)) {
                break;
            }
            minId = scrmWechatCustomerRecallDTOS.stream().mapToLong(ScrmWechatCustomerRecallDTO::getId).max().orElse(0L);
        }
        return new ProcessResult(true);
    }

    private void extracted(ScrmWechatCustomerRecallDTO scrmWechatCustomerRecallDTO) {
        CustomerRelationDO relationDO = customerRelationRepository.getByUniqueKey(scrmWechatCustomerRecallDTO.getUserId(), scrmWechatCustomerRecallDTO.getExternalUserId(), scrmWechatCustomerRecallDTO.getCorpId());
        if (Objects.isNull(relationDO)) {
            return ;
        }
        ScrmWechatCustomerRecallDO recordDO = new ScrmWechatCustomerRecallDO();
        //成为好友关系->召回成功
        if (CustomerRelationStatusEnum.FRIEND.getCode().equals(relationDO.getStatus().intValue())) {
            recordDO.setId(scrmWechatCustomerRecallDTO.getId());
            recordDO.setStatus(RescallStatusTypeEnum.SUCCESSFUL_RECALL.getType());
            recordDO.setAddTime(scrmWechatCustomerRecallDTO.getAddTime());
            int i = wechatCustomerRecallService.updateByPrimaryKey(recordDO);
            if (i < 1) {
                PlatformLogUtil.logFail("好友召回状态更新失败", LogListUtil.newArrayList(scrmWechatCustomerRecallDTO.getExternalUserId(), scrmWechatCustomerRecallDTO.getUserId()));
            }
            return ;
        }
        //不是好友关系 ->查看请求是否超过七天,超过则视为召回失败
        if ( DateUtils.calulateDays(scrmWechatCustomerRecallDTO.getRecallTime(),new Date())>7) {
            recordDO.setId(scrmWechatCustomerRecallDTO.getId());
            recordDO.setStatus(RescallStatusTypeEnum.FAILED_RECALL.getType());
            int i = wechatCustomerRecallService.updateByPrimaryKey(recordDO);
            if (i < 1) {
                PlatformLogUtil.logFail("好友召回状态更新失败", LogListUtil.newArrayList(scrmWechatCustomerRecallDTO.getExternalUserId(), scrmWechatCustomerRecallDTO.getUserId()));
            }
        }

    }


}
