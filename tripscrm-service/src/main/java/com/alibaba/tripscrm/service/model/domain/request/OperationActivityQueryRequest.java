package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OperationActivityQueryRequest extends BasePageRequest implements Serializable {
    /**
     * 活动id
     */
    @Min(value = 1L, message = "id非法")
    private Long id;

    /**
     * 活动类型
     *
     * @see com.alibaba.tripzoo.admin.enums.activity.OperationActivityTypeEnum
     */
    @NotNull(message = "活动类型不可为空")
    @Min(value = 1L, message = "活动类型非法")
    private Integer type;

    /**
     * 活动名称
     */
    @Length(min = 1, message = "活动名称不可为空")
    private String nameLike;
}
