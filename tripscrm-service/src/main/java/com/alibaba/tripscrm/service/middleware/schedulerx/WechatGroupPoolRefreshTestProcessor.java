package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.middleware.hts.WechatGroupPoolRefreshTestTaskExecutor;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/13 17:48
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatGroupPoolRefreshTestProcessor extends JavaProcessor {
    private final WechatUserService wechatUserService;
    private final WechatGroupPoolRefreshTestTaskExecutor wechatGroupPoolRefreshTestTaskExecutor;

    @Switch(description = "创建群聊最大延迟秒数", name = "createGroupDelaySeconds")
    public static Integer createGroupDelaySeconds = 3000;

    @Switch(description = "企微号列表，逗号分隔", name = "wechatUserIdList")
    public static String wechatUserIdList = "ZhuZhongYong,FuBuXueFuBuJiuQiongBuXueQiongBuJin";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            List<String> userIdList = Arrays.stream(wechatUserIdList.split(",")).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIdList)) {
                return new ProcessResult(true);
            }

            SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.sellerCorpId);
            WechatUserQuery query = new WechatUserQuery();
            query.setCorpId(SwitchConfig.sellerCorpId);
            query.setUserIdList(userIdList);
            List<WechatUserDTO> wechatUserList = wechatUserService.listValidWechatUserByCondition(query);
            if (CollectionUtils.isEmpty(wechatUserList)) {
                PlatformLogUtil.logFail("测试定时任务，刷新同步商家企业用户运营组群聊池，企微号列表为空", LogListUtil.newArrayList());
                return new ProcessResult(true);
            }

            for (WechatUserDTO wechatUserDTO : wechatUserList) {
                refresh(wechatUserDTO.getUserId());
            }

            PlatformLogUtil.logInfo("测试定时任务，刷新同步商家企业用户运营组群聊池，同步完毕", LogListUtil.newArrayList());
            return new ProcessResult(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("测试定时任务，刷新同步商家企业用户运营组群聊池，发生异常", e.getMessage(), e, LogListUtil.newArrayList());
            return new ProcessResult(false);
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    public void refresh(String userId) {
        PlatformLogUtil.logInfo("测试定时任务，刷新同步商家企业用户运营组群聊池，企微账号下可用群聊池数量不足，开始提交创建新群聊池hts任务", LogListUtil.newArrayList(userId));

        for (int i = 0; i < SwitchConfig.wechatGroupCreateCountPerHour; i++) {
            Date executeTime = new Date(System.currentTimeMillis() + ThreadLocalRandom.current().nextLong(createGroupDelaySeconds * 1000L));
            wechatGroupPoolRefreshTestTaskExecutor.register(SwitchConfig.sellerCorpId, userId, executeTime);
        }
        PlatformLogUtil.logInfo("测试定时任务，刷新同步商家企业用户运营组群聊池，企微账号下可用群聊池数量不足，提交创建新群聊池hts任务完毕", LogListUtil.newArrayList(userId));
    }
}
