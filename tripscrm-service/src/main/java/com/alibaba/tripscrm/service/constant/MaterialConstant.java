package com.alibaba.tripscrm.service.constant;

/**
 * <AUTHOR>
 * @create 2024/2/3 00:14
 */
public interface MaterialConstant {

    String HOTEL_H5_PRE = "https://market.m.taobao.com/app/trip/h5-hotel-detail/pages/detail/index.html?shid=";

    String H5_ROUTE_PRE = "https://scrm.feizhu.com/router";

    String GOODS_MINI_PROGRAM_URL_PRE = "pages/holiday/detail/index?id=";

    /**
     * 商品pc链接
     */
    String GOODS_PC_URL = "https://traveldetail.fliggy.com/item.htm?id=";

    /**
     * 视频号直播链接前缀
     */
    String CHANNEL_LIVE_URL_PRE ="pages/utils/channel/index?" ;

    /**
     * 视频号-短视频链接前缀
     */
    String CHANNEL_VIDEO_URL_PRE ="pages/utils/channel/video?" ;


    /**
     * 国内机票Listing页H5链接
     */
    String DMESTIC_FLIGHT_LISTING_H5 = "https://outfliggys.m.taobao.com/app/trip/rx-flight-eco/pages/listing";

    /**
     * 国内机票Listing页H5链接-PRE
     */
    String DMESTIC_FLIGHT_LISTING_H5_PRE = "https://outfliggys.wapa.taobao.com/app/trip/rx-flight-eco/pages/listing";

    /**
     * 国内机票OTA页H5链接
     */
    String DMESTIC_FLIGHT_OTA_H5_PRE = "https://market.m.taobao.com/app/trip/rx-traffic/pages/ota?";

    /**
     * 火车票Listing页H5链接
     */
    String TRAIN_TICKET_LISTING_H5_PRE = "https://outfliggys.m.taobao.com/app/trip/rx-train-main/pages/listing?_use_stream=1&_fli_online=true";

    /**
     * 国内酒店Listing页H5链接
     */
    String NON_PACKAGE_HOTEL_LISTING_H5 = "https://outfliggys.m.taobao.com/app/trip/rx-hotel-listing/pages/home";

    /**
     * 国内酒店Listing页H5链接-预发
     */
    String NON_PACKAGE_HOTEL_LISTING_H5_PRE = "https://outfliggys.wapa.taobao.com/app/trip/rx-hotel-listing/pages/home";


    /**
     * 国际机票Listing页H5链接
     */
    String INTERNATIONAL_FLIGHT_LISTING_H5 = "https://market.m.taobao.com/app/trip/rx-iflight-eco/pages/listing";

    /**
     * 国际机票Listing页H5链接-预发
     */
    String INTERNATIONAL_FLIGHT_LISTING_H5_PRE = "https://market.wapa.taobao.com/app/trip/rx-iflight-eco/pages/listing";

    /**
     * 国际机票OTA页H5链接
     */
    String INTERNATIONAL_FLIGHT_OTA_H5_PRE = "https://market.m.taobao.com/app/trip/rx-iflight-eco/pages/ota?";

    /**
     * 度假-景点门票Listing页H5链接
     */
    String VACATION_SCENIC_TICKET_LISTING_H5_PRE = "https://market.m.taobao.com/app/trip/rx-travel-search/pages/list?nav=jing_dian_men_piao";

    /**
     * 度假-度假小搜Listing页H5链接
     */
    String VACATION_SEARCH_TICKET_LISTING_H5_PRE = "https://market.m.taobao.com/app/trip/rx-travel-search/pages/list";

    /**
     * 度假-默认链接
     */
    String VACATION_DEFAULT_URL = "https://market.m.taobao.com/app/trip/rx-travel-search/pages/sug";


    String DEFAULT_TITLE = "飞猪定酒店机票火车租车门票";

    String SINGLE_CHAT_APP_ID = "gh_e4c5d4d5bc2f@app";

    String OTHER_APP_ID = "wx6a96c49f29850eb5";

    /**
     * 飞猪支付宝小程序应用id
     */
    String FLY_ALIPAY_APP_ID = "2018081461095002";
    /**
     * 飞猪支付宝小程序应用名称
     */
    String FLY_ALIPAY_APP_NAME = "飞猪订酒店机票火车票汽车票门票";
    /**
     * 飞猪小程序应用logo
     */
    String FLY_ALIPAY_APP_LOGO = "https://gw.alicdn.com/imgextra/i3/O1CN01crzX8B1tQQILahTzm_!!6000000005896-2-tps-600-600.png";

    /**
     * 素材id列表
     */
    String MATERIAL_ID_LIST = "materialIdList";
}
