package com.alibaba.tripscrm.service.constant;

/**
 * <AUTHOR>
 * @create 2024/2/3 00:14
 */
public interface MaterialConstant {

    String HOTEL_H5_PRE = "https://market.m.taobao.com/app/trip/h5-hotel-detail/pages/detail/index.html?shid=";

    String H5_ROUTE_PRE = "https://scrm.feizhu.com/router";

    String GOODS_MINI_PROGRAM_URL_PRE = "pages/holiday/detail/index?id=";

    /**
     * 商品pc链接
     */
    String GOODS_PC_URL = "https://traveldetail.fliggy.com/item.htm?id=";

    /**
     * 视频号直播链接前缀
     */
    String CHANNEL_LIVE_URL_PRE ="pages/utils/channel/index?" ;

    /**
     * 视频号-短视频链接前缀
     */
    String CHANNEL_VIDEO_URL_PRE ="pages/utils/channel/video?" ;


    /**
     * 国内机票Listing页H5链接
     */
    String DMESTIC_FLIGHT_LISTING_H5_PRE = "https://outfliggys.m.taobao.com/app/trip/rx-flight-eco/pages/listing";

    /**
     * 火车票Listing页H5链接
     */
    String TRAIN_TICKET_LISTING_H5_PRE = "https://outfliggys.m.taobao.com/app/trip/rx-train-main/pages/listing?_use_stream=1&_fli_online=true";

    /**
     * 国内酒店Listing页H5链接
     */
    String NON_PACKAGE_HOTEL_LISTING_H5_PRE = "https://outfliggys.m.taobao.com/app/trip/rx-hotel-listing/pages/home";

    /**
     * 国际机票Listing页H5链接
     */
    String INTERNATIONAL_FLIGHT_LISTING_H5_PRE = "https://market.m.taobao.com/app/trip/rx-iflight-eco/pages/listing";

    /**
     * 度假-景点门票Listing页H5链接
     */
    String VACATION_SCENIC_TICKET_LISTING_H5_PRE = "https://market.m.taobao.com/app/trip/rx-travel-search/pages/list?nav=jing_dian_men_piao";

    /**
     * 度假-度假小搜Listing页H5链接
     */
    String VACATION_SEARCH_TICKET_LISTING_H5_PRE = "https://market.m.taobao.com/app/trip/rx-travel-search/pages/list";

    /**
     * 度假-默认链接
     */
    String VACATION_DEFAULT_URL = "https://market.m.taobao.com/app/trip/rx-travel-search/pages/sug";


    String DEFAULT_TITLE = "飞猪定酒店机票火车租车门票";

    String SINGLE_CHAT_APP_ID = "gh_e4c5d4d5bc2f@app";

    String OTHER_APP_ID = "wx6a96c49f29850eb5";
}
