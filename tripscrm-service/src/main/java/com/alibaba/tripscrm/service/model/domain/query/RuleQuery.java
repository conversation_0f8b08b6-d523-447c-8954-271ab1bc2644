package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RuleQuery extends BasePageRequest {
    /**
     * Id
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则组id
     */
    private Long groupId;

    /**
     * 规则类型
     */
    private Byte type;

    /**
     * 目标id
     */
    private String targetId;

    /**
     * 目标类型
     */
    private Byte targetType;

    /**
     * 创建者
     */
    private String creatorId;

    /**
     * 管理员
     */
    private String memberIds;

    /**
     * 创建时间（开始时间）
     */
    private Date createStartTime;

    /**
     * 创建时间（结束时间）
     */
    private Date createEndTime;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 环境
     */
    private String env;

    /**
     * 类型列表
     */
    private List<Byte> typeList;

    /**
     * 是否删除：0-未删除,1-已删除
     */
    private Byte deleted;
}
