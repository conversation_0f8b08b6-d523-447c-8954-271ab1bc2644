package com.alibaba.tripscrm.service.service.activity;

import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.activity.ActivityInfoQuery;
import com.alibaba.tripscrm.service.enums.activity.ActivityStatusEnum;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/14
 */
public interface ActivityInfoService {
    /**
     * 添加活动信息
     *
     * @param infoDO
     * @return
     */
    Long add(ActivityInfoDO infoDO);

    /**
     * 更新活动信息
     *
     * @param infoDO
     * @return
     */
    Integer update(ActivityInfoDO infoDO);

    /**
     * 根据活动id查询活动信息
     *
     * @param activityId
     * @return
     */
    ActivityInfoDO queryInfoById(Long activityId);

    /**
     * 批量查询
     * @param activityIdList
     * @return
     */
    List<ActivityInfoDO> queryInfoByIdList(List<Long> activityIdList);

    /**
     * 删除活动
     *
     * @param activityId
     * @return
     */
    Integer deleteById(Long activityId);

    /**
     * 查询活动列表
     *
     * @param query
     * @return
     */
    PageInfo<ActivityInfoDO> list(ActivityInfoQuery query);

    /**
     * 重名校验
     *
     * @param spaceId
     * @param name
     * @param id
     * @return
     */
    boolean checkDuplicateName(Long spaceId, String name, Long id);

    /**
     * 乐观锁更新活动状态
     *
     * @param id        活动 Id
     * @param oldStatus 旧状态
     * @param newStatus 新状态
     * @return 修改行数
     */
    Integer compareAndUpdateStatus(Long id, ActivityStatusEnum oldStatus, ActivityStatusEnum newStatus);

    /**
     * 批量查询
     * @param idList
     * @return
     */
    List<ActivityInfoDO> selectByIdList(List<Long> idList);
}
