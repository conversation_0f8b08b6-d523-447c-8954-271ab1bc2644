package com.alibaba.tripscrm.service.middleware.metaq.consumer.share;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.service.task.ability.old.second.FliggyPlayService;
import com.alibaba.tripscrm.service.service.task.ability.old.second.TouchService;
import com.fliggy.fliggyplaycore.client.share.model.ShareActInfoDTO;
import com.taobao.ateye.annotation.Switch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 玩法人传人助力成功消息
 */
@Slf4j
@Service(value = "playShareRelationConsumer")
public class PlayShareRelationConsumer implements MessageListenerConcurrently {
    @Resource
    private TouchService touchService;
    @Resource
    private FliggyPlayService fliggyPlayService;
    @Switch(description = "发送裂变助力消息提醒玩法ID列表", name = "shareReminderPlayIds")
    public static String shareReminderPlayIds = "";
    @Switch(description = "发送裂变任务完成触达策略ID", name = "successTaskTouchId")
    private static String successTaskTouchId = "product_BOunPXraspGKiuITzuwE";
    @Switch(description = "发送裂变助力成功触达策略ID", name = "successHelpTouchId")
    private static String successHelpTouchId = "product_emovMybuMDuHDdCznngh";
    @Switch(description = "裂变提醒消息文案title", name = "fissionRemindTitleText")
    public static String fissionRemindTitleText = "10元红包助力活动";

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logFail("玩法人传人助力成功消息", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("玩法人传人助力成功消息，消息处理失败", LogListUtil.newArrayList(msg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logFail("玩法人传人助力成功消息，解析数据", LogListUtil.newArrayList(message));
            JSONObject msJS = JSONObject.parseObject(message);
            String playId = msJS.getString("playId");
            if (!Arrays.asList(shareReminderPlayIds.split(",")).contains(playId)) {
                return true;
            }
            // 发起助力者
            Long sharerUserId = msJS.getLong("userId");
            // 查询主活动信息
            TripSCRMResult<ShareActInfoDTO> shareResult = fliggyPlayService.queryPlayActInfoByActId(playId, sharerUserId);
            PlatformLogUtil.logFail("玩法人传人助力成功消息，查询主活动信息", LogListUtil.newArrayList(message, sharerUserId, shareResult));
            if (Objects.isNull(shareResult) || !shareResult.isSuccess() || Objects.isNull(shareResult.getData())) {
                PlatformLogUtil.logFail("玩法人传人助力成功消息，查询主活动信息失败", LogListUtil.newArrayList(playId, sharerUserId));
                return true;
            }
            // 助力状态
            Long establishedNumber = shareResult.getData().getShareProgressDTO().getEstablishedNumber();
            Long sharedCumulativeNum = (long) shareResult.getData().getActConfig().getSharedCumulativeNum();
            Map<String, Object> ext = new HashMap<>();
            ext.put("establishedNumber", establishedNumber);
            ext.put("fissionRemindTitleText", fissionRemindTitleText);
            // 不同的状态发送不同的任务，发送裂变助力成功/完成任务
            String templateId = establishedNumber.equals(sharedCumulativeNum) ? successTaskTouchId : successHelpTouchId;
            // 微信小程序订阅消息助力
            Map<String, String> touchExtInfo = new HashMap<>();
            touchExtInfo.put("establishedNumber", establishedNumber.toString());
            touchExtInfo.put("fissionRemindTitleText", fissionRemindTitleText);
            if (!touchService.send(sharerUserId.toString(), templateId, "28202301121449543207", touchExtInfo)) {
                PlatformLogUtil.logFail("玩法人传人助力成功消息，调用触达失败", LogListUtil.newArrayList(message));
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("玩法人传人助力成功消息，调用触达异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
        return true;
    }

}
