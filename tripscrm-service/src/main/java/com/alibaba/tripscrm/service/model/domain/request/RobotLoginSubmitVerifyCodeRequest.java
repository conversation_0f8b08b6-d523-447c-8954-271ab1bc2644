package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class RobotLoginSubmitVerifyCodeRequest implements Serializable {
    private static final long serialVersionUID = -941098558095052966L;
    /**
     * requestId
     */
    @NotBlank(message = "requestId不可为空")
    private String requestId;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不可为空")
    private String verifyCode;

    /**
     * isv 类型
     */
    @NotBlank(message = "userId不可为空")
    private Integer isvType;
}