package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.dto.task.TargetDataDTO;
import com.alibaba.tripscrm.service.service.task.ability.data.query.common.CommonCustomerDataQueryProcessor;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:04:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerDataQueryProcessor extends AbstractTaskDataProcessor {
    private final CommonCustomerDataQueryProcessor commonCustomerDataQueryProcessor;

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.INVITE_JOIN_GROUP, TaskType.ROBOT_CHAT_MESSAGE);
    }

    /**
     * 从离线表中从获取发送数据
     *
     * @param context 任务执行上下文
     * @return TaskDataVO
     */
    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        Long taskInstanceId = context.getInstanceId();

        List<TargetDataDTO> targetDataList = commonCustomerDataQueryProcessor.queryFromTask(context.getTaskInfoDOSnapshot(), taskInstanceId, true, ActivityTargetTypeEnum.WX_UNION_ID);
        for (TargetDataDTO targetDataDTO : targetDataList) {
            TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
            dataBodyVO.setTargetId(targetDataDTO.getTargetId());
            dataBodyVO.setTargetType(targetDataDTO.getTargetType().getCode());
            dataBodyVO.setExtInfo(targetDataDTO.getExtInfo().toJSONString());
            dataVO.getData().add(dataBodyVO);
        }

        dataVO.setTotalCount((long) dataVO.getData().size());
        return dataVO;
    }
}

