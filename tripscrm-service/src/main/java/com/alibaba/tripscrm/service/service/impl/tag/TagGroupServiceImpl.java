package com.alibaba.tripscrm.service.service.impl.tag;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.TagGroupMapper;
import com.alibaba.tripscrm.dal.model.domain.data.TagGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.TagGroupParam;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.convert.TagGroupConverter;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.TagGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.TagInfoQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.api.service.TagService;
import com.alibaba.tripzoo.proxy.request.DelCustomerTagRequest;
import com.alibaba.tripzoo.proxy.request.GetCorpTagListRequest;
import com.alibaba.tripzoo.proxy.request.UpdateCustomerTagRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.TagInfoResponse;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/10 15:40
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TagGroupServiceImpl implements TagGroupService {
    private final TagGroupMapper tagGroupMapper;
    private final TagGroupConverter tagGroupConverter;
    private final TagInfoService tagInfoService;
    private final TagService tagService;

    @Override
    public Integer insertSelective(TagGroupDTO record) {
        if (Objects.isNull(record) || !NumberUtils.validLong(record.getSpaceId()) || !StringUtils.hasText(record.getName()) || !NumberUtils.validInteger(record.getTagType()) || Objects.isNull(TagTypeEnum.of(record.getTagType()))) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (NumberUtils.isEqual(record.getTagType(), TagTypeEnum.CROWD_TAG.getCode())) {
            if (!StringUtils.hasLength(record.getSource()) || !SwitchConfig.CROWD_TAG_SYNC_LABEL_LIST.contains(record.getSource())) {
                PlatformLogUtil.logFail(TripSCRMErrorCode.CROWD_TAG_SOURCE_IS_INVALID.getDescCn(), LogListUtil.newArrayList(record));
                throw new TripscrmException(TripSCRMErrorCode.CROWD_TAG_SOURCE_IS_INVALID);
            }
        }
        return tagGroupMapper.upsertSelective(tagGroupConverter.convert2DO(record));
    }

    @Override
    public TagGroupDTO selectById(Long id) {
        if (!NumberUtils.validLong(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return Optional.ofNullable(tagGroupMapper.selectByPrimaryKey(id)).map(tagGroupConverter::convert2DTO).orElse(null);
    }

    @Override
    public Integer deleteById(Long id, Boolean isSync) {
        if (!NumberUtils.validLong(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        TagInfoQuery query = new TagInfoQuery();
        query.setGroupId(id);
        query.setDeleted(IsDeleteEnum.NO.getCode());
        List<TagInfoDTO> tagInfoList = tagInfoService.selectByCondition(query);
        TagGroupDTO tagGroupDTO = selectById(id);
        // 如果标签组下仍有标签在使用
        if (!CollectionUtils.isEmpty(tagInfoList)) {
            // 如果不是企微标签或诸葛标签，则不允许删除
            if (!Lists.newArrayList(TagTypeEnum.CROWD_TAG, TagTypeEnum.ENTERPRISE_WECHAT_TAG).contains(TagTypeEnum.of(tagGroupDTO.getTagType()))) {
                throw new TripscrmException(TripSCRMErrorCode.TAG_GROUP_IS_NOT_EMPTY);
            }
            Integer effectLines;
            // 如果是企微标签，需要先把企微标签下的标签组删除
            effectLines = tagInfoService.deleteByIdList(tagInfoList.stream().map(TagInfoDTO::getId).collect(Collectors.toList()), isSync);
            // 如果删除成功数量不等于标签数量，则删除失败
            if (!NumberUtils.isEqual(effectLines, tagInfoList.size())) {
                throw new TripscrmException(TripSCRMErrorCode.TAG_GROUP_DELETE_FAIL_DUE_TO_TAG_DELETE_FAIL);
            }
        }
        return tagGroupMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<TagGroupDTO> selectByCondition(TagGroupQuery query) {
        TagGroupParam param = buildParam(query);
        return Optional.ofNullable(tagGroupMapper.selectByParam(param)).orElse(new ArrayList<>()).stream().map(tagGroupConverter::convert2DTO).collect(Collectors.toList());
    }

    @Override
    public Integer updateSelective(TagGroupDTO record, TagGroupQuery condition, Boolean isSync) {
        if (Objects.isNull(condition) || !NumberUtils.validLong(condition.getId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TagGroupDTO tagGroupDTO = selectById(condition.getId());
        if (Objects.isNull(tagGroupDTO) || !NumberUtils.validInteger(tagGroupDTO.getTagType()) || Objects.isNull(TagTypeEnum.of(tagGroupDTO.getTagType()))) {
            PlatformLogUtil.logFail("标签组数据更新失败，标签组不存在或标签组类型参数错误", LogListUtil.newArrayList(record, condition));
            return 0;
        }
        if (!StringUtils.pathEquals(tagGroupDTO.getName(), record.getName()) && Objects.equals(TagTypeEnum.of(tagGroupDTO.getTagType()), TagTypeEnum.ENTERPRISE_WECHAT_TAG) && Objects.nonNull(isSync) && isSync) {
            if (!StringUtils.hasLength(tagGroupDTO.getSource())) {
                PlatformLogUtil.logFail("调用企微更新标签组接口出错，标签组source为空", LogListUtil.newArrayList(record, condition));
                return 0;
            }
            record.setSource(tagGroupDTO.getSource());
            if (!updateCustomerTag(record)) {
                throw new TripscrmException(TripSCRMErrorCode.WECHAT_ENTERPRISE_TAG_MANAGEMENT_INTERFACE_ERROR);
            }
        }
        TagGroupParam param = buildParam(condition);
        TagGroupDO tagGroupDO = tagGroupConverter.convert2DO(record);
        tagGroupDO.setId(null);
        return tagGroupMapper.updateByParamSelective(tagGroupDO, param);
    }

    @Override
    @Cacheable(key = "'id:' + #id", value = "tagGroupCacheManager", unless = "#result == null")
    public TagGroupDTO selectByIdWithCache(Long id) {
        return selectById(id);
    }

    @Override
    @Cacheable(key = "'spaceId:' + #spaceId + ',source:' + #source + ',tagType:' + #tagTypeEnum.getCode()", value = "tagGroupCacheManager", unless = "#result == null")
    public TagGroupDTO selectBySpaceIdAndTagTypeAndSourceWithCache(Long spaceId, TagTypeEnum tagTypeEnum, String source) {
        if (!NumberUtils.validLong(spaceId) || Objects.isNull(tagTypeEnum) || !StringUtils.hasText(source)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TagGroupQuery query = new TagGroupQuery();
        query.setSpaceIdList(Lists.newArrayList(spaceId));
        query.setTagType(Lists.newArrayList(tagTypeEnum.getCode()));
        query.setSourceList(Lists.newArrayList(source));
        List<TagGroupDTO> tagGroupList = selectByCondition(query);
        return CollectionUtils.isEmpty(tagGroupList) ? null : tagGroupList.get(0);
    }

    private static TagGroupParam buildParam(TagGroupQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TagGroupParam param = new TagGroupParam();
        TagGroupParam.Criteria criteria = param.or();

        if (NumberUtils.validLong(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }

        if(!CollectionUtils.isEmpty(query.getIds())) {
            criteria.andIdIn(query.getIds());
        }

        if (!CollectionUtils.isEmpty(query.getTagType())) {
            criteria.andTagTypeIn(query.getTagType().stream().map(Integer::byteValue).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(query.getSourceList())) {
            criteria.andSourceIn(query.getSourceList());
        }

        if (!CollectionUtils.isEmpty(query.getSpaceIdList())) {
            criteria.andSpaceIdIn(query.getSpaceIdList());
        }

        if (StringUtils.hasText(query.getNameLike())) {
            criteria.andNameLike('%' + query.getNameLike() + '%');
        }

        if (StringUtils.hasText(query.getName())) {
            criteria.andNameEqualTo(query.getName());
        }

        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        return param;
    }

    /**
     * 调用企微更新标签接口
     *
     * @param tagGroupDTO 标签组
     * @return 是否更新成功
     */
    private Boolean updateCustomerTag(TagGroupDTO tagGroupDTO) {
        try {
            PlatformLogUtil.logInfo("调用更新企微端标签组接口", LogListUtil.newArrayList(tagGroupDTO));
            if (Objects.isNull(tagGroupDTO) || !StringUtils.hasLength(tagGroupDTO.getName()) || !StringUtils.hasLength(tagGroupDTO.getSource())) {
                PlatformLogUtil.logFail("调用更新企微端标签接口组出错，参数非法", LogListUtil.newArrayList(tagGroupDTO));
                return false;
            }
            if (NumberUtils.isEqual(tagGroupDTO.getDeleted(), IsDeleteEnum.YES.getCode())) {
                PlatformLogUtil.logInfo("标签组已被删除,改为调用删除企微端标签接口", LogListUtil.newArrayList(tagGroupDTO));
                return deleteCustomerTag(tagGroupDTO);
            }
            UpdateCustomerTagRequest request = new UpdateCustomerTagRequest();
            request.setId(tagGroupDTO.getSource());
            request.setName(tagGroupDTO.getName());
            request.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            ResultDO<Boolean> result = tagService.updateCustomerTag(request);
            if (Objects.isNull(result) || !result.getSuccess()) {
                PlatformLogUtil.logFail("调用更新企微端标签组接口失败", LogListUtil.newArrayList(request, result));
                return false;
            }
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.WECHAT_ENTERPRISE_TAG_MANAGEMENT_INTERFACE_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(tagGroupDTO));
            return false;
        }
        return true;
    }

    /**
     * 调用企微删除标签接口
     *
     * @param tagGroupDTO 标签组
     * @return 是否删除成功
     */
    private Boolean deleteCustomerTag(TagGroupDTO tagGroupDTO) {
        try {
            PlatformLogUtil.logInfo("调用删除企微端标签接口", LogListUtil.newArrayList(tagGroupDTO));
            if (Objects.isNull(tagGroupDTO) || !StringUtils.hasLength(tagGroupDTO.getSource()) || !NumberUtils.isEqual(tagGroupDTO.getTagType(), TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode())) {
                return false;
            }
            GetCorpTagListRequest getCorpTagListRequest = new GetCorpTagListRequest();
            getCorpTagListRequest.setGroupIdList(Lists.newArrayList(tagGroupDTO.getSource()));
            ResultDO<List<TagInfoResponse>> tagInfoResponseResultDO = tagService.getCorpTagList(getCorpTagListRequest);
            if (Objects.isNull(tagInfoResponseResultDO) || !tagInfoResponseResultDO.getSuccess() || Objects.isNull(tagInfoResponseResultDO.getModel()) || !StringUtils.hasLength(tagInfoResponseResultDO.getModel().get(0).getGroupId())) {
                PlatformLogUtil.logFail("标签组已被删除", LogListUtil.newArrayList(getCorpTagListRequest, tagInfoResponseResultDO));
                return true;
            }
            PlatformLogUtil.logInfo("调用查询企微端标签接口", LogListUtil.newArrayList(getCorpTagListRequest, tagInfoResponseResultDO));
            DelCustomerTagRequest delCustomerTagRequest = new DelCustomerTagRequest();
            delCustomerTagRequest.setGroupIdList(Lists.newArrayList(tagGroupDTO.getSource()));
            delCustomerTagRequest.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            ResultDO<Boolean> delTagResult = tagService.delCustomerTag(delCustomerTagRequest);
            if (Objects.isNull(delTagResult) || !delTagResult.getSuccess()) {
                PlatformLogUtil.logFail("调用删除企微端标签接口失败", LogListUtil.newArrayList(delCustomerTagRequest, delTagResult));
                return false;
            }
            PlatformLogUtil.logInfo("调用删除企微端标签接口成功", LogListUtil.newArrayList(delCustomerTagRequest, delTagResult));
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.WECHAT_ENTERPRISE_TAG_MANAGEMENT_INTERFACE_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(tagGroupDTO));
            return false;
        }
        return true;
    }
}
