package com.alibaba.tripscrm.service.service.strategy.variable.inner;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.material.InnerServiceEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alitrip.frplf.client.enums.right.MemLevelNew;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/24 11:07
 */
@Component
public class TripffaLevelChangeGetNewLevelInnerService extends AbstractVariableDataInnerService {
    @Override
    public String doGetValue(Map<String, Object> paramMap) {
        if (!paramMap.containsKey("type") || !paramMap.containsKey("new_level")) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        if (!"upgrade".equals(paramMap.get("type"))) {
            throw new TripscrmException(TripSCRMErrorCode.REPLACE_VARIABLE_BE_FILTER);
        }

        Integer newLevelValue = MapUtils.getInteger(paramMap, "new_level", null);
        MemLevelNew memLevelNew = Arrays.stream(MemLevelNew.values())
                .filter(a -> a.getValue().equals(newLevelValue))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(memLevelNew)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return memLevelNew.name();
    }

    @Override
    public InnerServiceEnum getInnerServiceEnum() {
        return InnerServiceEnum.TRIPFFA_LEVEL_CHANGE_GET_NEW_LEVEL;
    }
}
