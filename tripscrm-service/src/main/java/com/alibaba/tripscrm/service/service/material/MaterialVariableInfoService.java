package com.alibaba.tripscrm.service.service.material;


import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;

import java.util.Map;

public interface MaterialVariableInfoService {

    /**
     * 替换素材变量
     * @param materailInfoDO 素材
     * @param carryMap 携带信息
     */
    void replaceMaterialVariable(MaterailInfoDO materailInfoDO, Map<String, Object> carryMap);

    /**
     * 替换内容变量
     * @param content 待替换内容
     * @param carryMap 携带信息
     * @return 替换后内容
     */
    String replaceVariable(String content, Map<String, Object> carryMap);

    /**
     * 替换内容变量
     * @param content 待替换内容
     * @param carryMap 携带信息
     * @param materialTrackRelationDTO 埋点信息（用于链接埋点）
     * @return 替换后内容
     */
    String replaceVariable(String content, Map<String, Object> carryMap, MaterialTrackRelationDTO materialTrackRelationDTO);

}
