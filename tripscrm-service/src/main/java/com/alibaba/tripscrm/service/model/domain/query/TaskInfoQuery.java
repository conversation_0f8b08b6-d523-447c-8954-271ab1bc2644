package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskInfoQuery extends BasePageRequest {
    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 任务名
     */
    private String name;

    /**
     * 任务类型列表
     */
    private List<String> taskTypeCodeList;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 管理员
     */
    private String manager;

    /**
     * 业务空间 Id
     */
    private Long spaceId;

    /**
     * 环境
     */
    private String env;

    /**
     * 标签列表
     */
    private String tags;

    /**
     * 事件源Id
     */
    private Long eventSourceId;

    /**
     * 创建时间（左区间）
     */
    private Date createStartTime;

    /**
     * 创建时间（右区间）
     */
    private Date createEndTime;
}
