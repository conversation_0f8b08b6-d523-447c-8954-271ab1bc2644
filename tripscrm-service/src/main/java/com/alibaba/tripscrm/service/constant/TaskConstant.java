package com.alibaba.tripscrm.service.constant;

/**
 * 任务相关常量
 */
public class TaskConstant {

    public static String ODPS_PROJECT_NAME_FIELD_KEY = "odpsProjectName";
    public static String ODPS_TABLE_NAME_FIELD_KEY = "odpsTableName";
    public static String WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST = "wechatJoinGroupActivityIdList";
    public static String WECHAT_JOIN_GROUP_ACTIVITY_NAME_LIST = "wechatJoinGroupActivityNameList";
    public static String WECHAT_CHAT_ID_LIST = "wechatChatIdList";
    public static String WECHAT_CHAT_NAME_LIST = "wechatChatNameList";

    public static String ALL_MATCH = "allMatch";
    public static String SCRM_INCLUDE_TAG_ID_LIST = "scrmIncludeTagIdList";
    public static String SCRM_INCLUDE_TAG_LIST = "scrmIncludeTagList";
    public static String SCRM_EXCLUDE_TAG_ID_LIST = "scrmExcludeTagIdList";
    public static String SCRM_EXCLUDE_TAG_LIST = "scrmExcludeTagList";
    public static String CROWD_ID_FIELD_KEY = "crowdId";

    public static String EXTERNAL_USERID_LIST = "externalUserIdList";
    public static String ALLOW_SELECT = "allowSelect";

    /**
     * 圈选包含表达式
     * 诸葛人群："crowdId:12345"
     * 标签圈人："tag:123,456 AND 789,234 OR 240,134,124 NOT 234,536,230"
     * 触达频次："{\"expressionType\":\"reachFrequency\",\"condition\":\"between\",\"threshold\":\"10,20\",\"startTime\":1746697860000,\"endTime\":1746997860000}"
     * 点击频次："{\"expressionType\":\"clickFrequency\",\"condition\":\"between\",\"threshold\":\"10,20\",\"startTime\":1746697860000,\"endTime\":1746997860000}"
     * 加好友时间："{\"expressionType\":\"addTime\",\"threshold\":\"10,20\",\"startTime\":1746697860000,\"endTime\":1746997860000}"
     */
    public static String INCLUDE_CROWD_EXPRESSION = "includeCrowdExpression";

    /**
     * 圈选排除表达式
     * 诸葛人群："crowdId:12345"
     * 标签圈人："tag:10696,10783,10601 AND 10478,10501,10489 OR 10506,10584,10654 NOT 10782,10490,10484"
     */
    public static String EXCLUDE_CROWD_EXPRESSION = "excludeCrowdExpression";

    public static String TRIGGER_TIME_TYPE = "triggerTimeType";
    public static String TRIGGER_TIME_VALUE = "triggerTimeValue";
    public static String LANDING_PAGE_URL = "landingPageUrl";
    public static String TASK_MESSAGE_TYPE_ID = "taskMessageTypeId";
    public static String TASK_MESSAGE_SCENE_ID = "taskMessageSceneId";
    public static String CALL_PUSH_CROWD_IDS = "callPushCrowdIds";
    public static String CALL_PUSH_IN_POPULATION = "callPushInPopulation";
    public static String WECHAT_TAG_NAME = "wechatTagName";

    // 个码State
    public static String WECHAT_CONTACT_ME_ACTIVITY_ID = "wechatContactMeActivityId";
    public static String WECHAT_CONTACT_ME_STATE = "wechatContactMeState";
    // 留样比例
    public static String SAMPLING_PERCENT = "samplingPercent";
    // 群码State
    public static String WECHAT_JOIN_GROUP_ACTIVITY_ID = "wechatJoinGroupActivityId";
    public static String WECHAT_JOIN_GROUP_STATE = "wechatJoinGroupState";
    public static String ADD_CUSTOMER_BY_PHONE_CONDITION_LIST = "addCustomerByPhoneConditionList";
    public static String CALL_PUSH_LIMIT = "callPushLimit";

    // 在线继承客户，继承成功话术
    public static String TRANSFER_SUCCESS_MSG = "transferSuccessMsg";

    // 客户继承
    public static String TAKEOVER_USER_ID_LIST = "takeoverUserIdList";
    public static String HANDOVER_USER_ID_LIST = "handoverUserIdList";
    // 原企微成员所在空间
    public static String TAKEOVER_USER_SPACE_ID = "takeoverUserSpaceId";


    /**
     * 子任务数据上传到oss的文件路径，占位符要替换为 主任务ID 和 子任务ID
     **/
    public static String SUB_TASK_DATA_OSS_PATH = "task_sub_%s_%s";

    /**
     * 子任务实例存储在extInfo的oss文件字段key
     **/
    public static String SUB_TASK_EXT_OSS_PATH_KEY = "ossPath";

    /**
     * 子任务实例数据量
     */
    public static String SUB_TASK_EXT_DATA_COUNT = "dataCount";


    /**
     * 定时触发任务，时间分片，记录当前已经执行完xxx时间前的任务
     **/
    public static final String TASK_EXECUTE_TIME_SHARDING_KEY = "TASK_EXECUTE_TIME_SHARDING_KEY";

    /**
     * 活动上下文
     */
    public static String CONTEXT_ID = "contextId";

    public static String ACTIVITY_ID = "activityId";

    public static String TASK_ID = "taskId";

    public static String CORP_ID = "corpId";

    public static String TARGET_ID = "targetId";

    public static String TARGET_TYPE = "targetType";

    /**
     * 子任务ID
     */
    public static String SUB_TASK_ID = "subTaskId";

    /**
     * 转发目标类型
     */
    public static String FORWARD_MESSAGE_TYPE = "forwardMessageType";
    /**
     * 群ID
     */
    public static String CHAT_ID = "chatId";

    /**
     * 发送用户ID
     */
    public static String SEND_USER_ID = "sendUserId";
    /**
     * msgId
     */
    public static String MSG_ID = "msgId";
    /*
     * 消息类型
     */
    public static String MESSAGE_TYPE = "messageType";
    /**
     * 企微用户id
     */
    public static String WECHAT_USER_ID_List = "wechatUserIdList";
    public static String ADD_CUSTOMER_SEND_MESSAGE_LIST = "addCustomerSendMessageList";
    public static String IS_PATROL = "isPatrol";

    /**
     * 支付宝消息群组id列表
     */
    public static String ALIPAY_GROUP_TEMPLATE_ID_LIST = "groupTemplateIdList";

    /**
     * 支付宝消息人群id
     */
    public static String ALIPAY_CROWD_ID = "alipayCrowdId";
    /**
     * 支付宝消息棋盘人群id
     */
    public static String ALIPAY_CROWD_TYPE = "alipayCrowdType";

    /**
     * 支付宝消息实际触发时间
     */
    public static String ALIPAY_TRIGGER_TIME = "aliPayTriggerTimeValue";

    /**
     * 支付宝周期消息scrm任务执行时间
     */
    public static String ALIPAY_CYCLE_TASK_EXECUTE_TIME = "alipayCycleTaskExecuteTime";

    /**
     * 小程序订阅消息任务执行策略
     */
    public static String SUBSCRIBE_MSG_TASK_EXECUTE_STRATEGY = "executeStrategy";
}
