package com.alibaba.tripscrm.service.service.impl.space;

import com.alibaba.tripscrm.dal.mapper.tddl.SpaceMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceDO;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.domain.enums.RoleTypeEnum;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.SpaceVO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.space.SpaceResourceService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.account.BucManager;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.wechat.WechatDepartmentService;
import com.alibaba.tripscrm.service.util.wx.DepartmentUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.model.DepartmentBO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-15 16:59:10
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SpaceServiceImpl implements SpaceService {
    @Switch(description = "超级管理员员工号", name = "superAdminIds")
    private static String superAdminIds = "402913,073666,065531,395824,308260";

    private final SpaceMapper spaceMapper;
    private final SpaceResourceService spaceResourceService;
    private final WechatDepartmentService wechatDepartmentService;
    private final AccountService accountService;

    @Override
    public int insert(SpaceDO spaceDO) {
        if (Objects.isNull(spaceDO) || Objects.isNull(spaceDO.getCreatorId()) || !StringUtils.hasLength(spaceDO.getAdminList())) {
            throw new RuntimeException("参数非法");
        }
        if (spaceDO.getCorpId() == null) {
            spaceDO.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
        }
        if (Objects.isNull(spaceDO.getLastOperatorId())) {
            spaceDO.setLastOperatorId(spaceDO.getCreatorId());
        }
        try {
            return spaceMapper.insert(spaceDO);
        } catch (DuplicateKeyException e) {
            throw new RuntimeException("业务空间名称重复");
        }
    }

    @AteyeInvoker(description = "插入业务空间信息", paraDesc = "name&adminList&creatorId&lastOperatorId&description")
    public int insert(String name, String adminList, String creatorId, String lastOperatorId, String description) {
        SpaceDO spaceDO = new SpaceDO();
        spaceDO.setGmtCreate(new Date());
        spaceDO.setGmtModified(new Date());
        spaceDO.setName(name);
        spaceDO.setAdminList(adminList);
        spaceDO.setCreatorId(creatorId);
        spaceDO.setLastOperatorId(lastOperatorId);
        spaceDO.setDescription(description);
        spaceDO.setIsDeleted((byte)0);
        return insert(spaceDO);
    }

    @AteyeInvoker(description = "修改业务空间信息", paraDesc = "id&adminList")
    public int update(Long id, String adminList) {
        SpaceDO spaceDO = new SpaceDO();
        spaceDO.setId(id);
        spaceDO.setAdminList(adminList);
        return spaceMapper.updateByPrimaryKey(spaceDO);
    }

    @Override
    public int update(SpaceDO spaceDO) {
        if (Objects.isNull(spaceDO) || !NumberUtils.validLong(spaceDO.getId()) || !StringUtils.hasLength(spaceDO.getLastOperatorId())) {
            throw new RuntimeException("参数非法");
        }

        return spaceMapper.updateByPrimaryKey(spaceDO);
    }

    @AteyeInvoker(description = "删除业务空间", paraDesc = "spaceId")
    public int delete(Long spaceId) {
        spaceResourceService.deleteBySpaceId(spaceId);
        return spaceMapper.deleteByPrimaryKey(spaceId);
    }

    @Override
    public SpaceDO getById(Long id) {
        return spaceMapper.selectByPrimaryKey(id);
    }

    @Override
    public SpaceDO getByName(String name) {
        return spaceMapper.selectByName(name);
    }

    @Override
    public List<SpaceDO> list(String userId, String corpId) {
        // 超级管理员拥有一切
        if (isSuperAdmin(userId)) {
            return listByCorpId(corpId);
        }

        List<SpaceResourceDO> spaceResourceDOList = spaceResourceService.listByResourceTypesAndResourceId(Lists.newArrayList(ResourceTypeEnum.MEMBER, ResourceTypeEnum.TAO_BAO_MEMBER), userId);
        if (CollectionUtils.isEmpty(spaceResourceDOList)) {
            return Lists.newArrayList();
        }

        List<SpaceDO> result = spaceMapper.listByIdList(spaceResourceDOList.stream().map(SpaceResourceDO::getSpaceId).distinct().collect(Collectors.toList()))
                .stream().filter(x->x.getCorpId().equals(corpId)).collect(Collectors.toList());
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    @Override
    public List<SpaceDO> list(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Lists.newArrayList();
        }

        List<SpaceDO> result = spaceMapper.listByIdList(idList);
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    @Override
    public List<SpaceDO> listByCorpId(String corpId) {
        if (corpId == null) {
            corpId = WxConstants.DEFAULT_ENTERPRISE_CORP_ID;
        }
        List<SpaceDO> result = spaceMapper.listByCorpId(corpId);
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result.stream().sorted((a, b) -> b.getGmtCreate().compareTo(a.getGmtCreate())).collect(Collectors.toList());
    }

    @Override
    public Long create(SpaceVO spaceVO) {
        if (Objects.nonNull(getByName(spaceVO.getName()))) {
            throw new TripscrmException("业务空间名称重复！");
        }

        if (!checkDepartmentLevelGtSecond(spaceVO.getDepartmentList())) {
            throw new TripscrmException("选中了不可绑定的部门！");
        }

        List<DepartmentBO> invalidDepartmentList = getInvalidDepartmentList(spaceVO.getDepartmentList(), null);
        if (!CollectionUtils.isEmpty(invalidDepartmentList)) {
            DepartmentBO root = wechatDepartmentService.list(spaceVO.getCorpId());
            List<Integer> departmentIdList = DepartmentUtils.getAllParentIdWithCurrent(root, Lists.newArrayList(invalidDepartmentList.get(0).getId()));
            List<SpaceResourceDO> spaceResourceDOList = spaceResourceService.listByResourceTypeAndResourceIdList(ResourceTypeEnum.DEPARTMENT, departmentIdList.stream().map(String::valueOf).collect(Collectors.toList()));
            SpaceDO space = getById(spaceResourceDOList.get(0).getSpaceId());
            if(space.getCorpId().equals(spaceVO.getCorpId())) {
                throw new TripscrmException(invalidDepartmentList.get(0).getName() + "部门已被" + space.getName() + "空间锁定");
            }
        }

        SpaceDO spaceDO = new SpaceDO();
        spaceDO.setName(spaceVO.getName());
        spaceDO.setDescription(spaceVO.getDescription());
        spaceDO.setCreatorId(spaceVO.getCreatorId());
        spaceDO.setLastOperatorId(spaceVO.getCreatorId());
        spaceDO.setAdminList(spaceVO.getAdminList());
        spaceDO.setCorpId(spaceVO.getCorpId());
        spaceDO.setSellerId(spaceVO.getSellerId());
        try {
            int ret = insert(spaceDO);
            if (ret < 1) {
                throw new TripscrmException("业务空间创建失败!");
            }

            createSpaceMemberResource(spaceDO, spaceVO.getMemberList());
            createSpaceTpMemberResource(spaceDO, spaceVO.getTpMemberList());
            createSpaceDepartmentResource(spaceDO, spaceVO.getDepartmentList());
            return spaceDO.getId();
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(spaceVO));
            throw e;
        }
    }

    @Override
    public Integer update(SpaceVO spaceVO) {
        SpaceDO spaceDO = getById(spaceVO.getId());
        if (Objects.isNull(spaceDO)) {
            throw new RuntimeException("修改的业务空间数据不存在！");
        }

        SpaceDO spaceInDb = getByName(spaceVO.getName());
        if (Objects.nonNull(spaceInDb) && !Objects.equals(spaceVO.getId(), spaceInDb.getId())) {
            throw new RuntimeException("业务空间名称重复！");
        }

        if (!checkDepartmentLevelGtSecond(spaceVO.getDepartmentList())) {
            throw new RuntimeException("选中了不可绑定的部门！");
        }

        List<DepartmentBO> invalidDepartmentList = getInvalidDepartmentList(spaceVO.getDepartmentList(), spaceDO.getId());
        if (!CollectionUtils.isEmpty(invalidDepartmentList)) {
            DepartmentBO root = wechatDepartmentService.list(spaceInDb.getCorpId());
            List<Integer> departmentIdList = DepartmentUtils.getAllParentIdWithCurrent(root, Lists.newArrayList(invalidDepartmentList.get(0).getId()));
            List<SpaceResourceDO> spaceResourceDOList = spaceResourceService.listByResourceTypeAndResourceIdList(ResourceTypeEnum.DEPARTMENT, departmentIdList.stream().map(String::valueOf).collect(Collectors.toList()));
            SpaceDO space = getById(spaceResourceDOList.get(0).getSpaceId());
            if(space.getCorpId().equals(spaceVO.getCorpId())) {
                throw new RuntimeException(invalidDepartmentList.get(0).getName() + "部门已被" + space.getName() + "空间锁定");
            }
        }

        try {
            SpaceVO oldSpaceVO = do2vo(spaceDO, true);
            spaceDO.setName(spaceVO.getName());
            spaceDO.setLastOperatorId(spaceVO.getLastOperatorId());
            spaceDO.setAdminList(spaceVO.getAdminList());
            spaceDO.setDescription(spaceVO.getDescription());

            if (update(spaceDO) < 1) {
                throw new RuntimeException("业务空间信息修改失败！");
            }

            updateResourceInfo(oldSpaceVO, spaceVO);
            updateDepartment(oldSpaceVO, spaceVO);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(spaceVO));
            throw e;
        }

        return 1;
    }

    /**
     * 更新空间资源信息
     * @param oldSpaceVO 老空间信息
     * @param newSpaceVO 新空间信息
     */
    private void updateResourceInfo(SpaceVO oldSpaceVO, SpaceVO newSpaceVO) {
        // 内容员工
        updateMember(oldSpaceVO, newSpaceVO);
        // 外部TP代运营
        updateTpMember(oldSpaceVO, newSpaceVO);
    }

    /**
     * 更新空间员工信息
     * @param oldSpaceVO 老空间信息
     * @param newSpaceVO 新空间信息
     */
    private void updateMember(SpaceVO oldSpaceVO, SpaceVO newSpaceVO) {
        List<String> newMemberList = new ArrayList<>(
                Sets.union(Arrays.stream(newSpaceVO.getAdminList().split(","))
                        .filter(StringUtils::hasLength)
                        .collect(Collectors.toSet()),
                        Arrays.stream(newSpaceVO.getMemberList().split(","))
                        .filter(StringUtils::hasLength)
                        .collect(Collectors.toSet()))
        );
        List<String> oldMemberList = new ArrayList<>(
                Sets.union(Arrays.stream(oldSpaceVO.getAdminList().split(","))
                        .filter(StringUtils::hasLength)
                        .collect(Collectors.toSet()),
                        Arrays.stream(oldSpaceVO.getMemberList().split(","))
                                .filter(StringUtils::hasLength)
                                .collect(Collectors.toSet()))
        );
        updateSpaceResource(newMemberList, oldMemberList, newSpaceVO, ResourceTypeEnum.MEMBER);
    }

    /**
     * 更新空间TP运营信息
     * @param oldSpaceVO 新空间信息
     * @param newSpaceVO 老空间信息
     */
    private void updateTpMember(SpaceVO oldSpaceVO, SpaceVO newSpaceVO) {
        List<String> newTpMemberList = new ArrayList<>(
                Arrays.stream(newSpaceVO.getTpMemberList().split(","))
                        .filter(StringUtils::hasLength)
                        .collect(Collectors.toSet())
        );
        List<String> oldTpMemberList = new ArrayList<>(
                Arrays.stream(oldSpaceVO.getTpMemberList().split(","))
                        .filter(StringUtils::hasLength)
                        .collect(Collectors.toSet())
        );
        updateSpaceResource(newTpMemberList, oldTpMemberList, newSpaceVO, ResourceTypeEnum.TAO_BAO_MEMBER);
    }

    /**
     * 更新空间资源信息
     * @param newList 新列表
     * @param oldList 老列表
     * @param newSpaceVO 新的空间信息
     * @param resourceTypeEnum 资源类型
     */
    private void updateSpaceResource(List<String> newList, List<String> oldList, SpaceVO newSpaceVO, ResourceTypeEnum resourceTypeEnum) {
        for (String memberId : oldList) {
            if (newList.contains(memberId)) {
                continue;
            }

            if (spaceResourceService.deleteBySpaceIdAndResourceIdAndResourceType(newSpaceVO.getId(), memberId, resourceTypeEnum) < 1) {
                PlatformLogUtil.logFail("deleteBySpaceIdAndResourceIdAndResourceType fail", LogListUtil.newArrayList(newSpaceVO.getId(), memberId));
            }
        }

        for (String memberId : newList) {
            if (oldList.contains(memberId)) {
                continue;
            }

            SpaceResourceDO spaceResourceDO = new SpaceResourceDO();
            spaceResourceDO.setSpaceId(newSpaceVO.getId());
            spaceResourceDO.setCreatorId(newSpaceVO.getLastOperatorId());
            spaceResourceDO.setLastOperatorId(newSpaceVO.getLastOperatorId());
            spaceResourceDO.setTargetType(resourceTypeEnum.getCode().byteValue());
            spaceResourceDO.setTargetId(memberId);
            if (spaceResourceService.insert(spaceResourceDO) < 1) {
                PlatformLogUtil.logFail("insert spaceResourceDO fail", LogListUtil.newArrayList(spaceResourceDO));
            }
        }
    }

    private void updateDepartment(SpaceVO oldSpaceVO, SpaceVO newSpaceVO) {
        List<String> newDepartmentList = Arrays.stream(newSpaceVO.getDepartmentList().split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());
        List<String> oldDepartmentList = Arrays.stream(oldSpaceVO.getDepartmentList().split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());

        for (String departmentId : newDepartmentList) {
            if (oldDepartmentList.contains(departmentId)) {
                continue;
            }

            SpaceResourceDO spaceResourceDO = new SpaceResourceDO();
            spaceResourceDO.setSpaceId(newSpaceVO.getId());
            spaceResourceDO.setCreatorId(newSpaceVO.getLastOperatorId());
            spaceResourceDO.setLastOperatorId(newSpaceVO.getLastOperatorId());
            spaceResourceDO.setTargetType(ResourceTypeEnum.DEPARTMENT.getCode().byteValue());
            spaceResourceDO.setTargetId(departmentId);
            if (spaceResourceService.insert(spaceResourceDO) < 1) {
                PlatformLogUtil.logFail("spaceResourceDO insert fail", LogListUtil.newArrayList(spaceResourceDO));
            }
        }

        for (String departmentId : oldDepartmentList) {
            if (newDepartmentList.contains(departmentId)) {
                continue;
            }

            if (spaceResourceService.deleteBySpaceIdAndResourceIdAndResourceType(newSpaceVO.getId(), departmentId, ResourceTypeEnum.DEPARTMENT) < 1) {
                PlatformLogUtil.logFail("deleteBySpaceIdAndResourceIdAndResourceType fail", LogListUtil.newArrayList(newSpaceVO.getId(), departmentId));
            }
        }
    }

    private void createSpaceDepartmentResource(SpaceDO spaceDO, String departmentList) {
        List<String> departmentIdList = Arrays.stream(departmentList.split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());

        for (String departmentId : departmentIdList) {
            SpaceResourceDO spaceResourceDO = new SpaceResourceDO();
            spaceResourceDO.setSpaceId(spaceDO.getId());
            spaceResourceDO.setCreatorId(spaceDO.getCreatorId());
            spaceResourceDO.setLastOperatorId(spaceDO.getCreatorId());
            spaceResourceDO.setTargetType(ResourceTypeEnum.DEPARTMENT.getCode().byteValue());
            spaceResourceDO.setTargetId(departmentId);
            if (spaceResourceService.insert(spaceResourceDO) < 1) {
                PlatformLogUtil.logFail("spaceResourceDO insert fail", LogListUtil.newArrayList(spaceResourceDO));
            }
        }
    }

    /**
     * 空间下的内部员工资源
     * @param spaceDO 空间信息
     * @param memberList 成员列表
     */
    private void createSpaceMemberResource(SpaceDO spaceDO, String memberList) {
        Set<String> adminIdSet = Arrays.stream(spaceDO.getAdminList().split(",")).filter(StringUtils::hasLength).map(BucManager::formatEmpId).collect(Collectors.toSet());
        Set<String> memberIdSet = Arrays.stream(memberList.split(",")).filter(StringUtils::hasLength).map(BucManager::formatEmpId).collect(Collectors.toSet());
        List<String> memberIdList = new ArrayList<>(Sets.union(adminIdSet, memberIdSet));

        insertResourceInfo(memberIdList, spaceDO, ResourceTypeEnum.MEMBER);
    }

    /**
     * 空间下的外部代运营资源
     * @param spaceDO 空间信息
     * @param tpMemberList 成员列表
     */
    private void createSpaceTpMemberResource(SpaceDO spaceDO, String tpMemberList) {
        ArrayList<String> tpMemberIdSet = new ArrayList<>(Arrays.stream(tpMemberList.split(",")).filter(StringUtils::hasLength).map(BucManager::formatEmpId).collect(Collectors.toSet()));

        insertResourceInfo(tpMemberIdSet, spaceDO, ResourceTypeEnum.TAO_BAO_MEMBER);
    }

    /**
     * 插入空间资源信息
     * @param memberIdList 成员信息列表
     * @param spaceDO 空间信息
     */
    private void insertResourceInfo(List<String> memberIdList, SpaceDO spaceDO, ResourceTypeEnum resourceTypeEnum) {
        for (String memberId : memberIdList) {
            SpaceResourceDO spaceResourceDO = new SpaceResourceDO();
            spaceResourceDO.setSpaceId(spaceDO.getId());
            spaceResourceDO.setCreatorId(spaceDO.getCreatorId());
            spaceResourceDO.setLastOperatorId(spaceDO.getCreatorId());
            spaceResourceDO.setTargetType(resourceTypeEnum.getCode().byteValue());
            spaceResourceDO.setTargetId(memberId);
            if (spaceResourceService.insert(spaceResourceDO) < 1) {
                PlatformLogUtil.logFail("spaceResourceDO insert fail", LogListUtil.newArrayList(spaceResourceDO));
            }
        }
    }

    public boolean checkDepartmentLevelGtSecond(String departmentList) {
        List<String> departmentIdList = Arrays.stream(departmentList.split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentIdList)) {
            return true;
        }

        DepartmentBO root = wechatDepartmentService.list(SpaceInfoThreadLocalUtils.getCorpId());
        Set<String> levelGtSecondDepartmentIdSet = DepartmentUtils.getLevelGtSecondDepartmentList(root).stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toSet());
        return levelGtSecondDepartmentIdSet.containsAll(departmentIdList);
    }

    public List<DepartmentBO> getInvalidDepartmentList(String departmentList, Long spaceId) {
        List<DepartmentBO> res = new ArrayList<>();
        List<String> departmentIdList = Arrays.stream(departmentList.split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departmentIdList)) {
            return res;
        }

        DepartmentBO root = wechatDepartmentService.list(SpaceInfoThreadLocalUtils.getCorpId());

        List<Integer> bindSpaceDepartmentIdList = spaceResourceService.listByResourceType(ResourceTypeEnum.DEPARTMENT)
                .stream()
                .filter(spaceResourceDO -> Objects.isNull(spaceId) || !Objects.equals(spaceResourceDO.getSpaceId(), spaceId))
                .map(SpaceResourceDO::getTargetId)
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        Set<String> bindSpaceDepartmentIdWithParentSet = DepartmentUtils.getAllParentIdWithCurrent(root, bindSpaceDepartmentIdList)
                .stream()
                .map(String::valueOf)
                .collect(Collectors.toSet());

        Set<String> bindOtherSpaceDepartmentIdWithSubSet = DepartmentUtils.getAllSubIdWithCurrent(root, bindSpaceDepartmentIdList)
                .stream()
                .map(String::valueOf)
                .collect(Collectors.toSet());

        List<String> idList = departmentIdList.stream().filter(x -> bindOtherSpaceDepartmentIdWithSubSet.contains(x) || bindSpaceDepartmentIdWithParentSet.contains(x)).collect(Collectors.toList());
        return DepartmentUtils.getDepartmentList(root, idList.stream().map(Integer::valueOf).collect(Collectors.toList()));
    }

    @Override
    public RoleTypeEnum getRoleType(String userId, Long spaceId) {
        if (!StringUtils.hasLength(userId)) {
            return RoleTypeEnum.NO_PERMISSION;
        }

        // 超级管理员
        if (isSuperAdmin(userId)) {
            return RoleTypeEnum.SUPER_ADMIN;
        }

        // 业务空间不存在
        SpaceDO spaceDO = getById(spaceId);
        if (Objects.isNull(spaceDO)) {
            return RoleTypeEnum.NO_PERMISSION;
        }

        //如果是spaceId为指定值且用户为BD
        if(Objects.equals(spaceId, SwitchConfig.bdWorkSpaceId)){
            return RoleTypeEnum.ADMIN;
        }

        // 业务空间管理员
        if (contains(spaceDO.getAdminList(), userId)) {
            return RoleTypeEnum.ADMIN;
        }

        // 业务空间普通成员
        if (Objects.nonNull(spaceResourceService.getBySpaceIdAndResourceTypeAndResourceId(spaceId, ResourceTypeEnum.MEMBER, BucManager.formatEmpId(userId)))) {
            return RoleTypeEnum.MEMBER;
        }

        // TP代运营
        if (Objects.nonNull(spaceResourceService.getBySpaceIdAndResourceTypeAndResourceId(spaceId, ResourceTypeEnum.TAO_BAO_MEMBER, BucManager.formatEmpId(userId)))){
            return RoleTypeEnum.TP_AGENT_OPERATION;
        }

        // 没权限
        return RoleTypeEnum.NO_PERMISSION;
    }

    private boolean contains(String empIdStr, String empId) {
        return Arrays.stream(empIdStr.split(",")).filter(StringUtils::hasLength).map(BucManager::formatEmpId).collect(Collectors.toSet()).contains(BucManager.formatEmpId(empId));
    }

    @Override
    public boolean isSuperAdmin(String empId) {
        return contains(superAdminIds, empId);
    }

    @Override
    @Cacheable(key = "'corpId:' + #corpId + ',departmentId:' + #departmentId", value = "departmentId2SpaceIdCacheManager")
    public Long getSpaceId(String corpId, Integer departmentId) {
        List<SpaceResourceDO> spaceResourceList = spaceResourceService.listByResourceTypeAndResourceId(ResourceTypeEnum.DEPARTMENT, String.valueOf(departmentId));
        if (CollectionUtils.isEmpty(spaceResourceList)) {
            return null;
        }

        return spaceResourceList.stream().map(SpaceResourceDO::getSpaceId).findFirst().orElse(null);
    }

    private SpaceVO do2vo(SpaceDO spaceDO) {
        return do2vo(spaceDO, false);
    }

    private SpaceVO do2vo(SpaceDO spaceDO, Boolean fullInfo) {
        SpaceVO spaceVO = new SpaceVO();
        BeanUtils.copyProperties(spaceDO, spaceVO);
        if (!fullInfo) {
            return spaceVO;
        }

        List<SpaceVO.Member> memberList = getMemberList(spaceDO);
        Set<String> adminIdSet = Arrays.stream(spaceDO.getAdminList().split(",")).filter(StringUtils::hasLength).map(BucManager::formatEmpId).collect(Collectors.toSet());

        List<SpaceVO.Member> tpMemberList = getTpMemberList(spaceDO);

        // 成员要去除管理员
        spaceVO.setMemberList(memberList.stream().map(SpaceVO.Member::getUserId).filter(x -> !adminIdSet.contains(x)).collect(Collectors.joining(",")));
        spaceVO.setTpMemberList(tpMemberList.stream().map(SpaceVO.Member::getUserId).collect(Collectors.joining(",")));
        spaceVO.setDepartmentList(getDepartmentList(spaceDO));
        spaceVO.setMemberMap(memberList.stream().collect(Collectors.toMap(SpaceVO.Member::getUserId, x -> x)));
        return spaceVO;
    }

    /**
     * 获取空间下内部成员列表
     * @param spaceDO 空间信息
     * @return List<SpaceVO.Member>
     */
    private List<SpaceVO.Member> getMemberList(SpaceDO spaceDO) {
        List<SpaceResourceDO> spaceResourceDOList = spaceResourceService.listBySpaceIdAndResourceType(spaceDO.getId(), ResourceTypeEnum.MEMBER);
        return getMemberInfoList(spaceDO, spaceResourceDOList);
    }

    /**
     * 获取空间下外部代运营列表
     * @param spaceDO 空间信息
     * @return List<SpaceVO.Member>
     */
    private List<SpaceVO.Member> getTpMemberList(SpaceDO spaceDO) {
        List<SpaceResourceDO> spaceResourceDOList = spaceResourceService.listBySpaceIdAndResourceType(spaceDO.getId(), ResourceTypeEnum.TAO_BAO_MEMBER);
        return getMemberInfoList(spaceDO, spaceResourceDOList);
    }

    /**
     * 获取空间下的成员列表
     * @param spaceDO 空间信息
     * @param spaceResourceDOList 空间下的成员列表
     * @return
     */
    private List<SpaceVO.Member> getMemberInfoList(SpaceDO spaceDO, List<SpaceResourceDO> spaceResourceDOList) {
        if (CollectionUtils.isEmpty(spaceResourceDOList)) {
            return Lists.newArrayList();
        }

        return spaceResourceDOList.stream()
                .filter(spaceResourceDO -> Objects.nonNull(accountService.getUserByAccountId(spaceResourceDO.getTargetId())))
                .map(spaceResourceDO -> {
            SpaceVO.Member member = new SpaceVO.Member();
            member.setUserId(spaceResourceDO.getTargetId());
            Set<String> adminIdSet = Arrays.stream(spaceDO.getAdminList().split(",")).filter(StringUtils::hasLength).map(BucManager::formatEmpId).collect(Collectors.toSet());
            Integer roleCode = adminIdSet.contains(spaceResourceDO.getTargetId()) ? RoleTypeEnum.ADMIN.getCode() : (spaceResourceDO.getTargetType().equals(ResourceTypeEnum.TAO_BAO_MEMBER.getCode().byteValue()) ? RoleTypeEnum.TP_AGENT_OPERATION.getCode() : RoleTypeEnum.MEMBER.getCode());
            member.setRole(roleCode);
            member.setSuperAdmin(isSuperAdmin(spaceResourceDO.getTargetId()));
            member.setJoinTime(spaceResourceDO.getGmtCreate());
            member.setUserName(Optional.ofNullable(accountService.getUserByAccountId(member.getUserId())).map(User::getUserName).orElse(null));
            return member;
        }).collect(Collectors.toList());
    }

    private String getDepartmentList(SpaceDO spaceDO) {
        List<SpaceResourceDO> spaceResourceDOList = spaceResourceService.listBySpaceIdAndResourceType(spaceDO.getId(), ResourceTypeEnum.DEPARTMENT);
        if (CollectionUtils.isEmpty(spaceResourceDOList)) {
            return "";
        }

        // 成员要去除管理员
        return spaceResourceDOList.stream().map(SpaceResourceDO::getTargetId).collect(Collectors.joining(","));
    }

    @Override
    @Cacheable(key = "'getCorpIdBySpaceId:' + #spaceId", value = "spaceId2CorpIdCacheManager")
    public String getCorpIdBySpaceId(Long spaceId) {
        SpaceDO space = this.getById(spaceId);
        return space.getCorpId();
    }

    @Override
    @Cacheable(key = "'getSellerIdBySpaceId' + #spaceId", value = "spaceId2SellerIdCacheManager")
    public String getSellerIdBySpaceId(Long spaceId) {
        SpaceDO space = this.getById(spaceId);
        return space.getSellerId();
    }
}
