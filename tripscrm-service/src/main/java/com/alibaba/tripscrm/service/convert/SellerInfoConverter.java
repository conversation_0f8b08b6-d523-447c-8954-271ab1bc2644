package com.alibaba.tripscrm.service.convert;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.SellerInfoDO;
import com.alibaba.tripscrm.service.model.vo.seller.SellerInfoManagementVO;
import com.alibaba.tripscrm.service.model.dto.seller.SellerInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class SellerInfoConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param sellerInfoDTO
     */
    public SellerInfoDO convertFromDTO(SellerInfoDTO sellerInfoDTO) {
        SellerInfoDO sellerInfoDO = new SellerInfoDO();
        sellerInfoDO.setId(sellerInfoDTO.getId());
        sellerInfoDO.setGmtCreate(sellerInfoDTO.getGmtCreate());
        sellerInfoDO.setGmtModified(sellerInfoDTO.getGmtModified());
        sellerInfoDO.setSellerId(sellerInfoDTO.getSellerId());
        sellerInfoDO.setSpaceId(sellerInfoDTO.getSpaceId());
        sellerInfoDO.setHasSignedAgreement(sellerInfoDTO.getHasSignedAgreement());
        sellerInfoDO.setEnabled(sellerInfoDTO.getEnabled());
        if (Objects.nonNull(sellerInfoDTO.getExtraInfo())) {
            sellerInfoDO.setExtInfo(JSONObject.toJSONString(sellerInfoDTO.getExtraInfo()));
        }
        return sellerInfoDO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param sellerInfoDO
     */
    public SellerInfoDTO convertFromDO(SellerInfoDO sellerInfoDO) {
        SellerInfoDTO sellerInfoDTO = new SellerInfoDTO();
        sellerInfoDTO.setId(sellerInfoDO.getId());
        sellerInfoDTO.setGmtCreate(sellerInfoDO.getGmtCreate());
        sellerInfoDTO.setGmtModified(sellerInfoDO.getGmtModified());
        sellerInfoDTO.setSellerId(sellerInfoDO.getSellerId());
        sellerInfoDTO.setSpaceId(sellerInfoDO.getSpaceId());
        sellerInfoDTO.setHasSignedAgreement(sellerInfoDO.getHasSignedAgreement());
        sellerInfoDTO.setEnabled(sellerInfoDO.getEnabled());
        sellerInfoDTO.setExtraInfo(sellerInfoDO.getExtInfo());
        return sellerInfoDTO;
    }

    public List<SellerInfoManagementVO> convertFromVO(List<SellerInfoDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(data -> {
            SellerInfoManagementVO sellerInfoManagementVO = new SellerInfoManagementVO();
            BeanUtils.copyProperties(data, sellerInfoManagementVO);
            return sellerInfoManagementVO;
        }).collect(Collectors.toList());
    }
}