package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.util.Date;

/**
 * 用户会话 消息更新对象
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class ChatConversationUpdateLastMessageParam {
    /**
     * 企微成员id
     */
    private String userId;
    /**
     * 会话id 1:群聊会话id，2:与企微客户的私聊会话id，3:与企微成员的私聊会话id
     */
    private String chatId;
    /**
     * 企微组织id
     */
    private String corpId;
    /**
     * 会话类型 1:群聊，2:与企微客户的私聊，3:与企微成员的私聊
     * @see ChatTypeEnum
     */
    private Integer chatType;
    /**
     * 发送人id
     */
    private String senderId;
    /**
     * 会话中最后一条消息发送人名称
     */
    private String lastMessageSenderName;
    /**
     * 会话中最后一条消息内容
     */
    private String lastMessageContent;
    /**
     * 会话中最后一条消息时间
     */
    private Date lastMessageCreateTime;
    /**
     * 会话更新时间戳（用于会话排序）
     */
    private Long updateTimestamp;
}