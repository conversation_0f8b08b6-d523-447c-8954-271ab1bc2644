package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.RobotStatusEnum;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.api.service.RobotService;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 平台号下线提示
 *
 * <AUTHOR>
 * @Date 2024/4/23 14:48
 **/
@Component
public class RobotOffLineProcessor extends JavaProcessor {

    @Resource
    private RobotService robotService;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private DingTalkApi dingTalkApi;
    public static String dingTalkAccessToken = "124b41d43a2cae3f29f3fab8df33fed506242e73254ae404917c5a38bb4b9956";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {

            // 激活的机器人列表
            WechatUserQuery query = new WechatUserQuery();
            query.setStatus((byte) 1);
            query.setOnlineStatus(RobotStatusEnum.OFFLINE.getCode().byteValue());
            query.setSpaceId(10L);
            List<WechatUserDTO> wechatUserList = wechatUserService.listByCondition(query);
            if (CollectionUtils.isEmpty(wechatUserList)) {
                PlatformLogUtil.logInfo("平台号下线提示，企微号列表为空");

                return new ProcessResult(true);
            }
            StringBuilder messageContent = new StringBuilder("【飞猪福利管家】-【用户运营】平台号\n");
            for (WechatUserDTO wechatUserDTO : wechatUserList) {
                if( Objects.equals(RobotTypeEnum.of(wechatUserDTO.getRobotType()), RobotTypeEnum.EMPLOYEE)){
                    continue;
                }
                Date offlineTime = wechatUserDTO.getGmtModified();
                if (Objects.nonNull(wechatUserDTO.getOfflineTime())) {
                    offlineTime = wechatUserDTO.getOfflineTime();
                }
                if (DateUtils.calulateDays(offlineTime, new Date()) > 2) {
                    String message =   wechatUserDTO.getName() + "已经掉线" + DateUtils.calulateDays(offlineTime, new Date()) + "天，至今未上线，请关注。\n";
                    messageContent.append( message);
                }
            }
            if(messageContent.length() > 0){
                messageContent.append("\n长期不处理可能影响好友关系的离职继承。");
            }

            dingTalkApi.sendMessageToDingTalkGroup(dingTalkAccessToken, messageContent.toString(), "322980,160812");


            return new ProcessResult(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("平台号下线提示定时任务执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return new ProcessResult(false, e.getMessage());
        }
    }
}
