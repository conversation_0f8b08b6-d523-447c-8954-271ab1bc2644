package com.alibaba.tripscrm.service.service.impl;

import com.ali.com.google.common.collect.Lists;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.CreateWeComRecordMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CreateWeComRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.CreateWeComRecordParam;
import com.alibaba.tripscrm.service.convert.CreateWeComRecordConverter;
import com.alibaba.tripscrm.service.model.domain.query.CreateWeComRecordQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.CreateWeComRecordDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.CreateWeComRecordService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;


import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/01/21 15:49
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CreateWeComRecordServiceImpl implements CreateWeComRecordService {

    private final CreateWeComRecordMapper createWeComRecordMapper;
    private final CreateWeComRecordConverter createWeComRecordConverter;

    private static final String REQUEST_ID = "requestId";
    private static final String ORDER_ID = "orderId";


    @Override
    public Integer insertSelective(CreateWeComRecordDTO record) {
        if (Objects.isNull(record) || !Lists.newArrayList(IsvTypeEnum.BI_LIN, IsvTypeEnum.BAI_YE).contains(IsvTypeEnum.valueOf(record.getIsvType()))
                || !StringUtils.hasLength(record.getCreatorId()) || !StringUtils.hasLength(record.getCorpId()) || !NumberUtils.validLong(record.getDepartmentId())
                || !NumberUtils.validInteger(record.getCount())) {
            PlatformLogUtil.logFail("企业微信上号申请记录写入失败，参数非法", LogListUtil.newArrayList(record));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);

        }
        return createWeComRecordMapper.insertSelective(createWeComRecordConverter.convert2DO(record));
    }

    @Override
    public Integer updateByIdSelective(CreateWeComRecordDTO record) {
        if (Objects.isNull(record) || Objects.isNull(record.getId())) {
            PlatformLogUtil.logFail("企业微信上号申请记录更新失败，参数非法", LogListUtil.newArrayList(record));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        CreateWeComRecordDTO oldRecord = selectById(record.getId());
        if (Objects.isNull(oldRecord)) {
            PlatformLogUtil.logFail("企业微信上号申请记录更新失败，记录不存在", LogListUtil.newArrayList(record));
            return 0;
        }
        return createWeComRecordMapper.updateByPrimaryKeySelective(createWeComRecordConverter.convert2DO(record));
    }

    @Override
    public CreateWeComRecordDTO selectById(Long id) {
        if (!NumberUtils.validLong(id)) {
            PlatformLogUtil.logFail("企业微信上号申请记录查询失败，参数非法", LogListUtil.newArrayList(id));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return Optional.ofNullable(createWeComRecordMapper.selectByPrimaryKey(id)).map(createWeComRecordConverter::convert2DTO).orElse(null);
    }

    @Override
    public Integer deleteById(Long id) {
        if (!NumberUtils.validLong(id)) {
            PlatformLogUtil.logFail("企业微信上号申请记录删除失败，参数非法", LogListUtil.newArrayList(id));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return createWeComRecordMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<CreateWeComRecordDTO> selectByCondition(CreateWeComRecordQuery query) {
        if (Objects.isNull(query)) {
            PlatformLogUtil.logFail("企业微信上号申请记录查询失败，参数非法", LogListUtil.newArrayList(query));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        CreateWeComRecordParam param = buildParam(query);
        return Optional.ofNullable(createWeComRecordMapper.selectByParam(param)).orElse(new ArrayList<>()).stream().map(createWeComRecordConverter::convert2DTO).collect(Collectors.toList());
    }

    @Override
    public CreateWeComRecordDTO selectByDateAndOrderId(Date startTime, Date endTime, String orderId) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime) || !StringUtils.hasLength(orderId)) {
            PlatformLogUtil.logFail("根据起止日期和requestId查询企业微信申请上号记录失败，参数非法", LogListUtil.newArrayList(startTime, endTime, orderId));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        CreateWeComRecordQuery query = new CreateWeComRecordQuery();
        query.setCreateStartTime(startTime);
        query.setCreateEndTime(endTime);
        List<CreateWeComRecordDTO> createWeComRecordDTOS = selectByCondition(query);
        if (CollectionUtils.isEmpty(createWeComRecordDTOS)) {
            PlatformLogUtil.logFail("未查询到上号记录", LogListUtil.newArrayList(startTime, endTime, orderId));
            return null;
        }
        for (CreateWeComRecordDTO createWeComRecordDTO : createWeComRecordDTOS) {
            JSONObject jsonObject = JSONObject.parseObject(createWeComRecordDTO.getExtInfo());
            if (Objects.isNull(jsonObject)) {
                continue;
            }
            if (StringUtils.pathEquals(orderId, jsonObject.getString(ORDER_ID))) {
                return createWeComRecordDTO;
            }
        }
        return null;
    }

    @Override
    public PageInfo<CreateWeComRecordDTO> pageQuery(CreateWeComRecordQuery query) {
        try {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
            CreateWeComRecordParam param = buildParam(query);
            param.appendOrderByClause(CreateWeComRecordParam.OrderCondition.GMTMODIFIED, CreateWeComRecordParam.SortType.DESC);
            List<CreateWeComRecordDO> list = createWeComRecordMapper.selectByParam(param);
            if (CollectionUtils.isEmpty(list)) {
                return new PageInfo<>();
            }
            PageInfo<CreateWeComRecordDO> pageInfo = new PageInfo<>(list);
            return PageUtils.getPageInfo(pageInfo, createWeComRecordConverter::convert2DTO);
        } catch (Exception e) {
            PlatformLogUtil.logException("企业微信上号记录查询出错", e.getMessage(), e, LogListUtil.newArrayList(query));
            throw e;
        }
    }

    private static CreateWeComRecordParam buildParam(CreateWeComRecordQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        CreateWeComRecordParam param = new CreateWeComRecordParam();
        CreateWeComRecordParam.Criteria criteria = param.or();
        if (NumberUtils.validLong(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }
        if (Objects.nonNull(query.getCreateStartTime())) {
            criteria.andGmtCreateGreaterThanOrEqualTo(query.getCreateStartTime());
        }
        if (Objects.nonNull(query.getCreateEndTime())) {
            criteria.andGmtCreateLessThanOrEqualTo(query.getCreateEndTime());
        }
        if (Objects.nonNull(query.getIsvType()) && NumberUtils.validInteger(Integer.valueOf(query.getIsvType()))) {
            criteria.andIsvTypeEqualTo(query.getIsvType());
        }
        if (StringUtils.hasLength(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }
        return param;
    }

    @AteyeInvoker(description = "企业微信上号申请记录更新", paraDesc = "id&istType&corpId&departmentId&inviteUrl&count&creatorId&spaceId&status&requestId&orderId&successCount")
    public Integer updateByIdSelective(Long id, Byte isvType, String corpId, Long departmentId, String inviteUrl, Integer count, String creatorId, Long spaceId, Byte status, String requestId, String orderId, Integer successCount) {
        CreateWeComRecordDTO createWeComRecordDTO = new CreateWeComRecordDTO();
        createWeComRecordDTO.setId(id);
        createWeComRecordDTO.setIsvType(isvType);
        createWeComRecordDTO.setCorpId(corpId);
        createWeComRecordDTO.setDepartmentId(departmentId);
        createWeComRecordDTO.setInviteUrl(inviteUrl);
        createWeComRecordDTO.setCount(count);
        createWeComRecordDTO.setCreatorId(creatorId);
        createWeComRecordDTO.setSpaceId(spaceId);
        createWeComRecordDTO.setStatus(status);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(REQUEST_ID, requestId);
        jsonObject.put(ORDER_ID, orderId);
        jsonObject.put("successCount", successCount);
        createWeComRecordDTO.setExtInfo(jsonObject.toJSONString());
        return updateByIdSelective(createWeComRecordDTO);
    }
}
