package com.alibaba.tripscrm.service.service.task.ability.data.query.common;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.CrowdExpressionConditionEnum;
import com.alibaba.tripscrm.service.enums.task.CrowdExpressionMethodTypeEnum;
import com.alibaba.tripscrm.service.manager.middleware.OdpsManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.dto.task.CrowdExpressionDTO;
import com.alibaba.tripscrm.service.model.dto.task.TargetDataDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.RecordReader;
import com.aliyun.odps.tunnel.InstanceTunnel;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/7 20:17
 **/
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CommonCustomerDataQueryProcessor {
    private final WechatUserService wechatUserService;
    private final OdpsManager odpsManager;
    private final TaskService taskService;

    @Switch(name = "SLEEP_PERIOD", description = "downloadSession获取失败时间隔多久再试一次。默认值30秒")
    public static Long sleepPeriod = 30 * 1000L;

    @Switch(name = "STANDARD_TASK_ODPS_FETCH_SQL", description = "获取任务odps数据执行标准语句")
    public static String standardTaskOdpsFetchSql = "select distinct user_id, ext_info from %s where dt = MAX_PT ('%s')";

    public static String crowdOdpsFetchSql = "SELECT  DISTINCT user_id, '' AS ext_info\n" +
            "FROM    trip_profile.dwd_trip_crowd_data_snapshot\n" +
            "WHERE   group_id = '%s'";

    public static String wechatCustomerFetchSql = "SELECT union_id, user_id, external_user_id, COLLECT_SET(follow_user_id)[CAST(rand(UNIX_TIMESTAMP()) * COUNT(*) AS BIGINT)] AS follow_user_id, '' AS ext_info\n" +
            "FROM    alitrip_wireless.scrm_wechat_customer_relation\n" +
            "WHERE   ds = MAX_PT('alitrip_wireless.scrm_wechat_customer_relation')\n" +
            " AND corp_id = '%s'\n" +
            " AND follow_user_id IN (%s)\n" +
            " AND status = 1\n" +
            "%s\n" +
            "GROUP BY union_id, user_id, external_user_id";

    public static String BASE_SQL_WITH_INCLUDE_PART = "SELECT base_data.%s, base_data.follow_user_id FROM (%s) AS base_data LEFT JOIN (%s) include_data ON base_data.%s = include_data.%s WHERE include_data.%s IS NOT NULL;";
    public static String BASE_ZERO_SQL_WITH_INCLUDE_PART = "SELECT base_data.%s, base_data.follow_user_id FROM (%s) AS base_data LEFT JOIN (%s) include_data ON base_data.%s = include_data.%s WHERE include_data.%s IS NULL;";
    public static String BASE_SQL_WITH_INCLUDE_PART_AND_EXCLUDE_PART = "SELECT base_data.%s, base_data.follow_user_id FROM (%s) AS base_data LEFT JOIN (%s) include_data ON base_data.%s = include_data.%s LEFT JOIN (%s) exclude_data ON base_data.%s = exclude_data.%s WHERE include_data.%s IS NOT NULL AND exclude_data.%s IS NULL;";
    public static String BASE_ZERO_SQL_WITH_INCLUDE_PART_AND_EXCLUDE_PART = "SELECT base_data.%s, base_data.follow_user_id FROM (%s) AS base_data LEFT JOIN (%s) include_data ON base_data.%s = include_data.%s LEFT JOIN (%s) exclude_data ON base_data.%s = exclude_data.%s WHERE include_data.%s IS NULL AND exclude_data.%s IS NULL;";

    // 包含全部
    public static String includeAllScrmTagSql = " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(scrm_tag_ids,''),','),ARRAY(%s))) = %d\n";
    // 包含任意
    public static String includeAnyScrmTagSql = " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(scrm_tag_ids,''),','),ARRAY(%s))) > 0\n";
    // 不包含
    public static String excludeAllScrmTagSql = " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(scrm_tag_ids,''),','),ARRAY(%s))) = 0\n";

    // ======================================================================================================================================================================================

    // 客户标签-第一个
    private static String getFirstCustomerTagSql() {
        return SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH
                ? " SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(scrm_tag_ids,''),','),ARRAY(%s))) > 0\n"
                : " SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(customer_tag_ids,''),','),ARRAY(%s))) > 0\n";
    }

    // 客户标签-并且
    private static String getAndCustomerTagSql() {
        return SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH
                ? " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(scrm_tag_ids,''),','),ARRAY(%s))) > 0\n"
                : " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(customer_tag_ids,''),','),ARRAY(%s))) > 0\n";
    }

    // 客户标签-或者
    private static String getOrCustomerTagSql() {
        return SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH
                ? " OR     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(scrm_tag_ids,''),','),ARRAY(%s))) > 0\n"
                : " OR     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(customer_tag_ids,''),','),ARRAY(%s))) > 0\n";
    }

    // 客户标签-排除
    private static String getNotCustomerTagSql() {
        return SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH
                ? " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(scrm_tag_ids,''),','),ARRAY(%s))) = 0\n"
                : " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(customer_tag_ids,''),','),ARRAY(%s))) = 0\n";
    }

    // 企微标签-第一个
    public static String firstWechatTagSql = " SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(wechat_tag_ids,''),','),ARRAY(%s))) > 0\n";

    // 企微标签-并且
    public static String andWechatTagSql = " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(wechat_tag_ids,''),','),ARRAY(%s))) > 0\n";

    // 企微标签-或者
    public static String orWechatTagSql = " OR     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(wechat_tag_ids,''),','),ARRAY(%s))) > 0\n";

    // 企微标签-排除
    public static String notWechatTagSql = " AND     SIZE(ARRAY_INTERSECT(SPLIT(COALESCE(wechat_tag_ids,''),','),ARRAY(%s))) = 0\n";

    @AteyeInvoker(description = "任务圈人测试", paraDesc = "taskId&needOnline&targetType")
    public void queryFromTask(Long taskId, Boolean needOnline, Integer targetType) {
        queryFromTask(taskService.queryTaskById(taskId), 1L, needOnline, ActivityTargetTypeEnum.codeOf(targetType));
    }

    /**
     * 获取任务中的客户数据
     *
     * @param taskInfoDO 任务信息
     * @return List<TargetDataDTO>
     */
    public List<TargetDataDTO> queryFromTask(TaskInfoDO taskInfoDO, Long taskInstanceId, Boolean needOnline, ActivityTargetTypeEnum targetType) {
        if (Objects.isNull(taskInfoDO)) {
            return new ArrayList<>();
        }

        try {
            List<String> wechatUserIdList = getWechatUserIdList(taskInfoDO);
            if (CollectionUtils.isEmpty(wechatUserIdList)) {
                throw new TripscrmException(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
            }

            WechatUserQuery query = new WechatUserQuery();
            query.setUserIdList(wechatUserIdList);
            query.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
            query.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
            query.setStatus(WechatUserStatusEnum.ACTIVE.getCode().byteValue());
            List<String> validWechatUserIdList;
            if (needOnline) {
                List<WechatUserDTO> validWechatUserList = wechatUserService.listValidWechatUserByCondition(query);
                validWechatUserIdList = validWechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList());
            } else {
                List<WechatUserDTO> validWechatUserList = wechatUserService.listByCondition(query);
                validWechatUserIdList = validWechatUserList.stream().map(WechatUserDTO::getUserId).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(validWechatUserIdList)) {
                throw new TripscrmException(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
            }

            JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
            String includeCrowdExpression = MapUtils.getString(extInfo, TaskConstant.INCLUDE_CROWD_EXPRESSION);
            if (StringUtils.isBlank(includeCrowdExpression)) {
                return queryFromOldTask(validWechatUserIdList, taskInfoDO, targetType);
            }
            return queryFromNewTask(validWechatUserIdList, taskInfoDO, targetType);
        } catch (Exception e) {
            PlatformLogUtil.logException("客户数据查询处理器，查询任务离线数据异常", e.getMessage(), e, LogListUtil.newArrayList(taskInfoDO.getId(), taskInstanceId));
            DingTalkApi.sendTaskMessage(String.format("客户数据查询处理器，查询任务离线数据异常，任务ID:%s，实例ID:%s", taskInfoDO.getId(), taskInstanceId));
            if (e instanceof TripscrmException) {
                TripscrmException tripscrmException = (TripscrmException) e;
                throw new TripscrmException(tripscrmException.getErrorCodeEnum(), tripscrmException.getErrorMsg());
            }
            throw new TripscrmException(TripSCRMErrorCode.QUERY_ODPS_FAIL);
        }
    }

    private List<TargetDataDTO> queryFromNewTask(List<String> wechatUserIdList, TaskInfoDO taskInfoDO, ActivityTargetTypeEnum targetType) throws Exception {
        List<TargetDataDTO> result = new ArrayList<>();
        String targetIdField = getTargetIdField(targetType);
        String downSql = getSql(wechatUserIdList, taskInfoDO, targetIdField);
        PlatformLogUtil.logInfo("客户数据查询处理器，生成Odps查询语句", LogListUtil.newArrayList(downSql));
        InstanceTunnel.DownloadSession downloadSession = odpsManager.getDefaultDownloadSession(downSql);
        if (downloadSession == null) {
            Thread.sleep(sleepPeriod);
            downloadSession = odpsManager.getDefaultDownloadSession(downSql);
        }

        if (downloadSession == null) {
            PlatformLogUtil.logFail("客户数据查询处理器，下载odps数据出错", LogListUtil.newArrayList(downSql));
            throw new TripscrmException(TripSCRMErrorCode.QUERY_ODPS_FAIL);
        }

        long recordCount = downloadSession.getRecordCount();
        if (recordCount == 0) {
            PlatformLogUtil.logFail("客户数据查询处理器，odps数据为空", LogListUtil.newArrayList(downSql));
            throw new TripscrmException(TripSCRMErrorCode.EMPTY_ODPS_DATA);
        }

        PlatformLogUtil.logInfo("客户数据查询处理器，odps数据查询完毕", LogListUtil.newArrayList(recordCount));
        // 读取全部数据到内存里
        Record record;
        RecordReader recordReader = downloadSession.openRecordReader(0, recordCount);
        while ((record = recordReader.read()) != null) {
            if (StringUtils.isBlank(record.getString(targetIdField)) || StringUtils.isBlank(record.getString("follow_user_id"))) {
                continue;
            }

            TargetDataDTO targetDataDTO = new TargetDataDTO();
            targetDataDTO.setTargetId(record.getString(targetIdField));
            targetDataDTO.setTargetType(targetType);
            JSONObject extInfo = new JSONObject();
            extInfo.put("sendUserId", record.getString("follow_user_id"));
            targetDataDTO.setExtInfo(extInfo);
            result.add(targetDataDTO);
        }
        return result;
    }

    private String getSql(List<String> wechatUserIdList, TaskInfoDO taskInfoDO, String targetIdField) throws Exception {
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        String includeCrowdExpression = MapUtils.getString(extInfo, TaskConstant.INCLUDE_CROWD_EXPRESSION);
        String excludeCrowdExpression = MapUtils.getString(extInfo, TaskConstant.EXCLUDE_CROWD_EXPRESSION);

        if (StringUtils.isBlank(includeCrowdExpression)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        String cropId = taskService.getCorpIdByTaskId(taskInfoDO.getId());
        String baseSql = getBaseSql(wechatUserIdList, cropId);
        CrowdExpressionDTO includeCrowdExpressionDTO;
        CrowdExpressionDTO excludeCrowdExpressionDTO;
        try {
            includeCrowdExpressionDTO = taskService.evaluateCrowdExpression(includeCrowdExpression);
            excludeCrowdExpressionDTO = StringUtils.isBlank(excludeCrowdExpression) ? null : taskService.evaluateCrowdExpression(excludeCrowdExpression);
        } catch (Exception e) {
            PlatformLogUtil.logException("客户数据查询处理器，圈选表达式解析异常", e.getMessage(), e, LogListUtil.newArrayList(taskInfoDO.getId(), includeCrowdExpression));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        CrowdExpressionMethodTypeEnum includeCrowdExpressionMethodTypeEnum = CrowdExpressionMethodTypeEnum.codeOf(includeCrowdExpressionDTO.getExpressionType());
        if (Objects.isNull(includeCrowdExpressionMethodTypeEnum)) {
            PlatformLogUtil.logFail("查询客户sql获取失败，圈选表达式类型非法", LogListUtil.newArrayList(includeCrowdExpressionDTO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        String includeSql = getJoinSql(includeCrowdExpressionDTO, cropId);
        String excludeSql = excludeCrowdExpressionDTO == null ? null : getJoinSql(excludeCrowdExpressionDTO, cropId);
        if (StringUtils.isBlank(excludeSql)) {
            String joinField = getJoinField(includeCrowdExpressionMethodTypeEnum);
            return String.format(
                    getSqlWithIncludePart(includeCrowdExpressionMethodTypeEnum, includeCrowdExpressionDTO.getThreshold(), CrowdExpressionConditionEnum.codeOf(includeCrowdExpressionDTO.getCondition())),
                    targetIdField,
                    baseSql,
                    includeSql,
                    joinField,
                    joinField,
                    joinField
            );
        }

        String includeJoinField = getJoinField(includeCrowdExpressionMethodTypeEnum);
        String excludeJoinField = NumberUtils.validLong(excludeCrowdExpressionDTO.getCrowdId()) ? "user_id" : "external_user_id";
        return String.format(
                getSqlWithIncludePartAndExcludePart(includeCrowdExpressionMethodTypeEnum, includeCrowdExpressionDTO.getThreshold(), CrowdExpressionConditionEnum.codeOf(includeCrowdExpressionDTO.getCondition())),
                targetIdField,
                baseSql,
                includeSql,
                includeJoinField,
                includeJoinField,
                excludeSql,
                excludeJoinField,
                excludeJoinField,
                includeJoinField,
                excludeJoinField
        );
    }

    private String getBaseSql(List<String> wechatUserIdList, String corpId) {
        String sql = " SELECT union_id, user_id, external_user_id, COLLECT_SET(follow_user_id)[CAST(rand(UNIX_TIMESTAMP()) * COUNT(*) AS BIGINT)] AS follow_user_id \n " +
                " FROM    alitrip_wireless.scrm_wechat_customer_relation\n " +
                " WHERE   ds = MAX_PT('alitrip_wireless.scrm_wechat_customer_relation')\n " +
                " AND corp_id = %s\n " +
                " AND follow_user_id IN (%s)\n " +
                " AND     status = 1 \n" +
                " GROUP BY union_id, user_id, external_user_id ";

        String corpIdStr = "'" + corpId + "'";
        String userIdListStr = wechatUserIdList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(","));
        return String.format(sql, corpIdStr, userIdListStr);
    }

    /**
     * 获取joinSql
     * @param crowdExpressionDTO 圈选表达式DTO
     * @param corpId 企业id
     * @return
     */
    private String getJoinSql(CrowdExpressionDTO crowdExpressionDTO, String corpId) {
        if (Objects.isNull(crowdExpressionDTO) || StringUtils.isBlank(crowdExpressionDTO.getExpressionType())) {
            PlatformLogUtil.logFail("查询客户sql获取失败，圈选表达式DTO非法", LogListUtil.newArrayList(crowdExpressionDTO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        CrowdExpressionMethodTypeEnum crowdExpressionMethodTypeEnum = CrowdExpressionMethodTypeEnum.codeOf(crowdExpressionDTO.getExpressionType());
        if (Objects.isNull(crowdExpressionMethodTypeEnum)) {
            PlatformLogUtil.logFail("查询客户sql获取失败，圈选表达式类型非法", LogListUtil.newArrayList(crowdExpressionDTO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        switch (crowdExpressionMethodTypeEnum) {
            case CROWD_ID:
                return getCrowdIdSql(crowdExpressionDTO.getCrowdId());
            case TAG:
                return getTagSql(crowdExpressionDTO, corpId);
            case REACH_FREQUENCY:
            case CLICK_FREQUENCY:
            case ADD_TIME:
                return getJoinSql(crowdExpressionDTO);
            default:
                PlatformLogUtil.logFail("查询客户sql获取失败，圈选表达式类型非法", LogListUtil.newArrayList(crowdExpressionDTO));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
    }

    /**
     * 获取人群idsql
     * @param crowdId
     * @return
     */
    private String getCrowdIdSql(Long crowdId) {
        String sql = " SELECT  DISTINCT user_id\n " +
                " FROM    trip_profile.dwd_trip_crowd_data_snapshot\n " +
                " WHERE   group_id = %s\n ";

        String crowdIdStr = "'" + crowdId + "'";
        return String.format(sql, crowdIdStr);
    }

    /**
     * 获取标签sql
     * @param crowdExpressionDTO 圈选表达式DTO
     * @param corpId 企业id
     * @return 标签sql
     */
    private String getTagSql(CrowdExpressionDTO crowdExpressionDTO, String corpId) {
        String sql = " SELECT  DISTINCT external_user_id\n " +
                " FROM    alitrip_wireless.scrm_wechat_customer_relation\n " +
                " WHERE   ds = MAX_PT('alitrip_wireless.scrm_wechat_customer_relation')\n " +
                " AND     corp_id = %s AND (%s)";

        StringBuilder tagSql = new StringBuilder();
        for (CrowdExpressionDTO.CrowdExpressionElementDTO element : crowdExpressionDTO.getElementList()) {
            String operator = Optional.ofNullable(element.getOperator()).orElse("");
            String tagIdListStr = element.getTagInfoList().stream().map(x -> "'" + x.getTag() + "'").collect(Collectors.joining(", "));
            boolean isWechatTag = Objects.equals(element.getTagInfoList().get(0).getTagType(), TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode());
            String currentSql;
            switch (operator) {
                case "AND":
                    currentSql = isWechatTag ? andWechatTagSql : getAndCustomerTagSql();
                    break;
                case "OR":
                    currentSql = isWechatTag ? orWechatTagSql : getOrCustomerTagSql();
                    break;
                case "NOT":
                    currentSql = isWechatTag ? notWechatTagSql : getNotCustomerTagSql();
                    break;
                default:
                    currentSql = isWechatTag ? firstWechatTagSql : getFirstCustomerTagSql();
            }

            tagSql.append(String.format(currentSql, tagIdListStr));
        }

        return String.format(sql, "'" + corpId + "'", tagSql);
    }

    /**
     * 获取除人群、标签圈选条件外的其他圈选sql
     * @param crowdExpressionDTO 圈选表达式DTO
     * @return 触达频次sql
     */
    private String getJoinSql(CrowdExpressionDTO crowdExpressionDTO) {
        CrowdExpressionMethodTypeEnum crowdExpressionMethodTypeEnum = CrowdExpressionMethodTypeEnum.codeOf(crowdExpressionDTO.getExpressionType());
        String baseSql = getJoinBaseSql(crowdExpressionMethodTypeEnum, crowdExpressionDTO.getStartTime(), crowdExpressionDTO.getEndTime());
        if (Lists.newArrayList(CrowdExpressionMethodTypeEnum.ADD_TIME).contains(crowdExpressionMethodTypeEnum)) {
            return baseSql;
        }
        return baseSql + " " + getJoinCountSql(crowdExpressionDTO.getCondition(), crowdExpressionDTO.getThreshold());
    }

    /**
     * 获取基础joinSql
     * @param crowdExpressionMethodTypeEnum 圈选表达式类型
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return joinSql
     */
    private String getJoinBaseSql(CrowdExpressionMethodTypeEnum crowdExpressionMethodTypeEnum, Long startTime, Long endTime) {
        if (!NumberUtils.validLong(startTime) || !NumberUtils.validLong(endTime)) {
            PlatformLogUtil.logFail("获取基础joinSql失败，圈选表达式DTO非法", LogListUtil.newArrayList(crowdExpressionMethodTypeEnum, startTime, endTime));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        String baseSql;
        switch (crowdExpressionMethodTypeEnum) {
            case REACH_FREQUENCY:
                baseSql = String.format(SwitchConfig.TOUCH_FREQUENCY_SQL, startTime, endTime);
                break;
            case CLICK_FREQUENCY:
                baseSql = String.format(SwitchConfig.CLICK_FREQUENCY_SQL, startTime, endTime);
                break;
            case ADD_TIME:
                baseSql = String.format(SwitchConfig.ADD_TIME_SQL, startTime, endTime, startTime, endTime);
                return baseSql;
            default:
                throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        return baseSql;
    }

    /**
     * 获取joinSql的统计部分
     * @param condition 条件
     * @param threshold 阈值
     * @return joinCountSql
     */
    private String getJoinCountSql(String condition, String threshold) {
        if (StringUtils.isBlank(condition) || StringUtils.isBlank(threshold)) {
            PlatformLogUtil.logFail("获取joinSql的统计部分失败，圈选表达式DTO非法", LogListUtil.newArrayList(condition, threshold));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        CrowdExpressionConditionEnum crowdExpressionConditionEnum = CrowdExpressionConditionEnum.codeOf(condition);
        if (Objects.isNull(crowdExpressionConditionEnum)) {
            PlatformLogUtil.logFail("查询客户sql获取失败，圈选表达式条件非法", LogListUtil.newArrayList(condition, threshold));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        if (!checkThreshold(threshold, crowdExpressionConditionEnum)) {
            PlatformLogUtil.logFail("查询客户sql获取失败，圈选表达式阈值非法", LogListUtil.newArrayList(condition, threshold));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }

        String countSql = "";
        switch (crowdExpressionConditionEnum) {
            case BIGGER_THAN:
                countSql = String.format(SwitchConfig.CROWD_SQL_THRESHOLD_CONDITION_SQL, ">", threshold);
                break;
            case SMALLER_THAN:
                countSql = String.format(SwitchConfig.CROWD_SQL_THRESHOLD_CONDITION_SQL, "<", threshold);
                break;
            case EQUAL:
                if (!checkThresholdIfZero(threshold, crowdExpressionConditionEnum)) {
                    countSql = String.format(SwitchConfig.CROWD_SQL_THRESHOLD_CONDITION_SQL, "=", threshold);
                }
                break;
            case BETWEEN:
                List<String> thresholdList = Arrays.asList(threshold.split(","));
                Integer threshold1 = NumberUtils.toInteger(thresholdList.get(0));
                Integer threshold2 = NumberUtils.toInteger(thresholdList.get(1));
                countSql = String.format(SwitchConfig.CROWD_SQL_THRESHOLD_CONDITION_SQL_BETWEEN, Math.min(threshold1, threshold2), Math.max(threshold1, threshold2));
                break;
            default:
                throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
        return countSql;
    }

    /**
     * 根据条件校验阈值
     *
     * @param threshold 阈值
     * @param crowdExpressionConditionEnum 阈值对比条件
     * @return 校验结果
     */
    private boolean checkThreshold(String threshold, CrowdExpressionConditionEnum crowdExpressionConditionEnum) {
        switch (crowdExpressionConditionEnum) {
            case BIGGER_THAN:
            case SMALLER_THAN:
            case EQUAL:
                Integer thresholdNum = NumberUtils.toInteger(threshold);
                if (Objects.isNull(thresholdNum) || thresholdNum < 0) {
                    PlatformLogUtil.logFail("大于、小于、等于类型的圈选表达式阈值不为自然数，非法", LogListUtil.newArrayList(threshold));
                    return false;
                }
                return true;
            case BETWEEN:
                List<String> thresholdList = Arrays.asList(threshold.split(","));
                if (thresholdList.size() != 2) {
                    PlatformLogUtil.logFail("介于类型的圈选表达式阈值数量不为2，非法", LogListUtil.newArrayList(threshold));
                    return false;
                }
                Integer thresholdL = NumberUtils.toInteger(thresholdList.get(0));
                Integer thresholdR = NumberUtils.toInteger(thresholdList.get(1));
                if (Objects.isNull(thresholdL) || thresholdL < 0 || Objects.isNull(thresholdR) || thresholdR < 0) {
                    PlatformLogUtil.logFail("介于类型的圈选表达式阈值不为自然数，非法", LogListUtil.newArrayList(threshold));
                    return false;
                }
                return true;
            default:
                PlatformLogUtil.logFail("圈选表达式条件非法", LogListUtil.newArrayList(crowdExpressionConditionEnum));
                return false;
        }
    }

    /**
     * 判断阈值是否为0
     *
     * @param threshold 阈值
     * @param crowdExpressionConditionEnum 阈值对比条件
     * @return 校验结果
     */
    private boolean checkThresholdIfZero(String threshold, CrowdExpressionConditionEnum crowdExpressionConditionEnum) {
        return NumberUtils.isEqual(0, NumberUtils.toInteger(threshold)) && Objects.equals(crowdExpressionConditionEnum, CrowdExpressionConditionEnum.EQUAL);
    }

    /**
     * 获取joinSql的join字段
     * @param crowdExpressionMethodTypeEnum 圈选方法类型
     * @return join字段
     */
    private String getJoinField(CrowdExpressionMethodTypeEnum crowdExpressionMethodTypeEnum) {
        switch (crowdExpressionMethodTypeEnum) {
            case CROWD_ID:
                return "user_id";
            case TAG:
            case ADD_TIME:
                return "external_user_id";
            case REACH_FREQUENCY:
            case CLICK_FREQUENCY:
                return "union_id";
            default:
                throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_CONFIG);
        }
    }

    private String getSqlWithIncludePart(CrowdExpressionMethodTypeEnum crowdExpressionMethodTypeEnum, String threshold, CrowdExpressionConditionEnum crowdExpressionConditionEnum) {
        if (Lists.newArrayList(CrowdExpressionMethodTypeEnum.REACH_FREQUENCY, CrowdExpressionMethodTypeEnum.CLICK_FREQUENCY).contains(crowdExpressionMethodTypeEnum)
                && checkThresholdIfZero(threshold, crowdExpressionConditionEnum)) {
            return BASE_ZERO_SQL_WITH_INCLUDE_PART;
        }
        return BASE_SQL_WITH_INCLUDE_PART;
    }

    private String getSqlWithIncludePartAndExcludePart(CrowdExpressionMethodTypeEnum crowdExpressionMethodTypeEnum, String threshold, CrowdExpressionConditionEnum crowdExpressionConditionEnum) {
        if (Lists.newArrayList(CrowdExpressionMethodTypeEnum.REACH_FREQUENCY, CrowdExpressionMethodTypeEnum.CLICK_FREQUENCY).contains(crowdExpressionMethodTypeEnum)
                && checkThresholdIfZero(threshold, crowdExpressionConditionEnum)) {
            return BASE_ZERO_SQL_WITH_INCLUDE_PART_AND_EXCLUDE_PART;
        }
        return BASE_SQL_WITH_INCLUDE_PART_AND_EXCLUDE_PART;
    }


    // ======================================================================================================================================================================================

    private List<TargetDataDTO> queryFromOldTask(List<String> wechatUserIdList, TaskInfoDO taskInfoDO, ActivityTargetTypeEnum targetType) throws Exception {
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        String projectName = MapUtils.getString(extInfo, TaskConstant.ODPS_PROJECT_NAME_FIELD_KEY);
        String tableName = MapUtils.getString(extInfo, TaskConstant.ODPS_TABLE_NAME_FIELD_KEY);
        Long crowdId = MapUtils.getLong(extInfo, TaskConstant.CROWD_ID_FIELD_KEY, -1L);
        String corpId = taskService.getCorpIdByTaskId(taskInfoDO.getId());
        Boolean allMatch = MapUtils.getBoolean(extInfo, TaskConstant.ALL_MATCH, false);
        List<String> scrmIncludeTagIdList = new ArrayList<>();
        List<String> scrmExcludeTagIdList = new ArrayList<>();
        if (extInfo.containsKey(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST)) {
            scrmIncludeTagIdList = extInfo.getObject(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
            });
        }
        if (extInfo.containsKey(TaskConstant.SCRM_EXCLUDE_TAG_ID_LIST)) {
            scrmExcludeTagIdList = extInfo.getObject(TaskConstant.SCRM_EXCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
            });
        }

        return query(wechatUserIdList, projectName, tableName, crowdId, corpId, allMatch, scrmIncludeTagIdList, scrmExcludeTagIdList, targetType);
    }

    /**
     * 从离线表中从获取发送数据
     *
     * @return List<TargetDataDTO>
     */
    public List<TargetDataDTO> query(
            List<String> wechatUserIdList,
            String projectName,
            String tableName,
            Long crowdId,
            String corpId,
            Boolean allMatch,
            List<String> scrmIncludeTagIdList,
            List<String> scrmExcludeTagIdList,
            ActivityTargetTypeEnum activityTargetTypeEnum
    ) throws Exception {
        List<TargetDataDTO> result = new ArrayList<>();
        String downSql = getOdpsSql(wechatUserIdList, projectName, tableName, crowdId, corpId, allMatch, scrmIncludeTagIdList, scrmExcludeTagIdList);
        PlatformLogUtil.logInfo("客户数据查询处理器，生成Odps查询语句", LogListUtil.newArrayList(downSql));

        InstanceTunnel.DownloadSession downloadSession = odpsManager.getDefaultDownloadSession(downSql);
        if (downloadSession == null) {
            Thread.sleep(sleepPeriod);
            downloadSession = odpsManager.getDefaultDownloadSession(downSql);
        }

        if (downloadSession == null) {
            PlatformLogUtil.logFail("客户数据查询处理器，下载odps数据出错", LogListUtil.newArrayList(downSql));
            throw new TripscrmException(TripSCRMErrorCode.QUERY_ODPS_FAIL);
        }

        long recordCount = downloadSession.getRecordCount();
        if (recordCount == 0) {
            PlatformLogUtil.logFail("客户数据查询处理器，odps数据为空", LogListUtil.newArrayList(downSql));
            throw new TripscrmException(TripSCRMErrorCode.EMPTY_ODPS_DATA);
        }

        PlatformLogUtil.logInfo("客户数据查询处理器，odps数据记录数", LogListUtil.newArrayList(downSql, recordCount));
        String targetIdField = getTargetIdField(activityTargetTypeEnum);
        // 读取全部数据到内存里
        Record record;
        RecordReader recordReader = downloadSession.openRecordReader(0, recordCount);
        while ((record = recordReader.read()) != null) {
            if (StringUtils.isBlank(record.getString(targetIdField)) || StringUtils.isBlank(record.getString("follow_user_id"))) {
                continue;
            }

            TargetDataDTO targetDataDTO = new TargetDataDTO();
            targetDataDTO.setTargetId(record.getString(targetIdField));
            targetDataDTO.setTargetType(activityTargetTypeEnum);
            JSONObject extInfo = new JSONObject();
            extInfo.put("sendUserId", record.getString("follow_user_id"));
            try {
                // 默认解析离线表中的ext_info字段内容
                String extInfoStr = record.getString("ext_info");
                if (StringUtils.isNotBlank(extInfoStr)) {
                    extInfo.putAll(JSONObject.parseObject(extInfoStr));
                }
            } catch (IllegalArgumentException ex) {
                // 不包含该列，不抛异常
            }
            targetDataDTO.setExtInfo(extInfo);
            result.add(targetDataDTO);
        }
        return result;
    }

    private String getOdpsSql(
            List<String> wechatUserIdList,
            String projectName,
            String tableName,
            Long crowdId,
            String corpId,
            Boolean allMatch,
            List<String> scrmIncludeTagIdList,
            List<String> scrmExcludeTagIdList
    ) {
        // 获取企微客户的sql
        String onlineUserIdListStr = wechatUserIdList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(","));
        String customerSql = "";
        String tagSQL = "";

        if (CollectionUtils.isNotEmpty(scrmIncludeTagIdList)) {
            String tagIdListStr = scrmIncludeTagIdList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(", "));
            if (allMatch) {
                tagSQL = String.format(includeAllScrmTagSql, tagIdListStr, scrmIncludeTagIdList.size());
            } else {
                tagSQL = String.format(includeAnyScrmTagSql, tagIdListStr);
            }
        }

        if (CollectionUtils.isNotEmpty(scrmExcludeTagIdList)) {
            String tagIdListStr = scrmExcludeTagIdList.stream().map(x -> "'" + x + "'").collect(Collectors.joining(", "));
            tagSQL = tagSQL + String.format(excludeAllScrmTagSql, tagIdListStr);
        }

        customerSql = String.format(wechatCustomerFetchSql, corpId, onlineUserIdListStr, tagSQL);

        boolean validOdps = !StringUtils.isBlank(projectName) && !StringUtils.isBlank(tableName);
        boolean validCrowd = NumberUtils.validLong(crowdId);

        if (!validOdps) {
            PlatformLogUtil.logInfo("客户数据查询处理器，odps表名为空", LogListUtil.newArrayList());
        }

        if (!validCrowd) {
            PlatformLogUtil.logInfo("客户数据查询处理器，人群Id为空", LogListUtil.newArrayList());
        }

        if (!validOdps && !validCrowd) {
            return customerSql + ";";
        }

        String downSql = "";
        // odps-sql
        if (validOdps) {
            String tablePath = projectName + "." + tableName;
            downSql = String.format(standardTaskOdpsFetchSql, tablePath, tablePath);
        }

        if (validCrowd) {
            // crowd-sql
            String crowdSql = String.format(crowdOdpsFetchSql, crowdId);
            if (validOdps) {
                downSql = String.format("SELECT COALESCE(A.user_id, B.user_id) AS user_id, A.ext_info AS ext_info FROM (%s) A FULL JOIN (%s) B ON A.user_id = B.user_id", downSql, crowdSql);
            } else {
                downSql = crowdSql;
            }
        }

        return String.format("SELECT A.user_id AS user_id, A.ext_info AS ext_info, B.union_id AS union_id, B.external_user_id AS external_user_id, B.follow_user_id AS follow_user_id FROM (%s) A JOIN (%s) B ON A.user_id = B.user_id;", downSql, customerSql);
    }

    private String getTargetIdField(ActivityTargetTypeEnum activityTargetTypeEnum) {
        switch (activityTargetTypeEnum) {
            case WX_UNION_ID:
                return "union_id";
            case WX_EXTERNAL_USERID:
                return "external_user_id";
            case TAOBAO_USER_ID:
                return "user_id";
            default:
                throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }
    }

    private List<String> getWechatUserIdList(TaskInfoDO taskInfoDO) {
        JSONObject extraInfoJson = JSONObject.parseObject(taskInfoDO.getExtInfo());
        String sendUserIdListStr = StringUtils.isNotBlank(taskInfoDO.getSendUserId()) ?
                taskInfoDO.getSendUserId() : ((String) extraInfoJson.getOrDefault("robotUser", ""));

        if (StringUtils.isBlank(sendUserIdListStr)) {
            return Optional.ofNullable(wechatUserService.getAutoMatchUserIdList(taskInfoDO)).orElse(new ArrayList<>());
        }
        return Arrays.stream(sendUserIdListStr.split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }
}
