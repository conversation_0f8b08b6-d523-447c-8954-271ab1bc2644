package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.request.TripSCRMJourneyRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMJourneyService;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskBuildContext;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.task.ability.old.WelcomeTaskServiceImpl;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
@Slf4j
@HSFProvider(serviceInterface = TripSCRMJourneyService.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TripSCRMJourneyServiceImpl implements TripSCRMJourneyService {
    private final TagRelationService tagRelationService;
    private final WelcomeTaskServiceImpl welcomeTaskService;
    private final WechatCustomerService wechatCustomerService;

    @Override
    @ServiceLog("行程管家-活动触发")
    public TripSCRMResult<Map<String, Long>> buildTask(TripSCRMJourneyRequest request) {
        Map<String, Long> result = new HashMap<>();
        try {
            List<String> subCodeList = hasPersonMaSubCodeList(request.getTagId(), request.getSubCodeList());
            for (String subCode : subCodeList) {
                TaskBuildContext taskBuildContext = new TaskBuildContext();
                taskBuildContext.setBizType(BizTypeEnum.PERSON_MA_OLD.getCode());
                taskBuildContext.setTargetId(request.getUid());
                taskBuildContext.setTargetType(1);
                taskBuildContext.setActivityId(1L);
                taskBuildContext.setTagId(request.getTagId());
                taskBuildContext.setSubCode(subCode);
                Map<String, String> extraInfo = new HashMap<>();
                extraInfo.put("needPush", "false");
                taskBuildContext.setExtraInfo(extraInfo);
                JSONObject context = new JSONObject();
                context.put("personMaTagId", request.getTagId() + "_" + subCode);
                context.put("groupMaTagId", request.getTagId() + "_" + subCode);
                taskBuildContext.setContext(context.toJSONString());
                Long taskId = welcomeTaskService.buildTask(taskBuildContext);
                if (NumberUtils.validLong(taskId)) {
                    result.put(subCode, taskId);
                }
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }

        return TripSCRMResult.success(result);
    }

    @Override
    @ServiceLog("行程管家-企微成员-客户好友关系查询")
    public TripSCRMResult<FollowUserInfo> getEnterpriseWechatFollowUserList(String uid) {
        if (!StringUtils.hasText(uid)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        FollowUserInfo followUserInfo = wechatCustomerService.listFollowUserForJourney(uid);
        return TripSCRMResult.success(followUserInfo);
    }

    private List<String> hasPersonMaSubCodeList(Long tagId, List<String> subCodeList) {
        //获取个码信息
        ItemTagRelationQuery tagRelationQuery = new ItemTagRelationQuery();
        tagRelationQuery.setTagId(tagId);
        tagRelationQuery.setItemType(BizTypeEnum.PERSON_MA_OLD.getCode());
        tagRelationQuery.setSubCodeList(subCodeList);
        PageInfo<ItemTagRelationDTO> tagRelationPageInfo = tagRelationService.pageQuery(tagRelationQuery);
        if (tagRelationPageInfo == null || CollectionUtils.isEmpty(tagRelationPageInfo.getList())) {
            PlatformLogUtil.logFail("pageQuery fail", LogListUtil.newArrayList(subCodeList, tagRelationQuery, tagRelationPageInfo));
            return new ArrayList<>();
        }

        return tagRelationPageInfo.getList().stream().map(ItemTagRelationDTO::getSubCode).distinct().collect(Collectors.toList());
    }
}
