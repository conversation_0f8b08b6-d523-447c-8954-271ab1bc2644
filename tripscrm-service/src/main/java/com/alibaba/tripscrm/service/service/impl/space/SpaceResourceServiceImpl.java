package com.alibaba.tripscrm.service.service.impl.space;

import com.alibaba.tripscrm.dal.mapper.tddl.SpaceResourceMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.service.space.SpaceResourceService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-08-15 17:35:11
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SpaceResourceServiceImpl implements SpaceResourceService {
    private final SpaceResourceMapper spaceResourceMapper;

    @Override
    public Integer insert(SpaceResourceDO spaceResourceDO) {
        if (Objects.isNull(spaceResourceDO) || Objects.isNull(spaceResourceDO.getTargetType()) || !NumberUtils.validLong(spaceResourceDO.getSpaceId())) {
            throw new RuntimeException("参数非法");
        }

        if (Objects.isNull(spaceResourceDO.getLastOperatorId())) {
            spaceResourceDO.setLastOperatorId(spaceResourceDO.getCreatorId());
        }

        return spaceResourceMapper.insert(spaceResourceDO);
    }

    @AteyeInvoker(description = "新增业务空间资源信息", paraDesc = "spaceId&targetType&targetId&adminList&creatorId&lastOperatorId")
    public Integer insertSpaceResource(Long spaceId, Byte targetType, String targetId, String adminList, String creatorId, String lastOperatorId) {
        SpaceResourceDO spaceResourceDO = new SpaceResourceDO();
        spaceResourceDO.setSpaceId(spaceId);
        spaceResourceDO.setTargetType(targetType);
        spaceResourceDO.setTargetId(targetId);
        spaceResourceDO.setAdminList(adminList);
        spaceResourceDO.setCreatorId(creatorId);
        spaceResourceDO.setLastOperatorId(lastOperatorId);
        return insert(spaceResourceDO);
    }

    @AteyeInvoker(description = "修改业务空间资源管理员信息", paraDesc = "id&adminList")
    public Integer updateAdminList(Long id, String adminList) {
        SpaceResourceDO spaceResourceDO = new SpaceResourceDO();
        spaceResourceDO.setId(id);
        spaceResourceDO.setAdminList(adminList);
        return spaceResourceMapper.updateByPrimaryKey(spaceResourceDO);
    }

    @AteyeInvoker(description = "修改业务空间资源Id信息", paraDesc = "id&targetId")
    public Integer updateTargetId(Long id, String targetId) {
        SpaceResourceDO spaceResourceDO = new SpaceResourceDO();
        spaceResourceDO.setId(id);
        spaceResourceDO.setTargetId(targetId);
        return spaceResourceMapper.updateByPrimaryKey(spaceResourceDO);
    }

    @AteyeInvoker(description = "删除业务空间资源信息", paraDesc = "id")
    public Integer deleteById(Long id) {
        return spaceResourceMapper.deleteByPrimaryKey(id);
    }

    @Override
    public Integer update(SpaceResourceDO spaceResourceDO) {
        if (Objects.isNull(spaceResourceDO) || !NumberUtils.validLong(spaceResourceDO.getId())) {
            throw new RuntimeException("参数非法");
        }
        return spaceResourceMapper.updateByPrimaryKey(spaceResourceDO);
    }

    @Override
    public List<SpaceResourceDO> listBySpaceIdAndResourceType(Long spaceId, ResourceTypeEnum resourceTypeEnum) {
        if (!NumberUtils.validLong(spaceId) || Objects.isNull(resourceTypeEnum)) {
            throw new RuntimeException("参数非法");
        }

        List<SpaceResourceDO> res = new ArrayList<>();
        for (long minId = 0; ; ) {
            List<SpaceResourceDO> selectResult = spaceResourceMapper.listBySpaceIdAndTargetTypeAndMinIdLimit(spaceId, resourceTypeEnum.getCode().byteValue(), minId, 300);
            if (CollectionUtils.isEmpty(selectResult)) {
                break;
            }

            minId = selectResult.stream().map(SpaceResourceDO::getId).max(Long::compareTo).orElse(0L);
            res.addAll(selectResult);
        }

        return res;
    }

    @Override
    @Cacheable(key = "'listBySpaceIdAndResourceType:' + #resourceTypeEnum.getCode() + '_' + #spaceId", value = "spaceResourceListManager")
    public List<SpaceResourceDO> listBySpaceIdAndResourceTypeWithCache(Long spaceId, ResourceTypeEnum resourceTypeEnum) {
        return listBySpaceIdAndResourceType(spaceId,resourceTypeEnum);
    }

    @Override
    public List<SpaceResourceDO> listByResourceType(ResourceTypeEnum resourceTypeEnum) {
        if (Objects.isNull(resourceTypeEnum)) {
            throw new RuntimeException("参数非法");
        }

        List<SpaceResourceDO> res = new ArrayList<>();
        for (long minId = 0; ; ) {
            List<SpaceResourceDO> selectResult = spaceResourceMapper.listByTargetTypeAndMinIdLimit(resourceTypeEnum.getCode().byteValue(), minId, 300);
            if (CollectionUtils.isEmpty(selectResult)) {
                break;
            }

            minId = selectResult.stream().map(SpaceResourceDO::getId).max(Long::compareTo).orElse(0L);
            res.addAll(selectResult);
        }

        return res;
    }

    @Override
    public List<SpaceResourceDO> listByResourceTypeAndResourceId(ResourceTypeEnum resourceTypeEnum, String resourceId) {
        List<SpaceResourceDO> result = spaceResourceMapper.listByTargetTypeAndTargetId(resourceTypeEnum.getCode().byteValue(), resourceId);
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    @Override
    public List<SpaceResourceDO> listByResourceTypesAndResourceId(List<ResourceTypeEnum> resourceTypeEnums, String resourceId) {
        ArrayList<SpaceResourceDO> result = new ArrayList<>();
        for (ResourceTypeEnum resourceTypeEnum : resourceTypeEnums) {
            List<SpaceResourceDO> spaceResourceDOS = listByResourceTypeAndResourceId(resourceTypeEnum, resourceId);
            result.addAll(CollectionUtils.isEmpty(spaceResourceDOS) ? Lists.newArrayList() : spaceResourceDOS);
        }
        return result;
    }

    @Override
    public List<SpaceResourceDO> listByResourceTypeAndResourceIdList(ResourceTypeEnum resourceTypeEnum, List<String> resourceIdList) {
        if (CollectionUtils.isEmpty(resourceIdList)) {
            return new ArrayList<>();
        }

        List<SpaceResourceDO> result = spaceResourceMapper.listByTargetTypeAndTargetIdList(resourceTypeEnum.getCode().byteValue(), resourceIdList);
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    @Override
    public List<SpaceResourceDO> listByResourceTypeAndSpaceIdAndResourceIdList(ResourceTypeEnum resourceTypeEnum, Long spaceId, List<String> resourceIdList) {
        if (CollectionUtils.isEmpty(resourceIdList)) {
            return new ArrayList<>();
        }

        List<SpaceResourceDO> result = spaceResourceMapper.listByTargetTypeAndSpaceIdAndResourceIdList(resourceTypeEnum.getCode().byteValue(), spaceId, resourceIdList);
        return CollectionUtils.isEmpty(result) ? Lists.newArrayList() : result;
    }

    @Override
    public SpaceResourceDO getBySpaceIdAndResourceTypeAndResourceId(Long spaceId, ResourceTypeEnum resourceTypeEnum, String resourceId) {
        return spaceResourceMapper.getBySpaceIdAndTargetTypeAndTargetId(spaceId, resourceTypeEnum.getCode().byteValue(), resourceId);
    }

    @Override
    public Integer deleteBySpaceIdAndResourceIdAndResourceType(Long spaceId, String resourceId, ResourceTypeEnum resourceTypeEnum) {
        return spaceResourceMapper.deleteBySpaceIdAndTargetTypeAndTargetId(spaceId, resourceTypeEnum.getCode().byteValue(), resourceId);
    }

    @Override
    public Integer deleteBySpaceId(Long spaceId) {
        return spaceResourceMapper.deleteBySpaceId(spaceId);
    }

}
