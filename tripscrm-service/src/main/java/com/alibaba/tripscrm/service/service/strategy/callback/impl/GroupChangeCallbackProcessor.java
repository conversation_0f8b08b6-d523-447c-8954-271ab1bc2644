package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.manager.opensearch.CustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.wechat.WechatGroupAutoOpenWorkManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupPoolService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.*;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WechatGroupSendMsgRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import com.tmall.beehive.common.lang.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;


/**
 * 群聊变更【微信回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupChangeCallbackProcessor implements ProxyCallbackProcessor {
    private final GroupRelationRepository groupRelationRepository;
    private final MetaqProducer metaqProducer;
    private final LdbTairManager ldbTairManager;
    private final WechatGroupAutoOpenWorkManager wechatGroupAutoOpenWorkManager;
    private final GroupService groupService;
    private final WechatGroupService wechatGroupService;
    private final WechatUserService wechatUserService;
    private final CustomerRelationOpenSearchManager customerRelationOpenSearchManager;
    private final ActivityContextService activityContextService;
    private final WechatGroupPoolService wechatGroupPoolService;

    private static final String CREATE_CHANGE_TYPE = "create";
    private static final String DISMISS_CHANGE_TYPE = "dismiss";
    private static final String UPDATE_CHANGE_TYPE = "update";
    private static final String ADD_MEMBER_UPDATE_DETAIL = "add_member";
    private static final String CHANGE_OWNER_UPDATE_DETAIL = "change_owner";
    private static final String DEL_MEMBER_UPDATE_DETAIL = "del_member";

    private static final Map<String, Consumer<ScrmCallbackMsg>> FUNCTION_MAP = new ConcurrentHashMap<>();

    @Switch(description = "企微成员入群欢迎语话术", name = "sendWechatUserWelcomeMessageContent")
    public static String sendWechatUserWelcomeMessageContent = "%s你好，欢迎加入群聊";

    @PostConstruct
    public void init() {
        FUNCTION_MAP.put(CREATE_CHANGE_TYPE, this::processCreateGroup);
        FUNCTION_MAP.put(DISMISS_CHANGE_TYPE, this::processDismissGroup);
        FUNCTION_MAP.put(UPDATE_CHANGE_TYPE, this::processUpdateGroup);
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        // 目前只处理入群消息
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        if (!content.containsKey(CallbackConstant.CHANGE_TYPE) || !content.containsKey(CallbackConstant.EVENT)) {
            return true;
        }

        String changeType = content.getString(CallbackConstant.CHANGE_TYPE);
        if (!FUNCTION_MAP.containsKey(changeType)) {
            PlatformLogUtil.logFail("暂不处理的消息类型", LogListUtil.newArrayList(scrmCallbackMsg));
            return true;
        }

        FUNCTION_MAP.get(changeType).accept(scrmCallbackMsg);
        return true;
    }

    private void processCreateGroup(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String changeType = content.getString(CallbackConstant.CHANGE_TYPE);
        String chatId = content.getString(CallbackConstant.CHAT_ID);

        JSONObject message = new JSONObject();
        message.put("chatId", chatId);
        message.put("updateDetail", changeType);
        message.put("corpId", scrmCallbackMsg.getPlatformCorpId());
        metaqProducer.send(MQEnum.WECHAT_GROUP_MEMBER_CHANGE, "", changeType, message.toJSONString());
    }

    private void processDismissGroup(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String changeType = content.getString(CallbackConstant.CHANGE_TYPE);
        String chatId = content.getString(CallbackConstant.CHAT_ID);
        wechatGroupPoolService.sync(chatId);

        JSONObject message = new JSONObject();
        message.put("chatId", chatId);
        message.put("updateDetail", changeType);
        message.put("corpId", scrmCallbackMsg.getPlatformCorpId());
        metaqProducer.send(MQEnum.WECHAT_GROUP_MEMBER_CHANGE, "", changeType, message.toJSONString());
    }

    private void processUpdateGroup(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String updateDetail = content.getString(CallbackConstant.UPDATE_DETAIL);
        // 只处理群成员变更
        if (Lists.newArrayList(ADD_MEMBER_UPDATE_DETAIL, DEL_MEMBER_UPDATE_DETAIL).contains(updateDetail)) {
            processMemberChange(scrmCallbackMsg);
            return;
        }

        if (Lists.newArrayList(CHANGE_OWNER_UPDATE_DETAIL).contains(updateDetail)) {
            processOwnerChange(scrmCallbackMsg);
        }
    }

    private void processOwnerChange(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String chatId = content.getString(CallbackConstant.CHAT_ID);
        wechatGroupPoolService.sync(chatId);
    }

    private void processMemberChange(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String updateDetail = content.getString(CallbackConstant.UPDATE_DETAIL);
        String chatId = content.getString(CallbackConstant.CHAT_ID);
        String memberChangeList = content.getString(CallbackConstant.MEMBER_CHANGE_LIST);
        JSONObject message = new JSONObject();
        message.put(CallbackConstant.CORP_ID, scrmCallbackMsg.getPlatformCorpId());
        message.put(CallbackConstant.CHAT_ID, chatId);
        message.put(CallbackConstant.UPDATE_DETAIL, updateDetail);
        message.put(CallbackConstant.MEMBER_CHANGE_LIST, memberChangeList);
        metaqProducer.send(MQEnum.WECHAT_GROUP_MEMBER_CHANGE, "", updateDetail, message.toJSONString());

        // 退群
        if (Objects.equals(DEL_MEMBER_UPDATE_DETAIL, updateDetail)) {
            // 只有企微成员退群才会触发自动关注群聊策略执行
            List<GroupRelationDO> memberChangeInfo = getMemberChangeInfo(chatId, memberChangeList, GroupRelationStatusEnum.NOT_MEMBER);
            if (CollectionUtils.isEmpty(memberChangeInfo)) {
                return;
            }

            if (memberChangeInfo.stream().anyMatch(groupRelationDO -> Objects.equals(GroupUserTypeEnum.USER, GroupUserTypeEnum.of(groupRelationDO.getUserType().intValue())))) {
                wechatGroupAutoOpenWorkManager.openWorkGroup(scrmCallbackMsg.getPlatformCorpId(), chatId, null);
            }
            return;
        }

        // 只有企微成员退群才会触发自动关注群聊策略执行
        List<GroupRelationDO> memberChangeInfo = getMemberChangeInfo(chatId, memberChangeList, GroupRelationStatusEnum.MEMBER);
        if (CollectionUtils.isEmpty(memberChangeInfo)) {
            return;
        }
        String joinScene = content.getString(CallbackConstant.JOIN_SCENE);
        // 客户入群方式（自定义参数）
        String state = getState(joinScene, memberChangeInfo);
        message.put(CallbackConstant.EXTERNAL_USER_ID, memberChangeList);
        message.put(CallbackConstant.STATE, Optional.ofNullable(state).orElse(""));
        // 统计群活码扫描入群数
        if (StringUtils.hasText(state)) {
            ldbTairManager.incr(TairConstant.WECHAT_JOIN_GROUP_JOIN_GROUP_COUNT_PREFIX + state, 1, 0, 86400 * 365 * 10);
            if (StringUtils.hasText(state) && Strings.isNumeric(state)) {
                ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(Long.parseLong(state));
                if (Objects.nonNull(activityTaskInfoBO) && NumberUtils.biggerThanZero(activityTaskInfoBO.getActivityId())) {
                    message.put(TaskConstant.ACTIVITY_ID, activityTaskInfoBO.getActivityId());
                }
            }
        }

        metaqProducer.send(MQEnum.WECHAT_JOIN_GROUP, "", updateDetail, message.toJSONString());
        metaqProducer.send(MQEnum.WECHAT_CUSTOMER_ADD_FOLLOW_USER_OR_JOIN_GROUP, "", updateDetail, message.toJSONString());
        // 发送欢迎语（私聊）
        processGroupWelcomeMessageByRobot(chatId, memberChangeList, message, updateDetail);
        // 监控外部企业用户
        processMonitorOtherCorpCustomerJoinGroup(chatId, memberChangeList, scrmCallbackMsg.getPlatformCorpId());
        // 如果是企微成员入群，需要邀请者发送消息，激活入群成员的会话列表，从而自动保存通讯录
        processSendWechatUserWelcomeMessage(memberChangeInfo);
    }

    private void processSendWechatUserWelcomeMessage(List<GroupRelationDO> joinMemberInfo) {
        GroupRelationDO wechatUserInfo = joinMemberInfo.stream().filter(x -> Objects.equals(GroupUserTypeEnum.USER, GroupUserTypeEnum.of(x.getUserType().intValue()))).findFirst().orElse(null);
        if (Objects.isNull(wechatUserInfo)) {
            return;
        }

        String invitorUserId = wechatUserInfo.getInvitor();
        if (!StringUtils.hasLength(invitorUserId)) {
            return;
        }

        WechatGroupVO wechatGroupVO = wechatGroupService.getWechatGroupVOByChatId(wechatUserInfo.getChatId());
        if (Objects.isNull(wechatGroupVO)) {
            return;
        }

        Long spaceId = wechatUserService.getSpaceId(wechatUserInfo.getUserId(), wechatUserInfo.getCorpId());
        if (!SwitchConfig.wechatUserClockInSpaceIdList.contains(spaceId)) {
            return;
        }

        WechatGroupSendMsgRequest request = new WechatGroupSendMsgRequest();
        request.setUserId(invitorUserId);
        request.setCorpId(wechatUserInfo.getCorpId());
        request.setChatId(wechatUserInfo.getChatId());
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgNum(0);
        messageBO.setMsgType(MessageTypeEnum.TEXT);
        messageBO.setMsgContent(String.format(sendWechatUserWelcomeMessageContent, wechatUserInfo.getName()));
        request.setMessageList(Lists.newArrayList(messageBO));
        if (!StringUtils.hasLength(request.getRequestId())) {
            request.setRequestId(UUID.randomUUID().toString());
        }
        ResultDO<String> resultDO = groupService.asyncSendMessage(request);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
            PlatformLogUtil.logFail("企微群入群场景，发送企微成员入群欢迎语失败", LogListUtil.newArrayList(invitorUserId, wechatUserInfo, resultDO));
        }
    }

    private List<GroupRelationDO> getMemberChangeInfo(String chatId, String memberChangeList, GroupRelationStatusEnum statusEnum) {
        // 可能包含企微成员和客户
        List<String> userIdList = Arrays.stream(memberChangeList.split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());
        List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(chatId), userIdList, SpaceInfoThreadLocalUtils.getCorpId(), statusEnum.getCode().byteValue());
        if (CollectionUtils.isEmpty(groupRelationList)) {
            PlatformLogUtil.logFail("企微群入群场景，查询入群人员信息为空", LogListUtil.newArrayList(chatId, memberChangeList));
            return new ArrayList<>();
        }

        return groupRelationList;
    }

    private String getState(String joinScene, List<GroupRelationDO> joinMemberInfo) {
        // 3是扫码进群
        if (!"3".equalsIgnoreCase(joinScene)) {
            PlatformLogUtil.logFail("企微群入群场景，非扫码进群场景", LogListUtil.newArrayList(joinScene));
            return null;
        }

        if (joinMemberInfo.size() > 1) {
            PlatformLogUtil.logFail("扫码进群场景，本次用户进群数目超过1个");
            return null;
        }

        GroupRelationDO groupRelationDO = joinMemberInfo.get(0);
        if (!Objects.equals(GroupUserTypeEnum.of(groupRelationDO.getUserType().intValue()), GroupUserTypeEnum.CUSTOMER)) {
            // 不是客户，不处理
            PlatformLogUtil.logFail("扫码进群场景，非客户(运营人员)不处理");
            return null;
        }

        return groupRelationDO.getState();
    }

    private void processGroupWelcomeMessageByRobot(String chatId, String memberChangeList, JSONObject message, String updateDetail) {
        message.put("targetType", ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
        // 查询入群用户所属企微号
        FollowUserQuery followUserQuery = new FollowUserQuery();
        followUserQuery.setExternalUserId(memberChangeList);
        followUserQuery.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode()));
        FollowUserInfo followUserInfo = customerRelationOpenSearchManager.getFollowUserInfo(followUserQuery);
        if (CollectionUtils.isEmpty(followUserInfo.getRelationList())) {
            PlatformLogUtil.logFail("企微群入群发送私聊欢迎语失败，查询入群人员所属企微号信息为空", LogListUtil.newArrayList(memberChangeList, followUserInfo));
            return;
        }
        // 如果所属企微号中有机器人在群内，则选择在群内的一个机器人发送欢迎语
        List<String> userIdList = followUserInfo.getRelationList().stream().map(FollowUserInfo.CustomerRelation::getUserId).collect(Collectors.toList());
        List<GroupRelationDO> groupRelationDOS = getMemberChangeInfo(chatId, String.join(",", userIdList), GroupRelationStatusEnum.MEMBER);
        if (!CollectionUtils.isEmpty(groupRelationDOS)) {
            message.put(CallbackConstant.USER_ID, groupRelationDOS.get(0).getUserId());
            metaqProducer.send(MQEnum.WECHAT_GROUP_WELCOME_MESSAGE_BY_ROBOT, "", updateDetail, message.toJSONString());
            PlatformLogUtil.logInfo("入群用户所属企微号中有机器人在群内，发送 企微客户入群（群欢迎语-私聊） MetaQ消息", message);
            return;
        }
        // 如果所属企微号都不在群内，选择和群主的空间相同的机器人发送欢迎语
        WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(chatId);
        if (Objects.isNull(wechatGroupDO)) {
            PlatformLogUtil.logFail("企微群入群发送私聊欢迎语失败，查询群信息为空", LogListUtil.newArrayList(chatId, memberChangeList, followUserInfo));
            return;
        }
        Long ownerUserSpaceId = wechatUserService.getSpaceId(wechatGroupDO.getOwnerUser(), SpaceInfoThreadLocalUtils.getCorpId());
        if (Objects.isNull(ownerUserSpaceId)) {
            PlatformLogUtil.logFail("企微群入群发送私聊欢迎语失败，查询群主空间信息为空", LogListUtil.newArrayList(chatId, memberChangeList, followUserInfo));
            return;
        }
        for (String userId : userIdList) {
            Long userSpaceId = wechatUserService.getSpaceId(userId, SpaceInfoThreadLocalUtils.getCorpId());
            if (NumberUtils.isEqualWithNul(ownerUserSpaceId, userSpaceId)) {
                message.put(CallbackConstant.USER_ID, userId);
                metaqProducer.send(MQEnum.WECHAT_GROUP_WELCOME_MESSAGE_BY_ROBOT, "", updateDetail, message.toJSONString());
                PlatformLogUtil.logInfo("入群用户所属企微号中没有机器人在群内，发送 企微客户入群（群欢迎语-私聊） MetaQ消息", message);
                return;
            }
        }
    }

    /**
     * 监控入群的外部企业用户
     *
     * @param chatId
     * @param externalUserId
     * @param corpId
     */
    private void processMonitorOtherCorpCustomerJoinGroup(String chatId, String externalUserId, String corpId) {
        List<GroupRelationDO> groupRelationDOS = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(chatId), Lists.newArrayList(externalUserId), corpId, GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (!CollectionUtils.isEmpty(groupRelationDOS) && Objects.equals(GroupUserTypeEnum.of(Integer.valueOf(groupRelationDOS.get(0).getUserType())), GroupUserTypeEnum.CUSTOMER) && !StringUtils.hasLength(groupRelationDOS.get(0).getUnionId())) {
            PlatformLogUtil.logInfo("外部企业用户进群进群，对用户进行监控", Lists.newArrayList(externalUserId, groupRelationDOS));
            JSONObject data = new JSONObject();
            WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(chatId);
            if (Objects.isNull(wechatGroupDO) || !StringUtils.hasLength(wechatGroupDO.getOwnerUser())) {
                PlatformLogUtil.logFail("外部企业用户进进群，监控失败，查询群信息为空", Lists.newArrayList(chatId, externalUserId, groupRelationDOS));
                return;
            }
            data.put("userId", wechatGroupDO.getOwnerUser());
            data.put("chatId", chatId);
            data.put("targetId", externalUserId);
            data.put("msgType", MessageTypeEnum.TEXT.getCode());
            data.put("senderType", GroupUserTypeEnum.CUSTOMER.getCode());
            data.put("message", "外部企业用户进群");
            data.put("corpId", corpId);
            Boolean result = metaqProducer.send(MQEnum.WECHAT_GROUP_ANTI_INFILTRATION_MONITOR, "", "", data.toJSONString());
            PlatformLogUtil.logInfo("外部企业用户进进群，对用户进行监控，消息发送结果", Lists.newArrayList(externalUserId, chatId, result));
        }
    }

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.GROUP_CHANGE;
    }
}
