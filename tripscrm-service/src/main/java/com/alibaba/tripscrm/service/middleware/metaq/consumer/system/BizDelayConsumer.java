package com.alibaba.tripscrm.service.middleware.metaq.consumer.system;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.domain.request.TripSCRMTaskBuildRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMTaskService;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.consumer.share.PlayShareRelationConsumer;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.BizDelayContext;
import com.alibaba.tripscrm.service.service.task.ability.old.second.FliggyPlayService;
import com.alibaba.tripscrm.service.service.task.ability.old.second.TouchService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.MetaQDelayLevelUtil;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.fliggy.fliggyplaycore.client.share.model.ShareActInfoDTO;
import com.fliggy.fliggyplaycore.client.share.model.ShareSignUpRecordDTO;
import com.taobao.ateye.annotation.Switch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 业务延迟消息
 */
@Slf4j
@Service(value = "bizDelayConsumer")
public class BizDelayConsumer implements MessageListenerConcurrently {
    @Resource
    private TripSCRMTaskService tripSCRMTaskService;
    @Resource
    private MetaqProducer metaqProducer;
    @Resource
    private FliggyPlayService fliggyPlayService;
    @Resource
    private TouchService touchService;

    @Switch(description = "临期任务ID", name = "expiringTaskId")
    private static Long expiringTaskId = 38L;
    @Switch(description = "临期仅剩1任务ID", name = "expiringTaskIdRemain1")
    private static Long expiringTaskIdRemain1 = 38L;
    @Switch(description = "过期任务ID", name = "expiredTaskId")
    private static Long expiredTaskId = 38L;

    @Switch(description = "临期触达ID", name = "expiringTouchId")
    private static String expiringTouchId = "product_jjkafNrFvPJAbxFnnodV";
    @Switch(description = "临期仅剩1助力触达ID", name = "expiringTouchIdRemain1")
    private static String expiringTouchIdRemain1 = "product_EyBlumTnLFvXkjQkFYME";
    @Switch(description = "过期触达ID", name = "expiredTouchId")
    private static String expiredTouchId = "product_KbIUppLJOABGOqDsXzuI";

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("receive", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("receiveMsg", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("receive message",  LogListUtil.newArrayList(message));
            BizDelayContext bizDelayContext = JSONObject.parseObject(message, BizDelayContext.class);
            // 延迟次数加1
            bizDelayContext.setDelayNum(bizDelayContext.getDelayNum() + 1);
            String key = bizDelayContext.getDelayKey() + "_" + bizDelayContext.getDelayNum();
            // 超过最大时间,要接力,按照最大时间来投递
            if (bizDelayContext.getDelayTime().compareTo(DateUtils.addSeconds(new Date(), MetaQDeleyLevel.LEVEL_18.getSeconds())) > 0) {
                metaqProducer.send(MQEnum.DELAY_BIZ_SCHEDULING, key, "", message, MetaQDeleyLevel.LEVEL_18.getLevel());
                return true;
            } else {
                // 超过1分钟,
                if (bizDelayContext.getDelayTime().compareTo(DateUtils.addMinutes(new Date(), 1)) > 0) {
                    metaqProducer.send(MQEnum.DELAY_BIZ_SCHEDULING, key, "", message, MetaQDelayLevelUtil.getBestDelayLevelWithMillis(bizDelayContext.getDelayTime().getTime() - System.currentTimeMillis()));
                    return true;
                }
                // 当前时间一分钟之内，执行真实操作
                sendReminder(bizDelayContext.getDelayMessage());
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
        }
        return true;
    }

    /**
     * 发送过期提醒
     *
     * @param message
     * @return
     */
    private boolean sendReminder(String message) {
        try {
            ShareSignUpRecordDTO shareSignUpRecordDTO = JSONObject.parseObject(message, ShareSignUpRecordDTO.class);
            // 查询主活动信息
            TripSCRMResult<ShareActInfoDTO> shareResult = fliggyPlayService.queryPlayActInfoByActId(shareSignUpRecordDTO.getPlayId(), shareSignUpRecordDTO.getUserId());
            if (Objects.isNull(shareResult) || !shareResult.isSuccess() || Objects.isNull(shareResult.getData())) {
                PlatformLogUtil.logFail("queryPlayActInfoByActId fail", LogListUtil.newArrayList(message));
            }
            Long establishedNumber = shareResult.getData().getShareProgressDTO().getEstablishedNumber();
            Long sharedCumulativeNum = (long) shareResult.getData().getActConfig().getSharedCumulativeNum();
            if (establishedNumber.equals(sharedCumulativeNum)) {
                PlatformLogUtil.logFail("establishedNumber = sharedCumulativeNum", LogListUtil.newArrayList(message));
                return true;
            }
            Long remainNum = sharedCumulativeNum - establishedNumber;
            Map<String, Object> ext = new HashMap<>();
            ext.put("establishedNumber", establishedNumber);
            ext.put("sharedCumulativeNum", sharedCumulativeNum);
            ext.put("remainNum", remainNum);
            ext.put("fissionRemindTitleText", PlayShareRelationConsumer.fissionRemindTitleText);
            TripSCRMTaskBuildRequest request = new TripSCRMTaskBuildRequest();
            request.setTargetType(ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode());
            request.setTargetId(shareSignUpRecordDTO.getUserId().toString());
            request.setTriggerTimeType(TriggerTimeEnum.REAL_TIME.getCode().toString());
            request.setExtInfo(ext);
            // 不同的状态发送不同的任务
            boolean expiring = shareSignUpRecordDTO.getExpireTime().compareTo(DateUtils.addMinutes(new Date(), 1)) > 0;
            if (expiring){
                request.setTaskId(remainNum == 1 ? expiringTaskIdRemain1 : expiringTaskId);
            }else {
                request.setTaskId(expiredTaskId);
            }
            TripSCRMResult<String> result = tripSCRMTaskService.buildTask(request);
            if (!result.isSuccess()) {
                PlatformLogUtil.logFail("buildTask fail", LogListUtil.newArrayList(message, request, result));
            }
            // 发送不同的触达策略
            Map<String, String> touchExtInfo = new HashMap<>();
            touchExtInfo.put("establishedNumber", establishedNumber.toString());
            touchExtInfo.put("sharedCumulativeNum", sharedCumulativeNum.toString());
            touchExtInfo.put("remainNum", remainNum.toString());
            touchExtInfo.put("fissionRemindTitleText", PlayShareRelationConsumer.fissionRemindTitleText);
            String templateId;
            if (expiring){
                templateId = remainNum == 1 ? expiringTouchIdRemain1 : expiringTouchId;
            }else {
                templateId = expiredTouchId;
            }
            if(!touchService.send(shareSignUpRecordDTO.getUserId().toString(), templateId, "28202301121449543207", touchExtInfo)) {
                PlatformLogUtil.logFail("sendTouch fail", LogListUtil.newArrayList(message, request));
            }
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
    }

}
