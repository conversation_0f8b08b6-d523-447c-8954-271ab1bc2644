package com.alibaba.tripscrm.service.service;


import com.alibaba.tripscrm.dal.model.domain.data.ScrmBlackListDO;
import com.alibaba.tripscrm.service.model.domain.query.ScrmBlackListQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-10-26 20:39:12
 */
public interface ScrmBlackListService {
    /**
     * 根据uid查询
     */
    List<ScrmBlackListDO> selectByCondition(ScrmBlackListQuery query);

    /**
     * 插入单条记录
     */
    Integer insert(ScrmBlackListDO record);

    /**
     * 存在更新，不存在插入
     */
    Integer upsert(ScrmBlackListDO record);

    /**
     * 更新单条记录
     */
    Integer updateById(ScrmBlackListDO record);

    /**
     * 根据id删除
     */
    Integer deleteById(Long id);

    /**
     * 更新单条记录
     */
    Integer updateByTargetIdAndTargetTypeAndType(ScrmBlackListDO record);
}
