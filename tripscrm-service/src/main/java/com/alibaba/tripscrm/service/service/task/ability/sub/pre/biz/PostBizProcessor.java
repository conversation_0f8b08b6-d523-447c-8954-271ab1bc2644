package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.log.ttlog.TtTaskExecuteLogUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;


/**
 * 单条任务数据处理_后置处理
 *
 * <AUTHOR>
 * @since 2024/4/22 11:56
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class PostBizProcessor implements BizProcessor {
    @Override
    @TaskExecuteLog("单条任务数据处理_后置处理")
    public TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception {
        // 任务执行埋点
        taskExecuteLog(context, todoTaskVO);
        long end = System.currentTimeMillis();
        PlatformLogUtil.logInfo("单条任务数据处理-后置处理，结束", LogListUtil.newArrayList(end - todoTaskVO.getStartTime(), context.getTaskId()));
        return TripSCRMResult.success(null);
    }

    private void taskExecuteLog(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO dataBodyVO = todoTaskVO.getData().get(0);
        String targetId = dataBodyVO.getTargetId();
        Integer targetType = dataBodyVO.getTargetType();
        Long materialId = AbstractTaskExecutor.getMaterialId(context, todoTaskVO);
        Integer messageCount = AbstractTaskExecutor.getMessageCount(context, todoTaskVO);
        TtTaskExecuteLogUtils.log(todoTaskVO.getTaskId(), todoTaskVO.getMainTaskInstanceId(), materialId, messageCount, targetId, targetType, AbstractTaskExecutor.getAbTestBucketId(todoTaskVO), todoTaskVO.getSuccess(), todoTaskVO.getAttribution(), todoTaskVO.getFailReason(), JSONObject.toJSONString(dataBodyVO.getContext()));
    }
}