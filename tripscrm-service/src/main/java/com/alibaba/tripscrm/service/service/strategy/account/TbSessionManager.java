package com.alibaba.tripscrm.service.service.strategy.account;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.manager.second.MemberManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.UserInfoVO;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.session.util.tools.SessionLoginHelper;
import com.taobao.uic.common.domain.BaseUserDO;
import com.taobao.uic.common.domain.ResultDO;
import com.taobao.uic.common.service.userinfo.client.UicReadServiceClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;

/**
 * 淘宝账号管理
 * <AUTHOR>
 * @Date 2024/1/12 15:01
 **/
@Component
public class TbSessionManager implements AccountManager {

    @Resource
    private UicReadServiceClient uicReadServiceClient;
    @Resource
    private MemberManager memberManager;

    @Override
    public boolean support(String serverName) {
        return SwitchConfig.TAO_BAO_ACCOUNT_SERVER_NAME_SUPPORT.contains(serverName);
    }

    @Override
    public boolean supportQuery(String serverName) {
        return true;
    }

    @Override
    public User getUserInWebThread() {
        // session获取
        HttpSession session = getRequestSession();
        if (session == null) {
            return null;
        }
        // 信息获取
        String userId = getUserId(session);
        String taoBaoId = getUserNick(session);
        if (StringUtils.isBlank(userId) || StringUtils.isBlank(taoBaoId)) {
            return null;
        }
        Pair<String, String> nickPair = memberManager.getAvatarNickPair(Long.valueOf(userId));
        // 对象组建
        User user = new User();
        user.setUserId(taoBaoId.toUpperCase());
        user.setUserName(nickPair.getRight());
        user.setIsInnerStaff(false);
        return user;
    }

    /**
     * 获取请求中的session
     * @return HttpSession
     */
    private HttpSession getRequestSession() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        if (request == null) {
            return null;
        }
        return request.getSession();
    }

    @Override
    public String getUserNameAndEmpId() {
        User user = getUserInWebThread();
        if (userInfoIsEmpty(user)) {
            return null;
        }
        if (StringUtils.isBlank(user.getUserName())) {
            return user.getUserId();
        }
        if (StringUtils.isBlank(user.getUserId())) {
            return user.getUserName();
        }
        return user.getUserName() + "-" + user.getUserId();
    }

    /**
     * 用户信息为空
     * @param user 用户信息对象
     * @return 信息是否为空
     */
    private Boolean userInfoIsEmpty(User user) {
        return user == null || (StringUtils.isBlank(user.getUserName()) && StringUtils.isBlank(user.getUserId()));
    }

    @Override
    public UserInfoVO getUserAndAccountIdByAccountId(String accountId) {
        return getUserInfoByAccountId(accountId);
    }

    @Override
    public List<UserInfoVO> batchGetUserByEmpId(List<String> accountIds) {
        ArrayList<UserInfoVO> userInfoVOList = new ArrayList<>();
        for (String accountId : accountIds) {
            UserInfoVO userInfoVO = getUserInfoByAccountId(accountId);
            if (userInfoVO != null) {
                userInfoVOList.add(userInfoVO);
            }
        }
        return userInfoVOList;
    }

    /**
     * 通过淘宝账号id获取用户信息
     * @param accountId 淘宝账号id
     * @return UserInfoVo
     */
    @AteyeInvoker(description = "淘宝账号id获取用户信息", paraDesc = "accountId")
    public UserInfoVO getUserInfoByAccountId(String accountId) {
        if (StringUtils.isEmpty(accountId)) {
            return null;
        }

        ResultDO<Long> userIdByNick = uicReadServiceClient.getUserIdByNick(accountId);
        if (userIdByNick == null || !userIdByNick.isSuccess() || userIdByNick.getModule() == null) {
            return null;
        }
        Pair<String, String> nickPair = memberManager.getAvatarNickPair(userIdByNick.getModule());
        return convertBaseUserToUserInfoVO(nickPair.getLeft(), nickPair.getRight(), String.valueOf(userIdByNick.getModule()), accountId);
    }

    @Override
    public User getUserByAccountId(String accountId) {
        UserInfoVO userInfoVO = getUserInfoByAccountId(accountId);
        if (userInfoVO == null) {
            return null;
        }
        User user = new User();
        user.setUserId(accountId.toUpperCase());
        user.setUserName(userInfoVO.getUserName());
        user.setIsInnerStaff(false);
        return user;
    }

    /**
     * 淘宝用户信息转化为UserInfoVO
     * @param avatar 淘宝用户头像
     * @param nickName 淘宝用户昵称
     * @param userId 淘系用户id
     * @param accountId 淘宝账户id
     * @return UserInfoVO
     */
    private UserInfoVO convertBaseUserToUserInfoVO(String avatar, String nickName, String userId, String accountId) {
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setEmpId(accountId.toUpperCase());
        userInfoVO.setBucId(userId);
        StringBuilder nameAndEmpId = new StringBuilder();
        if (StringUtils.isNotEmpty(nickName)) {
            nameAndEmpId.append(nickName);
        }
        nameAndEmpId.append("-");
        nameAndEmpId.append(accountId.toUpperCase());
        userInfoVO.setNameAndEmpId(nameAndEmpId.toString());
        userInfoVO.setUserName(nickName);
        userInfoVO.setNickNameCn(nickName);
        userInfoVO.setIcon(avatar);
        userInfoVO.setIsInnerStaff(false);
        return userInfoVO;
    }

    @Override
    public List<UserInfoVO> searchUser(String keyword) {
        ArrayList<UserInfoVO> userInfoVOList = new ArrayList<>();
        UserInfoVO userInfoVO = getUserInfoByAccountId(keyword);
        if (userInfoVO != null) {
            userInfoVOList.add(userInfoVO);
        }
        return userInfoVOList;
    }

    @Override
    public UserInfoVO getUserVoInWebThread() {
        return getUserAndAccountIdByAccountId(getUserNick(getRequestSession()));
    }

    /**
     * 批量根据用户id获取用户手机号
     * @param userIdList 账号id
     * @return 用户信息
     */
    public Map<Long, String> getPhoneListByUserIdList(List<Long> userIdList) {
        Map<Long, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(userIdList)) {
            return result;
        }

        List<List<Long>> userIdListPartition = Lists.partition(userIdList, 100);

        for (List<Long> partition : userIdListPartition) {
            ResultDO<List<BaseUserDO>> userResult = uicReadServiceClient.getBaseUserListByUserIds(StringUtils.join(partition, ","));
            if (userResult == null || !userResult.isSuccess() || userResult.getModule() == null) {
                PlatformLogUtil.logFail("批量根据用户id获取用户手机号，失败", LogListUtil.newArrayList(partition, userResult));
                continue;
            }

            PlatformLogUtil.logInfo("批量根据用户id获取用户手机号，成功", LogListUtil.newArrayList(partition, userResult));
            userResult.getModule()
                    .stream()
                    .filter(user -> Objects.nonNull(user) && Objects.nonNull(user.getMobilePhone()))
                    .forEach(user -> result.put(user.getUserId(), user.getMobilePhone()));
        }

        return result;
    }

    /**
     * 根据用户id获取用户手机号
     * @param userId 账号id
     * @return 用户信息
     */
    @AteyeInvoker(description = "根据uid获取用户手机号", paraDesc = "uid")
    public String getPhoneByUserId(Long userId) {
        ResultDO<BaseUserDO> userResult = uicReadServiceClient.getBaseUserByUserId(userId);
        PlatformLogUtil.logInfo("根据用户id获取用户手机号", LogListUtil.newArrayList(userId, userResult));
        if (userResult == null || !userResult.isSuccess() || userResult.getModule() == null) {
            return null;
        }
        return userResult.getModule().getMobilePhone();
    }

    /**
     * 根据用户id获取用户昵称
     * @param userId 账号id
     * @return 用户信息
     */
    @AteyeInvoker(description = "根据uid获取用户昵称", paraDesc = "uid")
    public String getNicknameByUserId(Long userId) {
        ResultDO<BaseUserDO> userResult = uicReadServiceClient.getBaseUserByUserId(userId);
        PlatformLogUtil.logFail("根据uid获取用户昵称", LogListUtil.newArrayList(userId, userResult));
        if (userResult == null || !userResult.isSuccess() || userResult.getModule() == null) {
            return null;
        }
        return userResult.getModule().getNick();
    }

    /**
     * 从请求session中获取userId
     * @param httpSession 请求session
     * @return 淘系userId
     */
    private String getUserId(HttpSession httpSession) {
        // 主账号
        if (SessionLoginHelper.isTaoMainUserLogin(httpSession)) {
            return SessionLoginHelper.getTaoUserIdNum(httpSession);
        }
        // 子账号
        if (SessionLoginHelper.isTaoSubUserLogin(httpSession)) {
            return SessionLoginHelper.getTaoSubUserId(httpSession);
        }
        return null;
    }

    /**
     * 从请求session中获取userNick
     * @param httpSession 请求session
     * @return 淘宝账号userNick
     */
    private String getUserNick(HttpSession httpSession) {
        // 主账号
        if (SessionLoginHelper.isTaoMainUserLogin(httpSession)) {
            return SessionLoginHelper.getTaoUserNick(httpSession);
        }
        // 子账号
        if (SessionLoginHelper.isTaoSubUserLogin(httpSession)) {
            return SessionLoginHelper.getTaoSubUserNick(httpSession);
        }
        return null;
    }

}
