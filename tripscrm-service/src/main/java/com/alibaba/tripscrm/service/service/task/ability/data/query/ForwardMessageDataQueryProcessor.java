package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupInfoMemberVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.dto.tag.TagCoverGroupDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.OdpsService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.GroupRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotTypeEnum;
import com.alibaba.tripzoo.proxy.model.UserGroupConfigBO;
import com.alibaba.tripzoo.proxy.request.UserGroupConfigRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.TripSCRMErrorCode.QUERY_ODPS_FAIL;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:04:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ForwardMessageDataQueryProcessor extends AbstractTaskDataProcessor {
    private final WechatUserService wechatUserService;
    private final TaskService taskService;
    private final ResourceRelationService resourceRelationService;
    private final OdpsService odpsService;
    private final GroupService groupService;
    private final GroupRelationService groupRelationService;
    private final WechatGroupService wechatGroupService;

    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.FORWARD_MESSAGE);
    }
    /**
     * 从离线中从获取发送数据
     *
     * @param context 任务执行上下文
     * @return TaskDataVO
     */
    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        Long taskId = context.getTaskId();
        Long taskInstanceId = context.getInstanceId();

        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());

        Set<String> chatIdSet = new HashSet<>();

        try {
            // 手工选择的群聊
            Set<String> manuallySpecifiedChatIds = getManuallySpecifiedChatIds(context.getTaskInfoDOSnapshot());
            PlatformLogUtil.logInfo("转发聊天记录数据查询器，手工选择的群聊", LogListUtil.newArrayList(taskInfoDO.getId(), manuallySpecifiedChatIds));
            chatIdSet.addAll(Optional.ofNullable(manuallySpecifiedChatIds).orElse(Sets.newHashSet()));
            // 活动覆盖的群聊
            List<Long> wechatJoinGroupIdActivityList =
                    extInfo.containsKey(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST)
                            ? extInfo.getObject(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, new TypeReference<List<Long>>() {
                    }) : new ArrayList<>();
            Set<String> joinGroupActivityCoverChatIds = getJoinGroupActivityCoverChatIds(wechatJoinGroupIdActivityList);
            PlatformLogUtil.logInfo("转发聊天记录数据查询器，活动覆盖的群聊", LogListUtil.newArrayList(taskInfoDO.getId(), joinGroupActivityCoverChatIds));
            chatIdSet.addAll(joinGroupActivityCoverChatIds);
            // 标签覆盖的群聊
            List<String> tagIdList = extInfo.containsKey(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST)
                    ? extInfo.getObject(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
            }) : Lists.newArrayList();
            Set<String> tagCoverChatIds = getTagCoverChatIds(taskService.getCorpIdByTaskId(taskId), tagIdList.stream().map(Long::valueOf).collect(Collectors.toSet()));
            PlatformLogUtil.logInfo("转发聊天记录数据查询器，标签覆盖的群聊", LogListUtil.newArrayList(taskInfoDO.getId(), tagCoverChatIds));
            chatIdSet.addAll(tagCoverChatIds);
            // 数据组装
            dataVO.setData(buildDataBodyVO(chatIdSet, taskId, taskInstanceId));
        } catch (Exception e) {
            PlatformLogUtil.logException("转发聊天记录数据查询器，查询任务离线数据异常", e.getMessage(), e, LogListUtil.newArrayList(context.getInstanceId(), context.getTaskId()));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】查询任务离线数据异常，任务ID:%s，实例ID:%s", taskId, taskInstanceId));
            throw new TripscrmException(QUERY_ODPS_FAIL);
        }
        dataVO.setTotalCount((long) dataVO.getData().size());
        return dataVO;
    }

    /**
     * 构建框架中传递的对象
     *
     * @param chatIdSet  群聊id列表
     * @param taskId     任务ID
     * @param instanceId 任务实例ID
     * @return 数据列表
     */
    private List<TaskDataVO.DataBodyVO> buildDataBodyVO(Set<String> chatIdSet, Long taskId, Long instanceId) {
        ArrayList<TaskDataVO.DataBodyVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(chatIdSet)) {
            return result;
        }
        // 在线的成员id列表
        Set<String> onlineWechatUserIdSet = wechatUserService.listByRobotStatus(Lists.newArrayList(RobotStatusEnum.ONLINE))
                .stream()
                .filter(wechatUser -> !SwitchConfig.wechatUserClockInCorpDepartmentIdList.contains(wechatUser.getCorpId() + "_" + wechatUser.getDepartmentId())
                        || Objects.equals(1, wechatUser.getActivateStatus()))
                .map(WechatUserDTO::getUserId)
                .collect(Collectors.toSet());
        PlatformLogUtil.logInfo("转发聊天记录数据查询器，当前在线成员列表", LogListUtil.newArrayList(taskId, onlineWechatUserIdSet));
        // 有效的群聊列表
        for (String chatId : chatIdSet) {
            TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
            //sop那边第一次过来的任务: 目标id为群管理员  群id单独存在extInfo中
            dataBodyVO.setTargetType(ActivityTargetTypeEnum.WX_USER_ID.getCode());

            JSONObject extInfoJson = new JSONObject();
            extInfoJson.put("chatId",chatId);
            List<String> canUseUserIdList = getGroupMessageCanUseUserId(chatId, onlineWechatUserIdSet );
            // 群主/管理员列表
            List<String> adminOrOwnerList = getGroupAdminOrOwnerList(chatId);
            if (CollectionUtils.isEmpty(canUseUserIdList) && CollectionUtils.isEmpty(adminOrOwnerList)) {
                PlatformLogUtil.logFail("转发聊天记录数据查询器，群内在线且保存该群到通信录的成员不存在/群内无平台号管理员", LogListUtil.newArrayList(taskId, instanceId, dataBodyVO.getTargetId()));
                continue;
            }
            List<String> canUseAdminOrOwnerIdList = canUseUserIdList.stream().filter(adminOrOwnerList::contains).collect(Collectors.toList());
            Collections.shuffle(canUseUserIdList);
            Collections.shuffle(canUseAdminOrOwnerIdList);
            // 优先使用群主/管理员
            if (!CollectionUtils.isEmpty(canUseAdminOrOwnerIdList)) {
                dataBodyVO.setTargetId(canUseAdminOrOwnerIdList.get(0));
            } else {
                dataBodyVO.setTargetId(canUseUserIdList.get(0));
            }
            String sendUserId = SwitchConfig.MESSAGE_FORWARD_RECEIVE_GROUP_ID.keySet().stream()
                    .findFirst()
                    .orElse(null);
            extInfoJson.put("sendUserId", sendUserId);
            extInfoJson.put("uuid", UUID.randomUUID().toString());
            dataBodyVO.setExtInfo(extInfoJson.toJSONString());
            result.add(dataBodyVO);
        }
        return result;
    }

    /**
     * 获取群聊中所有成员
     *
     * @param chatId 群聊id
     * @return 成员userId列表
     */
    private List<String> getGroupRobotList(String chatId) {
        if (!StringUtils.hasText(chatId)) {
            return Lists.newArrayList();
        }
        // 群聊中的企微成员列表
        List<WechatGroupInfoMemberVO> wechatGroupInfoMemberList = groupRelationService.listByChatIdAndUserType(chatId, Lists.newArrayList(GroupUserTypeEnum.USER));
        if (CollectionUtils.isEmpty(wechatGroupInfoMemberList)) {
            return Lists.newArrayList();
        }
        return wechatGroupInfoMemberList.stream().map(WechatGroupInfoMemberVO::getUserId).collect(Collectors.toList());
    }

    /**
     * 获取群聊中所有群主&管理员
     *
     * @param chatId 群聊id
     * @return 成员userId列表
     */
    private List<String> getGroupAdminOrOwnerList(String chatId) {
        List<String> result = new ArrayList<>();
        if (!StringUtils.hasText(chatId)) {
            return result;
        }
        List<String> userIdList = new ArrayList<>();

        // 群聊中的企微成员列表
        WechatGroupVO wechatGroupVO = wechatGroupService.getWechatGroupVOByChatId(chatId);
        if (Objects.isNull(wechatGroupVO)) {
            return result;
        }

        userIdList.add(wechatGroupVO.getOwnerUserId());
        userIdList.addAll(Optional.ofNullable(wechatGroupVO.getAdminUserIdList()).orElse(new ArrayList<>()));
        // 过滤掉非平台号
        result = wechatUserService.listById(userIdList)
                .stream()
                .filter(wechatUserDTO -> Objects.equals(RobotTypeEnum.of(wechatUserDTO.getRobotType()), RobotTypeEnum.ASSISTANT))
                .map(WechatUserDTO::getUserId)
                .collect(Collectors.toList());
        return result;
    }


    /**
     * 获取群聊消息可使用的用户id列表
     * 条件：企微成员+机器人在线+保存到通信录+平台号
     *
     * @param chatId                群聊id
     * @param onlineWechatUserIdSet 在线的用户id列表
     * @return 当前群聊可使用的用户id列表
     */
    private List<String> getGroupMessageCanUseUserId(String chatId, Set<String> onlineWechatUserIdSet) {
        List<String> result = Lists.newArrayList();
        if (!StringUtils.hasText(chatId)) {
            return result;
        }
        // 群内机器人列表
        List<String> groupRobotList = getGroupRobotList(chatId);
        if (CollectionUtils.isEmpty(groupRobotList)) {
            PlatformLogUtil.logFail("转发聊天记录数据查询器，想要触达的群聊不存在机器人", LogListUtil.newArrayList(chatId));
            return result;
        }
        // 机器人&&在线
        List<String> onlineUserIdList = new ArrayList<>(CollectionUtils.intersection(onlineWechatUserIdSet, groupRobotList));
        UserGroupConfigRequest request = new UserGroupConfigRequest();
        request.setChatIdList(Lists.newArrayList(chatId));
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        // 机器人&&在线&&保存通信录
        ResultDO<List<UserGroupConfigBO>> userGroupConfigInfo = groupService.getUserGroupConfigInfo(request);
        if (!userGroupConfigInfo.getSuccess() || CollectionUtils.isEmpty(userGroupConfigInfo.getModel())) {
            PlatformLogUtil.logFail("转发聊天记录数据查询器，获取用户群配置失败/无数据", LogListUtil.newArrayList(chatId, userGroupConfigInfo));
            return Lists.newArrayList();
        }
        List<String> concatGroupUserIdList = userGroupConfigInfo.getModel()
                .stream()
                .filter(userGroupConfigBO -> Objects.equals(1, userGroupConfigBO.getInConcat().intValue()))
                .map(UserGroupConfigBO::getUserId)
                .collect(Collectors.toList());
        List<String> userIdList = new ArrayList<>(CollectionUtils.intersection(onlineUserIdList, concatGroupUserIdList));
        if(CollectionUtils.isEmpty(userIdList)){
            PlatformLogUtil.logFail("转发聊天记录数据查询器，群内无可用管理员", LogListUtil.newArrayList(chatId, userGroupConfigInfo));
            return Lists.newArrayList();
        }

        // 过滤掉非平台号
        result = wechatUserService.listById(userIdList)
                .stream()
                .filter(wechatUserDTO -> Objects.equals(RobotTypeEnum.of(wechatUserDTO.getRobotType()), RobotTypeEnum.ASSISTANT))
                .map(wechatUserDTO -> wechatUserDTO.getUserId())
                .collect(Collectors.toList());
        return result;
    }

    /**
     * 从任务记录中获取手工指定的群聊列表
     *
     * @param taskInfoDO 任务对象
     * @return 群聊id列表
     */
    private Set<String> getManuallySpecifiedChatIds(TaskInfoDO taskInfoDO) {
        HashSet<String> result = new HashSet<>();
        if (taskInfoDO == null || !StringUtils.hasText(taskInfoDO.getExtInfo())) {
            return result;
        }
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        if (extInfo == null) {
            return result;
        }
        if (!extInfo.containsKey(TaskConstant.WECHAT_CHAT_ID_LIST)) {
            return result;
        }
        return extInfo.getObject(TaskConstant.WECHAT_CHAT_ID_LIST, new TypeReference<Set<String>>() {
        });
    }

    /**
     * 根据群活码活动id获取覆盖的群聊列表
     *
     * @param activityIdList 群活码获取活动id
     * @return 群聊id列表
     */
    private Set<String> getJoinGroupActivityCoverChatIds(List<Long> activityIdList) {
        Set<String> result = new HashSet<>();
        if (CollectionUtils.isEmpty(activityIdList)) {
            return result;
        }
        TaskQuery query = new TaskQuery();
        query.setActivityIdList(activityIdList);
        query.setType(TaskType.MANAGER_WECHAT_JOIN_GROUP.getCode());
        List<TaskInfoDO> taskList = taskService.query(query);
        if (CollectionUtils.isEmpty(taskList)) {
            return result;
        }
        List<Long> taskIdList = taskList.stream().map(TaskInfoDO::getId).collect(Collectors.toList());
        for (Long taskId : taskIdList) {
            result.addAll(getTaskCoverAllChatId(taskId));
        }

        return result;
    }

    /**
     * 获取群活码任务群聊池中的全部群聊
     *
     * @param taskId 群活码管理任务id
     * @return 群聊id列表
     */
    private Set<String> getTaskCoverAllChatId(Long taskId) {
        Set<String> result = new HashSet<>();
        if (!NumberUtils.validLong(taskId)) {
            return result;
        }
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
        query.setSourceType(ResourceTypeEnum.TASK.getCode().byteValue());
        query.setSourceId(String.valueOf(taskId));
        query.setTargetType(ResourceTypeEnum.CHAT_ID.getCode().byteValue());
        List<ResourceRelationDO> resourceRelationList = resourceRelationService.query(query);
        if (CollectionUtils.isEmpty(resourceRelationList)) {
            return result;
        }
        return resourceRelationList.stream().map(ResourceRelationDO::getTargetId).collect(Collectors.toSet());
    }

    /**
     * 获取标签覆盖的群聊id列表
     *
     * @param tagIdList 标签id列表
     * @return 群聊id列表
     */
    private Set<String> getTagCoverChatIds(String corpId, Set<Long> tagIdList) {
        HashSet<String> result = new HashSet<>();
        if (CollectionUtils.isEmpty(tagIdList)) {
            return result;
        }
        List<TagCoverGroupDTO> tagCoverGroupList = odpsService.queryTagCoverGroupInfo(corpId, tagIdList);
        if (CollectionUtils.isEmpty(tagCoverGroupList)) {
            return result;
        }
        return tagCoverGroupList.stream().map(TagCoverGroupDTO::getChatId).collect(Collectors.toSet());
    }

}
