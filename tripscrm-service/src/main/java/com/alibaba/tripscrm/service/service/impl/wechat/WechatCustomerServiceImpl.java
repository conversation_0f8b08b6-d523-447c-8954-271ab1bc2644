package com.alibaba.tripscrm.service.service.impl.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.tddl.CustomerRelationMapper;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatCustomerMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatCustomerDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.CustomerCountQuery;
import com.alibaba.tripscrm.dal.model.domain.result.CustomerCountResult;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.domain.*;
import com.alibaba.tripscrm.domain.enums.TagBizTypeEnum;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.convert.TagConverter;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.task.UserRelationStatusEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.opensearch.CustomerRelationForJourneyOpenSearchManager;
import com.alibaba.tripscrm.service.manager.opensearch.CustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.manager.opensearch.SCRMCustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.tag.TagInfoVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.domain.query.CustomerInfoQuery;
import com.alibaba.tripscrm.service.model.domain.query.CustomerRelationForJourneyQuery;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserPageQuery;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerListQuery;
import com.alibaba.tripscrm.service.model.domain.request.WechatCustomerAddTagRequest;
import com.alibaba.tripscrm.service.model.domain.request.WechatCustomerDeleteTagRequest;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.fusionchat.ProfileService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.SetUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023-07-04 21:30:18
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerServiceImpl implements WechatCustomerService {
    private final TagInfoService tagInfoService;
    private final CustomerRelationOpenSearchManager customerRelationOpenSearchManager;
    private final CustomerRelationForJourneyOpenSearchManager customerRelationForJourneyOpenSearchManager;
    private final WechatCustomerMapper wechatCustomerMapper;
    private final CustomerRelationMapper customerRelationMapper;
    private final GroupRelationRepository groupRelationRepository;
    private final LdbTairManager ldbTairManager;
    private final TagConverter tagConverter;
    private final TagRelationService tagRelationService;
    private final SCRMCustomerRelationOpenSearchManager scrmCustomerRelationOpenSearchManager;
    private final UicUtils uicUtils;
    private final ProfileService profileService;
    private final WechatUserService wechatUserService;

    @Override
    public List<CustomerRelationDO> listByUserId(String userId, String corpId) {
        List<CustomerRelationDO> result = new ArrayList<>();
        for (long minId = 0; minId >= 0; ) {
            List<CustomerRelationDO> customerRelationList = customerRelationMapper.listByUserIdAndCorpIdAndStatusAndMinIdLimit(userId, corpId, CustomerRelationStatusEnum.FRIEND.getCode().byteValue(), minId, 500);
            if (CollectionUtils.isEmpty(customerRelationList)) {
                break;
            }

            minId = customerRelationList.stream().mapToLong(CustomerRelationDO::getId).max().orElse(-1L);
            result.addAll(customerRelationList);
        }
        return result;
    }

    @Override
    public FollowUserInfo listFollowUserForJourney(String uid) {
        CustomerRelationForJourneyQuery query = new CustomerRelationForJourneyQuery();
        query.setUid(uid);
        return customerRelationForJourneyOpenSearchManager.getFollowUserInfoForJourney(query);
    }

    @Override
    public FollowUserInfo listFollowUser(FollowUserQuery query) {
        query.setPageSize(100);
        query.setPageNum(1);
        return customerRelationOpenSearchManager.getFollowUserInfo(query);
    }

    @Override
    public PageInfo<FollowUserCustomerRelationInfo> pageFollowUserInfo(FollowUserPageQuery query) {
        return customerRelationOpenSearchManager.pageFollowUserInfo(query);
    }

    @Override
    public PageInfoDTO<WechatCustomerVO> listPageInfo(WechatCustomerListQuery query) {
        // 根据客户关系信息，查询信息
        PageInfo<String> pageInfo = customerRelationOpenSearchManager.getCustomerExternalUserIdList(query);
        List<WechatCustomerVO> wechatCustomerVOList = listByExternalUserIdList(pageInfo.getList());
        Map<String, WechatCustomerVO> wechatCustomerVOMap =
                wechatCustomerVOList.stream().collect(Collectors.toMap(WechatCustomerVO::getExternalUserId, Function.identity()));
        PageInfoDTO<WechatCustomerVO> pageInfoDTO = PageUtils.getPageInfoDTO(
                wechatCustomerVOList.stream().filter(wechatCustomerVO -> pageInfo.getList().contains(wechatCustomerVO.getExternalUserId()))
                        .map(WechatCustomerVO::getExternalUserId).collect(Collectors.toList()), a -> {
                    WechatCustomerVO wechatCustomer = wechatCustomerVOMap.get(a);
                    FollowUserQuery followUserQuery = new FollowUserQuery();
                    followUserQuery.setDepartmentIdList(query.getDepartmentIdList());
                    followUserQuery.setExternalUserId(wechatCustomer.getExternalUserId());
                    followUserQuery.setPageSize(20);
                    FollowUserInfo followUserInfo = customerRelationOpenSearchManager.getFollowUserInfo(followUserQuery);
                    for (FollowUserInfo.CustomerRelation customerRelation : followUserInfo.getRelationList()) {
                        if (CollectionUtils.isEmpty(customerRelation.getTagInfoList())) {
                            PlatformLogUtil.logFail(customerRelation.getUserId() + "_企微标签列表不存在", LogListUtil.newArrayList(customerRelation));
                            continue;
                        }
                        for (TagInfo tagInfo : customerRelation.getTagInfoList()) {
                            if (Objects.isNull(tagInfo) || !StringUtils.hasLength(tagInfo.getId())) {
                                PlatformLogUtil.logInfo(customerRelation.getUserId() + "企微标签参数缺失", LogListUtil.newArrayList(tagInfo, customerRelation));
                                continue;
                            }
                            TagInfoDTO tagInfoDTO = tagInfoService.selectByTagId(tagInfo.getId());
                            if (Objects.isNull(tagInfoDTO)) {
                                PlatformLogUtil.logInfo("企微标签不存在", LogListUtil.newArrayList(tagInfo, customerRelation));
                                continue;
                            }
                            BeanUtils.copyProperties(tagInfoDTO, tagInfo);
                            if (!wechatCustomer.getTagIdList().contains(tagInfo.getId())) {
                                wechatCustomer.getTagIdList().add(tagInfo.getId());
                            }
                        }
                    }
                    List<TagInfoVO> tagList = tagInfoService.selectByTagIdList(wechatCustomer.getTagIdList())
                            .stream()
                            .filter(tagInfo -> Objects.equals(tagInfo.getSpaceId(), query.getSpaceId()) ||
                                    Arrays.asList(TagBizTypeEnum.CROWD_TAG,
                                            TagBizTypeEnum.SYSTEM,
                                            TagBizTypeEnum.ENTERPRISE_WECHAT_TAG).contains(tagInfo.getTagBizTypeEnum()))
                            .map(tagConverter::convert2VO)
                            .collect(Collectors.toList());
                    wechatCustomer.setTagList(tagList);
                    wechatCustomer.setFollowUserList(followUserInfo.getRelationList());
                    if (StringUtils.hasText(query.getChatId())) {
                        wechatCustomer.setStatus(getUserInGroupStatus(query, wechatCustomer));
                    }
                    return wechatCustomer;
                });
        pageInfoDTO.setPageNum(pageInfo.getPageNum());
        pageInfoDTO.setTotal(pageInfo.getTotal());
        return pageInfoDTO;
    }

    @Override
    public String getExternalUserIdByUnionId(String unionId) {
        if (!StringUtils.hasLength(unionId)) {
            return null;
        }

        WechatCustomerListQuery query = new WechatCustomerListQuery();
        query.setUnionId(unionId);
//        query.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode()));
        // 根据客户关系信息，查询信息
        PageInfo<String> pageInfo = customerRelationOpenSearchManager.getCustomerExternalUserIdList(query);
        if (Objects.isNull(pageInfo) || pageInfo.getSize() == 0) {
            return null;
        }

        return pageInfo.getList().get(0);
    }

    /**
     * 获取用户在群状态
     *
     * @param query          查询参数
     * @param wechatCustomer 企微用户
     * @return 状态值
     */
    private Integer getUserInGroupStatus(WechatCustomerListQuery query, WechatCustomerVO wechatCustomer) {
        List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(query.getChatId()), Lists.newArrayList(wechatCustomer.getExternalUserId()), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        Object o = null;
        if (SwitchConfig.CACHE_IS_OPEN) {
            String key = TairConstant.APPLY_TO_JOIN_GROUP_PREFIX + query.getChatId() + "_" + wechatCustomer.getExternalUserId();
            o = ldbTairManager.get(key);
        }
        if (!CollectionUtils.isEmpty(groupRelationList)) {
            return UserRelationStatusEnum.ADDED.getCode();
        } else if (Objects.nonNull(o)) {
            return UserRelationStatusEnum.TO_BE_ACCEPTED.getCode();
        } else {
            return UserRelationStatusEnum.NOT_ADDED.getCode();
        }

    }

    @Override
    public Integer addTag(WechatCustomerAddTagRequest request) {
        PlatformLogUtil.logInfo("增加客户身上的标签", LogListUtil.newArrayList(request));
        List<WechatCustomerDO> wechatCustomerList = wechatCustomerMapper.listByExternalUserIdAndCorpId(request.getExternalUserIdList(), SpaceInfoThreadLocalUtils.getCorpId());
        if (CollectionUtils.isEmpty(wechatCustomerList)) {
            return 0;
        }

        WechatCustomerDO wechatCustomer = wechatCustomerList.get(0);
        Set<String> newTagIdSet = new HashSet<>(request.getTagIdList());
        Set<String> oldTagIdSet = Arrays.stream(wechatCustomer.getTags().split(",")).filter(StringUtils::hasLength).collect(Collectors.toSet());
        List<String> newTagIdList = newTagIdSet.stream().filter(tagId -> !oldTagIdSet.contains(tagId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newTagIdList)) {
            return 0;
        }

        WechatCustomerDO record = new WechatCustomerDO();
        record.setExternalUserId(wechatCustomer.getExternalUserId());
        record.setTags(String.join(",", Sets.union(oldTagIdSet, newTagIdSet)));
        record.setCorpId(wechatCustomer.getCorpId());
        wechatCustomerMapper.updateByExternalUserIdAndCorpId(record);
        return newTagIdList.size();
    }


    @Override
    public void deleteTag(WechatCustomerDeleteTagRequest request) {
        PlatformLogUtil.logInfo("删除客户身上的标签", LogListUtil.newArrayList(request));
        List<WechatCustomerDO> wechatCustomerList = wechatCustomerMapper.listByExternalUserIdAndCorpId(request.getExternalUserIdList(), SpaceInfoThreadLocalUtils.getCorpId());
        if (CollectionUtils.isEmpty(wechatCustomerList)) {
            return;
        }

        WechatCustomerDO wechatCustomer = wechatCustomerList.get(0);
        Set<String> deleteTagIdSet = new HashSet<>(request.getTagIdList());
        Set<String> oldTagIdSet = Arrays.stream(wechatCustomer.getTags().split(",")).filter(StringUtils::hasLength).collect(Collectors.toSet());
        List<String> deleteTagIdList = deleteTagIdSet.stream().filter(oldTagIdSet::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deleteTagIdList)) {
            return;
        }

        WechatCustomerDO record = new WechatCustomerDO();
        record.setExternalUserId(wechatCustomer.getExternalUserId());
        record.setTags(oldTagIdSet.stream().filter(tagId -> !deleteTagIdSet.contains(tagId)).collect(Collectors.joining(",")));
        record.setCorpId(wechatCustomer.getCorpId());
        wechatCustomerMapper.updateByExternalUserIdAndCorpId(record);
    }

    @Override
    public List<WechatCustomerVO> listByExternalUserIdList(List<String> externalUserIdList) {
        if (CollectionUtils.isEmpty(externalUserIdList)) {
            return new ArrayList<>();
        }
        List<WechatCustomerDO> wechatCustomerList = wechatCustomerMapper.listByExternalUserIdAndCorpId(externalUserIdList, SpaceInfoThreadLocalUtils.getCorpId());
        if (CollectionUtils.isEmpty(wechatCustomerList)) {
            return new ArrayList<>();
        }

        return wechatCustomerList.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public String getUnionIdByExternalUserId(String externalUserId, String corpId) {
        if (!StringUtils.hasText(externalUserId) || !StringUtils.hasText(corpId)) {
            PlatformLogUtil.logInfo("根据externalUserId查询unionId，参数非法", LogListUtil.newArrayList(externalUserId, corpId));
            return null;
        }

        WechatCustomerDO wechatCustomerDO = wechatCustomerMapper.selectByExternalUserIdAndCorpId(externalUserId, corpId);
        return Optional.ofNullable(wechatCustomerDO).map(WechatCustomerDO::getUnionId).orElse(null);
    }

    @Override
    public List<String> getFollowUserIdListByUserIdListAndExternalUserId(List<String> userIdList, String externalUserId) {
        List<String> followUserIdList = customerRelationMapper.getFollowUserIdListByUserIdListAndExternalUserId(userIdList, externalUserId, CustomerRelationStatusEnum.FRIEND.getCode().byteValue(), SpaceInfoThreadLocalUtils.getCorpId());
        return CollectionUtils.isEmpty(followUserIdList) ? new ArrayList<>() : followUserIdList;
    }

    @Override
    public Long getCustomerCount(String userId, Boolean includeSingle) {
        CustomerInfoQuery query = new CustomerInfoQuery();
        query.setUserId(userId);
        query.setCountFields(Lists.newArrayList("follow_user_id"));
        query.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        if (!Optional.ofNullable(includeSingle).orElse(false)) {
            query.setStatus(CustomerRelationStatusEnum.FRIEND.getCode());
        }
        List<FieldCountInfo> countInfoList = customerRelationOpenSearchManager.getCustomerCount(query);
        if (CollectionUtils.isEmpty(countInfoList)) {
            return 0L;
        }

        Map<String, Long> mapCount = countInfoList.get(0).getCountMap();
        return mapCount.getOrDefault(userId, 0L);
    }

    @Override
    public Map<String, Long> getCustomerCount(List<String> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new HashMap<>();
        }

        CustomerCountQuery query = new CustomerCountQuery();
        query.setUserIdList(userIdList);
        query.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        query.setStatus(CustomerRelationStatusEnum.FRIEND.getCode().byteValue());
        List<CustomerCountResult> result = customerRelationMapper.countByUserIdList(query);
        Map<String, Long> countMap = result.stream().collect(Collectors.toMap(CustomerCountResult::getUserId, CustomerCountResult::getCount));
        for (String userId : userIdList) {
            countMap.putIfAbsent(userId, 0L);
        }

        return countMap;
    }

    @Override
    @Cacheable(key = "'getCustomerCount_' + 'userId:' + #userId + 'includeSingle' + #includeSingle", value = "sixHoursAnd512MaximumSizeCacheManager")
    public Long getCustomerCountWithCache(String userId, Boolean includeSingle) {
        return getCustomerCount(userId, includeSingle);
    }

    @Override
    @AteyeInvoker(description = "客户关系诸葛标签更新", paraDesc = "externalUserId&unionId&corpId")
    public boolean updateCustomerRelationCrowdTag(String unionId, String uid) {
        if (!StringUtils.hasLength(uid)) {
            PlatformLogUtil.logFail("客户关系诸葛标签更新失败，查询客户淘宝id失败", LogListUtil.newArrayList(unionId, uid));
            return false;
        }
        FollowUserQuery query = new FollowUserQuery();
        query.setUnionId(unionId);
        query.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode()));

        // 获取客户好友关系
        TripSCRMResult<FollowUserInfo> openSearchResult = scrmCustomerRelationOpenSearchManager.getFollowUserInfo(query);
        if (Objects.isNull(openSearchResult) || !openSearchResult.isSuccess()) {
            PlatformLogUtil.logFail("客户关系诸葛标签更新失败，查询客户好友关系失败", LogListUtil.newArrayList(unionId, uid));
            return false;
        }
        FollowUserInfo followUserInfo = openSearchResult.getData();
        // 检查标签并更新
        return updateTagsIfChanged(uid, followUserInfo);
    }

    @Override
    @AteyeInvoker(description = "根据externalUserId、corpId、unionId获取uid", paraDesc = "externalUserId&corpId&unionId")
    public String getUidByExternalUserIdAndCorpIdAndUnionId(String externalUserId, String corpId, String unionId) {
        if (!StringUtils.hasLength(unionId)) {
            unionId = getUnionIdByExternalUserId(externalUserId, corpId);
        }
        if (!StringUtils.hasLength(unionId)) {
            PlatformLogUtil.logFail("根据externalUserId、corpId、unionId获取uid失败，查询客户unionId失败", LogListUtil.newArrayList(externalUserId, corpId));
            return null;
        }
        String uid = uicUtils.getUidByUnionId(unionId);
        if (!StringUtils.hasLength(uid)) {
            PlatformLogUtil.logFail("根据externalUserId、corpId、unionId获取uid失败，查询客户淘宝id失败", LogListUtil.newArrayList(externalUserId, unionId, corpId));
            return null;
        }
        return uid;
    }

    /**
     * 更新标签值，如果新旧标签值不一致。
     *
     * @param uid               用户ID
     * @param followUserInfo    关系信息
     */
    private boolean updateTagsIfChanged(String uid, FollowUserInfo followUserInfo) {
        Set<String> oldTagValues = getOldTagValues(uid);
        Set<String> newTagValues = extractNewTagValues(followUserInfo);

        if (SetUtils.isEqualSet(oldTagValues, newTagValues)) {
            return true;
        }
        TripSCRMResult<Boolean> updateResult = profileService.updateTagValue(uid, SwitchConfig.CUSTOMER_RELATION_CROWD_TAG_CODE, String.join(",", newTagValues));
        return updateResult.isSuccess();
    }

    /**
     * 提取新的标签值集合。
     *
     * @param followUserInfo 关系信息
     * @return 返回新的标签值集合
     */
    private Set<String> extractNewTagValues(FollowUserInfo followUserInfo) {
        Set<String> tagValues = new HashSet<>();

        if (Objects.isNull(followUserInfo) || CollectionUtils.isEmpty(followUserInfo.getRelationList())) {
            tagValues.add("-1");
            return tagValues;
        }
        for (FollowUserInfo.CustomerRelation relation : followUserInfo.getRelationList()) {
            if (relation == null || !StringUtils.hasLength(relation.getUserId())) {
                continue;
            }
            List<WechatUserDTO> userDTOList = wechatUserService.listByUserIdWithCache(relation.getUserId());
            if (CollectionUtils.isEmpty(userDTOList)) {
                continue;
            }
            for (WechatUserDTO userDTO : userDTOList) {
                if (userDTO == null || !Objects.equals(userDTO.getStatus(), WechatUserStatusEnum.ACTIVE.getCode()) || !SwitchConfig.DEPARTMENT_INFO_MAP.containsKey(userDTO.getDepartmentId())) {
                    continue;
                }
                tagValues.add(String.valueOf(userDTO.getDepartmentId()));
            }
        }
        if (CollectionUtils.isEmpty(tagValues)) {
            tagValues.add("-1");
        }
        return tagValues;
    }

    /**
     * 获取旧的标签值集合。
     *
     * @param uid     用户ID
     * @return 返回旧的标签值集合
     */
    private Set<String> getOldTagValues(String uid) {
        Set<String> tagValues = new HashSet<>();
        TripSCRMResult<String> oldTagValue = profileService.getProfileValue(uid, SwitchConfig.CUSTOMER_RELATION_CROWD_TAG_CODE);
        if (Objects.isNull(oldTagValue) || !oldTagValue.isSuccess() || !StringUtils.hasLength(oldTagValue.getData())) {
            return tagValues;
        }
        tagValues.addAll(Lists.newArrayList(oldTagValue.getData().split(",")));
        return tagValues;
    }

    private WechatCustomerVO convert(WechatCustomerDO wechatCustomerDO) {
        WechatCustomerVO wechatCustomerVO = new WechatCustomerVO();
        BeanUtils.copyProperties(wechatCustomerDO, wechatCustomerVO);
        wechatCustomerVO.setGender(wechatCustomerDO.getGender().intValue());
        wechatCustomerVO.setType(wechatCustomerDO.getType().intValue());
        wechatCustomerVO.setFollowUserIdList(Lists.newArrayList(wechatCustomerDO.getFollowUser().split(",")));
        wechatCustomerVO.setAvatarUrl(wechatCustomerDO.getAvatar());
        wechatCustomerVO.setAvatarUrl(StringUtils.hasText(wechatCustomerVO.getAvatarUrl()) ? wechatCustomerVO.getAvatarUrl() : WxConstants.DEFAULT_AVATAR);
        ItemTagRelationQuery query = new ItemTagRelationQuery();
        query.setItemId(wechatCustomerDO.getExternalUserId() + "_" + wechatCustomerDO.getCorpId());
        query.setItemType(BizTypeEnum.WECHAT_CUSTOMER.getCode());
        query.setDeleted(IsDeleteEnum.NO.getCode());
        List<ItemTagRelationDTO> itemTagRelationList = tagRelationService.selectByCondition(query);
//        ItemTagRelationQuery wechatTagRelationQuery = new ItemTagRelationQuery();
//        wechatTagRelationQuery.setItemId(wechatCustomerDO.getExternalUserId() + "_" + wechatCustomerDO.getCorpId());
//        wechatTagRelationQuery.setItemType(BizTypeEnum.WECHAT_CUSTOMER_RELATION.getCode());
//        wechatTagRelationQuery.setDeleted(IsDeleteEnum.NO.getCode());
//        List<ItemTagRelationDTO> wechatItemTagRelationList = tagRelationService.selectByCondition(wechatTagRelationQuery);
//        itemTagRelationList.addAll(wechatItemTagRelationList);
        wechatCustomerVO.setTagIdList(itemTagRelationList.stream().map(ItemTagRelationDTO::getTag).distinct().collect(Collectors.toList()));
        return wechatCustomerVO;
    }

    private WechatCustomerDO convert(WechatCustomerVO wechatCustomerVO) {
        WechatCustomerDO wechatCustomerDO = new WechatCustomerDO();
        BeanUtils.copyProperties(wechatCustomerVO, wechatCustomerDO);
        if (Objects.nonNull(wechatCustomerVO.getTagIdList())) {
            wechatCustomerDO.setTags(wechatCustomerVO.getTagIdList().stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        wechatCustomerDO.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        return wechatCustomerDO;
    }

    private TagInfo convert(TagInfoDTO tagInfoDTO) {
        TagInfo tagInfo = new TagInfo();
        BeanUtils.copyProperties(tagInfoDTO, tagInfo);
        return tagInfo;
    }

    @AteyeInvoker(description = "删除标签", paraDesc = "externalUserId&tagId")
    public void deleteTagTest(String externalUserId, String tagId) {
        WechatCustomerDO wechatCustomerDO = new WechatCustomerDO();
        wechatCustomerDO.setExternalUserId(externalUserId);
        wechatCustomerDO.setTags(tagId);
        wechatCustomerDO.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wechatCustomerMapper.updateByExternalUserIdAndCorpId(wechatCustomerDO);
    }

}
