package com.alibaba.tripscrm.service.middleware.swtich;

import com.alibaba.tripscrm.service.enums.callpush.CallPushFatigueEnum;
import com.alibaba.tripscrm.service.enums.callpush.CrowdTypeEnum;
import com.alibaba.tripscrm.service.enums.task.CycleEnum;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.annotation.NameSpace;
import com.taobao.csp.switchcenter.bean.Switch;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/4/17
 */
@Component
@NameSpace(nameSpace = "com.alibaba.tripscrm.service.swtich.SwitchConfig")
public class SwitchConfig {
    @AppSwitch(des = "微信小程序mtop白名单", level = Switch.Level.p4)
    public static List<String> API_V_WHITE_LIST = new ArrayList<>();


    @AppSwitch(des = "行程管家是否随机活码", level = Switch.Level.p4)
    public static Boolean RAND_GROUP_MA = true;


    @AppSwitch(des = "行程管家是否取上下文", level = Switch.Level.p4)
    public static Boolean CONTEXT_ORDER = true;


    @AppSwitch(des = "新建群码是否自动群满建群（微信原生能力）", level = Switch.Level.p4)
    public static Boolean AUTO_CREATE_ROOM = false;

    @AppSwitch(des = "裂变助力欢迎语文案", level = Switch.Level.p4)
    public static Map<String, String> fissionHelpWelcomeText = new HashMap<>();

    @AppSwitch(des = "SCRM基础空间ID", level = Switch.Level.p4)
    public static Long SCRM_BASE_SPACE_ID = 14L;

    @AppSwitch(des = "BUC跳过验证的域名", level = Switch.Level.p4)
    public static List<String> BUC_SKIP_DOMAIN_NAME = Lists.newArrayList("scrm.fliggy.com", "pre-scrm.fliggy.com");

    @AppSwitch(des = "默认使用的用户域名", level = Switch.Level.p4)
    public static String DEFAULT_SERVER_NAME = "tripscrm-pre.alibaba-inc.com";

    @AppSwitch(des = "淘宝账号是否开启白名单", level = Switch.Level.p4)
    public static Boolean IS_OPEN_TAO_BAO_WHITE_LIST = false;

    @AppSwitch(des = "移除字符串中的空格", level = Switch.Level.p4)
    public static Boolean REMOVE_SPACE = false;

    @AppSwitch(des = "支持淘宝账号登陆的域名", level = Switch.Level.p4)
    public static List TAO_BAO_ACCOUNT_SERVER_NAME_SUPPORT = Lists.newArrayList("scrm.fliggy.com", "pre-scrm.fliggy.com");

    @AppSwitch(des = "支持BUC账号登陆的域名", level = Switch.Level.p4)
    public static List BUC_ACCOUNT_SERVER_NAME_SUPPORT = Lists.newArrayList("tripscrm.alibaba-inc.com", "tripscrm-pre.alibaba-inc.com");


    @AppSwitch(des = "请求好友缓存是否开启", level = Switch.Level.p4)
    public static boolean CACHE_IS_OPEN = false;

    static {
        fissionHelpWelcomeText.put("backUp", "助力失败，请从分享的链接点击助力");
        fissionHelpWelcomeText.put("success", "助力成功");
        fissionHelpWelcomeText.put("COUNT_OVER_LIMIT", "，计次超限");
        fissionHelpWelcomeText.put("success_ret", "成功");
        fissionHelpWelcomeText.put("fail_ret", "失败");
        fissionHelpWelcomeText.put("exception", "异常发生");

    }

    /**
     * 疲劳度管控白名单
     */
    @AppSwitch(des = "消息类型-疲劳度管控白名单", level = Switch.Level.p4)
    public static List<Long> messageTypeIdFatigueWhiteList = Lists.newArrayList(160L);

    @AppSwitch(des = "添加企微客户（欢迎语）事件源", level = Switch.Level.p4)
    public static Long addWechatCustomerEventSourceId = 18L;

    @AppSwitch(des = "企微添加客户/入群（客户打标）事件源", level = Switch.Level.p4)
    public static Long wechatCustomerAddTagEventSourceId = 20L;

    @AppSwitch(des = "通知企微成员在群内添加客户为好友-事件源", level = Switch.Level.p4)
    public static Long addWechatCustomerByGroupEventSourceId = 22L;

    @AppSwitch(des = "外呼数据处理-事件源", level = Switch.Level.p4)
    public static Long callPushDataProcessEventSourceId = 31L;

    @AppSwitch(des = "商家获客链接数据处理-事件源", level = Switch.Level.p4)
    public static Long sellerAcquisitionLinkDataProcessEventSourceId = 109L;

    @AppSwitch(des = "通过商家获客链接添加好友-事件源", level = Switch.Level.p4)
    public static Long addCustomerBySellerAcquisitionLinkEventSourceId = 110L;

    @AppSwitch(des = "通知初始化商家和客户群聊-事件源", level = Switch.Level.p4)
    public static Long notifyInitSellerAndCustomerGroupEventSourceId = 114L;

    @AppSwitch(des = "通知邀请商家和客户进入群聊-事件源", level = Switch.Level.p4)
    public static Long notifyInviteSellerAndCustomerJoinGroupEventSourceId = 111L;

    @AppSwitch(des = "邀请商家和客户进入群聊后-发送私聊消息-事件源", level = Switch.Level.p4)
    public static Long scrmInviteSellerCustomerJoinGroupSuccessForSingleChat = 115L;

    @AppSwitch(des = "邀请商家和客户进入群聊后-发送群聊消息-事件源", level = Switch.Level.p4)
    public static Long scrmInviteSellerCustomerJoinGroupSuccessForGroupChat = 112L;

    @AppSwitch(des = "商家获客活动-个人欢迎语素材", level = Switch.Level.p4)
    public static Long sellerAcquisitionActivityPersonalWelcomeMaterialId = 14376L;

    @AppSwitch(des = "商家获客活动-初始群聊消息素材", level = Switch.Level.p4)
    public static Long sellerAcquisitionActivityInitGroupChatMaterialId = 14375L;

    @AppSwitch(des = "商家获客活动-邀请入群后私聊消息素材", level = Switch.Level.p4)
    public static Long sellerAcquisitionActivityJoinGroupSuccessSingleChatMaterialId = 14378L;

    @AppSwitch(des = "通知企微成员通过手机号添加客户为好友-事件源", level = Switch.Level.p4)
    public static Long addCustomerByPhoneEventSourceId = 32L;

    @AppSwitch(des = "邀请客户入群请求发送成功-事件源", level = Switch.Level.p4)
    public static Long inviteCustomerJoinGroupRequestSendSuccessEventSourceId = 33L;

    @AppSwitch(des = "素材白名单", level = Switch.Level.p4)
    public static List<Long> materialWhiteList = Lists.newArrayList(16L, 17L, 18L, 19L, 20L, 21L, 22L);

    @AppSwitch(des = "聚合聊天素材白名单", level = Switch.Level.p4)
    public static List<Long> fusionChatMaterialWhiteList = Lists.newArrayList(18L);

    @AppSwitch(des = "SOP日志白名单", level = Switch.Level.p4)
    public static List<Integer> SOP_LOG_WHITE_LIST = Lists.newArrayList(SopTypeEnum.WECHAT_JOIN_GROUP.getCode());

    @AppSwitch(des = "百应外呼推送相关客户信息的人群配置", level = Switch.Level.p4)
    public static Map<String, Long> BAIYING_CALL_PUSH_VIP_CRAWD_CONFIG = new HashMap<>();

    static {
        BAIYING_CALL_PUSH_VIP_CRAWD_CONFIG.put(CrowdTypeEnum.F3_0_7_DAY_NO_VISIT.name(), 0L);
        BAIYING_CALL_PUSH_VIP_CRAWD_CONFIG.put(CrowdTypeEnum.F3_7_30_DAY_NO_VISIT.name(), 0L);
        BAIYING_CALL_PUSH_VIP_CRAWD_CONFIG.put(CrowdTypeEnum.F4$5_0_7_DAY_NO_VISIT.name(), 0L);
        BAIYING_CALL_PUSH_VIP_CRAWD_CONFIG.put(CrowdTypeEnum.F4$5_7_30_DAY_NO_VISIT.name(), 0L);
    }

    @AppSwitch(des = "百应外呼推送用户时候需要指定短信触达里面的链接地址,人群类型和地址的关系", level = Switch.Level.p4)
    public static Map<String, String> BAIYING_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP = new HashMap<>();

    static {
        BAIYING_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP.put(CrowdTypeEnum.F3_0_7_DAY_NO_VISIT.name(), "");
        BAIYING_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP.put(CrowdTypeEnum.F3_7_30_DAY_NO_VISIT.name(), "");
        BAIYING_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP.put(CrowdTypeEnum.F4$5_0_7_DAY_NO_VISIT.name(), "");
        BAIYING_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP.put(CrowdTypeEnum.F4$5_7_30_DAY_NO_VISIT.name(), "");
    }

    @AppSwitch(des = "百应外呼推送用户时更新本地是否开启异步", level = Switch.Level.p4)
    public static boolean BAIYING_CALL_PUSH_VIP_CRAWD_ASYNC_UPDATE = false;

    @AppSwitch(des = "百应外呼的外呼结果敏感标签集合", level = Switch.Level.p4)
    public static Set<String> BAIYING_CALL_PUSH_VIP_CRAWD_BLACK_WORDS = Sets.newHashSet("别发短信息", "别再打电话", "客户骂人", "差评/投诉");

    @AppSwitch(des = "羚羊外呼推送相关客户信息的人群配置", level = Switch.Level.p4)
    public static Map<String, Long> LINGYANG_CALL_PUSH_VIP_CRAWD_CONFIG = new HashMap<>();

    static {
        LINGYANG_CALL_PUSH_VIP_CRAWD_CONFIG.put(CrowdTypeEnum.F3_0_7_DAY_NO_VISIT.name(), 0L);
        LINGYANG_CALL_PUSH_VIP_CRAWD_CONFIG.put(CrowdTypeEnum.F3_7_30_DAY_NO_VISIT.name(), 0L);
        LINGYANG_CALL_PUSH_VIP_CRAWD_CONFIG.put(CrowdTypeEnum.F4$5_0_7_DAY_NO_VISIT.name(), 0L);
        LINGYANG_CALL_PUSH_VIP_CRAWD_CONFIG.put(CrowdTypeEnum.F4$5_7_30_DAY_NO_VISIT.name(), 0L);
    }

    @AppSwitch(des = "羚羊外呼推送用户时候需要指定短信触达里面的链接地址,人群类型和地址的关系", level = Switch.Level.p4)
    public static Map<String, String> LINGYANG_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP = new HashMap<>();

    static {
        LINGYANG_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP.put(CrowdTypeEnum.F3_0_7_DAY_NO_VISIT.name(), "");
        LINGYANG_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP.put(CrowdTypeEnum.F3_7_30_DAY_NO_VISIT.name(), "");
        LINGYANG_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP.put(CrowdTypeEnum.F4$5_0_7_DAY_NO_VISIT.name(), "");
        LINGYANG_CALL_PUSH_VIP_CRAWD_MSG_URL_MAP.put(CrowdTypeEnum.F4$5_7_30_DAY_NO_VISIT.name(), "");
    }

    @AppSwitch(des = "羚羊外呼推送用户时单次推送的数量上限", level = Switch.Level.p4)
    public static int LINGYANG_CALL_PUSH_VIP_CRAWD_MAX_SIZE = 200;

    @AppSwitch(des = "羚羊外呼推送用户时已推送成功是否可以重推", level = Switch.Level.p4)
    public static boolean LINGYANG_CALL_PUSH_VIP_CRAWD_PUSH_AGAIN_OPEN = false;

    @AppSwitch(des = "羚羊外呼推送用户时更新本地是否开启异步", level = Switch.Level.p4)
    public static boolean LINGYANG_CALL_PUSH_VIP_CRAWD_ASYNC_UPDATE = false;

    @AppSwitch(des = "小程序卡片默认属性", level = Switch.Level.p4)
    public static String MINI_PROGRAM_DEFAULT_PROP = "{\"thumb_fileid\":\"30680201020461305f020100020419de6d2602031e903802045cc7f46d0204654b5ab404343433343030373333345f3134343237303733325f393336386634356135336132373538383466653263303939373236623639646602010002027e2004000201010201000400\",\"hex_aes_key\":\"7069676A756E6478646465696C667961\",\"md5\":\"9368f45a53a275884fe2c099726b69df\",\"size\":32277,\"height\":660,\"width\":528}";

    @AppSwitch(des = "社区内容详情URL集合", level = Switch.Level.p4)
    public static Map<String, String> COMMUNITY_CONTENT_DETAIL_URL_MAP = Maps.newHashMap();

    static {
        COMMUNITY_CONTENT_DETAIL_URL_MAP.put("pre", "https://outfliggys.m.taobao.com/app/trip/rx-content-community/pages/wx-detail?contentId=%s");
        COMMUNITY_CONTENT_DETAIL_URL_MAP.put("online", "https://outfliggys.m.taobao.com/app/trip/rx-content-community/pages/wx-detail?contentId=%s");
    }

    @AppSwitch(des = "企微成员自动打卡企业&部门配置", level = Switch.Level.p4)
    public static List<String> wechatUserClockInCorpDepartmentIdList = Lists.newArrayList("1970326357976939_26", "1970326357976939_30");

    @AppSwitch(des = "企微成员自动打卡空间配置", level = Switch.Level.p4)
    public static List<Long> wechatUserClockInSpaceIdList = Lists.newArrayList(1L, 10L);

    @AppSwitch(des = "用户运营空间", level = Switch.Level.p4)
    public static Long userOperationSpaceId = 10L;

    @AppSwitch(des = "搜未购可用空间", level = Switch.Level.p4)
    public static List<Long> visitedNotPaySpaceIdList = Lists.newArrayList(1L, 10L, 66L, 133L);

    @AppSwitch(des = "微信现金红包空间配置", level = Switch.Level.p4)
    public static List<Long> wechatCashRedPacketSpaceIdList = Lists.newArrayList(1L, 10L);

    @AppSwitch(des = "个人活码推广链接兜底页匠页地址", level = Switch.Level.p4)
    public static String CONTACT_ME_MARKER_PAGE = "https://outfliggys.m.taobao.com/xj-csr/page/pcraft/pcraft/common/qiweiptsj?titleBarHidden=2&disableNav=YES&webViewBackgroundColor=ffffff&state=%s";

    @AppSwitch(des = "scrm系统同步的诸葛系统标签列表", level = Switch.Level.p4)
    public static List<String> ZHUGE_SYNC_LABEL_LIST = Lists.newArrayList("eight_crowd", "lbs_permanent_city", "lbs_permanent_province", "pred_age_level", "id_gender");

    @AppSwitch(des = "scrm系统公共空间Id", level = Switch.Level.p4)
    public static Long publicActivityId = 67L;

    @AppSwitch(des = "智能外呼展示空间", level = Switch.Level.p4)
    public static Set<Long> showCallPushSpaceId = Sets.newHashSet(1L, 10L);

    @AppSwitch(des = "主动发起好友单个机器人单日请求次数上限", level = Switch.Level.p4)
    public static int ADD_FRIENDS_SINGLE_USER_ID_DAY_MAX_LIMIT = 10;

    @AppSwitch(des = "单个外呼推送活动推送人数上限默认值", level = Switch.Level.p4)
    public static int CALL_PUSH_ACTIVITY_DEFAULT_PUSH_LIMIT = 200000;

    @AppSwitch(des = "主动发起好友单个机器人十分钟请求次数上限", level = Switch.Level.p4)
    public static int ADD_FRIENDS_SINGLE_USER_ID_TEN_MINUTES_MAX_LIMIT = 10;

    @AppSwitch(des = "主动发起好友请求的机器人已有好友数目限制", level = Switch.Level.p4)
    public static long ADD_FRIENDS_USER_ID_FRIENDS_NUM_MAX_LIMIT = 20000;

    @AppSwitch(des = "主动发起好友请求的人群类型和个人活码state的关系", level = Switch.Level.p4)
    public static Map<String, String> ADD_FRIENDS_CALL_CRAWD_TYPE_AND_STATE_RELATION = new HashMap<>();

    @AppSwitch(des = "企微成员的好友数量上限", level = Switch.Level.p4)
    public static Integer WECHAT_USER_FRIEND_COUNT_LIMIT = 19000;

    static {
        ADD_FRIENDS_CALL_CRAWD_TYPE_AND_STATE_RELATION.put(CrowdTypeEnum.F3_0_7_DAY_NO_VISIT.name(), "");
        ADD_FRIENDS_CALL_CRAWD_TYPE_AND_STATE_RELATION.put(CrowdTypeEnum.F3_7_30_DAY_NO_VISIT.name(), "");
        ADD_FRIENDS_CALL_CRAWD_TYPE_AND_STATE_RELATION.put(CrowdTypeEnum.F4$5_0_7_DAY_NO_VISIT.name(), "");
        ADD_FRIENDS_CALL_CRAWD_TYPE_AND_STATE_RELATION.put(CrowdTypeEnum.F4$5_7_30_DAY_NO_VISIT.name(), "");
    }

    @AppSwitch(des = "主动发起好友请求获取机器人指定corpId", level = Switch.Level.p4)
    public static String ADD_FRIENDS_CALL_USER_ID_CORP_ID = "1970326357976939";

    @AppSwitch(des = "系统标签同步的常驻城市列表", level = Switch.Level.p4)
    public static String SYSTEM_TAG_SYNC_LBS_PERMANENT_CITY_LIST_STR = "北京,天津,上海,重庆,石家庄,唐山,秦皇岛,邯郸,邢台,保定,张家口,承德,沧州,廊坊,衡水,太原,大同,阳泉,长治,晋城,朔州,晋中,运城,忻州,临汾,吕梁,呼和浩特,包头,乌海,赤峰,通辽,鄂尔多斯,呼伦贝尔,巴彦淖尔,乌兰察布,兴安,锡林郭勒,阿拉善,沈阳,大连,鞍山,抚顺,本溪,丹东,锦州,营口,阜新,辽阳,盘锦,铁岭,朝阳,葫芦岛,长春,吉林,四平,辽源,通化,白山,松原,白城,延边,哈尔滨,齐齐哈尔,鸡西,鹤岗,双鸭山,大庆,伊春,佳木斯,七台河,牡丹江,黑河,绥化,大兴安岭,南京,无锡,徐州,常州,苏州,南通,连云港,淮安,盐城,扬州,镇江,泰州,宿迁,杭州,宁波,温州,嘉兴,湖州,绍兴,金华,衢州,舟山,台州,丽水,合肥,芜湖,蚌埠,淮南,马鞍山,淮北,铜陵,安庆,黄山,滁州,阜阳,宿州,六安,亳州,池州,宣城,福州,厦门,莆田,三明,泉州,漳州,南平,龙岩,宁德,南昌,景德镇,萍乡,九江,新余,鹰潭,赣州,吉安,宜春,抚州,上饶,济南,青岛,淄博,枣庄,东营,烟台,潍坊,济宁,泰安,威海,日照,临沂,德州,聊城,滨州,菏泽,郑州,开封,洛阳,平顶山,安阳,鹤壁,新乡,焦作,濮阳,许昌,漯河,三门峡,南阳,商丘,信阳,周口,驻马店,武汉,黄石,十堰,宜昌,襄阳,鄂州,荆门,孝感,荆州,黄冈,咸宁,随州,长沙,株洲,湘潭,衡阳,邵阳,岳阳,常德,张家界,益阳,郴州,永州,怀化,娄底,湘西,广州,韶关,深圳,珠海,汕头,佛山,江门,湛江,茂名,肇庆,惠州,梅州,汕尾,河源,阳江,清远,东莞,中山,潮州,揭阳,云浮,南宁,柳州,桂林,梧州,北海,防城港,钦州,贵港,玉林,百色,贺州,河池,来宾,崇左,海口,三亚,三沙,儋州,成都,自贡,攀枝花,泸州,德阳,绵阳,广元,遂宁,内江,乐山,南充,眉山,宜宾,广安,达州,雅安,巴中,资阳,阿坝,甘孜,凉山,贵阳,六盘水,遵义,安顺,毕节,铜仁,黔西南,黔东南,黔南,昆明,曲靖,玉溪,保山,昭通,丽江,普洱,临沧,楚雄,红河,文山,西双版纳,大理,德宏,怒江,迪庆,拉萨,日喀则,昌都,林芝,山南,那曲,阿里,西安,铜川,宝鸡,咸阳,渭南,延安,汉中,榆林,安康,商洛,兰州,嘉峪关,金昌,白银,天水,武威,张掖,平凉,酒泉,庆阳,定西,陇南,临夏,甘南,西宁,海东,海北,黄南,海南,果洛,玉树,海西,银川,石嘴山,吴忠,固原,中卫,乌鲁木齐,克拉玛依,吐鲁番,哈密,昌吉,博尔塔拉,巴音郭楞,阿克苏,克孜勒苏,喀什,和田,伊犁,塔城,阿勒泰";

    @AppSwitch(des = "系统标签同步的常驻省份列表", level = Switch.Level.p4)
    public static String SYSTEM_TAG_SYNC_LBS_PERMANENT_PROVINCE_LIST_STR = "北京,天津,上海,重庆,河北,山西,内蒙古,辽宁,吉林,黑龙江,江苏,浙江,安徽,福建,江西,山东,河南,湖北,湖南,广东,广西,海南,四川,贵州,云南,西藏,陕西,甘肃,青海,宁夏,新疆,台湾,香港,澳门";

    @AppSwitch(des = "成员可关注的群聊数量上限", level = Switch.Level.p4)
    public static Integer USER_WORK_GROUP_LIMIT = 30;

    @AppSwitch(des = "社区内容发送形式", level = Switch.Level.p4)
    public static String CONTENT_SEND_TEMPLATE = "{\"messageList\":[{\"messageType\":\"miniprogram\",\"miniProgramConfig\":{\"supplyType\":\"communityContent\",\"data\":{\"id\":\"%s\",\"name\":\"%s\",\"url\":null},\"id\":\"%s\",\"cardTitle\":\"%s\",\"cardMainImage\":[\"%s\"]}}]}";

    @AppSwitch(des = "引流查询和客户拥有好友关系的部门", level = Switch.Level.p4)
    public static List<Integer> CUSTOMER_RELATION_DEPARTMENT = Lists.newArrayList(26);

    @AppSwitch(des = "定制师回调消息开关V2", level = Switch.Level.p4)
    public static Boolean CUSTOMIZER_MSG_CALLBACK_SWITCH_V2 = true;

    @AppSwitch(des = "标签关系是否写入旧数据开关", level = Switch.Level.p4)
    public static Boolean ITEM_TAG_RELATION_OLD_DATA_WRITE_SWITCH = true;

    @AppSwitch(des = "标签关系是否读取旧数据开关", level = Switch.Level.p4)
    public static Boolean ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH = true;

    @AppSwitch(des = "定制师回调消息开关V2", level = Switch.Level.p4)
    public static Map<String, String> CUSTOMIZER_MSG_PUSH_CONFIG = Maps.newHashMap();

    static {
        CUSTOMIZER_MSG_PUSH_CONFIG.put("actionCode", "product_SJFfqcvYmiqPgvRTlmfN");
        CUSTOMIZER_MSG_PUSH_CONFIG.put("bizCode", "56202305081739081320");
    }

    @AppSwitch(des = "外呼推送疲劳度配置", level = Switch.Level.p4)
    public static Map<String, Map<Integer, Integer>> CALL_PUSH_FATIGUE_CONFIG = Maps.newHashMap();

    static {
        // 推送，1天推一次
        Map<Integer, Integer> pushFatigueConfig = Maps.newHashMap();
        pushFatigueConfig.put(CycleEnum.DAY.getCode(), 1);
        CALL_PUSH_FATIGUE_CONFIG.put(CallPushFatigueEnum.PUSH.getCode(), pushFatigueConfig);

        // 接通，1月推一次
        Map<Integer, Integer> callSuccessFatigueConfig = Maps.newHashMap();
        callSuccessFatigueConfig.put(CycleEnum.MONTH.getCode(), 1);
        CALL_PUSH_FATIGUE_CONFIG.put(CallPushFatigueEnum.CALL_SUCCESS.getCode(), callSuccessFatigueConfig);

        // 未接通，半月推一次
        Map<Integer, Integer> callFailFatigueConfig = Maps.newHashMap();
        callFailFatigueConfig.put(CycleEnum.HALF_MONTH.getCode(), 1);
        CALL_PUSH_FATIGUE_CONFIG.put(CallPushFatigueEnum.CALL_FAIL.getCode(), callFailFatigueConfig);

        // 留样，2个月推一次
        Map<Integer, Integer> samplingFatigueConfig = Maps.newHashMap();
        samplingFatigueConfig.put(CycleEnum.TWO_MONTH.getCode(), 1);
        CALL_PUSH_FATIGUE_CONFIG.put(CallPushFatigueEnum.SAMPLING.getCode(), samplingFatigueConfig);
    }

    @AppSwitch(des = "素材变量替换卡点开关", level = Switch.Level.p4)
    public static Boolean MATERIAL_VARIABLE_STUCK_POINT = true;

    @AppSwitch(des = "二方转短链的小程序链接")
    public static List<String> SHORT_LINK_PRE = Lists.newArrayList("pages/main/act-webview?url=");

    @AppSwitch(des = "短链接入四海通拼接参数", level = Switch.Level.p4)
    public static Map<String, String> SHORT_LINK_SIHAITONG = Maps.newHashMap();

    @AppSwitch(des = "短链接入四海通拼接参数V2 (key:空间id)", level = Switch.Level.p4)
    public static Map<Long, Map<String, String>> SHORT_LINK_SIHAITONG_V2 = Maps.newHashMap();

    @AppSwitch(des = "支端链接入四海通拼接参数 (key:空间id)", level = Switch.Level.p4)
    public static Map<Long, Map<String, String>> ALIPAY_SHORT_LINK_SIHAITONG_V2 = Maps.newHashMap();

    static {
        // 推广id
        SHORT_LINK_SIHAITONG.put("fpid", "1147001");
        // 媒体id
        SHORT_LINK_SIHAITONG.put("mediaId", "311001");
        // 推广位id
        SHORT_LINK_SIHAITONG.put("positionId", "1166398");
        // 活动id
        SHORT_LINK_SIHAITONG.put("activityId", "multi-industry-wx");
    }

    static {
        Map<String, String> sht1 = new HashMap<>();
        // 推广id
        sht1.put("fpid", "1147001");
        // 媒体id
        sht1.put("mediaId", "311001");
        // 推广位id
        sht1.put("positionId", "1166398");
        // 活动id
        sht1.put("activityId", "multi-industry-wx");
        SHORT_LINK_SIHAITONG_V2.put(10L, sht1);
        Map<String, String> sht2 = new HashMap<>();
        // 推广id
        sht2.put("fpid", "1987005");
        // 媒体id
        sht2.put("mediaId", "2711001");
        // 推广位id
        sht2.put("positionId", "9404008");
        // 活动id
        sht2.put("activityId", "sht_zk");
        SHORT_LINK_SIHAITONG_V2.put(276L, sht2);
    }
    static {
        Map<String, String> sht1 = new HashMap<>();
        // 推广id
        sht1.put("fpid", "956002");
        // 媒体id
        sht1.put("mediaId", "1791002");
        // 推广位id
        sht1.put("positionId", "6178004");
        // 活动id
        sht1.put("activityId", "denghuo_sjtg");
        ALIPAY_SHORT_LINK_SIHAITONG_V2.put(10L, sht1);

    }

    @AppSwitch(des = "短链接入四海通空间配置", level = Switch.Level.p4)
    public static Long SHORT_LINK_SIHAITONG_SPACE = 10L;

    @AppSwitch(des = "短链接入四海通空间配置v2", level = Switch.Level.p4)
    public static List<Long> SHORT_LINK_SIHAITONG_SPACE_V2 = Arrays.asList(10L, 276L);

    @AppSwitch(des = "行业映射维护", level = Switch.Level.p4)
    public static Map<Byte, String> INDUSTRY_INFO_MAPPING = Maps.newHashMap();

    static {
        INDUSTRY_INFO_MAPPING.put((byte) 0, "平台");
        INDUSTRY_INFO_MAPPING.put((byte) 1, "保险");
        INDUSTRY_INFO_MAPPING.put((byte) 2, "火车票");
        INDUSTRY_INFO_MAPPING.put((byte) 3, "机票");
    }

    @AppSwitch(des = "企微客户入群（群欢迎语-私聊）事件源id", level = Switch.Level.p4)
    public static Long wechatCustomerJoinGroupWelcomeByRobotEventSourceId = 91L;

    @AppSwitch(des = "任务消息类型-IM类", level = Switch.Level.p4)
    public static Long TASK_MESSAGE_TYPE_IM = 160L;

    @AppSwitch(des = "企微客户入群（群欢迎语-私聊）消息场景", level = Switch.Level.p4)
    public static Long TASK_MESSAGE_SCENE_QUNMA_SILIAO_RW = 1000001L;

    @AppSwitch(des = "标签同步预发白名单", level = Switch.Level.p4)
    public static List<String> TAG_SYNC_PRE_WHITE_LIST = Lists.newArrayList("wma6eoWgAAV0FqKcWfKbOQH9MW4r-QGA");

    @AppSwitch(des = "消息转发收录群映射", level = Switch.Level.p4)
    public static Map<String, String> MESSAGE_FORWARD_RECEIVE_GROUP_ID = Maps.newHashMap();

    static {
        MESSAGE_FORWARD_RECEIVE_GROUP_ID.put("NiXiaoQiLaiZhenShiHaoTianQi", "wra6eoWgAA6EZDhZVur2iaMmwvuRgpIQ");
    }

    @AppSwitch(des = "聊天记录有效期分钟数", level = Switch.Level.p4)
    public static Long CHAT_HISTORY_VALIDITY_MINUTE = 10L;

    @AppSwitch(des = "转发消息到群管理员回调-事件源", level = Switch.Level.p4)
    public static Long ForwardMessage2GroupAdminCallbackEventSourceId = 92L;

    @AppSwitch(des = "聊天记录素材保存空间", level = Switch.Level.p4)
    public static Long CHAT_HISTORY_MATERIAL_SPACE_ID = 4L;

    @AppSwitch(des = "scrm系统同步的诸葛标签列表", level = Switch.Level.p4)
    public static List<String> CROWD_TAG_SYNC_LABEL_LIST = Lists.newArrayList("next_trvl_dom_dep_date", "next_trvl_intl_dep_date", "next_trvl_dom_biz_type", "next_trvl_intl_biz_type", "is_jt", "hl_level", "member_types", "lst_vst_time", "is_trip_user", "order_self_cnt_365d", "life_cycle", "user_interests", "point_get", "point_use", "point_expire", "activate_time", "activate_time_period", "id_gender", "pred_age_level", "baby_age_level_v2", "pred_is_undergraduate", "pred_life_stage_hasbaby", "vip_level_name", "pred_life_stage_marriedhaschild", "is_high_end_consumer", "purchase_level_vac", "purchase_level_htl", "purchase_level_traffic", "purchase_level", "prefer_advance_days_ie", "is_365_buyer", "is_next_trvl_dom_city_to_birth_receive", "os", "lst_vst_time_trip", "fst_vst_time_trip", "fst_vst_time", "prefer_region_name_weight", "dest_list", "mbr_list_htl", "visit_biz_list_7d", "visit_biz_list_30d", "visit_biz_list_90d", "visit_biz_list_365d", "visit_item_cate_level1_list_30d", "visit_item_cate_level2_list_30d", "visit_item_cate_level3_list_30d", "lst_pay_country_list_visa", "lst_pay_time_visa", "lst_pay_country_list_card", "lst_pay_time_mp", "lst_pay_city_list_mp", "lst_pay_time_vac", "lst_pay_abroad_city_list_vac", "lst_pay_domestic_city_list_vac", "lst_pay_domestic_time_vac", "prefer_price_htl", "can_push", "visit_days_trip_180d", "visit_days_trip_90d", "visit_days_trip_7d", "visit_days_trip_30d", "common_receive_province_180d", "common_receive_city_180d", "pred_life_stage", "next_trvl_dom_city", "next_trvl_intl_country", "next_trvl_intl_city", "alipay_fans", "lst_checkin_star_htl", "pay_brand_list_htl", "trade_cnt_domestic_htl_365d", "trade_cnt_international_htl_365d", "lst_book_city_htl", "is_sensitive_mbr", "pay_cate3_vac_30d", "holiday_prefer", "taobaofans", "is_point_get_7d", "is_point_get_30d", "is_invalid_30d", "is_point_use_90d", "point_account", "test_custom_label", "label_name", "fans_huokebao", "lst_pay_dep_time_et", "lst_pay_time_et", "pay_max_star_htl_365d", "lst_search_arr_city_et", "lst_search_time_et", "lst_search_dep_date_et", "flight_ie_user_tag", "template_code_list", "lst_visit_time_bybt_180d", "hkb_vnp_label", "hkb_fans_label", "hkb_menber_label", "hkb_repeat_label", "hkb_not_fans_label", "aware_label", "is_fliggy_emp", "test_custom_label_alipay", "if_package", "biz_cnt_1yr", "biz_cnt_hist", "pred_career_type", "flight_ie_user_max_country", "flight_ie_user_is_gangao", "flight_ie_user_is_scalper", "prefer_city_list", "lst_pay_time_jiesongfuwu_abroad", "lst_pay_time_jiesongfuwu", "lst_pay_time_guojizuche", "lst_pay_time_yongchebaobei", "lst_pay_time_guoneizuche", "lst_pay_time_train", "is_365_byr_usr_jiesongfuwu_abroad", "is_365_byr_usr_jiesongfuwu", "is_365_byr_usr_guojizuche", "is_365_byr_usr_yongchebaobei", "is_365_byr_usr_guoneizuche", "is_365_byr_usr_train", "slr_whole_chain", "hkb_dest_gs", "hbk_dest_jzh", "hbk_dest_jj", "hkb_dest_cy", "hkb_dest_sx", "hkb_dest_hn", "hkb_dest_sd", "hkb_dest_hnhb", "hkb_dest_yn", "hkb_dest_nm", "hkb_dest_xj", "hkb_dest_gx", "pred_has_car", "discount_sensitive", "birth_city", "birth_province", "lst_pay_time_hotel", "buy_airline_code_list", "buy_airline_name_list", "will_trip_recent_trd", "guoqing_trd_trip_will_level", "prefer_provinces_name_weight", "fraud_mbr_label", "chunjie_trd_trip_will_level", "laodong_trd_trip_will_level", "lst_buy_time_train", "bucket_id", "visit_ipv_7d", "result_start_time", "pred_life_stage_inlove", "activity_id_list", "visit_spm_list", "clk_spm_list", "is_1m_away_recall_365d", "base_qianke", "hotel_cem"
    );

    @AppSwitch(des = "满月企微空间ID", level = Switch.Level.p4)
    public static Long bdWorkSpaceId = 150L;

    @AppSwitch(des = "【新媒体微信群-群监控】报警群处理人", level = Switch.Level.p4)
    public static String MONITOR_RECORD_GROUP_MANAGER = "322980";

    @AppSwitch(des = "目标活动上下文过期时间（秒）", level = Switch.Level.p4)
    public static Integer targetActivityContextExpireSeconds = 86400 * 7;

    @AppSwitch(des = "搜未购-度假相关链接默认keyword", level = Switch.Level.p4)
    public static String SEARCH_NOT_PAY_VACATION_DEFAULT_KEYWORD = "出行地";

    @AppSwitch(des = "企微机器人服务商", level = Switch.Level.p4)
    public static List<Integer> ROBOT_ISV_LIST = Lists.newArrayList(IsvTypeEnum.BAI_YE.getCode(), IsvTypeEnum.BI_LIN.getCode());

    @AppSwitch(des = "群管理员数量上限", level = Switch.Level.p4)
    public static final Integer MAX_GROUP_ADMIN_COUNT = 3;

    @AppSwitch(des = "是否根据账号分配策略选取管理员开关", level = Switch.Level.p4)
    public static Boolean SELECT_ADMIN_USER_BY_ISV_ROUTE_STRATEGY = false;

    @AppSwitch(des = "度假相关类目ID", level = Switch.Level.p4)
    public static List<Long> VACATION_CATE_ID_LIST = Lists.newArrayList(50258004L, 50272002L, 50258005L, 50278002L, 124770001L, 124488003L, 124720017L, 124864009L, 125352005L, 125456001L, 50012917L, 127220006L, 50276003L, 125310001L, 50462016L, 124866006L, 201173810L, 125094026L, 201301317L);

    @AppSwitch(des = "度假相关类目目的地缺省值", level = Switch.Level.p4)
    public static List<String> VACATION_CATE_DEFAULT_ARR_CITY_NAME_LIST = Lists.newArrayList("-1", "null");

    @AppSwitch(des = "券过期自动化事件-预发白名单", level = Switch.Level.p4)
    public static List<String> COUPON_EXPIRING_WHITE_LIST = Lists.newArrayList("wma6eoWgAAV0FqKcWfKbOQH9MW4r-QGA");

    @AppSwitch(des = "商家获客渠道Id列表", level = Switch.Level.p4)
    public static List<String> sellerAcquisitionChannelIdList = Lists.newArrayList("12387", "12388");

    @AppSwitch(des = "商家获客渠道Id-视频号小店", level = Switch.Level.p4)
    public static String sellerAcquisitionChannelIdForWechatVideoRoom = "12388";

    @AppSwitch(des = "商家企微组织Id", level = Switch.Level.p4)
    public static String sellerCorpId = "1970325277989527";

    @AppSwitch(des = "商家企微-平台运营组空间Id", level = Switch.Level.p4)
    public static Long sellerCorpPlatformSpaceId = 133L;

    @AppSwitch(des = "商家获客链接", level = Switch.Level.p2)
    public static String sellerAcquisitionLinkUrl = "https://www.baidu.com";

    @AppSwitch(des = "微信太阳码小程序链接", level = Switch.Level.p2)
    public static String unlimitQrCodeSceneUrl = "pages/utils/weappcode/index";

    @AppSwitch(des = "商家企微-平台运营组账号下最少多少个可用群才透出", level = Switch.Level.p4)
    public static Integer sellerCorpPlatformUserMinValidGroupCountForAcquisition = 3;

    @AppSwitch(des = "商家企微-平台运营组账号下最少多少个可用群聊", level = Switch.Level.p4)
    public static Integer sellerCorpPlatformUserMinValidGroupCountForPool = 30;

    @AppSwitch(des = "单企微号-每天创建企微群数量上限", level = Switch.Level.p4)
    public static Integer wechatGroupCreateCountPerDay = 49;

    @AppSwitch(des = "单企微号-每小时创建企微群数量上限", level = Switch.Level.p4)
    public static Integer wechatGroupCreateCountPerHour = 9;

    @AppSwitch(des = "单企微号-每分钟创建企微群数量上限", level = Switch.Level.p4)
    public static Integer wechatGroupCreateCountPerMinute = 1;

    @AppSwitch(des = "单企微号-每天修改企微群配置数量上限", level = Switch.Level.p4)
    public static Integer wechatGroupModifyConfigCountPerDay = 29;

    @AppSwitch(des = "单企微号-每小时修改企微群配置数量上限", level = Switch.Level.p4)
    public static Integer wechatGroupModifyConfigCountPerHour = 9;

    @AppSwitch(des = "单企微号-每分钟修改企微群配置数量上限", level = Switch.Level.p4)
    public static Integer wechatGroupModifyConfigCountPerMinute = 1;

    @AppSwitch(des = "商家企微-可用群聊告警阈值", level = Switch.Level.p4)
    public static Integer sellerCorpValidGroupCountForAlarm = 100;

    @AppSwitch(des = "商家企微-可用平台运营组账号告警阈值", level = Switch.Level.p4)
    public static Integer sellerCorpValidPlatformUserCountForAlarm = 10;

    @AppSwitch(des = "商家获客-类目信息对应标签组Id", level = Switch.Level.p4)
    public static Long sellerAcquisitionCategoryTagGroupId = 228L;

    @AppSwitch(des = "商家获客-目的地城市行政区划对应标签组Id", level = Switch.Level.p4)
    public static Long sellerAcquisitionArrCityDivisionIdTagGroupId = 229L;


    /**
     * 商家企微透出父级部门树id列表
     */
    @AppSwitch(des = "商家企微透出父级部门树id列表", level = Switch.Level.p4)
    public static List<Integer> sellerCorpParentDepartmentIdList = Lists.newArrayList(90, 266, 267);

    /**
     * 预发地址：https://pre-fsc.fliggy.com/#/contract/ExpandCooperateProgress?signTaskIds=
     * 线上地址：https://fsc.fliggy.com/#/contract/ExpandCooperateProgress?signTaskIds=
     */
    @AppSwitch(des = "商家签署协议链接", level = Switch.Level.p4)
    public static String sellerHasSignAgreementLink = "https://fsc.fliggy.com/#/contract/ExpandCooperateProgress?signTaskIds=";

    @AppSwitch(des = "企微2.0合同起草ID", level = Switch.Level.p4)
    public static Long bizAgtVersionId = 638L;

    @AppSwitch(des = "群聊at所有人字符串", level = Switch.Level.p4)
    public static String GROUP_CHAT_AT_ALL_STR = "All";

    @AppSwitch(des = "聊天记录去重时间阈值", level = Switch.Level.p4)
    public static Integer CHAT_RECORD_DUPLICATE_TIME_THRESHOLD = 60;

    @AppSwitch(des = "微信现金红包活动落地页链接", level = Switch.Level.p4)
    public static String wechatCashRedPacketActivityLandingPageUrl = "https://market.m.taobao.com/app/trip/rx-qw-redpacket/pages/home?activityId=%s&scrmTrackId=%s";

    @AppSwitch(des = "部门信息配置", level = Switch.Level.p4)
    public static Map<Integer, String> DEPARTMENT_INFO_MAP = new HashMap<>();

    static {
        DEPARTMENT_INFO_MAP.put(6, "度假团队（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(7, "度假自营（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(9, "飞猪市场部（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(10, "飞猪酒店套餐企业微信（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(13, "TPA服务商（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(17, "飞猪商家对接_微信（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(19, "飞猪商业化（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(24, "飞猪行程管家（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(26, "用户运营（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(27, "飞猪度假售前（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(30, "测试空间（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(32, "度假直连（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(35, "阿信业务（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(46, "企微宝（飞猪官方福利）");
        DEPARTMENT_INFO_MAP.put(187, "测试-平台运营组(飞猪商家）");
        DEPARTMENT_INFO_MAP.put(246, "平台运营组（飞猪商家）");
    }

    @AppSwitch(des = "客户关系诸葛标签code", level = Switch.Level.p4)
    public static String CUSTOMER_RELATION_CROWD_TAG_CODE = "k944f5c60d";

    @AppSwitch(des = "客户关系诸葛标签消息重试次数", level = Switch.Level.p4)
    public static Integer CUSTOMER_RELATION_CROWD_TAG_SYNC_RETRY_COUNT = 1;

    @AppSwitch(des = "scrm缺省标签id", level = Switch.Level.p4)
    public static Long SCRM_DEFAULT_TAG_ID = 12154L;

    @AppSwitch(des = "个码企微账号客户数最大值", level = Switch.Level.p4)
    public static long WECHAT_CONTACT_ME_USER_MAX_CUSTOMER_COUNT = 19000L;
    @AppSwitch(des = "个码可替换企微账号客户数阈值", level = Switch.Level.p4)
    public static long WECHAT_CONTACT_ME_USER_REPLACE_CUSTOMER_COUNT_THRESHOLD = 15000L;
    @AppSwitch(des = "可用企微账号替换个码最大值", level = Switch.Level.p4)
    public static Integer WECHAT_CONTACT_ME_USER_REPLACE_COUNT_MAX = 10;
    @AppSwitch(des = "有uid标签id", level = Switch.Level.p4)
    public static Long WITH_UID_TAG_ID = 163723L;

    @AppSwitch(des = "无uid标签id", level = Switch.Level.p4)
    public static Long WITHOUT_UID_TAG_ID = 163724L;

    @AppSwitch(des = "触达频次圈人sql", level = Switch.Level.p4)
    public static String TOUCH_FREQUENCY_SQL = "SELECT execute_union_id as union_id, COUNT(*) AS count\n" +
            "FROM alitrip_wireless.scrm_task_execute_user_record\n" +
            "WHERE execute_time BETWEEN '%s' AND '%s'" +
            "AND task_type in ('singleChatGroupMsg','robotChatMessage')\n" +
            "AND ds = MAX_PT('alitrip_wireless.scrm_task_execute_user_record')\n" +
            "GROUP BY\n" +
            "    execute_union_id\n";


    @AppSwitch(des = "圈人sql阈值条件部分(大于、小于、等于)", level = Switch.Level.p4)
    public static String CROWD_SQL_THRESHOLD_CONDITION_SQL = " HAVING (COALESCE(count, 0) %s %s";

    @AppSwitch(des = "圈人sql阈值条件部分(介于)", level = Switch.Level.p4)
    public static String CROWD_SQL_THRESHOLD_CONDITION_SQL_BETWEEN = " HAVING (COALESCE(count, 0) BETWEEN %s AND %s";

    @AppSwitch(des = "点击频次圈人sql", level = Switch.Level.p4)
    public static String CLICK_FREQUENCY_SQL = "SELECT click_union_id as union_id, COUNT(*) AS count\n" +
            "FROM alitrip_wireless.scrm_user_reached_72_click_material\n" +
            "WHERE TO_DATE(click_time, 'yyyy-MM-dd HH:mi:ss.ff3') BETWEEN FROM_UNIXTIME('%s' / 1000) AND FROM_UNIXTIME('%s' / 1000)\n" +
            "AND task_type in ('singleChatGroupMsg','robotChatMessage')\n" +
            "AND ds = MAX_PT('alitrip_wireless.scrm_user_reached_72_click_material')\n" +
            "GROUP BY click_union_id";

    @AppSwitch(des = "添加时间圈人sql", level = Switch.Level.p4)
    public static String ADD_TIME_SQL = "SELECT external_user_id\n" +
            "    FROM alitrip_wireless.scrm_wechat_customer_relation \n" +
            "WHERE ds = MAX_PT('alitrip_wireless.scrm_wechat_customer_relation')\n" +
            "AND TO_DATE(add_time, 'yyyy-MM-dd HH:mi:ss') BETWEEN FROM_UNIXTIME('%s' / 1000) AND FROM_UNIXTIME('%s' / 1000)\n" +
            "AND status = 1\n" +
            "UNION ALL \n" +
            "SELECT external_user_id\n" +
            "    FROM alitrip_wireless.scrm_wechat_customer_relation_hh\n" +
            "WHERE ds = MAX_PT('alitrip_wireless.scrm_wechat_customer_relation_hh')\n" +
            "AND TO_DATE(add_time, 'yyyy-MM-dd HH:mi:ss') BETWEEN FROM_UNIXTIME('%s' / 1000) AND FROM_UNIXTIME('%s' / 1000)\n" +
            "AND status = 1 ";

    @AppSwitch(des = "群码自动建群巡检群人数阈值", level = Switch.Level.p4)
    public static Integer AUTO_CREATE_GROUP_CHECK_MIN_MEMBER_COUNT = 150;

    @AppSwitch(des = "签证订单详情链接-预发", level = Switch.Level.p4)
    public static String VISA_ORDER_LINK_PRE = "https://market.wapa.taobao.com/app/trip/rx-travel-order-detail/pages/detail?orderId=%s";

    @AppSwitch(des = "签证订单详情链接-线上", level = Switch.Level.p4)
    public static String VISA_ORDER_LINK_ONLINE = "https://market.m.taobao.com/app/trip/rx-travel-order-detail/pages/detail?orderId=%s";

    @AppSwitch(des = "任务频控缓存日志开关", level = Switch.Level.p4)
    public static Boolean TASK_FATIGUE_CACHE_LOG_SWITCH = false;

    @AppSwitch(des = "支付宝群组指定corpId", level = Switch.Level.p4)
    public static String ALIPAY_GROUP_CORP_ID = "1970326357976939";

    @AppSwitch(des = "诸葛平台-实时-短信黑名单标签code", level = Switch.Level.p4)
    public static String ZHUGE_REALTIME_SMS_BLACK_TAG_CODE = "o56abb3c8b";

    @AppSwitch(des = "支付宝商家群同步分片最大值", level = Switch.Level.p4)
    public static Integer ALIPAY_GROUP_SYNC_SHARD_MAX = 5;

    @AppSwitch(des = "支付宝消息任务延迟触发时间（分钟）", level = Switch.Level.p4)
    public static Integer ALIPAY_MSG_TASK_DELAY_TIME = 5;

    @AppSwitch(des = "根据部门id查询空间id是否需要校验corpId开关", level = Switch.Level.p4)
    public static Boolean QUERY_SPACE_ID_BY_DEPARTMENT_ID_CHECK_CORP_ID_SWITCH = false;

    @AppSwitch(des = "任务实例校验开关", level = Switch.Level.p4)
    public static Boolean TASK_INSTANCE_CHECK_SWITCH = false;

    @AppSwitch(des = "支付宝小程序mtop白名单", level = Switch.Level.p2)
    public static Set<String> API_AP_WHITE_LIST = Sets.newHashSet();

    @AppSwitch(des = "支付宝ttid", level = Switch.Level.p2)
    public static String ALIPAY_TTID = "12zfb000033030";
    @AppSwitch(des = "支付宝fptid", level = Switch.Level.p2)
    public static String ALIPAY_FPTID = "019414";

    @AppSwitch(des = "tripscrm知识文档fai知识库id", level = Switch.Level.p4)
    public static Long TRIPSCRM_DOCUMENT_LIBRARY_ID = 1337L;

    @AppSwitch(des = "智能回复消息类型白名单", level = Switch.Level.p4)
    public static List<Integer> SMART_RESPONSE_MESSAGE_TYPE_WHITE_LIST = Lists.newArrayList(2001);

    @AppSwitch(des = "券过期提醒-券面额默认文案")
    public static String DEFAULT_EXPIRING_COUPON_AMT = "惊喜旅行红包";

    @AppSwitch(des = "订阅消息任务执行器查询任务实例最大数量")
    public static Integer SUBSCRIBE_MSG_TASK_EXECUTOR_QUERY_TASK_INSTANCE_MAX_COUNT = 3;

    @AppSwitch(des = "任务疲劳度预发测试用户id列表")
    public static List<String> TASK_FATIGUE_PRE_TEST_LIST = Lists.newArrayList();

    @AppSwitch(des = "智能回复文本黑名单", level = Switch.Level.p4)
    public static List<String> SMART_RESPONSE_TEXT_MESSAGE_BLACK_LIST = Lists.newArrayList("我已经添加了你，现在我们可以开始聊天了。");

    @AppSwitch(des = "智能回复拒答策略文本", level = Switch.Level.p4)
    public static Map<Long, String> SMART_RESPONSE_REJECT_STRATEGY_CONTENT = Maps.newHashMap();

    @AppSwitch(des = "图灵申请的标签字段", level = Switch.Level.p2)
    public static String TULING_TAG_FIELD = "rt0b4jh61";

    @AppSwitch(des = "诸葛转图灵分片大小", level = Switch.Level.p2)
    public static int ZHUGE_TO_TULING_SHARD_SIZE = 10000;

    @AppSwitch(des = "诸葛转图灵人群最大值", level = Switch.Level.p2)
    public static int ZHUGE_TO_TULING_CROWD_MAX_SIZE = 500*10000;

    @AppSwitch(des = "诸葛转图灵任务时间最大值", level = Switch.Level.p2)
    public static int ZHUGE_TO_TULING_TASK_TIME_MAX = 5;

    @AppSwitch(des = "诸葛转图灵任务是否查询灵动", level = Switch.Level.p2)
    public static boolean ZHUGE_TO_TULING_TASK_QUERY_LINGDONG = false;
    @AppSwitch(des = "图灵任务重刷开关", level = Switch.Level.p2)
    public static boolean TULING_TASK_RESET_SWITCH = false;

    @AppSwitch(des = "调用proxy接口发送订阅消息开关", level = Switch.Level.p4)
    public static boolean SEND_SUBSCRIBE_MSG_SWITCH = true;
}