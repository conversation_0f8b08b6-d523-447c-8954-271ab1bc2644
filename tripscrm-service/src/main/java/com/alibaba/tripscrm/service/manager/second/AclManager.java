package com.alibaba.tripscrm.service.manager.second;

import com.alibaba.buc.acl.api.input.check.CheckPermissionsParam;
import com.alibaba.buc.acl.api.input.menu.ListMenuTreeParam;
import com.alibaba.buc.acl.api.output.check.CheckPermissionsResult;
import com.alibaba.buc.acl.api.output.menu.TreeMenuResult;
import com.alibaba.buc.acl.api.service.AccessControlService;
import com.alibaba.buc.acl.api.service.MenuService;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 菜单管理
 *
 * <AUTHOR>
 * @date 2023/3/13
 */
@Slf4j
@Service("aclMenuManager")
public class AclManager {

    @Value("${acl.accessKey}")
    private String accessKey;

    @Autowired
    private MenuService menuService;

    @Autowired
    private AccessControlService accessControlService;

    public TreeMenuResult queryMenuTree(Integer userId) {
        ListMenuTreeParam param = new ListMenuTreeParam();
        param.setAccessKey(accessKey);
        param.setUserId(userId);
        return menuService.listAllTreeMenu(param);
    }


    public BaseResult<Boolean> checkPermissions(Integer userId, String permissionName) {
        boolean checkResult = false;
        CheckPermissionsParam checkPermissionsParam = new CheckPermissionsParam();
        checkPermissionsParam.setAccessKey(accessKey);
        checkPermissionsParam.setUserId(userId);
        checkPermissionsParam.setPermissionNames(Collections.singletonList(permissionName));
        CheckPermissionsResult checkPermissions = accessControlService.checkPermissions(checkPermissionsParam);
        if (checkPermissions == null) {
            return BaseResult.fail("鉴权失败,结果为空");
        }

        if (checkPermissions.isSuccess()) {
            List<CheckPermissionsResult.CheckPermissionResultInner> checkPermissionResults = checkPermissions.getCheckPermissionResults();
            for (CheckPermissionsResult.CheckPermissionResultInner checkPermissionResult : checkPermissionResults) {
                if (permissionName.equals(checkPermissionResult.getPermissionName())) {
                    checkResult = checkPermissionResult.isAccessible();
                }
            }
        } else {
            PlatformLogUtil.logFail("鉴权失败",LogListUtil.newArrayList(checkPermissions.getCode(), checkPermissions.getMsg()));
            return BaseResult.fail("鉴权失败:" + checkPermissions.getMsg());
        }
        return BaseResult.success(checkResult);
    }
}
