package com.alibaba.tripscrm.service.manager.second;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.User;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.mmp.client.cuic.domainobject.SubAccountDO;
import com.taobao.mmp.client.cuic.service.client.SubAccountReadClient;
import com.taobao.mmp.client.cuic.transferobject.ResultTO;
import com.taobao.uic.common.domain.BaseUserDO;
import com.taobao.uic.common.domain.ResultDO;
import com.taobao.uic.common.service.userinfo.client.UicReadServiceClient;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2025/3/31 20:54
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MmpManager {
    private final UicReadServiceClient uicReadServiceClient;
    private final SubAccountReadClient subAccountReadClient;

    /**
     * 判断是否主账号
     */
    @AteyeInvoker(description = "判断是否主账号", paraDesc = "userId")
    public TripSCRMResult<Boolean> isMainAccount(Long userId) {
        ResultDO<BaseUserDO> result = uicReadServiceClient.getBaseUserByUserId(userId);
        if (!result.isSuccess() || Objects.isNull(result.getModule())) {
            PlatformLogUtil.logFail("判断是否主账号，查询接口失败", LogListUtil.newArrayList(userId, result));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        PlatformLogUtil.logInfo("判断是否主账号，查询结果", LogListUtil.newArrayList(userId, result));
        return TripSCRMResult.success(result.getModule().getIsAdmin());
    }

    /**
     * 获取主账号Id
     */
    @AteyeInvoker(description = "获取主账号Id", paraDesc = "userId")
    public TripSCRMResult<Long> getMainAccountId(Long userId) {
        ResultTO<Long> result = subAccountReadClient.getMainAccountId(userId);
        if (!result.isSuccess() || Objects.isNull(result.getModule())) {
            PlatformLogUtil.logFail("获取主账号Id，查询接口失败", LogListUtil.newArrayList(userId, result));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        PlatformLogUtil.logInfo("获取主账号Id，查询结果", LogListUtil.newArrayList(userId, result));
        return TripSCRMResult.success(result.getModule());
    }

    /**
     * 获取账号昵称
     */
    @AteyeInvoker(description = "获取账号昵称", paraDesc = "userId")
    public TripSCRMResult<String> getDisplayNick(Long userId) {
        ResultDO<BaseUserDO> result = uicReadServiceClient.getBaseUserByUserId(userId);
        if (!result.isSuccess() || Objects.isNull(result.getModule())) {
            PlatformLogUtil.logFail("获取账号昵称，查询接口失败", LogListUtil.newArrayList(userId, result));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        PlatformLogUtil.logInfo("获取账号昵称，查询结果", LogListUtil.newArrayList(userId, result));
        return TripSCRMResult.success(result.getModule().getDisplayNick());
    }

    @AteyeInvoker(description = "获取账号登录名", paraDesc = "userId")
    public TripSCRMResult<String> getLoginIdByUserId(Long userId) {
        ResultDO<BaseUserDO> result = uicReadServiceClient.getBaseUserByUserId(userId);
        if (!result.isSuccess() || Objects.isNull(result.getModule())) {
            PlatformLogUtil.logFail("获取账号登录名，查询接口失败", LogListUtil.newArrayList(userId, result));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
        PlatformLogUtil.logInfo("获取账号登录名，查询结果", LogListUtil.newArrayList(userId, result));
        return TripSCRMResult.success(result.getModule().getLoginId());
    }

    /**
     * 获取账号userId
     */
    @AteyeInvoker(description = "获取账号userId", paraDesc = "loginId登录名")
    public TripSCRMResult<BaseUserDO> getByLoginId(String loginId) {
        ResultDO<BaseUserDO> result = uicReadServiceClient.getBaseUserByLoginId(loginId);
        if (!result.isSuccess() || Objects.isNull(result.getModule())) {
            PlatformLogUtil.logFail("获取账号userId，查询接口失败", LogListUtil.newArrayList(loginId, result));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        PlatformLogUtil.logInfo("获取账号userId，查询结果", LogListUtil.newArrayList(loginId, result));
        return TripSCRMResult.success(result.getModule());
    }

    /**
     * 获取账号登录Id
     */
    @AteyeInvoker(description = "获取账号登录Id", paraDesc = "userId")
    public TripSCRMResult<String> getLoginId(Long userId) {
        ResultDO<BaseUserDO> result = uicReadServiceClient.getBaseUserByUserId(userId);
        if (!result.isSuccess() || Objects.isNull(result.getModule())) {
            PlatformLogUtil.logFail("获取账号登录Id，查询接口失败", LogListUtil.newArrayList(userId, result));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        PlatformLogUtil.logInfo("获取账号登录Id，查询结果", LogListUtil.newArrayList(userId, result));
        return TripSCRMResult.success(result.getModule().getLoginId());
    }

    /**
     * 根据子账号登录名（例子：商家测试帐号57:将昼）获取子账号Id
     */
    @AteyeInvoker(description = "根据子账号登录名获取子账号Id", paraDesc = "loginId")
    public TripSCRMResult<Long> getSubAccountId(String loginId) {
        ResultTO<SubAccountDO> result = subAccountReadClient.getSubAccountByNick(loginId);
        if (!result.isSuccess() || Objects.isNull(result.getModule())) {
            PlatformLogUtil.logFail("根据子账号登录名获取子账号Id，查询接口失败", LogListUtil.newArrayList(loginId, result));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
        PlatformLogUtil.logInfo("根据子账号登录名获取子账号Id，查询结果", LogListUtil.newArrayList(loginId, result));
        return TripSCRMResult.success(result.getModule().getUserId());
    }

    public Boolean isMainAccount(User user) {
        //  登录名获取账号信息, 默认是主账号
        TripSCRMResult<BaseUserDO> loginId = getByLoginId(user.getUserId());
        if (loginId.isSuccess() && loginId.getData() != null) {
            //  有数据，是主账号
            return ignoreCaseMatch(loginId.getData().getLoginId(), user.getUserId());
        } else {
            //  子账号登录
            ResultTO<SubAccountDO> subAccountByNick = subAccountReadClient.getSubAccountByNick(user.getUserId());
            //  查询成功
            if (subAccountByNick.isSuccess() && subAccountByNick.getModule() != null) {
                return false;
            } else {
                //  主账号，子账号都查询失败
                PlatformLogUtil.logFail("是否主子账号查询失败", LogListUtil.newArrayList(user, loginId, subAccountByNick));
                return null;
            }
        }


    }

    /**
     * 忽略大小写的字符串匹配
     */
    private static boolean ignoreCaseMatch(String str1, String str2) {
        //  字符串都是空
        if (StringUtils.isBlank(str1) && StringUtils.isBlank(str2)) {
            return false;
        }
        if (StringUtils.isBlank(str1) || StringUtils.isBlank(str2)) {
            return false;
        }
        if (str1.equals(str2)) {
            return true;
        }
        //  字符串忽略字母大小写,空格和tab符号进行两个字符串的匹配
        //  字符串忽略字母大小写,空格和tab符号进行两个字符串的匹配
        String cleanStr1 = str1.replaceAll("[ \t]", "");
        String cleanStr2 = str2.replaceAll("[ \t]", "");
        return cleanStr1.equalsIgnoreCase(cleanStr2);
    }


    /**
     * 获取主账号Id
     *
     * @param user 字符串的登录ID(例如：商家测试账号46)
     * @return
     */
    public Long getMainAccountId(User user) {

        //  登录名获取账号信息, 默认是主账号
        TripSCRMResult<BaseUserDO> loginId = getByLoginId(user.getUserId());
        Long mainUserId;
        if (loginId.isSuccess() && loginId.getData() != null) {
            //  主账号登录
            mainUserId = loginId.getData().getUserId();
            //  二次查询确认
            TripSCRMResult<Long> mainAccount = getMainAccountId(mainUserId);
            if (mainAccount.isSuccess() && mainAccount.getData() != null) {
                return mainAccount.getData();
            } else {
                PlatformLogUtil.logFail("获取主账号Id查询接口失败", LogListUtil.newArrayList(user, mainAccount, loginId));
                return null;
            }
        } else {
            //  子账号登录
            ResultTO<SubAccountDO> subAccountByNick = subAccountReadClient.getSubAccountByNick(user.getUserId());
            //  查询成功
            if (subAccountByNick.isSuccess() && subAccountByNick.getModule() != null) {
                Long userId = subAccountByNick.getModule().getUserId();
                TripSCRMResult<Long> mainAccountId = getMainAccountId(userId);
                if (mainAccountId.isSuccess() && mainAccountId.getData() != null) {
                    return mainAccountId.getData();
                } else {
                    PlatformLogUtil.logFail("子账号获取主账号Id查询接口失败", LogListUtil.newArrayList(user, mainAccountId, subAccountByNick));
                    return null;
                }
            } else {
                //  查询失败
                PlatformLogUtil.logFail("子账号获取子账号Id查询接口失败", LogListUtil.newArrayList(user, subAccountByNick));
                return null;
            }
        }
    }
}
