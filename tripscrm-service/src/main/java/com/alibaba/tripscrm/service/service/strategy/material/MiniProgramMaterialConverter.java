package com.alibaba.tripscrm.service.service.strategy.material;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.material.MiniProgramMaterialDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripscrm.service.util.wx.WxMediaUtils;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.message.TinyAppMsgDTO;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@AllArgsConstructor
public class MiniProgramMaterialConverter extends AbstractMaterialConverter<MiniProgramMaterialDTO> {

    private final WxMediaUtils wxMediaUtils;
    private final MaterialLinkService materialLinkService;

    @Override
    public MiniProgramMaterialDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        MiniProgramMaterialDTO miniProgramMaterialDTO = JSONObject.parseObject(content, MiniProgramMaterialDTO.class);
        materialTrackRelationDTO.setMsgParagraphId(-1);
        String scrmTrackId = materialLinkService.getBuriedPointId(materialTrackRelationDTO);
        // 链接生成
        String pathUrl = "";
        if (StringUtils.isNotBlank(miniProgramMaterialDTO.getPagePath())) {
            pathUrl = miniProgramMaterialDTO.getPagePath();
        } else {
            pathUrl = buildPathUrl(new MaterialSupplyDTO(miniProgramMaterialDTO.getSupplyType(), miniProgramMaterialDTO.getSupplyId()), null, miniProgramMaterialDTO.getPagePath(), scrmTrackId, materialTrackRelationDTO.getSceneType());
        }
        if (StringUtils.isBlank(pathUrl)) {
            return miniProgramMaterialDTO;
        }
        // 链接处理
        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(pathUrl);
        request.setTargetLinkType(LinkTypeEnum.MINI_PROGRAM_LIKE);
        request.setConvertShortLink(false);
        request.setTitle("");
        request.setScrmTrackId(scrmTrackId);
        request.setMaterialId(materialTrackRelationDTO.getMaterialId());
        // 红包需要设置默认参数
        if (hasRedPacketSupply(content)) {
            request.getCustomizeParamMap().put("navBg", "%23FFE226");
            request.getCustomizeParamMap().put("navColor", "%23000000");
        }
        request.setSceneType(materialTrackRelationDTO.getSceneType());
        // 支付宝群发
        if (Objects.equals(MaterialSendSceneTypeConstant.ALIPAY_GROUP_MSG, request.getSceneType())) {
            request.setTargetLinkType(LinkTypeEnum.ALIPAY_LINK);
        }
        String tempUrl = materialLinkService.convertLink(request);
        miniProgramMaterialDTO.setPagePath(MaterialUtils.replaceMiniProgramPathWithDotHtml(tempUrl));
        return miniProgramMaterialDTO;
    }

    private Boolean hasRedPacketSupply(String content) {
        List<MaterialSupplyDTO> supplyList = getMaterialSupplyList(content);
        if (CollectionUtils.isEmpty(supplyList)) {
            return false;
        }
        return supplyList.stream().anyMatch(supply -> supply.getSupplyType().equals(MaterialSupplyTypeEnum.WECHAT_CASH_RED_PACKET_ACTIVITY.getType()));
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        MiniProgramMaterialDTO miniProgramMaterialDTO = JSONObject.parseObject(content, MiniProgramMaterialDTO.class);
        if (Objects.isNull(miniProgramMaterialDTO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_MATERIAL_CONTENT);
        }

        List<MaterialSupplyDTO> materialSupplyList = Lists.newArrayList();
        if (StringUtils.isNotBlank(miniProgramMaterialDTO.getSupplyType()) && StringUtils.isNotBlank(miniProgramMaterialDTO.getSupplyId())) {
            materialSupplyList.add(new MaterialSupplyDTO(miniProgramMaterialDTO.getSupplyType(), miniProgramMaterialDTO.getSupplyId()));
        }
        return materialSupplyList;
    }

    @Override
    public List<MessageBO> buildMessageBO(MiniProgramMaterialDTO materialDTO, MaterialContentConvertContext context, String sceneType) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgNum(materialDTO.getIndex());
        messageBO.setMsgType(MessageTypeEnum.MINI_PROGRAM);
        // 支付宝消息
        if (Objects.equals(MaterialSendSceneTypeConstant.ALIPAY_GROUP_MSG, sceneType)) {
            return buildAlipayMessageBO(materialDTO, messageBO);
        }

        JSONObject jsonObject = new JSONObject();
        JSONObject webMsgJsonObject = JSONObject.parseObject(SwitchConfig.MINI_PROGRAM_DEFAULT_PROP);
        jsonObject.put("we_app_msg", webMsgJsonObject);
        webMsgJsonObject.put("name", MaterialConstant.DEFAULT_TITLE);
        webMsgJsonObject.put("description", materialDTO.getTitle());
        webMsgJsonObject.put("app_id", MaterialConstant.SINGLE_CHAT_APP_ID);
        webMsgJsonObject.put("uri", materialDTO.getPagePath());
        String messageContent = Base64.getEncoder().encodeToString(jsonObject.toJSONString().getBytes());
        messageBO.setTitle(materialDTO.getTitle());
        messageBO.setMsgContent(messageContent);
        messageBO.setHref(materialDTO.getCoverPicture().get(0));
        return Lists.newArrayList(messageBO);
    }

    /**
     * 构建小程序消息
     *
     * @param materialDTO
     * @param messageBO
     * @return
     */
    public List<MessageBO> buildAlipayMessageBO(MiniProgramMaterialDTO materialDTO, MessageBO messageBO) {
        TinyAppMsgDTO tinyAppMsgDTO = new TinyAppMsgDTO();
        tinyAppMsgDTO.setImageId(materialDTO.getImageId());
        tinyAppMsgDTO.setTinyAppId(MaterialConstant.FLY_ALIPAY_APP_ID);
        tinyAppMsgDTO.setTinyAppName(MaterialConstant.FLY_ALIPAY_APP_NAME);
        tinyAppMsgDTO.setUrl(materialDTO.getPagePath());
        tinyAppMsgDTO.setTinyAppLogo(MaterialConstant.FLY_ALIPAY_APP_LOGO);
        messageBO.setExtFields(JSONObject.toJSONString(tinyAppMsgDTO));
        messageBO.setDesc(materialDTO.getDesc());
        messageBO.setTitle(materialDTO.getTitle());
        return Lists.newArrayList(messageBO);
    }

    @Override
    public List<WxMessageBO> buildWxMessageBO(MiniProgramMaterialDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        WxMessageBO wxMessageBO = new WxMessageBO();
        wxMessageBO.setMsgType(WxAttachmentTypeEnum.MINI_PROGRAM);
        wxMessageBO.setPicUrl(materialDTO.getCoverPicture().get(0));
        wxMessageBO.setMediaId(wxMediaUtils.getMediaId(WxAttachmentTypeEnum.IMAGE, materialDTO.getCoverPicture().get(0), SpaceInfoThreadLocalUtils.getCorpId(), false));
        wxMessageBO.setTitle(materialDTO.getTitle());
        wxMessageBO.setPathUrl(materialDTO.getPagePath());
        String appId = Optional.ofNullable(context.getExtraInfo())
                .map(extra -> MapUtils.getString(extra, "appId", MaterialConstant.OTHER_APP_ID))
                .orElse(MaterialConstant.OTHER_APP_ID);
        wxMessageBO.setAppId(appId);
        return Lists.newArrayList(wxMessageBO);
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.MINI_PROGRAM;
    }

    @Override
    public String buildSubscribeMsgTemplateContent(String content, String paramType) {
        return "";
    }
}
