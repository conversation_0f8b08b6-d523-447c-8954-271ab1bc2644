package com.alibaba.tripscrm.service.service.common;

import com.alibaba.tripscrm.service.enums.system.ResourceRelationWayEnum;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.domain.request.JoinGroupRelationRequest;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupRelationVO;

/**
 * <AUTHOR>
 */
public interface JoinGroupRelationService {

    /**
     * 查询群码绑定群列表
     * @param query 查询条件
     * @return 查询结果
     */
    PageResultVO<WechatGroupRelationVO> list(ResourceRelationQuery query);

    /**
     * 新增资源关系
     * @param way 关联渠道
     * @param resourceId 资源Id
     * @param targetId 目标Id
     * @return 新增结果
     */
    Boolean add(ResourceRelationWayEnum way, String resourceId, String targetId);

    /**
     * 删除资源关系
     * @param id 关系Id
     * @return 删除结果
     */
    Boolean delete(Long id);

    /**
     * 更新群码资源
     * @param request 请求体
     * @return 更新结果
     */
    Boolean update(JoinGroupRelationRequest request);

}
