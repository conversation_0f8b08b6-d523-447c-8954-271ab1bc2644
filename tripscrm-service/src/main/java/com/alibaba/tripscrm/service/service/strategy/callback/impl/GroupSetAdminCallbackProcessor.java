package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.log.BatchOperateLogInfoService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/6/6 14:20
 **/
@Component
@AllArgsConstructor
public class GroupSetAdminCallbackProcessor implements ProxyCallbackProcessor {

    private final LdbTairManager ldbTairManager;
    private final BatchOperateLogInfoService batchOperateLogInfoService;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.GROUP_SET_ADMIN_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (scrmCallbackMsg == null || StringUtils.isBlank(scrmCallbackMsg.getRequestId())) {
            PlatformLogUtil.logFail("设置管理员回调内容为空或者请求id为空");
            return false;
        }
        boolean dealResult = true;
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("设置管理员回调显示失败", LogListUtil.newArrayList(scrmCallbackMsg));
            dealResult = false;
        }
        // 批量设置管理员操作结果处理
        dealBatchOperate(scrmCallbackMsg.getRequestId(), dealResult);
        return true;
    }

    /**
     * 批量设置管理员操作结果处理
     * @param requestId 请求id
     * @param dealResult 处理结果
     */
    private void dealBatchOperate(String requestId, boolean dealResult) {
        Object o = ldbTairManager.get(TairConstant.BATCH_GROUP_OPERATE_MAPPING + requestId);
        if (Objects.isNull(o)) {
            return;
        }
        batchOperateLogInfoService.updateLogInfoByRequestId((Long) o, requestId, dealResult);
    }
}
