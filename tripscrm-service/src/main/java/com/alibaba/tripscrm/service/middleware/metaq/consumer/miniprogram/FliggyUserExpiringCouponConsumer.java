package com.alibaba.tripscrm.service.middleware.metaq.consumer.miniprogram;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.enums.subscribemsg.ExpiringCouponParamKeyEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 飞猪客户券过期消息监听
 * topic: fliggy_customer_expiring_coupon
 * consumerId: CID_fliggy_customer_expiring_coupon_scrm
 * <p>
 * 消息格式为：${user_id}\t${template_code}\t${template_name}\t${end_time}\t${coupon_amt}\t${biz_type}
 *
 * <AUTHOR>
 * @since 2025/10/20
 */
@Slf4j
@Service
public class FliggyUserExpiringCouponConsumer implements MessageListenerConcurrently {

    @Resource
    private MetaqProducer metaqProducer;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgList) {
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("处理用户券过期消息失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private Boolean dealWithMessage(String message) {
        try {
            // 解析数据
            if (!StringUtils.hasLength(message)) {
                PlatformLogUtil.logFail("用户券过期消息解析为空", LogListUtil.newArrayList(message));
                return false;
            }
            List<String> contentList = Arrays.stream(message.split("\t")).filter(StringUtils::hasLength).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(contentList) || contentList.size() != 6 || !contentList.stream().allMatch(StringUtils::hasLength)) {
                PlatformLogUtil.logFail("用户券过期消息解析失败", LogListUtil.newArrayList(message));
                return false;
            }
            String userId = contentList.get(0);
            String templateCode = contentList.get(1);
            String templateName = contentList.get(2);
            String endTime = contentList.get(3);
            String couponAmt = contentList.get(4);
            if (!canConvertToNumber(couponAmt)) {
                PlatformLogUtil.logInfo("面额为空，采用默认值", message, couponAmt);
                couponAmt = SwitchConfig.DEFAULT_EXPIRING_COUPON_AMT;
            }
            String bizType = contentList.get(5);
            PlatformLogUtil.logInfo("用户券过期消息处理成功", userId, templateCode, templateName);
            JSONObject msg = new JSONObject();
            msg.put(ExpiringCouponParamKeyEnum.USER_ID.getKey(), userId);
            msg.put(ExpiringCouponParamKeyEnum.TEMPLATE_CODE.getKey(), templateCode);
            msg.put(ExpiringCouponParamKeyEnum.TEMPLATE_NAME.getKey(), templateName);
            msg.put(ExpiringCouponParamKeyEnum.COUPON_AMT.getKey(), couponAmt);
            msg.put(ExpiringCouponParamKeyEnum.BIZ_TYPE.getKey(), bizType);
            msg.put(ExpiringCouponParamKeyEnum.END_TIME.getKey(), endTime);
            return metaqProducer.send(MQEnum.SUBSCRIBE_MSG_EXPIRING_COUPON, userId, "", msg.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("用户券过期消息处理失败", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
    }

    private Boolean canConvertToNumber(String couponAmt) {
        try {
            Double.parseDouble(couponAmt);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    @AteyeInvoker(description = "手动触发用户券过期消息", paraDesc = "message")
    public boolean processExpiringCouponMsg(String message) {
        return dealWithMessage(message);
    }
}
