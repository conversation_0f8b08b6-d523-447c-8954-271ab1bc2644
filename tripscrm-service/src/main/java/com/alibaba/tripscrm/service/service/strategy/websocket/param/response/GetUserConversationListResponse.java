package com.alibaba.tripscrm.service.service.strategy.websocket.param.response;

import lombok.Data;

import java.util.List;

/**
 * 获取当前企微号的会话列表 推送体
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class GetUserConversationListResponse {

    /**
     * 会话列表
     */
    private List<GetUserConversationResponseBody> userConversations;

    /**
     * 总数量
     */
    private Long count;

    /**
     * 非全量
     */
    private Boolean append;
}
