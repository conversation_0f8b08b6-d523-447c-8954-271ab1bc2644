package com.alibaba.tripscrm.service.config;

import com.alibaba.alipmc.api.ProcessInstanceService;
import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.buc.acl.api.service.AccessControlService;
import com.alibaba.buc.acl.api.service.MenuService;
import com.alibaba.dataworks.dataservice.service.HsfDataApiService;
import com.alibaba.fliggy.furl.service.FurlService;
import com.alibaba.fliggypoi.client.service.TripPoiReadService;
import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import com.alibaba.trip.channel.ai.client.service.task.AiTaskService;
import com.alibaba.trip.tripdivision.client.TrdiDivisionMappingService;
import com.alibaba.trip.tripdivision.client.TrdiDivisionQueryService;
import com.alibaba.trip.tripdivision.client.TrdiDivisionReadService;
import com.alibaba.tripbaymaxbusi.client.service.PromoteSupplyService;
import com.alibaba.tripcdest.facade.service.FliggyDestInfoService;
import com.alibaba.tripscrm.service.constant.AppConstant;
import com.alibaba.tripzoo.admin.client.service.ActivityService;
import com.alibaba.tripzoo.proxy.api.service.*;
import com.alibaba.tripzoo.proxy.api.service.alipay.group.AlipayGroupService;
import com.alitrip.travel.travelitems.service.TravelItemSearchService;
import com.aliyun.quicka.tianquan.client.service.FliggyAudiencePocDataService;
import com.fliggy.crowd.service.TripCrowdCommonService;
import com.fliggy.crowd.service.open.api.PicassoCommonService;
import com.fliggy.ffa.touch.prod.customize.client.common.MemberInfoService;
import com.fliggy.fic.service.FItemMultiAppService;
import com.fliggy.fliggyplaycore.client.biz.wechat.WechatShareService;
import com.fliggy.fliggyplaycore.client.share.ShareActOperationService;
import com.fliggy.fliggyplaycore.client.share.ShareActQueryService;
import com.fliggy.fliggyplaycore.client.task.TaskOperationService;
import com.fliggy.fmp.mms.client.admin.contract.api.ContractDraftClientService;
import com.fliggy.hotel.seller.base.client.service.account.HotelAccountQueryService;
import com.fliggy.tccp.client.api.query.TccpQueryService;
import com.fliggy.travelsummary.service.SummarySearchService;
import com.taobao.contentplatform.article.base.read.ArticleReadService;
import com.taobao.contentplatform.article.base.write.ArticleWriteService;
import com.taobao.mtop.api.agent.MtopAgent;
import com.taobao.payment.member.client.facade.MerchantQueryFacade;
import com.taobao.pushcenter.client.UnifiedPushTaskService;
import com.taobao.tccpos.query.biz.TccpBizSearchService;
import com.taobao.trip.hcp.service.PoolItemService;
import com.taobao.trip.hpc.client.service.SHotelService;
import com.taobao.trip.tripgalaxy.service.ItemQueryService;
import com.taobao.trip.tripjourneyop.api.JourneyOpQueryFacade;
import com.taobao.trip.wireless.mc.client.hsf.EnterpriseWeChatService;
import com.taobao.triptower.service.scrm.ScrmCheckService;
import com.taobao.uic.common.service.userinfo.client.UicPaymentAccountReadServiceClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * hsf服务的统一个Config类，在其它需要使用的地方，直接@Autowired注入即可。详情见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
 *
 * <AUTHOR>
 */
@Configuration
public class HsfConfig {

    @HSFConsumer(clientTimeout = 2000)
    private MemberInfoService memberInfoService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.buc}")
    private MenuService menuService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.buc}")
    private AccessControlService accessControlService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.amdp}", clientTimeout = 5000)
    private AmdpDataQueryService amdpDataQueryService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripwmc}")
    private EnterpriseWeChatService enterpriseWeChatService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 10000)
    private RobotService robotService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 10000)
    private CustomerService customerService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 5000)
    private UserService userService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 5000)
    private MediaService mediaService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 5000)
    private MomentService momentService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 5000)
    private TagService wxTagService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 5000)
    private WxMiniProgramService wxMiniProgramService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private ContactMeService contactMeService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private JoinGroupService joinGroupService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 5000, methodSpecials = @HSFConsumer.ConsumerMethodSpecial(methodName = "asyncCreate", clientTimeout = "10000"))
    private GroupService groupService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private DepartmentService departmentService;

    @HSFConsumer(clientTimeout = 5000)
    private EnterpriseWechatMsgService enterpriseWechatMsgService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.trippc}")
    private UnifiedPushTaskService unifiedPushTaskService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.fliggyplaycore.task}")
    private TaskOperationService taskOperationService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.fliggyplaycore.task}")
    private ShareActOperationService shareActOperationService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripdivision.client}")
    private TrdiDivisionReadService trdiDivisionReadService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripdivision.client}")
    private TrdiDivisionQueryService trdiDivisionQueryService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripgalaxy}")
    private ItemQueryService galaxyItemQueryService;

    @HSFConsumer(serviceVersion = "${srping.hsf.version.fic}")
    private FItemMultiAppService fItemMultiAppService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.fliggyplaycore.task}")
    private ShareActQueryService shareActQueryService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.fliggyplaycore.task}")
    private WechatShareService wechatShareService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.finace.member.client}")
    private MerchantQueryFacade merchantQueryFacade;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.hcp.client}")
    private PoolItemService poolItemService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.summary.client}")
    private SummarySearchService summarySearchService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.alipmc.api}")
    private ProcessInstanceService processInstanceService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private CustomerAcquisitionService customerAcquisitionService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private WxOfficialAccountService wxOfficialAccountService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.data.api}", serviceGroup = "DataService", clientTimeout = 5000)
    private HsfDataApiService hsfDataApiService;

//    @HSFConsumer(serviceVersion = "${spring.hsf.version.uic}")
//    private UccServiceClient uccServiceClient;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private TripCrowdCommonService tripCrowdCommonService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.triptower}")
    private ScrmCheckService scrmCheckService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.hotel}")
    private SHotelService sHotelService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.picasso}")
    private PicassoCommonService picassoCommonService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.fc}", clientTimeout = 5000)
    private com.aliyun.fc.open.api.hsf.HsfGatewayService gatewayService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.furl}")
    private FurlService furlService;

    @HSFConsumer(clientTimeout = 5000)
    private ArticleWriteService articleWriteService;

    @HSFConsumer(clientTimeout = 5000)
    private ArticleReadService articleReadService;

    @HSFConsumer(clientTimeout = 5000)
    private TripPoiReadService tripPoiReadService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private BaiYingService baiYingService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private FliggyAudiencePocDataService pocDataService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.travelitems}")
    private TravelItemSearchService travelItemSearchService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}")
    private FliggyDestInfoService fliggyDestInfoService;

    @HSFConsumer(clientTimeout = 5000)
    private PromoteSupplyService promoteSupplyService;

    @HSFConsumer(clientTimeout = 5000)
    private TrdiDivisionMappingService trdiDivisionMappingService;

    @HSFConsumer(clientTimeout = 5000)
    private JourneyOpQueryFacade journeyOpQueryFacade;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tripzoo}", clientTimeout = 5000)
    private ForwardMessageService forwardMessageService;

    @HSFConsumer(clientTimeout = 3000)
    private HotelAccountQueryService hotelAccountQueryService;

    @HSFConsumer(clientTimeout = 3000)
    private TccpQueryService tccpQueryService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.tccpos}", clientTimeout = 3000)
    private TccpBizSearchService tccpBizSearchService;

    @HSFConsumer(clientTimeout = 5000)
    private ActivityService activityService;

    @HSFConsumer(clientTimeout = 5000)
    private AlipayGroupService alipayGroupService;

//    @HSFConsumer(clientTimeout = 5000 )
//    private UicPaymentAccountReadServiceClient uicPaymentAccountReadServiceClient;
    @Bean(initMethod = "init", name = "mtopHsfAgent")
    public MtopAgent getMtopAgent() {
        MtopAgent mtopAgent = new MtopAgent();
        mtopAgent.setAppName(AppConstant.APP_NAME);
        return mtopAgent;
    }

    @HSFConsumer(serviceVersion = "1.0.0.core", clientTimeout = 5000)
    private ContractDraftClientService contractDraftClientService;

    @HSFConsumer(serviceVersion = "1.0.0", clientTimeout = 2000)
    private AiTaskService aiTaskService;

}
