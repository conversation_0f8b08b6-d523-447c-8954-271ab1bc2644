package com.alibaba.tripscrm.service.service.task.ability.sub.post;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceExecuteStatusEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;


/**
 * 任务执行_子任务_执行完成后更新子任务实例状态
 *
 * <AUTHOR>
 * @since 2024/4/22 16:51
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SubTaskStatusUpdateProcessor implements ExecuteProcessor {
    private final SubTaskInstanceService subTaskInstanceService;

    @Override
    @TaskExecuteLog("任务执行_子任务_执行完成后更新子任务实例状态")
    public TripSCRMResult<Void> process(TaskExecuteContext context) {
        // 流
        if (!NumberUtils.validLong(context.getInstanceId())) {
            return TripSCRMResult.success(null);
        }

        // 更新状态
        TaskInstanceExecuteStatusEnum taskInstanceExecuteStatusEnum = TaskInstanceExecuteStatusEnum.RUN_FAIL;
        if (context.getRetry() || context.getRunTaskResult()) {
            taskInstanceExecuteStatusEnum = TaskInstanceExecuteStatusEnum.RUN_SUCCESS;
        }
        PlatformLogUtil.logInfo("任务执行_子任务_执行完成后更新子任务实例状态，尝试更新子任务实例状态", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context.getRetry(), taskInstanceExecuteStatusEnum));
        if (!subTaskInstanceService.updateSubTaskStatus(context.getInstanceId(), context.getRetry(), taskInstanceExecuteStatusEnum)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.UPDATE_SUB_TASK_INSTANCE_STATUS_ERROR);
        }

        PlatformLogUtil.logInfo("任务执行_子任务_执行完成后更新子任务实例状态，结束", LogListUtil.newArrayList((System.currentTimeMillis() - context.getStartTime()), context.getTaskId(), context.getInstanceId(), context.getRetry()));
        return TripSCRMResult.success(null);
    }
}
