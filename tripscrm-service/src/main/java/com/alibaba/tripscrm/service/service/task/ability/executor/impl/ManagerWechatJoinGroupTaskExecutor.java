package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatJoinGroupDO;
import com.alibaba.tripscrm.dal.model.domain.result.GroupMemberCountResult;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskAttributionConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.convert.ResourceRelationConverter;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationWayEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.MemberTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.domain.request.CreateGroupRequest;
import com.alibaba.tripscrm.service.model.dto.isv.GroupMemberAllocationDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.vo.manage.ManagementGroupVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupRelationVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.common.JoinGroupRelationService;
import com.alibaba.tripscrm.service.service.common.ManagementGroupRelationService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatJoinGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.task.TaskDataUtils;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.fliggy.pokemon.lock.client.api.TairLockManager;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-01-05 14:41:27
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ManagerWechatJoinGroupTaskExecutor extends AbstractTaskExecutor {
    private final ActivityContextService activityContextService;
    private final WechatUserService wechatUserService;
    private final EnterpriseWechatManager enterpriseWechatManager;
    private final WechatJoinGroupService wechatJoinGroupService;
    private final GroupRelationRepository groupRelationRepository;
    private final TairLockManager taskLockManager;
    private final LdbTairManager ldbTairManager;
    private final ResourceRelationService resourceRelationService;
    private final ManagementGroupRelationService managementGroupRelationService;
    private final JoinGroupRelationService groupCodeRelationService;
    private final ResourceRelationConverter resourceRelationConverter;
    private final TagRelationService tagRelationService;
    private final IsvRouteService isvRouteService;

    private final static Map<String, BiConsumer<TaskExecuteContext, TodoTaskVO>> FUNCTION_MAP = new HashMap<>();

    private final static String GROUP_OWNER_ID = "groupOwnerId";
    private final static String GROUP_ADMIN_ID = "groupAdminId";

    @PostConstruct
    public void init1() {
        FUNCTION_MAP.put("add_member", this::processAddMember);
        FUNCTION_MAP.put("dismiss", this::processDismiss);
        FUNCTION_MAP.put("isv_create", this::processIsvCreate);
    }

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        Map<String, Object> extInfo = context.getExtInfo();
        String updateDetail = extInfo.containsKey("updateDetail") ? (String) extInfo.get("updateDetail") : null;
        if (!FUNCTION_MAP.containsKey(updateDetail)) {
            PlatformLogUtil.logFail("企微入群任务调度，任务类型不支持", LogListUtil.newArrayList(updateDetail));
            todoTaskVO.setSuccess(true);
            return;
        }

        FUNCTION_MAP.get(updateDetail).accept(context, todoTaskVO);
        String chatId = getFinalTargetId(context, taskDataBody);
        JSONObject data = new JSONObject();
        data.put("chatId", chatId);
        data.put("extraInfo", context.getExtInfo());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
    }

    private void processIsvCreate(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        JSONObject taskExtInfo = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        boolean autoCreateGroup = taskExtInfo.containsKey("autoCreateGroup") && taskExtInfo.getBoolean("autoCreateGroup");
        if (!autoCreateGroup) {
            PlatformLogUtil.logFail("企微入群任务调度，非自动建群不执行", LogListUtil.newArrayList(todoTaskVO.getTaskId()));
            todoTaskVO.setSuccess(true);
            return;
        }
        // 群聊Id
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        String chatId = getFinalTargetId(context, taskDataBody);
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        if (Objects.isNull(activityContext)) {
            PlatformLogUtil.logFail("企微入群任务调度，任务关联活动缺少上下文", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            todoTaskVO.setSuccess(true);
            todoTaskVO.setAttribution(TaskAttributionConstant.NOT_FOUND_ACTIVITY_CONTEXT);
            return;
        }
        // 上下文Id
        Long contextId = activityContext.getContextId();
        JSONObject extraJson = Optional.ofNullable(activityContext.getExtraJson()).orElse(new JSONObject());
        if (!extraJson.containsKey("wechatJoinGroupId")) {
            PlatformLogUtil.logFail("【任务执行异常】群活码管理任务，群活码上下文数据出错", LogListUtil.newArrayList(contextId));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】群活码管理任务，群活码上下文数据出错，任务ID:%s，上下文ID:%s", todoTaskVO.getTaskId(), contextId));
            return;
        }
        // 群码Id
        Long wechatJoinGroupId = extraJson.getLong("wechatJoinGroupId");
        // 群打标
        addTag(context, todoTaskVO, chatId);
        taskLockManager.tryRunWithLock(TairConstant.UPDATE_JOIN_GROUP_CONFIG_LOCK + wechatJoinGroupId,
                5000L,
                5,
                () -> {
                    WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getById(wechatJoinGroupId);
                    if (Objects.isNull(wechatJoinGroupDO)) {
                        PlatformLogUtil.logFail("【任务执行异常】群活码管理任务，群活码数据不存在", LogListUtil.newArrayList(wechatJoinGroupId));
                        DingTalkApi.sendTaskMessage(String.format("【任务执行异常】群活码管理任务，群活码数据不存在，任务ID:%s，群活码Id:%s", todoTaskVO.getTaskId(), wechatJoinGroupId));
                        return;
                    }

                    // 群人员上限
                    JSONObject taskExtraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
                    Integer maxGroupMemberCount = taskExtraInfoJson.getInteger("maxGroupMemberCount");
                    // 改动的群聊列表
                    List<String> addChatIdList = Lists.newArrayList(chatId);
                    List<String> removeChatIdList = getRemoveChatIdList(wechatJoinGroupDO, maxGroupMemberCount);

                    PlatformLogUtil.logFail("群活码管理任务，更新群活码", LogListUtil.newArrayList(wechatJoinGroupId, removeChatIdList, addChatIdList));
                    wechatJoinGroupService.modifyChatIdList(wechatJoinGroupDO.getId(), addChatIdList, removeChatIdList);
                    wechatJoinGroupDO = wechatJoinGroupService.getById(wechatJoinGroupId);
                    ActivityTaskInfoBO activityContext1 = getActivityContext(context, taskDataBody);
                    // 更新当前活码活动上下文信息
                    updateGroupMaActivityContext(activityContext1, chatId, Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList()));

                    // 删除已移除群聊上下文信息（targetType = 客户群类型）
                    removeActivityContext(removeChatIdList, activityContext1.getActivityId(), ActivityTargetTypeEnum.WX_CHAT_ID);

                    // 插入/更新活动上下文信息（targetType = 客户群类型）
                    upsertActivityContext(addChatIdList, activityContext1.getActivityId(), ActivityTargetTypeEnum.WX_CHAT_ID, contextId);

                    // 更新资源
                    addResource(ResourceRelationWayEnum.AUTO_CREATED, todoTaskVO.getTaskId(), addChatIdList);
                },
                () -> {
                    PlatformLogUtil.logFail("【任务执行异常】群活码管理任务，群活码配置更新失败", LogListUtil.newArrayList(todoTaskVO.getTaskId(), contextId, wechatJoinGroupId));
                    DingTalkApi.sendTaskMessage(String.format("【任务执行异常】群活码配置更新失败，任务ID:%s，上下文ID:%s，群活码ID:%s", todoTaskVO.getTaskId(), contextId, wechatJoinGroupId));
                }
        );
    }

    /**
     * 给群聊打标
     *
     * @param todoTaskVO 任务
     * @param chatId     群聊
     */
    private void addTag(TaskExecuteContext context, TodoTaskVO todoTaskVO, String chatId) {
        if (todoTaskVO == null || context == null || StringUtils.isBlank(chatId)) {
            return;
        }
        List<String> groupAutoAddTagList = Optional.ofNullable(TaskDataUtils.getListFromExtInfo(context.getTaskInfoDOSnapshot(), "groupAutoAddTagList")).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(groupAutoAddTagList)) {
            return;
        }

        List<ItemTagRelationDTO> addList =
                groupAutoAddTagList.stream()
                        .map(tagId -> {
                            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                            itemTagRelationDTO.setItemId(chatId + "_" + SpaceInfoThreadLocalUtils.getCorpId());
                            itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_GROUP.getCode());
                            itemTagRelationDTO.setTag(tagId);
                            itemTagRelationDTO.setDeleted((byte) 0);
                            return itemTagRelationDTO;
                        })
                        .collect(Collectors.toList());
        tagRelationService.batchUpsertSelective(addList);
    }

    /**
     * 获取活码需要移除的群聊列表
     *
     * @param wechatJoinGroupDO   活码信息
     * @param maxGroupMemberCount 群最大成员数量
     * @return 需要移除的群聊列表
     */
    private List<String> getRemoveChatIdList(WechatJoinGroupDO wechatJoinGroupDO, Integer maxGroupMemberCount) {
        ArrayList<String> removeChatIdList = new ArrayList<>();
        String chatIdListStr = Optional.ofNullable(wechatJoinGroupDO.getChatIdList()).orElse("");
        List<String> chatIdList = Arrays.stream(chatIdListStr.split(",")).collect(Collectors.toList());
        List<GroupMemberCountResult> groupMemberCountList = groupRelationRepository.countByShardingKeyList(chatIdList.stream().map(groupRelationRepository::chatId2ShardingKey).collect(Collectors.toList()), Lists.newArrayList(MemberTypeEnum.EnterpriseMember.getType(), MemberTypeEnum.ExternalContact.getType()), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (!CollectionUtils.isEmpty(groupMemberCountList)) {
            List<GroupMemberCountResult> fullGroupMemberCountList = groupMemberCountList.stream().filter(x -> x.getCount() >= maxGroupMemberCount).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(fullGroupMemberCountList) || groupMemberCountList.size() >= 5) {
                String maxChatId = groupRelationRepository.shardingKey2ChatIdOrUserId(groupMemberCountList.stream().max(Comparator.comparing(GroupMemberCountResult::getCount)).get().getShardingKey());
                removeChatIdList.add(maxChatId);
            }
        }
        return removeChatIdList;
    }

    /**
     * 更新当前群码活动上下文的群聊信息
     *
     * @param activityContext 活动上下文
     * @param newChatId       新增群聊
     * @param nowChatIdList   当前群码绑定的群聊列表
     */
    private void updateGroupMaActivityContext(ActivityTaskInfoBO activityContext, String newChatId, List<String> nowChatIdList) {
        JSONObject activityContextExtraJson = activityContext.getExtraJson();
        Set<String> allChatIdList = activityContextExtraJson.getObject("allChatIdList", new TypeReference<Set<String>>() {
        });
        allChatIdList.add(newChatId);
        activityContextExtraJson.put("allChatIdList", allChatIdList);
        activityContextExtraJson.put("chatIdList", nowChatIdList);
        activityContextExtraJson.put("dataTime", System.currentTimeMillis());
        activityContextService.upsert(activityContext);
        PlatformLogUtil.logFail("企微入群任务调度，更新当前群码活动上下文的群聊信息", LogListUtil.newArrayList(activityContext));
    }

    /**
     * 新增资源资源
     *
     * @param wayEnum   自动创建
     * @param taskId    任务id
     * @param addIdList 新增的资源列表
     */
    private void addResource(ResourceRelationWayEnum wayEnum, Long taskId, List<String> addIdList) {
        if (CollectionUtils.isEmpty(addIdList)) {
            return;
        }
        ArrayList<ResourceRelationDO> resourceRelationList = new ArrayList<>();
        for (String id : addIdList) {
            resourceRelationList.add(resourceRelationConverter.buildGroupManageTaskResourceRelation(String.valueOf(taskId), id, wayEnum));
        }
        resourceRelationService.batchAdd(resourceRelationList);
    }

    /**
     * 批量移除活动上下文
     *
     * @param removeIdList           需要移除的id列表
     * @param activityId             活动Id
     * @param activityTargetTypeEnum 活动目标类型
     */
    private void removeActivityContext(List<String> removeIdList, Long activityId, ActivityTargetTypeEnum activityTargetTypeEnum) {
        for (String removeChatId : removeIdList) {
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setActivityId(activityId);
            query.setTargetTypeEnum(activityTargetTypeEnum);
            query.setTargetId(removeChatId);
            List<ActivityTaskInfoBO> removeActivityTaskInfoList = activityContextService.queryByActivityAndTarget(query);
            activityContextService.batchDeleteByTaskId(removeActivityTaskInfoList.stream().map(ActivityTaskInfoBO::getContextId).collect(Collectors.toList()));
            PlatformLogUtil.logFail("企微入群任务调度，批量移除活动上下文", LogListUtil.newArrayList(removeActivityTaskInfoList));
        }
    }

    /**
     * 插入/更新群聊的活动上下文
     *
     * @param addIdList      需要新增的id列表
     * @param activityId     活动id
     * @param targetTypeEnum 目标类型
     * @param contextId      活动上下文
     */
    private void upsertActivityContext(List<String> addIdList, Long activityId, ActivityTargetTypeEnum targetTypeEnum, Long contextId) {
        for (String addChatId : addIdList) {
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setActivityId(activityId);
            query.setTargetTypeEnum(targetTypeEnum);
            query.setTargetId(addChatId);

            List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
            Long targetActivityContextId;
            if (!CollectionUtils.isEmpty(activityTaskInfoBOS)) {
                targetActivityContextId = ActivityContextService.getNewestBo(activityTaskInfoBOS).getContextId();
            } else {
                targetActivityContextId = activityContextService.generateContextId();
            }

            ActivityTaskInfoBO targetActivityTaskInfoBO = new ActivityTaskInfoBO();
            targetActivityTaskInfoBO.setContextId(targetActivityContextId);
            targetActivityTaskInfoBO.setActivityId(activityId);
            targetActivityTaskInfoBO.setTargetTypeEnum(targetTypeEnum);
            targetActivityTaskInfoBO.setTargetId(addChatId);
            targetActivityTaskInfoBO.setTagIdList(new ArrayList<>());
            JSONObject extraJson1 = new JSONObject();
            extraJson1.put("contextId", contextId);
            extraJson1.put("dataTime", System.currentTimeMillis());
            targetActivityTaskInfoBO.setExtraJson(extraJson1);
            activityContextService.upsert(targetActivityTaskInfoBO);
            PlatformLogUtil.logFail("企微入群任务调度，插入/更新群聊的活动上下文", LogListUtil.newArrayList(targetActivityTaskInfoBO));
        }
    }

    @AteyeInvoker(description = "新增活动上下文")
    public boolean upsertActivityContext(String addIdStr, Long activityId, Long contextId) {
        if (StringUtils.isBlank(addIdStr)) {
            return false;
        }
        List<String> addIdList = Arrays.stream(addIdStr.split(",")).collect(Collectors.toList());
        for (String addChatId : addIdList) {
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setActivityId(activityId);
            query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            query.setTargetId(addChatId);

            List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
            Long targetActivityContextId;
            if (!CollectionUtils.isEmpty(activityTaskInfoBOS)) {
                targetActivityContextId = ActivityContextService.getNewestBo(activityTaskInfoBOS).getContextId();
            } else {
                targetActivityContextId = activityContextService.generateContextId();
            }

            ActivityTaskInfoBO targetActivityTaskInfoBO = new ActivityTaskInfoBO();
            targetActivityTaskInfoBO.setContextId(targetActivityContextId);
            targetActivityTaskInfoBO.setActivityId(activityId);
            targetActivityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            targetActivityTaskInfoBO.setTargetId(addChatId);
            targetActivityTaskInfoBO.setTagIdList(new ArrayList<>());
            JSONObject extraJson1 = new JSONObject();
            extraJson1.put("contextId", contextId);
            extraJson1.put("dataTime", System.currentTimeMillis());
            targetActivityTaskInfoBO.setExtraJson(extraJson1);
            activityContextService.upsert(targetActivityTaskInfoBO);
            PlatformLogUtil.logFail("企微入群任务调度，新增活动上下文", LogListUtil.newArrayList(targetActivityTaskInfoBO));
        }
        return true;
    }

    private void processDismiss(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        JSONObject taskExtInfo = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        boolean autoCreateGroup = taskExtInfo.containsKey("autoCreateGroup") && taskExtInfo.getBoolean("autoCreateGroup");
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        String chatId = getFinalTargetId(context, taskDataBody);
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        if (Objects.isNull(activityContext)) {
            PlatformLogUtil.logFail("群活码管理任务，未找到活动上下文", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            todoTaskVO.setSuccess(true);
            todoTaskVO.setAttribution(TaskAttributionConstant.NOT_FOUND_ACTIVITY_CONTEXT);
            return;
        }

        Long contextId = activityContext.getContextId();
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getByState(String.valueOf(activityContext.getContextId()));
        if (Objects.isNull(wechatJoinGroupDO)) {
            PlatformLogUtil.logFail("【任务执行异常】群活码管理任务，群活码数据不存在", LogListUtil.newArrayList(todoTaskVO.getTaskId(), contextId));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】群活码管理任务，群活码数据不存在，任务ID:%s，上下文ID:%s", todoTaskVO.getTaskId(), contextId));
        }

        taskLockManager.tryRunWithLock(TairConstant.UPDATE_JOIN_GROUP_CONFIG_LOCK + wechatJoinGroupDO.getId(),
                3000L,
                3,
                () -> {
                    ResourceRelationQuery query = new ResourceRelationQuery();
                    query.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
                    query.setSourceType(ResourceTypeEnum.TASK.getCode().byteValue());
                    query.setTargetType(ResourceTypeEnum.CHAT_ID.getCode().byteValue());
                    query.setTargetId(chatId);
                    resourceRelationService.deleteByParam(query);
                    wechatJoinGroupService.modifyChatIdList(wechatJoinGroupDO.getId(), null, Lists.newArrayList(chatId));
                    if (autoCreateGroup) {
                        checkCreateGroup(context, todoTaskVO, wechatJoinGroupDO.getId(), contextId);
                    }
                },
                () -> {
                    PlatformLogUtil.logFail("【任务执行异常】群活码配置更新失败", LogListUtil.newArrayList(todoTaskVO.getTaskId(), contextId, wechatJoinGroupDO.getId()));
                    DingTalkApi.sendTaskMessage(String.format("【任务执行异常】群活码配置更新失败，任务ID:%s，上下文ID:%s，群活码ID:%s", todoTaskVO.getTaskId(), contextId, wechatJoinGroupDO.getId()));
                }
        );
    }

    private void processAddMember(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        String chatId = getFinalTargetId(context, taskDataBody);
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        if (Objects.isNull(activityContext)) {
            PlatformLogUtil.logFail("群活码管理任务，执行加人获取上下文失败", LogListUtil.newArrayList(todoTaskVO.getTaskId(), taskDataBody));
            todoTaskVO.setSuccess(true);
            todoTaskVO.setAttribution(TaskAttributionConstant.NOT_FOUND_ACTIVITY_CONTEXT);
            return;
        }


        Long contextId = activityContext.getContextId();
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getByState(String.valueOf(contextId));
        if (Objects.isNull(wechatJoinGroupDO)) {
            PlatformLogUtil.logFail("【任务执行异常】群活码管理任务，群活码数据不存在", LogListUtil.newArrayList(todoTaskVO.getTaskId(), chatId, contextId));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】群活码管理任务，群活码数据不存在，任务ID:%s，群聊ID:%s，上下文ID:%s", todoTaskVO.getTaskId(), chatId, contextId));
        }

        taskLockManager.tryRunWithLock(TairConstant.UPDATE_JOIN_GROUP_CONFIG_LOCK + wechatJoinGroupDO.getId(),
                5000L,
                5,
                () -> checkCreateGroup(context, todoTaskVO, wechatJoinGroupDO.getId(), contextId),
                () -> {
                    PlatformLogUtil.logFail("【任务执行异常】群活码配置管理任务执行失败", LogListUtil.newArrayList(todoTaskVO.getTaskId(), contextId, wechatJoinGroupDO.getId()));
                    DingTalkApi.sendTaskMessage(String.format("【任务执行异常】群活码配置管理任务执行失败，任务ID:%s，上下文ID:%s，群活码ID:%s", todoTaskVO.getTaskId(), contextId, wechatJoinGroupDO.getId()));
                }
        );
    }

    /**
     * 必须要在持有分布式锁时调用
     */
    private void checkCreateGroup(TaskExecuteContext context, TodoTaskVO todoTaskVO, Long wechatJoinGroupId, Long contextId) {
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        JSONObject taskExtraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        boolean autoCreateGroup = taskExtraInfoJson.containsKey("autoCreateGroup") && taskExtraInfoJson.getBoolean("autoCreateGroup");
        // 锁内查最新群活码信息
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getById(wechatJoinGroupId);
        String chatIdListStr = Optional.ofNullable(wechatJoinGroupDO.getChatIdList()).orElse("");
        List<String> chatIdList = Arrays.stream(chatIdListStr.split(",")).collect(Collectors.toList());
        List<GroupMemberCountResult> groupMemberCountList = groupRelationRepository.countByShardingKeyList(chatIdList.stream().map(groupRelationRepository::chatId2ShardingKey).collect(Collectors.toList()), Lists.newArrayList(MemberTypeEnum.EnterpriseMember.getType(), MemberTypeEnum.ExternalContact.getType()), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        // 群人员上限
        Integer maxGroupMemberCount = getMaxGroupMemberCount(context,taskExtraInfoJson);
        if (!taskExtraInfoJson.containsKey("autoCreateGroupMinValidCount")) {
            return;
        }
        // 自动创建群可用群最小值
        Integer autoCreateGroupMinValidCount = taskExtraInfoJson.getInteger("autoCreateGroupMinValidCount");
        List<GroupMemberCountResult> fullGroupList = groupMemberCountList.stream().filter(x -> x.getCount() >= maxGroupMemberCount).collect(Collectors.toList());
        // 未设置自动建群，直接移除
        if (!CollectionUtils.isEmpty(fullGroupList) && !autoCreateGroup) {
            if (chatIdList.size() <= 1) {
                PlatformLogUtil.logFail("【任务执行异常】群活码管理任务，未设置自动建群直接移除，只剩一个人不执行", LogListUtil.newArrayList(todoTaskVO.getTaskId(), contextId));
                return;
            }

            String removeChatId = groupRelationRepository.shardingKey2ChatIdOrUserId(fullGroupList.stream().max(Comparator.comparing(GroupMemberCountResult::getCount)).get().getShardingKey());
            wechatJoinGroupService.modifyChatIdList(wechatJoinGroupDO.getId(), null, Lists.newArrayList(removeChatId));
            PlatformLogUtil.logFail("remove group", LogListUtil.newArrayList(removeChatId, contextId));
            ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
            JSONObject activityContextExtraJson = activityContext.getExtraJson();
            activityContextExtraJson.put("chatIdList", Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            activityContextExtraJson.put("dataTime", System.currentTimeMillis());
            activityContextService.upsert(activityContext);
            PlatformLogUtil.logFail("未设置自动建群直接移除，更新上下文", LogListUtil.newArrayList(activityContext));
            // 删除已移除群聊上下文信息（targetType = 客户群类型）
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setActivityId(context.getTaskInfoDOSnapshot().getActivityId());
            query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_CHAT_ID);
            query.setTargetId(removeChatId);
            List<ActivityTaskInfoBO> removeActivityTaskInfoList = activityContextService.queryByActivityAndTarget(query);
            activityContextService.batchDeleteByTaskId(removeActivityTaskInfoList.stream().map(ActivityTaskInfoBO::getContextId).collect(Collectors.toList()));
            PlatformLogUtil.logFail("未设置自动建群直接移除，删除上下文", LogListUtil.newArrayList(removeActivityTaskInfoList));
            return;
        }

        // 可用群数量暂时满足设置
        if (groupMemberCountList.size() - fullGroupList.size() >= autoCreateGroupMinValidCount) {
            PlatformLogUtil.logFail("可用群数量暂时满足设置", LogListUtil.newArrayList(contextId));
            return;
        }

        // 历史群可否复用
        List<WechatGroupRelationVO> wechatGroupRelationVOS = canReuseGroupList(todoTaskVO.getTaskId(), maxGroupMemberCount, chatIdList);
        if (!CollectionUtils.isEmpty(wechatGroupRelationVOS)) {
            ArrayList<String> maxChatIdList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(fullGroupList)) {
                maxChatIdList.add(groupRelationRepository.shardingKey2ChatIdOrUserId(fullGroupList.stream().max(Comparator.comparing(GroupMemberCountResult::getCount)).get().getShardingKey()));
            }
            String chatId = wechatGroupRelationVOS.get(0).getChatId();
            wechatJoinGroupService.modifyChatIdList(wechatJoinGroupId, Lists.newArrayList(chatId), maxChatIdList);
            ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
            if (Objects.isNull(activityContext)) {
                return;
            }

            List<String> nowChatIdList = Arrays.stream(wechatJoinGroupService.getById(wechatJoinGroupId).getChatIdList().split(",")).collect(Collectors.toList());
            // 更新群码上下文
            updateGroupMaActivityContext(activityContext, chatId, nowChatIdList);
            // 删除已移除群聊上下文信息（targetType = 客户群类型）
            removeActivityContext(maxChatIdList, activityContext.getActivityId(), ActivityTargetTypeEnum.WX_CHAT_ID);
            // 插入/更新活动上下文信息（targetType = 客户群类型）
            upsertActivityContext(Lists.newArrayList(chatId), activityContext.getActivityId(), ActivityTargetTypeEnum.WX_CHAT_ID, contextId);
            PlatformLogUtil.logFail("历史群可否复用", LogListUtil.newArrayList(maxChatIdList, chatId));
            return;
        }

        // 检查是否重复建群（异步操作耗时较长）
        if (checkDuplicate(context, todoTaskVO)) {
            return;
        }


        Map<String, List<String>> userIdMap = getGroupInitUserIdList(context.getTaskInfoDOSnapshot(), context.getTaskDataVO(), context.getInstanceId());
        List<String> initUserIdList = userIdMap.get(GROUP_ADMIN_ID);
        String ownerUserId = getSendUserId(context, todoTaskVO);

        String groupNamePrefix = taskExtraInfoJson.getObject("autoCreateGroupNamePrefix", new TypeReference<String>() {
        });
        Integer groupNameFirstIndex = taskExtraInfoJson.getInteger("autoCreateGroupNameFirstIndex");

        // 群名称后缀
        int index = ldbTairManager.incr(TairConstant.AUTO_CREATE_GROUP_NAME_INDEX_PREFIX + wechatJoinGroupId + "_" + groupNamePrefix, 1, groupNameFirstIndex, 86400 * 365 * 10);
        // 群名称
        String groupName = groupNamePrefix + (index - 1);
        // 群公告
        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(MaterialSendSceneTypeConstant.QUNGG);
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setWxUserId(ownerUserId);
        //todo 暂时不支持群公告带群id
        materialTrackRelationDTO.setSendChatId("-1L");

        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setWechatUserId(ownerUserId);
        materialContentConvertContext.setExtraInfo(new HashMap<>());
        if (StringUtils.isNotBlank(todoTaskVO.getData().get(0).getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo()));
        }
        materialContentConvertContext.setOriginContent(Objects.nonNull(materailInfoDO) ? materailInfoDO.getContent() : null);

        CreateGroupRequest createGroupRequest = new CreateGroupRequest();
        createGroupRequest.setRequestId(contextId + "_" + UUID.randomUUID().toString().substring(0, 10));
        createGroupRequest.setGroupName(groupName);
        createGroupRequest.setUserId(ownerUserId);
        createGroupRequest.setInitUserIdList(initUserIdList);
        createGroupRequest.setMaterailInfoDO(materailInfoDO);
        createGroupRequest.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        createGroupRequest.setMaterialContentConvertContext(materialContentConvertContext);
        createGroupRequest.setTaskType(TaskType.MANAGER_WECHAT_JOIN_GROUP);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> enterpriseWechatManager.asyncCreateGroup(createGroupRequest));
        if (!result.isSuccess()) {
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED, result.getMsg());
        }

        PlatformLogUtil.logFail("异步企微建群执行", LogListUtil.newArrayList(wechatJoinGroupId));
    }

    /**
     * 获取群成员数上限
     *
     * @param context
     * @param taskExtraInfoJson
     * @return
     */
    private Integer getMaxGroupMemberCount(TaskExecuteContext context, JSONObject taskExtraInfoJson) {
        Integer maxGroupMemberCount = taskExtraInfoJson.getInteger("maxGroupMemberCount");
        if(!CollectionUtils.isEmpty(context.getExtInfo()) && context.getExtInfo().containsKey(TaskConstant.IS_PATROL) && (Boolean) context.getExtInfo().get(TaskConstant.IS_PATROL)){
            return SwitchConfig.AUTO_CREATE_GROUP_CHECK_MIN_MEMBER_COUNT < maxGroupMemberCount ? SwitchConfig.AUTO_CREATE_GROUP_CHECK_MIN_MEMBER_COUNT : maxGroupMemberCount;

        }
        return maxGroupMemberCount;

    }

    /**
     * 可复用的群聊列表
     *
     * @param taskId              任务Id
     * @param maxGroupMemberCount 群聊人数上限
     * @param chatIdList          当前群码chatId列表
     * @return 可复用列表
     */
    private List<WechatGroupRelationVO> canReuseGroupList(Long taskId, Integer maxGroupMemberCount, List<String> chatIdList) {
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
        query.setSourceType(ResourceTypeEnum.TASK.getCode().byteValue());
        query.setSourceId(String.valueOf(taskId));
        PageResultVO<WechatGroupRelationVO> pageResultVO = groupCodeRelationService.list(query);
        if (pageResultVO.isSuccess() && CollectionUtils.isEmpty(pageResultVO.getData())) {
            return null;
        }
        List<WechatGroupRelationVO> canReuseGroupList = pageResultVO.getData()
                .stream()
                .filter(wechatGroupRelationVO -> !chatIdList.contains(wechatGroupRelationVO.getChatId()) && wechatGroupRelationVO.getInGroupCount() < maxGroupMemberCount)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canReuseGroupList)) {
            return null;
        }
        return canReuseGroupList;
    }

    /**
     * 获取创建群聊的初始化成员
     *
     * @param taskInfoDO 任务信息
     * @return 初始化成员列表
     */
    private Map<String, List<String>> getGroupInitUserIdList(TaskInfoDO taskInfoDO, TaskDataVO taskDataVO, Long instanceId) {
        Long manageGroupId = TaskDataUtils.getLongFromExtInfo(taskInfoDO, "manageGroupId");
        if (manageGroupId == null) {
            return getIsvRouteUserIdList(TaskDataUtils.getListFromExtInfo(taskInfoDO, "groupMemberUserIdList"), taskDataVO, instanceId);
        }
        ManagementGroupVO managementGroupVO = managementGroupRelationService.getById(manageGroupId);
        if (managementGroupVO != null && !CollectionUtils.isEmpty(managementGroupVO.getInfoList())) {
            List<ManagementGroupVO.Info> infoList = managementGroupVO.getInfoList();
            List<String> userIdList = infoList.stream().map(ManagementGroupVO.Info::getTargetId).collect(Collectors.toList());
            return getIsvRouteUserIdList(userIdList, taskDataVO, instanceId);
        }
        // 兼容历史
        return getIsvRouteUserIdList(TaskDataUtils.getListFromExtInfo(taskInfoDO, "groupMemberUserIdList"), taskDataVO, instanceId);
    }

    private Map<String, List<String>> getIsvRouteUserIdList(List<String> initUserIdList, TaskDataVO taskDataVO, Long instanceId) {
        Map<String, List<String>> userIdMap = new HashMap<>();
        if (CollectionUtils.isEmpty(initUserIdList)) {
            PlatformLogUtil.logFail("根据群账号分配策略获取管理员账号失败, 初始成员列表为空", LogListUtil.newArrayList(initUserIdList));
            throw new TripscrmException(TripSCRMErrorCode.INSUFFICIENT_WECHAT_USER);
        }
        TripSCRMResult<GroupMemberAllocationDTO> result = isvRouteService.getGroupAllocationWechatUserList(initUserIdList, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(), Math.min(initUserIdList.size(), SwitchConfig.MAX_GROUP_ADMIN_COUNT + 1));
        if (!result.isSuccess() || Objects.isNull(result.getData()) || CollectionUtils.isEmpty(result.getData().getAdminList())) {
            PlatformLogUtil.logFail("根据群账号分配策略获取管理员账号失败", LogListUtil.newArrayList(initUserIdList, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(),  Math.min(initUserIdList.size(), SwitchConfig.MAX_GROUP_ADMIN_COUNT + 1), result));
            DingTalkApi.sendTaskMessage(String.format("根据群账号分配策略获取管理员账号失败，任务ID:%s，实例ID:%s, 失败原因:%s", taskDataVO, instanceId, result.getMsg()));
            if (SwitchConfig.SELECT_ADMIN_USER_BY_ISV_ROUTE_STRATEGY) {
                throw new TripscrmException(TripSCRMErrorCode.INSUFFICIENT_WECHAT_USER);
            }
            userIdMap.put(GROUP_ADMIN_ID, initUserIdList.subList(0, SwitchConfig.MAX_GROUP_ADMIN_COUNT + 1));
            return userIdMap;
        }

        userIdMap.put(GROUP_ADMIN_ID, result.getData().getAdminList().stream().map(WechatUserDTO::getUserId).collect(Collectors.toList()));
        if (Objects.nonNull(result.getData().getOwner()) && StringUtils.isNotBlank(result.getData().getOwner().getUserId())) {
            userIdMap.put(GROUP_OWNER_ID, Collections.singletonList(result.getData().getOwner().getUserId()));
        }
        return userIdMap;
    }

    private boolean checkDuplicate(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 限制30秒内只能创建一个群
        if (Objects.nonNull(ldbTairManager.get(TairConstant.TASK_CREATE_GROUP_PREFIX + todoTaskVO.getTaskId()))) {
            return true;
        }

        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        PlatformLogUtil.logFail("任务重复执行", LogListUtil.newArrayList(taskDataBody));
        return ldbTairManager.incr(TairConstant.TASK_CREATE_GROUP_PREFIX + todoTaskVO.getTaskId(), 1, 0, 90) > 1;
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            return (String) taskDataBody.getContext().get("sendUserId");
        }

        Map<String, List<String>> userIdMap = getGroupInitUserIdList(context.getTaskInfoDOSnapshot(), context.getTaskDataVO(), context.getInstanceId());
        if (userIdMap.containsKey(GROUP_OWNER_ID)) {
            return userIdMap.get(GROUP_OWNER_ID).get(0);
        }

        List<String> groupMemberUserIdList = userIdMap.get(GROUP_ADMIN_ID);

        List<WechatUserDTO> wechatUserList = wechatUserService.listByIdAndStatus(groupMemberUserIdList, WechatUserStatusEnum.ACTIVE);
        List<WechatUserDTO> onlineWechatUserList = wechatUserList.stream().filter(x -> Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(x.getOnlineStatus()))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(onlineWechatUserList)) {
            PlatformLogUtil.logFail("发送消息的userId为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE);
        }

        WechatUserDTO wechatUserDTO = onlineWechatUserList.get(ThreadLocalRandom.current().nextInt(onlineWechatUserList.size()));
        taskDataBody.getContext().put("sendUserId", wechatUserDTO.getUserId());
        return wechatUserDTO.getUserId();
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_CHAT_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (taskDataBody.getContext().containsKey("chatId")) {
            return (String) taskDataBody.getContext().get("chatId");
        }

        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("任务的targetId为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!Lists.newArrayList(ActivityTargetTypeEnum.WX_CHAT_ID).contains(activityTargetType)) {
            PlatformLogUtil.logFail("任务的仅存在接收的微信id", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        String chatId = taskDataBody.getTargetId();
        taskDataBody.getContext().put("chatId", chatId);
        return chatId;
    }

    @AteyeInvoker(description = "更新群活码信息", paraDesc = "id&chatIdListStr&activityId")
    public Boolean updateCodeData(Long id, String chatIdListStr) {
        List<String> newChatIdList = Arrays.stream(chatIdListStr.split(",")).collect(Collectors.toList());
        Integer update = wechatJoinGroupService.update(id, newChatIdList);
        if (update <= 0) {
            return false;
        }
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getById(id);
        if (Objects.isNull(wechatJoinGroupDO)) {
            throw new RuntimeException("更新的群活码信息不存在");
        }
        Long contextId = Long.valueOf(wechatJoinGroupDO.getState());
        ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        if (Objects.isNull(activityTaskInfoBO)) {
            throw new RuntimeException("活动不存在");
        }
        // 活码上下文更新
        updateGroupMaActivityContext(activityTaskInfoBO, newChatIdList);
        // 移除上下文
        List<String> oldChatIdList = Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).collect(Collectors.toList());
        removeActivityContext(oldChatIdList, activityTaskInfoBO.getActivityId(), ActivityTargetTypeEnum.WX_CHAT_ID);
        // 新增上下文
        upsertActivityContext(newChatIdList, activityTaskInfoBO.getActivityId(), ActivityTargetTypeEnum.WX_CHAT_ID, contextId);
        return true;
    }

    /**
     * 更新当前群码活动上下文的群聊信息
     *
     * @param activityContext 活动上下文
     * @param newChatIdList   新的chatId列表
     */
    private void updateGroupMaActivityContext(ActivityTaskInfoBO activityContext, List<String> newChatIdList) {
        JSONObject activityContextExtraJson = activityContext.getExtraJson();
        List<String> allChatIdList = activityContextExtraJson.getObject("allChatIdList", new TypeReference<List<String>>() {
        });
        allChatIdList.addAll(newChatIdList);
        Set<String> newAllChatIdList = allChatIdList.stream().collect(Collectors.toSet());
        activityContextExtraJson.put("allChatIdList", newAllChatIdList);
        activityContextExtraJson.put("chatIdList", newChatIdList);
        activityContextExtraJson.put("dataTime", System.currentTimeMillis());
        activityContextService.upsert(activityContext);
        PlatformLogUtil.logFail("更新当前群码活动上下文的群聊信息", LogListUtil.newArrayList(activityContext));
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.MANAGER_WECHAT_JOIN_GROUP;
    }
}
