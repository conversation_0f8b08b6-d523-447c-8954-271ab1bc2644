package com.alibaba.tripscrm.service.middleware.diamond;

import org.springframework.stereotype.Component;

/**
 * Diamond的例子，通过@Value将Diamond的配置注入到对象中
 * 详见http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-diamond
 *
 * <AUTHOR>
 */
@Component
public class DiamondDemo {

    /**
     * 这里null的数据，是通过application.properties配置注入的
     */
    //@Value("${string}")
    //private String stringValue;
    //
    //@Value("${number}")
    //private int intValue;
    //
    //@Value("${boolean}")
    //private boolean booleanValue;
    //
    //public String getStringValue() {
    //    return stringValue;
    //}
    //
    //public int getIntValue() {
    //    return intValue;
    //}
    //
    //public boolean getBooleanValue() {
    //    return booleanValue;
    //}
}
