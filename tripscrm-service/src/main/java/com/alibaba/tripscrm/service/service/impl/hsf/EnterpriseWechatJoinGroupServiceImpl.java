package com.alibaba.tripscrm.service.service.impl.hsf;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.WechatJoinGroupDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatJoinGroupQuery;
import com.alibaba.tripscrm.domain.EnterpriseWechatChatGroupQrCodeDTO;
import com.alibaba.tripscrm.domain.request.TripSCRMEnterpriseWechatFetchGroupQrCodeRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMEnterpriseWechatJoinGroupRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbLockManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatJoinGroupService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.wechat.EnterpriseWechatJoinGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023-08-10 18:16:22
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@HSFProvider(serviceInterface = EnterpriseWechatJoinGroupService.class)
public class EnterpriseWechatJoinGroupServiceImpl implements EnterpriseWechatJoinGroupService {
    private final WechatGroupService wechatGroupService;
    private final WechatJoinGroupService wechatJoinGroupService;
    private final LdbTairManager ldbTairManager;
    private final LdbLockManager ldbLockManager;
    private final ActivityContextService activityContextService;

    @Override
    @ServiceLog("查询群活码当前绑定任一群聊的群码")
    public TripSCRMResult<String> getGroupQrCode(TripSCRMEnterpriseWechatJoinGroupRequest request) {
        if (Objects.isNull(request) || !StringUtils.hasText(request.getState())) {
            PlatformLogUtil.logFail("invalid request", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        PlatformLogUtil.logInfo("日志信息", LogListUtil.newArrayList(request));
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getByState(request.getState());
        if (Objects.isNull(wechatJoinGroupDO)) {
            PlatformLogUtil.logFail("wechatJoinGroupDO is null", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }

        String sortedChatIdListStr = Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).sorted().collect(Collectors.joining(","));
        String cacheData = (String) ldbTairManager.get(sortedChatIdListStr);
        // 走缓存
        if (StringUtils.hasText(cacheData)) {
            List<String> qrCodeList = JSONObject.parseObject(cacheData, new TypeReference<List<String>>() {
            });

            String qrCode = qrCodeList.get(ThreadLocalRandom.current().nextInt(qrCodeList.size()));
            PlatformLogUtil.logFail("data in cache", LogListUtil.newArrayList(request, cacheData, qrCode));
            return TripSCRMResult.success(qrCode);
        }

        List<String> chatIdList = Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).collect(Collectors.toList());
        List<WechatGroupVO> wechatGroupVOList = wechatGroupService.listByChatIdList(chatIdList, false);
        if (CollectionUtils.isEmpty(wechatGroupVOList)) {
            PlatformLogUtil.logFail("wechatGroup is empty", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }


        List<String> qrCodeList = wechatGroupVOList.stream().map(WechatGroupVO::getQrCode).filter(StringUtils::hasText).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(qrCodeList)) {
            PlatformLogUtil.logFail("group qrcode all empty", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        }

        String qrCode = qrCodeList.get(ThreadLocalRandom.current().nextInt(qrCodeList.size()));
        if (wechatGroupVOList.size() == qrCodeList.size()) {
            // 缓存1小时
            cacheData = JSONObject.toJSONString(qrCodeList);
            PlatformLogUtil.logFail("put cache data", LogListUtil.newArrayList(request, cacheData));
            ldbTairManager.put(sortedChatIdListStr, JSONObject.toJSONString(qrCodeList), 3600);
        } else {
            PlatformLogUtil.logFail("exist empty qrcode", LogListUtil.newArrayList(request));
        }

        PlatformLogUtil.logFail("success", LogListUtil.newArrayList(request, qrCode));
        return TripSCRMResult.success(qrCode);
    }

    @Override
    public TripSCRMResult<EnterpriseWechatChatGroupQrCodeDTO> fetchGroupQrCode(TripSCRMEnterpriseWechatFetchGroupQrCodeRequest request) {
        if (Objects.isNull(request) || !StringUtils.hasText(request.getCorpId())) {
            PlatformLogUtil.logFail("invalid request", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        EnterpriseWechatChatGroupQrCodeDTO qrCodeDTO = new EnterpriseWechatChatGroupQrCodeDTO();
        // 查询群活码
        if (StringUtils.hasText(request.getConfigId())) {
            WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.selectByConfigId(request.getConfigId());
            if (null != wechatJoinGroupDO) {
                qrCodeDTO.setQrCode(wechatJoinGroupDO.getQrCode());
                qrCodeDTO.setConfigId(wechatJoinGroupDO.getConfigId());
                qrCodeDTO.setState(wechatJoinGroupDO.getState());
                return TripSCRMResult.success(qrCodeDTO);
            }
        }
        // 是否自动创建群活码
        if (BooleanUtils.isTrue(request.getAutoCreate()) && !CollectionUtils.isEmpty(request.getChatIdList())) {
            // 尝试获取分布式锁
            boolean locked = false;
            String lockKey = getLockKey(request.getChatIdList());
            try {
                locked = ldbLockManager.lock(lockKey, 30);
                if (!locked) {
                    PlatformLogUtil.logFail("创建群活码，获取分布式锁失败", LogListUtil.newArrayList(request));
                    return TripSCRMResult.fail(TripSCRMErrorCode.GET_TAIR_LOCK_FAIL);
                }
                WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.create(request.getChatIdList(), activityContextService.generateContextId().toString(), null, request.getCorpId());
                qrCodeDTO.setQrCode(wechatJoinGroupDO.getQrCode());
                qrCodeDTO.setState(wechatJoinGroupDO.getState());
                qrCodeDTO.setConfigId(wechatJoinGroupDO.getConfigId());
            } catch (Exception e) {
                PlatformLogUtil.logException("创建群活码异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            } finally {
                if (locked) {
                    ldbLockManager.unlock(lockKey);
                }
            }
        }
        return TripSCRMResult.success(qrCodeDTO);
    }


    private String getLockKey(List<String> chatIdList) {
        List<String> sortedList = chatIdList.stream().sorted().collect(Collectors.toList());
        return TairConstant.CREATE_GROUP_QR_CODE_LOCK_PREFIX + String.join("_", sortedList);
    }
}
