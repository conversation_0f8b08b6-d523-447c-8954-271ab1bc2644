package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/5 13:58
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ResourceRelationQuery extends BasePageRequest {

    /**
     * 分页标识
     */
    private boolean page;

    /**
     * 资源关系类型
     */
    private Byte type;

    /**
     * 资源类型
     */
    private Byte sourceType;

    /**
     * 资源id
     */
    private String sourceId;

    /**
     * 资源Id列表
     */
    private List<String> sourceIdList;

    /**
     * 目标类型
     */
    private Byte targetType;

    /**
     * 目标Id
     */
    private String targetId;

    /**
     * 目标名称
     */
    private String targetName;


    /**
     * state码
     */
    private String state;

    /**
     * 最小id
     */
    private Long minId;

    /**
     * 最小插入时间
     */
    private Date startCreateTime;
}
