package com.alibaba.tripscrm.service.service.strategy.websocket.param.response;

import lombok.Data;

/**
 * 发消息 推送体
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Data
public class SendMessageResponse {
    /**
     * 会话id 1:群聊会话id，2:私聊时，企微客户的userId，3:私聊时，企微成员的userId
     */
    private String chatId;
    /**
     * 会话类型 1:群聊，2:与企微客户的私聊，3:与企微成员的私聊
     * @see ChatTypeEnum
     */
    private Integer chatType;

    /**
     * 发送人id
     */
    private String senderId;
    /**
     * 发送人名称
     */
    private String senderName;
    /**
     * 发送人头像
     */
    private String senderAvatar;
    /**
     * 消息id
     */
    private String messageId;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 消息类型
     * @see FusionChatMessageTypeEnum
     */
    private String msgType;
    /**
     * 消息内容
     */
    private String msgContent;
    /**
     * 用户会话显示最新一条消息内容
     */
    private String text;
    /**
     * 消息未读数
     */
    private Integer unreadCount;
    /**
     * 消息未读数
     */
    private Integer allUnreadCount;

}
