package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.domain.FollowUserCustomerRelationInfo;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserPageQuery;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerListQuery;
import com.alibaba.tripscrm.service.model.domain.request.WechatCustomerAddTagRequest;
import com.alibaba.tripscrm.service.model.domain.request.WechatCustomerDeleteTagRequest;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-07-04 11:37:28
 */
public interface WechatCustomerService {
    /**
     * 查询指定企微成员的客户列表
     *
     * @param userId 企微成员id
     * @return 客户列表
     */
    List<CustomerRelationDO> listByUserId(String userId, String corpId);

    /**
     * 获取客户添加的企微成员列表（行程管家专用）
     *
     * @param uid 淘宝Id
     * @return FollowUserInfo
     */
    FollowUserInfo listFollowUserForJourney(String uid);

    /**
     * 获取客户添加的企微成员列表
     *
     * @param query 查询条件
     * @return FollowUserInfo
     */
    FollowUserInfo listFollowUser(FollowUserQuery query);

    /**
     * 获取客户添加的企微成员列表
     *
     * @param query 查询条件
     * @return FollowUserPageQuery
     */
    PageInfo<FollowUserCustomerRelationInfo> pageFollowUserInfo(FollowUserPageQuery query);

    /**
     * 分页获取企微客户列表
     *
     * @param query 查询条件
     * @return 企微客户信息
     */
    PageInfoDTO<WechatCustomerVO> listPageInfo(WechatCustomerListQuery query);

    /**
     * 根据unionId查询externalUserId
     */
    String getExternalUserIdByUnionId(String unionId);

    /**
     * 客户批量添加标签
     *
     * @param request 请求
     */
    @Deprecated
    Integer addTag(WechatCustomerAddTagRequest request);

    /**
     * 客户批量删除标签
     *
     * @param request 请求
     */
    @Deprecated
    void deleteTag(WechatCustomerDeleteTagRequest request);

    /**
     * 根据 externalUserId 查询 客户的信息
     *
     * @param externalUserIdList externalUserId
     * @return 客户的信息
     */
    List<WechatCustomerVO> listByExternalUserIdList(List<String> externalUserIdList);

    /**
     * 根据 externalUserId 查询 unionId
     *
     * @param externalUserId externalUserId
     * @param corpId         corpId
     * @return 客户的信息
     */
    String getUnionIdByExternalUserId(String externalUserId, String corpId);

    /**
     * 根据 userIdList + externalUserId 查询 followUserIdList
     *
     * @param userIdList     userIdList
     * @param externalUserId externalUserId
     * @return 客户的信息
     */
    List<String> getFollowUserIdListByUserIdListAndExternalUserId(List<String> userIdList, String externalUserId);

    /**
     * 获取企微成员的客户数量
     *
     * @param userId
     * @return
     */
    Long getCustomerCount(String userId, Boolean includeSingle);

    /**
     * 查询企微账号下对应的客户数量
     *
     * @param userIdList 企微 userId
     * @return 客户数量
     */
    Map<String, Long> getCustomerCount(List<String> userIdList);

    /**
     * 查询企微账号下对应的客户数量（读本地缓存）
     *
     * @param userId
     * @return 客户数量
     */
    Long getCustomerCountWithCache(String userId, Boolean includeSingle);

    /**
     * 更新客户关系诸葛标签
     *
     * @param unionId 客户微信unionId
     * @param uid 客户淘宝id
     * @return 是否更新成功
     */
    boolean updateCustomerRelationCrowdTag(String unionId, String uid);

    /**
     * 根据externalUserId、corpId、unionId 获取uid
     *
     * @param externalUserId 客户externalUserId
     * @param corpId 企业id
     * @param unionId 客户微信unionId
     * @return uid
     */
    String getUidByExternalUserIdAndCorpIdAndUnionId(String externalUserId, String corpId, String unionId);
}
