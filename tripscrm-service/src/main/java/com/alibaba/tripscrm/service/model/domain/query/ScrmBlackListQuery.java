package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.service.enums.system.BlackListTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/6/6 19:43
 */
@Data
@Builder
public class ScrmBlackListQuery {
    /**
     * Id
     */
    private Long id;

    /**
     * 目标Id
     */
    private List<String> targetIdList;

    /**
     * 目标类型
     */
    private ActivityTargetTypeEnum targetType;

    /**
     * 加密后的客户淘宝id
     */
    private BlackListTypeEnum type;
}
