package com.alibaba.tripscrm.service.service.strategy.sop.factory;

import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.model.vo.activity.AbstractActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.service.strategy.sop.AbstractActivitySop;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 活动SOP处理器工厂
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ActivitySopFactory<T extends AbstractActivitySopConfigVO> {
    private final List<AbstractActivitySop<T>> activitySopList;

    private final Map<SopTypeEnum, AbstractActivitySop<T>> activitySopMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        for (AbstractActivitySop<T> activitySop : activitySopList) {
            activitySopMap.put(activitySop.getSopType(), activitySop);
        }
    }

    /**
     * 注册
     *
     * @param sopType     sopType
     * @param activitySop activitySop
     */
    public void registry(SopTypeEnum sopType, AbstractActivitySop<T> activitySop) {
        activitySopMap.put(sopType, activitySop);
    }

    public AbstractActivitySop<T> getActivitySop(SopTypeEnum sopType) {
        return activitySopMap.get(sopType);
    }

    public Long create(ActivitySopVO<T> activitySopVO) {
        SopTypeEnum sopType = SopTypeEnum.of(activitySopVO.getSopType());
        AbstractActivitySop<T> activitySop = getActivitySop(sopType);
        return activitySop.create(activitySopVO);
    }

    public void update(ActivitySopVO<T> activitySopVO) {
        SopTypeEnum sopType = SopTypeEnum.of(activitySopVO.getSopType());
        AbstractActivitySop<T> activitySop = getActivitySop(sopType);
        activitySop.update(activitySopVO);
    }

    public void fillInfo(ActivitySopVO<T> activitySopVO) {
        SopTypeEnum sopType = SopTypeEnum.of(activitySopVO.getSopType());
        AbstractActivitySop<T> activitySop = getActivitySop(sopType);
        activitySop.fillInfo(activitySopVO);
    }
}
