package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class TaskInstanceQueryRequest implements Serializable {
    private static final long serialVersionUID = -4535749078388092426L;

    /** 主键（任务实例）ID **/
    private Long id;

    /** 任务ID **/
    private Long taskId;

    /** 逻辑删除标志 **/
    private byte deleted;

    /** 执行状态 **/
    private TaskStatusEnum status;

}
