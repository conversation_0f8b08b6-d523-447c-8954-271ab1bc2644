package com.alibaba.tripscrm.service.manager.opensearch;

import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.alibaba.tripscrm.service.model.domain.query.WxCustomizerRelatedRecordQuery;
import com.alibaba.tripscrm.service.model.vo.customizer.CustomizerRelatedRecordDTO;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.aliyun.opensearch.SearcherClient;
import com.aliyun.opensearch.sdk.generated.search.*;
import com.aliyun.opensearch.sdk.generated.search.general.SearchResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-02 16:12:48
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Slf4j
public class WxCustomizerOpenSearchManager {

    // 【新版SARO链路】企微成员与企微客户的关联数据
    private final String WX_CUSTOMIZER_RELATED_RECORD = "wx_customizer_related_record";
    private final List<String> WX_CUSTOMIZER_RELATED_RECORD_FIELDS = Lists.newArrayList("gmt_create", "wechat_user_id", "origin_city_name", "dest_city_name", "user_id", "origin_city_code", "dest_city_code", "id", "state", "ext_info", "union_id", "gmt_modified", "send_msg_flag", "wx_user_id");


    private final String COUNT = "count";
    private final String DISTINCT_COUNT = "distinct_count";
    private final String VALUE = "value";
    private final String KEY = "key";
    private final String RESULT = "result";
    private final String ITEMS = "items";
    private final String TOTAL = "total";
    private final String FACET = "facet";
    private final String ERRORS = "errors";

    private final SearcherClient searcherClient;


    private SearchParams buildPageSearchParams(BasePageRequest query, String appName, List<String> fields, String kvPairs) {
        // 分页条件
        Config config = new Config(Lists.newArrayList(appName));
        config.setStart((query.getPageNum() - 1) * query.getPageSize());
        config.setHits(query.getPageSize());
        config.setSearchFormat(SearchFormat.JSON);
        config.setFetchFields(fields);
        config.setKvpairs(kvPairs);
        return new SearchParams(config);
    }

    private <R> PageInfo<R> getResultItems(Integer pageNum, Integer pageSize, SearchResult result, Function<JSONObject, R> function) {
        List<R> res = new ArrayList<>();
        JSONObject obj = JSONObject.parseObject(result.getResult());
        if (!obj.containsKey(RESULT)) {
            return PageUtils.getPageInfo(res, a -> a);
        }

        JSONObject resultJo = obj.getJSONObject(RESULT);
        if (!resultJo.containsKey(ITEMS)) {
            return PageUtils.getPageInfo(res, a -> a);
        }

        JSONArray items = resultJo.getJSONArray(ITEMS);
        for (int i = 0; i < items.size(); i++) {
            res.add(function.apply(items.getJSONObject(i)));
        }

        return PageUtils.getPageInfo(pageNum, pageSize, resultJo.getInteger(TOTAL), res);
    }

    /**
     * 查询定制师关系
     *
     * @param query 查询条件
     * @return FollowUserInfo
     */
    public PageInfo<CustomizerRelatedRecordDTO> queryCustomizerRelatedRecordList(WxCustomizerRelatedRecordQuery query) {
        List<CustomizerRelatedRecordDTO> res = new ArrayList<>();

        try {
            SearchParams searchParams = buildPageSearchParams(query, WX_CUSTOMIZER_RELATED_RECORD, WX_CUSTOMIZER_RELATED_RECORD_FIELDS, null);

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (StringUtils.isNotEmpty(query.getUnionId())) {
                queryList.add(String.format("union_id:'%s'", query.getUnionId()));
            }
            if (Objects.nonNull(query.getSendMsgFlag())) {
                queryList.add(String.format("send_msg_flag:'%s'", query.getSendMsgFlag()));
            }
            if (StringUtils.isNotEmpty(query.getDestCityName())) {
                queryList.add(String.format("dest_city_name:'%s'", query.getDestCityName()));
            }
            if (StringUtils.isNotEmpty(query.getState())) {
                queryList.add(String.format("state:'%s'", query.getState()));
            }
            if (StringUtils.isNotBlank(query.getOriginCityName())) {
                queryList.add(String.format("dest_city_code:'%s'", query.getDestCityCode()));
            }
            if (Objects.nonNull(query.getWechatUserId())) {
                queryList.add(String.format("wechat_user_id:'%s'", query.getWechatUserId()));
            }
            if (Objects.nonNull(query.getUserId())) {
                queryList.add(String.format("user_id:'%s'", query.getUserId()));
            }

            if (CollectionUtils.isNotEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            List<String> filterList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("gmt_modified", Order.DECREASE));
            searchParams.setSort(sorter);

            PageInfo<CustomizerRelatedRecordDTO> pageInfo = getResultItems(query.getPageNum(), query.getPageSize(), searcherClient.execute(searchParams), this::convert2CustomizerRelatedRecord);
            return pageInfo;
        } catch (Exception e) {
            PlatformLogUtil.logInfo("Opensearch查询商品信息异常", query);
        }

        return PageUtils.getPageInfo(res, a -> a);
    }

    private CustomizerRelatedRecordDTO convert2CustomizerRelatedRecord(JSONObject jsonObject) {
        if (MapUtils.isEmpty(jsonObject)) {
            return null;
        }
        CustomizerRelatedRecordDTO customizerRelatedRecordDTO = new CustomizerRelatedRecordDTO();
        customizerRelatedRecordDTO.setId(MapUtils.getLong(jsonObject, "id"));

        String destCityName = MapUtils.getString(jsonObject, "dest_city_name");
        if (StringUtils.isNotBlank(destCityName)) {
            List<String> destCityNames = Arrays.stream(destCityName.split("\t")).collect(Collectors.toList());
            customizerRelatedRecordDTO.setDestCityName(Joiner.on(",").join(destCityNames));
        }

        String destCityCode = MapUtils.getString(jsonObject, "dest_city_code");
        if (StringUtils.isNotBlank(destCityCode)) {
            List<String> destCityCodes = Arrays.stream(destCityCode.split("\t")).collect(Collectors.toList());
            customizerRelatedRecordDTO.setDestCityName(Joiner.on(",").join(destCityCodes));
        }

        customizerRelatedRecordDTO.setOriginCityName(MapUtils.getString(jsonObject, "origin_city_name"));
        customizerRelatedRecordDTO.setOriginCityCode(MapUtils.getString(jsonObject, "origin_city_code"));
        customizerRelatedRecordDTO.setState(MapUtils.getString(jsonObject, "state"));
        customizerRelatedRecordDTO.setWechatUserId(MapUtils.getLong(jsonObject, "wechat_user_id"));
        customizerRelatedRecordDTO.setExtInfo(MapUtils.getString(jsonObject, "ext_info"));
        customizerRelatedRecordDTO.setUserId(MapUtils.getLong(jsonObject, "user_id"));
        customizerRelatedRecordDTO.setUnionId(MapUtils.getString(jsonObject, "union_id"));
        customizerRelatedRecordDTO.setSendMsgFlag(MapUtils.getInteger(jsonObject, "send_msg_flag"));
        customizerRelatedRecordDTO.setWxUserId(MapUtils.getString(jsonObject, "wx_user_id"));

        return customizerRelatedRecordDTO;
    }

    private <R> List<R> getResultItems(SearchResult result, Function<JSONObject, R> function) {
        List<R> res = new ArrayList<>();
        JSONObject obj = JSONObject.parseObject(result.getResult());
        if (!obj.containsKey(RESULT)) {
            return res;
        }
        JSONObject resultJo = obj.getJSONObject(RESULT);
        if (!resultJo.containsKey(ITEMS)) {
            return res;
        }
        JSONArray items = resultJo.getJSONArray(ITEMS);
        for (int i = 0; i < items.size(); i++) {
            res.add(function.apply(items.getJSONObject(i)));
        }
        return res;
    }
}