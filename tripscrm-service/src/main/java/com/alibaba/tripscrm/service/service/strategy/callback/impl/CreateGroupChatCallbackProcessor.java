package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.wechat.WechatGroupAutoOpenWorkManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationCreateParam;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatGroupPoolDTO;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.CreateGroupChatRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.AsyncCreateGroupChatProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.callback.TaskAsyncRequestCallbackProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupPoolService;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;


/**
 * 发起群聊【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CreateGroupChatCallbackProcessor implements ProxyCallbackProcessor {
    private final WebSocketFactory webSocketFactory;
    private final ChatConversationService chatConversationService;
    private final WechatGroupAutoOpenWorkManager wechatGroupAutoOpenWorkManager;
    private final WechatGroupPoolService wechatGroupPoolService;
    private final AsyncCreateGroupChatProcessor asyncCreateGroupChatProcessor;
    private final MetaqProducer metaqProducer;
    private final TaskAsyncRequestCallbackProcessor taskAsyncRequestCallbackProcessor;
    private final LdbTairManager ldbTairManager;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.CREATE_GROUP_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        processTaskExecuteResult(scrmCallbackMsg);
        processEventSource(scrmCallbackMsg);
        processFusionChat(scrmCallbackMsg);
        processAutoOpenWork(scrmCallbackMsg);
        processGroupPoolRefresh(scrmCallbackMsg);
        return true;
    }

    private void processGroupPoolRefresh(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            String requestId = scrmCallbackMsg.getRequestId();
            Object o = ldbTairManager.get(TairConstant.GROUP_POOL_REFRESH_ASYNC_CREATE_REQUEST_ID_PREFIX + requestId);
            if (Objects.isNull(o)) {
                return;
            }

            String userId = String.valueOf(o);
            String chatId = (String) JSONPath.read(scrmCallbackMsg.getContent(), "$.chatId");
            if (!StringUtils.hasText(userId) || !StringUtils.hasText(chatId)) {
                PlatformLogUtil.logFail("第三方群聊创建回调，处理群聊池刷新逻辑，数据异常", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            WechatGroupPoolDTO wechatGroupPoolDTO = new WechatGroupPoolDTO();
            wechatGroupPoolDTO.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            wechatGroupPoolDTO.setChatId(chatId);
            wechatGroupPoolDTO.setOwner(userId);
            if (wechatGroupPoolService.insertSelective(wechatGroupPoolDTO) < 0) {
                PlatformLogUtil.logFail("第三方群聊创建回调，处理群聊池刷新逻辑，插入群聊池数据失败", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("wechatUserId", wechatGroupPoolDTO.getOwner());
            metaqProducer.send(MQEnum.SCRM_SELLER_CORP_VALID_PLATFORM_WECHAT_USER_SYNC, null, null, jsonObject.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方群聊创建回调，处理群聊池刷新逻辑出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
            DingTalkApi.sendTaskMessage("【企微群聊池刷新】创建群聊回调处理失败");
        }
    }

    private void processAutoOpenWork(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            String chatId = (String) JSONPath.read(scrmCallbackMsg.getContent(), "$.chatId");
            wechatGroupAutoOpenWorkManager.openWorkGroup(scrmCallbackMsg.getPlatformCorpId(), chatId, null);
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方群聊创建回调，处理自动关注群聊逻辑出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private void processEventSource(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            String chatId = (String) JSONPath.read(scrmCallbackMsg.getContent(), "$.chatId");
            String requestId = scrmCallbackMsg.getRequestId();
            if (!StringUtils.hasText(chatId) || !StringUtils.hasText(requestId)) {
                return;
            }

            if (requestId.contains("_")) {
                requestId = requestId.split("_")[0];
            }

            JSONObject message = new JSONObject();
            message.put("result", scrmCallbackMsg.getResult());
            message.put("chatId", chatId);
            message.put("requestId", requestId);
            message.put("corpId", scrmCallbackMsg.getPlatformCorpId());
            // 给外部使用的metaq
            metaqProducer.send(MQEnum.ASYNC_CREATE_GROUP_RESULT, null, null, message.toJSONString());
            message.put("updateDetail", "isv_create");
            metaqProducer.send(MQEnum.WECHAT_GROUP_MEMBER_CHANGE, "", "isv_create", message.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方群聊创建回调，处理事件源逻辑出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private void processFusionChat(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            // 执行主动创建群聊的后置动作
            String chatId = (String) JSONPath.read(scrmCallbackMsg.getContent(), "$.chatId");
            WsEvent wsEvent = asyncCreateGroupChatProcessor.afterCreate(scrmCallbackMsg.getRequestId(), chatId);
            if (wsEvent == null) {
                PlatformLogUtil.logFail("处理创建群聊回调（聚合聊天）", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }
            // 校验回调是否成功
            if (!scrmCallbackMsg.getResult()) {
                PlatformLogUtil.logFail("处理创建群聊回调，结果为失败（聚合聊天）", LogListUtil.newArrayList(wsEvent, scrmCallbackMsg));
                wsEvent.setSuccess(false);
                wsEvent.setErrorMessage(scrmCallbackMsg.getMessage());
                webSocketFactory.pushMessageByDistributed(wsEvent);
                return;
            }

            PlatformLogUtil.logInfo("处理创建群聊回调，结果为成功（聚合聊天）", LogListUtil.newArrayList(wsEvent, scrmCallbackMsg));
            String userId = wsEvent.getUserId();
            CreateGroupChatRequest request = wsEvent.getData().toJavaObject(CreateGroupChatRequest.class);
            String groupName = request.getChatName();
            // 保存用户会话库
            ChatConversationCreateParam createParam = new ChatConversationCreateParam();
            createParam.setUserId(userId);
            createParam.setChatType(ChatTypeEnum.GROUP.getValue());
            createParam.setChatId(chatId);
            createParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            chatConversationService.createChatConversation(createParam);
            // 推送ws
            asyncCreateGroupChatProcessor.pushMessageByDistributed(wsEvent, chatId, groupName);
            // 删除记录创建成功的群聊
            asyncCreateGroupChatProcessor.deleteCreateGroupRecord(scrmCallbackMsg.getRequestId());
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方群聊创建回调，处理聚合聊天逻辑出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private void processTaskExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            taskAsyncRequestCallbackProcessor.process(scrmCallbackMsg);
        } catch (Exception e) {
            PlatformLogUtil.logException("第三方群聊创建回调，处理任务异步执行结果出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }
}
