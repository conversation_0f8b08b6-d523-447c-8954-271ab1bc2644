package com.alibaba.tripscrm.service.middleware.metaq.consumer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 聚合聊天metaQ消费者
 *
 * <AUTHOR>
 * @date 2023/9/28
 */
@Slf4j(topic = "FusionChatLog")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service(value = "fusionChatConsumer")
public class FusionChatConsumer implements MessageListenerConcurrently {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        try {
            for (MessageExt msg : msgs) {
                String msgBody = new String(msg.getBody());
                WsEvent wsEvent = JSONObject.parseObject(msgBody, WsEvent.class);
                PlatformLogUtil.logFail("接收聚合聊天消息", LogListUtil.newArrayList(msgBody));
                SpaceInfoThreadLocalUtils.setCorpId(wsEvent.getCorpId());
                webSocketFactory.pushMessageByLocalServer(wsEvent);
                SpaceInfoThreadLocalUtils.remove();
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("聚合聊天消息消费异常", e.getMessage(), e, LogListUtil.newArrayList());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
