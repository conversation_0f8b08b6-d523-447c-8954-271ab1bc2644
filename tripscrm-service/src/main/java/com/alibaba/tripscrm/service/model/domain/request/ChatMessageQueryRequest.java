package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import lombok.Data;

/**
 * 群聊消息查询参数
 *
 * <AUTHOR>
 * @since 2025/5/19 15:03
 */
@Data
public class ChatMessageQueryRequest {
    /**
     * 群聊Id
     */
    private String chatId;

    /**
     * 发送者Id
     */
    private String senderId;

    /**
     * 发送者类型（5：企微客户externalUserId，9：企微成员userId）
     */
    private Integer senderType;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 聊天记录条数（最多1000条，不填默认1000条）
     */
    private Integer messageCount;

    /**
     * 会话类型
     * @see ChatTypeEnum
     */
    private Integer chatType;

    /**
     * 企业id
     */
    private String corpId;
}
