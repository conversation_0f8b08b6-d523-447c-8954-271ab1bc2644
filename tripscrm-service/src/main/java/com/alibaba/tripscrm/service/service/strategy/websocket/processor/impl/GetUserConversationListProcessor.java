package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.query.fusionchat.ChatConversationListQuery;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.GetUserConversationListRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetUserConversationListResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetUserConversationResponseBody;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * 获取当前企微成员的会话列表 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class GetUserConversationListProcessor implements WsEventProcessor {
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private WebSocketFactory webSocketFactory;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.GET_USER_CONVERSATION_LIST;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        if (wsEvent.getUserId() == null) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        // 请求体构建
        GetUserConversationListRequest request = new GetUserConversationListRequest();
        if (wsEvent.getData() != null) {
            request = wsEvent.getData().toJavaObject(GetUserConversationListRequest.class);
        }
        ChatConversationListQuery query = new ChatConversationListQuery();
        query.setUserId(wsEvent.getUserId());
        query.setPageSize(request.getPageSize());
        query.setCorpId(Optional.ofNullable(wsEvent.getCorpId()).orElse(SpaceInfoThreadLocalUtils.getCorpId()));
        query.setChatIdList(request.getChatIdList());
        query.setChatType(request.getChatType());
        // 获取当前企微成员的会话列表
        GetUserConversationListResponse response = new GetUserConversationListResponse();
        response.setUserConversations(chatConversationService.listByParam(query));
        response.setCount(chatConversationService.countByParam(query));
        response.setAppend(request.getAppend());
        // 获取上下文
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        chatConversationService.cleanUserDoingChat(wsContextInfo.getAccount(), wsContextInfo.getSpaceId());
        // 推送到websocket
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        webSocketFactory.pushMessageBySession(session, wsEvent, false);
    }

    /**
     * 构建会话列表推送事件
     * @param userId 成员ID
     * @param response 响应结果
     * @return 会话事件
     */
    private WsEvent buildConversationListEvent(String userId, GetUserConversationListResponse response) {
        WsEvent wsEvent = new WsEvent();
        wsEvent.setType(WsEventTypeEnum.GET_USER_CONVERSATION_LIST.getValue());
        wsEvent.setUserId(userId);
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        return wsEvent;
    }

    public void pushMessageByDistributed(String userId) {
        // 获取当前企微成员的会话列表
        List<GetUserConversationResponseBody> chatConversations = chatConversationService.listByUserId(userId);
        GetUserConversationListResponse response = new GetUserConversationListResponse();
        response.setUserConversations(chatConversations);
        // 推送到websocket
        webSocketFactory.pushMessageByDistributed(buildConversationListEvent(userId, response));
    }

    /**
     * 推送指定会话列表
     * @param userId 成员ID
     * @param chatIdList 聊天ID
     */
    public void pushSpecifyConversationList(String userId, String corpId, ChatTypeEnum chatTypeEnum, List<String> chatIdList) {
        // 数据查询
        ChatConversationListQuery query = new ChatConversationListQuery();
        query.setUserId(userId);
        query.setCorpId(corpId);
        query.setChatIdList(chatIdList);
        query.setChatType(chatTypeEnum.getValue());
        List<GetUserConversationResponseBody> conversationList = chatConversationService.listByParam(query);
        // 数据组装
        GetUserConversationListResponse response = new GetUserConversationListResponse();
        response.setUserConversations(conversationList);
        // 推送动作
        webSocketFactory.pushMessageByDistributed(buildConversationListEvent(userId, response));
    }

}
