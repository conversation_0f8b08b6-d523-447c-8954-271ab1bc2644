package com.alibaba.tripscrm.service.manager.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.csp.sentinel.adapter.metaq.SentinelMetaRegistry;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.tripscrm.dal.model.domain.data.EventSourceDO;
import com.alibaba.tripscrm.service.middleware.metaq.consumer.task.EventSourceMessageListener;
import com.alibaba.tripscrm.service.service.task.EventSourceService;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.metaq.client.MetaPushConsumer;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;

/**
 * 事件源管理器
 *
 * <AUTHOR>
 * @date 2023-12-03 17:03:15
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class EventSourceManager {
    private final EventSourceService eventSourceService;
    private final EventSourceMessageListener eventSourceMessageListener;

    // volatile 保证可见性
    private volatile EventSourceInfo eventSourceInfo;

    /**
     * 设置定时任务每20000毫秒（即20秒）刷新一次事件源信息
     */
    @Scheduled(fixedRate = 20000)
    public void refreshEventSourceInfo() {
        // 还没创建出来，不执行，等待 init 方法执行后再执行刷新策略
        if (Objects.isNull(eventSourceInfo)) {
            return;
        }

        try {
            PlatformLogUtil.logFail("start");
            refresh();
            PlatformLogUtil.logFail("end");
        } catch (MQClientException e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    /**
     * 初始化事件源信息，并创建MetaQ-Consumer
     */
    @PostConstruct()
    public void init() {
        try {
            PlatformLogUtil.logFail("start");
            refresh();
            PlatformLogUtil.logFail("end");
        } catch (MQClientException e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            throw new RuntimeException(e);
        }
    }

    @PreDestroy()
    public synchronized void destroy() {
        if (Objects.isNull(eventSourceInfo)) {
            return;
        }

        PlatformLogUtil.logFail("start");
        eventSourceInfo.getMetaPushConsumerMap().forEach((key, value) -> {
            try {
                PlatformLogUtil.logFail("shutdown", LogListUtil.newArrayList(key));
                value.shutdown();
            } catch (Exception e) {
                PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            }
        });
        PlatformLogUtil.logFail("end");
    }

    /**
     * 该方法不能并发执行
     */
    public synchronized void refresh() throws MQClientException {
        // consumer延迟启动，等待事件源数据初始化完成
        List<BooleanSupplier> supplierList = new ArrayList<>();

        // 从 DB 中查询最新事件源信息
        List<EventSourceDO> eventSourceList = eventSourceService.selectAll();
        // 有效性判断
        eventSourceList = eventSourceList.stream().filter(this::checkEventSourceValid).collect(Collectors.toList());

        EventSourceInfo newEventSourceInfo = new EventSourceInfo();
        Map<String, List<EventSourceDO>> newTopic2EventSourceList = new HashMap<>();
        Map<String, MetaPushConsumer> newMetaPushConsumerMap = new HashMap<>();
        newEventSourceInfo.setMetaPushConsumerMap(newMetaPushConsumerMap);
        newEventSourceInfo.setTopic2EventSourceList(newTopic2EventSourceList);

        for (EventSourceDO eventSourceDO : eventSourceList) {
            newTopic2EventSourceList.putIfAbsent(eventSourceDO.getTopic(), new ArrayList<>());
            newTopic2EventSourceList.get(eventSourceDO.getTopic()).add(eventSourceDO);

            // consumer 已经创建出来了
            if (Objects.nonNull(eventSourceInfo) && eventSourceInfo.getMetaPushConsumerMap().containsKey(eventSourceDO.getTopic())) {
                newMetaPushConsumerMap.put(eventSourceDO.getTopic(), eventSourceInfo.getMetaPushConsumerMap().get(eventSourceDO.getTopic()));
                continue;
            }

            MetaPushConsumer consumer = createConsumer(eventSourceDO);
            newMetaPushConsumerMap.put(eventSourceDO.getTopic(), consumer);

            // 延迟启动
            supplierList.add(() -> {
                try {
                    consumer.start();
                    PlatformLogUtil.logFail("startConsumer", LogListUtil.newArrayList(eventSourceDO.getTopic(), consumer.getConsumerGroup()));
                } catch (MQClientException e) {
                    PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
                    throw new RuntimeException(e);
                }
                return true;
            });
        }

        // 销毁无效的 consumer
        if (Objects.nonNull(eventSourceInfo)) {
            for (String topic : eventSourceInfo.getMetaPushConsumerMap().keySet()) {
                if (newMetaPushConsumerMap.containsKey(topic)) {
                    continue;
                }
                eventSourceInfo.getMetaPushConsumerMap().get(topic).shutdown();
            }
        }

        // 更新事件源数据
        eventSourceInfo = newEventSourceInfo;
        // 启动 consumer
        supplierList.forEach(BooleanSupplier::getAsBoolean);
    }

    public List<EventSourceDO> getEventSourceList(String topic) {
        if (Objects.isNull(eventSourceInfo)) {
            return new ArrayList<>();
        }

        return eventSourceInfo.getTopic2EventSourceList().getOrDefault(topic, new ArrayList<>());
    }

    public boolean checkEventSourceValid(EventSourceDO eventSourceDO) {
        if (Objects.isNull(eventSourceDO)) {
            return false;
        }

        // 已被删除
        if (Objects.equals((byte) 1, eventSourceDO.getIsDeleted())) {
            return false;
        }

        // 编辑中
        if (Objects.equals((byte) 0, eventSourceDO.getStatus())) {
            return false;
        }

        // 已过期
        return !eventSourceDO.getExpireTime().before(new Date());
    }

    private MetaPushConsumer createConsumer(EventSourceDO eventSourceDO) throws MQClientException {
        MetaPushConsumer consumer = new MetaPushConsumer(eventSourceDO.getConsumerId());
        String subExpression = StringUtils.hasText(eventSourceDO.getMessageTag()) ? eventSourceDO.getMessageTag() : "*";
        consumer.subscribe(eventSourceDO.getTopic(), subExpression);
        consumer.registerMessageListener(eventSourceMessageListener);
        SentinelMetaRegistry.registerConsumerInterceptor(consumer);
        PlatformLogUtil.logFail("startConsumer", LogListUtil.newArrayList(eventSourceDO.getTopic(), subExpression));
        return consumer;
    }

    @AteyeInvoker(description = "查询事件源信息")
    public String getTopic2EventSourceList() {
        Map<String, List<EventSourceDO>> topic2EventSourceList = eventSourceInfo.getTopic2EventSourceList();

        return topic2EventSourceList.entrySet().stream().map(entry -> {
            String topic = entry.getKey();
            List<EventSourceDO> eventSourceDOList = entry.getValue();
            return topic + ":" + eventSourceDOList.stream().map(EventSourceDO::getName).collect(Collectors.joining(","));
        }).collect(Collectors.joining("\n"));
    }

    /**
     * 事件源信息
     */
    @Data
    public static class EventSourceInfo {
        private Map<String, List<EventSourceDO>> topic2EventSourceList;
        private Map<String, MetaPushConsumer> metaPushConsumerMap;
    }
}
