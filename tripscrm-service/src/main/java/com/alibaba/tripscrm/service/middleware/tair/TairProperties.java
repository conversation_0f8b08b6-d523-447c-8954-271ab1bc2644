package com.alibaba.tripscrm.service.middleware.tair;

import org.springframework.stereotype.Component;

/**
 * tair的例子，直接注入TairManager，通过它进行tair的put和get操作。详情见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tair
 *
 * <AUTHOR>
 */
@Component
public class TairProperties {

    //private static final int NAME_SPACE = 1;
    //
    //private static final String SUCCESS = "success";
    //
    //@Autowired
    //private TairManager tairManager;
    //
    //public boolean put(String key, String value) {
    //    // tair的put操作，返回的ResultCode包括操作是否成功的信息
    //    ResultCode code = tairManager.put(NAME_SPACE, key, value);
    //    return SUCCESS.equals(code.getMessage());
    //}
    //
    //public String get(String key) {
    //    Result<DataEntry> result = tairManager.get(NAME_SPACE, key);
    //    return (String) result.getValue().getValue();
    //}
    //
    //public boolean delete(String key) {
    //    // tair的delete操作，返回的ResultCode包括操作是否成功的信息
    //    ResultCode code = tairManager.delete(NAME_SPACE, key);
    //    if (SUCCESS.equals(code.getMessage())) {
    //        return true;
    //    } else {
    //        return false;
    //    }
    //}
}