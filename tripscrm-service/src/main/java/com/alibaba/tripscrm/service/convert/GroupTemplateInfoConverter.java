package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.GroupTemplateInfoDO;
import com.alibaba.tripscrm.service.model.dto.group.GroupTemplateInfoDTO;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/14 11:37
 * @Filename：GroupTemplateInfoConverter
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupTemplateInfoConverter {

    public GroupTemplateInfoDO convert2DO(GroupTemplateInfoDTO groupTemplateInfo) {
        GroupTemplateInfoDO groupTemplateInfoDO = new GroupTemplateInfoDO();
        groupTemplateInfoDO.setId(groupTemplateInfo.getId());
        groupTemplateInfoDO.setDeleted(groupTemplateInfo.getDeleted());
        groupTemplateInfoDO.setGroupTemplateId(groupTemplateInfo.getGroupTemplateId());
        groupTemplateInfoDO.setGroupName(groupTemplateInfo.getGroupName());
        groupTemplateInfoDO.setAdminInfo(groupTemplateInfo.getAdminInfo());
        groupTemplateInfoDO.setCorpId(groupTemplateInfo.getCorpId());
        groupTemplateInfoDO.setPlatformType(groupTemplateInfo.getPlatformType().getCode().byteValue());
        groupTemplateInfoDO.setExtraInfo(groupTemplateInfo.getExtraInfo());
        return groupTemplateInfoDO;
    }
    public GroupTemplateInfoDTO convert2DTO(GroupTemplateInfoDO groupTemplateInfoDO) {
        GroupTemplateInfoDTO groupTemplateInfoDTO = new GroupTemplateInfoDTO();
        groupTemplateInfoDTO.setId(groupTemplateInfoDO.getId());
        groupTemplateInfoDTO.setDeleted(groupTemplateInfoDO.getDeleted());
        groupTemplateInfoDTO.setGroupTemplateId(groupTemplateInfoDO.getGroupTemplateId());
        groupTemplateInfoDTO.setGroupName(groupTemplateInfoDO.getGroupName());
        groupTemplateInfoDTO.setAdminInfo(groupTemplateInfoDO.getAdminInfo());
        groupTemplateInfoDTO.setCorpId(groupTemplateInfoDO.getCorpId());
        groupTemplateInfoDTO.setPlatformType(PlatformTypeEnum.valueOf(groupTemplateInfoDO.getPlatformType()));
        groupTemplateInfoDTO.setExtraInfo(groupTemplateInfoDO.getExtraInfo());
        return groupTemplateInfoDTO;
    }
}
