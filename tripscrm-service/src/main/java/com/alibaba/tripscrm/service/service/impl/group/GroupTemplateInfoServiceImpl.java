package com.alibaba.tripscrm.service.service.impl.group;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.GroupTemplateInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.GroupTemplateInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.GroupTemplateInfoQuery;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.convert.GroupTemplateInfoConverter;
import com.alibaba.tripscrm.service.model.domain.query.GroupTemplateQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.dto.group.GroupTemplateInfoDTO;
import com.alibaba.tripscrm.service.service.group.GroupTemplateInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/13 18:58
 * @Filename：GroupTemplateInfoServiceImpl
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupTemplateInfoServiceImpl implements GroupTemplateInfoService {
    private final GroupTemplateInfoMapper groupTemplateInfoMapper;
    private final GroupTemplateInfoConverter groupTemplateInfoConverter;

    /**
     * 新增群组模板
     *
     * @param groupTemplateInfoDTO
     * @return
     */
    @Override
    public Integer insertSelective(GroupTemplateInfoDTO groupTemplateInfoDTO) {
        if (Objects.isNull(groupTemplateInfoDTO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (!StringUtils.hasText(groupTemplateInfoDTO.getGroupTemplateId()) || !StringUtils.hasText(groupTemplateInfoDTO.getGroupName())
                || !StringUtils.hasText(groupTemplateInfoDTO.getCorpId()) || !StringUtils.hasText(groupTemplateInfoDTO.getGroupTemplateId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return groupTemplateInfoMapper.insertSelective(groupTemplateInfoConverter.convert2DO(groupTemplateInfoDTO));
    }

    /**
     * 查询群组模板
     *
     * @param query
     * @return
     */
    @Override
    public List<GroupTemplateInfoDTO> select(GroupTemplateQuery query) {
        GroupTemplateInfoQuery param = buildParam(query);
        List<GroupTemplateInfoDO> groupTemplateInfoDOS = groupTemplateInfoMapper.selectByParamWithBLOBs(param);
        if (CollectionUtils.isEmpty(groupTemplateInfoDOS)) {
            return Collections.emptyList();
        }
        return groupTemplateInfoDOS.stream().map(groupTemplateInfoConverter::convert2DTO).collect(Collectors.toList());
    }

    /** 
     * 分页查询群组模板
     *
     * @param query
     * @return
     */
    @Override
    public PageInfoDTO<GroupTemplateInfoDTO> pageQuery(GroupTemplateQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        
        // 构建查询参数（包含分页参数）
        GroupTemplateInfoQuery param = buildParam(query);
        
        // 查询总数
        long totalCount = groupTemplateInfoMapper.countByParam(param);
        
        // 分页查询数据
        List<GroupTemplateInfoDO> groupTemplateInfoDOS = groupTemplateInfoMapper.selectByParamWithBLOBs(param);
        
        // 转换为DTO列表
        List<GroupTemplateInfoDTO> dtoList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(groupTemplateInfoDOS)) {
            dtoList = groupTemplateInfoDOS.stream()
                    .map(groupTemplateInfoConverter::convert2DTO)
                    .collect(Collectors.toList());
        }
        
        // 构建分页结果
        PageInfoDTO<GroupTemplateInfoDTO> pageInfo = new PageInfoDTO<>();
        pageInfo.setTotal(totalCount);
        pageInfo.setPageNum(query.getPageNum());
        pageInfo.setPageSize(query.getPageSize());
        pageInfo.setSize(dtoList.size());
        pageInfo.setPages((int) Math.ceil((double) totalCount / query.getPageSize()));
        pageInfo.setHasNextPage(query.getPageNum() < pageInfo.getPages());
        pageInfo.setList(dtoList);
        
        return pageInfo;
    }

    /**
     * 更新群组模板
     *
     * @param groupTemplateInfoDTO
     * @return
     */
    @Override
    public Integer updateSelective(GroupTemplateInfoDTO groupTemplateInfoDTO) {
        if (Objects.isNull(groupTemplateInfoDTO) || !NumberUtils.validLong(groupTemplateInfoDTO.getId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (!StringUtils.hasText(groupTemplateInfoDTO.getGroupTemplateId()) || !StringUtils.hasText(groupTemplateInfoDTO.getGroupName())
                || !StringUtils.hasText(groupTemplateInfoDTO.getCorpId()) || !StringUtils.hasText(groupTemplateInfoDTO.getGroupTemplateId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return groupTemplateInfoMapper.updateByPrimaryKeySelective(groupTemplateInfoConverter.convert2DO(groupTemplateInfoDTO));
    }

    /**
     * 删除群组模板
     *
     * @param query
     * @return
     */
    @Override
    public Integer delete(GroupTemplateQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        GroupTemplateInfoQuery param = buildParam(query);
        return groupTemplateInfoMapper.deleteByParam(param);
    }

    private static GroupTemplateInfoQuery buildParam(GroupTemplateQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        GroupTemplateInfoQuery param = new GroupTemplateInfoQuery();
        GroupTemplateInfoQuery.Criteria criteria = param.or();

        if (NumberUtils.validLong(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }

        if(!CollectionUtils.isEmpty(query.getIdList())) {
            criteria.andIdIn(query.getIdList());
        }

        if (StringUtils.hasText(query.getTemplateNameLike())) {
            criteria.andGroupNameLike('%' + query.getTemplateNameLike() + '%');
        }
        if(Objects.nonNull(query.getPlatformType())) {
            criteria.andPlatformTypeEqualTo(query.getPlatformType().getCode().byteValue());
        }

        if (StringUtils.hasText(query.getGroupTemplateId())) {
            criteria.andGroupTemplateIdEqualTo(query.getGroupTemplateId());
        }
        if(!CollectionUtils.isEmpty(query.getGroupTemplateIdList())) {
            criteria.andGroupTemplateIdIn(query.getGroupTemplateIdList());
        }
        if (StringUtils.hasText(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }
        if(!CollectionUtils.isEmpty(query.getCorpIdList())) {
            criteria.andCorpIdIn(query.getCorpIdList());
        }
        // 默认未删除,若传入就使用传入
        if(NumberUtils.validInteger(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted().byteValue());
        }
        
        // 设置分页参数
        if (NumberUtils.validInteger(query.getPageNum()) && NumberUtils.validInteger(query.getPageSize())) {
            param.setPage(true);
            param.setPageStart(query.getPageNum());
            param.setPageSize(query.getPageSize());
        }
        
        return param;
    }

}
