package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 私聊群发任务配置处理器
 *
 * <AUTHOR>
 * @date 2023-12-30 15:26:28
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SingleChatGroupMsgTaskConfigProcessor extends AbstractTaskConfigProcessor {
    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.SINGLE_CHAT_GROUP_MSG;
    }
}
