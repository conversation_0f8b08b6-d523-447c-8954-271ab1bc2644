package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import java.util.Arrays;
import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.CustomerAddWayEnum;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerChangeThirdPartCallbackProcessor implements ProxyCallbackProcessor {

    @Resource
    private MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.UPDATE_CUSTOMER_CALLBACK_THIRD_PART;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        // 目前只处理添加好友消息
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        if (!content.containsKey(CallbackConstant.CHANGE_TYPE) || !content.containsKey(CallbackConstant.EVENT)) {
            return true;
        }
        processAddCustomer(content, scrmCallbackMsg.getPlatformCorpId());
        return true;
    }

    private void processAddCustomer(JSONObject content, String platformCorpId) {
        try {
            String changeType = content.getString(CallbackConstant.CHANGE_TYPE);
            // 目前只处理添加好友消息
            if (!Lists.newArrayList("add_contact_with_fill_state").contains(changeType)) {
                return;
            }

            String userId = content.getString(CallbackConstant.USER_ID);
            String externalUserId = content.getString(CallbackConstant.EXTERNAL_USER_ID);
            String taoId = content.getString(CallbackConstant.TAO_ID);
            String unionId = content.getString(CallbackConstant.UNION_ID);
            String state = content.containsKey(CallbackConstant.STATE) ? content.getString(CallbackConstant.STATE) : "";
            String welcomeCode = content.containsKey(CallbackConstant.WELCOME_CODE) ? content.getString(CallbackConstant.WELCOME_CODE) : "";
            Integer addWay = content.containsKey(CallbackConstant.ADD_WAY) ? content.getInteger(CallbackConstant.ADD_WAY) : 0;
            CustomerAddWayEnum customerAddWayEnum = Arrays.stream(CustomerAddWayEnum.values()).filter(e -> Objects.equals(e.getCode(), addWay)).findAny().orElse(null);
            JSONObject message = new JSONObject();
            message.put("userId", userId);
            message.put("externalUserId", externalUserId);
            message.put("taoId", taoId);
            message.put("unionId", unionId);
            message.put("changeType", changeType);
            message.put("state", state);
            message.put("welcomeCode", welcomeCode);
            message.put(TaskConstant.CORP_ID, platformCorpId);
            // 继承好友不触发
            if (Lists.newArrayList(CustomerAddWayEnum.SHARED_BY_MEMBER, CustomerAddWayEnum.ADMIN_ASSIGNMENT).contains(customerAddWayEnum)) {
                return;
            }
            // 继承好友不触发
            if (Lists.newArrayList(CustomerAddWayEnum.SHARED_BY_MEMBER, CustomerAddWayEnum.ADMIN_ASSIGNMENT, CustomerAddWayEnum.UN_KNOWN).contains(customerAddWayEnum)) {
                return;
            }

            message.put("addWay", addWay);
            // 发送添加客户好友消息
            metaqProducer.send(MQEnum.WECHAT_ADD_CUSTOMER_THIRD_PART, "", changeType, message.toJSONString(), MetaQDeleyLevel.LEVEL_5.getLevel());
        } catch (Exception e) {
            PlatformLogUtil.logException("添加客户回调，处理添加客户逻辑出错", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

}
