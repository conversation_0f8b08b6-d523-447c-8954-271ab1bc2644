package com.alibaba.tripscrm.service.service.strategy.sop;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterialTemplateInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialTemplateInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.convert.TagConverter;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskAbTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.activity.GroupActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTemplateService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.AbTestBucketService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.fliggy.pokemon.common.enums.TimeUnitEnum;
import com.fliggy.pokemon.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.convert.TaskConverter.generateCron;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.SINGLE_EXECUTE;

/**
 * 群活码SOP
 *
 * <AUTHOR>
 * @date 2023-12-30 14:50:08
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ForwardMessageGroupActivitySop extends AbstractActivitySop<GroupActivitySopConfigVO> {
    private final MaterialService materialService;
    private final TaskService taskService;
    private final TagInfoService tagInfoService;
    private final ActivityContextService activityContextService;
    private final MaterialTemplateService materialTemplateService;
    private final AbTestBucketService abTestService;
    private final ActivityInfoService activityInfoService;
    private final WechatGroupService wechatGroupService;
    private final TagConverter tagConverter;


    @Override
    protected void checkConfigValid(GroupActivitySopConfigVO config, Boolean isUpdate) {

        if (CollectionUtils.isEmpty(config.getTaskInfoList())) {
            throw new TripscrmException(TripSCRMErrorCode.TASK_LIST_EMPTY);
        }

        if (CollectionUtils.isEmpty(config.getScrmIncludeTagIdList()) && CollectionUtils.isEmpty(config.getWechatJoinGroupActivityIdList()) && CollectionUtils.isEmpty(config.getWechatChatIdList())) {
            throw new TripscrmException(TripSCRMErrorCode.GROUP_TAG_EMPTY);
        }

        for (TaskVO taskInfo : config.getTaskInfoList()) {
            TaskTriggerTypeEnum triggerTypeEnum = TaskTriggerTypeEnum.getByCode(taskInfo.getTriggerType());
            if (Objects.isNull(triggerTypeEnum)) {
                taskInfo.setTargetType(Integer.valueOf(SINGLE_EXECUTE.getCode()));
            }

            checkTaskMaterial(taskInfo);
        }

    }

    private void checkTaskMaterial(TaskVO taskVO) {
        // 没有配置素材
        if (Optional.ofNullable(taskVO.getMaterialId()).orElse(-1L) == -1L) {
            taskVO.setMaterialId(-1L);
            return;
        }

        MaterailInfoDO materailInfoDO = materialService.queryById(taskVO.getMaterialId());
        // 素材不存在
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("素材不存在", LogListUtil.newArrayList(taskVO));
            throw new TripscrmException(TripSCRMErrorCode.MATERIAL_NOT_EXIST);
        }

        MaterialTemplateInfoQuery query = new MaterialTemplateInfoQuery();
        query.setId(materailInfoDO.getMaterialTemplateId());
        List<MaterialTemplateInfoDO> templateInfoDOList = materialTemplateService.list(query);
        if (CollectionUtils.isEmpty(templateInfoDOList)) {
            PlatformLogUtil.logFail("素材类型不存在", LogListUtil.newArrayList(taskVO));
            throw new TripscrmException(TripSCRMErrorCode.MATERIAL_TYPE_ILLEGAL);
        }
        //转发消息素材校验有效期
        if (Objects.equals(TaskType.FORWARD_MESSAGE, TaskType.getByCode(templateInfoDOList.get(0).getType()))) {
            Date taskTime = new Date(Long.valueOf(taskVO.getTriggerTimeValue()));
            Date materailEndTime = DateUtils.dateAddTime(materailInfoDO.getGmtCreate(), TimeUnitEnum.DAY, 1);
            long l = DateUtils.dateSubtraction(taskTime, materailEndTime, TimeUnitEnum.MINUTE);
            if (l < SwitchConfig.CHAT_HISTORY_VALIDITY_MINUTE) {
                PlatformLogUtil.logFail("转发消息素材有效期不足10分钟", LogListUtil.newArrayList(taskVO));
                throw new TripscrmException(TripSCRMErrorCode.ATERIAL_VALIDITY_LESS_THAN_TEN_MINUTES);
            }
        }

    }

    @Override
    protected void createTaskList(GroupActivitySopConfigVO config, Long activityId) {
        List<TaskVO> taskInfoList = config.getTaskInfoList();
        for (TaskVO taskInfo : taskInfoList) {
            if (Objects.equals(taskInfo.getTaskTypeCode(), TaskType.GROUP_CHAT_MESSAGE.getCode())) {
                // 创建群聊任务
                createGroupChatTask(taskInfo, activityId, config);
                continue;
            }
            // 1.创建转发消息到群
            Long subTaskId = createForwardMessageToChatTask(taskInfo, activityId, config);
            // 2.创建转发消息到群管理员任务
            createForwardMessageToManagerTask(taskInfo, activityId, config, subTaskId);

        }
        updateActivityContext(activityId, config);
    }

    private void updateActivityContext(Long activityId, GroupActivitySopConfigVO config) {
        ActivityInfoDO activityInfoDO = activityInfoService.queryInfoById(activityId);
        activityInfoDO.setContext(JSONObject.toJSONString(config));
        Integer update = activityInfoService.update(activityInfoDO);
        if (update <= 0) {
            PlatformLogUtil.logFail("更新活动上下文失败", LogListUtil.newArrayList(activityInfoDO));
            throw new TripscrmException(TripSCRMErrorCode.GROUP_SOP_UPDATE_ACTIVITY_CONTEXT_FAIL);
        }
    }

    private void createForwardMessageToManagerTask(TaskVO taskInfo, Long activityId, GroupActivitySopConfigVO config, Long subTaskId) {

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.FORWARD_MESSAGE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setMaterialId(taskInfo.getMaterialId());
        //如果是AB实验
        if (Objects.nonNull(TaskAbTypeEnum.of(taskInfo.getAbType().intValue()))) {
            taskInfoDO.setMaterialId(null);
        }
        taskInfoDO.setTriggerType(Integer.parseInt(SINGLE_EXECUTE.getCode()));
        JSONObject extJs = StringUtils.hasText(taskInfoDO.getExtInfo()) ? JSONObject.parseObject(taskInfoDO.getExtInfo()) : new JSONObject();
        if (Objects.isNull(taskInfo.getTriggerType())) {
            taskInfo.setTriggerType(Integer.parseInt(SINGLE_EXECUTE.getCode()));
        }
        Map<String, Object> map = new HashMap<>();
        map.put(TaskConstant.SUB_TASK_ID, subTaskId);
        taskInfoDO.setConfig(JSONObject.toJSONString(map));
        // 时间表达式
        taskInfoDO.setTriggerTimeCron(generateCron(taskInfo));
        extJs.put("triggerTimeValue", taskInfo.getTriggerTimeValue());
        // 生效时间
        Long triggerTime = Long.valueOf(taskInfo.getTriggerTimeValue());
        Date currDateStart = org.apache.commons.lang.time.DateUtils.truncate(new Date(triggerTime), Calendar.DATE);
        Date currDateEnd = com.alibaba.tripscrm.service.util.system.DateUtils.addDays(currDateStart, 1);
        taskInfoDO.setEffectStartTime(currDateStart);
        taskInfoDO.setEffectEndTime(currDateEnd);

        // 任务开始结束时间
        String executeStartTime = taskInfo.getExecuteStartTime();
        String executeEndTime = taskInfo.getExecuteEndTime();
        taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);

        // AB
        taskInfoDO.setAbType(Objects.isNull(taskInfo.getAbType()) ? (byte) 0 : taskInfo.getAbType());

        // 环境
        taskInfoDO.setEnv(EnvUtils.getEnvironment());

        if (!CollectionUtils.isEmpty(config.getScrmIncludeTagIdList())) {
            extJs.put(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST, config.getScrmIncludeTagIdList());
        }
        if (!CollectionUtils.isEmpty(config.getWechatJoinGroupActivityIdList())) {
            extJs.put(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, config.getWechatJoinGroupActivityIdList());
        }
        if (!CollectionUtils.isEmpty(config.getWechatChatIdList())) {
            extJs.put(TaskConstant.WECHAT_CHAT_ID_LIST, config.getWechatChatIdList());
        }

        // 最后将extInfo设置到模型里
        taskInfoDO.setExtInfo(extJs.toJSONString());

        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_FORWARD_MESSAGE_TO_ADMIN_FAIL);
        }

        TaskInfoDO existTaskInfoDO = taskService.queryTaskByNameAndSpaceId(taskInfoDO.getName(), taskInfoDO.getSpaceId());
        //存活动上下文
        processActivityContext(taskInfoDO);

        taskInfo.setId(existTaskInfoDO.getId());
        if (Objects.isNull(taskInfo.getAbType()) || Objects.isNull(TaskAbTypeEnum.of(taskInfo.getAbType().intValue()))) {
            return;
        }
        //AB分桶
        ABTask(existTaskInfoDO, taskInfo);
    }

    private Long createForwardMessageToChatTask(TaskVO taskInfo, Long activityId, GroupActivitySopConfigVO config) {

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.FORWARD_MESSAGE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        String taskName = taskInfoDO.getName();
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(com.alibaba.tripscrm.service.util.system.DateUtils.addDays(new Date(), 7));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.ForwardMessage2GroupAdminCallbackEventSourceId);
        taskInfoDO.setMaterialId(taskInfo.getMaterialId());

        //如果是AB实验
        if (Objects.nonNull(TaskAbTypeEnum.of(taskInfo.getAbType().intValue()))) {
            taskInfoDO.setMaterialId(null);
        }
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            PlatformLogUtil.logFail("创建转发群内消息任务失败", LogListUtil.newArrayList(taskInfoDO));
            throw new TripscrmException(TripSCRMErrorCode.CREATE_FORWARD_MESSAGE_TO_GROUP_FAIL);
        }
        return taskInfoDO.getId();
    }

    private Long processActivityContext(TaskInfoDO taskInfoDO) {
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        Long contextId = configJson.containsKey("contextId") ? configJson.getLong("contextId") : activityContextService.generateContextId();
        // 插入/更新活动上下文信息（targetType = 活动id类型）
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(contextId);
        activityTaskInfoBO.setActivityId(taskInfoDO.getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_TASK_ID);
        activityTaskInfoBO.setTargetId(String.valueOf(taskInfoDO.getId()));
        JSONObject config = JSONObject.parseObject(taskInfoDO.getConfig());
        if (config.containsKey(TaskConstant.SUB_TASK_ID)) {
            activityTaskInfoBO.setTargetId(config.getString(TaskConstant.SUB_TASK_ID));
        }
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("dataTime", System.currentTimeMillis());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);

        PlatformLogUtil.logInfo("群聊sop，转发消息任务,活动上下文处理", LogListUtil.newArrayList(activityTaskInfoBO));

        return contextId;
    }


    private void createGroupChatTask(TaskVO taskInfo, Long activityId, GroupActivitySopConfigVO config) {

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.GROUP_CHAT_MESSAGE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setMaterialId(taskInfo.getMaterialId());
        //如果是AB实验
        if (Objects.nonNull(TaskAbTypeEnum.of(taskInfo.getAbType().intValue()))) {
            taskInfoDO.setMaterialId(null);
        }
        taskInfoDO.setTriggerType(Integer.parseInt(SINGLE_EXECUTE.getCode()));
        JSONObject extJs = StringUtils.hasText(taskInfoDO.getExtInfo()) ? JSONObject.parseObject(taskInfoDO.getExtInfo()) : new JSONObject();
        if (Objects.isNull(taskInfo.getTriggerType())) {
            taskInfo.setTriggerType(Integer.parseInt(SINGLE_EXECUTE.getCode()));
        }
        // 时间表达式
        taskInfoDO.setTriggerTimeCron(generateCron(taskInfo));
        extJs.put("triggerTimeValue", taskInfo.getTriggerTimeValue());
        // 生效时间
        Long triggerTime = Long.valueOf(taskInfo.getTriggerTimeValue());
        Date currDateStart = org.apache.commons.lang.time.DateUtils.truncate(new Date(triggerTime), Calendar.DATE);
        Date currDateEnd = com.alibaba.tripscrm.service.util.system.DateUtils.addDays(currDateStart, 1);
        taskInfoDO.setEffectStartTime(currDateStart);
        taskInfoDO.setEffectEndTime(currDateEnd);

        // 任务开始结束时间
        String executeStartTime = taskInfo.getExecuteStartTime();
        String executeEndTime = taskInfo.getExecuteEndTime();
        taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);

        // AB
        taskInfoDO.setAbType(Objects.isNull(taskInfo.getAbType()) ? (byte) 0 : taskInfo.getAbType());

        // 环境
        taskInfoDO.setEnv(EnvUtils.getEnvironment());

        if (!CollectionUtils.isEmpty(config.getScrmIncludeTagIdList())) {
            extJs.put(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST, config.getScrmIncludeTagIdList());
        }
        if (!CollectionUtils.isEmpty(config.getWechatJoinGroupActivityIdList())) {
            extJs.put(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, config.getWechatJoinGroupActivityIdList());
        }
        if (!CollectionUtils.isEmpty(config.getWechatChatIdList())) {
            extJs.put(TaskConstant.WECHAT_CHAT_ID_LIST, config.getWechatChatIdList());
        }

        // 最后将extInfo设置到模型里
        taskInfoDO.setExtInfo(extJs.toJSONString());

        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines <= 0) {
            PlatformLogUtil.logFail("创建群聊sop任务失败", LogListUtil.newArrayList(taskInfoDO));
            throw new TripscrmException(TripSCRMErrorCode.GROUP_SOP_CREATE_GROUP_TASK_FAIL);
        }

        TaskInfoDO existTaskInfoDO = taskService.queryTaskByNameAndSpaceId(taskInfoDO.getName(), taskInfoDO.getSpaceId());
        taskInfo.setId(taskInfoDO.getId());
        if (Objects.isNull(taskInfo.getAbType()) || Objects.isNull(TaskAbTypeEnum.of(taskInfo.getAbType().intValue()))) {
            return;
        }
        //AB分桶
        ABTask(existTaskInfoDO, taskInfo);
    }

    @Override
    protected void updateTaskList(GroupActivitySopConfigVO config) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        //db中获取任务列表
        List<TaskInfoDO> taskList = taskService.query(query);
        if (CollectionUtils.isEmpty(taskList)) {
            createTaskList(config, config.getActivityId());
            return;
        }
        List<TaskVO> taskInfoList = config.getTaskInfoList();

        Set<Long> configTaskIds = taskInfoList.stream()
                .filter(taskVO -> taskVO.getId() != null)
                .map(TaskVO::getId)
                .collect(Collectors.toSet());
        //删除
        List<TaskInfoDO> deleteList = taskList.stream()
                .filter(taskInfoDO -> !configTaskIds.contains(taskInfoDO.getId()))
                .collect(Collectors.toList());
        PlatformLogUtil.logInfo("群聊sop删除任务", LogListUtil.newArrayList(deleteList));
        deleteTask(deleteList);

        for (TaskVO taskVO : taskInfoList) {
            //判断是更新还是新增
            if (taskVO.getId() != null) {
                TaskInfoDO taskInfoDO = taskList.stream().filter(info -> info.getId().equals(taskVO.getId())).findFirst().orElse(null);
                if (Objects.isNull(taskInfoDO)) {
                    PlatformLogUtil.logFail("群聊sop删除任务失败", LogListUtil.newArrayList(taskVO));
                    throw new TripscrmException(TripSCRMErrorCode.TASK_LIST_EMPTY);
                }
                //更新
                updateTask(config, taskInfoDO, taskVO);
            } else {
                //新增,判断任务类型
                if (Objects.equals(taskVO.getTaskTypeCode(), TaskType.GROUP_CHAT_MESSAGE.getCode())) {
                    // 创建群聊任务
                    createGroupChatTask(taskVO, config.getActivityId(), config);
                    continue;
                }
                // 1.创建转发消息到群
                Long subTaskId = createForwardMessageToChatTask(taskVO, config.getActivityId(), config);
                // 2.创建转发消息到群管理员任务
                createForwardMessageToManagerTask(taskVO, config.getActivityId(), config, subTaskId);
            }
        }
        updateActivityContext(config.getActivityId(), config);
    }

    private void deleteTask(List<TaskInfoDO> deleteList) {
        for (TaskInfoDO taskInfoDO : deleteList) {

            if (taskInfoDO.getType().equals(TaskType.GROUP_CHAT_MESSAGE.getCode())) {
                Integer delete = taskService.delete(taskInfoDO.getId(), taskInfoDO.getSpaceId(), false);
                if (delete <= 0) {
                    PlatformLogUtil.logFail("群聊sop删除任务失败", LogListUtil.newArrayList(taskInfoDO));
                    throw new TripscrmException(TripSCRMErrorCode.GROUP_SOP_DELETE_TASK_FAIL);
                }
            }
            if (taskInfoDO.getType().equals(TaskType.FORWARD_MESSAGE.getCode()) && SINGLE_EXECUTE.getCode().equals(taskInfoDO.getTriggerType().toString())) {
                Integer delete = taskService.delete(taskInfoDO.getId(), taskInfoDO.getSpaceId(), false);
                if (delete <= 0) {
                    PlatformLogUtil.logFail("群聊sop删除任务失败", LogListUtil.newArrayList(taskInfoDO));
                    throw new TripscrmException(TripSCRMErrorCode.GROUP_SOP_DELETE_TASK_FAIL);
                }
                JSONObject config = JSONObject.parseObject(taskInfoDO.getConfig());
                if (Objects.isNull(config) || !config.containsKey(TaskConstant.SUB_TASK_ID)) {
                    return;
                }
                String subId = config.getString(TaskConstant.SUB_TASK_ID);
                Integer subDelete = taskService.delete(Long.valueOf(subId), taskInfoDO.getSpaceId(), false);
                if (subDelete <= 0) {
                    PlatformLogUtil.logFail("群聊sop删除转发消息事件任务失败", LogListUtil.newArrayList(taskInfoDO));
                    throw new TripscrmException(TripSCRMErrorCode.GROUP_SOP_DELETE_TASK_FAIL);
                }
            }
        }
    }


    private void updateTask(GroupActivitySopConfigVO config, TaskInfoDO taskInfoDO, TaskVO taskInfo) {

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST, config.getScrmIncludeTagIdList());
        extraInfo.put(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, config.getWechatJoinGroupActivityIdList());
        extraInfo.put(TaskConstant.WECHAT_CHAT_ID_LIST, config.getWechatChatIdList());

        if (Objects.isNull(taskInfo.getTriggerType())) {
            taskInfo.setTriggerType(Integer.parseInt(SINGLE_EXECUTE.getCode()));
        }
        // 时间表达式
        taskInfoDO.setTriggerTimeCron(generateCron(taskInfo));
        extraInfo.put("triggerTimeValue", taskInfo.getTriggerTimeValue());
        // 生效时间
        Long triggerTime = Long.valueOf(taskInfo.getTriggerTimeValue());
        Date currDateStart = org.apache.commons.lang.time.DateUtils.truncate(new Date(triggerTime), Calendar.DATE);
        Date currDateEnd = com.alibaba.tripscrm.service.util.system.DateUtils.addDays(currDateStart, 1);
        taskInfoDO.setEffectStartTime(currDateStart);
        taskInfoDO.setEffectEndTime(currDateEnd);

        taskInfoDO.setMaterialId(taskInfo.getMaterialId());
        //如果开了ab,素材id为null
        if (taskInfoDO.getMaterialId() < 0) {
            taskInfoDO.setMaterialId(null);
        }
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        // 任务开始结束时间
        String executeStartTime = taskInfo.getExecuteStartTime();
        String executeEndTime = taskInfo.getExecuteEndTime();
        taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);


        //如果是转发任务,需要将子事件任务一起更新
        if (Objects.equals(taskInfoDO.getType(), TaskType.FORWARD_MESSAGE.getCode())) {
            Long subId = updateSubTask(config, taskInfoDO, taskInfo);
            JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getConfig());
            taskInfoDO.setConfig(extInfo.toJSONString());
        }
        Integer i = taskService.updateTaskInfo(taskInfoDO, false);
        if (i <= 0) {
            PlatformLogUtil.logFail("群聊sop更新任务失败", LogListUtil.newArrayList(taskInfoDO));
            throw new TripscrmException(TripSCRMErrorCode.GROUP_SOP_UPDATE_TASK_FAIL);
        }
        if (Objects.isNull(taskInfo.getAbType()) || Objects.isNull(TaskAbTypeEnum.of(taskInfo.getAbType().intValue()))) {
            return;
        }
        ABTask(taskInfoDO, taskInfo);

    }


    /**
     * AB分桶
     *
     * @param taskInfoDO
     * @param taskInfo
     */
    private void ABTask(TaskInfoDO taskInfoDO, TaskVO taskInfo) {
        //AB分桶
        taskInfo.setAbTestBucketList(org.apache.commons.collections4.CollectionUtils.isEmpty(taskInfo.getAbTestBucketList()) ? new ArrayList<>() : taskInfo.getAbTestBucketList());
        List<AbTestBucketVO> newBucketList = taskInfo.getAbTestBucketList();

        // 百分比不足100
        if (newBucketList.stream().map(AbTestBucketVO::getBucketPercent).mapToInt(a -> a).sum() != 100) {
            PlatformLogUtil.logFail("群聊sopAB分桶失败", LogListUtil.newArrayList(taskInfoDO));
            throw new TripscrmException("AB实验总分桶流量不足100%");
        }
        // 更新分桶数据（新增+更新+删除）
        Integer update = abTestService.update(taskInfoDO.getId(), newBucketList);
        if (update <= 0) {
            PlatformLogUtil.logFail("群聊sopAB分桶失败", LogListUtil.newArrayList(taskInfoDO));
            throw new TripscrmException(TripSCRMErrorCode.GROUP_SOP_AB_BUCKET_FAIL);
        }
    }

    public Long updateSubTask(GroupActivitySopConfigVO config, TaskInfoDO taskInfoDO, TaskVO taskInfo) {
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getConfig());
        Long subTaskId;
        if (Objects.isNull(extInfo) || !extInfo.containsKey(TaskConstant.SUB_TASK_ID)) {
            //创建子任务
            subTaskId = createForwardMessageToChatTask(taskInfo, taskInfoDO.getActivityId(), config);
            extInfo.put(TaskConstant.SUB_TASK_ID, subTaskId);
            return subTaskId;
        }
        subTaskId = Long.valueOf(extInfo.getString(TaskConstant.SUB_TASK_ID));
        TaskInfoDO subTaskInfoDO = taskService.queryTaskById(subTaskId);
        if (Objects.isNull(subTaskInfoDO)) {
            //创建子任务
            subTaskId = createForwardMessageToChatTask(taskInfo, taskInfoDO.getActivityId(), config);
            return subTaskId;
        }
        subTaskInfoDO.setMaterialId(taskInfo.getMaterialId());
        //如果开了ab,素材id为null
        if (subTaskInfoDO.getMaterialId() < 0) {
            subTaskInfoDO.setMaterialId(null);
        }
        // 任务开始结束时间
        subTaskInfoDO.setEffectStartTime(new Date());
        subTaskInfoDO.setEffectEndTime(com.alibaba.tripscrm.service.util.system.DateUtils.addDays(new Date(), 7));
        String executeStartTime = taskInfo.getExecuteStartTime();
        String executeEndTime = taskInfo.getExecuteEndTime();
        subTaskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);
        if (taskService.updateTaskInfo(subTaskInfoDO, false) <= 0) {
            PlatformLogUtil.logFail("群聊sop更新任务失败", LogListUtil.newArrayList(taskInfoDO));
            throw new TripscrmException(TripSCRMErrorCode.GROUP_SOP_UPDATE_TASK_FAIL);
        }
        return subTaskId;
    }

    @Override
    public SopTypeEnum getSopType() {
        return SopTypeEnum.GROUP_SOP;
    }

    @Override
    public void fillInfo(ActivitySopVO<GroupActivitySopConfigVO> activitySopVO) {
        GroupActivitySopConfigVO config = activitySopVO.getConfig();
        // 包含标签信息
        if (!CollectionUtils.isEmpty(config.getScrmIncludeTagIdList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(config.getScrmIncludeTagIdList());
            config.setScrmIncludeTagIdList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
            config.setScrmIncludeTagVoList(tagInfoList.stream().map(tagConverter::convert2VO).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(config.getWechatChatIdList())) {
            List<WechatGroupVO> wechatGroupList = wechatGroupService.listByChatIdList(config.getWechatChatIdList(), false);
            config.setWechatChatIdVoList(wechatGroupList);
        }
        if (!CollectionUtils.isEmpty(config.getWechatJoinGroupActivityIdList())) {
            List<Long> wechatJoinGroupActivityIdList = config.getWechatJoinGroupActivityIdList().stream().map(Long::parseLong).collect(Collectors.toList());
            List<ActivityInfoDO> activityInfoList = activityInfoService.selectByIdList(wechatJoinGroupActivityIdList);
            config.setWechatJoinGroupActivityIdVoList(activityInfoList);
        }

    }
}
