package com.alibaba.tripscrm.service.service.task.ability;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.RoleTypeEnum;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.convert.TaskConverter;
import com.alibaba.tripscrm.service.enums.alarm.AlarmTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.SourceTypeEnum;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.factory.DelayScheduleMethodFactory;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.task.ability.main.*;
import com.alibaba.tripscrm.service.service.task.ability.sub.post.MainTaskStatusUpdateProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.post.SubTaskStatusUpdateProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.pre.*;
import com.alibaba.tripscrm.service.service.task.ability.sub.retry.RetryPostProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.retry.RetryPreCheckProcessor;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/4/23 17:31
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskExecuteStrategy {
    /**
     * 接口/事件触发-主任务执行器列表
     */
    private static final List<ExecuteProcessor> MAIN_STREAM_TASK_EXECUTE_PROCESSOR_LIST = new ArrayList<>();
    /**
     * 单次/周期执行-主任务执行器列表
     */
    private static final List<ExecuteProcessor> MAIN_BATCH_TASK_EXECUTE_PROCESSOR_LIST = new ArrayList<>();
    /**
     * 单次/周期执行-主任务执行失败后-执行器列表
     */
    private static final List<ExecuteProcessor> MAIN_BATCH_TASK_ERROR_EXECUTE_PROCESSOR_LIST = new ArrayList<>();
    /**
     * 子任务执行前置处理-执行器列表
     */
    private static final List<ExecuteProcessor> SUB_EXECUTE_PROCESSOR_LIST = new ArrayList<>();
    /**
     * 子任务执行后置处理-执行器列表
     */
    private static final List<ExecuteProcessor> SUB_POST_EXECUTE_PROCESSOR_LIST = new ArrayList<>();

    /**
     * 单条记录重试-执行器列表
     */
    private static final List<ExecuteProcessor> RECORD_RETRY_PROCESSOR_LIST = new ArrayList<>();

    /**
     * 单条记录重试后置处理-执行器列表
     */
    private static final List<ExecuteProcessor> RECORD_RETRY_POST_PROCESSOR_LIST = new ArrayList<>();

    /**
     * Spring上下文
     */
    private final ApplicationContext applicationContext;
    private final MetaqProducer metaqProducer;
    private final TaskService taskService;
    private final SpaceService spaceService;

    @PostConstruct
    public void init() {
        /**
         * 接口/事件触发_主任务执行器列表
         */
        // 任务执行_主任务_前置校验
        MAIN_STREAM_TASK_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(MainPreCheckProcessor.class));

        /**
         * 单次/周期执行_主任务执行器列表
         */
        // 任务执行_主任务_前置校验
        MAIN_BATCH_TASK_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(MainPreCheckProcessor.class));
        // 任务执行_主任务_数据查询
        MAIN_BATCH_TASK_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(MainDataQueryProcessor.class));
        // 任务执行_主任务_子任务拆分
        MAIN_BATCH_TASK_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(TaskSplitProcessor.class));
        // 任务执行_主任务_子任务分发
        MAIN_BATCH_TASK_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(TaskDispatchProcessor.class));

        /**
         * 单次/周期执行_主任务执行失败后-执行器列表
         */
        // 任务执行_主任务_出错后执行的逻辑
        MAIN_BATCH_TASK_ERROR_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(MainTaskErrorProcessor.class));

        /**
         * 子任务执行_执行器列表
         */
        // 任务执行_子任务_任务执行上下文初始化
        SUB_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(InitContextProcessor.class));
        // 任务执行_子任务_任务有效性检查
        SUB_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(CheckValidProcessor.class));
        // 任务执行_子任务_活动上下文有效性检查
        SUB_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(CheckActivityContextProcessor.class));
        // 任务执行_子任务_设置限流器
        SUB_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(InitRateLimiterProcessor.class));
        // 任务执行_子任务_数据查询
        SUB_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(DataQueryProcessor.class));
        // 任务执行_子任务_对每条数据执行业务逻辑
        SUB_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(TaskBizExecuteProcessor.class));

        /**
         * 子任务执行后置处理_执行器列表
         */
        // 任务执行_子任务_执行完成后状态更新
        SUB_POST_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(SubTaskStatusUpdateProcessor.class));
        // 任务执行_主任务_执行完成后状态更新
        SUB_POST_EXECUTE_PROCESSOR_LIST.add(applicationContext.getBean(MainTaskStatusUpdateProcessor.class));

        /**
         * 单条记录重试后置处理_执行器列表
         */
        // 任务执行_单条记录重试-执行完成后更新子任务实例执行进度
        RECORD_RETRY_POST_PROCESSOR_LIST.add(applicationContext.getBean(RetryPostProcessor.class));
        // 任务执行_子任务_执行完成后状态更新
        RECORD_RETRY_POST_PROCESSOR_LIST.add(applicationContext.getBean(SubTaskStatusUpdateProcessor.class));
        // 任务执行_主任务_执行完成后状态更新
        RECORD_RETRY_POST_PROCESSOR_LIST.add(applicationContext.getBean(MainTaskStatusUpdateProcessor.class));


        /**
         * 单条记录重试-执行器列表
         */
        RECORD_RETRY_PROCESSOR_LIST.add(applicationContext.getBean(InitContextProcessor.class));
        RECORD_RETRY_PROCESSOR_LIST.add(applicationContext.getBean(RetryPreCheckProcessor.class));
        RECORD_RETRY_PROCESSOR_LIST.add(applicationContext.getBean(DataQueryProcessor.class));
        RECORD_RETRY_PROCESSOR_LIST.add(applicationContext.getBean(TaskBizExecuteProcessor.class));

        // 注册延迟调度方法
        DelayScheduleMethodFactory.register(DelayScheduleMethodEnum.MAIN_TASK_EXECUTE, this::runMain);
        DelayScheduleMethodFactory.register(DelayScheduleMethodEnum.SUB_TASK_EXECUTE, this::runSub);
    }

    /**
     * 执行入口
     */
    @TaskExecuteLog("任务执行_主任务执行")
    public TripSCRMResult<String> runMain(TaskExecuteContext context) {
        boolean isBatchTask = false;
        try {
            initSpaceInfo(context.getTaskId());
            isBatchTask = Lists.newArrayList(TaskTriggerTypeEnum.SINGLE_EXECUTE, TaskTriggerTypeEnum.EXECUTION_PLAN).contains(context.getTriggerType());
            return isBatchTask ? runBatchMainTask(context) : runStreamMainTask(context);
        } catch (TripscrmException e) {
            PlatformLogUtil.logException("主任务执行失败", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
            runBatchMainErrorProcessorList(context);
            if (isBatchTask) {
                sendTaskExecuteMessage(AlarmTypeEnum.TASK_EXECUTE_ERROR, context.getTaskId());
            }
            return TripSCRMResult.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Throwable e) {
            PlatformLogUtil.logException("主任务执行异常", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
            runBatchMainErrorProcessorList(context);
            if (isBatchTask) {
                sendTaskExecuteMessage(AlarmTypeEnum.TASK_EXECUTE_ERROR, context.getTaskId());
            }
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    /**
     * 重试入口
     */
    @TaskExecuteLog("任务执行_单条记录重试")
    public TripSCRMResult<String> retryRecord(TaskExecuteContext context) {
        try {
            initSpaceInfo(context.getTaskId());
            runRetryRecordProcessorList(context);
            return TripSCRMResult.success(context.getResult());
        } catch (TripscrmException e) {
            PlatformLogUtil.logException("任务执行_单条记录重试失败", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
            context.getTaskDataVO().getData().get(0).setResultErrorCode(TripSCRMErrorCode.codeOf(e.getErrorCode()));
        } catch (Throwable e) {
            PlatformLogUtil.logException("任务执行_单条记录重试异常", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
            context.getTaskDataVO().getData().get(0).setResultErrorCode(TripSCRMErrorCode.SYSTEM_EXCEPTION);
        } finally {
            runRecordRetryPostProcessorList(context);
            SpaceInfoThreadLocalUtils.remove();
        }

        return TripSCRMResult.fail(context.getTaskDataVO().getData().get(0).getResultErrorCode());
    }

    /**
     * 执行入口
     */
    @TaskExecuteLog("任务执行_子任务执行")
    public TripSCRMResult<String> runSub(TaskExecuteContext context) {
        try {
            initSpaceInfo(context.getTaskId());
            runSubPreProcessorList(context);
            return TripSCRMResult.success(context.getResult());
        } catch (TripscrmException e) {
            PlatformLogUtil.logException("子任务执行失败", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
            return TripSCRMResult.fail(e.getErrorCode(), e.getErrorMsg());
        } catch (Throwable e) {
            PlatformLogUtil.logException("子任务执行异常", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        } finally {
            runSubPostProcessorList(context);
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    /**
     * 执行入口
     */
    public TripSCRMResult<String> runMain(String paramStr) {
        TaskExecuteParam taskExecuteParam = JSONObject.parseObject(paramStr, new TypeReference<TaskExecuteParam>() {
        });
        return runMain(taskExecuteParam);
    }

    /**
     * 执行入口
     */
    public TripSCRMResult<String> runSub(String paramStr) {
        TaskExecuteParam taskExecuteParam = JSONObject.parseObject(paramStr, new TypeReference<TaskExecuteParam>() {
        });
        return runSub(taskExecuteParam);
    }

    /**
     * 执行入口
     */
    public TripSCRMResult<String> runMain(TaskExecuteParam param) {
        if (Objects.isNull(param)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        PlatformLogUtil.logInfo("主任务执行开始", LogListUtil.newArrayList(param.getTaskId(), param.getInstanceId(), param));
        TaskExecuteContext context = TaskConverter.param2Context(param);
        return runMain(context);
    }

    /**
     * 执行入口
     */
    public TripSCRMResult<String> runSub(TaskExecuteParam param) {
        if (Objects.isNull(param)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        PlatformLogUtil.logInfo("主任务执行开始", LogListUtil.newArrayList(param.getTaskId(), param.getInstanceId(), param));
        TaskExecuteContext context = TaskConverter.param2Context(param);
        return runSub(context);
    }

    private TripSCRMResult<String> runStreamMainTask(TaskExecuteContext context) throws Exception {
        // 只有接口调用和事件触发才执行
        if (!Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType())) {
            return TripSCRMResult.success(null);
        }

        for (ExecuteProcessor executeProcessor : MAIN_STREAM_TASK_EXECUTE_PROCESSOR_LIST) {
            TripSCRMResult<Void> result = executeProcessor.process(context);
            if (!result.isSuccess()) {
                return TripSCRMResult.fail(result.getCode(), result.getMsg());
            }
        }

        context.setMainTaskInstanceId(context.getInstanceId());
        context.setInstanceId(null);
        return runSub(context);
    }

    private TripSCRMResult<String> runBatchMainTask(TaskExecuteContext context) throws Exception {
        // 只有单次和周期任务才执行
        if (Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType())) {
            return TripSCRMResult.success(null);
        }

        sendTaskExecuteMessage(AlarmTypeEnum.TASK_EXECUTE_START, context.getTaskId());
        for (ExecuteProcessor executeProcessor : MAIN_BATCH_TASK_EXECUTE_PROCESSOR_LIST) {
            TripSCRMResult<Void> result = executeProcessor.process(context);
            if (!result.isSuccess()) {
                sendTaskExecuteMessage(AlarmTypeEnum.TASK_EXECUTE_FAIL, context.getTaskId());
                return TripSCRMResult.fail(result.getCode(), result.getMsg());
            }
        }

        sendTaskExecuteMessage(AlarmTypeEnum.TASK_EXECUTE_FINISH, context.getTaskId());
        return TripSCRMResult.success(null);
    }

    private TripSCRMResult<String> runRetryRecordProcessorList(TaskExecuteContext context) throws Exception {
        // 只有单次触发和周期触发才执行
        if (!Lists.newArrayList(TaskTriggerTypeEnum.SINGLE_EXECUTE, TaskTriggerTypeEnum.EXECUTION_PLAN).contains(context.getTriggerType())) {
            return TripSCRMResult.success(null);
        }

        for (ExecuteProcessor executeProcessor : RECORD_RETRY_PROCESSOR_LIST) {
            TripSCRMResult<Void> result = executeProcessor.process(context);
            if (!result.isSuccess()) {
                return TripSCRMResult.fail(result.getCode(), result.getMsg());
            }
        }

        return TripSCRMResult.success(null);
    }

    private TripSCRMResult<String> runBatchMainErrorProcessorList(TaskExecuteContext context) {
        try {
            for (ExecuteProcessor executeProcessor : MAIN_BATCH_TASK_ERROR_EXECUTE_PROCESSOR_LIST) {
                TripSCRMResult<Void> result = executeProcessor.process(context);
                if (!result.isSuccess()) {
                    return TripSCRMResult.fail(result.getCode(), result.getMsg());
                }
            }
        } catch (Throwable ex) {
            PlatformLogUtil.logException("主任务执行失败后-执行器列表，执行异常", ex.getMessage(), ex, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
        }

        return TripSCRMResult.success(null);
    }

    private void runSubPreProcessorList(TaskExecuteContext context) throws Exception {
        for (ExecuteProcessor preExecuteProcessor : SUB_EXECUTE_PROCESSOR_LIST) {
            TripSCRMResult<Void> result = preExecuteProcessor.process(context);
            if (!result.isSuccess()) {
                return;
            }
        }
    }

    private void runSubPostProcessorList(TaskExecuteContext context) {
        try {
            for (ExecuteProcessor postExecuteProcessor : SUB_POST_EXECUTE_PROCESSOR_LIST) {
                TripSCRMResult<Void> result = postExecuteProcessor.process(context);
                if (!result.isSuccess()) {
                    return;
                }
            }
        } catch (Throwable e) {
            PlatformLogUtil.logException("子任务后置处理-执行器列表，执行异常", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
        }
    }

    private void runRecordRetryPostProcessorList(TaskExecuteContext context) {
        try {
            for (ExecuteProcessor postExecuteProcessor : RECORD_RETRY_POST_PROCESSOR_LIST) {
                TripSCRMResult<Void> result = postExecuteProcessor.process(context);
                if (!result.isSuccess()) {
                    return;
                }
            }
        } catch (Throwable e) {
            PlatformLogUtil.logException("任务执行_单条记录重试-执行器列表，执行异常", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId(), context));
        }
    }

    private void sendTaskExecuteMessage(AlarmTypeEnum alarmTypeEnum, Long taskId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", "task");
        jsonObject.put("scene", alarmTypeEnum.getDesc());
        jsonObject.put("taskId", taskId);
        jsonObject.put("message", alarmTypeEnum.getCode());
        metaqProducer.send(MQEnum.TRIP_SCRM_ALRAM, "", "task", jsonObject.toJSONString(), null);
    }

    private void initSpaceInfo(Long taskId) {
        Long spaceId = taskService.querySpaceIdByTaskIdWithCache(taskId);
        String corpId = spaceService.getCorpIdBySpaceId(spaceId);
        SpaceInfoThreadLocalUtils.setCorpId(corpId).setSpaceId(spaceId).setRoleType(RoleTypeEnum.SUPER_ADMIN).setSourceId(taskId.toString()).setSourceType(SourceTypeEnum.TASK_OPT);
    }
}
