package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.CallPushDataUpdateEventTypeEnum;
import com.alibaba.tripscrm.service.enums.task.SellerAcquisitionLinkDataUpdateEventTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.CustomerChangeTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.CustomizerApprovalService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.log.ttlog.TtCustomerAcquisitionAddLogUtils;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.CustomerAddWayEnum;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.google.common.collect.Lists;
import com.tmall.beehive.common.lang.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户变更【微信回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerChangeCallbackProcessor implements ProxyCallbackProcessor {
    private final ActivityContextService activityContextService;
    private final LdbTairManager ldbTairManager;
    private final MetaqProducer metaqProducer;
    private final CustomizerApprovalService customizerApprovalService;
    private final TagRelationService tagRelationService;
    private final WechatCustomerService wechatCustomerService;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.CUSTOMER_CHANGE;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        // 目前只处理添加好友消息
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        if (!content.containsKey(CallbackConstant.CHANGE_TYPE) || !content.containsKey(CallbackConstant.EVENT)) {
            return true;
        }
        processJourneyManager(content);
        processAddCustomer(content, scrmCallbackMsg.getPlatformCorpId());
        processStatistic(content);
        processCustomizerMsg(content, scrmCallbackMsg.getPlatformCorpId());
        updateCustomerRelationCrowdTag(content, scrmCallbackMsg.getPlatformCorpId());
        return true;
    }

    private void processAddCustomer(JSONObject content, String platformCorpId) {
        try {
            String changeType = content.getString(CallbackConstant.CHANGE_TYPE);
            // 目前只处理添加好友消息
            if (!Lists.newArrayList("add_external_contact", "add_half_external_contact").contains(changeType)) {
                return;
            }

            String userId = content.getString(CallbackConstant.USER_ID);
            String externalUserId = content.getString(CallbackConstant.EXTERNAL_USER_ID);
            String taoId = content.getString(CallbackConstant.TAO_ID);
            String unionId = content.getString(CallbackConstant.UNION_ID);
            String state = content.containsKey(CallbackConstant.STATE) ? content.getString(CallbackConstant.STATE) : "";
            String welcomeCode = content.containsKey(CallbackConstant.WELCOME_CODE) ? content.getString(CallbackConstant.WELCOME_CODE) : "";

            // 打默认标签和uid标签
            addBasicTag(externalUserId, platformCorpId, unionId, taoId);

            Integer addWay = content.containsKey(CallbackConstant.ADD_WAY) ? content.getInteger(CallbackConstant.ADD_WAY) : 0;
            CustomerAddWayEnum customerAddWayEnum = Arrays.stream(CustomerAddWayEnum.values()).filter(e -> Objects.equals(e.getCode(), addWay)).findAny().orElse(null);
            JSONObject message = new JSONObject();
            message.put("userId", userId);
            message.put("externalUserId", externalUserId);
            message.put("taoId", taoId);
            message.put("unionId", unionId);
            message.put("changeType", changeType);
            message.put("state", state);
            message.put("welcomeCode", welcomeCode);
            message.put(TaskConstant.CORP_ID, platformCorpId);

            // 继承好友不触发
            if (Lists.newArrayList(CustomerAddWayEnum.SHARED_BY_MEMBER, CustomerAddWayEnum.ADMIN_ASSIGNMENT).contains(customerAddWayEnum)) {
                return;
            }

            if (StringUtils.hasText(state) && Strings.isNumeric(state)) {
                ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(Long.parseLong(state));
                if (Objects.nonNull(activityTaskInfoBO) && NumberUtils.validLong(activityTaskInfoBO.getActivityId())) {
                    message.put(TaskConstant.ACTIVITY_ID, activityTaskInfoBO.getActivityId());
                }
            }

            // 继承好友不触发
            if (Lists.newArrayList(CustomerAddWayEnum.SHARED_BY_MEMBER, CustomerAddWayEnum.ADMIN_ASSIGNMENT, CustomerAddWayEnum.UN_KNOWN).contains(customerAddWayEnum)) {
                return;
            }

            message.put("addWay", addWay);
            // 发送添加客户好友消息
            metaqProducer.send(MQEnum.WECHAT_ADD_CUSTOMER, "", changeType, message.toJSONString(), MetaQDeleyLevel.LEVEL_5.getLevel());
            // 内部使用添加客户好友消息
            metaqProducer.send(MQEnum.WECHAT_ADD_CUSTOMER_INNER, "", changeType, message.toJSONString());
            // 发送客户入群或者添加成员消息
            metaqProducer.send(MQEnum.WECHAT_CUSTOMER_ADD_FOLLOW_USER_OR_JOIN_GROUP, "", changeType, message.toJSONString());
            if (Lists.newArrayList(CustomerAddWayEnum.SEARCH_MOBILE_NUMBER.getCode(), CustomerAddWayEnum.SCAN_QR_CODE.getCode()).contains(addWay)) {
                // 发送外呼数据变更消息
                JSONObject callPushData = new JSONObject(message);
                callPushData.put("targetId", unionId);
                callPushData.put("targetType", ActivityTargetTypeEnum.WX_UNION_ID.getCode());
                callPushData.put("eventType", CallPushDataUpdateEventTypeEnum.ADD_CUSTOMER_SUCCESS.getCode());
                callPushData.remove(TaskConstant.ACTIVITY_ID);
                metaqProducer.send(MQEnum.SCRM_CALL_PUSH_DATA_PROCESS, externalUserId, CallPushDataUpdateEventTypeEnum.ADD_CUSTOMER_SUCCESS.getCode(), callPushData.toJSONString());
            }

            // 发送客户诸葛标签变更消息
            // ${corp_id}\t${union_id}\t${external_user_id}
            metaqProducer.send(MQEnum.SCRM_WECHAT_CUSTOMER_CROWD_TAG_DIFF, externalUserId, "new_customer", String.format("%s\t%s\t%s", platformCorpId, "", externalUserId));

            // 商家获客
            if (Objects.equals(SwitchConfig.sellerCorpId, platformCorpId)) {
                Long activityId = getLongFromTair(TairConstant.CUSTOMER_LAST_VISIT_SELLER_ACQUISITION_PAGE_ACTIVITY + unionId);
                if (NumberUtils.validLong(activityId)) {
                    // 发送商家获客链接添加好友消息
                    JSONObject sellerAcquisitionLinkDataUpdateData = new JSONObject(message);
                    sellerAcquisitionLinkDataUpdateData.put(TaskConstant.ACTIVITY_ID, activityId);
                    sellerAcquisitionLinkDataUpdateData.put("eventType", SellerAcquisitionLinkDataUpdateEventTypeEnum.ADD_CUSTOMER.getCode());
                    metaqProducer.send(MQEnum.SCRM_SELLER_ACQUISITION_LINK_DATA_UPDATE, null, null, sellerAcquisitionLinkDataUpdateData.toJSONString());
                }
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("添加客户回调，处理添加客户逻辑出错", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    /**
     * 统计客户添加数据
     */
    private void processStatistic(JSONObject content) {
        try {
            String externalUserId = content.getString(CallbackConstant.EXTERNAL_USER_ID);
            String state = content.containsKey(CallbackConstant.STATE) ? content.getString(CallbackConstant.STATE) : "";
            // 统计个码/获客助手添加数
            if (NumberUtils.toLong(state, -1L) != -1) {
                ldbTairManager.incr(TairConstant.WECHAT_CONTACT_ME_ADD_CUSTOMER_COUNT_PREFIX + state, 1, 0, 86400 * 365 * 10);
                return;
            }

            // 根据上下文统计添加数据
            ActivityTaskInfoBO query = new ActivityTaskInfoBO();
            query.setTargetTypeEnum(ActivityTargetTypeEnum.WX_EXTERNAL_USERID);
            query.setTargetId(externalUserId);
            List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByTarget(query);
            if (CollectionUtils.isEmpty(activityTaskInfoBOS)) {
                return;
            }

            Map<Long, List<ActivityTaskInfoBO>> activityId2ContextList = activityTaskInfoBOS.stream().collect(Collectors.groupingBy(ActivityTaskInfoBO::getActivityId));
            for (Long activityId : activityId2ContextList.keySet()) {
                ActivityTaskInfoBO activityTaskInfoBO = ActivityContextService.getNewestBo(activityId2ContextList.get(activityId));
                JSONObject extraJson = Optional.ofNullable(activityTaskInfoBO.getExtraJson()).orElse(new JSONObject());
                if (!extraJson.containsKey("addWechatCustomerByGroup") || !extraJson.containsKey("taskId")) {
                    continue;
                }

                if (extraJson.containsKey("statistic") && extraJson.getBoolean("statistic")) {
                    continue;
                }

                // 群内添加好友通过数据
                Long taskId = extraJson.getLong("taskId");
                ldbTairManager.incr(TairConstant.ADD_WECHAT_CUSTOMER_BY_GROUP_SUCCESS_COUNT_PREFIX + taskId, 1, 0, 86400 * 365 * 10);
                extraJson.put("statistic", true);
                activityContextService.upsert(activityTaskInfoBO);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("添加客户回调，处理数据统计逻辑出错", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    private void processJourneyManager(JSONObject content) {
        try {
            // 目前只处理添加好友消息
            if (!Lists.newArrayList("add_external_contact", "add_half_external_contact").contains(content.getString(CallbackConstant.CHANGE_TYPE))) {
                return;
            }

            String userId = content.getString(CallbackConstant.USER_ID);
            String externalUserId = content.getString(CallbackConstant.EXTERNAL_USER_ID);
            if (!content.containsKey(CallbackConstant.STATE)) {
                return;
            }

            String state = content.getString(CallbackConstant.STATE);
            if (!StringUtils.hasText(state) || !state.startsWith("hkzs_")) {
                return;
            }

            String contextId = state.substring(5);
            if (!StringUtils.hasText((contextId))) {
                return;
            }

            ActivityTaskInfoBO activityTaskInfoBO = activityContextService.queryByTaskId(Long.parseLong(contextId));
            if (Objects.isNull((activityTaskInfoBO))) {
                return;
            }

            if (!Objects.equals(ActivityTargetTypeEnum.TAOBAO_USER_ID, activityTaskInfoBO.getTargetTypeEnum())) {
                return;
            }

            String uid = activityTaskInfoBO.getTargetId();
            JSONObject extraJson = activityTaskInfoBO.getExtraJson();
            String tag = extraJson.containsKey("personMaTagId") ? extraJson.getString("personMaTagId") : "";
            if (!StringUtils.hasText(uid)) {
                return;
            }

            //缓存记录获客链接的客户数量
            String linkId = extraJson.getString("linkId");
            if (StringUtils.hasText(linkId)) {
                //存储50年，如果使用put不带过期时间，考虑并发问题还是使用缓存带过期时间原子能力
                ldbTairManager.incr(TairConstant.LINK_ADD_CLIENT_PREFIX_.concat(linkId), 1, 0, 50 * 365 * 24 * 3600);
            }
            TtCustomerAcquisitionAddLogUtils.log(userId, externalUserId, tag, uid);
        } catch (Exception e) {
            PlatformLogUtil.logException("添加客户回调，处理行程管家业务逻辑出错", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    /**
     * 定制师发送打招呼信息
     */
    public void processCustomizerMsg(JSONObject content, String corpId) {
        try {
            if (!SwitchConfig.CUSTOMIZER_MSG_CALLBACK_SWITCH_V2) {
                return;
            }
            // 目前只处理添加好友消息
            if (!Lists.newArrayList("add_external_contact", "add_half_external_contact").contains(content.getString(CallbackConstant.CHANGE_TYPE))) {
                return;
            }

            String userId = content.getString(CallbackConstant.USER_ID);
            String externalUserId = content.getString(CallbackConstant.EXTERNAL_USER_ID);
            String taoId = content.getString(CallbackConstant.TAO_ID);
            String state = content.containsKey(CallbackConstant.STATE) ? content.getString(CallbackConstant.STATE) : "";
            String welcomeCode = content.containsKey(CallbackConstant.WELCOME_CODE) ? content.getString(CallbackConstant.WELCOME_CODE) : "";
            customizerApprovalService.sendWelcomeMessage(userId, externalUserId, taoId, state, corpId, welcomeCode);
        } catch (Exception e) {
            PlatformLogUtil.logException("添加客户回调，处理定制师逻辑出错", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    private Long getLongFromTair(String key) {
        Object o = ldbTairManager.get(key);
        if (Objects.isNull(o)) {
            return null;
        }

        return Long.parseLong(String.valueOf(o));
    }

    /**
     * 更新客户关系诸葛标签
     *
     * @param content 回调内容
     * @param platformCorpId 企业Id
     */
    private void updateCustomerRelationCrowdTag(JSONObject content, String platformCorpId) {
        CustomerChangeTypeEnum changeTypeEnum = CustomerChangeTypeEnum.codeOf(content.getString(CallbackConstant.CHANGE_TYPE));
        if (!Lists.newArrayList(CustomerChangeTypeEnum.ADD_EXTERNAL_CONTACT,
                CustomerChangeTypeEnum.ADD_HALF_EXTERNAL_CONTACT,
                CustomerChangeTypeEnum.DEL_EXTERNAL_CONTACT,
                CustomerChangeTypeEnum.DEL_FOLLOW_USER).contains(changeTypeEnum)) {
            return;
        }
        String externalUserId = content.getString(CallbackConstant.EXTERNAL_USER_ID);
        content.put(CallbackConstant.CORP_ID, platformCorpId);
        metaqProducer.send(MQEnum.CUSTOMER_RELATION_CROWD_TAG_SYNC, null, externalUserId, content.toJSONString(), MetaQDeleyLevel.LEVEL_2.getLevel());
    }

    /**
     * 打默认标签和uid标签
     * @param externalUserId
     * @param platformCorpId
     * @param unionId
     * @param taoId
     */
    private void addBasicTag(String externalUserId, String platformCorpId, String unionId, String taoId) {
        if (!StringUtils.hasLength(taoId)) {
            taoId = wechatCustomerService.getUidByExternalUserIdAndCorpIdAndUnionId(externalUserId, platformCorpId, unionId);
        }
        tagRelationService.addBasicTag(externalUserId, platformCorpId, taoId);
    }
}
