package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatCustomerAcquisitionLinkDO;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerAcquisitionService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.log.ttlog.TtCustomerAcquisitionStatisticLogUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.wx.WXBizMsgCryptConfig;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.alibaba.tripzoo.proxy.api.service.CustomerAcquisitionService;
import com.alibaba.tripzoo.proxy.model.CustomerAcquisitionLinkBO;
import com.alibaba.tripzoo.proxy.model.CustomerAcquisitionLinkQuotaBO;
import com.alibaba.tripzoo.proxy.model.CustomerAcquisitionLinkStatisticBO;
import com.alibaba.tripzoo.proxy.request.GetLinkRequest;
import com.alibaba.tripzoo.proxy.request.GetStatisticRequest;
import com.alibaba.tripzoo.proxy.request.base.CorpBaseRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 微信获客助手链接数据统计任务
 *
 * <AUTHOR>
 * @date 2023-08-28 16:12:48
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatCustomerAcquisitionStatisticProcessor extends JavaProcessor {
    @Switch(name = "customerAcquisitionAlarmBalance", description = "customerAcquisitionAlarmBalance")
    public static Integer customerAcquisitionAlarmBalance = 1000;

    @Switch(name = "dingTalkAccessToken", description = "dingTalkAccessToken")
    public static String dingTalkAccessToken = "d79a6454705a059bf4b417266aa508aa3e303d38d233109f46b07e46d58656ee";

    private final WechatCustomerAcquisitionService wechatCustomerAcquisitionService;
    private final CustomerAcquisitionService customerAcquisitionService;
    private final DingTalkApi dingTalkApi;
    private final SpaceService spaceService;
    private final WXCorpStorage wxCorpStorage;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        // 同步企微侧获客链接最新数据
        syncData(wechatCustomerAcquisitionService.selectAll());
        // 同步企微侧获客链接前一日数据
        syncStatistic(wechatCustomerAcquisitionService.selectAll());
        // 告警
        for (WXBizMsgCryptConfig corpConfig : wxCorpStorage.listConfig()) {
            String corpId = corpConfig.getCorpId();
            SpaceInfoThreadLocalUtils.setCorpId(corpId);
            alarm(corpId);
            SpaceInfoThreadLocalUtils.remove();
        }
        return new ProcessResult(true);
    }

    private void alarm(String corpId) {
        try {
            CorpBaseRequest request = new CorpBaseRequest();
            request.setCorpId(corpId);
            ResultDO<CustomerAcquisitionLinkQuotaBO> resultDO = customerAcquisitionService.getQuota(request);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
                PlatformLogUtil.logFail("微信获客助手链接数据统计任务，获客助手剩余额度查询失败", LogListUtil.newArrayList());
                return;
            }

            CustomerAcquisitionLinkQuotaBO customerAcquisitionLinkQuotaBO = resultDO.getModel();
            if (customerAcquisitionLinkQuotaBO.getBalance() <= customerAcquisitionAlarmBalance) {
                dingTalkApi.sendMessage(dingTalkAccessToken,
                    "获客助手剩余额度不足，当前额度：" + customerAcquisitionLinkQuotaBO.getBalance());
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("微信获客助手链接数据统计任务，告警过程异常", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    /**
     * 1. 统计每一个获客链接的前一天的点击&添加数据
     * 2. 统计每一个获客链接当前累计添加的客户数据
     *
     * @param wechatCustomerAcquisitionLinkListInDb
     */
    private void syncStatistic(List<WechatCustomerAcquisitionLinkDO> wechatCustomerAcquisitionLinkListInDb) {
        for (WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO : wechatCustomerAcquisitionLinkListInDb) {
            Date yesterday = DateUtils.addDays(new Date(), -1);
            GetStatisticRequest request = new GetStatisticRequest();
            String corpId = spaceService.getCorpIdBySpaceId(wechatCustomerAcquisitionLinkDO.getSpaceId());
            request.setCorpId(corpId);
            request.setLinkId(wechatCustomerAcquisitionLinkDO.getLinkId());
            request.setStartTime(yesterday);
            request.setEndTime(yesterday);
            ResultDO<CustomerAcquisitionLinkStatisticBO> resultDO = customerAcquisitionService.getStatistic(request);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
                PlatformLogUtil.logFail("微信获客助手链接数据统计任务，获取获客统计数据失败", LogListUtil.newArrayList(wechatCustomerAcquisitionLinkDO));
                continue;
            }

            CustomerAcquisitionLinkStatisticBO customerAcquisitionLinkStatisticBO = resultDO.getModel();
            TtCustomerAcquisitionStatisticLogUtils.log(wechatCustomerAcquisitionLinkDO.getLinkId(),
                wechatCustomerAcquisitionLinkDO.getName(),
                customerAcquisitionLinkStatisticBO.getClickLinkCustomerCount(),
                customerAcquisitionLinkStatisticBO.getNewCustomerCnt());
        }
    }

    /**
     * 数据同步，将微信侧数据同步至 db，防止数据不一致
     *
     * @param wechatCustomerAcquisitionLinkListInDb db中的数据
     */
    private void syncData(List<WechatCustomerAcquisitionLinkDO> wechatCustomerAcquisitionLinkListInDb) {
        for (WechatCustomerAcquisitionLinkDO wechatCustomerAcquisitionLinkDO : wechatCustomerAcquisitionLinkListInDb) {
            GetLinkRequest getLinkRequest = new GetLinkRequest();
            SpaceDO space = spaceService.getById(wechatCustomerAcquisitionLinkDO.getSpaceId());
            getLinkRequest.setCorpId(space.getCorpId());
            ResultDO<CustomerAcquisitionLinkBO> resultDO = customerAcquisitionService.getLink(getLinkRequest);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
                PlatformLogUtil.logFail("微信获客助手链接数据统计任务，获客链接数据查询失败", LogListUtil.newArrayList(wechatCustomerAcquisitionLinkDO));
                continue;
            }

            CustomerAcquisitionLinkBO linkBO = resultDO.getModel();
            // 企微侧已删除
            if (Objects.isNull(linkBO)) {
                wechatCustomerAcquisitionService.deleteById(wechatCustomerAcquisitionLinkDO.getId(),
                    wechatCustomerAcquisitionLinkDO.getSpaceId());
                continue;
            }

            if (Objects.equals(linkBO.getName(), wechatCustomerAcquisitionLinkDO.getName())
                && Objects.equals(linkBO.getUserIdList().stream().sorted().collect(Collectors.joining(",")),
                Arrays.stream(wechatCustomerAcquisitionLinkDO.getUserIdList().split(",")).sorted()
                    .collect(Collectors.joining(",")))) {
                continue;
            }

            WechatCustomerAcquisitionLinkDO newRecord = new WechatCustomerAcquisitionLinkDO();
            newRecord.setId(wechatCustomerAcquisitionLinkDO.getId());
            newRecord.setName(linkBO.getName());
            newRecord.setUserIdList(String.join(",", linkBO.getUserIdList()));
            wechatCustomerAcquisitionService.update(newRecord, wechatCustomerAcquisitionLinkDO.getSpaceId());
        }
    }

    @AteyeInvoker(description = "发送钉钉消息", paraDesc = "accessToken&content")
    public void sendMsg(String accessToken, String content) throws Exception {
        accessToken = StringUtils.hasText(accessToken) ? accessToken : dingTalkAccessToken;
        dingTalkApi.sendMessage(accessToken, content);
    }
}
