package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;

import java.util.List;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/6/24 17:04
 * @Filename：ScrmTaskEffectDataRequest
 */
@Data
public class ScrmTaskEffectDataQuery extends BasePageRequest {
    /**
     * 任务名称
     */
    private String likeName;
    /**
     *   空间
     */
    private Long spaceId;

    /**
     * 任务类型
     */
    private List<String> taskTypeList;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 消息类型
     */
    private Long taskMessageType;


}
