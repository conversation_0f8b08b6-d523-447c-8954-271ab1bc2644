package com.alibaba.tripscrm.service.middleware.diamond;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.diamond.annotation.DiamondListener;
import com.alibaba.boot.diamond.listener.DiamondDataCallback;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.domain.enums.TaskType;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 任务自定义参数schema模板
 */
@Slf4j
@DiamondListener(dataId = "task_customer_config_template.json")
public class TaskCustomerConfigTemplateDiamond implements DiamondDataCallback {
    private static final String DEFAULT_KEY = "default";
    private Map<String, String> templateMap = new HashMap<>();


    @Override
    public void received(String data) {
        try {
            templateMap = JSONObject.parseObject(data, new TypeReference<Map<String, String>>() {
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }

    /**
     * 获取任务自定义参数schema模板
     *
     * @param taskType TaskType
     * @return JSONObject
     */
    public String getTemplate(TaskType taskType) {
        if (Objects.isNull(taskType) || !templateMap.containsKey(taskType.getCode())) {
            return templateMap.get(DEFAULT_KEY);
        }

        return templateMap.get(taskType.getCode());
    }
}
