package com.alibaba.tripscrm.service.service.material;

import com.alibaba.tripscrm.service.model.domain.query.ServiceInfoQuery;
import com.alibaba.tripscrm.service.model.dto.material.service.ServiceInfoDTO;

import java.util.List;

public interface ServiceInfoService {

    /**
     * 新增服务
     * @param serviceInfoDTO 服务信息
     * @return 处理结果
     */
    Boolean add(ServiceInfoDTO serviceInfoDTO);

    /**
     * 删除服务
     * @param id 主键
     * @return 处理结果
     */
    Boolean deleteById(Long id);

    /**
     * 更新服务
     * @param serviceInfoDTO 服务信息
     * @return 处理结果
     */
    Boolean updateById(ServiceInfoDTO serviceInfoDTO);

    /**
     * 条件查询
     * @param query 查询条件
     * @return 服务信息列表
     */
    List<ServiceInfoDTO> list(ServiceInfoQuery query);

    /**
     * 主键查询
     * @param id 主键
     * @return 服务信息
     */
    ServiceInfoDTO queryById(Long id);

}
