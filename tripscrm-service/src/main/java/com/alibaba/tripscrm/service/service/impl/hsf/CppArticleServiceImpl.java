package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.tripscrm.domain.FileDTO;
import com.alibaba.tripscrm.domain.WxCommunityContentDTO;
import com.alibaba.tripscrm.domain.WxContentItemDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.CppArticleService;
import com.alibaba.tripscrm.service.convert.CppConverter;
import com.alibaba.tripscrm.service.manager.opensearch.WxContentOpenSearchManager;
import com.taobao.contentplatform.article.base.read.ArticleReadService;
import com.taobao.contentplatform.article.base.read.req.ArticleListQueryReq;
import com.taobao.contentplatform.article.base.read.req.ArticleQueryReq;
import com.taobao.contentplatform.article.base.read.resp.ArticleDTO;
import com.taobao.contentplatform.article.base.write.ArticleWriteService;
import com.taobao.contentplatform.article.base.write.req.ArticleDeleteReq;
import com.taobao.contentplatform.article.base.write.req.ArticleEditReq;
import com.taobao.contentplatform.article.base.write.req.ArticlePublishReq;
import com.taobao.contentplatform.content.base.element.JsonArrayElement;
import com.taobao.contentplatform.content.base.element.PicElement;
import com.taobao.contentplatform.sdk.base.type.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 类描述
 * @author: huiyi
 * @create: 2024-04-20 23:04
 **/
@Slf4j
@Service
@HSFProvider(serviceInterface = CppArticleService.class)
public class CppArticleServiceImpl implements CppArticleService {
    @Resource
    private ArticleWriteService articleWriteService;
    @Resource
    private ArticleReadService articleReadService;
    @Resource
    private CppConverter cppConverter;
    @Resource
    private WxContentOpenSearchManager wxContentOpenSearchManager;

    private static final String BIZ_LINE = "fliggywx";
    private static final String BIZ_CODE = "community_content";
    private static final String PUBLISH_PRODUCE_TYPE = "UGC";
    private static final String PUBLISH_PLATFORM_TYPE = "fliggywx";
    private static final String PUBLISH_ENTRANCE = "wxcommunity";

    @Override
    public TripSCRMResult<Long> publish(WxCommunityContentDTO communityContentDTO) {
        try {
            // 发布内容
            ArticlePublishReq req = new ArticlePublishReq();

            // 默认参数
            req.setBizLine(BIZ_LINE);
            req.setBizCode(BIZ_CODE);
            req.setPublishProduceType(PUBLISH_PRODUCE_TYPE);
            req.setPublishPlatformType(PUBLISH_PLATFORM_TYPE);
            req.setPublishEntrance(PUBLISH_ENTRANCE);

            // 发布者信息
            req.setUserId(communityContentDTO.getUserId());
            req.setUserAvatar(communityContentDTO.getUserAvatar());
            req.setUserName(communityContentDTO.getUserName());

            // 图文内容
            req.setTitle(communityContentDTO.getTitle());
            req.setSummary(communityContentDTO.getSummary());

            // 图片信息
            List<FileDTO> picList = communityContentDTO.getPics();
            if (CollectionUtils.isNotEmpty(picList)) {
                List<PicElement> pics = picList.stream().map(e -> {
                    PicElement pic = new PicElement();
                    pic.setUrl(e.getUrl());
                    pic.setHeight(e.getHeight());
                    pic.setWidth(e.getWidth());
                    return pic;
                }).collect(Collectors.toList());
                req.setPics(pics);
            }

            // 微信公众号信息
            req.setFliggywxWxArticleId(communityContentDTO.getWxArticleId());
            req.setFliggywxWxArticleUrl(communityContentDTO.getWxArticleUrl());
            req.setFliggywxWxArticlePkId(communityContentDTO.getWxArticlePkId());

            req.setFliggywxMonthLabels(communityContentDTO.getMonthLabels());

            // 活码信息
            req.setFliggywxQrCodeActivityId(communityContentDTO.getQrCodeActivityId());
            req.setFliggywxQrCodeState(communityContentDTO.getQrCodeState());
            req.setFliggywxQrCodeType(communityContentDTO.getQrCodeType());
            req.setFliggywxQrCodeTitle(communityContentDTO.getQrCodeTitle());
            req.setFliggywxQrCodeSummary(communityContentDTO.getQrCodeSummary());

            // 商品信息
            List<WxContentItemDTO> relatedItemList = communityContentDTO.getRelatedItems();
            if (CollectionUtils.isNotEmpty(relatedItemList)) {
                JsonArrayElement relatedItems = new JsonArrayElement();
                JSONArray jsonArray = new JSONArray();

                relatedItemList.stream().forEach(e -> jsonArray.add(e));
                relatedItems.setValue(jsonArray);
                req.setFliggywxRelatedItems(relatedItems);
            }

            // 标签
            List<String> tagIds = communityContentDTO.getTagIds();
            req.setLabels(tagIds);

            // 编辑者信息
            req.setFliggywxCreator(communityContentDTO.getCreatorName());
            req.setFliggywxUpdater(communityContentDTO.getLastOperatorName());
            req.setFliggywxOwner(communityContentDTO.getManagerNames());

            // 自定义状态更新
            req.setFliggywxStatus(communityContentDTO.getStatus());

            // 环境
            req.setFliggywxEnv(communityContentDTO.getEnv());

            Result<Long> result = articleWriteService.userPublish(req);
            if (result.isFail()) {
                return TripSCRMResult.fail(result.getErrCode(), result.getErrMessage());
            }
            return TripSCRMResult.success(result.getResult());
        } catch (Exception e) {
            return TripSCRMResult.fail("EXCEPTION", e.getMessage());
        }
    }

    @Override
    public TripSCRMResult<Boolean> delete(Long cppContentId) {
        try {
            ArticleDeleteReq req = new ArticleDeleteReq();
            req.setBizLine(BIZ_LINE);
            req.setId(cppContentId);

            Result<Void> result = articleWriteService.userDelete(req);
            if (result.isFail()) {
                return TripSCRMResult.fail(result.getErrCode(), result.getErrMessage());
            }
            return TripSCRMResult.success(true);
        } catch (Exception e) {
            return TripSCRMResult.fail("EXCEPTION", e.getMessage());
        }
    }

    @Override
    public TripSCRMResult<Boolean> update(WxCommunityContentDTO communityContentDTO) {
        try {
            // 发布内容
            ArticleEditReq req = new ArticleEditReq();

            req.setBizLine(BIZ_LINE);

            // cpp内容id
            req.setId(communityContentDTO.getCppContentId());

            // 发布者信息
            req.setUserAvatar(communityContentDTO.getUserAvatar());
            req.setUserName(communityContentDTO.getUserName());

            // 图文内容
            req.setTitle(communityContentDTO.getTitle());
            req.setSummary(communityContentDTO.getSummary());

            // 图片信息
            List<FileDTO> picList = communityContentDTO.getPics();
            if (CollectionUtils.isNotEmpty(picList)) {
                List<PicElement> pics = picList.stream().map(e -> {
                    PicElement pic = new PicElement();
                    pic.setUrl(e.getUrl());
                    pic.setHeight(e.getHeight());
                    pic.setWidth(e.getWidth());
                    return pic;
                }).collect(Collectors.toList());
                req.setPics(pics);
            }

            // 微信公众号信息
            req.setFliggywxWxArticleId(communityContentDTO.getWxArticleId());
            req.setFliggywxWxArticleUrl(communityContentDTO.getWxArticleUrl());
            req.setFliggywxWxArticlePkId(communityContentDTO.getWxArticlePkId());

            req.setFliggywxMonthLabels(communityContentDTO.getMonthLabels());

            // 活码信息
            req.setFliggywxQrCodeActivityId(communityContentDTO.getQrCodeActivityId());
            req.setFliggywxQrCodeState(communityContentDTO.getQrCodeState());
            req.setFliggywxQrCodeType(communityContentDTO.getQrCodeType());
            req.setFliggywxQrCodeTitle(communityContentDTO.getQrCodeTitle());
            req.setFliggywxQrCodeSummary(communityContentDTO.getQrCodeSummary());

            // 商品信息
            List<WxContentItemDTO> relatedItemList = communityContentDTO.getRelatedItems();
            if (CollectionUtils.isNotEmpty(relatedItemList)) {
                JsonArrayElement relatedItems = new JsonArrayElement();
                JSONArray jsonArray = new JSONArray();

                relatedItemList.stream().forEach(e -> jsonArray.add(e));
                relatedItems.setValue(jsonArray);
                req.setFliggywxRelatedItems(relatedItems);
            }

            // 标签
            List<String> tagIds = communityContentDTO.getTagIds();
            req.setLabels(tagIds);

            // 编辑者信息
            req.setFliggywxCreator(communityContentDTO.getCreatorName());
            req.setFliggywxUpdater(communityContentDTO.getLastOperatorName());
            req.setFliggywxOwner(communityContentDTO.getManagerNames());

            // 自定义状态更新
            req.setFliggywxStatus(communityContentDTO.getStatus());

            // 环境
            req.setFliggywxEnv(communityContentDTO.getEnv());

            Result<Void> result = articleWriteService.userEdit(req);
            if (result.isFail()) {
                return TripSCRMResult.fail(result.getErrCode(), result.getErrMessage());
            }
            return TripSCRMResult.success(true);
        } catch (Exception e) {
            return TripSCRMResult.fail("EXCEPTION", e.getMessage());
        }
    }

    @Override
    public TripSCRMResult<WxCommunityContentDTO> find(Long cppContentId) {
        try {
            ArticleQueryReq req = new ArticleQueryReq();
            req.setBizLine(BIZ_LINE);
            req.setId(cppContentId);
            Result<ArticleDTO> result = articleReadService.getById(req);
            if (result.isFail()) {
                return TripSCRMResult.fail(result.getErrCode(), result.getErrMessage());
            }

            ArticleDTO articleDTO = result.getResult();
            WxCommunityContentDTO communityContentDTO = cppConverter.convertFromArticleDTO(articleDTO);
            return TripSCRMResult.success(communityContentDTO);
        } catch (Exception e) {
            return TripSCRMResult.fail("EXCEPTION", e.getMessage());
        }
    }

    @Override
    public TripSCRMResult<List<WxCommunityContentDTO>> list(List<Long> cppContentIds) {
        try {
            ArticleListQueryReq req = new ArticleListQueryReq();
            req.setIds(cppContentIds);
            Result<List<ArticleDTO>> result = articleReadService.listByIds(req);
            if (result.isFail()) {
                return TripSCRMResult.fail(result.getErrCode(), result.getErrMessage());
            }

            List<ArticleDTO> articleDTOList = result.getResult();
            List<WxCommunityContentDTO> communityContentDTOList = articleDTOList.stream().map(cppConverter::convertFromArticleDTO).collect(Collectors.toList());
            return TripSCRMResult.success(communityContentDTOList);
        } catch (Exception e) {
            return TripSCRMResult.fail("EXCEPTION", e.getMessage());
        }
    }
}
