package com.alibaba.tripscrm.service.service.impl.log;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.ManagementGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoDO;
import com.alibaba.tripscrm.service.enums.task.ManagementGroupTypeEnum;
import com.alibaba.tripscrm.service.enums.system.OperationLogStatusEnum;
import com.alibaba.tripscrm.service.enums.system.OperationLogTypeEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.domain.log.BatchOperateLogContentBO;
import com.alibaba.tripscrm.service.model.domain.query.GroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.OperationLogInfoQuery;
import com.alibaba.tripscrm.service.model.vo.log.BatchUpdateVO;
import com.alibaba.tripscrm.service.model.vo.log.UpdateDetailVO;
import com.alibaba.tripscrm.service.model.dto.BatchOperateLogDTO;
import com.alibaba.tripscrm.service.service.common.ManagementGroupService;
import com.alibaba.tripscrm.service.service.log.BatchOperateLogInfoService;
import com.alibaba.tripscrm.service.service.log.OperationLogInfoService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.strategy.log.factory.AbstractLogFactory;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.log.OperatorLogUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/4 19:32
 **/
@Service
public class BatchOperateLogInfoServiceImpl implements BatchOperateLogInfoService {

    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private AbstractLogFactory abstractLogFactory;
    @Resource
    private ManagementGroupService managementGroupService;
    @Resource
    private AccountService accountService;
    @Resource
    private OperationLogInfoService operationLogInfoService;

    @Override
    public Boolean insert(OperationLogInfoDO operationLogInfoDO) {
        return operationLogInfoService.insert(operationLogInfoDO);
    }

    @Override
    public List<BatchUpdateVO> queryBatchUpdateInfoList(GroupQuery query) {
        List<ManagementGroupDO> managementGroupList = managementGroupService.queryByPage(query);
        if (CollectionUtils.isEmpty(managementGroupList)) {
            return Lists.newArrayList();
        }
        return managementGroupList.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Long countBatchUpdateGroup(GroupQuery query) {
        return managementGroupService.count(query);
    }

    @Override
    public UpdateDetailVO convertToVO(OperationLogInfoDO operationLogInfoDO) {
        return convert(operationLogInfoDO);
    }

    @Override
    public List<UpdateDetailVO> queryDetailListByGroupId(Long groupId) {
        if (!NumberUtils.validLong(groupId)) {
            return Lists.newArrayList();
        }
        List<OperationLogInfoDO> logInfoDOList = operationLogInfoService.queryLogInfoListByGroupId(groupId);
        if (CollectionUtils.isEmpty(logInfoDOList)) {
            return Lists.newArrayList();
        }
        return logInfoDOList.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Long countByGroupId(Long groupId) {
        OperationLogInfoQuery query = new OperationLogInfoQuery();
        query.setGroupId(groupId);
        return operationLogInfoService.countByQuery(query);
    }

    @Override
    public Boolean updateLogInfoByRequestId(Long id, String requestId, Boolean success) {
        OperationLogInfoDO operationLogInfoDO = operationLogInfoService.queryById(id);
        if (StringUtils.isBlank(operationLogInfoDO.getOperatorContent())) {
            return false;
        }
        List<BatchOperateLogContentBO> batchOperateLogContentList = JSONObject.parseArray(operationLogInfoDO.getOperatorContent(), BatchOperateLogContentBO.class);
        if (CollectionUtils.isEmpty(batchOperateLogContentList)) {
            return false;
        }
        for (BatchOperateLogContentBO batchOperateLogContentBO : batchOperateLogContentList) {
            if (StringUtils.isNotBlank(batchOperateLogContentBO.getRequestId()) && batchOperateLogContentBO.getRequestId().equals(requestId)) {
                batchOperateLogContentBO.setOperateState(success ? OperationLogStatusEnum.SUCCESS.getCode() : OperationLogStatusEnum.FAIL.getCode());
            }
        }
        OperationLogInfoDO result = new OperationLogInfoDO();
        result.setId(operationLogInfoDO.getId());
        result.setOperatorContent(JSONObject.toJSONString(batchOperateLogContentList));
        result.setOperatorStatus(OperatorLogUtils.getOperationLogStatus(batchOperateLogContentList).getCode());
        Boolean aBoolean = operationLogInfoService.updateById(result);
        PlatformLogUtil.logInfo("更新批量操作日志状态", LogListUtil.newArrayList(id, requestId, batchOperateLogContentList, aBoolean));
        return true;
    }

    @Override
    public BatchOperateLogDTO queryById(Long id) {
        OperationLogInfoDO operationLogInfoDO = operationLogInfoService.queryById(id);
        if (operationLogInfoDO == null) {
            return null;
        }
        return convertToDTO(operationLogInfoDO);
    }

    @Override
    public Boolean updateById(BatchOperateLogDTO batchOperateLogDTO) {
        if (batchOperateLogDTO == null) {
            return false;
        }
        return operationLogInfoService.updateById(convertToDO(batchOperateLogDTO));
    }

    /**
     * DO对象转化为VO对象
     * @param operationLogInfoDO 操作日志DO对象
     * @return VO对象
     */
    public UpdateDetailVO convert(OperationLogInfoDO operationLogInfoDO) {
        UpdateDetailVO updateDetailVO = new UpdateDetailVO();
        updateDetailVO.setOperator(operationLogInfoDO.getOperatorName());
        updateDetailVO.setOperateType(operationLogInfoDO.getType());
        updateDetailVO.setChatId(operationLogInfoDO.getTargetId());
        updateDetailVO.setGroupName(queryChatName(operationLogInfoDO.getTargetId()));
        updateDetailVO.setOperateDate(operationLogInfoDO.getGmtCreate());
        updateDetailVO.setStatus(operationLogInfoDO.getOperatorStatus());
        List<String> contentList = getBatchOperateLogContentList(operationLogInfoDO.getOperatorContent());
        if (!CollectionUtils.isEmpty(contentList)) {
            updateDetailVO.setMessage(String.join("&", contentList));
        }
        return updateDetailVO;
    }

    /**
     * DTO转化为DO对象
     * @param batchOperateLogDTO 批量操作日志DTO
     * @return DO对象
     */
    public OperationLogInfoDO convertToDO(BatchOperateLogDTO batchOperateLogDTO) {
        OperationLogInfoDO operationLogInfoDO = new OperationLogInfoDO();
        BeanUtils.copyProperties(batchOperateLogDTO, operationLogInfoDO);
        if (CollectionUtils.isNotEmpty(batchOperateLogDTO.getBatchOperateLogContentList())) {
            operationLogInfoDO.setOperatorContent(JSONObject.toJSONString(batchOperateLogDTO.getBatchOperateLogContentList()));
            operationLogInfoDO.setOperatorStatus(getOperateStatus(batchOperateLogDTO.getBatchOperateLogContentList()).getCode());
        }
        return operationLogInfoDO;
    }

    /**
     * DO对象转化为DTO对象
     * @param operationLogInfoDO 操作日志DO对象
     * @return DTO对象
     */
    public BatchOperateLogDTO convertToDTO(OperationLogInfoDO operationLogInfoDO) {
        BatchOperateLogDTO batchOperateLogDTO = new BatchOperateLogDTO();
        BeanUtils.copyProperties(operationLogInfoDO, batchOperateLogDTO);
        if (StringUtils.isNotBlank(operationLogInfoDO.getOperatorContent())) {
            batchOperateLogDTO.setBatchOperateLogContentList(JSONObject.parseObject(operationLogInfoDO.getOperatorContent(), new TypeReference<List<BatchOperateLogContentBO>>() {
            }));
        }
        return batchOperateLogDTO;
    }

    /**
     * DO对象转化为VO对象
     * @param managementGroupDO 管理组DO对象
     * @return VO对象
     */
    public BatchUpdateVO convert(ManagementGroupDO managementGroupDO) {
        BatchUpdateVO batchUpdateVO = new BatchUpdateVO();
        User user = accountService.getUserByAccountId(managementGroupDO.getCreatorId());
        if (user != null) {
            batchUpdateVO.setOperator(user.getUserName());
        }
        batchUpdateVO.setId(managementGroupDO.getId());
        batchUpdateVO.setOperateDate(managementGroupDO.getGmtCreate());
        batchUpdateVO.setStatus(getGroupStatusByGroupId(managementGroupDO.getId()).getCode());
        batchUpdateVO.setRate(getSuccessRateInGroup(managementGroupDO.getId()));
        ManagementGroupTypeEnum managementGroupTypeEnum = ManagementGroupTypeEnum.codeOf(managementGroupDO.getType());
        if (managementGroupTypeEnum != null) {
            batchUpdateVO.setOperateType(getOperateTypeEnum(managementGroupTypeEnum).getCode());
        }
        return batchUpdateVO;
    }

    /**
     * 根据管理组类型获取操作类型
     * @param typeEnum 管理组类型
     * @return 操作日志类型
     */
    private OperationLogTypeEnum getOperateTypeEnum(ManagementGroupTypeEnum typeEnum) {
        OperationLogTypeEnum logTypeEnum = null;
        switch (typeEnum) {
            case BATCH_UPDATE_GROUP_NAME:
                logTypeEnum = OperationLogTypeEnum.BATCH_UPDATE_GROUP_NAME;
                break;
            case BATCH_UPDATE_GROUP_NOTICE:
                logTypeEnum = OperationLogTypeEnum.BATCH_UPDATE_GROUP_NOTICE;
                break;
            case BATCH_UPDATE_GROUP_OWNER:
                logTypeEnum = OperationLogTypeEnum.BATCH_UPDATE_GROUP_OWNER;
                break;
            case BATCH_UPDATE_GROUP_ADMIN:
                logTypeEnum = OperationLogTypeEnum.BATCH_UPDATE_GROUP_ADMIN;
                break;
            default:
                break;
        }
        return logTypeEnum;
    }

    /**
     * 当一个群聊中涉及到多个请求操作的时候获取操作状态
     *  1、存在任意一个等待 --> 等待中
     *  2、存在任意一个失败 --> 失败
     *  3、全部成功 --> 成功
     * @param batchOperateLogContentList 群聊的更新详情列表
     * @return 更新状态
     */
    private OperationLogStatusEnum getOperateStatus(List<BatchOperateLogContentBO> batchOperateLogContentList) {
        if (CollectionUtils.isEmpty(batchOperateLogContentList)) {
            return OperationLogStatusEnum.FAIL;
        }
        Map<Byte, List<BatchOperateLogContentBO>> contentMap = batchOperateLogContentList.stream().collect(Collectors.groupingBy(BatchOperateLogContentBO::getOperateState));
        int failCount = Optional.ofNullable(contentMap.get(OperationLogStatusEnum.FAIL.getCode())).orElse(new ArrayList<>()).size();
        int waitCount = Optional.ofNullable(contentMap.get(OperationLogStatusEnum.WAIT.getCode())).orElse(new ArrayList<>()).size();
        if (waitCount > 0) {
            return OperationLogStatusEnum.WAIT;
        }
        if (failCount > 0) {
            return OperationLogStatusEnum.FAIL;
        }
        return OperationLogStatusEnum.SUCCESS;
    }

    /**
     * 计算一个操作组的状态
     *  1、存在任意一个等待 --> 等待中
     *  2、存在任意一个失败 --> 失败
     *  3、全部成功 --> 成功
     * @param groupId 操作组id
     * @return 操作状态枚举
     */
    private OperationLogStatusEnum getGroupStatusByGroupId(Long groupId) {
        if (!NumberUtils.validLong(groupId)) {
            return OperationLogStatusEnum.FAIL;
        }
        List<OperationLogInfoDO> operationLogInfoList = operationLogInfoService.queryLogInfoListByGroupId(groupId);
        if (CollectionUtils.isEmpty(operationLogInfoList)) {
            return OperationLogStatusEnum.FAIL;
        }
        Map<Byte, List<OperationLogInfoDO>> operateMap = operationLogInfoList.stream().collect(Collectors.groupingBy(OperationLogInfoDO::getOperatorStatus));
        int failCount = 0;
        if (operateMap.containsKey(OperationLogStatusEnum.FAIL.getCode())) {
            failCount = operateMap.get(OperationLogStatusEnum.FAIL.getCode()).size();
        }
        int waitCount = 0;
        if (operateMap.containsKey(OperationLogStatusEnum.WAIT.getCode())) {
            waitCount = operateMap.get(OperationLogStatusEnum.WAIT.getCode()).size();
        }
        if (waitCount > 0) {
            return OperationLogStatusEnum.WAIT;
        }
        if (failCount > 0) {
            return OperationLogStatusEnum.FAIL;
        }
        return OperationLogStatusEnum.SUCCESS;
    }

    /**
     * 获取一个操作组内的成功率
     * @param groupId 操作组id
     * @return 成功率
     */
    private String getSuccessRateInGroup(Long groupId) {
        if (!NumberUtils.validLong(groupId)) {
            return "0%";
        }
        List<OperationLogInfoDO> operationLogInfoList = operationLogInfoService.queryLogInfoListByGroupId(groupId);
        if (CollectionUtils.isEmpty(operationLogInfoList)) {
            return "0%";
        }
        Map<Byte, List<OperationLogInfoDO>> operateMap = operationLogInfoList.stream().collect(Collectors.groupingBy(OperationLogInfoDO::getOperatorStatus));
        int successCount = 0;
        if (operateMap.containsKey(OperationLogStatusEnum.SUCCESS.getCode())) {
            successCount = operateMap.get(OperationLogStatusEnum.SUCCESS.getCode()).size();
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(2);
        return numberFormat.format((float) successCount / (float) operationLogInfoList.size() * 100) + "%";
    }

    /**
     * 查询群名称
     * @param chatId 群聊id
     * @return 群名称
     */
    private String queryChatName(String chatId) {
        if (StringUtils.isBlank(chatId)) {
            return null;
        }
        WechatGroupVO wechatGroup = wechatGroupService.getWechatGroupVOByChatId(chatId);
        if (wechatGroup == null) {
            return null;
        }
        return wechatGroup.getName();
    }

    /**
     * 获取批量操作日志内容列表
     * @param contentStr 内容字符串
     * @return 内容列表
     */
    public List<String> getBatchOperateLogContentList(String contentStr) {
        List<BatchOperateLogContentBO> batchOperateLogContentList = JSONObject.parseObject(contentStr, new TypeReference<List<BatchOperateLogContentBO>>() {
        });
        return getBatchOperateLogContentList(batchOperateLogContentList);
    }

    /**
     * 获取批量操作日志内容列表
     * @param logContentList 批量操作内容列表
     * @return 内容列表
     */
    public List<String> getBatchOperateLogContentList(List<BatchOperateLogContentBO> logContentList) {
        ArrayList<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(logContentList)) {
            return result;
        }
        for (BatchOperateLogContentBO batchOperateLogContentBO : logContentList) {
            String logShowStr = abstractLogFactory.getLogShowStr(batchOperateLogContentBO);
            if (StringUtils.isBlank(logShowStr)) {
                continue;
            }
            result.add(logShowStr);
        }
        return result;
    }

}
