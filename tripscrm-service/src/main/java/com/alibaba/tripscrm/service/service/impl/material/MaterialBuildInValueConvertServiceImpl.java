package com.alibaba.tripscrm.service.service.impl.material;

import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.material.MaterialBuildInValueEnum;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.response.MaterialContentConvertResult;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.material.MaterialContentConvertService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @date 2023-09-14 17:52:52
 */
@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MaterialBuildInValueConvertServiceImpl implements MaterialContentConvertService {
    private final WechatUserService wechatUserService;
    private final WechatGroupService wechatGroupService;
    private final WechatCustomerService wechatCustomerService;

    private final Map<MaterialBuildInValueEnum, BiConsumer<MaterialContentConvertContext, MaterialContentConvertResult>> functionMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        functionMap.put(MaterialBuildInValueEnum.WECHAT_USER_NAME, this::getWechatUserName);
        functionMap.put(MaterialBuildInValueEnum.WECHAT_CUSTOMER_NAME, this::getWechatCustomerName);
        functionMap.put(MaterialBuildInValueEnum.WECHAT_GROUP_CUSTOMER_NAME, this::getWechatGroupCustomerName);
        functionMap.put(MaterialBuildInValueEnum.WECHAT_GROUP_AT_CUSTOMER_NAME, this::getWechatGroupAtCustomerName);
        functionMap.put(MaterialBuildInValueEnum.WECHAT_GROUP_NAME, this::getWechatGroupName);
        functionMap.put(MaterialBuildInValueEnum.WECHAT_GROUP_AT_ALL, this::getWechatGroupAtAll);
    }

    @Override
    public MaterialContentConvertResult convert(MaterialContentConvertContext context) {
        context.setOriginContent(Optional.ofNullable(context.getOriginContent()).orElse(""));
        MaterialContentConvertResult result = new MaterialContentConvertResult(context.getOriginContent(), false);
        if (!StringUtils.hasText(context.getOriginContent()) || MapUtils.isEmpty(context.getExtraInfo())) {
            return result;
        }

        result.setContent(MaterialUtils.valReplace(context.getOriginContent(), context.getExtraInfo()));
        MaterialBuildInValueEnum[] materialBuildInValueEnums = MaterialBuildInValueEnum.values();
        for (MaterialBuildInValueEnum materialBuildInValueEnum : materialBuildInValueEnums) {
            if (!functionMap.containsKey(materialBuildInValueEnum) || !result.getContent().contains(materialBuildInValueEnum.getKey())) {
                continue;
            }
            functionMap.get(materialBuildInValueEnum).accept(context, result);
        }

        return result;
    }


    private void getWechatUserName(MaterialContentConvertContext context, MaterialContentConvertResult result) {
        String userId = context.getWechatUserId();
        if (!StringUtils.hasLength(userId)) {
            throw new RuntimeException("wechatUserId不可为空");
        }

        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(userId));
        if (CollectionUtils.isEmpty(wechatUserList)) {
            throw new RuntimeException("企微成员信息不存在");
        }

        String wechatUserName = wechatUserList.get(0).getName();
        result.setContent(StringUtils.replace(result.getContent(), MaterialBuildInValueEnum.WECHAT_USER_NAME.getKey(), wechatUserName));
    }

    private void getWechatCustomerName(MaterialContentConvertContext context, MaterialContentConvertResult result) {
        String externalUserId = context.getExternalUserId();
        if (!StringUtils.hasLength(externalUserId)) {
            throw new RuntimeException("企微客户Id映射信息不存在");
        }

        List<WechatCustomerVO> wechatCustomerVOList = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(externalUserId));
        if (CollectionUtils.isEmpty(wechatCustomerVOList)) {
            throw new RuntimeException("企微客户信息不存在");
        }

        String wechatCustomerName = wechatCustomerVOList.get(0).getName();
        result.setContent(StringUtils.replace(result.getContent(), MaterialBuildInValueEnum.WECHAT_CUSTOMER_NAME.getKey(), wechatCustomerName));
    }

    private void getWechatGroupCustomerName(MaterialContentConvertContext context, MaterialContentConvertResult result) {
        String name = StringUtils.hasText(context.getGroupNickName()) ? context.getGroupNickName() : context.getCustomerName();
        result.setContent(StringUtils.replace(result.getContent(), MaterialBuildInValueEnum.WECHAT_GROUP_CUSTOMER_NAME.getKey(), name));
    }

    private void getWechatGroupAtCustomerName(MaterialContentConvertContext context, MaterialContentConvertResult result) {
        String name = StringUtils.hasText(context.getGroupNickName()) ? context.getGroupNickName() : context.getCustomerName();
        result.setContent(StringUtils.replace(result.getContent(), MaterialBuildInValueEnum.WECHAT_GROUP_AT_CUSTOMER_NAME.getKey(), "@" + name));
    }

    private void getWechatGroupName(MaterialContentConvertContext context, MaterialContentConvertResult result) {
        String chatId = context.getChatId();
        if (!StringUtils.hasLength(chatId)) {
            throw new RuntimeException("客户群Id为空");
        }

        WechatGroupVO wechatGroupVO = wechatGroupService.getWechatGroupVOByChatId(chatId);
        if (Objects.isNull(wechatGroupVO)) {
            throw new RuntimeException("客户群信息不存在");
        }

        String groupName = wechatGroupVO.getName();
        result.setContent(StringUtils.replace(result.getContent(), MaterialBuildInValueEnum.WECHAT_GROUP_NAME.getKey(), groupName));
    }

    private void getWechatGroupAtAll(MaterialContentConvertContext context, MaterialContentConvertResult result) {
        result.setAtAll(true);
        result.setContent(StringUtils.replace(result.getContent(), MaterialBuildInValueEnum.WECHAT_GROUP_AT_ALL.getKey(), ""));
    }
}
