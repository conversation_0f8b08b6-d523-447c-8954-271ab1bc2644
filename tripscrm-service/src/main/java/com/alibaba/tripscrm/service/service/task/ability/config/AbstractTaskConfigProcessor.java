package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 抽象任务配置处理器
 *
 * <AUTHOR>
 * @date 2023-12-30 15:26:28
 */
@Slf4j
@Component
public abstract class AbstractTaskConfigProcessor implements TaskConfigProcessor {

    @Resource
    private AccountService accountService;

    @Override
    public JSONObject getExtraInfo(TaskInfoDO taskInfoDO) {
        if (!StringUtils.hasText(taskInfoDO.getExtInfo())) {
            return new JSONObject();
        }

        return JSONObject.parseObject(taskInfoDO.getExtInfo());
    }

    @Override
    public TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName) {
        TaskInfoDO taskInfoDO = new TaskInfoDO();
        taskInfoDO.setName(taskName);
        taskInfoDO.setActivityId(activityId);
        taskInfoDO.setType(getTaskType().getCode());
        taskInfoDO.setCreator(accountService.getUserNameAndEmpId()== null ? "刘育龙-**********" : accountService.getUserNameAndEmpId());
        User userInWebThread = accountService.getUserInWebThread();
        if (userInWebThread == null){
            userInWebThread = new User();
            userInWebThread.setUserName("刘育龙");
            userInWebThread.setUserId("**********");
            userInWebThread.setIsInnerStaff(true);
        }
        taskInfoDO.setLastOperatorId(userInWebThread.getUserId());
        taskInfoDO.setLastOperatorName(userInWebThread.getUserName());
        taskInfoDO.setManager(accountService.getUserNameAndEmpId()== null ? "刘育龙-**********" : accountService.getUserNameAndEmpId());
        taskInfoDO.setSendUserId("");
        taskInfoDO.setCustomerConfig("[]");
        taskInfoDO.setNotDisturbTime("");
        taskInfoDO.setStatus(TaskStatusEnum.EDITING.getStatus());
        taskInfoDO.setSpaceId(NumberUtils.validLong(spaceId) ? spaceId : SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        taskInfoDO.setOffsetTime(0L);
        taskInfoDO.setExecuteTime("");
        taskInfoDO.setAbType((byte) 0);
        taskInfoDO.setEnv(EnvUtils.getEnvironment());
        taskInfoDO.setEventSourceId(-1L);
        taskInfoDO.setExtInfo("{}");
        taskInfoDO.setConfig("{}");
        return taskInfoDO;
    }

    @PostConstruct
    public void init() {
        TaskConfigProcessorFactory.registry(getTaskType(), this);
    }
}
