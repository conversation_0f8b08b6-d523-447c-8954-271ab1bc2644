package com.alibaba.tripscrm.service.service.impl.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.TaskMessageTypeMapper;
import com.alibaba.tripscrm.dal.model.domain.data.TaskMessageTypeDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.task.TaskMessageLimitUnitEnum;
import com.alibaba.tripscrm.service.model.vo.task.TaskMessageSceneVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskMessageTypeVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.base.TaskMessageSceneService;
import com.alibaba.tripscrm.service.service.task.base.TaskMessageTypeService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-19 18:29:31
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskMessageTypeServiceImpl implements TaskMessageTypeService {
    private final TaskMessageTypeMapper taskMessageTypeMapper;
    private final TaskMessageSceneService taskMessageSceneService;

    @Override
    public TaskMessageTypeVO getById(Long id) {
        if (!NumberUtils.validLong(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return convert(taskMessageTypeMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<TaskMessageTypeVO> listByTaskType(TaskType taskType) {
        if (Objects.isNull(taskType)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<TaskMessageTypeDO> taskMessageTypeDOList = taskMessageTypeMapper.listByTaskType(taskType.getCode());
        if (CollectionUtils.isEmpty(taskMessageTypeDOList)) {
            return new ArrayList<>();
        }

        List<TaskMessageTypeVO> taskMessageTypeVOList = taskMessageTypeDOList.stream()
                .map(this::convert)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        taskMessageTypeVOList.forEach(taskMessageType -> taskMessageType.setMessageScenes(taskMessageSceneService.listByTaskMessageTypeId(taskMessageType.getId())));
        return taskMessageTypeVOList;
    }

    @Override
    @Cacheable(key = "#taskType.getCode()", value = "taskMessageTypeCacheManager")
    public List<TaskMessageTypeVO> listCacheByTaskType(TaskType taskType) {
        return listByTaskType(taskType);
    }

    @Override
    public Integer batchUpsert(List<TaskMessageTypeVO> list) {
        if (CollectionUtils.isEmpty(list) || list.stream().anyMatch(Objects::isNull)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        int res = 0;
        for (TaskMessageTypeVO taskMessageTypeVO : list) {
            res += taskMessageTypeMapper.upsert(convert(taskMessageTypeVO));
            // upsert 回填的 id 是 0，还得查一遍
            TaskMessageTypeDO taskMessageTypeDO = taskMessageTypeMapper.selectByTaskTypeAndName(taskMessageTypeVO.getTaskType(), taskMessageTypeVO.getName());
            List<TaskMessageSceneVO> messageScenes = taskMessageTypeVO.getMessageScenes();
            if (CollectionUtils.isEmpty(messageScenes)) {
                continue;
            }

            for (TaskMessageSceneVO taskMessageSceneVO : messageScenes) {
                taskMessageSceneVO.setTaskMessageTypeId(taskMessageTypeDO.getId());
            }
            List<TaskMessageSceneVO> oldMessageScenes = taskMessageSceneService.listByTaskMessageTypeId(taskMessageTypeDO.getId());
            Map<String, TaskMessageSceneVO> taskMessageSceneMap = messageScenes.stream().filter(taskMessageSceneVO -> Objects.nonNull(taskMessageSceneVO) && StringUtils.hasLength(taskMessageSceneVO.getName())).collect(Collectors.toMap(TaskMessageSceneVO::getName, taskMessageSceneVO -> taskMessageSceneVO));
            List<Long> deleteIds = new ArrayList<>();
            for (TaskMessageSceneVO taskMessageSceneVO : oldMessageScenes) {
                if (!taskMessageSceneMap.containsKey(taskMessageSceneVO.getName()) && NumberUtils.validLong(taskMessageSceneVO.getId())) {
                    deleteIds.add(taskMessageSceneVO.getId());
                }
            }
            Integer deleteEffectLines = 0;
            if (!CollectionUtils.isEmpty(deleteIds)) {
                deleteEffectLines = taskMessageSceneService.deleteByIds(deleteIds);
            }
            Integer upsertEffectLines =  taskMessageSceneService.batchUpsert(messageScenes);
            if (deleteEffectLines + upsertEffectLines != messageScenes.size()) {
                PlatformLogUtil.logFail("存在更新失败的任务场景", LogListUtil.newArrayList(messageScenes, deleteIds, upsertEffectLines, deleteEffectLines));
            }
            taskMessageSceneService.batchUpsert(messageScenes);
        }
        return res;
    }

    @Override
    public Integer updateById(TaskMessageTypeVO taskMessageTypeVO) {
        if (Objects.isNull(taskMessageTypeVO) || !NumberUtils.validLong(taskMessageTypeVO.getId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return taskMessageTypeMapper.insert(convert(taskMessageTypeVO));
    }

    @Override
    @AteyeInvoker(description = "删除任务场景", paraDesc = "id")
    public Integer deleteById(Long id) {
        if (!NumberUtils.validLong(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        return taskMessageTypeMapper.deleteByPrimaryKey(id);
    }

    private TaskMessageTypeDO convert(TaskMessageTypeVO taskMessageTypeVO) {
        if (Objects.isNull(taskMessageTypeVO)) {
            return null;
        }

        TaskMessageTypeDO taskMessageTypeDO = new TaskMessageTypeDO();
        taskMessageTypeDO.setId(taskMessageTypeVO.getId());
        taskMessageTypeDO.setTaskType(taskMessageTypeVO.getTaskType());
        taskMessageTypeDO.setType(taskMessageTypeVO.getType().byteValue());
        taskMessageTypeDO.setLimitType(taskMessageTypeVO.getLimitType().byteValue());
        taskMessageTypeDO.setName(taskMessageTypeVO.getName());
        JSONObject jsonConfig = new JSONObject();
        jsonConfig.put("limit", taskMessageTypeVO.getLimit());
        jsonConfig.put("unit", Optional.ofNullable(taskMessageTypeVO.getLimitUnit()).orElse(TaskMessageLimitUnitEnum.MESSAGE.getCode()));
        taskMessageTypeDO.setConfig(jsonConfig.toJSONString());
        return taskMessageTypeDO;
    }

    private TaskMessageTypeVO convert(TaskMessageTypeDO taskMessageTypeDO) {
        if (Objects.isNull(taskMessageTypeDO)) {
            return null;
        }

        TaskMessageTypeVO taskMessageTypeVO = new TaskMessageTypeVO();
        taskMessageTypeVO.setId(taskMessageTypeDO.getId());
        taskMessageTypeVO.setName(taskMessageTypeDO.getName());
        taskMessageTypeVO.setType(taskMessageTypeDO.getType().intValue());
        taskMessageTypeVO.setTaskType(taskMessageTypeDO.getTaskType());
        taskMessageTypeVO.setLimitType(taskMessageTypeDO.getLimitType().intValue());
        if (StringUtils.hasText(taskMessageTypeDO.getConfig())) {
            JSONObject config = JSONObject.parseObject(taskMessageTypeDO.getConfig());
            taskMessageTypeVO.setLimit(config.getLong("limit"));
            taskMessageTypeVO.setLimitUnit(config.getInteger("unit"));

        }
        return taskMessageTypeVO;
    }
}
