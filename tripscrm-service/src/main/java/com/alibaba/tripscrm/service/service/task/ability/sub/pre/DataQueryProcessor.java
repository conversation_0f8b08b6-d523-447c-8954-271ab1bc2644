package com.alibaba.tripscrm.service.service.task.ability.sub.pre;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.middleware.OssClientManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.alibaba.tripscrm.service.constant.TaskConstant.SUB_TASK_EXT_OSS_PATH_KEY;

/**
 * 任务执行_子任务_数据查询
 *
 * <AUTHOR>
 * @since 2024/4/22 11:32
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class DataQueryProcessor implements ExecuteProcessor {
    private final OssClientManager ossClientManager;
    private final LdbTairManager ldbTairManager;
    private final SubTaskInstanceService subTaskInstanceService;

    @Override
    @TaskExecuteLog("任务执行_子任务_数据查询")
    public TripSCRMResult<Void> process(TaskExecuteContext context) throws Exception {
        Long taskId = context.getTaskId();
        Long instanceId = context.getInstanceId();
        TaskDataVO taskDataVO = context.getTaskDataVO();
        // 从oss查询数据分片
        if (Objects.isNull(taskDataVO)) {
            PlatformLogUtil.logInfo("子任务执行-数据查询，开始", LogListUtil.newArrayList(taskId, instanceId));
            taskDataVO = queryTargetData(context);
            PlatformLogUtil.logInfo("子任务执行-数据查询，结束", LogListUtil.newArrayList(taskId, instanceId, taskDataVO.getTotalCount()));
        }


        TodoTaskVO todoTaskVO = new TodoTaskVO();
        todoTaskVO.setTaskId(context.getTaskId());
        todoTaskVO.setMainTaskInstanceId(context.getMainTaskInstanceId());
        todoTaskVO.setInstanceId(context.getInstanceId());
        todoTaskVO.setData(taskDataVO.getData());
        // 设置到上下文中
        context.setTodoTaskVOList(Lists.newArrayList(todoTaskVO));

        // 设置到上下文中
        context.setTaskDataVO(taskDataVO);
        return TripSCRMResult.success(null);
    }

    /**
     * 读取指定数据，支持测试链路和oss分片数据读取
     *
     * @param context
     * @return
     */
    private TaskDataVO queryTargetData(TaskExecuteContext context) {
        Long instanceId = context.getInstanceId();
        if (context.getTestFlag()) {
            String subListStr = (String) ldbTairManager.get(String.valueOf(instanceId));
            List<TaskDataVO.DataBodyVO> subList = JSONArray.parseArray(subListStr, TaskDataVO.DataBodyVO.class);
            TaskDataVO taskDataVO = new TaskDataVO();
            taskDataVO.setTotalCount((long) subList.size());
            taskDataVO.setData(subList);
            return taskDataVO;
        }

        // 从oss读取分片数据
        return readDataFromOss(context.getTaskId(), instanceId);
    }

    /**
     * 从oss上读取数据分片
     *
     * @param subTaskInstanceId 子任务实例ID
     * @return
     */
    private TaskDataVO readDataFromOss(Long taskId, Long subTaskInstanceId) {
        TaskDataVO taskDataVO = new TaskDataVO();
        SubTaskInstanceDO subTaskInstanceDO = subTaskInstanceService.queryById(subTaskInstanceId);
        if (subTaskInstanceDO == null) {
            PlatformLogUtil.logFail("子任务执行-数据查询，子任务实例数据为空", LogListUtil.newArrayList(taskId, subTaskInstanceId));
            return taskDataVO;
        }

        String ossPath = MapUtils.getString(JSONObject.parseObject(subTaskInstanceDO.getExtInfo()), SUB_TASK_EXT_OSS_PATH_KEY);
        if (StringUtils.isBlank(ossPath)) {
            PlatformLogUtil.logFail("子任务执行-数据查询，oss存储路径为空", LogListUtil.newArrayList(taskId, subTaskInstanceId));
            return taskDataVO;
        }

        try {
            String data = ossClientManager.readOssFile(ossPath);
            if (StringUtils.isBlank(data)) {
                PlatformLogUtil.logFail("子任务执行-数据查询，oss数据为空", LogListUtil.newArrayList(taskId, subTaskInstanceId));
                return taskDataVO;
            }
            List<TaskDataVO.DataBodyVO> list = JSONArray.parseArray(data, TaskDataVO.DataBodyVO.class);
            taskDataVO.setData(list);
            taskDataVO.setTotalCount(CollectionUtils.isEmpty(list) ? 0L : list.size());
        } catch (Exception ex) {
            PlatformLogUtil.logException("子任务执行-数据查询，读取oss数据异常", ex.getMessage(), ex, LogListUtil.newArrayList(taskId, subTaskInstanceId));
        }
        return taskDataVO;
    }
}
