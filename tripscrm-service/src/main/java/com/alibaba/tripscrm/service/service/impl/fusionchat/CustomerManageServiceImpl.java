package com.alibaba.tripscrm.service.service.impl.fusionchat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerManageGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.RelationInfoDO;
import com.alibaba.tripscrm.dal.repository.CustomerRelationRepository;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.task.RelationInfoTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.service.common.RelationInfoService;
import com.alibaba.tripscrm.service.service.fusionchat.CustomerManageService;
import com.alibaba.tripscrm.service.service.fusionchat.CustomerManagementGroupService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.ChatGroupRequest;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerManageServiceImpl implements CustomerManageService {

    private final CustomerManagementGroupService customerManagementGroupService;
    private final RelationInfoService relationInfoService;
    private final CustomerRelationRepository customerRelationRepository;
    private final GroupRelationRepository groupRelationRepository;
    private final AccountService accountService;

    @Override
    public CustomerManageGroupDO queryGroupByRelationId(String relationId, ResourceTypeEnum relationType) {
        // 关系查询
        List<RelationInfoDO> relationInfoList = relationInfoService.queryByTypeAndSourceInfo(
                RelationInfoTypeEnum.CUSTOMER_RELATION_MANAGEMENT_GROUP, relationType, relationId);
        if (CollectionUtils.isEmpty(relationInfoList)) {
            return null;
        }
        // 管理组查询
        return customerManagementGroupService.selectById(Long.valueOf(relationInfoList.get(0).getTargetId()));
    }

    @Override
    @AteyeInvoker(description = "查询客户管理组", paraDesc = "userId&externalUserId&corpId")
    public CustomerManageGroupDO queryGroupByUser(String userId, String externalUserId, String corpId) {
        CustomerRelationDO customerRelationDO = customerRelationRepository.getByUniqueKey(userId, externalUserId, corpId);
        if (customerRelationDO == null) {
            return null;
        }
        return queryGroupByRelationId(String.valueOf(customerRelationDO.getId()), ResourceTypeEnum.CUSTOMER_RELATION);
    }

    @Override
    public CustomerManageGroupDO queryGroupByGroup(String userId, String chatId, String corpId) {
        List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserIdList(
                groupRelationRepository.chatId2ShardingKey(chatId), Lists.newArrayList(userId), corpId, GroupRelationStatusEnum.MEMBER.getCode().byteValue());
        if (CollectionUtils.isEmpty(groupRelationList)) {
            return null;
        }
        return queryGroupByRelationId(String.valueOf(groupRelationList.get(0).getId()), ResourceTypeEnum.GROUP_RELATION);
    }

    @Override
    public Boolean chatGroup(ChatGroupRequest request) {
        // 数据校验
        if (request == null || StringUtils.isBlank(request.getUserId()) || StringUtils.isBlank(request.getCorpId()) || request.getGroupId() == null) {
            PlatformLogUtil.logFail("聚合聊天客户分组参数缺失", LogListUtil.newArrayList(request));
            return false;
        }
        Long relationId = getRelationId(request.getUserId(), request.getChatId(), ChatTypeEnum.parse(request.getChatType()), request.getCorpId());
        ResourceTypeEnum typeEnum = getRelationType(ChatTypeEnum.parse(request.getChatType()));
        if (relationId == null || typeEnum == null) {
            PlatformLogUtil.logFail("参与分组的成员和客户没有关系", LogListUtil.newArrayList(request));
            return false;
        }
        // 移除操作
        if (request.getRemove()) {
            return relationInfoService.deleteByInfo(RelationInfoTypeEnum.CUSTOMER_RELATION_MANAGEMENT_GROUP, typeEnum
                    , String.valueOf(relationId), ResourceTypeEnum.CUSTOMER_MANAGE_GROUP, String.valueOf(request.getGroupId()));
        }
        // 新增操作
        return addGroupRelation(relationId, typeEnum, request.getCorpId(), String.valueOf(request.getGroupId()));
    }

    /**
     * 新增管理组关系
     * @param relationId 关系ID
     * @param typeEnum 资源类型ID
     * @return 处理结果
     */
    private Boolean addGroupRelation(Long relationId, ResourceTypeEnum typeEnum, String corpId, String groupId) {
        // 处理当前关系
        CustomerManageGroupDO customerManageGroupDO = queryGroupByRelationId(String.valueOf(relationId), typeEnum);
        if (customerManageGroupDO != null) {
            Boolean deleteResult = relationInfoService.deleteByInfo(RelationInfoTypeEnum.CUSTOMER_RELATION_MANAGEMENT_GROUP
                    , typeEnum, String.valueOf(relationId), ResourceTypeEnum.CUSTOMER_MANAGE_GROUP, String.valueOf(customerManageGroupDO.getId()));
            PlatformLogUtil.logFail("删除客户所属管理组结果", LogListUtil.newArrayList(typeEnum, relationId, groupId, deleteResult));
        }
        // 建立新关系
        RelationInfoDO relationInfoDO = new RelationInfoDO();
        relationInfoDO.setCorpId(corpId);
        relationInfoDO.setCreatorId(Optional.ofNullable(accountService.getUserInWebThread()).orElse(new User()).getUserId());
        relationInfoDO.setType(RelationInfoTypeEnum.CUSTOMER_RELATION_MANAGEMENT_GROUP.getCode());
        relationInfoDO.setSourceType(typeEnum.getCode().byteValue());
        relationInfoDO.setSourceId(String.valueOf(relationId));
        relationInfoDO.setTargetType(ResourceTypeEnum.CUSTOMER_MANAGE_GROUP.getCode().byteValue());
        relationInfoDO.setTargetId(groupId);
        return relationInfoService.addRelation(relationInfoDO);
    }

    @Override
    public Boolean existGroupRelation(Long id) {
        if (!NumberUtils.validLong(id)) {
            return false;
        }
        List<RelationInfoDO> relationInfoList = relationInfoService.queryByTypeAndTargetInfo(
                RelationInfoTypeEnum.CUSTOMER_RELATION_MANAGEMENT_GROUP, ResourceTypeEnum.CUSTOMER_MANAGE_GROUP, String.valueOf(id));
        return CollectionUtils.isNotEmpty(relationInfoList);
    }

    /**
     * 获取关系类型
     * @param chatTypeEnum 聊天类型枚举
     * @return 资源类型枚举
     */
    private ResourceTypeEnum getRelationType(ChatTypeEnum chatTypeEnum) {
        if (chatTypeEnum == null) {
            return null;
        }
        ResourceTypeEnum typeEnum = null;
        switch (chatTypeEnum) {
            case GROUP:
                typeEnum = ResourceTypeEnum.GROUP_RELATION;
                break;
            case SINGLE_FOR_CUSTOMER:
                typeEnum = ResourceTypeEnum.CUSTOMER_RELATION;
                break;
            default:
                break;
        }
        return typeEnum;
    }

    /**
     * 获取关系ID
     * @param userId 成员ID
     * @param chatId 聊天ID（externalUserId or chatId）
     * @param chatType 聊天类型
     * @return 关系ID
     */
    private Long getRelationId(String userId, String chatId, ChatTypeEnum chatType, String corpId) {
        if (chatType == null) {
            return null;
        }
        Long relationId = null;
        switch (chatType) {
            case GROUP:
                List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(chatId)
                        , Lists.newArrayList(userId), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
                if (CollectionUtils.isNotEmpty(groupRelationList)) {
                    relationId = groupRelationList.get(0).getId();
                }
                break;
            case SINGLE_FOR_CUSTOMER:
                CustomerRelationDO customerRelationDO = customerRelationRepository.getByUniqueKey(userId, chatId, corpId);
                if (customerRelationDO != null) {
                    relationId = customerRelationDO.getId();
                }
                break;
            default:
                break;
        }
        return relationId;
    }
}
