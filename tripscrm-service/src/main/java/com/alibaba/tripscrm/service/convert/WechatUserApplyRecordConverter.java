package com.alibaba.tripscrm.service.convert;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserApplyRecordDO;
import com.alibaba.tripscrm.service.model.vo.isv.CreateWeComRecordVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatUserApplyRecordVO;
import com.alibaba.tripscrm.service.model.dto.wechat.CreateWeComRecordDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatUserApplyRecordDTO;
import com.alibaba.tripscrm.service.service.CreateWeComRecordService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * WechatUserApplyRecord相关数据防腐层
 *
 * <AUTHOR>
 * @since 2024-01-21 14:46
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatUserApplyRecordConverter {

    private final CreateWeComRecordService createWeComRecordService;
    private final AccountService accountService;
    private final CreateWeComRecordConverter createWeComRecordConverter;

    private final static String APPLY_SUCCESS_TIME = "applySuccessTime";
    private final static String ORDER_ID = "orderId";
    private final static String ROBOT_NAME = "robotName";

    /**
     * WechatUserApplyRecordVO 转 WechatUserApplyRecordDTO
     */
    public WechatUserApplyRecordDTO convert2DTO(WechatUserApplyRecordVO wechatUserApplyRecordVO) {
        if (Objects.isNull(wechatUserApplyRecordVO)) {
            return null;
        }
        WechatUserApplyRecordDTO wechatUserApplyRecordDTO = new WechatUserApplyRecordDTO();
        BeanUtils.copyProperties(wechatUserApplyRecordVO, wechatUserApplyRecordDTO);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(APPLY_SUCCESS_TIME, wechatUserApplyRecordVO.getApplySuccessTime());
        wechatUserApplyRecordDTO.setExtInfo(jsonObject.toJSONString());
        return wechatUserApplyRecordDTO;
    }

    /**
     * WechatUserApplyRecordDTO 转 WechatUserApplyRecordVO
     */
    public WechatUserApplyRecordVO convert2VO(WechatUserApplyRecordDTO wechatUserApplyRecordDTO) {
        if (Objects.isNull(wechatUserApplyRecordDTO)) {
            return null;
        }
        WechatUserApplyRecordVO wechatUserApplyRecordVO = new WechatUserApplyRecordVO();
        CreateWeComRecordDTO createWeComRecordDTO = createWeComRecordService.selectById(wechatUserApplyRecordDTO.getCreateWeComRecordId());
        if (Objects.nonNull(createWeComRecordDTO)) {
            CreateWeComRecordVO createWeComRecordVO = createWeComRecordConverter.convert2VO(createWeComRecordDTO);
            wechatUserApplyRecordVO = convert2VO(createWeComRecordVO);
        }
        BeanUtils.copyProperties(wechatUserApplyRecordDTO, wechatUserApplyRecordVO);
        JSONObject jsonObject = JSONObject.parseObject(wechatUserApplyRecordDTO.getExtInfo());
        wechatUserApplyRecordVO.setApplySuccessTime(jsonObject.getDate(APPLY_SUCCESS_TIME));
        wechatUserApplyRecordVO.setUserName(jsonObject.getString(ROBOT_NAME));
        return wechatUserApplyRecordVO;
    }

    /**
     * WechatUserApplyRecordDTO 转 WechatUserApplyRecordDO
     */
    public WechatUserApplyRecordDO convert2DO(WechatUserApplyRecordDTO wechatUserApplyRecordDTO) {
        if (Objects.isNull(wechatUserApplyRecordDTO)) {
            return null;
        }
        WechatUserApplyRecordDO wechatUserApplyRecordDO = new WechatUserApplyRecordDO();
        BeanUtils.copyProperties(wechatUserApplyRecordDTO, wechatUserApplyRecordDO);
        return wechatUserApplyRecordDO;
    }

    /**
     * WechatUserApplyRecordDO 转 WechatUserApplyRecordDTO
     */
    public WechatUserApplyRecordDTO convert2DTO(WechatUserApplyRecordDO wechatUserApplyRecordDO) {
        if (Objects.isNull(wechatUserApplyRecordDO)) {
            return null;
        }
        WechatUserApplyRecordDTO wechatUserApplyRecordDTO = new WechatUserApplyRecordDTO();
        BeanUtils.copyProperties(wechatUserApplyRecordDO, wechatUserApplyRecordDTO);
        return wechatUserApplyRecordDTO;
    }

    public WechatUserApplyRecordVO convert2VO(CreateWeComRecordVO createWeComRecordVO) {
        if (Objects.isNull(createWeComRecordVO)) {
            return null;
        }
        WechatUserApplyRecordVO wechatUserApplyRecordVO = new WechatUserApplyRecordVO();
        wechatUserApplyRecordVO.setIsvType(createWeComRecordVO.getIsvType());
        wechatUserApplyRecordVO.setIsvName(createWeComRecordVO.getIsvName());
        wechatUserApplyRecordVO.setCorpId(createWeComRecordVO.getCorpId());
        wechatUserApplyRecordVO.setCorpName(createWeComRecordVO.getCorpName());
        wechatUserApplyRecordVO.setDepartmentId(createWeComRecordVO.getDepartmentId());
        wechatUserApplyRecordVO.setDepartmentName(createWeComRecordVO.getDepartmentName());
        wechatUserApplyRecordVO.setInviteUrl(createWeComRecordVO.getInviteUrl());
        wechatUserApplyRecordVO.setApplyCount(createWeComRecordVO.getApplyCount());
        wechatUserApplyRecordVO.setCreatorId(createWeComRecordVO.getCreatorId());
        wechatUserApplyRecordVO.setCreatorName(createWeComRecordVO.getCreatorName());
        wechatUserApplyRecordVO.setApplyCount(createWeComRecordVO.getApplyCount());
        wechatUserApplyRecordVO.setOrderId(createWeComRecordVO.getOrderId());
        wechatUserApplyRecordVO.setGmtCreate(createWeComRecordVO.getGmtCreate());
        return wechatUserApplyRecordVO;
    }
}
