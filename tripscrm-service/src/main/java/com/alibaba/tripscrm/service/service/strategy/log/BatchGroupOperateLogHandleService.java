package com.alibaba.tripscrm.service.service.strategy.log;

import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.enums.system.OperationLogStatusEnum;
import com.alibaba.tripscrm.service.model.domain.log.BatchOperateLogContentBO;
import com.tmall.beehive.common.lang.Strings;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/6/4 14:50
 **/
@Service
public class BatchGroupOperateLogHandleService extends BatchOperateLogHandleService {

    @Override
    public LogShowTypeEnum handleType() {
        return LogShowTypeEnum.BATCH_GROUP_UPDATE;
    }

    @Override
    public String getLogContentStr(BatchOperateLogContentBO sopLogContentBO) {
        OperationLogStatusEnum statusEnum = Optional.ofNullable(OperationLogStatusEnum.codeOf(sopLogContentBO.getOperateState())).orElse(OperationLogStatusEnum.FAIL);
        String logContentFormat = statusEnum.getName() + "【%s】";
        if (StringUtils.isBlank(logContentFormat)) {
            return Strings.EMPTY;
        }
        String message;
        if (StringUtils.isNotBlank(sopLogContentBO.getMessage())) {
            message = sopLogContentBO.getMessage();
        } else {
            message = String.join(",", sopLogContentBO.getInvalidTargetNameList());
        }
        return String.format(logContentFormat, message);
    }
}
