package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.CustomerRelationMapper;
import com.alibaba.tripscrm.dal.mapper.tddl.TagRelationMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.TagRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.factory.ThreadPoolExecutorFactory;
import com.alibaba.tripscrm.service.service.impl.tag.ItemTagRelationOldServiceImpl;
import com.alibaba.tripscrm.service.service.impl.tag.ItemTagRelationServiceImpl;
import com.alibaba.tripscrm.service.service.impl.tag.TagRelationServiceImpl;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.WXBizMsgCryptConfig;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/12/16 16:47
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ItemTagRelationDataSyncProcessor extends JavaProcessor {
    private final ItemTagRelationServiceImpl itemTagRelationService;
    private final ItemTagRelationOldServiceImpl itemTagRelationOldService;
    private final TagRelationServiceImpl tagRelationService;
    private final TagRelationMapper tagRelationMapper;
    private final WechatGroupService wechatGroupService;
    private final CustomerRelationMapper customerRelationMapper;
    private final WechatUserService wechatUserService;
    private final WXCorpStorage wxCorpStorage;

    @Switch(description = "标签关系要同步的限定企微号下的企微客户和群聊", name = "syncWechatUserIdListStr")
    public static String syncWechatUserIdListStr = "ZhangYuanHang_1";

    private static final String DEFAULT_TAG_ID = "12154";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        if (!SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_WRITE_SWITCH) {
            PlatformLogUtil.logFail("物料标签数据同步，旧数据已经不再更新，不能再做数据同步了");
            return new ProcessResult(true);
        }

        Integer customerSyncCount = syncWechatCustomerAndGroupData();
        PlatformLogUtil.logInfo("物料标签数据同步，客户&客户群标签数据同步完毕", LogListUtil.newArrayList(customerSyncCount));
        Integer otherSyncCount = syncOtherData();
        PlatformLogUtil.logInfo("物料标签数据同步，其他物料标签数据同步完毕", LogListUtil.newArrayList(otherSyncCount));

        if (customerSyncCount == 0 && otherSyncCount == 0) {
            PlatformLogUtil.logInfo("物料标签数据同步，全部物料标签数据同步完毕，本次同步前两侧数据完全一致");
        } else {
            PlatformLogUtil.logInfo("物料标签数据同步，全部物料标签数据同步完毕，本次同步前两侧数据未完全一致", LogListUtil.newArrayList(customerSyncCount, otherSyncCount));
        }
        return new ProcessResult(true);
    }

    private Integer syncWechatCustomerAndGroupData() {
        Integer syncCount = 0;
        for (WXBizMsgCryptConfig wxBizMsgCryptConfig : wxCorpStorage.listConfig()) {
            String corpId = wxBizMsgCryptConfig.getCorpId();
            syncCount += syncCustomerAndGroupByCorpId(corpId);
        }
        return syncCount;
    }

    @AteyeInvoker(description = "给客户打默认标签")
    public Integer addDefaultCustomerTagRelation() {
        Integer addCount = 0;
        for (WXBizMsgCryptConfig wxBizMsgCryptConfig : wxCorpStorage.listConfig()) {
            String corpId = wxBizMsgCryptConfig.getCorpId();
            addCount += addDefaultCustomerTagRelation(corpId);
        }
        return addCount;
    }

    private Integer addDefaultCustomerTagRelation(String corpId) {
        AtomicInteger syncCount = new AtomicInteger(0);
        try {
            SpaceInfoThreadLocalUtils.setCorpId(corpId);
            List<WechatUserDTO> wechatUserList = wechatUserService.listAll();
            Set<String> syncWechatUserIdSet = Arrays.stream(syncWechatUserIdListStr.split(",")).collect(Collectors.toSet());

            List<CompletableFuture<Boolean>> completableFutureList = new ArrayList<>();
            for (WechatUserDTO wechatUser : wechatUserList) {
                if (!EnvUtils.isOnline() && !syncWechatUserIdSet.contains(wechatUser.getUserId())) {
                    PlatformLogUtil.logInfo("客户默认标签全量打标 ，企微成员不在同步白名单", LogListUtil.newArrayList(wechatUser));
                    continue;
                }

                PlatformLogUtil.logInfo("客户默认标签全量打标，开始同步单个企微成员下的客户标签信息", LogListUtil.newArrayList(wechatUser));
                CompletableFuture<Boolean> completableFuture = CompletableFuture.supplyAsync(() -> {
                    syncCount.addAndGet(addDefaultCustomerByFollowUser(wechatUser.getUserId()));
                    return true;
                }, ThreadPoolExecutorFactory.ITEM_TAG_RELATION_SYNC_THREAD_POOL);
                completableFutureList.add(completableFuture);
            }

            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            PlatformLogUtil.logException("客户默认标签全量打标，出现异常", e.getMessage(), e, LogListUtil.newArrayList(corpId));
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
        return syncCount.get();
    }

    private Integer addDefaultCustomerByFollowUser(String userId) {
        Integer syncCount = 0;
        for (long minId = 0; minId >= 0; ) {
            List<CustomerRelationDO> customerRelationList = customerRelationMapper.listByUserIdAndCorpIdAndMinIdLimit(userId, SpaceInfoThreadLocalUtils.getCorpId(), minId, 500);
            if (CollectionUtils.isEmpty(customerRelationList)) {
                break;
            }

            for (CustomerRelationDO customerRelation : customerRelationList) {
                ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                itemTagRelationDTO.setItemId(customerRelation.getExternalUserId() + "_" + customerRelation.getCorpId());
                itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_CUSTOMER.getCode());
                itemTagRelationDTO.setTag(DEFAULT_TAG_ID);
                itemTagRelationDTO.setDeleted((byte) 0);
                Integer addCount = tagRelationService.upsertSelective(itemTagRelationDTO);
                if (addCount > 0) {
                    syncCount++;
                }
            }
            minId = customerRelationList.stream().mapToLong(CustomerRelationDO::getId).max().orElse(-1L);
        }
        return syncCount;
    }

    private Integer syncCustomerAndGroupByCorpId(String corpId) {
        AtomicInteger syncCount = new AtomicInteger(0);
        try {
            SpaceInfoThreadLocalUtils.setCorpId(corpId);
            List<WechatUserDTO> wechatUserList = wechatUserService.listAll();
            Set<String> syncWechatUserIdSet = Arrays.stream(syncWechatUserIdListStr.split(",")).collect(Collectors.toSet());

            List<CompletableFuture<Boolean>> completableFutureList = new ArrayList<>();
            for (WechatUserDTO wechatUser : wechatUserList) {
                if (!EnvUtils.isOnline() && !syncWechatUserIdSet.contains(wechatUser.getUserId())) {
                    PlatformLogUtil.logInfo("物料标签数据同步，企微成员不在同步白名单", LogListUtil.newArrayList(wechatUser));
                    continue;
                }

                PlatformLogUtil.logInfo("物料标签数据同步，开始同步单个企微成员下的客户标签信息", LogListUtil.newArrayList(wechatUser));
                CompletableFuture<Boolean> completableFuture = CompletableFuture.supplyAsync(() -> {
                    syncCount.addAndGet(syncCustomerByFollowUser(wechatUser.getUserId()));
                    syncCount.addAndGet(syncGroupByOwner(wechatUser.getUserId()));
                    return true;
                }, ThreadPoolExecutorFactory.ITEM_TAG_RELATION_SYNC_THREAD_POOL);
                completableFutureList.add(completableFuture);
            }

            CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            PlatformLogUtil.logException("物料标签数据同步，同步客户数据异常", e.getMessage(), e, LogListUtil.newArrayList(corpId));
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
        return syncCount.get();
    }

    private Integer syncCustomerByFollowUser(String userId) {
        Integer syncCount = 0;
        for (long minId = 0; minId >= 0; ) {
            List<CustomerRelationDO> customerRelationList = customerRelationMapper.listByUserIdAndCorpIdAndMinIdLimit(userId, SpaceInfoThreadLocalUtils.getCorpId(), minId, 500);
            if (CollectionUtils.isEmpty(customerRelationList)) {
                break;
            }

            for (CustomerRelationDO customerRelation : customerRelationList) {
                if (sync(customerRelation.getExternalUserId() + "_" + customerRelation.getCorpId(), BizTypeEnum.WECHAT_CUSTOMER.getCode())) {
                    syncCount++;
                }
            }
            minId = customerRelationList.stream().mapToLong(CustomerRelationDO::getId).max().orElse(-1L);
        }
        return syncCount;
    }

    private Integer syncGroupByOwner(String userId) {
        Integer syncCount = 0;
        List<WechatGroupDO> wechatGroupList = wechatGroupService.listByOwnerUser(userId, SpaceInfoThreadLocalUtils.getCorpId());
        for (WechatGroupDO wechatGroupDO : wechatGroupList) {
            if (sync(wechatGroupDO.getChatId() + "_" + wechatGroupDO.getCorpId(), BizTypeEnum.WECHAT_GROUP.getCode())) {
                syncCount++;
            }
        }
        return syncCount;
    }

    private Integer syncOtherData() {
        Integer syncCount = 0;
        // 由于 tag_relation 表中的 item_id 不可枚举，比如个码，没有对应的数据表，因此只能扫描全表，不过这里数据量很小，问题不大。
        Map<Integer, Set<String>> itemType2ItemIdSet = fetchAllTagRelationItemIdFromDb();
        for (Integer itemType : itemType2ItemIdSet.keySet()) {
            for (String itemId : itemType2ItemIdSet.get(itemType)) {
                if (sync(itemId, itemType)) {
                    syncCount++;
                }
            }
        }
        return syncCount;
    }

    private Map<Integer, Set<String>> fetchAllTagRelationItemIdFromDb() {
        Map<Integer, Set<String>> itemType2ItemIdSet = new HashMap<>();
        for (long minId = 0; minId >= 0; ) {
            List<TagRelationDO> tagRelationList = tagRelationMapper.listByMinIdLimit(minId, 500);
            if (CollectionUtils.isEmpty(tagRelationList)) {
                break;
            }

            for (TagRelationDO tagRelationDO : tagRelationList) {
                itemType2ItemIdSet.putIfAbsent(tagRelationDO.getItemType(), new HashSet<>());
                itemType2ItemIdSet.get(tagRelationDO.getItemType()).add(tagRelationDO.getItemId());
            }

            minId = tagRelationList.stream().map(TagRelationDO::getId).mapToLong(Long::longValue).max().orElse(-1L);
        }
        return itemType2ItemIdSet;
    }

    @TairLock(value = "'updateItemTagRelation_' + #itemId + '_' + #itemType", waitMilliseconds = 2000L)
    @AteyeInvoker(description = "同步指定物料的标签信息", paraDesc = "itemId&itemType")
    public boolean sync(String itemId, Integer itemType) {
        ItemTagRelationQuery query = new ItemTagRelationQuery();
        query.setItemId(itemId);
        query.setItemType(itemType);
        query.setDeleted((byte) 0);

        List<ItemTagRelationDTO> newItemTagRelationList = itemTagRelationService.selectByCondition(query);
        List<ItemTagRelationDTO> oldItemTagRelationListOld = queryOldItemRelationList(query);
        Map<String, ItemTagRelationDTO> newTagId2TagRelationList = newItemTagRelationList.stream().collect(Collectors.toMap(ItemTagRelationDTO::getTag, a -> a, (a, b) -> a));
        Map<String, ItemTagRelationDTO> oldTagId2TagRelationList = oldItemTagRelationListOld.stream().collect(Collectors.toMap(ItemTagRelationDTO::getTag, a -> a, (a, b) -> a));

        List<String> deleteTagIdList = newTagId2TagRelationList.keySet().stream().filter(tagId -> !oldTagId2TagRelationList.containsKey(tagId)).collect(Collectors.toList());
        List<String> addTagIdList = oldTagId2TagRelationList.keySet().stream().filter(tagId -> !newTagId2TagRelationList.containsKey(tagId)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(deleteTagIdList) && CollectionUtils.isEmpty(addTagIdList)) {
            PlatformLogUtil.logInfo("单个物料标签数据同步，修旧数据相同，无需同步", LogListUtil.newArrayList(itemId, itemType));
            return false;
        }

        Integer deleteCount = deleteTagIdList.stream().map(tagId -> {
            ItemTagRelationQuery itemTagRelationQuery = new ItemTagRelationQuery();
            itemTagRelationQuery.setItemId(itemId);
            itemTagRelationQuery.setItemType(itemType);
            itemTagRelationQuery.setTag(tagId);
            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            itemTagRelationDTO.setDeleted((byte) 1);
            return itemTagRelationService.updateSelective(itemTagRelationDTO, itemTagRelationQuery);
        }).mapToInt(Integer::intValue).sum();

        List<ItemTagRelationDTO> addTagRelationList = addTagIdList.stream().map(tagId -> {
            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            itemTagRelationDTO.setItemId(itemId);
            itemTagRelationDTO.setItemType(itemType);
            itemTagRelationDTO.setTag(tagId);
            itemTagRelationDTO.setDeleted((byte) 0);
            itemTagRelationDTO.setCreatorId(oldTagId2TagRelationList.get(tagId).getCreatorId());
            itemTagRelationDTO.setCreatorName(oldTagId2TagRelationList.get(tagId).getCreatorName());
            return itemTagRelationDTO;
        }).collect(Collectors.toList());
        Integer addCount = itemTagRelationService.batchUpsertSelective(addTagRelationList);

        if (deleteCount == deleteTagIdList.size() && addCount == addTagRelationList.size()) {
            PlatformLogUtil.logInfo("单个物料标签数据同步，数据同步成功", LogListUtil.newArrayList(itemId, itemType));
        } else {
            PlatformLogUtil.logFail("单个物料标签数据同步，数据同步失败", LogListUtil.newArrayList(itemId, itemType, deleteCount, deleteTagIdList.size(), addCount, addTagRelationList.size()));
        }

        return true;
    }


    public List<ItemTagRelationDTO> queryOldItemRelationList(ItemTagRelationQuery query) {
        BizTypeEnum bizTypeEnum = BizTypeEnum.findByCode(query.getItemType());
        if (Objects.isNull(bizTypeEnum)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (!Lists.newArrayList(BizTypeEnum.WECHAT_CUSTOMER, BizTypeEnum.WECHAT_GROUP, BizTypeEnum.GROUP_MA_OLD, BizTypeEnum.PERSON_MA_OLD, BizTypeEnum.CUSTOMER_ACQUISITION_LINK).contains(bizTypeEnum)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        switch (bizTypeEnum) {
            case WECHAT_CUSTOMER:
                return tagRelationService.getWechatCustomerTagRelationOldList(query);
            case WECHAT_GROUP:
                return tagRelationService.getWechatGroupTagRelationOldList(query);
            default:
                break;
        }
        return itemTagRelationOldService.selectByCondition(query);
    }
}
