package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.domain.enums.EnterpriseWechatChatMessageTypeEnum;
import com.alibaba.tripscrm.domain.message.EnterpriseWechatChatMessageDTO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.task.AutoResponseMsgSourceEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatSenderTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationCreateParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateLastMessageParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageCreateParam;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.CreateSingleChatProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.ReceiveMessageProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.result.AbstractWechatMessageBO;
import com.alibaba.tripzoo.proxy.result.WechatCustomerMessageBO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 收到私聊的消息【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class ReceiveCustomerMessageCallbackProcessor implements ProxyCallbackProcessor {
    public static final List<String> CLOSE_CONVER_SYSTEM_MESSAGE_LIST = Stream.of("请先发送联系人验证请求", "但被对方拒收了").collect(Collectors.toList());
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private ChatConversationRepository chatConversationRepository;
    @Resource
    private CreateSingleChatProcessor createSingleChatProcessor;
    @Resource
    private ReceiveMessageProcessor receiveMessageProcessor;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private MetaqProducer metaqProducer;
    @Resource
    private AccountService accountService;
    @Resource
    private LdbTairManager ldbTairManager;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.RECEIVE_CUSTOMER_MESSAGE;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("接收企微客户私聊消息回调结果为失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return true;
        }
        PlatformLogUtil.logInfo("接收企微客户私聊消息回调结果为成功", LogListUtil.newArrayList(scrmCallbackMsg));

        // 自动回复处理
        processAutoResponse(scrmCallbackMsg);
        // 处理聚合聊天
        processFusionChat(scrmCallbackMsg);
        return true;
    }

    private void processSendMessage(String corpId, String senderId, String userId, String externalUserId, MessageTypeEnum messageTypeEnum, FusionChatMessageBody fusionChatMessageBody, String messageId, Long timestamp, String requestId) {
        EnterpriseWechatChatMessageDTO message = new EnterpriseWechatChatMessageDTO();
        message.setSenderId(senderId);
        if (senderId.equals(userId)) {
            message.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
            message.setReceiverId(externalUserId);
            message.setReceiverType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
        } else {
            message.setSenderType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
            message.setReceiverId(userId);
            message.setReceiverType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
        }
        message.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(messageTypeEnum.name()));
        message.setMsgContent(fusionChatMessageBody.getMsgContent());
        message.setMessageId(messageId);
        message.setTimestamp(timestamp);
        message.setCorpId(corpId);
        message.setRequestId(requestId);
        PlatformLogUtil.logInfo("发送企业微信聊天记录消息，接收私聊消息", LogListUtil.newArrayList(message));
        metaqProducer.send(MQEnum.ENTERPRISE_WECHAT_CHAT_MESSAGE, null, corpId, JSONObject.toJSONString(message));
    }

    private void processFusionChat(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (StringUtils.isBlank(scrmCallbackMsg.getContent())) {
                PlatformLogUtil.logFail("将外部消息格式转换成聚合聊天消息，消息内容为空", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            WechatCustomerMessageBO wechatCustomerMessage = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatCustomerMessageBO.class);
            String userId = wechatCustomerMessage.getUserId();
            String externalUserId = wechatCustomerMessage.getExternalUserId();
            String senderId = wechatCustomerMessage.getSenderId();
            String chatId = externalUserId;
            ChatTypeEnum chatType = ChatTypeEnum.SINGLE_FOR_CUSTOMER;
            Long timestamp = wechatCustomerMessage.getTimestamp() * 1000;
            MessageTypeEnum messageTypeEnum = wechatCustomerMessage.getMessageTypeEnum();
            AbstractWechatMessageBO.MessageContent messageContent = wechatCustomerMessage.getMessageContent();

            if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(externalUserId)) {
                PlatformLogUtil.logFail("接收企微客户私聊消息回调结果，参数异常", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            // 将外部消息格式转换成聚合聊天消息格式
            FusionChatMessageBody fusionChatMessageBody = MessageUtils.receiveMessage2fusionChatMessage(messageTypeEnum, messageContent);
            if (fusionChatMessageBody == null) {
                PlatformLogUtil.logFail("将外部消息格式转换成聚合聊天消息格式失败", LogListUtil.newArrayList(messageTypeEnum.getCode(), userId, messageContent));
                return;
            }

            // 获取发送人信息
            Integer senderType = senderId.equals(userId) ? FusionChatSenderTypeEnum.USER.getValue() : FusionChatSenderTypeEnum.CUSTOMER.getValue();
            FusionChatUserBody senderUserBody = chatMessageService.getUserInfo(senderId, senderType);
            // 判断会话是否存在，不存在则保存用户会话，并唤起会话
            boolean pushSession = false;
            ChatConversationDO chatConversation = chatConversationRepository.getByUniqueKeyWithNull(userId, chatId, chatType.getValue());
            if (chatConversation == null) {
                ChatConversationCreateParam chatConversationCreateParam = new ChatConversationCreateParam();
                chatConversationCreateParam.setUserId(userId);
                chatConversationCreateParam.setChatId(chatId);
                chatConversationCreateParam.setChatType(chatType.getValue());
                chatConversationCreateParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
                chatConversationService.createChatConversation(chatConversationCreateParam);
                pushSession = true;
            } else {
                // 若被关闭，则重新唤起
                if (chatConversation.getDeleted()) {
                    chatConversation.setDeleted(false);
                    chatConversationRepository.update(chatConversation);
                    pushSession = true;
                }
            }
            if (pushSession) {
                createSingleChatProcessor.pushMessageByDistributed(userId, chatId, chatType, senderUserBody.getUserName(), senderUserBody.getUserAvatar(), "微信", timestamp);
            }
            // 保存消息到会话消息表
            String lockUser = wechatUserService.queryLockUserWithCache(userId, scrmCallbackMsg.getPlatformCorpId());
            User lockAccount = accountService.getUserByAccountId(lockUser);
            String lockUserName = Objects.nonNull(lockAccount) ? lockAccount.getUserName() : null;
            ChatMessageCreateParam createParam = new ChatMessageCreateParam();
            createParam.setSenderId(senderId);
            createParam.setSenderType(senderType);
            createParam.setReceiveId(chatMessageService.chatId2ReceiveId(userId, externalUserId, chatType.getValue()));
            createParam.setReceiveType(chatType.getReceiveType().getValue());
            createParam.setContent(MessageUtils.fusionChatMessage2DbContent(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
            createParam.setTimestamp(timestamp);
            createParam.setLockUser(lockUser);
            createParam.setLockUserName(lockUserName);
            createParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            createParam.setRequestId(scrmCallbackMsg.getRequestId());
            String messageId = chatMessageService.createChatMessage(createParam);
            // 收到消息，更新用户会话的最后一条消息
            ChatConversationUpdateLastMessageParam updateLastMessageParam = new ChatConversationUpdateLastMessageParam();
            updateLastMessageParam.setSenderId(senderId);
            updateLastMessageParam.setUserId(userId);
            updateLastMessageParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            updateLastMessageParam.setChatId(chatId);
            updateLastMessageParam.setChatType(chatType.getValue());
            updateLastMessageParam.setLastMessageSenderName(senderUserBody.getUserName());
            updateLastMessageParam.setLastMessageContent(MessageUtils.fusionChatMessage2Text(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
            updateLastMessageParam.setLastMessageCreateTime(new Date(createParam.getTimestamp()));
            // 自己发的消息，不更新会话排序
            if (!senderId.equals(userId)) {
                updateLastMessageParam.setUpdateTimestamp(createParam.getTimestamp());
            }
            chatConversationService.updateChatConversationLastMessage(updateLastMessageParam);
            // 推送消息
            if (getIntValueFromTair(TairConstant.FUSION_CHAT_SEND_MESSAGE_UNIQUE_KEY_PREFIX + scrmCallbackMsg.getRequestId()) <= 0) {
                receiveMessageProcessor.pushMessageByDistributed(userId, externalUserId, chatType, senderId, lockAccount, messageId, timestamp, scrmCallbackMsg.getPlatformCorpId(), fusionChatMessageBody, senderUserBody);
            }
            // 判断若是系统消息：“请先发送联系人验证请求/但被对方拒收了”，则关闭该会话（屏蔽已被微信用户删除的会话，避免影响到重要的会话）
            if (fusionChatMessageBody.getMsgType().equals(FusionChatMessageTypeEnum.SYSTEM_MESSAGE.getValue())
                    && (CLOSE_CONVER_SYSTEM_MESSAGE_LIST.stream().anyMatch(x -> fusionChatMessageBody.getMsgContent().contains(x)))) {
                ChatConversationUpdateParam chatConversationUpdateParam = new ChatConversationUpdateParam();
                chatConversationUpdateParam.setChatId(chatId);
                chatConversationUpdateParam.setChatType(chatType.getValue());
                chatConversationUpdateParam.setUserId(userId);
                chatConversationUpdateParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
                chatConversationUpdateParam.setCloseChat(true);
                chatConversationService.updateChatConversationConfig(chatConversationUpdateParam);
            }

            processSendMessage(scrmCallbackMsg.getPlatformCorpId(), senderId, userId, externalUserId, messageTypeEnum, fusionChatMessageBody, messageId, timestamp, scrmCallbackMsg.getRequestId());
        } catch (Exception e) {
            PlatformLogUtil.logException("接收企微客户私聊消息回调结果，聚合聊天逻辑处理失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    /**
     * 处理自动回复的消息
     *
     * @param scrmCallbackMsg 回调消息
     */
    private void processAutoResponse(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (StringUtils.isBlank(scrmCallbackMsg.getContent())) {
                PlatformLogUtil.logFail("自动回复处理的上游信息为空", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            WechatCustomerMessageBO wechatCustomerMessageBO = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatCustomerMessageBO.class);
            if (wechatCustomerMessageBO == null) {
                PlatformLogUtil.logFail("自动回复处理的消息体转化失败", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }
            // 消息过滤
            if (skipAutoResponse(wechatCustomerMessageBO)) {
                return;
            }

            List<String> messageUniqueKeyList = MessageUtils.getMessageUniqueKeyList(wechatCustomerMessageBO.getMessageTypeEnum(), wechatCustomerMessageBO);
            for (String messageUniqueKey : messageUniqueKeyList) {
                // 同一条消息多个回调只取一条
                String onlyKey = TairConstant.AUTO_RESPONSE_MESSAGE_UNIQUE_KEY_LOCK
                        + wechatCustomerMessageBO.getExternalUserId()
                        + "_" + messageUniqueKey.hashCode()
                        + "_" + wechatCustomerMessageBO.getUserId();
                if (ldbTairManager.incr(onlyKey, 1, 0, 30) > 1) {
                    PlatformLogUtil.logFail("自动回复消息接收重复，不做处理", LogListUtil.newArrayList(wechatCustomerMessageBO, scrmCallbackMsg.getPlatformCorpId(), onlyKey));
                    return;
                }
            }

            JSONObject data = new JSONObject();
            data.put("sendUserId", wechatCustomerMessageBO.getUserId());
            data.put("externalUserId", wechatCustomerMessageBO.getSenderId());
            data.put("message", wechatCustomerMessageBO.getMessageContent().getContent());
            data.put("msgSourceType", AutoResponseMsgSourceEnum.ROBOT_CUSTOMER_MESSAGE.getCode());
            data.put("corpId", scrmCallbackMsg.getPlatformCorpId());
            metaqProducer.send(MQEnum.RECEIVED_MESSAGE_CONTENT, "", "customer_message", data.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("自动回复回调处理失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    /**
     * 跳过自动回复
     * @param wechatCustomerMessageBO 客户消息对象
     * @return 是否跳过
     */
    private Boolean skipAutoResponse(WechatCustomerMessageBO wechatCustomerMessageBO) {
        String senderId = wechatCustomerMessageBO.getSenderId();
        String userId = wechatCustomerMessageBO.getUserId();
        // 过滤自己消息
        if (StringUtils.isNotBlank(senderId) && StringUtils.isNotBlank(userId) && senderId.equals(userId)) {
            PlatformLogUtil.logInfo("自动回复触发过滤规则【过滤自身消息】", LogListUtil.newArrayList(userId, wechatCustomerMessageBO));
            return true;
        }
        // 过滤企业成员消息
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(senderId));
        if (CollectionUtils.isNotEmpty(wechatUserList)) {
            PlatformLogUtil.logInfo("自动回复触发过滤规则【过滤企业成员消息】", LogListUtil.newArrayList(wechatUserList.get(0).getUserId(), wechatCustomerMessageBO));
            return true;
        }
        // 过滤系统消息
        if (Objects.nonNull(wechatCustomerMessageBO.getMessageTypeEnum())
                && MessageTypeEnum.SYSTEM_MESSAGE.getCode().equals(wechatCustomerMessageBO.getMessageTypeEnum().getCode())) {
            PlatformLogUtil.logInfo("自动回复过滤系统消息", LogListUtil.newArrayList(senderId, wechatCustomerMessageBO));
            return true;
        }
        return false;
    }

    private Integer getIntValueFromTair(String key) {
        return Optional.ofNullable(ldbTairManager.get(key))
                .map(String::valueOf)
                .map(Integer::valueOf)
                .orElse(0);
    }
}
