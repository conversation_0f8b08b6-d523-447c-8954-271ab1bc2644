package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.trip.channel.ai.client.enums.TaskBizSceneType;
import com.alibaba.trip.channel.ai.client.model.request.task.TaskSubmitRequest;
import com.alibaba.trip.channel.ai.client.model.response.task.TaskSubmitResponse;
import com.alibaba.trip.channel.ai.client.service.task.AiTaskService;
import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.EnterpriseWechatChatMessageTypeEnum;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.message.EnterpriseWechatChatMessageDTO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatSenderTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.AutoResponseMsgSourceEnum;
import com.alibaba.tripscrm.service.enums.task.SmartResponseTaskParamKey;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationCreateParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateLastMessageParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageCreateParam;
import com.alibaba.tripscrm.service.model.dto.task.SmartResponseTaskExtInfoDTO;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.CreateSingleChatProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.ReceiveMessageProcessor;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.result.AbstractWechatMessageBO;
import com.alibaba.tripzoo.proxy.result.AbstractWechatMessageBO.MessageContent;
import com.alibaba.tripzoo.proxy.result.WechatCustomerMessageBO;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 收到私聊的消息【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class ReceiveCustomerMessageCallbackProcessor implements ProxyCallbackProcessor {
    public static final List<String> CLOSE_CONVER_SYSTEM_MESSAGE_LIST = Stream.of("请先发送联系人验证请求", "但被对方拒收了").collect(Collectors.toList());
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private ChatConversationRepository chatConversationRepository;
    @Resource
    private CreateSingleChatProcessor createSingleChatProcessor;
    @Resource
    private ReceiveMessageProcessor receiveMessageProcessor;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private MetaqProducer metaqProducer;
    @Resource
    private AccountService accountService;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private TaskService taskService;
    @Resource
    private AiTaskService aiTaskService;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.RECEIVE_CUSTOMER_MESSAGE;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("接收企微客户私聊消息回调结果为失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return true;
        }
        PlatformLogUtil.logInfo("接收企微客户私聊消息回调结果为成功", LogListUtil.newArrayList(scrmCallbackMsg));

        // 自动回复处理
        processAutoResponse(scrmCallbackMsg);
        // 处理聚合聊天
        processFusionChat(scrmCallbackMsg);
        // 智能回复处理
        processSmartResponse(scrmCallbackMsg);
        return true;
    }

    private void processSendMessage(String corpId, String senderId, String userId, String externalUserId, MessageTypeEnum messageTypeEnum, FusionChatMessageBody fusionChatMessageBody, String messageId, Long timestamp, String requestId) {
        EnterpriseWechatChatMessageDTO message = new EnterpriseWechatChatMessageDTO();
        message.setSenderId(senderId);
        if (senderId.equals(userId)) {
            message.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
            message.setReceiverId(externalUserId);
            message.setReceiverType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
        } else {
            message.setSenderType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
            message.setReceiverId(userId);
            message.setReceiverType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
        }
        message.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(messageTypeEnum.name()));
        message.setMsgContent(fusionChatMessageBody.getMsgContent());
        message.setMessageId(messageId);
        message.setTimestamp(timestamp);
        message.setCorpId(corpId);
        message.setRequestId(requestId);
        PlatformLogUtil.logInfo("发送企业微信聊天记录消息，接收私聊消息", LogListUtil.newArrayList(message));
        metaqProducer.send(MQEnum.ENTERPRISE_WECHAT_CHAT_MESSAGE, null, corpId, JSONObject.toJSONString(message));
    }

    private void processFusionChat(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (StringUtils.isBlank(scrmCallbackMsg.getContent())) {
                PlatformLogUtil.logFail("将外部消息格式转换成聚合聊天消息，消息内容为空", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            WechatCustomerMessageBO wechatCustomerMessage = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatCustomerMessageBO.class);
            String userId = wechatCustomerMessage.getUserId();
            String externalUserId = wechatCustomerMessage.getExternalUserId();
            String senderId = wechatCustomerMessage.getSenderId();
            String chatId = externalUserId;
            ChatTypeEnum chatType = ChatTypeEnum.SINGLE_FOR_CUSTOMER;
            Long timestamp = wechatCustomerMessage.getTimestamp() * 1000;
            MessageTypeEnum messageTypeEnum = wechatCustomerMessage.getMessageTypeEnum();
            AbstractWechatMessageBO.MessageContent messageContent = wechatCustomerMessage.getMessageContent();

            if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(externalUserId)) {
                PlatformLogUtil.logFail("接收企微客户私聊消息回调结果，参数异常", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            // 将外部消息格式转换成聚合聊天消息格式
            FusionChatMessageBody fusionChatMessageBody = MessageUtils.receiveMessage2fusionChatMessage(messageTypeEnum, messageContent);
            if (fusionChatMessageBody == null) {
                PlatformLogUtil.logFail("将外部消息格式转换成聚合聊天消息格式失败", LogListUtil.newArrayList(messageTypeEnum.getCode(), userId, messageContent));
                return;
            }

            // 获取发送人信息
            Integer senderType = senderId.equals(userId) ? FusionChatSenderTypeEnum.USER.getValue() : FusionChatSenderTypeEnum.CUSTOMER.getValue();
            FusionChatUserBody senderUserBody = chatMessageService.getUserInfo(senderId, senderType);
            // 判断会话是否存在，不存在则保存用户会话，并唤起会话
            boolean pushSession = false;
            ChatConversationDO chatConversation = chatConversationRepository.getByUniqueKeyWithNull(userId, chatId, chatType.getValue());
            if (chatConversation == null) {
                ChatConversationCreateParam chatConversationCreateParam = new ChatConversationCreateParam();
                chatConversationCreateParam.setUserId(userId);
                chatConversationCreateParam.setChatId(chatId);
                chatConversationCreateParam.setChatType(chatType.getValue());
                chatConversationCreateParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
                chatConversationService.createChatConversation(chatConversationCreateParam);
                pushSession = true;
            } else {
                // 若被关闭，则重新唤起
                if (chatConversation.getDeleted()) {
                    chatConversation.setDeleted(false);
                    chatConversationRepository.update(chatConversation);
                    pushSession = true;
                }
            }
            if (pushSession) {
                createSingleChatProcessor.pushMessageByDistributed(userId, chatId, chatType, senderUserBody.getUserName(), senderUserBody.getUserAvatar(), "微信", timestamp);
            }
            // 保存消息到会话消息表
            String lockUser = wechatUserService.queryLockUserWithCache(userId, scrmCallbackMsg.getPlatformCorpId());
            User lockAccount = accountService.getUserByAccountId(lockUser);
            String lockUserName = Objects.nonNull(lockAccount) ? lockAccount.getUserName() : null;
            ChatMessageCreateParam createParam = new ChatMessageCreateParam();
            createParam.setSenderId(senderId);
            createParam.setSenderType(senderType);
            createParam.setReceiveId(chatMessageService.chatId2ReceiveId(userId, externalUserId, chatType.getValue()));
            createParam.setReceiveType(chatType.getReceiveType().getValue());
            createParam.setContent(MessageUtils.fusionChatMessage2DbContent(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
            createParam.setTimestamp(timestamp);
            createParam.setLockUser(lockUser);
            createParam.setLockUserName(lockUserName);
            createParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            createParam.setRequestId(scrmCallbackMsg.getRequestId());
            String messageId = chatMessageService.createChatMessage(createParam);
            // 收到消息，更新用户会话的最后一条消息
            ChatConversationUpdateLastMessageParam updateLastMessageParam = new ChatConversationUpdateLastMessageParam();
            updateLastMessageParam.setSenderId(senderId);
            updateLastMessageParam.setUserId(userId);
            updateLastMessageParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            updateLastMessageParam.setChatId(chatId);
            updateLastMessageParam.setChatType(chatType.getValue());
            updateLastMessageParam.setLastMessageSenderName(senderUserBody.getUserName());
            updateLastMessageParam.setLastMessageContent(MessageUtils.fusionChatMessage2Text(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
            updateLastMessageParam.setLastMessageCreateTime(new Date(createParam.getTimestamp()));
            // 自己发的消息，不更新会话排序
            if (!senderId.equals(userId)) {
                updateLastMessageParam.setUpdateTimestamp(createParam.getTimestamp());
            }
            chatConversationService.updateChatConversationLastMessage(updateLastMessageParam);
            // 推送消息
            if (getIntValueFromTair(TairConstant.FUSION_CHAT_SEND_MESSAGE_UNIQUE_KEY_PREFIX + scrmCallbackMsg.getRequestId()) <= 0) {
                receiveMessageProcessor.pushMessageByDistributed(userId, externalUserId, chatType, senderId, lockAccount, messageId, timestamp, scrmCallbackMsg.getPlatformCorpId(), fusionChatMessageBody, senderUserBody);
            }
            // 判断若是系统消息：“请先发送联系人验证请求/但被对方拒收了”，则关闭该会话（屏蔽已被微信用户删除的会话，避免影响到重要的会话）
            if (fusionChatMessageBody.getMsgType().equals(FusionChatMessageTypeEnum.SYSTEM_MESSAGE.getValue())
                    && (CLOSE_CONVER_SYSTEM_MESSAGE_LIST.stream().anyMatch(x -> fusionChatMessageBody.getMsgContent().contains(x)))) {
                ChatConversationUpdateParam chatConversationUpdateParam = new ChatConversationUpdateParam();
                chatConversationUpdateParam.setChatId(chatId);
                chatConversationUpdateParam.setChatType(chatType.getValue());
                chatConversationUpdateParam.setUserId(userId);
                chatConversationUpdateParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
                chatConversationUpdateParam.setCloseChat(true);
                chatConversationService.updateChatConversationConfig(chatConversationUpdateParam);
            }

            processSendMessage(scrmCallbackMsg.getPlatformCorpId(), senderId, userId, externalUserId, messageTypeEnum, fusionChatMessageBody, messageId, timestamp, scrmCallbackMsg.getRequestId());
        } catch (Exception e) {
            PlatformLogUtil.logException("接收企微客户私聊消息回调结果，聚合聊天逻辑处理失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    /**
     * 处理自动回复的消息
     *
     * @param scrmCallbackMsg 回调消息
     */
    private void processAutoResponse(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (StringUtils.isBlank(scrmCallbackMsg.getContent())) {
                PlatformLogUtil.logFail("自动回复处理的上游信息为空", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            WechatCustomerMessageBO wechatCustomerMessageBO = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatCustomerMessageBO.class);
            if (wechatCustomerMessageBO == null) {
                PlatformLogUtil.logFail("自动回复处理的消息体转化失败", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }
            // 消息过滤
            if (skipAutoResponse(wechatCustomerMessageBO, scrmCallbackMsg)) {
                return;
            }

            List<String> messageUniqueKeyList = MessageUtils.getMessageUniqueKeyList(wechatCustomerMessageBO.getMessageTypeEnum(), wechatCustomerMessageBO);
            for (String messageUniqueKey : messageUniqueKeyList) {
                // 同一条消息多个回调只取一条
                String onlyKey = TairConstant.AUTO_RESPONSE_MESSAGE_UNIQUE_KEY_LOCK
                        + wechatCustomerMessageBO.getExternalUserId()
                        + "_" + messageUniqueKey.hashCode()
                        + "_" + wechatCustomerMessageBO.getUserId();
                if (ldbTairManager.incr(onlyKey, 1, 0, 30) > 1) {
                    PlatformLogUtil.logFail("自动回复消息接收重复，不做处理", LogListUtil.newArrayList(wechatCustomerMessageBO, scrmCallbackMsg.getPlatformCorpId(), onlyKey));
                    return;
                }
            }

            JSONObject data = new JSONObject();
            data.put("sendUserId", wechatCustomerMessageBO.getUserId());
            data.put("externalUserId", wechatCustomerMessageBO.getSenderId());
            data.put("message", wechatCustomerMessageBO.getMessageContent().getContent());
            data.put("msgSourceType", AutoResponseMsgSourceEnum.ROBOT_CUSTOMER_MESSAGE.getCode());
            data.put("corpId", scrmCallbackMsg.getPlatformCorpId());
            metaqProducer.send(MQEnum.RECEIVED_MESSAGE_CONTENT, "", "customer_message", data.toJSONString());
        } catch (Exception e) {
            PlatformLogUtil.logException("自动回复回调处理失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    /**
     * 跳过自动回复
     * @param wechatCustomerMessageBO 客户消息对象
     * @param scrmCallbackMsg 回调消息
     * @return 是否跳过
     */
    private Boolean skipAutoResponse(WechatCustomerMessageBO wechatCustomerMessageBO, ScrmCallbackMsg scrmCallbackMsg) {
        String senderId = wechatCustomerMessageBO.getSenderId();
        String userId = wechatCustomerMessageBO.getUserId();
        // 过滤自己消息
        if (StringUtils.isNotBlank(senderId) && StringUtils.isNotBlank(userId) && senderId.equals(userId)) {
            PlatformLogUtil.logInfo("自动回复触发过滤规则【过滤自身消息】", LogListUtil.newArrayList(userId, wechatCustomerMessageBO));
            return true;
        }
        // 过滤企业成员消息
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(senderId));
        if (CollectionUtils.isNotEmpty(wechatUserList)) {
            PlatformLogUtil.logInfo("自动回复触发过滤规则【过滤企业成员消息】", LogListUtil.newArrayList(wechatUserList.get(0).getUserId(), wechatCustomerMessageBO));
            return true;
        }
        // 过滤系统消息
        if (Objects.nonNull(wechatCustomerMessageBO.getMessageTypeEnum())
                && MessageTypeEnum.SYSTEM_MESSAGE.getCode().equals(wechatCustomerMessageBO.getMessageTypeEnum().getCode())) {
            PlatformLogUtil.logInfo("自动回复过滤系统消息", LogListUtil.newArrayList(senderId, wechatCustomerMessageBO));
            return true;
        }
        // 校验是否智能回复拦截
        if (skipBySmartResponse(wechatCustomerMessageBO, scrmCallbackMsg)) {
            PlatformLogUtil.logInfo("自动回复被智能回复拦截", LogListUtil.newArrayList(wechatCustomerMessageBO));
            return true;
        }
        return false;
    }

    /**
     * 判断是自动回复是被否智能回复拦截（只要智能回复配置有效，就跳过自动回复配置）
     *
     * @param wechatCustomerMessageBO 客户消息对象
     * @param scrmCallbackMsg
     * @return true表示跳过
     */
    private boolean skipBySmartResponse(WechatCustomerMessageBO wechatCustomerMessageBO, ScrmCallbackMsg scrmCallbackMsg) {
        // 查询空间下的智能回复任务
        TaskInfoDO smartResponseTask = querySmartResponseTask(wechatCustomerMessageBO, scrmCallbackMsg);
        if (Objects.isNull(smartResponseTask)) {
            return false;
        }
        // 智能回复规则未跳过时，跳过自动回复规则
        return !skipSmartResponse(smartResponseTask, wechatCustomerMessageBO);
    }

    private Integer getIntValueFromTair(String key) {
        return Optional.ofNullable(ldbTairManager.get(key))
                .map(String::valueOf)
                .map(Integer::valueOf)
                .orElse(0);
    }

    /**
     * 处理智能回复的消息
     *
     * @param scrmCallbackMsg 回调消息
     */
    private void processSmartResponse(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (StringUtils.isBlank(scrmCallbackMsg.getContent())) {
                PlatformLogUtil.logFail("智能回复处理的上游信息为空", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            WechatCustomerMessageBO wechatCustomerMessageBO = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatCustomerMessageBO.class);
            if (wechatCustomerMessageBO == null) {
                PlatformLogUtil.logFail("智能回复处理的消息体转化失败", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }
            // 校验无效消息参数
            if (skipInvalidMessage(wechatCustomerMessageBO, scrmCallbackMsg)) {
                return;
            }
            // 查询空间下的智能回复任务
            TaskInfoDO smartResponseTask = querySmartResponseTask(wechatCustomerMessageBO, scrmCallbackMsg);
            if (Objects.isNull(smartResponseTask)) {
                PlatformLogUtil.logInfo("智能回复过滤规则【智能回复任务为空】", LogListUtil.newArrayList(wechatCustomerMessageBO, scrmCallbackMsg.getPlatformCorpId()));
                return;
            }
            // 跳过无效任务
            if (skipSmartResponse(smartResponseTask, wechatCustomerMessageBO)) {
                return;
            }

            // 智能回复AI消息触发
            TaskSubmitRequest taskSubmitRequest = new TaskSubmitRequest();
            taskSubmitRequest.setBizId(wechatCustomerMessageBO.getSenderId());
            taskSubmitRequest.setRequestId(UUID.randomUUID().toString());
            taskSubmitRequest.setBizScene(TaskBizSceneType.SCRM_QA_REPLY.getCode());
            taskSubmitRequest.setParam(buildSmartResponseTaskParam(wechatCustomerMessageBO, scrmCallbackMsg, smartResponseTask));
            com.alibaba.trip.channel.ai.client.model.common.ResultDO<TaskSubmitResponse> resultDO = aiTaskService.submitTask(taskSubmitRequest);
            if (Objects.isNull(resultDO) || !resultDO.isSuccess()) {
                PlatformLogUtil.logFail("智能回复AI消息触发失败", LogListUtil.newArrayList(smartResponseTask, taskSubmitRequest, resultDO));
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("智能回复回调处理失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    /**
     * 校验无效消息
     *
     * @param wechatCustomerMessageBO 客户消息对象
     * @param scrmCallbackMsg         回调消息
     * @return 是否为无效消息
     */
    private boolean skipInvalidMessage(WechatCustomerMessageBO wechatCustomerMessageBO, ScrmCallbackMsg scrmCallbackMsg) {
        String senderId = wechatCustomerMessageBO.getSenderId();
        String userId = wechatCustomerMessageBO.getUserId();
        if (StringUtils.isBlank(senderId)) {
            PlatformLogUtil.logFail("智能回复触发过滤规则【senderId为空】", LogListUtil.newArrayList(wechatCustomerMessageBO));
            return true;
        }
        if (StringUtils.isBlank(userId)) {
            PlatformLogUtil.logFail("智能回复触发过滤规则【userId为空】", LogListUtil.newArrayList(wechatCustomerMessageBO));
            return true;
        }
        // 过滤自己消息
        if (StringUtils.isNotBlank(senderId) && StringUtils.isNotBlank(userId) && senderId.equals(userId)) {
            PlatformLogUtil.logInfo("智能回复触发过滤规则【过滤自身消息】", LogListUtil.newArrayList(userId, wechatCustomerMessageBO));
            return true;
        }
        // 过滤企业成员消息
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(senderId));
        if (CollectionUtils.isNotEmpty(wechatUserList)) {
            PlatformLogUtil.logInfo("智能回复触发过滤规则【过滤企业成员消息】", LogListUtil.newArrayList(wechatUserList.get(0).getUserId(), wechatCustomerMessageBO));
            return true;
        }
        // 过滤非消息类型白名单消息
        if (Objects.nonNull(wechatCustomerMessageBO.getMessageTypeEnum()) && !SwitchConfig.SMART_RESPONSE_MESSAGE_TYPE_WHITE_LIST.contains(wechatCustomerMessageBO.getMessageTypeEnum().getCode())) {
            PlatformLogUtil.logInfo("智能回复非消息类型白名单过滤", LogListUtil.newArrayList(senderId, wechatCustomerMessageBO));
            return true;
        }
        // 如果是文本消息
        if (Objects.nonNull(wechatCustomerMessageBO.getMessageTypeEnum()) && MessageTypeEnum.TEXT.getCode().equals(wechatCustomerMessageBO.getMessageTypeEnum().getCode())) {
            MessageContent messageContent = wechatCustomerMessageBO.getMessageContent();
            // 并且消息在黑名单中，则设置为无效消息
            if (Objects.nonNull(messageContent) && StringUtils.isNotEmpty(messageContent.getContent()) && SwitchConfig.SMART_RESPONSE_TEXT_MESSAGE_BLACK_LIST.stream().anyMatch(
                messageContent.getContent()::contains)) {
                PlatformLogUtil.logInfo("智能回复过滤文本黑名单消息", LogListUtil.newArrayList(senderId, wechatCustomerMessageBO, SwitchConfig.SMART_RESPONSE_TEXT_MESSAGE_BLACK_LIST));
                return true;
            }
        }
        // 重复消息拦截处理
        List<String> messageUniqueKeyList = MessageUtils.getMessageUniqueKeyList(wechatCustomerMessageBO.getMessageTypeEnum(), wechatCustomerMessageBO);
        for (String messageUniqueKey : messageUniqueKeyList) {
            // 同一条消息多个回调只取一条
            String onlyKey = TairConstant.SMART_RESPONSE_MESSAGE_UNIQUE_KEY_LOCK + wechatCustomerMessageBO.getExternalUserId() + "_" + messageUniqueKey.hashCode() + "_"
                + wechatCustomerMessageBO.getUserId();
            if (ldbTairManager.incr(onlyKey, 1, 0, 30) > 1) {
                PlatformLogUtil.logFail("智能回复消息接收重复，不做处理", LogListUtil.newArrayList(wechatCustomerMessageBO, scrmCallbackMsg.getPlatformCorpId(), onlyKey));
                return true;
            }
        }
        return false;
    }

    /**
     * 跳过智能回复处理
     *
     * @param smartResponseTask       智能回复任务
     * @param wechatCustomerMessageBO 客户消息对象
     * @return 是否跳过
     */
    private boolean skipSmartResponse(TaskInfoDO smartResponseTask, WechatCustomerMessageBO wechatCustomerMessageBO) {
        // 如果智能回复任务没有上线，则直接跳过
        if (!TaskStatusEnum.ONLINE.getStatus().equals(smartResponseTask.getStatus())) {
            PlatformLogUtil.logInfo("智能回复过滤规则【智能回复任务未上线】", LogListUtil.newArrayList(smartResponseTask));
            return true;
        }
        // 获取智能回复任务扩展信息
        SmartResponseTaskExtInfoDTO taskExtInfoDTO = JSONObject.parseObject(smartResponseTask.getExtInfo(), SmartResponseTaskExtInfoDTO.class);
        if (Objects.isNull(taskExtInfoDTO)) {
            PlatformLogUtil.logInfo("智能回复过滤规则【智能回复任务扩展信息为空】", LogListUtil.newArrayList(smartResponseTask));
            return true;
        }
        // 判断企微成员是否在生效范围, 如果不是全部生效下，判断企微成员是否在生效范围内
        if (BooleanUtils.isNotTrue(taskExtInfoDTO.getApplyAllUserId()) && !ListUtils.emptyIfNull(taskExtInfoDTO.getApplyUserList()).contains(wechatCustomerMessageBO.getUserId())) {
            PlatformLogUtil.logInfo("智能回复过滤规则【智能回复任务指定企微成员未匹配】", LogListUtil.newArrayList(smartResponseTask, taskExtInfoDTO, wechatCustomerMessageBO));
            return true;
        }
        return false;
    }

    /**
     * 查询指定业务空间下的智能回复任务
     *
     * @param wechatCustomerMessageBO 客户消息对象
     * @param scrmCallbackMsg         回调消息
     * @return 智能回复任务
     */
    private TaskInfoDO querySmartResponseTask(WechatCustomerMessageBO wechatCustomerMessageBO, ScrmCallbackMsg scrmCallbackMsg) {
        String userId = wechatCustomerMessageBO.getUserId();
        String corpId = scrmCallbackMsg.getPlatformCorpId();

        // 查询企微用户的空间id
        Long spaceId = wechatUserService.getSpaceId(userId, corpId);
        if (Objects.isNull(spaceId)) {
            PlatformLogUtil.logInfo("智能回复规则查询【查询企微用户的空间id为空】", LogListUtil.newArrayList(userId, scrmCallbackMsg.getPlatformCorpId()));
            return null;
        }

        TaskQuery taskQuery = new TaskQuery();
        taskQuery.setSpaceId(spaceId);
        taskQuery.setType(TaskType.SMART_RESPONSE.getCode());
        List<TaskInfoDO> taskInfoDOList = taskService.query(taskQuery);
        if (CollectionUtils.isEmpty(taskInfoDOList)) {
            PlatformLogUtil.logInfo("智能回复规则查询【查询空间下的智能回复任务为空】", LogListUtil.newArrayList(spaceId));
            return null;
        }
        // 业务配置上，一个业务空间下最多只会有一个智能回复任务
        return taskInfoDOList.get(0);
    }

    /**
     * 构建智能回复任务参数
     *
     * @param wechatCustomerMessageBO 客户消息对象
     * @param scrmCallbackMsg         回调消息
     * @param smartResponseTask       智能回复任务
     * @return 智能回复任务参数
     */
    private Map<String, String> buildSmartResponseTaskParam(WechatCustomerMessageBO wechatCustomerMessageBO, ScrmCallbackMsg scrmCallbackMsg, TaskInfoDO smartResponseTask) {
        Map<String, String> param = Maps.newHashMap();
        param.put(SmartResponseTaskParamKey.EXTERNAL_USER_ID.getCode(), wechatCustomerMessageBO.getSenderId());
        param.put(SmartResponseTaskParamKey.CORP_ID.getCode(), scrmCallbackMsg.getPlatformCorpId());
        param.put(SmartResponseTaskParamKey.SPACE_ID.getCode(), smartResponseTask.getSpaceId().toString());
        param.put(SmartResponseTaskParamKey.MEMBER_ID.getCode(), wechatCustomerMessageBO.getUserId());
        param.put(SmartResponseTaskParamKey.SEND_USER_ID.getCode(), wechatCustomerMessageBO.getUserId());
        param.put(SmartResponseTaskParamKey.SMART_RESPONSE_TASK_ID.getCode(), smartResponseTask.getId().toString());
        param.put(SmartResponseTaskParamKey.MESSAGE.getCode(), wechatCustomerMessageBO.getMessageContent().getContent());
        param.put(SmartResponseTaskParamKey.TARGET_TYPE.getCode(), ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode().toString());
        param.put(SmartResponseTaskParamKey.TARGET_ID.getCode(), wechatCustomerMessageBO.getSenderId());
        return param;
    }
}
