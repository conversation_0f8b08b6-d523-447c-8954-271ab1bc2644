package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripzoo.proxy.model.DepartmentBO;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-08-17 14:38:03
 */
public interface WechatDepartmentService {

    /**
     * 根据企业Id，获取部门树
     *
     * @param corpId 企业Id
     * @return 部门树
     */
    DepartmentBO list(String corpId);

    /**
     * 根据空间Id，获取部门Id列表
     *
     * @param spaceId 业务空间Id
     * @return 部门Id列表
     */
    List<Integer> queryBindDepartmentIdListBySpaceId(Long spaceId);

    /**
     * 根据空间Id，获取部门Id列表
     *
     * @param spaceId 业务空间Id
     * @return 部门Id列表
     */
    List<Integer> queryBindDepartmentIdListBySpaceIdWithCache(Long spaceId);

    /**
     * 根据部门Id，获取业务空间Id
     *
     * @param departmentId 部门Id
     * @return 业务空间Id
     */
    Long getSpaceIdByDepartmentId(Integer departmentId);
}
