package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class FastCreateSpaceRequest implements Serializable {
    /**
     * 业务空间名称
     */
    private String name;
    /**
     * 业务空间描述
     */
    private String description;
    /**
     * 管理员员工号列表
     */
    private String adminList;
    /**
     * 业务空间成员员工号列表
     */
    private String memberList;
    /**
     * TP代运营列表
     */
    private String tpMemberList;
    /**
     * 绑定的部门列表
     */
    private String departmentList;
    /**
     * 创建者员工号
     */
    private String creatorId;
    /**
     * 企微组织id
     */
    private String corpId;
    /**
     * 空间所属的sellerId
     */
    private String sellerId;
}
