package com.alibaba.tripscrm.service.service.impl.fusionchat;

import com.alibaba.tripscrm.dal.mapper.tddl.CustomerManageGroupMapper;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerManageGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerManageGroupParam;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.model.domain.query.CustomerManagementGroupQuery;
import com.alibaba.tripscrm.service.service.fusionchat.CustomerManagementGroupService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerManagementGroupServiceImpl implements CustomerManagementGroupService {

    private final CustomerManageGroupMapper customerManageGroupMapper;

    @Override
    public Boolean addManagementGroup(CustomerManageGroupDO customerManageGroupDO) {
        if (customerManageGroupDO.getGmtCreate() == null) {
            customerManageGroupDO.setGmtCreate(new Date());
        }
        if (customerManageGroupDO.getGmtModified() == null) {
            customerManageGroupDO.setGmtModified(new Date());
        }
        return customerManageGroupMapper.insert(customerManageGroupDO) > 0;
    }

    @Override
    public Boolean deleteManagementGroup(Long id) {
        CustomerManageGroupDO customerManageGroupDO = new CustomerManageGroupDO();
        customerManageGroupDO.setId(id);
        customerManageGroupDO.setIsDelete(IsDeleteEnum.YES.getCode());
        return customerManageGroupMapper.updateByPrimaryKeySelective(customerManageGroupDO) > 0;
    }

    @Override
    public Boolean updateManagementGroupById(CustomerManageGroupDO customerManageGroupDO) {
        return customerManageGroupMapper.updateByPrimaryKeySelective(customerManageGroupDO) > 0;
    }

    @Override
    public CustomerManageGroupDO selectById(Long id) {
        return customerManageGroupMapper.selectByPrimaryKey(id);
    }

    @Override
    public Boolean customerGroupExist(String userId, String corpId, String name) {
        CustomerManagementGroupQuery query = new CustomerManagementGroupQuery();
        query.setUserId(userId);
        query.setCorpId(corpId);
        query.setName(name);
        List<CustomerManageGroupDO> customerManageGroupList = list(query);
        if (CollectionUtils.isEmpty(customerManageGroupList)) {
            return false;
        }
        List<String> nameFilterList = customerManageGroupList.stream().map(CustomerManageGroupDO::getName)
                .filter(s -> s.equals(name)).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(nameFilterList);
    }

    @Override
    public List<CustomerManageGroupDO> list(CustomerManagementGroupQuery query) {
        if (query == null) {
            PlatformLogUtil.logFail("客户管理组查询入参为空");
            return Lists.newArrayList();
        }
        CustomerManageGroupParam param = new CustomerManageGroupParam();
        CustomerManageGroupParam.Criteria criteria = param.createCriteria();
        if (query.getId() != null) {
            criteria.andIdEqualTo(query.getId());
        }
        if (StringUtils.isNotBlank(query.getName())) {
            criteria.andNameEqualTo(query.getName());
        }
        if (StringUtils.isNotBlank(query.getUserId())) {
            criteria.andUserIdEqualTo(query.getUserId());
        }
        if (StringUtils.isNotBlank(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }
        if (query.getIsDelete() != null) {
            criteria.andIsDeleteEqualTo(query.getIsDelete());
        } else {
            criteria.andIsDeleteEqualTo(IsDeleteEnum.NO.getCode());
        }
        return customerManageGroupMapper.selectByParam(param);
    }
}
