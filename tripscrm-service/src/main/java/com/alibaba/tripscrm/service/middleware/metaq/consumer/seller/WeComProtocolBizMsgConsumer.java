package com.alibaba.tripscrm.service.middleware.metaq.consumer.seller;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.service.seller.SellerInfoService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.fliggy.fmp.mms.client.support.event.model.MerBizAgtFulfillDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家签署协议消息 企微2.0
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service(value = "weComProtocolBizMsgConsumer")
public class WeComProtocolBizMsgConsumer implements MessageListenerConcurrently {

    private final SellerInfoService sellerInfoService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : list) {
            PlatformLogUtil.logInfo("接收到商家签署协议原始消息体", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logInfo("接收到商家签署协议消息失败", LogListUtil.newArrayList(receivedMsg));
            }
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private boolean dealWithMessage(String message) {
        // 解析数据
        try {
            SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.sellerCorpId);
            MerBizAgtFulfillDTO merBizAgtFulfillDTO = JSON.parseObject(message, MerBizAgtFulfillDTO.class);
            PlatformLogUtil.logInfo("接收到商家签署协议消息", LogListUtil.newArrayList(merBizAgtFulfillDTO));
            if (!SwitchConfig.bizAgtVersionId.equals(merBizAgtFulfillDTO.getBizAgtVersionId())){
                PlatformLogUtil.logInfo("接收到商家签署协议消息非638ID", LogListUtil.newArrayList(merBizAgtFulfillDTO));
                return false;
            }
            return sellerInfoService.updateBizAgtStatus(String.valueOf(merBizAgtFulfillDTO.getBizIdentityCode()), merBizAgtFulfillDTO.getStatus());
        } catch (Throwable e) {
            PlatformLogUtil.logException("接收到商家签署协议消息异常", null, e, LogListUtil.newArrayList(message));
            return false;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }
}
