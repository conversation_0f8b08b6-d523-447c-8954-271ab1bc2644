package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/1/8 下午5:26
 * @Filename：unilateralChatMessageTaskExecutor
 */
@Slf4j
@Service
public class UnilateralChatMessageTaskExecutor extends SingleChatTaskExecutor {
    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            return (String) taskDataBody.getContext().get("sendUserId");
        }
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        if (extInfoJson.containsKey(TaskConstant.SEND_USER_ID)) {
            String sendUserId = extInfoJson.getString(TaskConstant.SEND_USER_ID);
            List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(sendUserId));
            if (CollectionUtils.isNotEmpty(wechatUserList) && Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
                taskDataBody.getContext().put(TaskConstant.SEND_USER_ID, sendUserId);
                return getSendUserId(context, todoTaskVO);
            }
        }
        throw new TripscrmException("无sendUserId");

    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.UNILATERAL_CHAT_MESSAGE;
    }

}
