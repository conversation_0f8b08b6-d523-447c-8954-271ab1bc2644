package com.alibaba.tripscrm.service.service.impl.seller;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.SellerInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SellerInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.SellerInfoParam;
import com.alibaba.tripscrm.dal.model.domain.data.SellerInfoParam.Criteria;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceDO;
import com.alibaba.tripscrm.domain.enums.BasicElementEnum;
import com.alibaba.tripscrm.service.convert.SellerInfoConverter;
import com.alibaba.tripscrm.service.enums.seller.MerBizAgtStatusEnum;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.SourceTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.request.CreateNewSellerInfoParam;
import com.alibaba.tripscrm.service.model.vo.SpaceVO;
import com.alibaba.tripscrm.service.model.domain.query.seller.SellerInfoQuery;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.seller.SellerAcquisitionActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.dto.seller.SellerInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.activity.ActivitySopService;
import com.alibaba.tripscrm.service.service.seller.SellerInfoService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.util.env.EnvThreadLocalUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerInfoServiceImpl implements SellerInfoService {
    private final SellerInfoMapper sellerInfoMapper;
    private final SellerInfoConverter sellerInfoConverter;
    private final SpaceService spaceService;
    private final ActivitySopService activitySopService;

    /**
     * 根据参数统计总数
     *
     * @param param
     */
    @Override
    public long count(SellerInfoQuery param) {
        SellerInfoParam sellerInfoParam = buildParam(param);
        return sellerInfoMapper.countByParam(sellerInfoParam);
    }

    /**
     * 根据参数查询
     *
     * @param param
     */
    @Override
    public SellerInfoDTO find(SellerInfoQuery param) {
        List<SellerInfoDTO> list = listByCondition(param);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    /**
     * 列表查询
     *
     * @param param
     */
    @Override
    public List<SellerInfoDTO> listByCondition(SellerInfoQuery param) {
        SellerInfoParam sellerInfoParam = buildParam(param);
        List<SellerInfoDO> list = sellerInfoMapper.selectByParam(sellerInfoParam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<SellerInfoDTO> result = new ArrayList<>();
        for (SellerInfoDO record : list) {
            SellerInfoDTO sellerInfoDTO = sellerInfoConverter.convertFromDO(record);
            result.add(sellerInfoDTO);
        }
        return result;
    }

    @Override
    public List<SellerInfoDTO> listAllValid() {
        List<SellerInfoDTO> list = new ArrayList<>();
        int pageSize = 500;
        for (long minId = 0; minId >= 0; ) {
            SellerInfoParam sellerInfoParam = new SellerInfoParam();
            Criteria criteria = sellerInfoParam.createCriteria();
            criteria.andEnabledEqualTo((byte) 1);
            criteria.andHasSignedAgreementEqualTo((byte) 1);
            criteria.andIdGreaterThan(minId);
            sellerInfoParam.setPage(true);
            sellerInfoParam.setPageSize(pageSize);
            sellerInfoParam.appendOrderByClause(SellerInfoParam.OrderCondition.ID, SellerInfoParam.SortType.ASC);

            List<SellerInfoDO> sellerInfoList = sellerInfoMapper.selectByParam(sellerInfoParam);
            if (CollectionUtils.isEmpty(sellerInfoList)) {
                break;
            }
            list.addAll(sellerInfoList.stream().map(sellerInfoConverter::convertFromDO).collect(Collectors.toList()));
            minId = sellerInfoList.stream().mapToLong(SellerInfoDO::getId).max().orElse(-1L);
        }

        return list;
    }

    /**
     * 创建
     *
     * @param record
     */
    @Override
    public int createSelective(SellerInfoDTO record) {
        SellerInfoDO sellerInfoDO = sellerInfoConverter.convertFromDTO(record);
        return sellerInfoMapper.insertSelective(sellerInfoDO);
    }

    /**
     * 修改
     *
     * @param dto
     * @param param
     */
    @Override
    public int updateSelective(SellerInfoDTO dto, SellerInfoQuery param) {
        SellerInfoDO record = sellerInfoConverter.convertFromDTO(dto);
        SellerInfoParam sellerInfoParam = buildParam(param);
        return sellerInfoMapper.updateByParamSelective(record, sellerInfoParam);
    }

    private SellerInfoParam buildParam(SellerInfoQuery param) {
        SellerInfoParam sellerInfoParam = new SellerInfoParam();
        Criteria criteria = sellerInfoParam.createCriteria();
        if (StringUtils.hasText(param.getSellerId())) {
            criteria.andSellerIdEqualTo(param.getSellerId());
        }
        if (NumberUtils.biggerThanZero(param.getSpaceId())) {
            criteria.andSpaceIdEqualTo(param.getSpaceId());
        }
        if (Objects.nonNull(param.getEnabled())) {
            criteria.andEnabledEqualTo(param.getEnabled());
        }
        if (Objects.nonNull(param.getHasSignedAgreement())) {
            criteria.andHasSignedAgreementEqualTo(param.getHasSignedAgreement());
        }
        if (Objects.nonNull(param.getStartTime())) {
            criteria.andGmtCreateGreaterThanOrEqualTo(param.getStartTime());
        }
        if (Objects.nonNull(param.getEndTime())) {
            criteria.andGmtCreateLessThanOrEqualTo(param.getEndTime());
        }
        if (NumberUtils.biggerThanZero(param.getId())) {
            criteria.andIdEqualTo(param.getId());
        }
        if (NumberUtils.biggerThanZero(param.getPageNo())) {
            sellerInfoParam.setPagination(param.getPageNo(), param.getPageSize());
        }
        return sellerInfoParam;
    }


    /**
     * 用作消息接收后的状态变更
     *
     * @param sellerId 招商协议通知消息传回的sellerId
     * @param status   招商协议通知消息告诉协议status
     */
    @Override
    public boolean updateBizAgtStatus(String sellerId, Integer status) {
        Objects.requireNonNull(sellerId);
        Objects.requireNonNull(status);
        SellerInfoQuery infoQuery = new SellerInfoQuery();
        infoQuery.setSellerId(sellerId);
        SellerInfoDTO infoDTO = find(infoQuery);
        if (Objects.isNull(infoDTO)) {
            PlatformLogUtil.logInfo("遇到未接入的商家通知", sellerId, status);
            return false;
        }
        infoQuery.setId(infoDTO.getId());
        infoQuery.setSpaceId(infoDTO.getSpaceId());

        SellerInfoDTO dto = new SellerInfoDTO();
        //  失效
        if (MerBizAgtStatusEnum.BIZ_INVALID.getCode().equals(status)) {
            dto.setHasSignedAgreement(BasicElementEnum.NO.getCode());
            int line = updateSelective(dto, infoQuery);
            return line != 0;
        } else if (MerBizAgtStatusEnum.BIZ_VALID.getCode().equals(status)) {
            //  生效
            dto.setHasSignedAgreement(BasicElementEnum.YES.getCode());
            dto.setEnabled(BasicElementEnum.YES.getCode());
            int line = updateSelective(dto, infoQuery);
            return line != 0;
        }
        PlatformLogUtil.logInfo("招商协议通知的未知状态", sellerId, status, infoDTO);
        return false;
    }

    @AteyeInvoker(description = "设置空间表中的商家Id", paraDesc = "sellerId")
    public String settingSpaceSellerId(Long sellerId, Long spaceId) {
        SpaceDO spaceDO = spaceService.getById(spaceId);

        if (spaceDO == null) {
            return "空间不存在";
        }

        JSONObject jsonObject = JSONObject.parseObject(spaceDO.getExtInfo());
        jsonObject.put("sellerId", String.valueOf(sellerId));

        SpaceDO newSpaceDO = new SpaceDO();
        newSpaceDO.setId(spaceDO.getId());
        newSpaceDO.setExtInfo(jsonObject.toJSONString());
        newSpaceDO.setLastOperatorId("WB01127364");

        spaceService.update(newSpaceDO);
        return "success";
    }

    //  企微1.0迁移2.0
    @AteyeInvoker(description = "企微1.0空间数据历史数据迁移", paraDesc = "空间主键ID")
    @Transactional(rollbackFor = Throwable.class)
    public String historicalDataMovement(Long spaceId) {
        StringBuilder builder = null;
        try {
            SpaceDO baseSpaceDO = spaceService.getById(spaceId);
            List<SpaceDO> spaceDOList = Lists.newArrayList(baseSpaceDO);

            //  取出space空间下1970325277989527 已存在的数据
            Map<String, SpaceDO> sellerVsDo = spaceDOList.stream().collect(Collectors.toMap(key -> {
                String extInfo = key.getExtInfo();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(extInfo)) {
                    JSONObject jsonObject = JSONObject.parseObject(extInfo);
                    String sellerId = jsonObject.getString("sellerId");
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(sellerId)) {
                        return sellerId;
                    } else {
                        return org.apache.commons.lang3.StringUtils.EMPTY;
                    }
                } else {
                    return org.apache.commons.lang3.StringUtils.EMPTY;
                }
            }, Function.identity(), (a, b) -> a));

            //  过滤掉空字符串
            List<String> baseSellerIdList = sellerVsDo.keySet().stream().filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toList());

            SellerInfoParam sellerInfoParam = new SellerInfoParam();
            Criteria criteria = sellerInfoParam.createCriteria();
            criteria.andSellerIdIn(baseSellerIdList);
            //  取出在新表中的已存在的seller列表
            List<SellerInfoDO> sellerInfoDOList = sellerInfoMapper.selectByParam(sellerInfoParam);
            //  取新表的sellerId列表
            List<String> sellerIdListIsExist = sellerInfoDOList.stream().map(SellerInfoDO::getSellerId).collect(Collectors.toList());

            //  过滤掉已经存在的
            List<String> moveSellerIdList = baseSellerIdList.stream().filter(data -> !sellerIdListIsExist.contains(data)).collect(Collectors.toList());

            builder = new StringBuilder();

            for (String sellerId : moveSellerIdList) {
                SpaceDO spaceDO = sellerVsDo.get(sellerId);
                if (spaceDO == null) {
                    builder.append(sellerId).append(",");
                    continue;
                }
                SellerInfoDTO sellerInfoDTO = new SellerInfoDTO();
                sellerInfoDTO.setSellerId(sellerId);
                sellerInfoDTO.setSpaceId(spaceDO.getId());
                // 创建默认获客活动
                Map<String, Long> sellerAcquisitionActivityInfo = createSellerAcquisitionActivity(spaceDO.getId());
                sellerInfoDTO.setExtraInfo(SellerInfoDTO.ExtraInfo.builder().defaultSellerAcquisitionActivityInfo(sellerAcquisitionActivityInfo).build());
                int line = createSelective(sellerInfoDTO);
                if (line != 1) {
                    throw new TripscrmException("创建商家信息失败");
                }
            }
        } catch (TripscrmException e) {
            throw new RuntimeException(e);
        }
        return builder.toString();
    }

    /**
     * 用作商家第一次的创建
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int newSellerCreate(CreateNewSellerInfoParam param) {
        int line;

        try {
            //创建space
            SpaceVO spaceVO = new SpaceVO();
            spaceVO.setName(param.getSellerName());
            spaceVO.setDescription(param.getSellerName());
            //  使用Set集合去重
            spaceVO.setAdminList(param.getCreateId());
            spaceVO.setMemberList(org.apache.commons.lang3.StringUtils.EMPTY);
            spaceVO.setTpMemberList(param.getSellerLoginName());
            spaceVO.setRobotSeatCount(0L);
            spaceVO.setMemberMap(Collections.emptyMap());
            spaceVO.setDepartmentList(String.join(",", param.getDepartmentList()));
            spaceVO.setCreatorId(param.getCreateId());
            spaceVO.setLastOperatorId(param.getCreateId());
            spaceVO.setCorpId(SwitchConfig.sellerCorpId);
            spaceVO.setSellerId(param.getSellerId());
            Long spaceId = spaceService.create(spaceVO);
            if (!com.alibaba.tripscrm.service.util.system.NumberUtils.validLong(spaceId)) {
                throw new TripscrmException("业务空间Id为空");
            }
            // 因为是跨空间操作，必须要在上下文里面塞spaceId
            String corpId = spaceService.getCorpIdBySpaceId(spaceId);
            String sellerId = spaceService.getSellerIdBySpaceId(spaceId);
            SpaceInfoThreadLocalUtils.setCorpId(corpId).setSpaceId(spaceId).setSellerId(sellerId)
                    .setRoleType(spaceService.getRoleType(param.getCreateId(), spaceId)).setSourceId(param.getCreateId()).setSourceType(SourceTypeEnum.USER_OPT);
            //如果没有，创建一个和飞猪店铺名称同名的空间
            SellerInfoDTO sellerInfoDTO = new SellerInfoDTO();
            sellerInfoDTO.setSellerId(String.valueOf(param.getSellerId()));
            sellerInfoDTO.setSpaceId(spaceId);
            //  新商家默认没有签署协议
            sellerInfoDTO.setHasSignedAgreement((byte) 0);
            //  新商家不启用
            sellerInfoDTO.setEnabled((byte) 0);

            // 创建默认获客活动
            Map<String, Long> sellerAcquisitionActivityInfo = createSellerAcquisitionActivity(spaceId);
            sellerInfoDTO.setExtraInfo(SellerInfoDTO.ExtraInfo.builder().defaultSellerAcquisitionActivityInfo(sellerAcquisitionActivityInfo).build());
            line = createSelective(sellerInfoDTO);
            if (line != 1) {
                throw new TripscrmException("创建商家信息失败");
            }
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }

        return line;
    }

    @AteyeInvoker(description = "修改商家默认获客活动Id", paraDesc = "sellerId&env&activityId")
    public int updateDefaultAcquisitionActivity(String sellerId, String env, Long activityId) {
        try {
            EnvThreadLocalUtils.setEnv(env);
            SellerInfoQuery query = new SellerInfoQuery();
            query.setSellerId(sellerId);
            SellerInfoDTO sellerInfoDTO = find(query);
            if (Objects.isNull(sellerInfoDTO)) {
                return 0;
            }

            SellerInfoDTO dto = new SellerInfoDTO();
            dto.setId(sellerInfoDTO.getId());
            dto.setExtraInfo(sellerInfoDTO.getExtraInfo());
            if (CollectionUtils.isEmpty(dto.getExtraInfo().getDefaultSellerAcquisitionActivityInfo())) {
                dto.getExtraInfo().setDefaultSellerAcquisitionActivityInfo(new HashMap<>());
            }
            dto.getExtraInfo().getDefaultSellerAcquisitionActivityInfo().put(env, activityId);
            return updateSelective(dto, query);
        } catch (Exception e) {
            PlatformLogUtil.logException("修改商家默认获客活动Id出错", e.getMessage(), e, LogListUtil.newArrayList(sellerId, env, activityId));
            return 0;
        } finally {
            EnvThreadLocalUtils.remove();
        }
    }

    /**
     * 创建商家默认获客活动
     *
     * @param spaceId spaceId
     * @return
     */
    private Map<String, Long> createSellerAcquisitionActivity(Long spaceId) {
        Map<String, Long> defaultAcquisitionActivityInfo = new HashMap<>();
        for (String env : Lists.newArrayList("online", "pre")) {
            try {
                EnvThreadLocalUtils.setEnv(env);
                SellerAcquisitionActivitySopConfigVO sellerAcquisitionActivitySopConfigVO = new SellerAcquisitionActivitySopConfigVO();
                String activityName = "默认活动（微信小程序客户&视频号小店客服）";
                if (!Objects.equals(env, "online")) {
                    activityName = "默认活动-预发（微信小程序客户&视频号小店客服）";
                }
                sellerAcquisitionActivitySopConfigVO.setName(activityName);
                sellerAcquisitionActivitySopConfigVO.setDesc(activityName);
                sellerAcquisitionActivitySopConfigVO.setIsDefaultActivity(true);
                sellerAcquisitionActivitySopConfigVO.setSopType(SopTypeEnum.SELLER_ACQUISITION.getCode());
                sellerAcquisitionActivitySopConfigVO.setSpaceId(spaceId);
                ActivitySopVO<SellerAcquisitionActivitySopConfigVO> activitySopVO = new ActivitySopVO<>();
                activitySopVO.setConfig(sellerAcquisitionActivitySopConfigVO);
                activitySopVO.setSopType(SopTypeEnum.SELLER_ACQUISITION.getCode());
                activitySopVO.setSpaceId(spaceId);
                Long activityId = activitySopService.create(activitySopVO);
                if (!com.alibaba.tripscrm.service.util.system.NumberUtils.validLong(activityId)) {
                    PlatformLogUtil.logFail("创建商家默认获客活动失败", LogListUtil.newArrayList(spaceId));
                    throw new TripscrmException(TripSCRMErrorCode.CREATE_DEFAULT_SELLER_ACQUISITION_ACTIVITY_FAIL);
                }
                if (!activitySopService.online(activityId)) {
                    PlatformLogUtil.logFail("创建商家默认获客活动成功，上线失败", LogListUtil.newArrayList(spaceId, activityId));
                    throw new TripscrmException(TripSCRMErrorCode.CREATE_DEFAULT_SELLER_ACQUISITION_ACTIVITY_FAIL);
                }

                defaultAcquisitionActivityInfo.put(env, activityId);
            } catch (Exception e) {
                PlatformLogUtil.logFail("创建商家默认获客活动失败", LogListUtil.newArrayList(spaceId));
                throw new TripscrmException(TripSCRMErrorCode.CREATE_DEFAULT_SELLER_ACQUISITION_ACTIVITY_FAIL);
            } finally {
                EnvThreadLocalUtils.remove();
            }
        }
        return defaultAcquisitionActivityInfo;
    }
}