package com.alibaba.tripscrm.service.service.impl.crowd;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.task.CrowdTagConvertExecuteTypeEnum;
import com.alibaba.tripscrm.service.enums.task.CrowdTagConvertTaskStatusEnum;
import com.alibaba.tripscrm.service.manager.second.CrowdInfoManager;
import com.alibaba.tripscrm.service.model.domain.query.CrowdTagConvertTaskQuery;
import com.alibaba.tripscrm.service.model.dto.crowd.CrowdTagConvertTaskDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.request.crowd.CrowdTagConvertRequest;
import com.alibaba.tripscrm.service.repository.crowd.CrowdTagConvertTaskRepository;
import com.alibaba.tripscrm.service.service.crowd.CrowdTagConvertTaskService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.crowd.service.TripCrowdCommonService;
import com.fliggy.crowd.service.domain.CrowdDTO;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.trip.platform.protocol.TripCommonPlatformResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 人群标签转换任务服务实现
 *
 * <AUTHOR>
 * @date 2025/10/13
 */
@Service
@RequiredArgsConstructor
public class CrowdTagConvertTaskServiceImpl implements CrowdTagConvertTaskService {

    private final CrowdTagConvertTaskRepository crowdTagConvertTaskRepository;
    private final TripCrowdCommonService tripCrowdCommonService;

    /**
     * 人群数量限制：500万
     */
    private static final long MAX_CROWD_SIZE = 5000000L;

    /**
     * 创建人群标签转换任务
     *
     * @param request 创建请求
     * @return 任务信息
     */
    @Override
    public CrowdTagConvertTaskDTO createTask(CrowdTagConvertRequest request) {
        PlatformLogUtil.logInfo("开始创建人群标签转换任务", request);

        // 参数校验
        validateCreateRequest(request);

        // 查询源平台信息并进行业务校验，同时构建任务DTO
        CrowdTagConvertTaskDTO taskDTO = validateAndBuildTaskDTO(request);

        // 保存任务
        Integer result = crowdTagConvertTaskRepository.insertSelective(taskDTO);
        if (result <= 0) {
            throw new TripscrmException(TripSCRMErrorCode.SYSTEM_EXCEPTION, "创建任务失败");
        }

        PlatformLogUtil.logInfo("创建人群标签转换任务成功", taskDTO);
        return taskDTO;
    }

    /**
     * 查询人群标签转换任务
     *
     * @param query 查询条件
     * @return 任务列表
     */
    @Override
    public List<CrowdTagConvertTaskDTO> queryTasks(CrowdTagConvertTaskQuery query) {
        return crowdTagConvertTaskRepository.select(query);
    }

    /**
     * 分页查询人群标签转换任务
     *
     * @param query 查询条件（包含分页参数）
     * @return 分页结果
     */
    @Override
    public PageInfo<CrowdTagConvertTaskDTO> queryTasksWithPage(CrowdTagConvertTaskQuery query) {
        // 参数校验
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "查询条件不能为空");
        }
        return crowdTagConvertTaskRepository.selectWithPage(query);
    }

    /**
     * 根据ID查询人群标签转换任务
     *
     * @param id 任务ID
     * @return 任务信息
     */
    @Override
    public CrowdTagConvertTaskDTO getTaskById(Long id) {
        if (Objects.isNull(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务ID不能为空");
        }
        return crowdTagConvertTaskRepository.selectById(id);
    }

    /**
     * 更新任务状态
     * @return 是否更新成功
     */
    @Override
    public Boolean updateTaskStatus(CrowdTagConvertTaskDTO updateDTO) {
        if (Objects.isNull(updateDTO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务ID和状态不能为空");
        }
        Integer result = crowdTagConvertTaskRepository.updateByIdSelective(updateDTO);
        return result > 0;
    }

    /**
     * 统计人群标签转换任务数量
     *
     * @param query 查询条件
     * @return 任务数量
     */
    @Override
    public Long countTasks(CrowdTagConvertTaskQuery query) {
        return crowdTagConvertTaskRepository.count(query);
    }

    /**
     * 根据ID删除人群标签转换任务
     *
     * @param id 任务ID
     * @return 任务信息
     */
    @Override
    public Integer deleteTaskById(Long id) {
        if (!NumberUtils.biggerThanZero(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "任务ID不能为空");
        }
        return crowdTagConvertTaskRepository.deleteById(id);
    }

    /**
     * 校验创建请求参数
     */
    private void validateCreateRequest(CrowdTagConvertRequest request) {
        if (Objects.isNull(request)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "请求参数不能为空");
        }

        if (Objects.isNull(request.getSourcePlatform())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "源平台不能为空");
        }

        if (Objects.isNull(request.getTargetPlatform())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "目标平台不能为空");
        }

        if (!StringUtils.hasText(request.getSourceCrowdId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "源人群ID不能为空");
        }

        if (!StringUtils.hasText(request.getCreatorId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "创建人ID不能为空");
        }

        if (Objects.isNull(request.getExecuteType())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "执行类型不能为空");
        }
    }

    /**
     * 校验并构建任务DTO（在校验时查询源平台信息并保存到DTO）
     */
    private CrowdTagConvertTaskDTO validateAndBuildTaskDTO(CrowdTagConvertRequest request) {
        // 获取源平台枚举
        PlatformTypeEnum sourcePlatform = PlatformTypeEnum.valueOf(request.getSourcePlatform());
        PlatformTypeEnum targetPlatform = PlatformTypeEnum.valueOf(request.getTargetPlatform());

        // 构建基础任务DTO
        CrowdTagConvertTaskDTO taskDTO = buildBaseTaskDTO(request, sourcePlatform, targetPlatform);

        // 根据平台分类进行业务校验，并查询平台信息保存到DTO
        if (PlatformTypeEnum.ZHU_GE.equals(sourcePlatform)) {
            validateZhugePlatformAndFillDTO(request, taskDTO);
        }else {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "不支持的源平台类型");
        }
        return taskDTO;
    }

    /**
     * 构建基础任务DTO
     */
    private CrowdTagConvertTaskDTO buildBaseTaskDTO(CrowdTagConvertRequest request,
                                                   PlatformTypeEnum sourcePlatform,
                                                   PlatformTypeEnum targetPlatform) {
        CrowdTagConvertTaskDTO taskDTO = new CrowdTagConvertTaskDTO();
        taskDTO.setSourcePlatform(sourcePlatform);
        taskDTO.setTargetPlatform(targetPlatform);
        taskDTO.setTargetCrowdId(request.getTargetCrowdId());
        taskDTO.setSourceCrowdId(request.getSourceCrowdId());
        taskDTO.setFirstLevelTag(request.getFirstLevelTag());
        taskDTO.setSecondLevelTag(request.getSecondLevelTag());
        taskDTO.setStatus(CrowdTagConvertTaskStatusEnum.PENDING);

        // 转换执行类型
        taskDTO.setExecuteType(CrowdTagConvertExecuteTypeEnum.getByCode(request.getExecuteType().byteValue()));

        // 转换日期
        if (StringUtils.hasText(request.getStartDate())) {
            taskDTO.setStartDate(DateUtils.parseToDateOfDay(request.getStartDate()));
        }
        if (StringUtils.hasText(request.getEndDate())) {
            taskDTO.setEndDate(DateUtils.parseToDateOfDay(request.getEndDate()));
        }

        taskDTO.setOperatorId(request.getCreatorId());
        taskDTO.setGmtCreate(new Date());
        taskDTO.setGmtModified(new Date());

        return taskDTO;
    }

    /**
     * 诸葛平台校验并填充DTO信息
     */
    private void validateZhugePlatformAndFillDTO(CrowdTagConvertRequest request, CrowdTagConvertTaskDTO taskDTO) {
        String sourceCrowdId = request.getSourceCrowdId();

        try {
            PlatformLogUtil.logInfo("开始诸葛平台综合校验", sourceCrowdId);

            // 1. 校验人群是否存在并获取人群信息
            TripCommonPlatformResult<CrowdDTO> crowdInfoResult = tripCrowdCommonService.getCrowdInfo(Long.valueOf(sourceCrowdId));
            if (crowdInfoResult == null || !crowdInfoResult.isSuccess() || crowdInfoResult.getData() == null) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "诸葛平台人群不存在或无权限访问");
            }
            CrowdDTO crowdDTO = crowdInfoResult.getData();

            if (!NumberUtils.biggerThanZero(crowdDTO.getCrowdAmount())) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "诸葛平台人群不存在或无权限访问");
            }

            // 2. 校验人群数量是否超过限制
            if (crowdDTO.getCrowdAmount() > MAX_CROWD_SIZE) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS,
                    String.format("人群数量%d超过%d限制，不允许转换", crowdDTO.getCrowdAmount(), MAX_CROWD_SIZE));
            }

            // 3. 校验人群是否在有效期内
            Date expiredDate = crowdDTO.getExpiredDate();
            if (new Date().after(expiredDate)) {
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "人群已过期，不允许转换");
            }

            // 4. 查询并保存源平台信息到DTO
            taskDTO.setSourceCrowdName(crowdDTO.getCrowdName());
            taskDTO.setTotalCount(crowdDTO.getCrowdAmount());
            taskDTO.setFirstLevelTag(sourceCrowdId);
            if(CrowdTagConvertExecuteTypeEnum.SOURCE_CROWD_VALID_TIME.equals(CrowdTagConvertExecuteTypeEnum.getByCode(request.getExecuteType().byteValue())) && !StringUtils.hasText(request.getStartDate()) && !StringUtils.hasText(request.getEndDate())) {
                taskDTO.setStartDate(new Date());
                taskDTO.setEndDate(expiredDate);
            }

        } catch (NumberFormatException e) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "诸葛平台人群ID格式错误");
        } catch (Exception e) {
            PlatformLogUtil.logException("诸葛平台校验异常", e.getMessage(), e, LogListUtil.newArrayList(sourceCrowdId));
            if (e instanceof TripscrmException) {
                throw e;
            }
            throw new TripscrmException(TripSCRMErrorCode.SYSTEM_EXCEPTION, "诸葛平台校验失败：" + e.getMessage());
        }
    }

    /**
     * 校验是否存在重复任务
     */
    private void validateDuplicateTask(CrowdTagConvertRequest request) {
        CrowdTagConvertTaskQuery query = new CrowdTagConvertTaskQuery();
        query.setSourcePlatform(request.getSourcePlatform().byteValue());
        query.setTargetPlatform(request.getTargetPlatform().byteValue());
        query.setSourceCrowdId(request.getSourceCrowdId());

        List<CrowdTagConvertTaskDTO> existingTasks = crowdTagConvertTaskRepository.select(query);
        if (!existingTasks.isEmpty()) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "存在相同的进行中任务，请勿重复创建");
        }
    }

}