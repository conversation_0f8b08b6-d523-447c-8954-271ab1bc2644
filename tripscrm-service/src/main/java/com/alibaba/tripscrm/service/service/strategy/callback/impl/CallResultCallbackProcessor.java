package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.callpush.CallStageEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.CallPushDataUpdateEventTypeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/8/6 16:56
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CallResultCallbackProcessor implements ProxyCallbackProcessor {
    private final MetaqProducer metaqProducer;

    private static final Map<Integer, CallStageEnum> CALL_STAGE_ENUM_MAP = Maps.newHashMap();

    static {
        CALL_STAGE_ENUM_MAP.put(1, CallStageEnum.CALL_SUCCESS_A);
        CALL_STAGE_ENUM_MAP.put(2, CallStageEnum.CALL_SUCCESS_B);
        CALL_STAGE_ENUM_MAP.put(3, CallStageEnum.CALL_SUCCESS_C);
        CALL_STAGE_ENUM_MAP.put(4, CallStageEnum.CALL_SUCCESS_D);
    }

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.CALL_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject jsonContent = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String cellPhone = jsonContent.getString(CallbackConstant.CELL_PHONE);
        JSONArray taskResultList = jsonContent.getJSONArray(CallbackConstant.TASK_RESULT_LIST);
        JSONObject callbackContent = new JSONObject();
        callbackContent.put("taskResult", taskResultList);
        CallStageEnum callStageEnum = getCallStageEnum(jsonContent);

        JSONObject data = new JSONObject();
        data.put("targetId", cellPhone);
        data.put("targetType", ActivityTargetTypeEnum.PHONE_MOBILE.getCode());
        data.put("callbackContent", callbackContent);
        data.put("callStage", Optional.ofNullable(callStageEnum).map(CallStageEnum::getCode).orElse(null));
        data.put("hitBlackWord", hitBlackWord(taskResultList));
        data.put("eventType", CallPushDataUpdateEventTypeEnum.CALL_RESULT.getCode());
        data.put("corpId", WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
        if (jsonContent.containsKey(TaskConstant.ACTIVITY_ID)) {
            data.put(TaskConstant.ACTIVITY_ID, jsonContent.getLong(TaskConstant.ACTIVITY_ID));
        }
        metaqProducer.send(MQEnum.SCRM_CALL_PUSH_DATA_PROCESS, cellPhone, CallPushDataUpdateEventTypeEnum.CALL_RESULT.getCode(), data.toJSONString());
        return false;
    }

    private Boolean hitBlackWord(JSONArray taskResultList) {
        if (CollectionUtils.isEmpty(taskResultList)) {
            return false;
        }

        for (Object taskResult : taskResultList) {
            if (hitBlackWord((JSONObject) taskResult)) {
                return true;
            }
        }
        return false;
    }

    private static boolean hitBlackWord(JSONObject taskResult) {
        String resultValue = taskResult.getString("resultValue");
        if (StringUtils.hasText(resultValue) && SwitchConfig.BAIYING_CALL_PUSH_VIP_CRAWD_BLACK_WORDS.contains(resultValue)) {
            return true;
        }

        String resultDesc = taskResult.getString("resultDesc");
        if (StringUtils.hasText(resultDesc) && SwitchConfig.BAIYING_CALL_PUSH_VIP_CRAWD_BLACK_WORDS.contains(resultDesc)) {
            return true;
        }

        JSONArray resultLabels = taskResult.getJSONArray("resultLabels");
        if (resultLabels == null) {
            return false;
        }

        for (Object o : resultLabels) {
            JSONObject label = (JSONObject) o;
            String value = label.getString("value");
            if (StringUtils.hasText(value) && SwitchConfig.BAIYING_CALL_PUSH_VIP_CRAWD_BLACK_WORDS.contains(value)) {
                return true;
            }
        }
        return false;
    }

    private CallStageEnum getCallStageEnum(JSONObject jsonContent) {
        // 0没有接通
        // 1 外呼接通意向等级为A
        // 2 外呼接通意向等级为B
        // 3 外呼接通意向等级不为A且意向等级不为B
        // 4 意向等级为G
        Integer result = jsonContent.getInteger(CallbackConstant.RESULT);
        if (Objects.isNull(result)) {
            PlatformLogUtil.logFail("外呼回调结果异常", LogListUtil.newArrayList(jsonContent));
            return null;
        }

        return CALL_STAGE_ENUM_MAP.getOrDefault(result, CallStageEnum.CALL_FAIL);
    }
}
