package com.alibaba.tripscrm.service.service.strategy.websocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.risk.SourceTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbLockManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.fusionchat.FusionChatService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.ManagerWechatUser;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.AsyncCreateGroupChatProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.AsyncSendMessageProcessor;
import com.alibaba.tripscrm.service.util.dingtalk.DingRobotUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.constant.TairConstant.*;

/**
 * WebSocket工厂，负责管理维护websocket，消息收发的路由
 *
 * <AUTHOR>
 * @date 2023/9/28
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class WebSocketFactory {
    /**
     * 存储key和session的映射关系，用于ws消息推送
     */
    private final Map<String, Set<WebSocketSession>> keySessionsMap = new ConcurrentHashMap<>();
    /**
     * 存储account和session的映射关系，用于ws消息推送
     */
    private final Map<String, Set<WebSocketSession>> accountSessionsMap = new ConcurrentHashMap<>();
    /**
     * 存储sessionId的上下文，包括订阅的key列表、用户信息、空间信息
     */
    private final Map<String, WsContextInfo> sessionInfoMap = new ConcurrentHashMap<>();

    @Resource
    private WsEventRouter wsEventRouter;
    @Resource
    private MetaqProducer metaqProducer;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private LdbLockManager ldbLockManager;
    @Resource
    private AsyncSendMessageProcessor asyncSendMessageProcessor;
    @Resource
    private AsyncCreateGroupChatProcessor asyncCreateGroupChatProcessor;
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private SpaceService spaceService;
    @Resource
    private WXCorpStorage wxCorpStorage;
    @Resource
    private FusionChatService fusionChatService;

    /**
     * 建立session后置动作
     *
     * @param session ws
     */
    public void afterSessionBuild(WebSocketSession session, User account, Long spaceId) {
        // 缓存websocket上下文
        WsContextInfo wsContextInfo = new WsContextInfo();
        wsContextInfo.setAccount(account);
        wsContextInfo.setSpaceId(spaceId);
        String corpId = spaceService.getCorpIdBySpaceId(spaceId);
        wsContextInfo.setCorpId(corpId);
        wsContextInfo.setBuildDate(new Date());
        sessionInfoMap.put(session.getId(), wsContextInfo);
        // 订阅keys
        this.refreshSubscribeKeys(session, account, spaceId);
        // 缓存account与session的关系
        Set<WebSocketSession> accountSessions = accountSessionsMap.computeIfAbsent(account.getUserId(), k -> new HashSet<>());
        accountSessions.add(session);
        // tair中上线account
        this.onlineAccount(session.getId(), account.getUserId());
        // 输出当前ws在线情况
        this.logInfo(session.getId());
    }

    /**
     * 关闭session后置动作
     *
     * @param session ws
     */
    public void afterSessionClose(WebSocketSession session) {
        WsContextInfo wsContextInfo = sessionInfoMap.get(session.getId());
        if (wsContextInfo != null) {
            for (String key : wsContextInfo.getKeys()) {
                Set<WebSocketSession> keySessions = keySessionsMap.computeIfAbsent(key, k -> new HashSet<>());
                keySessions.remove(session);
                if (keySessions.isEmpty()) {
                    keySessionsMap.remove(key);
                }
            }
            chatConversationService.cleanUserDoingChat(wsContextInfo.getAccount(), wsContextInfo.getSpaceId());
            sessionInfoMap.remove(session.getId());
            accountSessionsMap.remove(wsContextInfo.getAccount().getUserId());
            PlatformLogUtil.logFail("关闭session后置动作", LogListUtil.newArrayList(session.getId(), wsContextInfo));
            // tair中下线key
            this.offlineKeys(session.getId(), wsContextInfo.getKeys());
            // tair中下线account
            this.offlineAccount(session.getId(), wsContextInfo.getAccount().getUserId());
        }
    }

    /**
     * 刷新session订阅key
     *
     * @param session session
     */
    public void refreshSubscribeKeys(WebSocketSession session, User account, Long spaceId) {
        List<ManagerWechatUser> managerUsers = fusionChatService.listManagerUsers(account.getUserId(), spaceId);
        Set<String> keySet = managerUsers.stream().map(x -> buildKey(x.getUserId())).collect(Collectors.toSet());
        for (String key : keySet) {
            // keySessionsMap add
            Set<WebSocketSession> keySessions = keySessionsMap.computeIfAbsent(key, k -> new HashSet<>());
            keySessions.add(session);
        }
        // sessionInfoMap add
        WsContextInfo wsContextInfo = sessionInfoMap.computeIfAbsent(session.getId(), k -> new WsContextInfo());
        wsContextInfo.setKeys(keySet);
        // tair中上线key
        this.onlineKeys(session.getId(), keySet);
    }

    /**
     * 收到来自session的消息
     *
     * @param
     */
    public void receiveMessage(WebSocketSession session, String msgBody) {
        if (msgBody != null && !msgBody.contains("heartbeat")) {
            PlatformLogUtil.logInfo("收到来自session的消息", LogListUtil.newArrayList(session.getId(), msgBody));
        }
        sessionInfoMap.get(session.getId()).setLastUpdateTime(new Date());
        // 设置空间上下文
        WsContextInfo wsContextInfo = getWsContextInfo(session);
        SpaceInfoThreadLocalUtils.setCorpId(wsContextInfo.getCorpId()).setSpaceId(wsContextInfo.getSpaceId()).setSourceId(wsContextInfo.getAccount().getUserId()).setSourceType(SourceTypeEnum.FUSION_CHAT);
        wsEventRouter.routeProcessor(session, msgBody);
        SpaceInfoThreadLocalUtils.remove();
    }

    /**
     * 推送消息到websocket通道（直接推送）
     *
     * @param
     */
    public void pushMessageBySession(WebSocketSession session, WsEvent wsEvent, Boolean isLog) {
        try {
            synchronized (session) {
                session.sendMessage(new TextMessage(JSON.toJSONString(wsEvent)));
            }
            if (isLog) {
                PlatformLogUtil.logInfo("推送消息到websocket通道（直接推送）", LogListUtil.newArrayList(session.getId(), wsEvent.getUserId(), wsEvent));
            }
            sessionInfoMap.getOrDefault(session.getId(), new WsContextInfo()).setLastUpdateTime(new Date());
        } catch (Exception e) {
            PlatformLogUtil.logException("推送消息到websocket通道（直接推送）失败", e.getMessage(), e, LogListUtil.newArrayList(session.getId(), wsEvent));
        }
    }

    /**
     * 推送消息到websocket通道（分布式推送），用于回调、Http请求的推送
     *
     * @param
     */
    public void pushMessageByDistributed(WsEvent wsEvent) {
        // 设置corpId
        wsEvent.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        // 仅当用户在线时才做分布式推送
        Set<String> userIds = this.getWsOnlineUserIds();
        if (wsEvent.getUserId() != null && userIds.contains(wsEvent.getUserId())) {
            PlatformLogUtil.logInfo("推送消息到websocket通道（分布式推送）", LogListUtil.newArrayList(wsEvent.getUserId(), wsEvent));
            metaqProducer.send(MQEnum.FUSION_CHAT, UUID.randomUUID().toString(), JSONObject.toJSONString(wsEvent));
        }
    }

    /**
     * 推送消息到websocket通道（分布式推送），用于回调、Http请求的推送，account维度的推送
     *
     * @param
     */
    public void pushMessageByDistributedWithAccount(WsEvent wsEvent, List<String> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        // 设置corpId
        wsEvent.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        // 仅当账号在线时才做分布式推送
        Set<String> onlineAccountIds = this.getWsOnlineAccountIds();
        for (String accountId : accountIds) {
            if (onlineAccountIds.contains(accountId)) {
                wsEvent.setAccountId(accountId);
                PlatformLogUtil.logInfo("推送消息到websocket通道（分布式推送）", LogListUtil.newArrayList(wsEvent));
                metaqProducer.send(MQEnum.FUSION_CHAT, UUID.randomUUID().toString(), JSONObject.toJSONString(wsEvent));
            }
        }
    }

    /**
     * 推送消息到websocket通道（本地服务推送），仅用在metaQ-fusionChatConsumer消费
     *
     * @param
     */
    public void pushMessageByLocalServer(WsEvent wsEvent) {
        // 通过accountId推送
        if (wsEvent.getAccountId() != null) {
            Set<WebSocketSession> webSocketSessions = accountSessionsMap.get(wsEvent.getAccountId());
            if (CollectionUtils.isEmpty(webSocketSessions)) {
                return;
            }

            for (WebSocketSession session : webSocketSessions) {
                this.pushMessageBySession(session, wsEvent, true);
            }
            return;
        }

        // 通过key推送
        if (wsEvent.getUserId() != null) {
            String key = buildKey(wsEvent.getUserId());
            if (!keySessionsMap.containsKey(key)) {
                PlatformLogUtil.logFail("推送消息到websocket通道（本地服务推送），session为空", LogListUtil.newArrayList(wsEvent));
                return;
            }

            Set<WebSocketSession> webSocketSessions = keySessionsMap.get(key);
            for (WebSocketSession session : webSocketSessions) {
                this.pushMessageBySession(session, wsEvent, true);
            }
        }
    }

    /**
     * websocket用户上线
     *
     * @param keys keys
     */
    public void onlineKeys(String sessionId, Set<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        try {
            ldbLockManager.lockWithRunnable(WEB_SOCKET_USER_ONLINE_INFO_LOCK, 3, true, () -> {
                // 存储当前在线的keys
                Map<String, Set<String>> wsOnlineInfoMap = this.getWsKeyUserOnlineInfoMap();
                try {
                    InetAddress address = InetAddress.getLocalHost();
                    String ipAddress = address.getHostAddress();
                    Set<String> keyList = wsOnlineInfoMap.computeIfAbsent(ipAddress, k -> new HashSet<>());
                    for (String key : keys) {
                        PlatformLogUtil.logFail("websocket用户上线", LogListUtil.newArrayList(sessionId, key));
                        // 这里存储和移除时都要带上sessionId，避免一个用户双开情况下，一个ws关闭用户就下线了
                        keyList.add(sessionId + "|" + key);
                    }
                    String wsOnlineInfoMapJson = JSONObject.toJSONString(wsOnlineInfoMap);
                    ldbTairManager.put(NEW_WEB_SOCKET_USER_ONLINE_INFO, wsOnlineInfoMapJson);
                } catch (Exception e) {
                    PlatformLogUtil.logException("websocket用户上线异常", e.getMessage(), e, LogListUtil.newArrayList(sessionId, keys));
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("websocket用户上线异常", e.getMessage(), e, LogListUtil.newArrayList(sessionId, keys));
        }
    }

    /**
     * websocket用户下线
     *
     * @param keys keys
     */
    public void offlineKeys(String sessionId, Set<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }

        try {
            ldbLockManager.lockWithRunnable(WEB_SOCKET_USER_ONLINE_INFO_LOCK, 3, true, () -> {
                Map<String, Set<String>> wsOnlineInfoMap = this.getWsKeyUserOnlineInfoMap();
                try {
                    InetAddress address = InetAddress.getLocalHost();
                    String ipAddress = address.getHostAddress();
                    Set<String> keyList = wsOnlineInfoMap.computeIfAbsent(ipAddress, k -> new HashSet<>());
                    for (String key : keys) {
                        PlatformLogUtil.logFail("websocket用户下线", LogListUtil.newArrayList(sessionId, key));
                        // 这里存储和移除时都要带上sessionId，避免一个用户双开情况下，一个ws关闭用户就下线了
                        keyList.remove(sessionId + "|" + key);
                    }
                    String wsOnlineInfoMapJson = JSONObject.toJSONString(wsOnlineInfoMap);
                    ldbTairManager.put(NEW_WEB_SOCKET_USER_ONLINE_INFO, wsOnlineInfoMapJson);
                } catch (Exception e) {
                    PlatformLogUtil.logException("websocket用户下线异常", e.getMessage(), e, LogListUtil.newArrayList(sessionId, keys));
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("websocket用户下线异常", e.getMessage(), e, LogListUtil.newArrayList(sessionId, keys));
        }
    }

    /**
     * 获取所有服务器websocket用户的在线情况
     *
     * @return return
     */
    private Map<String, Set<String>> getWsKeyUserOnlineInfoMap() {
        Map<String, Set<String>> wsOnlineInfoMap = new HashMap<>();
        String webSocketOnlineInfo = (String) ldbTairManager.get(NEW_WEB_SOCKET_USER_ONLINE_INFO);
        if (webSocketOnlineInfo != null) {
            wsOnlineInfoMap = JSONObject.parseObject(webSocketOnlineInfo, new TypeReference<Map<String, Set<String>>>() {
            });
        }
        return wsOnlineInfoMap;
    }

    /**
     * 获取所有服务器websocket用户的在线key情况
     *
     * @return return
     */
    public Set<String> getWsOnlineKeys() {
        Map<String, Set<String>> wsOnlineInfoMap = this.getWsKeyUserOnlineInfoMap();
        Set<String> userOnlineKeys = wsOnlineInfoMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        return userOnlineKeys.stream().map(this::userOnlineKey2Key).collect(Collectors.toSet());
    }

    /**
     * 获取所有服务器websocket用户的在线情况
     *
     * @return return
     */
    public Set<String> getWsOnlineUserIds() {
        Map<String, Set<String>> wsOnlineInfoMap = this.getWsKeyUserOnlineInfoMap();
        Set<String> userOnlineKeys = wsOnlineInfoMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        return userOnlineKeys.stream().map(this::userOnlineKey2UserId).collect(Collectors.toSet());
    }

    /**
     * websocket账号上线
     *
     * @param sessionId sessionId
     * @param accountId 平台账号id
     */
    private void onlineAccount(String sessionId, String accountId) {
        try {
            ldbLockManager.lockWithRunnable(WEB_SOCKET_ACCOUNT_ONLINE_INFO_LOCK, 3, true, () -> {
                // 存储当前在线的keys
                Map<String, Set<String>> wsOnlineInfoMap = this.getWsAccountOnlineInfoMap();
                try {
                    InetAddress address = InetAddress.getLocalHost();
                    String ipAddress = address.getHostAddress();
                    Set<String> accountList = wsOnlineInfoMap.computeIfAbsent(ipAddress, k -> new HashSet<>());
                    accountList.add(sessionId + "|" + accountId);
                    String wsOnlineInfoMapJson = JSONObject.toJSONString(wsOnlineInfoMap);
                    ldbTairManager.put(NEW_WEB_SOCKET_ACCOUNT_ONLINE_INFO, wsOnlineInfoMapJson);
                    PlatformLogUtil.logFail("websocket账号上线", LogListUtil.newArrayList(sessionId, accountId));
                } catch (Exception e) {
                    PlatformLogUtil.logException("websocket账号上线异常", e.getMessage(), e, LogListUtil.newArrayList(sessionId, accountId));
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("websocket账号上线异常", e.getMessage(), e, LogListUtil.newArrayList(sessionId, accountId));
        }
    }

    /**
     * websocket账号下线
     *
     * @param sessionId sessionId
     * @param accountId 平台账号id
     */
    private void offlineAccount(String sessionId, String accountId) {
        try {
            ldbLockManager.lockWithRunnable(WEB_SOCKET_ACCOUNT_ONLINE_INFO_LOCK, 3, true, () -> {
                Map<String, Set<String>> wsOnlineInfoMap = this.getWsAccountOnlineInfoMap();
                try {
                    InetAddress address = InetAddress.getLocalHost();
                    String ipAddress = address.getHostAddress();
                    Set<String> accountList = wsOnlineInfoMap.computeIfAbsent(ipAddress, k -> new HashSet<>());
                    accountList.remove(sessionId + "|" + accountId);
                    String wsOnlineInfoMapJson = JSONObject.toJSONString(wsOnlineInfoMap);
                    ldbTairManager.put(NEW_WEB_SOCKET_ACCOUNT_ONLINE_INFO, wsOnlineInfoMapJson);
                    PlatformLogUtil.logFail("websocket账号下线", LogListUtil.newArrayList(sessionId, accountId));
                } catch (Exception e) {
                    PlatformLogUtil.logException("websocket账号下线异常", e.getMessage(), e, LogListUtil.newArrayList(sessionId, accountId));
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("websocket账号下线异常", e.getMessage(), e, LogListUtil.newArrayList(sessionId, accountId));
        }
    }

    /**
     * 获取所有服务器websocket账号的在线情况
     *
     * @return return
     */
    private Map<String, Set<String>> getWsAccountOnlineInfoMap() {
        Map<String, Set<String>> wsOnlineInfoMap = new HashMap<>();
        String webSocketOnlineInfo = (String) ldbTairManager.get(NEW_WEB_SOCKET_ACCOUNT_ONLINE_INFO);
        if (webSocketOnlineInfo != null) {
            wsOnlineInfoMap = JSONObject.parseObject(webSocketOnlineInfo, new TypeReference<Map<String, Set<String>>>() {
            });
        }
        return wsOnlineInfoMap;
    }

    /**
     * 获取所有服务器websocket用户的在线情况
     *
     * @return return
     */
    private Set<String> getWsOnlineAccountIds() {
        Map<String, Set<String>> wsOnlineInfoMap = this.getWsAccountOnlineInfoMap();
        Set<String> accountOnlineKeys = wsOnlineInfoMap.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        return accountOnlineKeys.stream().map(this::accountOnlineKey2AccountId).collect(Collectors.toSet());
    }

    /**
     * 获取session的上下文信息
     *
     * @param session session
     * @return return
     */
    public WsContextInfo getWsContextInfo(WebSocketSession session) {
        return sessionInfoMap.get(session.getId());
    }

    public void logInfo(String sessionId) {
        try {
            long currentTime = System.currentTimeMillis();
            InetAddress address = InetAddress.getLocalHost();
            String ipAddress = address.getHostAddress();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("\n>>> [").append(ipAddress).append("] key <-> session\n");
            for (Map.Entry<String, Set<WebSocketSession>> entry : keySessionsMap.entrySet()) {
                stringBuilder.append(entry.getKey()).append(" => ").append(entry.getValue().stream().map(WebSocketSession::getId).sorted().collect(Collectors.joining(","))).append("\n");
            }
            stringBuilder.append("\n>>> [").append(ipAddress).append("] account <-> session\n");
            for (Map.Entry<String, Set<WebSocketSession>> entry : accountSessionsMap.entrySet()) {
                stringBuilder.append(entry.getKey()).append(" => ").append(entry.getValue().stream().map(WebSocketSession::getId).sorted().collect(Collectors.joining(","))).append("\n");
            }
            stringBuilder.append("\n>>> [").append(ipAddress).append("] sessionId <-> sessionInfo\n");
            for (Map.Entry<String, WsContextInfo> entry : sessionInfoMap.entrySet()) {
                SimpleDateFormat sdf = new SimpleDateFormat("MM-dd HH:mm:ss");
                stringBuilder.append(entry.getKey()).append(" => ").append(entry.getValue().getLog(sdf)).append("\n");
            }
            stringBuilder.append("\n>>> 创建失败的群聊（超过10分钟）\n");
            Map<String, Long> createGroupRecordMap = asyncCreateGroupChatProcessor.getCreateGroupRecordMap();
            for (Map.Entry<String, Long> entry : createGroupRecordMap.entrySet()) {
                long second = (currentTime - entry.getValue()) / 1000;
//              if (second > 600) {
                stringBuilder.append(entry.getKey()).append("(").append(second).append("s)\n");
//              }
            }
            stringBuilder.append("\n>>> 发送失败的消息（超过10分钟）\n");
            Map<String, Long> sendMessageRecordMap = asyncSendMessageProcessor.getSendMessageRecordMap();
            for (Map.Entry<String, Long> entry : sendMessageRecordMap.entrySet()) {
                long second = (currentTime - entry.getValue()) / 1000;
//                if (second > 600) {
                stringBuilder.append(entry.getKey()).append("(").append(second).append("s)\n");
//                }
            }
            stringBuilder.append("\n>>> 在线的企微号列表\n");
            Map<String, Set<String>> wsOnlineUserInfoMap = this.getWsKeyUserOnlineInfoMap();
            for (Map.Entry<String, Set<String>> entry : wsOnlineUserInfoMap.entrySet()) {
                String value = entry.getValue().stream().map(this::userOnlineKey2UserId).distinct().sorted().collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(value)) {
                    stringBuilder.append(entry.getKey()).append(" => ").append(value).append("\n");
                }
            }
            stringBuilder.append("\n>>> 在线的平台账号列表\n");
            Map<String, Set<String>> wsOnlineAccountInfoMap = this.getWsAccountOnlineInfoMap();
            for (Map.Entry<String, Set<String>> entry : wsOnlineAccountInfoMap.entrySet()) {
                String value = entry.getValue().stream().map(this::accountOnlineKey2AccountId).distinct().sorted().collect(Collectors.joining(","));
                if (StringUtils.isNotBlank(value)) {
                    stringBuilder.append(entry.getKey()).append(" => ").append(value).append("\n");
                }
            }

            PlatformLogUtil.logFail("logInfo", LogListUtil.newArrayList(sessionId, stringBuilder));
            DingRobotUtils.sendMessageForFusionChat("当前websocket在线情况:" + stringBuilder);
        } catch (Exception e) {
            PlatformLogUtil.logException("logInfo", e.getMessage(), e, LogListUtil.newArrayList(sessionId));
        }
    }

    /**
     * 构建key
     *
     * @param userId
     * @return
     */
    public String buildKey(String userId) {
        String corpId = SpaceInfoThreadLocalUtils.getCorpId();
        return wxCorpStorage.getTag(corpId) + "#" + userId;
    }

    /**
     * 从key中解析userId
     *
     * @param key
     * @return userId
     */
    public String getUserIdByKey(String key) {
        return key.split("#")[1];
    }

    private String userOnlineKey2Key(String userOnlineKey) {
        // 因为存储和移除时都要带上sessionId，userId通过|分割获得
        String[] onlineKeyArray = userOnlineKey.split("\\|");
        return onlineKeyArray[1];
    }

    private String userOnlineKey2UserId(String userOnlineKey) {
        // 因为存储和移除时都要带上sessionId，userId通过|分割获得
        String[] onlineKeyArray = userOnlineKey.split("\\|");
        String key = onlineKeyArray[1];
        // 解析key中的userId
        return getUserIdByKey(key);
    }

    private String accountOnlineKey2AccountId(String accountOnlineKey) {
        // 因为存储和移除时都要带上sessionId，accountId通过|分割获得
        String[] onlineKeyArray = accountOnlineKey.split("\\|");
        // 解析key中的accountId
        return onlineKeyArray[1];
    }
}
