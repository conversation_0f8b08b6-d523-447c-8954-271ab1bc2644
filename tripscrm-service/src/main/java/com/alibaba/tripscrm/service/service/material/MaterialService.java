package com.alibaba.tripscrm.service.service.material;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.service.constant.MaterialJsonKeyConstant;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.vo.material.CheckDeleteVO;
import com.alibaba.tripscrm.service.model.vo.material.ItemVO;
import com.alibaba.tripscrm.service.model.vo.material.MaterialInfoVO;
import com.alibaba.tripscrm.service.model.vo.material.MaterialMessageVO;
import com.alibaba.tripscrm.service.model.dto.material.FusionChatMaterialDTO;
import com.alibaba.tripscrm.service.model.dto.ItemInfoDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSelfTestDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/5/22
 */
public interface MaterialService {

    /**
     * 保存/更新
     *
     * @param materailInfoDO
     * @return
     */
    Long upsert(MaterailInfoDO materailInfoDO);

    /**
     * 判断素材是否是微信红包
     */
    boolean isWechatCashRedPacketMaterial(MaterailInfoDO materailInfoDO);

    /**
     * 获取素材消息数量
     */
    static Integer getMessageCount(MaterailInfoDO materialInfoDO) {
        if (Objects.isNull(materialInfoDO) || StringUtils.isBlank(materialInfoDO.getContent())) {
            return 0;
        }

        JSONObject materialContentJo = JSONObject.parseObject(materialInfoDO.getContent());
        if (materialContentJo.containsKey("msgList")) {
            return materialContentJo.getJSONArray("msgList").size();
        }

        return materialContentJo.getJSONArray(MaterialJsonKeyConstant.messageList).size();
    }

    PageInfo<MaterailInfoDO> list(MaterialInfoVO queryVO);

    /**
     * 根据素材id列表查询素材
     *
     * @param ids 素材id列表
     * @return 素材信息
     */
    List<MaterailInfoDO> listByIds(List<Long> ids);
    
    /**
     * 根据spaceId和type查询素材列表
     *
     * @param spaceId 业务空间Id
     * @param type 素材类型
     * @return 素材列表
     */
    List<MaterailInfoDO> listBySpaceIdAndType(Long spaceId, String type);

    MaterailInfoDO queryById(Long id);

    MaterailInfoDO queryByIdAndSpaceId(Long id, Long spaceId);

    Integer delete(Long id, Long spaceId);

    /**
     * 重名校验
     *
     * @param spaceId
     * @param name
     * @param id
     * @return
     */
    boolean checkDuplicateName(Long spaceId, String name, Long id);

    /**
     * 获取使用该素材的任务
     *
     * @param materialId 素材Id
     * @param statusList 任务状态
     * @return
     */
    List<TaskInfoDO> getTaskListByMaterial(Long materialId, List<TaskStatusEnum> statusList);

    /**
     * 查询不同类型的项目(包含酒店、页匠、商品)
     *
     * @param itemInfoDTO
     * @return
     */
    ItemVO queryItemById(ItemInfoDTO itemInfoDTO);

    /**
     * 素材自测
     *
     * @param materialSelfTestDTO
     * @return
     */
    Boolean selfTest(MaterialSelfTestDTO materialSelfTestDTO);

    /**
     * 判断素材是否能删除
     *
     * @param id
     * @return
     */
    CheckDeleteVO checkDelete(Long id);

    /**
     * 聚合聊天素材信息查询
     *
     * @param fusionChatMaterialDTO
     * @return
     */
    List<MaterialMessageVO> fusionChatMaterial(FusionChatMaterialDTO fusionChatMaterialDTO);

    /**
     * 获取社区内容对应的聊天消息
     *
     * @param fusionChatMaterialDTO 入参
     * @return 聚合聊天消息体
     */
    List<FusionChatMessageBody> fusionChatContent(FusionChatMaterialDTO fusionChatMaterialDTO);

    /**
     * 获取素材的消息列表
     *
     * @param materialId               素材id
     * @param materialTrackRelationDTO 素材点击关系
     * @param extraInfo                动态参数值
     * @return 消息列表
     */
    List<MessageBO> getMaterialMessageList(Long materialId, MaterialTrackRelationDTO materialTrackRelationDTO, Map<String, Object> extraInfo);

    /**
     * 预览聊天记录
     *
     * @param materialId 素材id
     * @param uid        预览用户id
     * @return
     */
    Boolean previewChat(Long materialId, String uid);

}
