package com.alibaba.tripscrm.service.service.impl.wechat;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatTransferCustomerResultMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatTransferCustomerResultDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatTransferCustomerResultParam;
import com.alibaba.tripscrm.service.model.domain.query.WechatTransferCustomerResultQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.wechat.WechatTransferCustomerResultService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.tddl.client.sequence.Sequence;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class WechatTransferCustomerResultServiceImpl implements WechatTransferCustomerResultService {
    private final WechatTransferCustomerResultMapper wechatTransferCustomerResultMapper;
    private final Sequence sequence;

    public WechatTransferCustomerResultServiceImpl(WechatTransferCustomerResultMapper wechatTransferCustomerResultMapper, @Qualifier("wechatTransferCustomerResultIdSequence") Sequence sequence) {
        this.wechatTransferCustomerResultMapper = wechatTransferCustomerResultMapper;
        this.sequence = sequence;
    }

    /**
     * 根据参数统计总数
     *
     * @param query
     */
    @Override
    public long count(WechatTransferCustomerResultQuery query) {
        WechatTransferCustomerResultParam wechatTransferCustomerResultParam = buildParam(query);
        return wechatTransferCustomerResultMapper.countByParam(wechatTransferCustomerResultParam);
    }

    /**
     * 根据参数查询
     *
     * @param query
     */
    @Override
    public WechatTransferCustomerResultDO find(WechatTransferCustomerResultQuery query) {
        List<WechatTransferCustomerResultDO> list = list(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    /**
     * 列表查询
     *
     * @param query
     */
    @Override
    public List<WechatTransferCustomerResultDO> list(WechatTransferCustomerResultQuery query) {
        WechatTransferCustomerResultParam wechatTransferCustomerResultParam = buildParam(query);
        List<WechatTransferCustomerResultDO> list = wechatTransferCustomerResultMapper.selectByParam(wechatTransferCustomerResultParam);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        return list;
    }

    @AteyeInvoker(description = "删除客户继承记录", paraDesc = "taskId")
    public int delete(Long taskId) {
        WechatTransferCustomerResultQuery query = new WechatTransferCustomerResultQuery();
        query.setTaskId(taskId);
        return wechatTransferCustomerResultMapper.deleteByParam(buildParam(query));
    }

    @AteyeInvoker(description = "新增客户继承记录", paraDesc = "taskId")
    public int insert(Long taskId, String handoverUserId, String takeoverUserId, String externalUserId) {
        WechatTransferCustomerResultDO wechatTransferCustomerResultDO = new WechatTransferCustomerResultDO();
        wechatTransferCustomerResultDO.setTaskId(taskId);
        wechatTransferCustomerResultDO.setHandoverUserId(handoverUserId);
        wechatTransferCustomerResultDO.setTakeoverUserId(takeoverUserId);
        wechatTransferCustomerResultDO.setExternalUserId(externalUserId);
        return insertSelective(wechatTransferCustomerResultDO);
    }

    /**
     * 创建
     *
     * @param record
     */
    @Override
    public int insertSelective(WechatTransferCustomerResultDO record) {
        record.setId(sequence.nextValue());
        return wechatTransferCustomerResultMapper.insertSelective(record);
    }

    /**
     * 修改
     *
     * @param record
     * @param condition
     */
    @Override
    public int updateSelective(WechatTransferCustomerResultDO record, WechatTransferCustomerResultQuery condition) {
        WechatTransferCustomerResultParam wechatTransferCustomerResultParam = buildParam(condition);
        return wechatTransferCustomerResultMapper.updateByParamSelective(record, wechatTransferCustomerResultParam);
    }

    private WechatTransferCustomerResultParam buildParam(WechatTransferCustomerResultQuery query) {
        if (!NumberUtils.biggerThanZero(query.getTaskId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WechatTransferCustomerResultParam wechatTransferCustomerResultParam = new WechatTransferCustomerResultParam();
        WechatTransferCustomerResultParam.Criteria criteria = wechatTransferCustomerResultParam.createCriteria();
        if (StringUtils.hasText(query.getHandoverUserId())) {
            criteria.andHandoverUserIdEqualTo(query.getHandoverUserId());
        }
        if (StringUtils.hasText(query.getTakeoverUserId())) {
            criteria.andTakeoverUserIdEqualTo(query.getTakeoverUserId());
        }
        if (StringUtils.hasText(query.getExternalUserId())) {
            criteria.andExternalUserIdEqualTo(query.getExternalUserId());
        }
        if (NumberUtils.biggerThanZero(query.getTaskId())) {
            criteria.andTaskIdEqualTo(query.getTaskId());
        }
        if (!CollectionUtils.isEmpty(query.getStatusList())) {
            criteria.andStatusIn(query.getStatusList().stream().map(i -> i.getCode().byteValue()).collect(Collectors.toList()));
        }
        if (Objects.nonNull(query.getStartCreateTime())) {
            criteria.andGmtCreateGreaterThanOrEqualTo(query.getStartCreateTime());
        }
        if (Objects.nonNull(query.getMinId())) {
            criteria.andIdGreaterThanOrEqualTo(query.getMinId());
            wechatTransferCustomerResultParam.appendOrderByClause(WechatTransferCustomerResultParam.OrderCondition.ID, WechatTransferCustomerResultParam.SortType.ASC);
        }
        if (NumberUtils.biggerThanZero(query.getSize())) {
            wechatTransferCustomerResultParam.setPageSize(query.getSize());
        }
        return wechatTransferCustomerResultParam;
    }
}