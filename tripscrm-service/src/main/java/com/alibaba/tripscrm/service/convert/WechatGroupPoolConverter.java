package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupPoolDO;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatGroupPoolDTO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class WechatGroupPoolConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param wechatGroupPoolDTO
     */
    public WechatGroupPoolDO convertFromDTO(WechatGroupPoolDTO wechatGroupPoolDTO) {
        WechatGroupPoolDO wechatGroupPoolDO = new WechatGroupPoolDO();
        wechatGroupPoolDO.setId(wechatGroupPoolDTO.getId());
        wechatGroupPoolDO.setGmtCreate(wechatGroupPoolDTO.getGmtCreate());
        wechatGroupPoolDO.setGmtModified(wechatGroupPoolDTO.getGmtModified());
        wechatGroupPoolDO.setDeleted(wechatGroupPoolDTO.getDeleted());
        wechatGroupPoolDO.setOwner(wechatGroupPoolDTO.getOwner());
        wechatGroupPoolDO.setCorpId(wechatGroupPoolDTO.getCorpId());
        wechatGroupPoolDO.setChatId(wechatGroupPoolDTO.getChatId());
        wechatGroupPoolDO.setStatus(wechatGroupPoolDTO.getStatus());
        wechatGroupPoolDO.setEnv(wechatGroupPoolDTO.getEnv());
        wechatGroupPoolDO.setExtraInfo(wechatGroupPoolDTO.getExtraInfo());
        return wechatGroupPoolDO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param wechatGroupPoolDO
     */
    public WechatGroupPoolDTO convertFromDO(WechatGroupPoolDO wechatGroupPoolDO) {
        WechatGroupPoolDTO wechatGroupPoolDTO = new WechatGroupPoolDTO();
        wechatGroupPoolDTO.setId(wechatGroupPoolDO.getId());
        wechatGroupPoolDTO.setGmtCreate(wechatGroupPoolDO.getGmtCreate());
        wechatGroupPoolDTO.setGmtModified(wechatGroupPoolDO.getGmtModified());
        wechatGroupPoolDTO.setDeleted(wechatGroupPoolDO.getDeleted());
        wechatGroupPoolDTO.setOwner(wechatGroupPoolDO.getOwner());
        wechatGroupPoolDTO.setCorpId(wechatGroupPoolDO.getCorpId());
        wechatGroupPoolDTO.setChatId(wechatGroupPoolDO.getChatId());
        wechatGroupPoolDTO.setStatus(wechatGroupPoolDO.getStatus());
        wechatGroupPoolDTO.setEnv(wechatGroupPoolDO.getEnv());
        wechatGroupPoolDTO.setExtraInfo(wechatGroupPoolDO.getExtraInfo());
        return wechatGroupPoolDTO;
    }
}