package com.alibaba.tripscrm.service.model.domain.context;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class BaseSynchronizerContext<T> {
    private String corpId;
    private String userId;
    private String externalUserId;
    private String tagId;
    private String tagGroupId;
    private String unionId;
    private Map<String, String> extraInfo = new HashMap<>();
    private T data;

    public boolean containsInfo(String key) {
        return extraInfo.containsKey(key);
    }

    public String getInfo(String key) {
        return extraInfo.get(key);
    }

    public void addInfo(String key, String value) {
        extraInfo.put(key, value);
    }
}
