package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.GetMessageListRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetMessageListResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetMessageListResponseBody;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.List;

/**
 * 获取消息列表 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class GetMessageListProcessor implements WsEventProcessor {
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private WebSocketFactory webSocketFactory;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.GET_MESSAGE_LIST;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        GetMessageListRequest request = wsEvent.getData().toJavaObject(GetMessageListRequest.class);
        if (wsEvent.getUserId() == null || request.getChatId() == null || request.getChatType() == null) {
            throw new TripscrmException("缺少必要的参数 userId or chatId or chatType");
        }
        // 获取当前企微成员的会话列表
        List<GetMessageListResponseBody> messageList = chatMessageService.listByChatId(wsEvent.getUserId(),request.getChatId(),request.getChatType());
        GetMessageListResponse response = new GetMessageListResponse();
        response.setChatId(request.getChatId());
        response.setChatType(request.getChatType());
        response.setMessageList(messageList);
        // 推送到websocket
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        webSocketFactory.pushMessageBySession(session, wsEvent, false);
    }
}
