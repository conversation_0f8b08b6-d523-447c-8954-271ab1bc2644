package com.alibaba.tripscrm.service.convert;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.service.enums.rule.RuleGroupEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleOptionEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleStatusEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IndustryTypeEnum;
import com.alibaba.tripscrm.service.enums.system.SceneTypeEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.query.RuleQuery;
import com.alibaba.tripscrm.service.model.domain.request.RuleQueryRequest;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.monitor.MonitorRuleConfigVO;
import com.alibaba.tripscrm.service.model.vo.rule.AbstractRuleConfigVO;
import com.alibaba.tripscrm.service.model.vo.rule.RuleVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskRuleConfigVO;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RuleConverter {
    private final AccountService accountService;

    public RuleQuery queryRequest2Query(RuleQueryRequest request) {
        RuleQuery query = new RuleQuery();
        BeanUtils.copyProperties(request, query);
        query.setEnv(EnvUtils.getEnvironment());
        if (Objects.nonNull(request.getCreateEndTime())) {
            // createEndTime的后一天
            query.setCreateEndTime(DateUtils.addDays(request.getCreateEndTime(), 1));
        }
        // 如果是群监控相关请求
        if (checkTypeList(request.getTypeList())) {
            Long groupId = RuleGroupEnum.getGroupId();
            if (Objects.isNull(groupId)) {
                PlatformLogUtil.logFail("规则转换queryRequest2Query，规则组id为空", LogListUtil.newArrayList(request));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);

            }
            query.setGroupId(groupId);
        }
        return query;
    }

    public <T extends AbstractRuleConfigVO> RuleVO<T> do2vo(RuleDO ruleDO) {
        if (Objects.isNull(ruleDO)) {
            return null;
        }

        RuleVO<T> ruleVO = new RuleVO<>();
        BeanUtils.copyProperties(ruleDO, ruleVO);
        T config = JSONObject.parseObject(ruleDO.getConfig(), new TypeReference<T>() {
        });
        ruleVO.setConfig(config);
        ruleVO.setCreateTime(ruleDO.getGmtCreate());
        ruleVO.setUpdateTime(ruleDO.getGmtModified());

        if (StringUtils.hasText(ruleVO.getMemberIds())) {
            String[] memberIdList = ruleVO.getMemberIds().split(",");
            String memberNameList = Arrays.stream(memberIdList).map(accountService::getUserByAccountId).filter(Objects::nonNull).map(User::getUserName).collect(Collectors.joining(","));
            ruleVO.setMemberNames(memberNameList);
        }

        if (StringUtils.hasText(ruleVO.getCreatorId())) {
            User user = accountService.getUserByAccountId(ruleVO.getCreatorId());
            if (Objects.nonNull(user)) {
                ruleVO.setCreatorName(user.getUserName());
            }
        }

        ruleVO.setOpts(RuleOptionEnum.getRuleOptions(RuleStatusEnum.getByStatus(ruleVO.getStatus())));
        return ruleVO;
    }

    public <T extends AbstractRuleConfigVO> RuleDO vo2do(RuleVO<T> ruleVO) {
        if (Objects.isNull(ruleVO)) {
            return null;
        }

        if (Objects.nonNull(ruleVO.getConfig()) && ruleVO.getConfig() instanceof TaskRuleConfigVO) {
            checkTaskRule((TaskRuleConfigVO) ruleVO.getConfig());
        }

        // 当type为群监控时，对config进行校验
        if (Objects.nonNull(ruleVO.getConfig())
                && ruleVO.getConfig() instanceof MonitorRuleConfigVO) {
            checkGroupMonitorRule(ruleVO);
        }

        RuleDO ruleDO = new RuleDO();
        BeanUtils.copyProperties(ruleVO, ruleDO);
        ruleDO.setConfig(JSONObject.toJSONString(Optional.ofNullable(ruleVO.getConfig())));
        ruleDO.setDynamicConfig("{}");
        User user = accountService.getUserInWebThread();
        if (Objects.nonNull(user)) {
            ruleDO.setLastOperatorId(user.getUserId());
        }

        return ruleDO;
    }

    private <T extends AbstractRuleConfigVO> void checkGroupMonitorRule(RuleVO<T> ruleVO) {
        checkIndustryAndScene((MonitorRuleConfigVO) ruleVO.getConfig());
        MonitorRuleConfigVO monitorRuleConfigVO = (MonitorRuleConfigVO) ruleVO.getConfig();
        ruleVO.setName(monitorRuleConfigVO.getMonitorContent());
        Long groupId = RuleGroupEnum.getGroupId();
        if (groupId == null) {
            PlatformLogUtil.logFail("规则转换checkGroupMonitorRule，规则组id为空", LogListUtil.newArrayList(ruleVO));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);

        }
        ruleVO.setGroupId(groupId);
        ruleVO.setPriority(1);
        ruleVO.setTargetId("");
        ruleVO.setTargetType((byte) 1);
        ruleVO.setMemberIds(ruleVO.getCreatorId());
        ruleVO.setStatus((byte) 1);
    }

    private void checkTaskRule(TaskRuleConfigVO config) {
        if (!StringUtils.hasLength(config.getExecuteTime())) {
            return;
        }

        List<String> executeTimeList = Arrays.stream(config.getExecuteTime().split(";")).filter(StringUtils::hasLength).collect(Collectors.toList());
        if (executeTimeList.size() != 2) {
            throw new TripscrmException("规则执行时间非法");
        }

        try {
            LocalTime beginTime = LocalTime.parse(executeTimeList.get(0));
            LocalTime endTime = LocalTime.parse(executeTimeList.get(1));
            if (beginTime.isAfter(endTime)) {
                throw new TripscrmException("规则执行起始时间不可晚于结束时间");
            }
        } catch (Exception e) {
            throw new TripscrmException("规则执行时间非法");
        }
    }

    private void checkIndustryAndScene(MonitorRuleConfigVO config) {
        if (!IndustryTypeEnum.isValid(Integer.valueOf(config.getIndustry()))) {
            PlatformLogUtil.logFail("规则转换checkIndustryAndScene，行业为空", LogListUtil.newArrayList(config));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        if (!SceneTypeEnum.isValid(Integer.valueOf(config.getScene()))) {
            PlatformLogUtil.logFail("规则转换checkIndustryAndScene，场景为空", LogListUtil.newArrayList(config));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
    }

    private boolean checkTypeList(List<Byte> typeList) {
        if (CollectionUtils.isEmpty(typeList)) {
            return false;
        }
        Set<Integer> allowedValues = new HashSet<Integer>(Arrays.asList(RuleTypeEnum.GROUP_MONITOR_KEYWORD.getCode(), RuleTypeEnum.GROUP_MONITOR_MINI_PROGRAM_CARD.getCode(), RuleTypeEnum.GROUP_MONITOR_OTHER_CORP_CUSTOMER.getCode()));
        for (Byte type : typeList) {
            if (!allowedValues.contains(Integer.valueOf(type))) {
                return false;
            }
        }
        return true;
    }
}
