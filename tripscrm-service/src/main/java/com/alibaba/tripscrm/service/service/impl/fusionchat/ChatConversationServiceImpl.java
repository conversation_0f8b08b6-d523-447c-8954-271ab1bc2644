package com.alibaba.tripscrm.service.service.impl.fusionchat;

import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerManageGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.CustomerRelationDO;
import com.alibaba.tripscrm.dal.model.domain.query.fusionchat.ChatConversationListQuery;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.dal.repository.CustomerRelationRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.middleware.RedisManager;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.domain.context.DataContext;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationCreateParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateLastMessageParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateParam;
import com.alibaba.tripscrm.service.model.vo.fusionchat.ChatConversationVO;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.fusionchat.CustomerManageService;
import com.alibaba.tripscrm.service.service.fusionchat.FusionChatService;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetUserConversationResponseBody;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.ManagerWechatUser;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.AsyncUpdateUserConversationConfigProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.system.BeanCopyUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.request.WechatGroupWorkListRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.eagleeye.redis.clients.jedis.Pipeline;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用户会话 Manager实现类
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class ChatConversationServiceImpl implements ChatConversationService {
    public static final String DEFAULT_GROUP_AVATAR = "https://img.alicdn.com/imgextra/i3/O1CN01p7WVQd1dtFAFybKo7_!!6000000003793-2-tps-200-200.png";
    public static final String USER_DOING_CHAT_CACHE_KEY = "userDoingChat|";
    public static final String WECHAT_USER_UNREAD_COUNT = "wechatUserUnreadCount|";
    public static final List<String> IGNORE_UNREAD_MESSAGE_LIST = Stream.of("现在我们可以开始聊天了").collect(Collectors.toList());
    @Resource
    private ChatConversationRepository chatConversationRepository;
    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private WechatCustomerService wechatCustomerService;
    @Resource
    private AsyncUpdateUserConversationConfigProcessor asyncUpdateUserConversationConfigProcessor;
    @Resource
    private GroupService groupService;
    @Resource
    private LdbTairManager ldbTairManager;
    @Autowired(required = false)
    private RedisManager redisManager;
    @Resource
    private CustomerRelationRepository customerRelationRepository;
    @Resource
    private FusionChatService fusionChatService;
    @Resource
    private CustomerManageService customerManageService;

    @Override
    public Integer upsertChatConversation(ChatConversationDO record) {
        return chatConversationRepository.upsert(record);
    }

    @Override
    public ChatConversationVO createChatConversation(ChatConversationCreateParam param) {
        ChatConversationDO create = BeanCopyUtils.copy(param, ChatConversationCreateParam.class, ChatConversationDO.class);
        create = chatConversationRepository.create(create);
        return BeanCopyUtils.copy(create, ChatConversationDO.class, ChatConversationVO.class);
    }

    @Override
    public ChatConversationDO updateChatConversationConfig(ChatConversationUpdateParam update) {
        ChatConversationDO chatConversation = chatConversationRepository.getByUniqueKey(update.getUserId(), update.getChatId(), update.getChatType());
        if (update.getTop() != null) {
            chatConversation.setTopNo(update.getTop() ? 1 : 0);
            chatConversation.setUpdateTimestamp(System.currentTimeMillis());
        }
        if (update.getDnd() != null) {
            chatConversation.setDnd(update.getDnd());
        }

        // 当进入会话时，在tair中更新当前用户所在会话（用于判断是否更新消息未读数）
        if (update.getEnterChat() != null && update.getEnterChat()) {
            // 减少企微号的消息未读数
            decrWechatUserUnreadCount(update.getCorpId(), update.getUserId(), chatConversation.getChatId());
            // 设置未读数为0
            chatConversation.setUnreadCount(0);
            ldbTairManager.put(USER_DOING_CHAT_CACHE_KEY + update.getUserId(), chatConversation.getChatId());
            // 更新企微端会话已读
            asyncUpdateUserConversationConfigProcessor.markAsReadMessage(chatConversation, true);
        }
        // 关闭会话
        if (update.getCloseChat() != null) {
            chatConversation.setDeleted(update.getCloseChat());
            // 若在当前会话，则删除
            if (chatConversation.getChatId().equals(ldbTairManager.get(USER_DOING_CHAT_CACHE_KEY + update.getUserId()))) {
                ldbTairManager.delete(USER_DOING_CHAT_CACHE_KEY + update.getUserId());
            }
        }
        // 标记已读/未读
        if (update.getMarkRead() != null) {
            if (update.getMarkRead()) {
                // 减少企微号的消息未读数
                decrWechatUserUnreadCount(update.getCorpId(), update.getUserId(), chatConversation.getChatId());
                // 设置未读数为0
                chatConversation.setUnreadCount(0);
            } else {
                if (chatConversation.getUnreadCount() == 0) {
                    // 增加企微号的消息未读数+1
                    incrWechatUserUnreadCount(update.getCorpId(), update.getUserId(), update.getChatId());
                    // 直接设置消息未读数为1
                    chatConversation.setUnreadCount(1);
                }
            }
        }
        chatConversationRepository.update(chatConversation);
        return chatConversation;
    }

    @Override
    public ChatConversationDO updateChatConversationLastMessage(ChatConversationUpdateLastMessageParam param) {
        ChatConversationDO one = chatConversationRepository.getByUniqueKey(param.getUserId(), param.getChatId(), param.getChatType());
        ChatConversationDO chatConversation = new ChatConversationDO();
        chatConversation.setId(one.getId());
        chatConversation.setLastMessageSenderName(param.getLastMessageSenderName());
        chatConversation.setLastMessageContent(param.getLastMessageContent());
        chatConversation.setLastMessageCreateTime(param.getLastMessageCreateTime());
        chatConversation.setUpdateTimestamp(param.getUpdateTimestamp());
        ChatConversationDO chatConversationDO = chatConversationRepository.update(chatConversation);

        // 在tair中获取当前用户所在会话，以此判断是否更新当前会话的消息未读数）
        String doingChatId = (String) ldbTairManager.get(USER_DOING_CHAT_CACHE_KEY + param.getUserId());
        // 若是进行中的会话 或 发送人是我的消息，则不更新未读数，否则更新
        if ((doingChatId != null && doingChatId.equals(one.getChatId()))
                || param.getUserId().equals(param.getSenderId())
                || IGNORE_UNREAD_MESSAGE_LIST.stream().anyMatch(x -> param.getLastMessageContent().contains(x))) {
            return chatConversationDO;
        }

        // 增加企微号的消息未读数+1
        incrWechatUserUnreadCount(param.getCorpId(), param.getUserId(), param.getChatId());
        redisManager.execute(jedis ->
                chatConversationDO.setUnreadCount(Integer.parseInt(Optional.ofNullable(jedis.hget(getUserUnreadCountKey(param.getCorpId(), param.getUserId()), param.getChatId())).orElse("0")))
        );
        return chatConversationDO;
    }

    public Integer addChatConversationUnreadCount(String userId, String senderId, String chatId, String corpId, String content) {
        int[] unreadCount = new int[1];
        // 在tair中获取当前用户所在会话，以此判断是否更新当前会话的消息未读数）
        String doingChatId = (String) ldbTairManager.get(USER_DOING_CHAT_CACHE_KEY + userId);
        // 若是进行中的会话 或 发送人是我的消息，则不更新未读数，否则更新
        if ((doingChatId != null && doingChatId.equals(chatId))
                || Objects.equals(userId, senderId)
                || IGNORE_UNREAD_MESSAGE_LIST.stream().anyMatch(content::contains)) {
            redisManager.execute(jedis ->
                    unreadCount[0] = Integer.parseInt(Optional.ofNullable(jedis.hget(getUserUnreadCountKey(corpId, userId), chatId)).orElse("0"))
            );
            return unreadCount[0];
        }

        // 增加企微号的消息未读数+1
        incrWechatUserUnreadCount(corpId, userId, chatId);
        redisManager.execute(jedis ->
                unreadCount[0] = Integer.parseInt(Optional.ofNullable(jedis.hget(getUserUnreadCountKey(corpId, userId), chatId)).orElse("0"))
        );
        return unreadCount[0];
    }

    @Override
    public List<GetUserConversationResponseBody> listByUserId(String userId) {
        String corpId = SpaceInfoThreadLocalUtils.getCorpId();
        List<ChatConversationDO> list = chatConversationRepository.listByUserId(corpId, userId, 1000);
        List<GetUserConversationResponseBody> result = BeanCopyUtils.copyList(list, ChatConversationDO.class, GetUserConversationResponseBody.class);
        // 从redis读取会话消息未读数
        redisManager.execute(jedis -> {
            Pipeline pipeline = jedis.pipelined();
            result.forEach(getUserConversationResponseBody ->
                    pipeline.hget(getUserUnreadCountKey(corpId, userId), getUserConversationUnreadCountHashKey(getUserConversationResponseBody.getChatId()))
            );
            List<Object> results = pipeline.syncAndReturnAll();
            for (int i = 0; i < result.size(); i++) {
                result.get(i).setUnreadCount(Integer.parseInt(String.valueOf(Optional.ofNullable(results.get(i)).orElse("0"))));
            }
        });

        Map<Integer, List<GetUserConversationResponseBody>> groupMap = result.stream().collect(Collectors.groupingBy(GetUserConversationResponseBody::getChatType));
        // 准备群聊数据
        List<String> groupChatIds = groupMap.getOrDefault(ChatTypeEnum.GROUP.getValue(), Collections.emptyList()).stream().map(GetUserConversationResponseBody::getChatId).collect(Collectors.toList());
        List<WechatGroupVO> wechatGroups = wechatGroupService.listByChatIdList(groupChatIds, false);
        Map<String, WechatGroupVO> wechatGroupMap = wechatGroups.stream().collect(Collectors.toMap(WechatGroupVO::getChatId, Function.identity()));
        // 准备与企微客户的私聊数据
        List<String> customerIds = groupMap.getOrDefault(ChatTypeEnum.SINGLE_FOR_CUSTOMER.getValue(), Collections.emptyList()).stream().map(GetUserConversationResponseBody::getChatId).collect(Collectors.toList());
        List<WechatCustomerVO> customers = wechatCustomerService.listByExternalUserIdList(customerIds);
        Map<String, WechatCustomerVO> customerMap = customers.stream().collect(Collectors.toMap(WechatCustomerVO::getExternalUserId, Function.identity()));
        List<CustomerRelationDO> customerRelations = customerRelationRepository.listByExternalUserIds(userId, customerIds, SpaceInfoThreadLocalUtils.getCorpId());
        Map<String, CustomerRelationDO> customerRelationMap = customerRelations.stream().collect(Collectors.toMap(CustomerRelationDO::getExternalUserId, Function.identity()));
        // 准备与企微成员的私聊数据
        List<String> userIds = groupMap.getOrDefault(ChatTypeEnum.SINGLE_FOR_USER.getValue(), Collections.emptyList()).stream().map(GetUserConversationResponseBody::getChatId).collect(Collectors.toList());
        List<WechatUserDTO> users = wechatUserService.listById(userIds);
        Map<String, WechatUserDTO> userMap = users.stream().collect(Collectors.toMap(WechatUserDTO::getUserId, Function.identity()));
        for (GetUserConversationResponseBody responseBody : result) {
            switch (ChatTypeEnum.parse(responseBody.getChatType())) {
                case GROUP:
                    // 设置群会话名称与头像
                    WechatGroupVO wechatGroupVO = wechatGroupMap.get(responseBody.getChatId());
                    if (wechatGroupVO != null) {
                        responseBody.setChatName(wechatGroupVO.getName());
                        responseBody.setChatAvatar(DEFAULT_GROUP_AVATAR);
                    }
                    break;
                case SINGLE_FOR_CUSTOMER:
                    // 设置私聊会话名称与头像
                    WechatCustomerVO wechatCustomerVO = customerMap.get(responseBody.getChatId());
                    if (wechatCustomerVO != null) {
                        responseBody.setChatName(wechatCustomerVO.getName());
                        responseBody.setChatAvatar(wechatCustomerVO.getAvatarUrl());
                        responseBody.setCorpName("微信");
                        CustomerRelationDO customerRelation = customerRelationMap.get(responseBody.getChatId());
                        if (customerRelation != null && customerRelation.getRemark() != null) {
                            responseBody.setChatName(customerRelation.getRemark());
                        }
                    }
                    break;
                case SINGLE_FOR_USER:
                    // 设置私聊会话名称与头像
                    WechatUserDTO wechatUserDTO = userMap.get(responseBody.getChatId());
                    if (wechatUserDTO != null) {
                        responseBody.setChatName(wechatUserDTO.getName());
                        responseBody.setChatAvatar(wechatUserDTO.getAvatarUrl());
                        responseBody.setCorpName(wechatUserDTO.getCorpName());
                    }
                    break;
                default:
                    break;
            }
        }
        // 获取关注的群聊列表
        WechatGroupWorkListRequest wechatGroupWorkListRequest = new WechatGroupWorkListRequest();
        wechatGroupWorkListRequest.setUserId(userId);
        wechatGroupWorkListRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        ResultDO<List<String>> workResult = groupService.listWorkGroupId(wechatGroupWorkListRequest);
        if (workResult.getSuccess()) {
            PlatformLogUtil.logFail("获取关注群聊列表成功", LogListUtil.newArrayList(wechatGroupWorkListRequest, workResult, userId));
            if (workResult.getModel() != null) {
                for (GetUserConversationResponseBody getUserConversationResponseBody : result) {
                    if (getUserConversationResponseBody.getChatType().equals(ChatTypeEnum.GROUP.getValue())) {
                        getUserConversationResponseBody.setGroupWork(workResult.getModel().contains(getUserConversationResponseBody.getChatId()));
                    }
                }
            }
        } else {
            PlatformLogUtil.logFail("获取关注群聊信息失败", LogListUtil.newArrayList(wechatGroupWorkListRequest, workResult, userId));
        }
        return result;
    }

    @Override
    public List<GetUserConversationResponseBody> listByParam(ChatConversationListQuery query) {
        // 基础数据查询
        List<ChatConversationDO> chatConversationList = getOrCreateConversation(query);
        if (CollectionUtils.isEmpty(chatConversationList)) {
            return Lists.newArrayList();
        }
        List<GetUserConversationResponseBody> conversationResponseList =
                BeanCopyUtils.copyList(chatConversationList, ChatConversationDO.class, GetUserConversationResponseBody.class);
        // 统一数据准备（减少IO次数）
        Map<Integer, List<GetUserConversationResponseBody>> chatTypeMap = conversationResponseList.stream()
                .collect(Collectors.groupingBy(GetUserConversationResponseBody::getChatType));
        DataContext dataContext = new DataContext();
        dataContext.setCorpId(query.getCorpId());
        dataContext.setWechatGroupList(wechatGroupService.listByChatIdList(getChatIdList(chatTypeMap, ChatTypeEnum.GROUP), true));
        dataContext.setWechatCustomerList(wechatCustomerService.listByExternalUserIdList(getChatIdList(chatTypeMap, ChatTypeEnum.SINGLE_FOR_CUSTOMER)));
        dataContext.setWechatUserList(wechatUserService.listById(getChatIdList(chatTypeMap, ChatTypeEnum.SINGLE_FOR_USER)));
        dataContext.setCustomerRelationList(customerRelationRepository.listByExternalUserIds(query.getUserId()
                , getChatIdList(chatTypeMap, ChatTypeEnum.SINGLE_FOR_CUSTOMER), SpaceInfoThreadLocalUtils.getCorpId()));

        // 从redis读取会话消息未读数
        redisManager.execute(jedis -> {
            Pipeline pipeline = jedis.pipelined();
            conversationResponseList.forEach(getUserConversationResponseBody ->
                    pipeline.hget(getUserUnreadCountKey(query.getCorpId(), query.getUserId()), getUserConversationUnreadCountHashKey(getUserConversationResponseBody.getChatId()))
            );
            List<Object> results = pipeline.syncAndReturnAll();
            for (int i = 0; i < conversationResponseList.size(); i++) {
                conversationResponseList.get(i).setUnreadCount(Integer.parseInt(String.valueOf(Optional.ofNullable(results.get(i)).orElse("0"))));
            }
        });


        // 补充会话数据
        return conversationResponseList.stream()
                .map(getUserConversationResponseBody -> fillConversationInfo(getUserConversationResponseBody, dataContext))
                .collect(Collectors.toList());
    }

    /**
     * 获取或者创建一个会话
     *
     * @param query 查询条件
     * @return 会话框列表
     */
    private List<ChatConversationDO> getOrCreateConversation(ChatConversationListQuery query) {
        // 存量会话框获取
        List<ChatConversationDO> canUsedConversationList = chatConversationRepository.listByParam(query);
        if (CollectionUtils.isEmpty(query.getChatIdList()) ||
                (CollectionUtils.isNotEmpty(query.getChatIdList()) && query.getChatIdList().size() == canUsedConversationList.size())) {
            // 没有指定聊天框/指定的聊天框都存在
            return canUsedConversationList;
        }
        // 需要处理的聊天
        List<String> canUsedConversationChatIdList = canUsedConversationList.stream()
                .map(ChatConversationDO::getChatId).collect(Collectors.toList());
        List<String> notExistCanUsedConversationIdList = query.getChatIdList().stream()
                .filter(chatId -> !canUsedConversationChatIdList.contains(chatId)).collect(Collectors.toList());
        // 处理会话框
        List<ChatConversationDO> newConversationList = dealNotExistConversation(query.getUserId(), query.getChatType(), notExistCanUsedConversationIdList);
        return Lists.newArrayList(CollectionUtils.union(canUsedConversationList, newConversationList));
    }

    /**
     * 处理不存在会话的聊天列表
     *
     * @param notExistCanUsedConversationIdList 不存在可用的会话聊天ID列表
     * @return 会话列表
     */
    private List<ChatConversationDO> dealNotExistConversation(String userId, Integer chatType, List<String> notExistCanUsedConversationIdList) {
        List<ChatConversationDO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(notExistCanUsedConversationIdList) || StringUtils.isBlank(userId) || chatType == null) {
            return result;
        }
        // 已删除的会话列表
        List<ChatConversationDO> deletedConversationList = notExistCanUsedConversationIdList.stream()
                .map(chatId -> chatConversationRepository.getByUniqueKeyWithNull(userId, chatId, chatType))
                .filter(chatConversationDO -> Objects.nonNull(chatConversationDO) && chatConversationDO.getDeleted()).collect(Collectors.toList());
        List<String> deletedChatIdList = deletedConversationList.stream().map(ChatConversationDO::getChatId).collect(Collectors.toList());
        for (ChatConversationDO chatConversationDO : deletedConversationList) {
            long currentTimeMillis = System.currentTimeMillis();
            // 更新部分字段
            ChatConversationDO newChatConversation = new ChatConversationDO();
            newChatConversation.setId(chatConversationDO.getId());
            newChatConversation.setDeleted(false);
            newChatConversation.setUpdateTimestamp(currentTimeMillis);
            ChatConversationDO updateConversation = chatConversationRepository.update(newChatConversation);
            // 结果集填充
            updateConversation.setDeleted(false);
            updateConversation.setUpdateTimestamp(currentTimeMillis);
            result.add(updateConversation);
        }
        // 未创建的会话列表
        List<String> needCreateChatIdList = notExistCanUsedConversationIdList.stream().filter(chatId -> !deletedChatIdList.contains(chatId)).collect(Collectors.toList());
        for (String chatId : needCreateChatIdList) {
            ChatConversationDO chatConversationDO = new ChatConversationDO();
            chatConversationDO.setUserId(userId);
            chatConversationDO.setChatType(chatType);
            chatConversationDO.setChatId(chatId);
            chatConversationDO.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
            result.add(chatConversationRepository.create(chatConversationDO));
        }
        return result;
    }

    @Override
    public Long countByParam(ChatConversationListQuery query) {
        return chatConversationRepository.countByParam(query);
    }

    /**
     * 从指定集合中获取指定key值
     *
     * @param chatTypeMap  指定集合
     * @param chatTypeEnum 指定key类型
     * @return 指定ID列表
     */
    private List<String> getChatIdList(Map<Integer, List<GetUserConversationResponseBody>> chatTypeMap, ChatTypeEnum chatTypeEnum) {
        if (MapUtils.isEmpty(chatTypeMap)) {
            return Lists.newArrayList();
        }
        return chatTypeMap.getOrDefault(chatTypeEnum.getValue(), Collections.emptyList())
                .stream()
                .map(GetUserConversationResponseBody::getChatId)
                .collect(Collectors.toList());
    }

    /**
     * 补充会话数据
     *
     * @param getUserConversationResponseBody 会话消息体
     * @return 会话消息体
     */
    private GetUserConversationResponseBody fillConversationInfo(GetUserConversationResponseBody getUserConversationResponseBody, DataContext dataContext) {
        // 参数校验
        if (getUserConversationResponseBody == null || ChatTypeEnum.parse(getUserConversationResponseBody.getChatType()) == null) {
            return getUserConversationResponseBody;
        }
        // 分类填充
        switch (ChatTypeEnum.parse(getUserConversationResponseBody.getChatType())) {
            case GROUP:
                fillGroupConversationInfo(getUserConversationResponseBody
                        , Optional.ofNullable(dataContext.getWechatGroupMap())
                                .orElse(new HashMap<>(1)).get(getUserConversationResponseBody.getChatId())
                        , dataContext.getCorpId());
                break;
            case SINGLE_FOR_CUSTOMER:
                fillCustomerConversationInfo(getUserConversationResponseBody,
                        Optional.ofNullable(dataContext.getWechatCustomerMap())
                                .orElse(new HashMap<>(1)).get(getUserConversationResponseBody.getChatId())
                        , Optional.ofNullable(dataContext.getCustomerRelationMap())
                                .orElse(new HashMap<>(1)).get(getUserConversationResponseBody.getChatId()));
                break;
            case SINGLE_FOR_USER:
                fillUserConversationInfo(getUserConversationResponseBody
                        , Optional.ofNullable(dataContext.getWechatUserMap())
                                .orElse(new HashMap<>(1)).get(getUserConversationResponseBody.getChatId()));
                break;
            default:
                break;
        }
        return getUserConversationResponseBody;
    }

    /**
     * 填充成员会话信息
     *
     * @param responseBody 会话信息
     * @param wechatUserDTO 企微成员信息
     */
    private void fillUserConversationInfo(GetUserConversationResponseBody responseBody
            , WechatUserDTO wechatUserDTO) {
        if (wechatUserDTO == null) {
            return;
        }
        responseBody.setChatName(wechatUserDTO.getName());
        responseBody.setChatAvatar(wechatUserDTO.getAvatarUrl());
        responseBody.setCorpName(wechatUserDTO.getCorpName());
    }

    /**
     * 填充客户会话信息
     *
     * @param responseBody     会话信息
     * @param wechatCustomerVO 企微客户信息
     * @param customerRelation 客户关系信息
     */
    private void fillCustomerConversationInfo(GetUserConversationResponseBody responseBody
            , WechatCustomerVO wechatCustomerVO, CustomerRelationDO customerRelation) {
        if (wechatCustomerVO != null) {
            responseBody.setChatName(wechatCustomerVO.getName());
            responseBody.setChatAvatar(wechatCustomerVO.getAvatarUrl());
            responseBody.setCorpName("微信");
            CustomerManageGroupDO customerManageGroupDO = customerManageService
                    .queryGroupByUser(responseBody.getUserId(), responseBody.getChatId(), responseBody.getCorpId());
            if (customerManageGroupDO != null) {
                responseBody.setCustomerManageGroupId(customerManageGroupDO.getId());
                responseBody.setCustomerManageGroupName(customerManageGroupDO.getName());
            } else {
                responseBody.setCustomerManageGroupId(-1L);
                responseBody.setCustomerManageGroupName("未分类");
            }
            if (customerRelation != null && customerRelation.getRemark() != null) {
                responseBody.setChatName(customerRelation.getRemark());
            }
        }
    }

    /**
     * 填充群聊会话信息
     *
     * @param responseBody  会话信息
     * @param wechatGroupVO 企微群聊信息
     */
    private void fillGroupConversationInfo(GetUserConversationResponseBody responseBody
            , WechatGroupVO wechatGroupVO, String corpId) {
        if (wechatGroupVO == null) {
            return;
        }
        responseBody.setChatName(wechatGroupVO.getName());
        responseBody.setChatAvatar(DEFAULT_GROUP_AVATAR);
        responseBody.setGroupWork(true);
        CustomerManageGroupDO customerManageGroupDO = customerManageService
                .queryGroupByGroup(responseBody.getUserId(), responseBody.getChatId(), responseBody.getCorpId());
        if (customerManageGroupDO != null) {
            responseBody.setCustomerManageGroupId(customerManageGroupDO.getId());
            responseBody.setCustomerManageGroupName(customerManageGroupDO.getName());
        } else {
            responseBody.setCustomerManageGroupId(-1L);
            responseBody.setCustomerManageGroupName("未分类");
        }
    }

    @Override
    public Integer getWechatUserUnreadCount(String corpId, String userId) {
        int[] result = new int[1];
        // 修改redis
        redisManager.execute(jedis -> {
            // 定义要操作的键
            result[0] = Integer.parseInt(Optional.ofNullable(jedis.hget(getUserUnreadCountKey(corpId, userId), getUserAllUnreadCountHashKey())).orElse("0"));
        });

        return result[0];
    }

    @Override
    public Integer getWechatUserConversationUnreadCount(String corpId, String userId, String chatId) {
        int[] result = new int[1];
        // 修改redis
        redisManager.execute(jedis -> {
            // 定义要操作的键
            result[0] = Integer.parseInt(Optional.ofNullable(jedis.hget(getUserUnreadCountKey(corpId, userId), getUserConversationUnreadCountHashKey(chatId))).orElse("0"));
        });

        return result[0];
    }

    /**
     * 增加企微号的消息未读数
     *
     * @param userId 企微号id
     */
    @AteyeInvoker(description = "增加企微消息未读数", paraDesc = "corpId&userId&chatId")
    public void incrWechatUserUnreadCount(String corpId, String userId, String chatId) {
        // 修改redis
        redisManager.execute(jedis -> {
            // Lua 脚本
            String script = "local allUnreadCountHashKey = ARGV[1] "
                    + "local conversationUnreadCountHashKey = ARGV[2] "
                    + "local count = tonumber(ARGV[3]) "
                    + "redis.call('HINCRBY', KEYS[1], allUnreadCountHashKey, count) "
                    + "redis.call('HINCRBY', KEYS[1], conversationUnreadCountHashKey, count)";
            PlatformLogUtil.logInfo("执行增加企微消息未读数操作", LogListUtil.newArrayList(script, getUserUnreadCountKey(corpId, userId), getUserAllUnreadCountHashKey(), getUserConversationUnreadCountHashKey(chatId)));
            // 执行 Lua 脚本
            jedis.eval(script
                    , Lists.newArrayList(getUserUnreadCountKey(corpId, userId))
                    , Lists.newArrayList(getUserAllUnreadCountHashKey(), getUserConversationUnreadCountHashKey(chatId), "1"));
        });
    }

    /**
     * 减少企微号的消息未读数
     *
     * @param userId 企微号id
     */
    @AteyeInvoker(description = "减少企微消息未读数", paraDesc = "corpId&userId&msgCount&chatId")
    public void decrWechatUserUnreadCount(String corpId, String userId, String chatId) {
        // 修改redis
        redisManager.execute(jedis -> {
            // Lua 脚本
            String script = "local allUnreadCountHashKey = ARGV[1] "
                    + "local conversationUnreadCountHashKey = ARGV[2] "
                    + "local exists = redis.call('HEXISTS', KEYS[1], conversationUnreadCountHashKey) "
                    + "if exists ~= 1 then"
                    + "  return 1 "
                    + "end "
                    + "local conversationUnreadCount = tonumber(redis.call('HGET', KEYS[1], conversationUnreadCountHashKey)) "
                    + "redis.call('HDEL', KEYS[1], conversationUnreadCountHashKey) "
                    + "redis.call('HINCRBY', KEYS[1], allUnreadCountHashKey, conversationUnreadCount * -1) ";

            PlatformLogUtil.logInfo("执行减少企微消息未读数操作", LogListUtil.newArrayList(script, getUserUnreadCountKey(corpId, userId), getUserAllUnreadCountHashKey(), getUserConversationUnreadCountHashKey(chatId)));
            // 执行 Lua 脚本
            jedis.eval(script
                    , Lists.newArrayList(getUserUnreadCountKey(corpId, userId))
                    , Lists.newArrayList(getUserAllUnreadCountHashKey(), getUserConversationUnreadCountHashKey(chatId)));
        });
    }

    /**
     * 用户所有会话未读消息总数 redis-key
     *
     * @param corpId 企微企业Id
     * @param userId 企微userId
     * @return redis-key
     */
    private String getUserUnreadCountKey(String corpId, String userId) {
        return String.format("{%s_%s}_unread_count", userId, corpId);
    }

    /**
     * 用户所有会话未读消息总数 redis-key
     *
     * @return redis-key
     */
    private String getUserAllUnreadCountHashKey() {
        return "all";
    }

    /**
     * 用户所有会话未读消息总数 redis-key
     *
     * @param chatId 会话Id
     * @return redis-key
     */
    private String getUserConversationUnreadCountHashKey(String chatId) {
        return chatId;
    }

    @Override
    public void cleanUserDoingChat(User account, Long spaceId) {
        // 清除该平台账号锁定企微号的进行中会话标记缓存
        List<ManagerWechatUser> lockUsers = fusionChatService.listLockUsers(account.getUserId(), spaceId);
        for (ManagerWechatUser lockUser : lockUsers) {
            ldbTairManager.delete(USER_DOING_CHAT_CACHE_KEY + lockUser.getUserId());
        }
    }

    @Override
    public void cleanUserDoingChat(User account, String userId) {
        ldbTairManager.delete(USER_DOING_CHAT_CACHE_KEY + userId);
    }
}