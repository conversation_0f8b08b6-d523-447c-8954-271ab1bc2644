package com.alibaba.tripscrm.service.annotation.aspect;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.util.log.monitor.TaskMonitorLoggerUtils;
import com.google.common.collect.Lists;
import org.apache.ibatis.exceptions.IbatisException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023-10-30 15:01:57
 */
@Aspect
@Component
public class TaskExecuteLogAspect {
    @Around(value = "@annotation(log)")
    public Object logAspect(ProceedingJoinPoint point, TaskExecuteLog log) throws Throwable {
        long startTime = System.currentTimeMillis();
        String className = null;
        String methodName = null;
        String mark = null;
        Long activityId = null;
        Long taskId = null;
        Boolean isSubTask = null;
        Long mainTaskInstanceId = null;
        Long subTaskInstanceId = null;
        ActivityTargetTypeEnum targetType = null;
        String targetId = null;

        try {
            mark = StringUtils.hasText(log.value()) ? log.value() : null;
            className = ClassUtils.getShortName(point.getTarget().getClass().getName());
            methodName = point.getSignature().getName();
            Object[] args = point.getArgs();
            TaskExecuteContext context = null;
            TodoTaskVO todoTaskVO = null;
            for (Object arg : args) {
                if (arg instanceof TaskExecuteContext) {
                    context = (TaskExecuteContext) arg;
                }
                if (arg instanceof TodoTaskVO) {
                    todoTaskVO = (TodoTaskVO) arg;
                }
            }

            // 没有任务执行上下文，无法处理
            if (Objects.isNull(context)) {
                return point.proceed();
            }

            taskId = context.getTaskId();
            isSubTask = context.getIsSub();
            mainTaskInstanceId = isSubTask ? context.getMainTaskInstanceId() : context.getInstanceId();
            subTaskInstanceId = isSubTask ? context.getInstanceId() : null;
            TaskInfoDO taskInfoDOSnapshot = context.getTaskInfoDOSnapshot();
            if (Objects.nonNull(taskInfoDOSnapshot)) {
                activityId = taskInfoDOSnapshot.getActivityId();
            }
            if (Lists.newArrayList(TaskTriggerTypeEnum.EVENT, TaskTriggerTypeEnum.INTERFACE).contains(context.getTriggerType())) {
                TaskDataVO.DataBodyVO dataBodyVO = context.getTaskDataVO().getData().get(0);
                targetType = ActivityTargetTypeEnum.codeOf(dataBodyVO.getTargetType());
                targetId = dataBodyVO.getTargetId();
            }

            if (Objects.nonNull(todoTaskVO)) {
                targetType = ActivityTargetTypeEnum.codeOf(todoTaskVO.getData().get(0).getTargetType());
                targetId = todoTaskVO.getData().get(0).getTargetId();
            }

            Object result = point.proceed();
            if (result != null) {
                // 其他类型暂时无法处理
                if (!(result instanceof TripSCRMResult)) {
                    return result;
                }

                TripSCRMResult<?> response = (TripSCRMResult<?>) result;
                TaskMonitorLoggerUtils.record(mark, className, methodName, activityId, taskId, mainTaskInstanceId, subTaskInstanceId, isSubTask, targetId, targetType, TripSCRMErrorCode.codeOf(response.getCode()), System.currentTimeMillis() - startTime, response.getMsg());
                return result;
            }

            TaskMonitorLoggerUtils.record(mark, className, methodName, activityId, taskId, mainTaskInstanceId, subTaskInstanceId, isSubTask, targetId, targetType, TripSCRMErrorCode.UNKNOWN, System.currentTimeMillis() - startTime, null);
            return result;
        } catch (TripscrmException e) {
            TaskMonitorLoggerUtils.record(mark, className, methodName, activityId, taskId, mainTaskInstanceId, subTaskInstanceId, isSubTask, targetId, targetType, TripSCRMErrorCode.codeOf(e.getErrorCode()), System.currentTimeMillis() - startTime, e.getErrorMsg());
            throw e;
        } catch (IbatisException | MyBatisSystemException e) {
            TaskMonitorLoggerUtils.record(mark, className, methodName, activityId, taskId, mainTaskInstanceId, subTaskInstanceId, isSubTask, targetId, targetType, TripSCRMErrorCode.DB_FAILED, System.currentTimeMillis() - startTime, e.getMessage());
            throw e;
        } catch (Throwable e) {
            TaskMonitorLoggerUtils.record(mark, className, methodName, activityId, taskId, mainTaskInstanceId, subTaskInstanceId, isSubTask, targetId, targetType, TripSCRMErrorCode.SYSTEM_EXCEPTION, System.currentTimeMillis() - startTime, e.getMessage());
            throw e;
        }
    }
}
