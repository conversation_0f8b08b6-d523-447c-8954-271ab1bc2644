package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * 客户在群内艾特企微成员【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerAtUserInGroupCallbackProcessor implements ProxyCallbackProcessor {
    private final MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.WECHAT_CUSTOMER_AT_USER_IN_GROUP;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject content = JSONObject.parseObject(Optional.ofNullable(scrmCallbackMsg.getContent()).orElse("{}"));
        String chatId = content.getString("chatId");
        String userId = content.getString("userId");
        String externalUserId = content.getString("externalUserId");
        if (!StringUtils.hasText(chatId) || !StringUtils.hasText(userId) || !StringUtils.hasText(externalUserId)) {
            PlatformLogUtil.logFail("客户在群内艾特企微成员【三方回调】，消息内容校验失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }

        JSONObject message = new JSONObject();
        message.put("chatId", chatId);
        message.put("userId", userId);
        message.put("externalUserId", externalUserId);
        message.put("eventType", "atUserInGroup");
        message.put("corpId", scrmCallbackMsg.getPlatformCorpId());
        metaqProducer.send(MQEnum.NOTIFY_WECHAT_USER_ADD_CUSTOMER_BY_GROUP, "", "atUserInGroup", message.toJSONString());
        return true;
    }
}
