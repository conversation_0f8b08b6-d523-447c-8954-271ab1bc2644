package com.alibaba.tripscrm.service.service.task.effectdata;

import com.alibaba.tripscrm.service.model.domain.query.ScrmTaskTotalEffectDataQuery;
import com.alibaba.tripscrm.service.model.dto.task.ScrmTaskTotalEffectDataDTO;

import java.util.List;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/6/24 16:51
 * @Filename：ScrmTaskTotalEffectDataService
 */
public interface ScrmTaskTotalEffectDataService {

    /**
     * 查询
     * @param query
     * @return
     */
    List<ScrmTaskTotalEffectDataDTO> list(ScrmTaskTotalEffectDataQuery query);
}
