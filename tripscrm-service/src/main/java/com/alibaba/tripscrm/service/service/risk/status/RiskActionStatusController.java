package com.alibaba.tripscrm.service.service.risk.status;

import com.alibaba.tripscrm.dal.model.domain.data.RiskObjectDO;
import com.alibaba.tripscrm.dal.model.domain.data.RuleDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.dal.repository.RiskObjectRepository;
import com.alibaba.tripscrm.dal.repository.WechatUserRepository;
import com.alibaba.tripscrm.service.enums.risk.RateLimitTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskTargetEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTargetEnum;
import com.alibaba.tripscrm.service.enums.rule.RuleTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbLockManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.rule.RuleService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 行动项风控状态控制器
 */
@Component
public class RiskActionStatusController {
    public static final String ACTION_STATUS_CHANGE_PREFIX = "ACTION_STATUS_CHANGE_";
    public static final String ACTION_STATUS_CACHE_PREFIX = "ACTION_STATUS_CACHE_";
    public static final String ACTION_STATUS_CACHE_LIMIT_TYPE_PREFIX = "ACTION_STATUS_CACHE_LIMIT_TYPE_";
    @Resource
    private RiskObjectRepository riskObjectRepository;
    @Resource
    private RuleService ruleService;
    @Resource
    private WechatUserRepository wechatUserRepository;
    @Resource
    private LdbLockManager ldbLockManager;
    @Resource
    private LdbTairManager ldbTairManager;

    public RiskStatusEnum get(String corpId, String userId, RiskActionEnum riskAction) {
        String actionLdbKey = this.buildCacheKey(corpId, userId, riskAction);
        String actionLimitLdbKey = this.buildLimitTypeKey(corpId, userId, riskAction);
        Integer actionRiskStatusCode = (Integer) ldbTairManager.get(actionLdbKey);
        RiskStatusEnum actionRiskStatus;
        if (actionRiskStatusCode == null) {
            RiskObjectDO actionRiskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()), RiskTargetEnum.ACTION.getValue());
            actionRiskStatus = actionRiskObject != null ? RiskStatusEnum.of(actionRiskObject.getRiskStatus()) : RiskStatusEnum.NORMAL;
            ldbTairManager.put(actionLdbKey, actionRiskStatus.getCode());
            if (actionRiskStatus == RiskStatusEnum.PROTECT) {
                ldbTairManager.put(actionLimitLdbKey, actionRiskObject.getLimitType());
            }
        } else {
            actionRiskStatus = RiskStatusEnum.of(actionRiskStatusCode);
        }
        return actionRiskStatus;
    }

    /**
     * 转换至可用
     */
    public void toNormal(String corpId, String userId, RiskActionEnum riskAction, RiskStatusEnum fromStatus) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId, riskAction));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId, riskAction), 1, true, () -> {
                if (fromStatus != null) {
                    RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()), RiskTargetEnum.ACTION.getValue());
                    if (riskObject != null && riskObject.getRiskStatus().equals(fromStatus.getCode())) {
                        riskObjectRepository.deleteByTargetId(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()), RiskTargetEnum.ACTION.getValue());
                    }
                } else {
                    riskObjectRepository.deleteByTargetId(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()), RiskTargetEnum.ACTION.getValue());
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("行动项状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
        }
    }

    /**
     * 转换至异常
     */
    public void toAbnormal(String corpId, String userId, RiskActionEnum riskAction) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId, riskAction));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId, riskAction), 1, true, () -> {
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()), RiskTargetEnum.ACTION.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()));
                    riskObject.setTargetType(RiskTargetEnum.ACTION.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.ABNORMAL.getCode());
                    riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.ABNORMAL.getCode());
                    riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObjectRepository.update(riskObject);
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("行动项状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
        }
    }

    /**
     * 转换至保护
     */
    public void toProtect(String corpId, String userId, RiskActionEnum riskAction, RateLimitTypeEnum limitType) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId, riskAction));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId, riskAction), 1, true, () -> {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()), RiskTargetEnum.ACTION.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()));
                    riskObject.setTargetType(RiskTargetEnum.ACTION.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.PROTECT.getCode());
                    riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObject.setLimitTimeIndex(sdf.format(new Date()));
                    riskObject.setLimitType(limitType.getValue());
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.PROTECT.getCode());
                    riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObject.setLimitTimeIndex(sdf.format(new Date()));
                    riskObject.setLimitType(limitType.getValue());
                    riskObjectRepository.update(riskObject);
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("行动项状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
        }
    }

    /**
     * 转换至挂起
     */
    public void toHold(String corpId, String userId, RiskActionEnum riskAction) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId, riskAction));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId, riskAction), 1, true, () -> {
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()), RiskTargetEnum.ACTION.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()));
                    riskObject.setTargetType(RiskTargetEnum.ACTION.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.HOLD.getCode());
                    riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.HOLD.getCode());
                    riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObjectRepository.update(riskObject);
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("行动项状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
        }
    }

    /**
     * 转换至脱离
     */
    public void toEscape(String corpId, String userId, RiskActionEnum riskAction) {
        try {
            ldbTairManager.delete(this.buildCacheKey(corpId, userId, riskAction));
            ldbLockManager.lockWithRunnable(buildLockKey(corpId, userId, riskAction), 1, true, () -> {
                RiskObjectDO riskObject = riskObjectRepository.getByTargetIdWithNull(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()), RiskTargetEnum.ACTION.getValue());
                if (riskObject == null) {
                    riskObject = new RiskObjectDO();
                    riskObject.setTargetId(RiskObjectDO.buildActionTargetId(corpId, userId, riskAction.getActionCode()));
                    riskObject.setTargetType(RiskTargetEnum.ACTION.getValue());
                    riskObject.setRiskStatus(RiskStatusEnum.ESCAPE.getCode());
                    riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObjectRepository.create(riskObject);
                } else {
                    riskObject.setRiskStatus(RiskStatusEnum.ESCAPE.getCode());
                    riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(corpId, userId));
                    riskObjectRepository.update(riskObject);
                }
            });
        } catch (Exception e) {
            PlatformLogUtil.logException("行动项状态变更异常", e.getMessage(), e, LogListUtil.newArrayList(corpId, userId, riskAction.getActionCode()));
        }
    }

    /**
     * 修改默认风控的行动项状态，需要集联改到所有采用默认风控的机器人
     *
     * @param robotType  robotType
     * @param riskAction riskAction
     */
    public void toNormalWithRobotType(Integer robotType, RiskActionEnum riskAction) {
        // 获取某个类型下的所有机器人
        List<WechatUserDO> robots = wechatUserRepository.listByRobotType(robotType);
        // 获取某个类型下的所有rule
        List<String> targetIds = robots.stream().map(x -> RiskObjectDO.buildRobotTargetId(x.getCorpId(), x.getUserId())).collect(Collectors.toList());
        List<RuleDO> rules = ruleService.listByTargetIds(RuleTypeEnum.ROBOT_RISK.getCode().byteValue(),
                RuleTargetEnum.WECHAT_USER.getCode().byteValue(), targetIds);
        Map<String, RuleDO> ruleMap = rules.stream().collect(Collectors.toMap(RuleDO::getTargetId, Function.identity()));
        List<String> deleteTargetIds = new ArrayList<>();
        for (WechatUserDO wechatUser : robots) {
            // 未配置机器人风控规则的机器人才修改行动项状态
            if (!ruleMap.containsKey(RiskObjectDO.buildRobotTargetId(wechatUser.getCorpId(), wechatUser.getUserId()))) {
                deleteTargetIds.add(RiskObjectDO.buildActionTargetId(wechatUser.getCorpId(), wechatUser.getUserId(), riskAction.getActionCode()));
                ldbTairManager.delete(this.buildCacheKey(wechatUser.getCorpId(), wechatUser.getUserId(), riskAction));
            }
        }
        riskObjectRepository.deleteByTargetIds(deleteTargetIds, RiskTargetEnum.ACTION.getValue());
    }

    /**
     * 修改默认风控的行动项状态，需要集联改到所有采用默认风控的机器人
     *
     * @param robotType  robotType
     * @param riskAction riskAction
     */
    public void toHoldWithRobotType(Integer robotType, RiskActionEnum riskAction) {
        // 获取某个类型下的所有机器人
        List<WechatUserDO> robots = wechatUserRepository.listByRobotType(robotType);
        // 获取某个类型下的所有rule
        List<String> targetIds = robots.stream().map(x -> RiskObjectDO.buildRobotTargetId(x.getCorpId(), x.getUserId())).collect(Collectors.toList());
        List<RuleDO> rules = ruleService.listByTargetIds(RuleTypeEnum.ROBOT_RISK.getCode().byteValue(),
                RuleTargetEnum.WECHAT_USER.getCode().byteValue(), targetIds);
        Map<String, RuleDO> ruleMap = rules.stream().collect(Collectors.toMap(RuleDO::getTargetId, Function.identity()));
        List<WechatUserDO> needChanges = new ArrayList<>();
        for (WechatUserDO wechatUser : robots) {
            // 未配置机器人风控规则的机器人才修改行动项状态
            if (!ruleMap.containsKey(RiskObjectDO.buildRobotTargetId(wechatUser.getCorpId(), wechatUser.getUserId()))) {
                needChanges.add(wechatUser);
                ldbTairManager.delete(this.buildCacheKey(wechatUser.getCorpId(), wechatUser.getUserId(), riskAction));
            }
        }
        // 批量获取行动项
        List<String> actionTargetIds = needChanges.stream().map(x -> RiskObjectDO.buildActionTargetId(x.getCorpId(), x.getUserId(), riskAction.getActionCode())).collect(Collectors.toList());
        List<RiskObjectDO> actionRiskObjects = riskObjectRepository.listByTargetIds(actionTargetIds, RiskTargetEnum.ACTION.getValue());
        Map<String, RiskObjectDO> actionRiskObjectMap = actionRiskObjects.stream().collect(Collectors.toMap(RiskObjectDO::getTargetId, Function.identity()));
        for (WechatUserDO needChange : needChanges) {
            RiskObjectDO riskObject = actionRiskObjectMap.get(RiskObjectDO.buildActionTargetId(needChange.getCorpId(), needChange.getUserId(), riskAction.getActionCode()));
            if (riskObject == null) {
                riskObject = new RiskObjectDO();
                riskObject.setTargetId(RiskObjectDO.buildActionTargetId(needChange.getCorpId(), needChange.getUserId(), riskAction.getActionCode()));
                riskObject.setTargetType(RiskTargetEnum.ACTION.getValue());
                riskObject.setRiskStatus(RiskStatusEnum.HOLD.getCode());
                riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(needChange.getCorpId(), needChange.getUserId()));
                riskObjectRepository.create(riskObject);
            } else {
                riskObject.setRiskStatus(RiskStatusEnum.HOLD.getCode());
                riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(needChange.getCorpId(), needChange.getUserId()));
                riskObjectRepository.update(riskObject);
            }
        }
    }

    /**
     * 修改默认风控的行动项状态，需要集联改到所有采用默认风控的机器人
     *
     * @param robotType  robotType
     * @param riskAction riskAction
     */
    public void toEscapeWithRobotType(Integer robotType, RiskActionEnum riskAction) {
        // 获取某个类型下的所有机器人
        List<WechatUserDO> robots = wechatUserRepository.listByRobotType(robotType);
        // 获取某个类型下的所有rule
        List<String> targetIds = robots.stream().map(x -> RiskObjectDO.buildRobotTargetId(x.getCorpId(), x.getUserId())).collect(Collectors.toList());
        List<RuleDO> rules = ruleService.listByTargetIds(RuleTypeEnum.ROBOT_RISK.getCode().byteValue(),
                RuleTargetEnum.WECHAT_USER.getCode().byteValue(), targetIds);
        Map<String, RuleDO> ruleMap = rules.stream().collect(Collectors.toMap(RuleDO::getTargetId, Function.identity()));
        List<WechatUserDO> needChanges = new ArrayList<>();
        for (WechatUserDO wechatUser : robots) {
            // 未配置机器人风控规则的机器人才修改行动项状态
            if (!ruleMap.containsKey(RiskObjectDO.buildRobotTargetId(wechatUser.getCorpId(), wechatUser.getUserId()))) {
                needChanges.add(wechatUser);
                ldbTairManager.delete(this.buildCacheKey(wechatUser.getCorpId(), wechatUser.getUserId(), riskAction));
            }
        }
        // 批量获取行动项
        List<String> actionTargetIds = needChanges.stream().map(x -> RiskObjectDO.buildActionTargetId(x.getCorpId(), x.getUserId(), riskAction.getActionCode())).collect(Collectors.toList());
        List<RiskObjectDO> actionRiskObjects = riskObjectRepository.listByTargetIds(actionTargetIds, RiskTargetEnum.ACTION.getValue());
        Map<String, RiskObjectDO> actionRiskObjectMap = actionRiskObjects.stream().collect(Collectors.toMap(RiskObjectDO::getTargetId, Function.identity()));
        for (WechatUserDO needChange : needChanges) {
            RiskObjectDO riskObject = actionRiskObjectMap.get(RiskObjectDO.buildActionTargetId(needChange.getCorpId(), needChange.getUserId(), riskAction.getActionCode()));
            if (riskObject == null) {
                riskObject = new RiskObjectDO();
                riskObject.setTargetId(RiskObjectDO.buildActionTargetId(needChange.getCorpId(), needChange.getUserId(), riskAction.getActionCode()));
                riskObject.setTargetType(RiskTargetEnum.ACTION.getValue());
                riskObject.setRiskStatus(RiskStatusEnum.ESCAPE.getCode());
                riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(needChange.getCorpId(), needChange.getUserId()));
                riskObjectRepository.create(riskObject);
            } else {
                riskObject.setRiskStatus(RiskStatusEnum.ESCAPE.getCode());
                riskObject.setRiskGroup(RiskObjectDO.buildRobotTargetId(needChange.getCorpId(), needChange.getUserId()));
                riskObjectRepository.update(riskObject);
            }
        }
    }

    private String buildLockKey(String corpId, String userId, RiskActionEnum riskAction) {
        return ACTION_STATUS_CHANGE_PREFIX + corpId + "|" + userId + "|" + riskAction.getActionCode();
    }

    private String buildCacheKey(String corpId, String userId, RiskActionEnum riskAction) {
        return ACTION_STATUS_CACHE_PREFIX + corpId + "|" + userId + "|" + riskAction.getActionCode() + "|" + RiskTargetEnum.ACTION.getValue();
    }

    public String buildLimitTypeKey(String corpId, String userId, RiskActionEnum riskAction) {
        return ACTION_STATUS_CACHE_LIMIT_TYPE_PREFIX + corpId + "|" + userId + "|" + riskAction.getActionCode() + "|" + RiskTargetEnum.ACTION.getValue();
    }
}
