package com.alibaba.tripscrm.service.service.task.ability.data.split;

import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

import static com.alibaba.tripscrm.TripSCRMErrorCode.DATA_SPLIT_PROCESSOR_NOT_FOUND;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:04:31
 */
@Component
public abstract class AbstractTaskDataSplitProcessor {
    private static final Map<TaskType, AbstractTaskDataSplitProcessor> TASK_DATA_PROCESSOR_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void init() {
        getTaskTypeList().forEach(taskType -> TASK_DATA_PROCESSOR_MAP.put(taskType, this));
    }

    /**
     * 执行的任务类型
     *
     * @return
     */
    protected abstract List<TaskType> getTaskTypeList();

    public static List<List<TaskDataVO.DataBodyVO>> splitData(TaskExecuteContext context) {
        TaskType taskType = TaskType.getByCode(context.getTaskInfoDOSnapshot().getType());
        if (!TASK_DATA_PROCESSOR_MAP.containsKey(taskType)) {
            PlatformLogUtil.logFail("【任务执行异常】获取数据拆分处理器失败", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】获取数据拆分处理器失败，任务ID:%s，实例ID:%s", context.getTaskId(), context.getInstanceId()));
            throw new TripscrmException(DATA_SPLIT_PROCESSOR_NOT_FOUND);
        }

        return TASK_DATA_PROCESSOR_MAP.get(taskType).handleSplitData(context);
    }

    /**
     * 对任务执行数据进行拆分
     *
     * @param context 任务执行上下文
     * @return
     */
    protected abstract List<List<TaskDataVO.DataBodyVO>> handleSplitData(TaskExecuteContext context);
}
