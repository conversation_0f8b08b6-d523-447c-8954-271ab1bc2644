package com.alibaba.tripscrm.service.service.activity;

import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * 活动任务信息服务
 */
public interface ActivityContextService {

    /**
     * 生成上下文IDid
     *
     * @return 任务id
     */
    Long generateContextId();

    /**
     * 插入或更新：不存在插入，存在更新
     *
     * @param bo 数据
     * @return 影响行数
     */
    int upsert(ActivityTaskInfoBO bo);

    /**
     * 批量插入或更新：不存在插入，存在更新
     *
     * @param bos 数据
     * @return 影响行数
     */
    int batchUpsert(List<ActivityTaskInfoBO> bos);

    /**
     * 通过任务id删除
     *
     * @param contextId 任务id
     * @return 影响行数
     */
    int deleteByTaskId(Long contextId);

    /**
     * 批量通过任务id删除
     *
     * @param contextIds 任务id列表
     * @return 影响行数
     */
    int batchDeleteByTaskId(List<Long> contextIds);

    /**
     * 通过任务id查询
     *
     * @param contextId 任务id
     * @return bo
     */
    ActivityTaskInfoBO queryByTaskId(Long contextId);

    /**
     * 批量通过任务id查询
     *
     * @param contextIds 任务id列表
     * @return bo列表
     */
    List<ActivityTaskInfoBO> batchQueryByTaskId(List<Long> contextIds);

    /**
     * 根据活动以及目标查询
     *
     * @param query 查询条件，必须包含activityId、targetType、targetId
     * @return bo
     */
    List<ActivityTaskInfoBO> queryByActivityAndTarget(ActivityTaskInfoBO query);

    /**
     * 根据目标查询
     *
     * @param query 查询条件，必须包含targetType、targetId
     * @return bo
     */
    List<ActivityTaskInfoBO> queryByTarget(ActivityTaskInfoBO query);

    /**
     * 批量根据活动以及目标查询
     *
     * @param queries 查询条件列表，必须包含activityId、targetType、targetId
     * @return bo列表
     */
    List<ActivityTaskInfoBO> batchQueryByActivityAndTarget(List<ActivityTaskInfoBO> queries);

    /**
     * 取最新的bo数据
     *
     * @param activityTaskInfoBOS
     * @return
     */
    static ActivityTaskInfoBO getNewestBo(List<ActivityTaskInfoBO> activityTaskInfoBOS) {
        if (CollectionUtils.isEmpty(activityTaskInfoBOS)) {
            return null;
        }
        ActivityTaskInfoBO activityTaskInfoBO = activityTaskInfoBOS.get(0);
        // 取最新的那个数据
        for (ActivityTaskInfoBO infoBO : activityTaskInfoBOS) {
            boolean currentDateValid = activityTaskInfoBO.getExtraJson().containsKey("dataTime");
            boolean newDateValid = infoBO.getExtraJson().containsKey("dataTime");
            // 兼容老的不包含数据时间的数据
            if (!currentDateValid || (newDateValid && activityTaskInfoBO.getExtraJson().getLong("dataTime") < infoBO.getExtraJson().getLong("dataTime"))) {
                activityTaskInfoBO = infoBO;
            }
        }
        return activityTaskInfoBO;
    }
}
