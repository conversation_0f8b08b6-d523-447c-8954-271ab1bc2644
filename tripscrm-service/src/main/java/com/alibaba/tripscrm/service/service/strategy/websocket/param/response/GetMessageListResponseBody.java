package com.alibaba.tripscrm.service.service.strategy.websocket.param.response;

import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatSenderTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 消息体 推送体
 *
 * <AUTHOR>
 * @date 2023/10/11
 */
@Data
public class GetMessageListResponseBody {
    /**
     * 发送人id
     */
    private String senderId;
    /**
     * 发送人名称
     */
    private String senderName;
    /**
     * 发送人头像
     */
    private String senderAvatar;
    /**
     * 消息id
     */
    private String messageId;
    /**
     * 时间戳
     */
    private Long timestamp;
    /**
     * 消息类型
     * @see FusionChatMessageTypeEnum
     */
    private String msgType;
    /**
     * 消息内容
     */
    private String msgContent;
    /**
     * 状态 0 正常 1撤回
     */
    private Integer status;
    /**
     * 所属企业id
     */
    private String corpId;
    /**
     * 异步请求Id
     */
    private String requestId;
    /**
     * 发送人类型
     * @see FusionChatSenderTypeEnum
     */
    private Integer senderType;

    /**
     * 是否at所有人
     */
    private Boolean atAll;

    /**
     * 被at的成员id列表
     */
    private List<String> atUserIdList;

    /**
     * 被at的客户id列表
     */
    private List<String> atExternalUserIdList;
}
