package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.log.BatchOperateLogInfoService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/6/6 14:23
 **/
@Component
@AllArgsConstructor
public class GroupTransferOwnerCallbackProcessor implements ProxyCallbackProcessor {

    private final LdbTairManager ldbTairManager;
    private final BatchOperateLogInfoService batchOperateLogInfoService;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.GROUP_TRANSFER_OWNER_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (scrmCallbackMsg == null || StringUtils.isBlank(scrmCallbackMsg.getRequestId())) {
            PlatformLogUtil.logFail("转让群主回调内容为空或者请求id为空");
            return false;
        }
        boolean dealResult = true;
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("转让群主回调显示失败", LogListUtil.newArrayList(scrmCallbackMsg));
            dealResult = false;
        }
        // 批量转让群主操作结果处理
        dealBatchOperate(scrmCallbackMsg.getRequestId(), dealResult);
        return true;
    }

    /**
     * 批量转让群主操作结果处理
     * @param requestId 请求id
     * @param dealResult 处理结果
     */
    private void dealBatchOperate(String requestId, boolean dealResult) {
        Object logId = ldbTairManager.get(TairConstant.BATCH_GROUP_OPERATE_MAPPING + requestId);
        if (Objects.isNull(logId)) {
            return;
        }
        batchOperateLogInfoService.updateLogInfoByRequestId((Long) logId, requestId, dealResult);
    }

}
