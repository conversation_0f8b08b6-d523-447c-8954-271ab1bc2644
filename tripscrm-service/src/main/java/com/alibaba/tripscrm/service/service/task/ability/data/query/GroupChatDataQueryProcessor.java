package com.alibaba.tripscrm.service.service.task.ability.data.query;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.ScrmConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagCoverGroupDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.service.OdpsService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.TripSCRMErrorCode.QUERY_ODPS_FAIL;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:04:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupChatDataQueryProcessor extends AbstractTaskDataProcessor {
    private final TaskService taskService;
    private final ResourceRelationService resourceRelationService;
    private final OdpsService odpsService;
    private final IsvRouteService isvRouteService;


    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.GROUP_CHAT_MESSAGE);
    }

    /**
     * 从离线中从获取发送数据
     *
     * @param context 任务执行上下文
     * @return TaskDataVO
     */
    @Override
    protected TaskDataVO handleReadAllData(TaskExecuteContext context) {
        TaskDataVO dataVO = new TaskDataVO();
        dataVO.setData(new ArrayList<>());
        Long taskId = context.getTaskId();
        Long taskInstanceId = context.getInstanceId();

        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());

        Set<String> chatIdSet = new HashSet<>();

        try {
            // 手工选择的群聊
            Set<String> manuallySpecifiedChatIds = getManuallySpecifiedChatIds(context.getTaskInfoDOSnapshot());
            PlatformLogUtil.logFail("手工选择的群聊", LogListUtil.newArrayList(taskInfoDO.getId(), manuallySpecifiedChatIds));
            chatIdSet.addAll(Optional.ofNullable(manuallySpecifiedChatIds).orElse(Sets.newHashSet()));
            // 活动覆盖的群聊
            List<Long> wechatJoinGroupIdActivityList =
                    extInfo.containsKey(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST)
                            ? extInfo.getObject(TaskConstant.WECHAT_JOIN_GROUP_ACTIVITY_ID_LIST, new TypeReference<List<Long>>() {
                    }) : new ArrayList<>();
            Set<String> joinGroupActivityCoverChatIds = getJoinGroupActivityCoverChatIds(wechatJoinGroupIdActivityList);
            PlatformLogUtil.logFail("活动覆盖的群聊", LogListUtil.newArrayList(taskInfoDO.getId(), joinGroupActivityCoverChatIds));
            chatIdSet.addAll(joinGroupActivityCoverChatIds);
            // 标签覆盖的群聊
            List<String> tagIdList = extInfo.containsKey(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST)
                    ? extInfo.getObject(TaskConstant.SCRM_INCLUDE_TAG_ID_LIST, new TypeReference<List<String>>() {
            }) : Lists.newArrayList();
            Set<String> tagCoverChatIds = getTagCoverChatIds(taskService.getCorpIdByTaskId(taskId), tagIdList.stream().map(Long::valueOf).collect(Collectors.toSet()));
            PlatformLogUtil.logFail("标签覆盖的群聊", LogListUtil.newArrayList(taskInfoDO.getId(), tagCoverChatIds));
            chatIdSet.addAll(tagCoverChatIds);
            // 数据组装
            dataVO.setData(buildDataBodyVO(chatIdSet, taskId, taskInstanceId));
        } catch (Exception e) {
            PlatformLogUtil.logException("【任务执行异常】查询任务离线数据异常", e.getMessage(), e, LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            DingTalkApi.sendTaskMessage(String.format("【任务执行异常】查询任务离线数据异常，任务ID:%s，实例ID:%s", taskId, taskInstanceId));
            throw new TripscrmException(QUERY_ODPS_FAIL);
        }
        dataVO.setTotalCount((long) dataVO.getData().size());
        return dataVO;
    }

    /**
     * 构建框架中传递的对象
     *
     * @param chatIdSet  群聊id列表
     * @param taskId     任务ID
     * @param instanceId 任务实例ID
     * @return 数据列表
     */
    private List<TaskDataVO.DataBodyVO> buildDataBodyVO(Set<String> chatIdSet, Long taskId, Long instanceId) {
        ArrayList<TaskDataVO.DataBodyVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(chatIdSet)) {
            return result;
        }
        // 有效的群聊列表
        for (String chatId : chatIdSet) {
            TaskDataVO.DataBodyVO dataBodyVO = new TaskDataVO.DataBodyVO();
            dataBodyVO.setTargetType(ActivityTargetTypeEnum.WX_CHAT_ID.getCode());
            dataBodyVO.setTargetId(chatId);
            JSONObject extInfoJson = new JSONObject();
            IsvRouteContext isvRouteContext = new IsvRouteContext();
            isvRouteContext.setChatId(chatId);
            isvRouteContext.setRiskActionEnum(RiskActionEnum.GROUP_SEND_MESSAGE);
            isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
            isvRouteContext.addExtraInfo(ScrmConstant.NEED_OWNER_OR_ADMIN_PERMISSION, true);
            TripSCRMResult<WechatUserDTO> isvRouteResult = isvRouteService.matchWechatUser(isvRouteContext);
            if (!isvRouteResult.isSuccess() || Objects.isNull(isvRouteResult.getData()) || !StringUtils.hasLength(isvRouteResult.getData().getUserId())) {
                PlatformLogUtil.logFail("群内在线且保存该群到通信录的成员不存在", LogListUtil.newArrayList(chatId, isvRouteContext, isvRouteResult));
                continue;
            }
            extInfoJson.put("sendUserId", isvRouteResult.getData().getUserId());
            dataBodyVO.setExtInfo(extInfoJson.toJSONString());
            result.add(dataBodyVO);
        }
        return result;
    }

    /**
     * 从任务记录中获取手工指定的群聊列表
     *
     * @param taskInfoDO 任务对象
     * @return 群聊id列表
     */
    private Set<String> getManuallySpecifiedChatIds(TaskInfoDO taskInfoDO) {
        HashSet<String> result = new HashSet<>();
        if (taskInfoDO == null || !StringUtils.hasText(taskInfoDO.getExtInfo())) {
            return result;
        }
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        if (extInfo == null) {
            return result;
        }
        if (!extInfo.containsKey(TaskConstant.WECHAT_CHAT_ID_LIST)) {
            return result;
        }
        return extInfo.getObject(TaskConstant.WECHAT_CHAT_ID_LIST, new TypeReference<Set<String>>() {
        });
    }

    /**
     * 根据群活码活动id获取覆盖的群聊列表
     *
     * @param activityIdList 群活码获取活动id
     * @return 群聊id列表
     */
    private Set<String> getJoinGroupActivityCoverChatIds(List<Long> activityIdList) {
        Set<String> result = new HashSet<>();
        if (CollectionUtils.isEmpty(activityIdList)) {
            return result;
        }
        TaskQuery query = new TaskQuery();
        query.setActivityIdList(activityIdList);
        query.setType(TaskType.MANAGER_WECHAT_JOIN_GROUP.getCode());
        List<TaskInfoDO> taskList = taskService.query(query);
        if (CollectionUtils.isEmpty(taskList)) {
            return result;
        }
        List<Long> taskIdList = taskList.stream().map(TaskInfoDO::getId).collect(Collectors.toList());
        for (Long taskId : taskIdList) {
            result.addAll(getTaskCoverAllChatId(taskId));
        }
        return result;
    }

    /**
     * 获取群活码任务群聊池中的全部群聊
     *
     * @param taskId 群活码管理任务id
     * @return 群聊id列表
     */
    private Set<String> getTaskCoverAllChatId(Long taskId) {
        Set<String> result = new HashSet<>();
        if (!NumberUtils.validLong(taskId)) {
            return result;
        }
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
        query.setSourceType(ResourceTypeEnum.TASK.getCode().byteValue());
        query.setSourceId(String.valueOf(taskId));
        query.setTargetType(ResourceTypeEnum.CHAT_ID.getCode().byteValue());
        List<ResourceRelationDO> resourceRelationList = resourceRelationService.query(query);
        if (CollectionUtils.isEmpty(resourceRelationList)) {
            return result;
        }
        return resourceRelationList.stream().map(ResourceRelationDO::getTargetId).collect(Collectors.toSet());
    }

    /**
     * 获取标签覆盖的群聊id列表
     *
     * @param tagIdList 标签id列表
     * @return 群聊id列表
     */
    private Set<String> getTagCoverChatIds(String corpId, Set<Long> tagIdList) {
        HashSet<String> result = new HashSet<>();
        if (CollectionUtils.isEmpty(tagIdList)) {
            return result;
        }
        List<TagCoverGroupDTO> tagCoverGroupList = odpsService.queryTagCoverGroupInfo(corpId, tagIdList);
        if (CollectionUtils.isEmpty(tagCoverGroupList)) {
            return result;
        }
        return tagCoverGroupList.stream().map(TagCoverGroupDTO::getChatId).collect(Collectors.toSet());
    }

}
