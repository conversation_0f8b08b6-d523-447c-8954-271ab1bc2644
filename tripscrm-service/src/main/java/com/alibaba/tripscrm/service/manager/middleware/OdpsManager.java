package com.alibaba.tripscrm.service.manager.middleware;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.enums.system.OdpsProjectEnum;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.odps.tunnel.InstanceTunnel;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.common.keycenter.security.Cryptograph;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * odps离线表相关服务
 *
 * <AUTHOR>
 * @date 2023/5/31
 */
@Slf4j
@Component
public class OdpsManager {

    /**
     * 默认初始化的的alitrip_wireless空间的相关参数
     * tripscrm_odps_business 账号aksk配置
     */
    @Value("${odps.business.odpsAccessId}")
    private String odpsAccessId;
    @Value("${odps.business.odpsAccessKey}")
    private String odpsAccessKey;
    @Value("${odps.business.odpsAccessEndpoint}")
    private String odpsAccessEndpoint;

    @Autowired
    private Cryptograph cryptograph;

    public static final String keyCenterKey = "tripscrm_aone_key";


    private Account account;
    private Odps odps;

    @PostConstruct
    public void init() {
        Account account = new AliyunAccount(cryptograph.decrypt(odpsAccessId, keyCenterKey), cryptograph.decrypt(odpsAccessKey, keyCenterKey));
        Odps odps = new Odps(account);
        odps.setEndpoint(odpsAccessEndpoint);
        odps.setDefaultProject(OdpsProjectEnum.ALITRIP_WIRELESS.getProjectName());
        this.account = account;
        this.odps = odps;
    }

    public com.aliyun.odps.tunnel.InstanceTunnel.DownloadSession getDefaultDownloadSession(String odpsQuerySql) {
        InstanceTunnel.DownloadSession session = null;
        try {
            Instance odpsInstance = SQLTask.run(odps, odpsQuerySql);
            odpsInstance.waitForSuccess();
            //创建InstanceTunnel。
            InstanceTunnel tunnel = new InstanceTunnel(odps);
            session = tunnel.createDownloadSession(odps.getDefaultProject(), odpsInstance.getId());
            if (session == null) {
                //如果获取失败，等1秒后再重试一次
                Thread.sleep(1000L);
                session = tunnel.createDownloadSession(odps.getDefaultProject(), odpsInstance.getId());
            }
            return session;
        } catch (Exception e) {
            PlatformLogUtil.logException("ODPS获取默认下载会话异常", e.getMessage(), e, LogListUtil.newArrayList(odpsQuerySql));
        }
        return null;
    }

    /**
     * 根据SQL查询数量
     *
     * @param odpsQuerySql 查询SQL
     * @return 数量
     */
    @AteyeInvoker(description = "获取统计数量", paraDesc = "SQL")
    public Long getCount(String odpsQuerySql) {
        try {
            Instance instance = SQLTask.run(odps, odpsQuerySql);
            instance.waitForSuccess();
            List<Record> result = SQLTask.getResult(instance);
            if (CollectionUtils.isEmpty(result)) {
                return 0L;
            }
            return Long.valueOf((String) result.get(0).get(0));
        } catch (Exception e) {
            PlatformLogUtil.logException("Odps获取统计数量异常", e.getMessage(), e, LogListUtil.newArrayList(odpsQuerySql));
            return 0L;
        }
    }

    /**
     * 获取Odps Session
     *
     * @param sql
     * @return
     */
    public Instance getInstance(String sql) {
        Instance instance = null;
        try {
            //表信息
            Odps readOdps = new Odps(account);
            readOdps.setEndpoint(odpsAccessEndpoint);
            readOdps.setDefaultProject(odps.getDefaultProject());

            //ODPS查询实例
            instance = SQLTask.run(readOdps, sql);
            instance.waitForSuccess();
            return instance;
        } catch (Exception e) {
            PlatformLogUtil.logException("获取Odps Session异常", e.getMessage(), e, LogListUtil.newArrayList());
        }
        return null;
    }

    @AteyeInvoker(description = "解密", paraDesc = "encryptedText&keyName")
    public String decrypt(String encryptedText, String keyName) {
        return cryptograph.decrypt(encryptedText, keyName);
    }

}
