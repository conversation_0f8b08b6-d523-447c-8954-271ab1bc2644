package com.alibaba.tripscrm.service.manager;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripzoo.admin.client.domain.model.ActivityDTO;
import com.alibaba.tripzoo.admin.client.request.ActivityQueryRequest;
import com.alibaba.tripzoo.admin.client.response.ActivityQueryResponse;
import com.alibaba.tripzoo.admin.client.result.TripzooAdminResult;
import com.alibaba.tripzoo.admin.client.service.ActivityService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class OperationActivityManager {
    private final ActivityService activityService;

    public ActivityDTO queryById(Long id) {
        if (!NumberUtils.biggerThanZero(id)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ActivityQueryRequest request = new ActivityQueryRequest();
        request.setId(id);
        List<ActivityDTO> list = query(request);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.get(0);
    }

    public List<ActivityDTO> query(ActivityQueryRequest request) {
        if (Objects.isNull(request)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        TripzooAdminResult<ActivityQueryResponse> result = activityService.query(request);
        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData())) {
            PlatformLogUtil.logFail("查询运营活动出错", LogListUtil.newArrayList(request, result));
            return null;
        }

        return result.getData().getActivityList();
    }
}
