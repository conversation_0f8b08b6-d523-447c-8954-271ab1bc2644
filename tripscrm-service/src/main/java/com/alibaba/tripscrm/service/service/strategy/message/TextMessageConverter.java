package com.alibaba.tripscrm.service.service.strategy.message;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.constant.MaterialJsonKeyConstant;
import com.alibaba.tripscrm.service.enums.material.MaterialBuildInValueEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.manager.task.MaterialTrackIdRelationManager;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.model.domain.response.MaterialContentConvertResult;
import com.alibaba.tripscrm.service.model.dto.message.MessageTransferDTO;
import com.alibaba.tripscrm.service.model.dto.message.TextMessageInfoDTO;
import com.alibaba.tripscrm.service.service.material.MaterialContentConvertService;
import com.alibaba.tripscrm.service.service.second.ShortLinkService;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripzoo.proxy.api.service.WxMiniProgramService;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.*;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import com.taobao.usa.util.ObjectUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-02-04 23:32:31
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TextMessageConverter extends AbstractMessageConverter<TextMessageInfoDTO> {
    private final MaterialTrackIdRelationManager materialTrackIdRelationManager;
    private final WxMiniProgramService wxMiniProgramService;
    private final ShortLinkService shortLinkService;
    private final MaterialContentConvertService materialContentConvertService;

    @Override
    public TextMessageInfoDTO convert(MessageTransferDTO messageTransferDTO) {
        TextMessageInfoDTO textMessageInfoDTO = new TextMessageInfoDTO();
        TaskType taskType = messageTransferDTO.getTaskType();
        JSONObject messageContent = messageTransferDTO.getContent();
        String messageParagraph = messageContent.containsKey(MaterialJsonKeyConstant.messageParagraph)
                ? messageContent.getString(MaterialJsonKeyConstant.messageParagraph)
                : "";
        if (ObjectUtils.equals(TaskType.PERSONAL_WELCOME, taskType) && Objects.equals(0, messageTransferDTO.getMsgIndex())) {
            messageParagraph = "Hi " + MaterialBuildInValueEnum.WECHAT_CUSTOMER_NAME.getKey() + "，" + messageParagraph;
        }
        if (ObjectUtils.equals(TaskType.GROUP_WELCOME, taskType) && Objects.equals(0, messageTransferDTO.getMsgIndex())) {
            messageParagraph = MaterialBuildInValueEnum.WECHAT_GROUP_AT_CUSTOMER_NAME.getKey() + "，" + messageParagraph + MaterialBuildInValueEnum.WECHAT_GROUP_NAME.getKey() + "\uD83D\uDC4F\uD83D\uDC4F\uD83D\uDC4F";
        }

        if (!messageContent.containsKey(MaterialJsonKeyConstant.messageParagraphList)) {
            textMessageInfoDTO.setContent(messageParagraph);
            return textMessageInfoDTO;
        }

        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(messageParagraph)) {
            sb.append(messageParagraph).append("\n");
        }

        int paragraphIndex = StringUtils.isBlank(messageParagraph) ? 0 : 1;
        //处理段落列表
        JSONArray messageParagraphList = messageContent.getJSONArray(MaterialJsonKeyConstant.messageParagraphList);
        for (int i = 0; i < messageParagraphList.size(); i++) {
            JSONObject message = messageParagraphList.getJSONObject(i);
            String pageType = message.getString(MaterialJsonKeyConstant.pageType);
            String content = message.getString(MaterialJsonKeyConstant.textScript);
            boolean isH5 = Objects.equals(MaterialSupplyTypeEnum.H5.getType(), pageType);
            String originUrl = getOriginUrl(message);
            String title = message.getString(MaterialJsonKeyConstant.shortChainIntroduction);
            String customize = message.getString(MaterialJsonKeyConstant.customize);

            // 段落开头
            if (StringUtils.isNotBlank(content)) {
                sb.append(content).append("\n");
            }

            if (StringUtils.isNotBlank(originUrl)) {
                messageTransferDTO.getMaterialTrackRelationDTO().setMsgParagraphId(paragraphIndex);
                BaseResult<String> result = materialTrackIdRelationManager.insertMaterialRelationAndGetTrackId(messageTransferDTO.getMaterialTrackRelationDTO());
                PlatformLogUtil.logInfo("文本消息转换器，插入发送素材的信息获取匹配的跟踪id", LogListUtil.newArrayList(messageTransferDTO.getMaterialTrackRelationDTO(), result));
                if (result == null || !result.isSuccess()) {
                    throw new RuntimeException("生成trackId失败");
                }

                // 生成 trackId
                String trackId = result.getData();
                String shortLink = isH5 ?
                        buildHttpShortLink(originUrl, trackId)
                        : buildMiniProgramShortLink(originUrl, title, trackId, messageTransferDTO.getTaskType(), customize);
                sb.append(shortLink).append("\n");
            }
            paragraphIndex++;
            // 段落结尾换行
            if (i < messageParagraphList.size() - 1) {
                sb.append("\n");
            }
        }

        textMessageInfoDTO.setContent(sb.toString());
        return textMessageInfoDTO;
    }

    @Override
    public List<MessageBO> buildMessageBO(TextMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, Integer messageIndex) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgNum(messageIndex);
        messageBO.setMsgType(messageInfoDTO.getMessageType());
        context.setOriginContent(messageInfoDTO.getContent());
        MaterialContentConvertResult convertResult = materialContentConvertService.convert(context);
        messageBO.setMsgContent(convertResult.getContent());
        if (convertResult.getAtAll()) {
            // 在开始处，艾特所有人
            messageBO.setAtLocation(0);
            messageBO.setAt(1);
        }
        return Lists.newArrayList(messageBO);
    }

    /**
     * 构建微信文本消息
     *
     * @param messageInfoDTO 素材消息对象
     * @param context        素材上下文
     * @return 微信消息BO
     */
    @Override
    List<WxMessageBO> buildWxMessageBO(TextMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, TaskType taskType) {
        context.setOriginContent(messageInfoDTO.getContent());
        String content = materialContentConvertService.convert(context).getContent();
        WxMessageBO wxMessageBO = new WxMessageBO();
        wxMessageBO.setMsgType(WxAttachmentTypeEnum.TEXT);
        wxMessageBO.setMsgContent(content);
        return Lists.newArrayList(wxMessageBO);
    }

    @Override
    public MessageTypeEnum getMsgType() {
        return MessageTypeEnum.TEXT;
    }

    private String getOriginUrl(JSONObject message) {
        if (MaterialSupplyTypeEnum.H5.getType().equals(message.getString(MaterialJsonKeyConstant.pageType))) {
            return message.getString(MaterialJsonKeyConstant.pageUrl);
        }

        if (MaterialSupplyTypeEnum.PAGE_MAKER.getType().equals(message.getString(MaterialJsonKeyConstant.supplyType))) {
            JSONObject pageMaker = message.getJSONObject(MaterialJsonKeyConstant.pageMakerData);
            return pageMaker.getString(MaterialJsonKeyConstant.url);
        }

        if (MaterialSupplyTypeEnum.SHOP.getType().equals(message.getString(MaterialJsonKeyConstant.supplyType))) {
            JSONObject shop = message.getJSONObject(MaterialJsonKeyConstant.hotelData);
            String id = shop.getString(MaterialJsonKeyConstant.id);
            return MaterialConstant.HOTEL_H5_PRE + id;
        }

        if (MaterialSupplyTypeEnum.COMMODITY.getType().equals(message.getString(MaterialJsonKeyConstant.supplyType))) {
            JSONObject commodity = message.getJSONObject(MaterialJsonKeyConstant.commodityData);
            String id = commodity.getString(MaterialJsonKeyConstant.id);
            return MaterialConstant.GOODS_MINI_PROGRAM_URL_PRE + id;
        }

        return message.getString(MaterialJsonKeyConstant.pagePath);
    }

    public String buildMiniProgramUrlSchemeShortLink(String pathUrl) {
        WxUrlSchemeRequest request = new WxUrlSchemeRequest();
        request.setAvailableDays(30);
        request.setNeverExpire(false);

        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(pathUrl);
        UriComponents uriComponents = uriComponentsBuilder.build(true);
        WxJumpWxaBO wxJumpWxaBO = new WxJumpWxaBO(uriComponents.getPath(), uriComponents.getQuery());
        request.setWxJumpWxaBO(wxJumpWxaBO);
        ResultDO<String> resultDO = wxMiniProgramService.getUrlScheme(request);
        if (resultDO == null || !resultDO.getSuccess() || resultDO.getModel() == null) {
            PlatformLogUtil.logFail("获取地址的scheme失败", LogListUtil.newArrayList(pathUrl, request, resultDO));
            return "";
        }
        String shortLink = shortLinkService.convertToShortUrl(resultDO.getModel());
        PlatformLogUtil.logInfo("获取地址的scheme完成", LogListUtil.newArrayList(pathUrl, request, resultDO, shortLink));
        return shortLink;
    }

    private String buildMiniProgramShortLink(String originUrl, String title, String trackId, TaskType taskType, String customize) {
        if (StringUtils.isBlank(originUrl)) {
            return "";
        }

        String miniProgramPath = MaterialUtils.convert2MiniProgramPath(originUrl, trackId, customize);
        PlatformLogUtil.logInfo("对小程序链接进行统一转换", originUrl, title, trackId, taskType, miniProgramPath);

        if (Lists.newArrayList(TaskType.MANAGER_WECHAT_JOIN_GROUP, TaskType.PUBLISH_GROUP_NOTICE).contains(taskType)) {
            return buildMiniProgramUrlSchemeShortLink(miniProgramPath);
        }

        GenerateWechatMiniProgramShortLinkRequest request = new GenerateWechatMiniProgramShortLinkRequest();
        request.setPageTitle(title);
        request.setPageUrl(miniProgramPath);
        ResultDO<String> resultDO = wxMiniProgramService.generateShortLink(request);
        if (resultDO == null || !resultDO.getSuccess()) {
            PlatformLogUtil.logFail("小程序短链转换失败", LogListUtil.newArrayList(originUrl, title, trackId, taskType, request, resultDO));
            return "";
        }

        String shortLink = resultDO.getModel();
        PlatformLogUtil.logInfo("小程序短链转换完成", LogListUtil.newArrayList(originUrl, title, trackId, taskType, request, resultDO));
        return shortLink;
    }

    private String buildHttpShortLink(String originUrl, String trackId) {
        if (StringUtils.isBlank(originUrl)) {
            return "";
        }

        String urlWithParams = MaterialUtils.convert2HttpRouteUrl(originUrl, trackId);
        String shortLink = shortLinkService.convertToShortUrl(urlWithParams);
        PlatformLogUtil.logFail("HTTP短链转换完成", LogListUtil.newArrayList(originUrl, urlWithParams, shortLink));
        return shortLink;
    }
}
