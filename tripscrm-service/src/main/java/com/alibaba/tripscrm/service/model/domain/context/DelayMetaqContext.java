package com.alibaba.tripscrm.service.model.domain.context;

import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import lombok.Data;
import java.io.Serializable;

/**
 * 延迟消息上下文
 *
 * <AUTHOR>
 * @date 2023/5/24
 */
@Data
public class DelayMetaqContext implements Serializable {

    private static final long serialVersionUID = 5801948832369613068L;
    /**
     * 触发时间
     */
    private Long triggerTime;
    /**
     * 触发时间类型
     */
    private Integer triggerType;

    /**
     * 是否需要秒级延迟
     * false时，精确到分钟级即可
     * true时，秒级延迟
     */
    private Boolean secondDelayLevel = false;

    /**
     * 重试次数
     */
    private Integer delayNum = 0;
    /**
     * 重试key
     */
    private String delayKey;
    /**
     * 执行方法类型
     */
    private DelayScheduleMethodEnum functionType;
    /**
     * 执行方法参数
     */
    private String param;
}
