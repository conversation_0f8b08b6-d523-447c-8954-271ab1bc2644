package com.alibaba.tripscrm.service.service.task.ability.old;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.FissionTaskContext;
import com.alibaba.tripscrm.domain.TaskContext;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.request.TripSCRMTaskBuildRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMTaskService;
import com.alibaba.tripscrm.service.enums.wechat.EnterpriseWeChatEventEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.EventMetaInfo;
import com.alibaba.tripscrm.service.model.domain.context.TaskBuildContext;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.task.ability.old.second.FliggyPlayService;
import com.alibaba.tripscrm.service.util.system.JsonUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * 裂变类型任务实现
 *
 * <AUTHOR>
 * @date 2023/4/17
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class FissionTaskServiceImpl extends AbstractTaskTemplate {
    private final FliggyPlayService fliggyPlayService;
    private final UicUtils uicUtils;
    private final ActivityContextService activityContextService;
    private final TaskFactory taskFactory;
    private final TripSCRMTaskService tripSCRMTaskService;

    @Switch(description = "助力成功发送企微私聊消息的taskId", name = "sendHelpSuccessTaskId")
    public static Long sendHelpSuccessTaskId = 1051L;

    @PostConstruct
    public void init() {
        setTaskFactory();
    }

    @Override
    protected boolean taskExe(ActivityTaskInfoBO taskInfoBO, TaskBuildContext taskBuildContext) {
        // do nothing；单纯的保存上下文
        return true;
    }

    @Override
    public EventMetaInfo getCallBackInfo(Long activityId, EventMetaInfo eventMetaInfo, String unionId) {
        try {
            //判断事件类型是否是添加用户
            if (!EnterpriseWeChatEventEnum.ADD_EXTERNAL_CONTACT.getName().equals(eventMetaInfo.getChangeType())) {
                PlatformLogUtil.logFail("changeType is not add_external_contact", LogListUtil.newArrayList(eventMetaInfo, unionId));
                return eventMetaInfo;
            }
            // UIC查询淘宝ID
            String taoId = uicUtils.getUidByUnionId(unionId);
            if (StringUtils.isEmpty(taoId)) {
                PlatformLogUtil.logFail("getTaoId fail", LogListUtil.newArrayList(eventMetaInfo, unionId));
                return eventMetaInfo;
            }
            // 查询更新上下文,查询用户的word
            ActivityTaskInfoBO taskInfoBO = new ActivityTaskInfoBO();
            taskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.TAOBAO_USER_ID);
            taskInfoBO.setTargetId(taoId);
            taskInfoBO.setActivityId(activityId);
            List<ActivityTaskInfoBO> activityTaskInfoBOS = activityContextService.queryByActivityAndTarget(taskInfoBO);
            ActivityTaskInfoBO activityTaskInfoBO = ActivityContextService.getNewestBo(activityTaskInfoBOS);
            if (Objects.isNull(activityTaskInfoBO)) {
                PlatformLogUtil.logFail("activityTaskInfoBOS is null", LogListUtil.newArrayList(eventMetaInfo, taskInfoBO));
                return eventMetaInfo;
            }
            // 进行裂变助力
            TripSCRMResult<Boolean> tripSCRMResult = establishRelation(activityTaskInfoBO);
            // 将裂变结果补充到回调扩展数据中
            JSONObject fissionInfoJs = new JSONObject();
            fissionInfoJs.put("fissionSuccess", tripSCRMResult.isSuccess() ? SwitchConfig.fissionHelpWelcomeText.get("success_ret") : SwitchConfig.fissionHelpWelcomeText.get("fail_ret"));
            fissionInfoJs.put("fissionMsg", tripSCRMResult.getMsg());
            fissionInfoJs.put("sendUserId", eventMetaInfo.getUserID());
            eventMetaInfo.setExtraJson(JsonUtils.jsonMerge(fissionInfoJs, eventMetaInfo.getExtraJson()));
            TripSCRMTaskBuildRequest request = new TripSCRMTaskBuildRequest();
            request.setTargetType(ActivityTargetTypeEnum.WX_UNION_ID.getCode());
            request.setTargetId(unionId);
            request.setExtInfo(fissionInfoJs);
            request.setTaskId(sendHelpSuccessTaskId);
            // 助力结果私聊消息
            TripSCRMResult<String> result = tripSCRMTaskService.buildTask(request);
            if (!result.isSuccess()) {
                PlatformLogUtil.logFail("buildTask fail", LogListUtil.newArrayList(request, result));
            }
            return eventMetaInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(eventMetaInfo));
            return eventMetaInfo;
        }
    }

    /**
     * 完成助力
     *
     * @return
     */
    private TripSCRMResult<Boolean> establishRelation(ActivityTaskInfoBO taskInfoBO) {
        FissionTaskContext fissionTaskContext = (FissionTaskContext) getContext(taskInfoBO.getExtraJson());
        return fliggyPlayService.establishRelation(fissionTaskContext.getFliggyPlayId(), Long.parseLong(taskInfoBO.getTargetId()), fissionTaskContext.getFliggyPlayWord(), taskInfoBO.getContextId().toString());
    }

    @Override
    protected TaskContext getContext(JSONObject contextJson) {
        return JSON.parseObject(contextJson.toJSONString(), FissionTaskContext.class);
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.FISSION;
    }

    @Override
    protected void setTaskFactory() {
        taskFactory.setTaskTemplateMap(getTaskType(), this.getClass());
    }
}
