package com.alibaba.tripscrm.service.service.fai;

import com.alibaba.tripscrm.service.model.domain.request.fai.CreateDocumentReq;
import com.alibaba.tripscrm.service.model.domain.request.fai.RemoveDocumentReq;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;


/**
 * <AUTHOR>
 * @desc fai知识文档
 * @date 2025/9/17
 */
public interface FaiDocumentService {

    BaseResult<Boolean> createDocument(CreateDocumentReq request);

    BaseResult<Boolean> removeDocument(RemoveDocumentReq request);
}
