package com.alibaba.tripscrm.service.service.impl.hsf;

import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.EnterpriseWechatChatMessageTypeEnum;
import com.alibaba.tripscrm.domain.message.EnterpriseWechatChatMessageDTO;
import com.alibaba.tripscrm.domain.request.TripSCRMEnterpriseWechatGroupChatMessageQueryRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMEnterpriseWechatSingleChatMessageQueryRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatReceiveTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatSenderTypeEnum;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageQueryRequest;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetMessageListResponseBody;
import com.alibaba.tripscrm.service.wechat.EnterpriseWechatChatMessageService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/5 20:34
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@HSFProvider(serviceInterface = EnterpriseWechatChatMessageService.class)
public class EnterpriseWechatChatMessageServiceImpl implements EnterpriseWechatChatMessageService {
    private final ChatMessageService chatMessageService;

    @Override
    @ServiceLog("企业微信-查询私聊聊天记录")
    public TripSCRMResult<List<EnterpriseWechatChatMessageDTO>> querySingleChatMessage(TripSCRMEnterpriseWechatSingleChatMessageQueryRequest request) {
        if (!StringUtils.hasText(request.getUserId()) || Objects.isNull(request.getExternalUserId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<GetMessageListResponseBody> messageList = chatMessageService.listByChatId(request.getUserId(), request.getExternalUserId(), FusionChatReceiveTypeEnum.SINGLE.getValue());
        List<EnterpriseWechatChatMessageDTO> chatMessageList = new ArrayList<>();
        for (GetMessageListResponseBody body : messageList) {
            EnterpriseWechatChatMessageDTO enterpriseWechatChatMessageDTO = new EnterpriseWechatChatMessageDTO();
            enterpriseWechatChatMessageDTO.setCorpId(body.getCorpId());
            enterpriseWechatChatMessageDTO.setTimestamp(body.getTimestamp());
            enterpriseWechatChatMessageDTO.setMessageId(body.getMessageId());
            enterpriseWechatChatMessageDTO.setSenderId(body.getSenderId());
            enterpriseWechatChatMessageDTO.setRequestId(body.getRequestId());
            if (request.getExternalUserId().equals(body.getSenderId())) {
                enterpriseWechatChatMessageDTO.setSenderType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
                enterpriseWechatChatMessageDTO.setReceiverType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
                enterpriseWechatChatMessageDTO.setReceiverId(request.getUserId());
            } else {
                enterpriseWechatChatMessageDTO.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
                enterpriseWechatChatMessageDTO.setReceiverType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
                enterpriseWechatChatMessageDTO.setReceiverId(request.getExternalUserId());
            }
            enterpriseWechatChatMessageDTO.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(FusionChatMessageTypeEnum.parse(body.getMsgType()).name()));
            enterpriseWechatChatMessageDTO.setMsgContent(body.getMsgContent());
            chatMessageList.add(enterpriseWechatChatMessageDTO);
        }
        return TripSCRMResult.success(chatMessageList);
    }

    @Override
    @ServiceLog("企业微信-查询群聊聊天记录")
    public TripSCRMResult<List<EnterpriseWechatChatMessageDTO>> queryGroupChatMessage(TripSCRMEnterpriseWechatGroupChatMessageQueryRequest request) {
        if (!StringUtils.hasLength(request.getChatId()) || !StringUtils.hasLength(request.getCorpId())) {
            PlatformLogUtil.logFail("企业微信-查询群聊聊天记录失败，群聊id或corpId为空", Lists.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }
        Integer senderType = null;
        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(request.getSenderType());
        if (StringUtils.hasLength(request.getSenderId()) && Objects.nonNull(activityTargetType)) {
            switch(activityTargetType) {
                case WX_EXTERNAL_USERID:
                    senderType = FusionChatSenderTypeEnum.CUSTOMER.getValue();
                    break;
                case WX_USER_ID:
                    senderType = FusionChatSenderTypeEnum.USER.getValue();
                    break;
                default:
                    break;

            }
        }
        ChatMessageQueryRequest query = new ChatMessageQueryRequest();
        query.setChatId(request.getChatId());
        query.setSenderId(StringUtils.hasLength(request.getSenderId()) ? request.getSenderId() : null);
        query.setSenderType(senderType);
        query.setTimestamp(request.getTimestamp());
        query.setMessageCount(request.getMessageCount());
        query.setChatType(FusionChatReceiveTypeEnum.GROUP.getValue());
        query.setCorpId(request.getCorpId());
        List<GetMessageListResponseBody> messageList = chatMessageService.listByParam(query);
        List<EnterpriseWechatChatMessageDTO> chatMessageList = new ArrayList<>();
        for (GetMessageListResponseBody body : messageList) {
            EnterpriseWechatChatMessageDTO enterpriseWechatChatMessageDTO = new EnterpriseWechatChatMessageDTO();
            enterpriseWechatChatMessageDTO.setCorpId(body.getCorpId());
            enterpriseWechatChatMessageDTO.setTimestamp(body.getTimestamp());
            enterpriseWechatChatMessageDTO.setMessageId(body.getMessageId());
            enterpriseWechatChatMessageDTO.setSenderId(body.getSenderId());
            enterpriseWechatChatMessageDTO.setRequestId(body.getRequestId());
            if (body.getSenderType().equals(FusionChatSenderTypeEnum.USER.getValue())) {
                enterpriseWechatChatMessageDTO.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
            } else {
                enterpriseWechatChatMessageDTO.setSenderType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
            }
            enterpriseWechatChatMessageDTO.setReceiverId(request.getChatId());
            enterpriseWechatChatMessageDTO.setReceiverType(ActivityTargetTypeEnum.WX_CHAT_ID.getCode());
            enterpriseWechatChatMessageDTO.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(FusionChatMessageTypeEnum.parse(body.getMsgType()).name()));
            enterpriseWechatChatMessageDTO.setMsgContent(body.getMsgContent());
            chatMessageList.add(enterpriseWechatChatMessageDTO);
            if (Objects.nonNull(body.getAtAll()) && body.getAtAll()) {
                enterpriseWechatChatMessageDTO.setAtUserIdList("All");
                enterpriseWechatChatMessageDTO.setAtExternalUserIdList("All");
            } else {
                if (!CollectionUtils.isEmpty(body.getAtUserIdList())) {
                    enterpriseWechatChatMessageDTO.setAtUserIdList(String.join(",", body.getAtUserIdList()));
                }
                if (!CollectionUtils.isEmpty(body.getAtExternalUserIdList())) {
                    enterpriseWechatChatMessageDTO.setAtExternalUserIdList(String.join(",", body.getAtExternalUserIdList()));
                }
            }
        }
        return TripSCRMResult.success(chatMessageList);
    }
}
