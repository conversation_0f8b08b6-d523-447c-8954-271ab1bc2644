package com.alibaba.tripscrm.service.model.exception;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.system.ErrorCodeEnum;
import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
public class TripscrmException extends RuntimeException {
    private final String errorMsg;
    private String errorCode;

    public TripscrmException(String errorMsg, Throwable cause) {
        super(cause);
        this.errorMsg = errorMsg;
    }

    public TripscrmException(String errorMsg) {
        super(errorMsg);
        this.errorMsg = errorMsg;
    }

    public TripscrmException(String errorMsg, String errorCode) {
        this.errorMsg = errorMsg;
        this.errorCode = errorCode;
    }

    public TripscrmException(TripSCRMErrorCode bizErrorEnum) {
        super(bizErrorEnum.getDescCn());
        this.errorMsg = bizErrorEnum.getDescCn();
        this.errorCode = bizErrorEnum.getCode();
    }

    public TripscrmException(TripSCRMErrorCode bizErrorEnum, String errorMsg) {
        super(bizErrorEnum.getDescCn());
        this.errorMsg = errorMsg;
        this.errorCode = bizErrorEnum.getCode();
    }

    public TripscrmException(ErrorCodeEnum codeEnum) {
        super(codeEnum.getDesc());
        this.errorMsg = codeEnum.getDesc();
        this.errorCode = codeEnum.getCode();
    }

    public TripSCRMErrorCode getErrorCodeEnum() {
        if (!StringUtils.hasLength(errorCode)) {
            return TripSCRMErrorCode.UNKNOWN;
        }

        TripSCRMErrorCode errorCodeEnum = TripSCRMErrorCode.codeOf(errorCode);
        return Optional.ofNullable(errorCodeEnum).orElse(TripSCRMErrorCode.UNKNOWN);
    }
}
