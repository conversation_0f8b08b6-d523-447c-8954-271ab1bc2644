package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/3/8 20:42
 **/
@Data
public class CustomerCountQuery implements Serializable {

    /**
     * 成员Id列表
     */
    private Set<String> userIdList;

    /**
     * 去重字段
     */
    private String distinctKey;

    /**
     * 分组求count字段
     */
    private List<String> countFields;

    /**
     * 关系状态
     */
    private Integer relationStatus;

}
