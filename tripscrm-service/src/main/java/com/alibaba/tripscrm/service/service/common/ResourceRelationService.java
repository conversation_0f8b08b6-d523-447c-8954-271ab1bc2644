package com.alibaba.tripscrm.service.service.common;

import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.dto.ResourceRelationDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ResourceRelationService {

    /**
     * 新增资源关系
     * @param resourceRelationDO 资源关系对象
     * @return 新增结果
     */
    boolean add(ResourceRelationDO resourceRelationDO);

    /**
     * 批量新增
     * @param resourceRelationList 资源关系对象
     * @return 新增结果
     */
    boolean batchAdd(List<ResourceRelationDO> resourceRelationList);

    /**
     * 根据id删除资源关系
     * @param id 资源关系id
     * @return 删除结果
     */
    boolean deleteById(Long id);

    /**
     * 条件删除
     * @param query 删除条件
     * @return 删除结果
     */
    boolean deleteByParam(ResourceRelationQuery query);

    /**
     * 批量删除
     * @param ids 关系主键列表
     * @return 删除结果
     */
    boolean batchDelete(List<Long> ids);

    /**
     * 根据id查询资源关系
     * @param id 资源关系id
     * @return 资源关系
     */
    ResourceRelationDO queryById(Long id);

    /**
     * 查询资源关系
     * @param query 查询条件
     * @return 查询结果
     */
    List<ResourceRelationDO> query(ResourceRelationQuery query);

    /**
     * 条件查询数量
     * @param query 查询条件
     * @return 查询结果
     */
    Long count(ResourceRelationQuery query);

    /**
     * 更新管理组对应的关系
     * @param resourceRelationDTO 资源关系
     * @return 更新结果
     */
    boolean updateResourceRelation(ResourceRelationDTO resourceRelationDTO);

}
