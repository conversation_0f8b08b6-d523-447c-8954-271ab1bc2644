package com.alibaba.tripscrm.service.service.impl.moment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.mapper.tddl.MomentInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.MomentInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MomentInfoParam;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.query.MomentInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskInstanceQuery;
import com.alibaba.tripscrm.service.enums.wechat.WechatUserMatchStrategyEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.vo.wechat.MomentInfoVO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.moment.MomentInfoService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.MomentPushStatusEnum;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/1/25 15:45
 **/
@Service
@AllArgsConstructor
public class MomentInfoServiceImpl implements MomentInfoService {
    private final MomentInfoMapper momentInfoMapper;
    private final ActivityContextService activityContextService;
    private final TaskInstanceService taskInstanceService;

    @Override
    public boolean insert(MomentInfoDO momentInfoDO) {
        if (momentInfoDO == null) {
            return false;
        }
        return momentInfoMapper.insertSelective(momentInfoDO) > 0;
    }

    @Override
    public boolean updateByMomentIdAndUserId(MomentInfoDO momentInfoDO) {
        MomentInfoParam param = new MomentInfoParam();
        MomentInfoParam.Criteria criteria = param.createCriteria();
        criteria.andMomentIdEqualTo(momentInfoDO.getMomentId());
        criteria.andUserIdEqualTo(momentInfoDO.getUserId());
        List<MomentInfoDO> momentInfoDOS = momentInfoMapper.selectByParam(param);
        if (CollectionUtils.isEmpty(momentInfoDOS)) {
            return false;
        }
        return momentInfoMapper.updateByParamSelective(momentInfoDO, param) > 0;
    }

    @Override
    public boolean updateById(MomentInfoDO momentInfoDO) {
        if (momentInfoDO == null || momentInfoDO.getId() == null) {
            return false;
        }
        return momentInfoMapper.updateByPrimaryKey(momentInfoDO) > 0;
    }

    @Override
    public MomentInfoDO queryByMomentId(String momentId) {
        MomentInfoParam param = new MomentInfoParam();
        MomentInfoParam.Criteria criteria = param.createCriteria();
        criteria.andMomentIdEqualTo(momentId);
        List<MomentInfoDO> momentInfoDOS = momentInfoMapper.selectByParam(param);
        return momentInfoDOS.isEmpty() ? null : momentInfoDOS.get(0);
    }

    @Override
    public boolean batchInsert(List<MomentInfoDO> momentInfoDOList) {
        if (CollectionUtils.isEmpty(momentInfoDOList)) {
            return false;
        }
        return momentInfoMapper.batchInsert(momentInfoDOList) > 0;
    }

    @Override
    public List<MomentInfoDO> queryMomentInfoByTime(int days) {
        Date end = new Date();
        Date start = DateUtils.addDays(end, days);
        return queryAllMomentByDate(start, end);
    }

    @Override
    public MomentInfoVO queryMomentInfo(TaskInfoDO taskInfoDO) {
        MomentInfoVO momentInfoVO = new MomentInfoVO();
        momentInfoVO.setId(taskInfoDO.getId());
        List<MomentInfoDO> momentInfoDOList = getMomentListByTaskId(taskInfoDO.getId());
        if (CollectionUtils.isNotEmpty(momentInfoDOList)) {
            momentInfoVO.setCommentsNumber(momentInfoDOList.stream().filter(momentInfoDO -> Objects.nonNull(momentInfoDO.getCommentCount())).mapToInt(MomentInfoDO::getCommentCount).sum());
            momentInfoVO.setLikeCount(momentInfoDOList.stream().filter(momentInfoDO -> Objects.nonNull(momentInfoDO.getLikeCount())).mapToInt(MomentInfoDO::getLikeCount).sum());
            Long successfulPublicationNumber = momentInfoDOList.stream().filter(momentInfoDO -> MomentPushStatusEnum.PUBLISH.getCode().equals(momentInfoDO.getPushStatus().intValue())).count();
            momentInfoVO.setSuccessfulPublicationNumber(successfulPublicationNumber);
            momentInfoVO.setFailedPublicationNumber(momentInfoDOList.size() - successfulPublicationNumber);
            JSONObject extraInfo = JSON.parseObject(taskInfoDO.getExtInfo());
            // 获得客户覆盖数
            getAutoMatchMomentInfoVO(taskInfoDO, momentInfoVO, momentInfoDOList);
            if (extraInfo.containsKey("wechatUserMatchStrategy") && Objects.equals(Integer.parseInt(extraInfo.get("wechatUserMatchStrategy").toString()), WechatUserMatchStrategyEnum.SMART.getCode())) {
                momentInfoVO.setWechatUserMatchStrategy(WechatUserMatchStrategyEnum.SMART.getCode());
            }
        }
        return momentInfoVO;
    }

    private void getAutoMatchMomentInfoVO(TaskInfoDO taskInfoDO, MomentInfoVO momentInfoVO, List<MomentInfoDO> momentInfoDOList) {

        List<String> userIdList = momentInfoDOList.stream()
                .filter(momentInfoDO -> MomentPushStatusEnum.PUBLISH.getCode().equals(momentInfoDO.getPushStatus().intValue()))
                .map(MomentInfoDO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        momentInfoVO.setUserIdList(userIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        TaskInstanceQuery query = new TaskInstanceQuery();
        query.setTaskId(taskInfoDO.getId());
        TaskInstanceDO taskInstanceDO = taskInstanceService.getNewest(query);
        if (Objects.nonNull(taskInstanceDO)) {
            JSONObject extraInfo = JSON.parseObject(taskInstanceDO.getExtInfo());
            if (Objects.isNull(extraInfo)) {
                return;
            }
            Long coverCount = extraInfo.getLong("coverCount");
            if (NumberUtils.validLong(coverCount)) {
                momentInfoVO.setCustomerTotalNumber(coverCount);
            }
        }
    }

    /**
     * 获取任务的上下文信息
     *
     * @param taskInfoDO 任务实体
     * @return 活动上下文
     */
    private ActivityTaskInfoBO getActivityTaskInfo(TaskInfoDO taskInfoDO) {
        if (taskInfoDO == null) {
            return null;
        }
        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(taskInfoDO.getActivityId());
        query.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_TASK_ID);
        query.setTargetId(String.valueOf(taskInfoDO.getId()));
        List<ActivityTaskInfoBO> targetActivityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
        if (CollectionUtils.isEmpty(targetActivityTaskInfoBOS)) {
            return null;
        }
        return targetActivityTaskInfoBOS.get(0);
    }

    @Override
    public List<MomentInfoDO> queryAllMomentByDate(Date start, Date end) {
        ArrayList<MomentInfoDO> result = new ArrayList<>();
        for (long minId = 0; ; ) {
            List<MomentInfoDO> momentInfoDOS = momentInfoMapper.selectByDaysAndMinIdLimit(start, end, minId);
            if (CollectionUtils.isEmpty(momentInfoDOS)) {
                break;
            }
            minId = momentInfoDOS.stream().map(MomentInfoDO::getId).max(Long::compareTo).orElse(0L);
            result.addAll(momentInfoDOS);
        }
        return result;
    }

    @Override
    public List<MomentInfoDO> queryByParam(MomentInfoQuery query) {
        MomentInfoParam param = new MomentInfoParam();
        MomentInfoParam.Criteria criteria = param.createCriteria();
        if (query.getTaskId() != null) {
            criteria.andTaskIdEqualTo(query.getTaskId());
        }
        if (!StringUtils.isBlank(query.getMomentId())) {
            criteria.andMomentIdEqualTo(query.getMomentId());
        }
        if (!CollectionUtils.isEmpty(query.getUserIdList())) {
            criteria.andUserIdIn(query.getUserIdList());
        }
        if (query.getPushStatus() != null) {
            criteria.andPushStatusEqualTo(query.getPushStatus());
        }
        return momentInfoMapper.selectByParam(param);
    }


    /**
     * 通过任务id获取朋友圈信息
     *
     * @param taskId
     * @return
     */
    private List<MomentInfoDO> getMomentListByTaskId(Long taskId) {
        MomentInfoParam param = new MomentInfoParam();
        MomentInfoParam.Criteria criteria = param.createCriteria();
        criteria.andTaskIdEqualTo(taskId);
        return momentInfoMapper.selectByParam(param);
    }
}
