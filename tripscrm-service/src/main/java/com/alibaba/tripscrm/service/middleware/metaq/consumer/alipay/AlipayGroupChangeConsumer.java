package com.alibaba.tripscrm.service.middleware.metaq.consumer.alipay;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.domain.request.TripSCRMGroupTemplateSyncRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMSyncGroupService;
import com.alibaba.tripscrm.service.constant.AlipayConstant;
import com.alibaba.tripscrm.service.convert.AlipayGroupConverter;
import com.alibaba.tripscrm.service.enums.alipay.AlipayGroupChangeTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.dto.group.ChatSynchronizerDTO;
import com.alibaba.tripscrm.service.synchronizer.GroupChatSynchronizer;
import com.alibaba.tripscrm.service.synchronizer.SyncResult;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.api.service.alipay.group.AlipayGroupService;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupDetailDTO;
import com.alibaba.tripzoo.proxy.request.alipay.AlipayGroupInfoPageRequest;
import com.alibaba.tripzoo.proxy.result.PageInfoDTO;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/21 20:02
 * @Filename：AlipayGroupChangeConsumer
 */

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AlipayGroupChangeConsumer implements MessageListenerConcurrently {
    private final GroupChatSynchronizer groupChatSynchronizer;
    private final AlipayGroupService alipayGroupService;
    private final TripSCRMSyncGroupService syncGroupService;
    private final AlipayGroupConverter alipayGroupConverter;

    /**
     * It is not recommend to throw exception,rather than returning ConsumeConcurrentlyStatus.RECONSUME_LATER if
     * consumption failure
     *
     * @param msgs    msgs.size() >= 1<br> DefaultMQPushConsumer.consumeMessageBatchMaxSize=1,you can modify here
     * @param context
     * @return The consume status
     */
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {

        try {
            for (MessageExt msg : msgs) {
                PlatformLogUtil.logInfo("接收到支付宝群变更消息", msg);
                SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);

                handleMember(msg);

            }
        } catch (Exception e) {
            PlatformLogUtil.logException("接收到支付宝群变更消息同步异常", e.getMessage(), e, LogListUtil.newArrayList());
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void handleMember(MessageExt msg) {
        String tag = msg.getTags();
        String message = new String(msg.getBody());
        String key = msg.getKeys();
        // 根据tag执行不同方法
        AlipayGroupChangeTypeEnum type = AlipayGroupChangeTypeEnum.getByCode(tag);
        JSONObject jsonObject = JSONObject.parseObject(message);
        String chatId = jsonObject.getString(AlipayConstant.GROUP_INSTANCE_ID);
        String groupId = jsonObject.getString(AlipayConstant.GROUP_ID);
        Date createTime = new Date(Long.parseLong(jsonObject.getString(AlipayConstant.CREATE_TIME)));

        try {
            BaseSynchronizerContext<ChatSynchronizerDTO> syncContext = new BaseSynchronizerContext<>();
            ChatSynchronizerDTO data = new ChatSynchronizerDTO();
            data.setPlatformType(PlatformTypeEnum.ALI_PAY);
            data.setChatId(chatId);
            data.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);
            data.setChangeType(type);
            syncContext.setData(data);
            if (!Objects.equals(AlipayGroupChangeTypeEnum.CHANGE_NAME, type)) {
                syncContext.setUserId(key);
            }
            Map<String, String> extraInfo = new HashMap<>();
            extraInfo.put(AlipayConstant.CHANGE_TYPE, type.getCode());
            extraInfo.put(AlipayConstant.CHANGE_TIME, createTime.toString());
            syncContext.setExtraInfo(extraInfo);
            SyncResult<ChatSynchronizerDTO> sync = groupChatSynchronizer.sync(syncContext);
        } catch (Exception e) {
            PlatformLogUtil.logException("同步群聊信息异常", e.getMessage(), e, LogListUtil.newArrayList(chatId, e));
        }

        //群组同步
        GroupDetailDTO groupDetailDTO = pageGroupDetailDTOAll(chatId, groupId);
        if (Objects.isNull(groupDetailDTO)) {
            return;
        }
        executeSyncGroupTask(groupDetailDTO);

    }

    private GroupDetailDTO pageGroupDetailDTOAll(String chatId, String groupId) {
        //通过群id查询群组id
        List<GroupDetailDTO> groupDetailDTOList = new ArrayList<>();

        AlipayGroupInfoPageRequest pageRequest = new AlipayGroupInfoPageRequest();
        pageRequest.setPageNum(1L);
        pageRequest.setPageSize(50L);
        ResultDO<PageInfoDTO<GroupDetailDTO>> pageResultDO;
        // 分页查询所有群组
        do {
            pageResultDO = alipayGroupService.pageQuery(pageRequest);
            if (!pageResultDO.getSuccess() || Objects.isNull(pageResultDO.getModel()) || pageResultDO.getModel().getList().isEmpty()) {
                PlatformLogUtil.logFail("查询支付宝群组信息失败", LogListUtil.newArrayList(pageRequest, pageResultDO));
                break;
            }
            groupDetailDTOList.addAll(pageResultDO.getModel().getList());
            pageRequest.setPageNum(pageRequest.getPageNum() + 1);

        } while (Objects.nonNull(pageResultDO) && pageResultDO.getModel().getTotal() > groupDetailDTOList.size());
        if (groupDetailDTOList.isEmpty()) {
            PlatformLogUtil.logFail("查询支付宝群组信息为空", LogListUtil.newArrayList(pageRequest, pageResultDO));
            return null;
        }

        GroupDetailDTO groupDetailDTO = Optional.ofNullable(groupDetailDTOList).orElse(new ArrayList<>())
                .stream().filter(Objects::nonNull)
                .filter(item -> item.getGroupBaseInfoDTO().getGroupId().equals(groupId))
                .findFirst().orElse(null);

        return groupDetailDTO;
    }

    //群组同步任务
    private void executeSyncGroupTask(GroupDetailDTO groupDetailDTO) {
        if (Objects.isNull(groupDetailDTO)) {
            PlatformLogUtil.logFail("群组信息为空", LogListUtil.newArrayList(groupDetailDTO));
            return;
        }
        try {
            TripSCRMGroupTemplateSyncRequest request = alipayGroupConverter.convert2GroupTemplateBuildRequest(groupDetailDTO);
            TripSCRMResult<Boolean> result = syncGroupService.syncGroupTemplateInfo(request);
            if (!result.isSuccess() || !result.getData()) {
                PlatformLogUtil.logFail("同步群组信息失败", LogListUtil.newArrayList(groupDetailDTO, result));
            }

        } catch (Exception e) {
            PlatformLogUtil.logException("同步群组信息失败", e.getMessage(), e, LogListUtil.newArrayList(groupDetailDTO, e));
        }
    }


}