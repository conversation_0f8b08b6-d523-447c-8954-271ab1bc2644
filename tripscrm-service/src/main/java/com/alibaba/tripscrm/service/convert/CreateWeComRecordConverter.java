package com.alibaba.tripscrm.service.convert;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.CreateWeComRecordDO;
import com.alibaba.tripscrm.service.enums.wechat.WechatUserApplyStatusEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.query.WechatUserApplyRecordQuery;
import com.alibaba.tripscrm.service.model.vo.isv.CreateWeComRecordVO;
import com.alibaba.tripscrm.service.model.dto.wechat.CreateWeComRecordDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatUserApplyRecordDTO;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.wechat.WechatDepartmentService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserApplyRecordService;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.model.DepartmentBO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * CreateWeComRecord相关数据防腐层
 *
 * <AUTHOR>
 * @since 2024-01-21 14:46
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CreateWeComRecordConverter {

    private final WechatDepartmentService wechatDepartmentService;
    private final WXCorpStorage wxCorpStorage;
    private final WechatUserApplyRecordService wechatUserApplyRecordService;
    private final AccountService accountService;

    private static final String ORDER_ID = "orderId";
    /**
     * CreateWeComRecordVO 转 CreateWeComRecordDTO
     */
    public CreateWeComRecordDTO convert2DTO(CreateWeComRecordVO createWeComRecordVO) {
        if (Objects.isNull(createWeComRecordVO)) {
            return null;
        }
        CreateWeComRecordDTO createWeComRecordDTO = new CreateWeComRecordDTO();
        BeanUtils.copyProperties(createWeComRecordVO, createWeComRecordDTO);
        return createWeComRecordDTO;
    }

    /**
     * CreateWeComRecordDTO 转 CreateWeComRecordVO
     */
    public CreateWeComRecordVO convert2VO(CreateWeComRecordDTO createWeComRecordDTO) {
        if (Objects.isNull(createWeComRecordDTO)) {
            return null;
        }
        CreateWeComRecordVO createWeComRecordVO = new CreateWeComRecordVO();
        BeanUtils.copyProperties(createWeComRecordDTO, createWeComRecordVO);
        createWeComRecordVO.setApplyCount(createWeComRecordDTO.getCount());
        DepartmentBO departmentBO = wechatDepartmentService.list(createWeComRecordDTO.getCorpId());
        if (Objects.nonNull(departmentBO)) {
            createWeComRecordVO.setDepartmentName(departmentBO.getName());
        }
        createWeComRecordVO.setCorpName(wxCorpStorage.getCorpName(createWeComRecordDTO.getCorpId()));
        WechatUserApplyRecordQuery query = new WechatUserApplyRecordQuery();
        query.setCreateWeComRecordId(createWeComRecordDTO.getId());
        query.setStatus(WechatUserApplyStatusEnum.SUCCESS.getCode());
        List<WechatUserApplyRecordDTO> wechatUserApplyRecordDTOS = wechatUserApplyRecordService.selectByCondition(query);
        if (!CollectionUtils.isEmpty(wechatUserApplyRecordDTOS)) {
            createWeComRecordVO.setSuccessCount(wechatUserApplyRecordDTOS.size());
        } else {
            createWeComRecordVO.setSuccessCount(0);
        }
        User user = accountService.getUserByAccountId(createWeComRecordDTO.getCreatorId());
        if (Objects.nonNull(user)) {
            createWeComRecordVO.setCreatorName(user.getUserName());
        }
        IsvTypeEnum isvTypeEnum = IsvTypeEnum.valueOf(createWeComRecordDTO.getIsvType());
        if (Objects.nonNull(isvTypeEnum)) {
            createWeComRecordVO.setIsvName(isvTypeEnum.getDesc());
        }
        JSONObject extInfo = JSONObject.parseObject(createWeComRecordDTO.getExtInfo());
        if (Objects.nonNull(extInfo)) {
            createWeComRecordVO.setOrderId(extInfo.getString(ORDER_ID));
        }
        return createWeComRecordVO;
    }

    /**
     * CreateWeComRecordDTO 转 CreateWeComRecordDO
     */
    public CreateWeComRecordDO convert2DO(CreateWeComRecordDTO createWeComRecordDTO) {
        if (Objects.isNull(createWeComRecordDTO)) {
            return null;
        }
        CreateWeComRecordDO createWeComRecordDO = new CreateWeComRecordDO();
        BeanUtils.copyProperties(createWeComRecordDTO, createWeComRecordDO);
        return createWeComRecordDO;
    }

    /**
     * CreateWeComRecordDO 转 CreateWeComRecordDTO
     */
    public CreateWeComRecordDTO convert2DTO(CreateWeComRecordDO createWeComRecordDO) {
        if (Objects.isNull(createWeComRecordDO)) {
            return null;
        }
        CreateWeComRecordDTO createWeComRecordDTO = new CreateWeComRecordDTO();
        BeanUtils.copyProperties(createWeComRecordDO, createWeComRecordDTO);
        return createWeComRecordDTO;
    }


}
