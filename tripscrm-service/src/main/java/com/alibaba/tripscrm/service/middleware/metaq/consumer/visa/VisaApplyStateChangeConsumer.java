package com.alibaba.tripscrm.service.middleware.metaq.consumer.visa;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alitrip.travelvc.common.model.enums.ApplyMode;
import com.alitrip.travelvc.common.model.enums.VisaApplyState;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.*;


import java.util.List;
import java.util.Objects;

/**
 * topic: travelvc_unity_metaq_topic
 * consumerId: CID_tripscrm_travelvc_unity_metaq_topic
 *
 * 申请人签证履约状态变更消息消费
 *
 * <AUTHOR>
 * @since 2025/07/22
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class VisaApplyStateChangeConsumer implements MessageListenerConcurrently {

    private final UicUtils uicUtils;
    private final WechatCustomerService wechatCustomerService;
    private final MetaqProducer metaqProducer;

    private static final String EXTERNAL_USER_ID = "externalUserId";
    private static final String VISA_LINK = "visaLink";
    private static final String VISA_USER_NAME = "visaUserName";
    private static final String ORDER_ID = "orderId";
    private static final String APPLY_MODE = "applyMode";
    private static final String APPLY_STATUS = "status";
    private static final String BUYER_USER_ID = "buyerId";
    private static final String APPLY_NAME = "applyName";

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logInfo("接收到申请人签证履约状态变更消息", msg, context);
            String receivedMsg = unMarshall(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("申请人签证履约状态变更消息处理失败", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private <T extends Serializable> T unMarshall(byte[] dataByteArray) {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(dataByteArray);
        try {
            ObjectInputStream objectInputStream = new ObjectInputStream(byteArrayInputStream);
            Object data = objectInputStream.readObject();
            return (T) data;
        } catch (IOException | ClassNotFoundException e) {
            PlatformLogUtil.logException("BcMetaQTools.unMarshall", e.getMessage(), e, LogListUtil.newArrayList(dataByteArray));
        }
        return null;
    }

    private static <T extends Serializable> byte[] marshall(T data) {
        if (data == null) {
            return new byte[0];
        }
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            byteArrayOutputStream = new ByteArrayOutputStream();
            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(byteArrayOutputStream);
            ObjectOutputStream outputStream = new ObjectOutputStream(bufferedOutputStream);
            outputStream.writeObject(data);
            bufferedOutputStream.flush();

            return byteArrayOutputStream.toByteArray();

        } catch (IOException e) {
            PlatformLogUtil.logException("BcMetaQTools.marshall 对象转byte数组异常", e.getMessage(), e, LogListUtil.newArrayList(data));
        } finally {
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    PlatformLogUtil.logException("BcMetaQTools.marshall 流关闭异常", e.getMessage(), e, LogListUtil.newArrayList(data));
                }
            }
        }
        return null;
    }

    private boolean dealWithMessage(String message) {
        JSONObject msgJson;
        try {
            msgJson = JSONObject.parseObject(message);
            PlatformLogUtil.logInfo("申请人签证履约状态变更消息解析结果", msgJson);
        } catch (Exception e) {
            PlatformLogUtil.logException("申请人签证履约状态变更消息解析异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
        Long orderId = msgJson.getLong(ORDER_ID);
        Integer applyModeCode = msgJson.getInteger(APPLY_MODE);
        Integer applyStatusCode = msgJson.getInteger(APPLY_STATUS);
        Long buyerUserId = msgJson.getLong(BUYER_USER_ID);
        String buyerUserName = msgJson.getString(APPLY_NAME);
        if (!NumberUtils.validLong(orderId) || !NumberUtils.validInteger(applyModeCode) || !NumberUtils.validInteger(applyStatusCode) || !NumberUtils.validLong(buyerUserId) || !StringUtils.hasLength(buyerUserName)) {
            PlatformLogUtil.logFail("申请人签证履约状态变更消息参数校验失败, 参数非法", LogListUtil.newArrayList(message));
            return false;
        }
        ApplyMode applyMode = ApplyMode.codeOf(applyModeCode);
        VisaApplyState visaApplyState = VisaApplyState.codeOf(applyStatusCode);
        MQEnum mqEnum;
        switch (applyMode) {
            case ONLINE_MODE:
                mqEnum = processOnlineMode(visaApplyState);
                break;
            case OFFLINE_MODE:
                mqEnum = processOfflineMode(visaApplyState);
                break;
            default:
                PlatformLogUtil.logFail("申请人签证履约状态变更消息参数校验失败, applyMode非法", LogListUtil.newArrayList(message));
                return false;
        }
        if (Objects.isNull(mqEnum)) {
            PlatformLogUtil.logFail("申请人签证履约状态变更消息处理失败, 获取", LogListUtil.newArrayList(message));
            return false;
        }
        String unionId = uicUtils.getUnionIdByUid(String.valueOf(buyerUserId));
        if (!StringUtils.hasLength(unionId)) {
            PlatformLogUtil.logFail("申请人签证履约状态变更消息处理失败，根据uid查询unionId为空", LogListUtil.newArrayList(buyerUserId));
            return false;
        }

        String externalUserId = wechatCustomerService.getExternalUserIdByUnionId(unionId);
        if (!StringUtils.hasText(externalUserId)) {
            PlatformLogUtil.logFail("申请人签证履约状态变更消息处理失败，根据unionId查询externalUserId为空", LogListUtil.newArrayList(buyerUserId, unionId));
            return false;
        }
        JSONObject messageJson = new JSONObject();
        String visaLink = getVisaLink(orderId);
        messageJson.put(VISA_LINK, visaLink);
        messageJson.put(VISA_USER_NAME, buyerUserName);
        messageJson.put(EXTERNAL_USER_ID, externalUserId);
        return metaqProducer.send(mqEnum, externalUserId, "", messageJson.toJSONString());
    }

    /**
     * 获取在线签证mq消息topic
     * @param visaApplyState 签证履约状态
     * @return topic
     */
    private MQEnum processOnlineMode(VisaApplyState visaApplyState) {
        MQEnum mqEnum;
        switch (visaApplyState) {
            case APPLICANT_NOT_ADD:
                mqEnum = MQEnum.VISA_ONLINE_BUYER_PRE_APPLY_PAID_DONE;
                break;
            case SELLER_NEED_MORE_MATERIAL:
                mqEnum = MQEnum.VISA_ONLINE_SELLER_NEED_MORE_MATERIAL;
                break;
            case SELLER_GET_RESULT:
                mqEnum = MQEnum.VISA_ONLINE_SELLER_GET_RESULT;
                break;
            case SELLER_SEND_VISA_BACK:
                mqEnum = MQEnum.VISA_ONLINE_SELLER_SEND_VISA_BACK;
                break;
            case SELLER_REVIEW_DOWN:
                mqEnum = MQEnum.VISA_ONLINE_SELLER_REVIEW_DOWN;
                break;
            default:
                PlatformLogUtil.logFail("申请人签证履约状态变更消息参数校验失败, visaApplyState非法", LogListUtil.newArrayList(visaApplyState));
                return null;
        }
        return mqEnum;
    }

    /**
     * 获取非在线签证mq消息topic
     * @param visaApplyState 签证履约状态
     * @return topic
     */
    private MQEnum processOfflineMode(VisaApplyState visaApplyState) {
        MQEnum mqEnum;
        switch (visaApplyState) {
            case SELLER_NEED_MORE_MATERIAL:
                mqEnum = MQEnum.VISA_OFFLINE_SELLER_NEED_MORE_MATERIAL;
                break;
            case SELLER_GET_RESULT:
                mqEnum = MQEnum.VISA_OFFLINE_SELLER_GET_RESULT;
                break;
            case SELLER_SEND_VISA_BACK:
                mqEnum = MQEnum.VISA_OFFLINE_SELLER_SEND_VISA_BACK;
                break;
            case CONFIRM_BOOK_TIME:
                mqEnum = MQEnum.VISA_OFFLINE_CONFIRM_BOOK_TIME;
                break;
            default:
                PlatformLogUtil.logFail("申请人签证履约状态变更消息参数校验失败, visaApplyState非法", LogListUtil.newArrayList(visaApplyState));
                return null;
        }
        return mqEnum;
    }

    /**
     * 签证订单链接拼接orderId
     *
     * @param orderId 订单id
     * @return 签证订单链接
     */
    private String getVisaLink(Long orderId) {
        String visaLink;
        if (EnvUtils.isOnline()) {
            visaLink = String.format(SwitchConfig.VISA_ORDER_LINK_ONLINE, orderId);
        } else {
            visaLink = String.format(SwitchConfig.VISA_ORDER_LINK_PRE, orderId);
        }
        return visaLink;
    }


    @AteyeInvoker(description = "申请人签证履约状态变更消息处理", paraDesc = "message")
    public boolean dealWithMessageTest(String message) {
        return dealWithMessage(message);
    }
}
