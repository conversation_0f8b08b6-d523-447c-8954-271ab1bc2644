package com.alibaba.tripscrm.service.synchronizer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.CustomerRelationDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.CustomerTagDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.CustomerTagSynchronizerDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripzoo.proxy.api.service.TagService;
import com.alibaba.tripzoo.proxy.request.GetCorpTagListRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.TagInfoResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 客户标签关系同步器
 *
 * <AUTHOR>
 * @since 2025/01/02 10:46
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CustomerTagRelationSynchronizer extends BaseLockedSynchronizer<CustomerTagSynchronizerDTO> {
    private final TagRelationService tagRelationService;
    private final TagGroupService tagGroupService;
    private final TagInfoService tagInfoService;
    private final TagService tagService;
    @Override
    public void process(BaseSynchronizerContext<CustomerTagSynchronizerDTO> context) {
        try {
            // 参数校验
            if (!paramVerify(context)) {
                PlatformLogUtil.logFail("参数校验失败", LogListUtil.newArrayList(context));
                return;
            }
            CustomerTagSynchronizerDTO data = context.getData();
            for (CustomerRelationDTO customerRelationDTO : data.getFollowUserList()) {
                if (!StringUtils.hasLength(customerRelationDTO.getUserId()) || CollectionUtils.isEmpty(customerRelationDTO.getTagInfoList())) {
                    PlatformLogUtil.logFail("所属企微号信息缺失", LogListUtil.newArrayList(customerRelationDTO));
                    continue;
                }
                // 从DB中查询该企微成员对该客户打的标签
                List<Long> oldTagIdList = getTagIdListFromDB(context, customerRelationDTO);

                // 从企微端查询该企微成员对该客户打的标签
                List<Long> newTagIdList = getTagIdListFromWx(customerRelationDTO);

                // 对比并更新
                compareAndSave(context, customerRelationDTO, newTagIdList, oldTagIdList);

            }
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.SYNCHRONIZER_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.SYNCHRONIZER_ERROR);
        }
    }

    /**
     * 对比并更新
     * @param context 上下文
     * @param customerRelationDTO 客户关系信息
     * @param newTagIdList 从企微端获得的标签id列表
     * @param oldTagIdList 从DB中获得的标签id列表
     * @return
     */
    private void compareAndSave(BaseSynchronizerContext<CustomerTagSynchronizerDTO> context, CustomerRelationDTO customerRelationDTO, List<Long> newTagIdList, List<Long> oldTagIdList) {
        List<Long> deleteList = new ArrayList<>();
        List<Long> createList = new ArrayList<>();
        if (CollectionUtils.isEmpty(newTagIdList)) {
            deleteList = oldTagIdList;
        } else {
            deleteList = oldTagIdList.stream().filter(x -> !newTagIdList.contains(x)).collect(Collectors.toList());
        }
        PlatformLogUtil.logInfo("企微标签关系同步器，待删除的标签id列表", LogListUtil.newArrayList(deleteList, context.getExternalUserId(), context.getCorpId(), customerRelationDTO.getUserId()));
        delete(deleteList, context.getExternalUserId(), context.getCorpId(), customerRelationDTO.getUserId());
        if (CollectionUtils.isEmpty(oldTagIdList)) {
            createList = newTagIdList;
        } else {
            createList = newTagIdList.stream().filter(x -> !oldTagIdList.contains(x)).collect(Collectors.toList());
        }
        PlatformLogUtil.logInfo("企微标签关系同步器，待创建的标签id列表", LogListUtil.newArrayList(createList, context.getExternalUserId(), context.getCorpId(), customerRelationDTO.getUserId()));
        create(createList, context.getExternalUserId(), context.getCorpId(), customerRelationDTO.getUserId());
    }

    /**
     * 从企微端查询该企微成员对该客户打的标签
     * @param customerRelationDTO
     * @return newTagIdList
     */
    private List<Long> getTagIdListFromWx(CustomerRelationDTO customerRelationDTO) {
        List<Long> newTagIdList = new ArrayList<>();
        List<CustomerTagDTO> customerTagDTOList = customerRelationDTO.getTagInfoList();
        for (CustomerTagDTO customerTagDTO : customerTagDTOList) {
            if (!StringUtils.hasLength(customerTagDTO.getTagSource())) {
                PlatformLogUtil.logFail("标签信息缺失", LogListUtil.newArrayList(customerTagDTO));
                continue;
            }
            GetCorpTagListRequest getCorpTagListRequest = new GetCorpTagListRequest();
            getCorpTagListRequest.setTagIdList(Collections.singletonList(customerTagDTO.getTagSource()));
            getCorpTagListRequest.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            ResultDO<List<TagInfoResponse>> resultDO = tagService.getCorpTagList(getCorpTagListRequest);
            if (!resultDO.getSuccess() || CollectionUtils.isEmpty(resultDO.getModel())) {
                PlatformLogUtil.logFail("从企业微信查询企微标签信息失败", LogListUtil.newArrayList(customerTagDTO, resultDO));
                continue;
            }
            PlatformLogUtil.logInfo("从企业微信查询企微标签信息结果", LogListUtil.newArrayList(customerTagDTO, resultDO));
            String groupSource = resultDO.getModel().get(0).getGroupId();
            if (!StringUtils.hasLength(groupSource)) {
                PlatformLogUtil.logFail("标签组id为空", LogListUtil.newArrayList(customerTagDTO));
                continue;
            }
            TagGroupDTO tagGroupDTO = tagGroupService.selectBySpaceIdAndTagTypeAndSourceWithCache(SwitchConfig.SCRM_BASE_SPACE_ID, TagTypeEnum.ENTERPRISE_WECHAT_TAG, groupSource);
            if (Objects.isNull(tagGroupDTO)) {
                PlatformLogUtil.logFail("标签组不存在", LogListUtil.newArrayList(customerTagDTO, groupSource));
                continue;
            }
            TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(tagGroupDTO.getId(), customerTagDTO.getTagSource());
            if (Objects.isNull(tagInfoDTO)) {
                PlatformLogUtil.logFail("标签不存在", LogListUtil.newArrayList(customerTagDTO));
                continue;
            }
            newTagIdList.add(tagInfoDTO.getId());
        }
        return newTagIdList;
    }

    /**
     * 从DB中查询该企微成员对该客户打的标签
     * @param context
     * @param customerRelationDTO
     * @return oldTagIdList
     */
    private List<Long> getTagIdListFromDB(BaseSynchronizerContext<CustomerTagSynchronizerDTO> context, CustomerRelationDTO customerRelationDTO) {
        List<Long> oldTagIdList = new ArrayList<>();
        ItemTagRelationQuery itemTagRelationQuery = new ItemTagRelationQuery();
        itemTagRelationQuery.setItemType(BizTypeEnum.WECHAT_CUSTOMER_RELATION.getCode());
        itemTagRelationQuery.setItemId(context.getExternalUserId() +  "_" + context.getCorpId());
        itemTagRelationQuery.setRelationId(customerRelationDTO.getUserId());
        itemTagRelationQuery.setDeleted(IsDeleteEnum.NO.getCode());
        List<ItemTagRelationDTO> itemTagRelationDTOS = tagRelationService.selectByCondition(itemTagRelationQuery);
        oldTagIdList = itemTagRelationDTOS.stream().map(ItemTagRelationDTO::getTagId).collect(Collectors.toList());
        return oldTagIdList;
    }

    @Override
    public String getLockKey(BaseSynchronizerContext<CustomerTagSynchronizerDTO> context) {
        return TairConstant.WECHAT_CUSTOMER_TAG_RELATION_SYNCHRONIZER_LOCK_PREFIX + context.getExternalUserId() + "_" + context.getCorpId();
    }

    /**
     * 参数校验
     * @param context
     * @return
     */
    private Boolean paramVerify(BaseSynchronizerContext<CustomerTagSynchronizerDTO> context) {
        if (Objects.isNull(context) || !StringUtils.hasLength(context.getExternalUserId()) || !StringUtils.hasLength(context.getCorpId()) || Objects.isNull(context.getData())) {
            PlatformLogUtil.logFail("企微标签同步器参数错误，上下文参数为空", LogListUtil.newArrayList(context));
            return false;
        }
        CustomerTagSynchronizerDTO data = context.getData();
        if (CollectionUtils.isEmpty(data.getFollowUserList())) {
            PlatformLogUtil.logFail("企微标签同步器参数错误，上下文参数为空", LogListUtil.newArrayList(context));
            return false;
        }
        return true;
    }

    /**
     * 创建
     * @param createList
     * @param externalUserId
     * @param corpId
     * @param userId
     */
    private void create(List<Long> createList, String externalUserId, String corpId, String userId) {
        if (CollectionUtils.isEmpty(createList) || !StringUtils.hasLength(externalUserId) || !StringUtils.hasLength(corpId) || !StringUtils.hasLength(userId)) {
            return;
        }
        List<ItemTagRelationDTO> itemTagRelationDTOS = new ArrayList<>();
        for (Long tagId : createList) {
            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            itemTagRelationDTO.setItemId(externalUserId + "_" + corpId);
            itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_CUSTOMER_RELATION.getCode());
            itemTagRelationDTO.setTagId(tagId);
            itemTagRelationDTO.setIsSync(false);
            itemTagRelationDTO.setRelationId(userId);
            itemTagRelationDTO.setDeleted(IsDeleteEnum.NO.getCode());
            itemTagRelationDTOS.add(itemTagRelationDTO);
        }
        tagRelationService.batchUpsertSelective(itemTagRelationDTOS);
    }

    /**
     * 删除
     * @param deleteList
     * @param externalUserId
     * @param corpId
     * @param userId
     */
    private void delete(List<Long> deleteList, String externalUserId, String corpId, String userId) {
        if (CollectionUtils.isEmpty(deleteList) || !StringUtils.hasLength(externalUserId) || !StringUtils.hasLength(corpId) || !StringUtils.hasLength(userId)) {
            return;
        }
        for (Long tagId : deleteList) {
            ItemTagRelationQuery condition = new ItemTagRelationQuery();
            condition.setDeleted(IsDeleteEnum.NO.getCode());
            condition.setTagId(tagId);
            condition.setItemType(BizTypeEnum.WECHAT_CUSTOMER_RELATION.getCode());
            condition.setItemId(externalUserId + "_" + corpId);
            condition.setRelationId(userId);
            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            itemTagRelationDTO.setDeleted(IsDeleteEnum.YES.getCode());
            itemTagRelationDTO.setIsSync(false);

            tagRelationService.updateSelective(itemTagRelationDTO, condition);
        }
    }
}
