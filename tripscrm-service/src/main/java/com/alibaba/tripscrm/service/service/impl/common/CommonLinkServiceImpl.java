package com.alibaba.tripscrm.service.service.impl.common;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.alipay.AlipayPageTypeEnum;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.second.ShortLinkService;
import com.alibaba.tripzoo.proxy.api.service.WxMiniProgramService;
import com.alibaba.tripzoo.proxy.request.GenerateWechatMiniProgramShortLinkRequest;
import com.alibaba.tripzoo.proxy.request.WxJumpWxaBO;
import com.alibaba.tripzoo.proxy.request.WxUrlSchemeRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/8 16:35
 **/
@Component
public class CommonLinkServiceImpl {

    @Resource
    private WxMiniProgramService wxMiniProgramService;
    @Resource
    private ShortLinkService shortLinkService;

    /**
     * 是否为h5页面
     *
     * @param path 页面链接
     * @return 是否为h5页面
     */
    public boolean isH5Url(String path) {
        return path.startsWith("http");
    }

    /**
     * 对 url 追加参数
     *
     * @param url      链接
     * @param paramMap 参数
     * @return 追加参数后的 url
     */
    public String concatHttpUrlParam(String url, Map<String, String> paramMap) {
        if (!StringUtils.hasText(url)) {
            return url;
        }

        try {
            UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(url);
            for (String key : paramMap.keySet()) {
                uriComponentsBuilder.replaceQueryParam(key, paramMap.get(key));
            }
            return uriComponentsBuilder.build(true).toUriString();
        } catch (Exception e) {
            PlatformLogUtil.logException("物料工具，对http地址追加参数", e.getMessage(), e, LogListUtil.newArrayList(url, paramMap));
            return url;
        }
    }

    /**
     * 对 url 追加参数
     *
     * @param url      链接
     * @param paramMap 参数
     * @return 追加参数后的 url
     */
    public String concatMiniProgramParam(String url, Map<String, String> paramMap) {
        if (org.apache.commons.lang3.StringUtils.isBlank(url)) {
            return url;
        }

        try {
            UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(url);
            for (String key : paramMap.keySet()) {
                uriComponentsBuilder.replaceQueryParam(key, paramMap.get(key));
            }
            return uriComponentsBuilder.build(true).toUriString();
        } catch (Exception e) {
            PlatformLogUtil.logException("物料工具，对小程序地址追加参数", e.getMessage(), e, LogListUtil.newArrayList());
            return url;
        }
    }

    /**
     * 构建短链
     *
     * @param originUrl   原始链接
     * @param title       标题（微信内使用时生效）
     * @param useInWechat 是否在微信内使用（微信内使用时生效）
     * @return 短链
     */
    public String buildShortLink(String originUrl, String title, Boolean useInWechat) {
        if (isH5Url(originUrl)) {
            return shortLinkService.convertToShortUrl(originUrl);
        }
        return buildMinProgramShortLink(originUrl, title, useInWechat);
    }

    /**
     * 构建支付宝短链
     *
     * @param originUrl 原始链接
     * @return 短链
     */
    public String buildAlipayShortLink(String originUrl,Map<String, String> paramMap) {
        if (!isH5Url(originUrl)) {
            String concatMiniProgramUrl = concatMiniProgramParam(originUrl, paramMap);
            return shortLinkService.getAlipayShortUrl(concatMiniProgramUrl, null, AlipayPageTypeEnum.MINIAPP.getCode());

        }
        return shortLinkService.getAlipayShortUrl(originUrl, null, AlipayPageTypeEnum.WEBVIEW.getCode());

    }


    /**
     * 构建小程序短链
     *
     * @param originUrl   原始链接
     * @param title       标题
     * @param useInWechat 在微信中使用
     * @return 小程序短链
     */
    private String buildMinProgramShortLink(String originUrl, String title, Boolean useInWechat) {
        if (useInWechat) {
            return buildMiniProgramShortLinkUseInWechat(originUrl, title);
        }
        return buildMiniProgramShortLinkUseNotInWechat(originUrl);
    }

    /**
     * 构建小程序短链
     * 用于在微信内拉起小程序，可设置标题
     *
     * @param originUrl 原始链接
     * @param title     短链标题
     * @return 小程序短链短链
     */
    private String buildMiniProgramShortLinkUseInWechat(String originUrl, String title) {
        if (!StringUtils.hasText(originUrl)) {
            return originUrl;
        }

        GenerateWechatMiniProgramShortLinkRequest request = new GenerateWechatMiniProgramShortLinkRequest();
        request.setPageUrl(originUrl);
        if (StringUtils.hasText(title)) {
            request.setPageTitle(title);
        }

        ResultDO<String> resultDO = wxMiniProgramService.generateShortLink(request);
        if (resultDO == null || !resultDO.getSuccess() || !StringUtils.hasText(resultDO.getModel())) {
            PlatformLogUtil.logFail("构建小程序内部短链失败", LogListUtil.newArrayList(originUrl, title, resultDO));
            throw new TripscrmException(TripSCRMErrorCode.GENERATE_MINI_PROGRAM_SHORT_LINK_FAIL);
        }
        PlatformLogUtil.logInfo("构建小程序内部短链成功", LogListUtil.newArrayList(originUrl, title, resultDO));
        return resultDO.getModel();
    }

    /**
     * 构建小程序短链
     * 用于在微信内/微信外拉起小程序，不可设置标题
     *
     * @param originUrl 原始链接
     * @return 小程序短链
     */
    private String buildMiniProgramShortLinkUseNotInWechat(String originUrl) {
        WxUrlSchemeRequest request = new WxUrlSchemeRequest();
        request.setAvailableDays(30);
        request.setNeverExpire(false);

        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(originUrl);
        UriComponents uriComponents = uriComponentsBuilder.build(true);
        WxJumpWxaBO wxJumpWxaBO = new WxJumpWxaBO(uriComponents.getPath(), uriComponents.getQuery());
        request.setWxJumpWxaBO(wxJumpWxaBO);

        ResultDO<String> resultDO = wxMiniProgramService.getUrlScheme(request);
        if (resultDO == null || !resultDO.getSuccess() || !StringUtils.hasText(resultDO.getModel())) {
            PlatformLogUtil.logFail("构建微信外小程序短链失败", LogListUtil.newArrayList(originUrl, resultDO));
            return "";
        }
        PlatformLogUtil.logFail("构建微信外小程序短链成功", LogListUtil.newArrayList(originUrl, resultDO));
        return resultDO.getModel();
    }

    /**
     * 对 pagePath 进行两次编码，然后拼接上 webview 的前缀
     *
     * @param pagePath 小程序链接
     * @param paramMap 参数
     * @return 编码后的结果
     * @throws UnsupportedEncodingException 异常
     */
    public String codePath(String pagePath, Map<String, String> paramMap) throws UnsupportedEncodingException {
        return "pages/main/act-webview?url=" + URLEncoder.encode(URLEncoder.encode(concatHttpUrlParam(pagePath, paramMap), StandardCharsets.UTF_8.name()), StandardCharsets.UTF_8.name());
    }

}
