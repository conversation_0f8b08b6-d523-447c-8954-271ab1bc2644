package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.tripscrm.service.enums.wechat.GroupStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/4
 */
@Data
public class ConsumerGroupQuery {
    /**
     * 客户群跟进状态过滤
     */
    @JSONField(name = "status_filter")
    private int statusFilter = GroupStatusEnum.ALL.getStatus();

    /**
     * 群主过滤
     */
    @JSONField(name = "owner_filter")
    private OwnerFilterDO ownerFilter;

    /**
     * 用于分页查询的游标，字符串类型，由上一次调用返回，首次调用不填
     */
    private String cursor;

    /**
     * 分页，预期请求的数据量，取值范围 1 ~ 1000
     */
    private int limit;

    /**
     * 群名
     */
    private String name;

    /**
     * 群配置id
     */
    private String chatId;

}
