package com.alibaba.tripscrm.service.service.task.ability.sub.pre.biz;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;

/**
 * 单条任务数据处理_前置处理
 *
 * <AUTHOR>
 * @since 2024/4/22 11:56
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class PreBizProcessor implements BizProcessor {
    private final LdbTairManager ldbTairManager;
    private final TaskService taskService;

    @Override
    @TaskExecuteLog("单条任务数据处理_前置处理")
    public TripSCRMResult<Void> process(TaskExecuteContext context, TodoTaskVO todoTaskVO) throws Exception {
        // 任务执行前的状态校验
        checkTaskStatus(context, todoTaskVO);
        // 填充自定义参数
        fillCustomerConfig(context);
        return TripSCRMResult.success(null);
    }

    private void checkTaskStatus(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        Long taskId = context.getTaskId();
        String taskStatus = (String) ldbTairManager.get(TairConstant.LDB_TASK_STATUS_PREFIX + taskId);

        // 缓存不存在，从db中查询后填充
        if (StringUtils.isBlank(taskStatus)) {
            taskStatus = taskService.queryTaskById(taskId).getStatus();
            ldbTairManager.put(TairConstant.LDB_TASK_STATUS_PREFIX + taskId, taskStatus, EXPIRE_TIME_DAY_UNIT);
        }

        // 测试链路不需要卡任务上线
        if (!context.getTestFlag() && StringUtils.isNotBlank(taskStatus) && !TaskStatusEnum.ONLINE.equals(TaskStatusEnum.getByStatus(taskStatus))) {
            throw new TripscrmException(TripSCRMErrorCode.TASK_STATUS_NOT_ONLINE);
        }

        if (!context.getTestFlag() && !taskService.checkInWorkTimeWindow(context.getTaskInfoDOSnapshot())) {
            throw new TripscrmException(TripSCRMErrorCode.TASK_EXECUTE_OVER_EXECUTE_ENDTIME);
        }
    }

    private void fillCustomerConfig(TaskExecuteContext context) {
        Map<String, Object> extInfo = context.getExtInfo();
        TaskInfoDO taskInfoDO = context.getTaskInfoDOSnapshot();
        //处理自定义配置
        if (StringUtils.isBlank(taskInfoDO.getCustomerConfig())) {
            return;
        }

        List<Map<String, String>> customerConfig = JSONObject.parseObject(taskInfoDO.getCustomerConfig(), new TypeReference<List<Map<String, String>>>() {
        });
        for (Map<String, String> map : customerConfig) {
            extInfo.put(map.get("k"), map.get("v"));
        }
    }
}