package com.alibaba.tripscrm.service.service.task.ability.data.split;

import com.alibaba.fastjson.JSONPath;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-02-29 17:05:38
 */
@Component
public class SplitBySenderUserProcessor extends AbstractTaskDataSplitProcessor {
    @Override
    protected List<TaskType> getTaskTypeList() {
        return Lists.newArrayList(TaskType.ROBOT_CHAT_MESSAGE,
                TaskType.GROUP_CHAT_MESSAGE,
                TaskType.INVITE_JOIN_GROUP,
                TaskType.TRANSFER_CUSTOMER,
                TaskType.RESIGNED_TRANSFER_CUSTOMER,
                TaskType.FORWARD_MESSAGE,
                TaskType.CUSTOMER_RECALL,
                TaskType.UNILATERAL_CHAT_MESSAGE);
    }

    @Override
    protected List<List<TaskDataVO.DataBodyVO>> handleSplitData(TaskExecuteContext context) {
        List<TaskDataVO.DataBodyVO> allData = context.getTaskDataVO().getData();
        Map<String, List<TaskDataVO.DataBodyVO>> userId2DataList = allData.stream().collect(Collectors.groupingBy(dataBody -> (String) JSONPath.read(dataBody.getExtInfo(), "$.sendUserId")));
        return new ArrayList<>(userId2DataList.values());
    }
}