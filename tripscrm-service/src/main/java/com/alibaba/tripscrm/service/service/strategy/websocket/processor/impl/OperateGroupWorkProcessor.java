package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.OperateGroupWorkRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/6/27 17:24
 **/
@Service
public class OperateGroupWorkProcessor implements WsEventProcessor {

    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private GetUserConversationListProcessor getUserConversationListProcessor;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.OPERATE_GROUP_WORK_STATUS;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        // 请求参数获取
        OperateGroupWorkRequest operateGroupWorkRequest = wsEvent.getData().toJavaObject(OperateGroupWorkRequest.class);
        // 校验成员关注上限
        String corpId = Optional.ofNullable(wsEvent.getCorpId()).orElse(SpaceInfoThreadLocalUtils.getCorpId());
        List<String> userWorkChatIdList = wechatGroupService.getUserWorkChatIdList(wsEvent.getUserId(), corpId);
        if (CollectionUtils.isNotEmpty(userWorkChatIdList) && userWorkChatIdList.size() >= SwitchConfig.USER_WORK_GROUP_LIMIT) {
            wsEvent.setSuccess(false);
            wsEvent.setErrorMessage("当前成员关注群聊已到达上限");
            webSocketFactory.pushMessageBySession(session, wsEvent, false);
            return;
        }
        // 执行关注
        ResultDO<Void> resultDO = wechatGroupService.operateGroupWork(wsEvent.getUserId(), corpId, operateGroupWorkRequest.getChatId()
                , operateGroupWorkRequest.getGroupWork());
        if (!resultDO.getSuccess()) {
            wsEvent.setSuccess(false);
            wsEvent.setErrorMessage(resultDO.getResultMessage());
            webSocketFactory.pushMessageBySession(session, wsEvent, false);
            return;
        }
        // 推送数据（事件结果推送+数据更新推送）
        wsEvent.setSuccess(true);
        webSocketFactory.pushMessageBySession(session, wsEvent, false);
    }
}
