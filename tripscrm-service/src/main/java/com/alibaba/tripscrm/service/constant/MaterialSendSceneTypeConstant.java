package com.alibaba.tripscrm.service.constant;

/**
 * @DECLARATION: THE AUTHORITY OF CODE BELONGS TO ENTERPRISE TECHNOLOGY GROUP OF FEIZHU
 * @NOTE: 素材归因里面的消息发送场景
 * @AUTHOR: benjamin.zhm
 * @DATE: 2024/1/24
 **/
public class MaterialSendSceneTypeConstant {
    /**
     * 个人活码加好友欢迎语
     */
    public static final String GEMA_HYY = "gema_hyy";

    /**
     * 私聊任务
     */
    public static final String SILIAO_RW = "siliao_rw";

    /**
     * 私聊群发任务
     */
    public static final String SILIAO_GROUP_MSG_RW = "siliao_group_msg_rw";

    /**
     * 规则私聊任务
     */
    public static final String SILIAO_RW_FOR_RULE = "siliao_rw_for_rule";
    /**
     * 群聊任务
     */
    public static final String QUNLIAO_RW = "QUNLIAO_RW";
    /**
     * 聚合聊天私聊消息
     */
    public static final String JUHE_SILIAO = "juhe_siliao";
    /**
     * 群活码入群欢迎语
     */
    public static final String QUNMA_HYY = "qunma_hyy";
    /**
     * 聚合聊天群聊消息
     */
    public static final String JUHE_QUNLIAO = "juhe_qunliao";
    /**
     * 群公告
     */
    public static final String QUNGG = "qungg";
    /**
     * 朋友圈
     */
    public static final String PENGYQ = "pengyq";

    /**
     * 朋友圈评论
     */
    public static final String PENGYQ_PINGL = "pengyq_pingl";
    /**
     * 自动回复私聊
     */
    public static final String AUTO_RESPONSE_SILIAO = "auto_response_siliao";
    /**
     * 自动回复群聊
     */
    public static final String AUTO_RESPONSE_QUNLIAO = "auto_response_qunliao";
    /**
     * 智能回复私聊
     */
    public static final String SMART_RESPONSE_SILIAO = "smart_response_siliao";
    /**
     * 商品推广
     */
    public static final String GOODS_PROMOTION = "goods_promotion";

    /**
     * 转发消息
     */
    public static final String FORWARD_MSG = "forward_msg";

    /**
     * 支付宝群发消息
     */
    public static final String ALIPAY_GROUP_MSG = "alipay_group_msg";

    /**
     * 支付宝定向消息
     */
    public static final String ALIPAY_DIRECT_MSG = "alipay_direct_msg";

    /**
     * 小程序订阅消息
     */
    public static final String MINI_PROGRAM_SUBSCRIBE_MSG = "subscribe_msg";
}
