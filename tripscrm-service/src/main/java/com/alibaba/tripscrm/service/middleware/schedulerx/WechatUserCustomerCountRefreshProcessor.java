package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JobProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.taobao.unifiedsession.core.commons.limiter.RateLimiter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 企业微信成员-客户数刷新定时任务
 *
 * <AUTHOR>
 * @since 2024/8/8 19:43
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatUserCustomerCountRefreshProcessor implements JobProcessor {
    private final LdbTairManager ldbTairManager;
    private final WechatUserService wechatUserService;
    private final WechatCustomerService wechatCustomerService;

    private RateLimiter rateLimiter;

    @PostConstruct
    public void init() {
        rateLimiter = RateLimiter.create(4);
    }

    @Override
    public ProcessResult process(JobContext jobContext) throws Exception {
        Long dayVersion = DateUtils.getDayVersion(new Date());
        List<WechatUserDTO> wechatUserList = wechatUserService.listAll();
        Map<String, List<WechatUserDTO>> corpId2WechatUserList = wechatUserList.stream().collect(Collectors.groupingBy(WechatUserDTO::getCorpId));
        for (String corpId : corpId2WechatUserList.keySet()) {
            syncCustomerCount(corpId, corpId2WechatUserList, dayVersion);
        }

        return new ProcessResult(true);
    }

    private void syncCustomerCount(String corpId, Map<String, List<WechatUserDTO>> corpId2WechatUserList, Long dayVersion) {
        try {
            SpaceInfoThreadLocalUtils.setCorpId(corpId);
            for (WechatUserDTO wechatUser : corpId2WechatUserList.get(corpId)) {
                // 限流
                rateLimiter.acquire();
                Long customerCount = wechatCustomerService.getCustomerCount(wechatUser.getUserId(), true);
                String key = TairConstant.WECHAT_USER_CUSTOMER_COUNT_PREFIX + dayVersion + "_" + corpId + "_" + wechatUser.getUserId();
                ldbTairManager.put(key, customerCount, 2 * 60 * 60);
                Long customerCountFriend = wechatCustomerService.getCustomerCount(wechatUser.getUserId(), false);
                key = TairConstant.WECHAT_USER_CUSTOMER_COUNT_PREFIX + dayVersion + "_" + corpId + "_" + wechatUser.getUserId() + "_" + CustomerRelationStatusEnum.FRIEND.getDesc();
                ldbTairManager.put(key, customerCountFriend, 2 * 60 * 60);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("企业微信成员-客户数刷新定时任务执行异常", e.getMessage(), e, LogListUtil.newArrayList());
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }
}
