package com.alibaba.tripscrm.service.model.domain.request.knowledge;

import com.alibaba.tripscrm.service.model.dto.knowledge.KnowledgeDocumentDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 知识文档同步fai
 * @date 2025/9/18
 */
@Data
public class DocumentSyncFaiReq {

    private Long id;

    // 是否同步fai true同步，调用fai创建文档 false不同步，调用fai删除文档
    private Boolean syncFai;

    private String userId;

    private String userName;

    private KnowledgeDocumentDTO knowledgeDocument;
}
