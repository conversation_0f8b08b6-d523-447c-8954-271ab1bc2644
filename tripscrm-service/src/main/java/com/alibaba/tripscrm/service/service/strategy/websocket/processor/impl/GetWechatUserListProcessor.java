package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.service.fusionchat.FusionChatService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.GetWechatUserListResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.ManagerWechatUser;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 获取当前平台账号拥有权限的企微成员列表 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class GetWechatUserListProcessor implements WsEventProcessor {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private FusionChatService fusionChatService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.GET_WECHAT_USER_LIST;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        User account = wsContextInfo.getAccount();
        Long spaceId = wsContextInfo.getSpaceId();
        // 获取当前平台账号拥有权限的企微号列表
        List<ManagerWechatUser> managerUsers = fusionChatService.listManagerUsers(account.getUserId(), spaceId);
        GetWechatUserListResponse response = new GetWechatUserListResponse();
        response.setWechatUsers(managerUsers);
        // 推送到websocket
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        webSocketFactory.pushMessageBySession(session, wsEvent, false);
    }

    public void pushMessageBySession(WebSocketSession session, String accountId, Long spaceId) {
        List<ManagerWechatUser> managerUsers = fusionChatService.listManagerUsers(accountId, spaceId);
        // 仅负责推送response
        GetWechatUserListResponse response = new GetWechatUserListResponse();
        response.setWechatUsers(managerUsers);
        // 推送到websocket
        WsEvent wsEvent = new WsEvent();
        wsEvent.setType(WsEventTypeEnum.GET_WECHAT_USER_LIST.getValue());
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        webSocketFactory.pushMessageBySession(session, wsEvent, true);
    }

    public void pushMessageByDistributed(List<String> accountIds, Long spaceId) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        // 仅负责推送response
        for (String accountId : accountIds) {
            List<ManagerWechatUser> managerUsers = fusionChatService.listManagerUsers(accountId, spaceId);
            GetWechatUserListResponse response = new GetWechatUserListResponse();
            response.setWechatUsers(managerUsers);
            // 推送到websocket
            WsEvent wsEvent = new WsEvent();
            wsEvent.setType(WsEventTypeEnum.GET_WECHAT_USER_LIST.getValue());
            wsEvent.setData((JSONObject) JSONObject.toJSON(response));
            webSocketFactory.pushMessageByDistributedWithAccount(wsEvent, Collections.singletonList(accountId));
        }
    }
}
