package com.alibaba.tripscrm.service.service.strategy.callback;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * proxy回调路由分发
 *
 * <AUTHOR>
 * @date 2023/9/28
 */
@Service
@Slf4j
public class ScrmProxyCallbackRouter {
    @Resource
    private Map<String, ProxyCallbackProcessor> proxyCallbackProcessors;
    private final Map<Integer, ProxyCallbackProcessor> proxyCallbackProcessorMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (Map.Entry<String, ProxyCallbackProcessor> entry : proxyCallbackProcessors.entrySet()) {
            proxyCallbackProcessorMap.put(entry.getValue().type().getCode(), entry.getValue());
        }
    }

    /**
     * 处理proxy回调的消息
     */
    public Boolean routeProcessor(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (scrmCallbackMsg == null || scrmCallbackMsg.getEventType() == null) {
                PlatformLogUtil.logFail("处理proxy回调的消息异常", null,  LogListUtil.newArrayList(scrmCallbackMsg));
                return true;
            }

            ProxyCallbackProcessor processor = proxyCallbackProcessorMap.get(scrmCallbackMsg.getEventType().getCode());
            if (processor == null) {
                PlatformLogUtil.logFail("处理proxy回调的消息，没有对应处理器", LogListUtil.newArrayList(scrmCallbackMsg.getEventType().getCode()));
                return true;
            }
            // 初始化企微组织上下文
            if (Objects.nonNull(scrmCallbackMsg.getPlatformCorpId())) {
                SpaceInfoThreadLocalUtils.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            }
            return processor.handle(scrmCallbackMsg);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return false;
        } finally {
            // 移除企微组织上下文
            SpaceInfoThreadLocalUtils.remove();
        }
    }
}
