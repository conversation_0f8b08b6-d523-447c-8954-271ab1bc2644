package com.alibaba.tripscrm.service.middleware.metaq.consumer.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.convert.TaskConverter;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.dto.task.TaskSingleDataExecuteRetryInfoDTO;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.fliggy.pokemon.lock.client.api.TairLockManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * topic: task_single_data_execute_retry
 * consumerId: CID_task_single_data_execute_retry
 *
 * <AUTHOR>
 * @since 2024/6/6 10:46
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TaskSingleDataExecuteRetryConsumer implements MessageListenerConcurrently {
    private final TaskExecuteRecordService taskExecuteRecordService;
    private final TairLockManager tairLockManager;
    private final TaskExecuteStrategy taskExecuteStrategy;


    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            String receivedMsg = new String(msg.getBody());
            int reconsumeTimes = msg.getReconsumeTimes();
            MetaQDeleyLevel delayLevel = dealWithMessage(receivedMsg, reconsumeTimes);
            if (Objects.isNull(delayLevel)) {
                PlatformLogUtil.logInfo("接收到任务执行-单条数据重试消息，重试结束", LogListUtil.newArrayList(msg));
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }

            PlatformLogUtil.logInfo("处理任务执行-单条数据重试消息执行失败，继续重试", LogListUtil.newArrayList(msg));
            context.setDelayLevelWhenNextConsume(delayLevel.getLevel());
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private MetaQDeleyLevel dealWithMessage(String message, Integer reconsumeTimes) {
        try {
            // 解析数据
            if (!StringUtils.hasLength(message)) {
                PlatformLogUtil.logFail("接收到任务执行-单条数据重试消息，消息解析为空");
                return null;
            }

            PlatformLogUtil.logInfo("接收到任务执行-单条数据重试消息，消息解析后结果", LogListUtil.newArrayList(message));
            TaskSingleDataExecuteRetryInfoDTO content = JSONObject.parseObject(message, new TypeReference<TaskSingleDataExecuteRetryInfoDTO>() {
            });
            if (Objects.isNull(content) || !NumberUtils.validLong(content.getRecordId())) {
                PlatformLogUtil.logFail("接收到任务执行-单条数据重试消息，消息解析内容非法", LogListUtil.newArrayList(content));
                return null;
            }

            MetaQDeleyLevel[] delayLevels = new MetaQDeleyLevel[1];
            tairLockManager.tryRunWithLock(TairConstant.TASK_SINGLE_DATA_EXECUTE_LOCK_PREFIX + content.getRecordId(),
                    3000L,
                    () -> delayLevels[0] = retry(content, reconsumeTimes),
                    () -> PlatformLogUtil.logFail("接收到任务执行-单条数据重试消息，获取分布式锁失败", LogListUtil.newArrayList(content))
            );

            return delayLevels[0];
        } catch (Exception e) {
            PlatformLogUtil.logException("接收到任务执行-单条数据重试消息，执行出错", e.getMessage(), e, LogListUtil.newArrayList(message));
            return null;
        }
    }

    private MetaQDeleyLevel retry(TaskSingleDataExecuteRetryInfoDTO content, Integer reconsumeTimes) {
        TaskExecuteRecordBO taskExecuteRecordBO = taskExecuteRecordService.queryByRecordId(content.getRecordId());
        if (Objects.isNull(taskExecuteRecordBO)) {
            PlatformLogUtil.logFail("接收到任务执行-单条数据重试消息，根据recordId查询执行记录数据为空", LogListUtil.newArrayList(content));
            return null;
        }

        if (!Objects.equals(TaskSingleDataExecuteResultEnum.WAIT_RETRY, taskExecuteRecordBO.getStatusEnum())) {
            PlatformLogUtil.logFail("接收到任务执行-单条数据重试消息，该数据已无法重试", LogListUtil.newArrayList(content, taskExecuteRecordBO));
            return null;
        }

        TaskExecuteContext context = TaskConverter.record2Context(taskExecuteRecordBO);
        context.getTaskDataVO().getData().get(0).setRetryCount(reconsumeTimes);
        TripSCRMResult<String> result = taskExecuteStrategy.retryRecord(context);
        TripSCRMErrorCode errorCode = TripSCRMErrorCode.codeOf(result.getCode());
        if (!Objects.equals(TripSCRMErrorCode.FAIL_AND_RETRY, errorCode)) {
            PlatformLogUtil.logInfo("接收到任务执行-单条数据重试消息，执行完后无需再重试", LogListUtil.newArrayList(content, result));
            return null;
        }

        PlatformLogUtil.logInfo("接收到任务执行-单条数据重试消息，执行完后仍需再次重试", LogListUtil.newArrayList(content, result, context.getTaskDataVO().getData().get(0).getNextRetryDelayLevel()));
        return context.getTaskDataVO().getData().get(0).getNextRetryDelayLevel();
    }
}
