package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2024/1/15 17:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatInfoQuery extends BasePageRequest {

    /**
     * 企微用户id
     */
    private String userId;

    /**
     * 群成员idList
     */
    private List<String> userIdList;

    /**
     * 组织id
     */
    private String corpId;

    /**
     * 群聊chatIdList
     */
    private List<String> chatIdList;

    /**
     * 群聊名称
     */
    private String name;

    /**
     * 退群状态
     */
    private GroupRelationStatusEnum statusEnum;

    /**
     * 分组求count字段
     */
    private List<String> countFields;

    /**
     * 平台类型，微信：1，支付宝：4
     */
    private Integer platformType = 1;
}
