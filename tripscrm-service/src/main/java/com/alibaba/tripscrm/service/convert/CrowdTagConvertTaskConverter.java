package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.CrowdTagConvertTaskDO;
import com.alibaba.tripscrm.service.enums.task.CrowdTagConvertExecuteTypeEnum;
import com.alibaba.tripscrm.service.enums.task.CrowdTagConvertTaskStatusEnum;
import com.alibaba.tripscrm.service.model.dto.crowd.CrowdTagConvertTaskDTO;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 人群标签转换任务数据转换器
 *
 * <AUTHOR>
 * @create 2025/10/11 17:40
 */
@Component
public class CrowdTagConvertTaskConverter {

    /**
     * DO转DTO
     *
     * @param crowdTagConvertTaskDO
     * @return
     */
    public CrowdTagConvertTaskDTO convert2DTO(CrowdTagConvertTaskDO crowdTagConvertTaskDO) {
        if (Objects.isNull(crowdTagConvertTaskDO)) {
            return null;
        }

        CrowdTagConvertTaskDTO dto = new CrowdTagConvertTaskDTO();
        dto.setId(crowdTagConvertTaskDO.getId());
        dto.setGmtCreate(crowdTagConvertTaskDO.getGmtCreate());
        dto.setGmtModified(crowdTagConvertTaskDO.getGmtModified());
        dto.setSourcePlatform(PlatformTypeEnum.valueOf(crowdTagConvertTaskDO.getSourcePlatform()));
        dto.setTargetPlatform(PlatformTypeEnum.valueOf(crowdTagConvertTaskDO.getTargetPlatform()));
        dto.setTargetCrowdId(crowdTagConvertTaskDO.getTargetCrowdId());
        dto.setTargetCrowdName(crowdTagConvertTaskDO.getTargetCrowdName());
        dto.setSourceCrowdId(crowdTagConvertTaskDO.getSourceCrowdId());
        dto.setSourceCrowdName(crowdTagConvertTaskDO.getSourceCrowdName());
        dto.setFirstLevelTag(crowdTagConvertTaskDO.getFirstLevelTag());
        dto.setSecondLevelTag(crowdTagConvertTaskDO.getSecondLevelTag());
        dto.setTotalCount(crowdTagConvertTaskDO.getTotalCount());
        dto.setConvertCount(crowdTagConvertTaskDO.getConvertCount());
        dto.setStatus(CrowdTagConvertTaskStatusEnum.getByStatus(crowdTagConvertTaskDO.getStatus()));
        dto.setLastExecuteTime(crowdTagConvertTaskDO.getLastExecuteTime());
        dto.setExecuteType(CrowdTagConvertExecuteTypeEnum.getByCode(crowdTagConvertTaskDO.getExecuteType()));
        dto.setStartDate(crowdTagConvertTaskDO.getStartDate());
        dto.setEndDate(crowdTagConvertTaskDO.getEndDate());
        dto.setOperatorId(crowdTagConvertTaskDO.getOperatorId());

        return dto;
    }

    /**
     * DTO转DO
     *
     * @param crowdTagConvertTaskDTO
     * @return
     */
    public CrowdTagConvertTaskDO convert2DO(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO) {
        if (Objects.isNull(crowdTagConvertTaskDTO)) {
            return null;
        }

        CrowdTagConvertTaskDO crowdTagConvertTaskDO = new CrowdTagConvertTaskDO();
        crowdTagConvertTaskDO.setId(crowdTagConvertTaskDTO.getId());
        crowdTagConvertTaskDO.setGmtCreate(crowdTagConvertTaskDTO.getGmtCreate());
        crowdTagConvertTaskDO.setGmtModified(crowdTagConvertTaskDTO.getGmtModified());
        if(!Objects.isNull(crowdTagConvertTaskDTO.getSourcePlatform())) {
            crowdTagConvertTaskDO.setSourcePlatform(crowdTagConvertTaskDTO.getSourcePlatform().getCode().byteValue());
        }
        if(!Objects.isNull(crowdTagConvertTaskDTO.getTargetPlatform())) {
            crowdTagConvertTaskDO.setTargetPlatform(crowdTagConvertTaskDTO.getTargetPlatform().getCode().byteValue());
        }
        crowdTagConvertTaskDO.setTargetCrowdId(crowdTagConvertTaskDTO.getTargetCrowdId());
        crowdTagConvertTaskDO.setTargetCrowdName(crowdTagConvertTaskDTO.getTargetCrowdName());
        crowdTagConvertTaskDO.setSourceCrowdId(crowdTagConvertTaskDTO.getSourceCrowdId());
        crowdTagConvertTaskDO.setSourceCrowdName(crowdTagConvertTaskDTO.getSourceCrowdName());
        crowdTagConvertTaskDO.setFirstLevelTag(crowdTagConvertTaskDTO.getFirstLevelTag());
        crowdTagConvertTaskDO.setSecondLevelTag(Objects.isNull(crowdTagConvertTaskDTO.getSecondLevelTag()) ? "" : crowdTagConvertTaskDTO.getSecondLevelTag());
        crowdTagConvertTaskDO.setTotalCount(crowdTagConvertTaskDTO.getTotalCount());
        crowdTagConvertTaskDO.setConvertCount(crowdTagConvertTaskDTO.getConvertCount());
        crowdTagConvertTaskDO.setStatus(crowdTagConvertTaskDTO.getStatus().getCode());
        crowdTagConvertTaskDO.setLastExecuteTime(crowdTagConvertTaskDTO.getLastExecuteTime());
        if(!Objects.isNull(crowdTagConvertTaskDTO.getExecuteType())) {
            crowdTagConvertTaskDO.setExecuteType(crowdTagConvertTaskDTO.getExecuteType().getCode());
        }
        crowdTagConvertTaskDO.setStartDate(crowdTagConvertTaskDTO.getStartDate());
        crowdTagConvertTaskDO.setEndDate(crowdTagConvertTaskDTO.getEndDate());
        crowdTagConvertTaskDO.setOperatorId(crowdTagConvertTaskDTO.getOperatorId());

        return crowdTagConvertTaskDO;
    }

    /**
     * DO列表转DTO列表
     *
     * @param crowdTagConvertTaskDOList
     * @return
     */
    public List<CrowdTagConvertTaskDTO> convert2DTOs(List<CrowdTagConvertTaskDO> crowdTagConvertTaskDOList) {
        if (CollectionUtils.isEmpty(crowdTagConvertTaskDOList)) {
            return Collections.emptyList();
        }

        return crowdTagConvertTaskDOList.stream()
                .map(this::convert2DTO)
                .collect(Collectors.toList());
    }

    /**
     * DTO列表转DO列表
     *
     * @param crowdTagConvertTaskDTOList
     * @return
     */
    public List<CrowdTagConvertTaskDO> convert2DOs(List<CrowdTagConvertTaskDTO> crowdTagConvertTaskDTOList) {
        if (CollectionUtils.isEmpty(crowdTagConvertTaskDTOList)) {
            return Collections.emptyList();
        }

        return crowdTagConvertTaskDTOList.stream()
                .map(this::convert2DO)
                .collect(Collectors.toList());
    }
}