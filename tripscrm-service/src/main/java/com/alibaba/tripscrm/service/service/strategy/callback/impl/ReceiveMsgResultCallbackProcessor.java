package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.util.sms.TripTouchApi;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.fliggy.pokemon.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/8/6 16:56
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ReceiveMsgResultCallbackProcessor implements ProxyCallbackProcessor {
    private final TripTouchApi tripTouchApi;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.RECEIVE_MSG_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject jsonContent = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String cellPhone = jsonContent.getString(CallbackConstant.CELL_PHONE);
        String content = jsonContent.getString(CallbackConstant.CONTENT);
        PlatformLogUtil.logInfo("接收到退订短信结果回调", LogListUtil.newArrayList(cellPhone, content));
        //判断短息内容是否包含退订,R
        if (content.contains("退订") || content.contains("R")) {
            String sendTime = DateUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            return tripTouchApi.sendSmsUp(cellPhone, content, sendTime);
        }
        return true;
    }
}
