package com.alibaba.tripscrm.service.service.task.ability.old;

import com.alibaba.tripscrm.domain.enums.TaskType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 任务工厂，获取不同任务的实现
 * <AUTHOR>
 * @date 2023/5/25
 */
@Slf4j
@Component
public class TaskFactory implements ApplicationContextAware {

    private static ApplicationContext ac;

    public static Map<String, AbstractTaskTemplate> taskTemplateMap = new HashMap<>();

    @PostConstruct
    private void init(){
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ac = applicationContext;
    }

    /**
     * 初始化任务类工厂
     * @param taskType
     * @param clazz
     * @param <T>
     */
    public <T> void setTaskTemplateMap(TaskType taskType, Class<T> clazz) {
        T task = ac.getBean(clazz);
        taskTemplateMap.put(taskType.getCode(), (AbstractTaskTemplate)task);
    }

    /**
     * 工厂获取任务实现类bean
     * @param taskType
     * @return
     */
    public static AbstractTaskTemplate getTaskBeanByType(TaskType taskType){
        return taskTemplateMap.get(taskType.getCode());
    }

}
