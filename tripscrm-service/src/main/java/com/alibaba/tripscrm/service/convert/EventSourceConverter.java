package com.alibaba.tripscrm.service.convert;

import com.alibaba.tripscrm.dal.model.domain.data.EventSourceDO;
import com.alibaba.tripscrm.service.model.vo.task.EventSourceVO;
import org.springframework.beans.BeanUtils;

/**
 * EventSource数据防腐层
 */
public class EventSourceConverter {
    public static EventSourceVO do2Vo(EventSourceDO eventSourceDO) {
        EventSourceVO vo = new EventSourceVO();
        BeanUtils.copyProperties(eventSourceDO, vo);
        return vo;
    }

    public static EventSourceDO vo2Do(EventSourceVO eventSourceVO) {
        EventSourceDO doObj = new EventSourceDO();
        BeanUtils.copyProperties(eventSourceVO, doObj);
        return doObj;
    }
}
