package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.WechatTransferCustomerResultDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.wechat.WechatTransferCustomerStatusEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.WechatTransferCustomerResultQuery;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.wechat.WechatTransferCustomerResultService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.alibaba.tripzoo.proxy.request.WechatTransferCustomerRequest;
import com.alibaba.tripzoo.proxy.result.WechatTransferCustomerResponse;
import com.google.common.collect.Lists;
import com.taobao.csp.courier.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
public class TransferCustomerTaskExecutor extends AbstractTaskExecutor {
    @Autowired
    protected EnterpriseWechatManager enterpriseWechatManager;
    @Autowired
    protected WechatTransferCustomerResultService wechatTransferCustomerResultService;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        // 原添加的企微成员
        String handoverUserId = getHandoverUserId(context, todoTaskVO);
        // 接替的企微成员
        String takeoverUserId = getFinalTargetId(context, taskDataBody);
        // 接替的客户列表
        List<String> externalUserIdList = getExternalUserIdList(todoTaskVO);

        PlatformLogUtil.logInfo("在职继承开始执行任务，任务ID：" + context.getTaskId(), todoTaskVO, externalUserIdList);

        Boolean isResigned = Objects.equals(TaskType.RESIGNED_TRANSFER_CUSTOMER, getTaskType());
        WechatTransferCustomerRequest wechatTransferCustomerRequest = new WechatTransferCustomerRequest();
        wechatTransferCustomerRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        wechatTransferCustomerRequest.setIsResigned(isResigned);
        wechatTransferCustomerRequest.setHandoverUserId(handoverUserId);
        wechatTransferCustomerRequest.setTakeoverUserId(takeoverUserId);
        wechatTransferCustomerRequest.setExternalUserIds(externalUserIdList);
        wechatTransferCustomerRequest.setTransferSuccessMsg(getTransferSuccessMsg(context));

        // 初始化数据
        List<WechatTransferCustomerResultDO> wechatTransferCustomerResultDOS = buildWechatTransferCustomerResultDOList(handoverUserId, takeoverUserId, externalUserIdList, todoTaskVO.getTaskId());
        for (WechatTransferCustomerResultDO wechatTransferCustomerResultDO : wechatTransferCustomerResultDOS) {
            wechatTransferCustomerResultService.insertSelective(wechatTransferCustomerResultDO);
        }

        // 好友继承
        TripSCRMResult<WechatTransferCustomerResponse> result = enterpriseWechatManager.transferCustomer(wechatTransferCustomerRequest);
        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

//         更新继承结果
        Map<String, WechatTransferCustomerResultDO> externalUserId2Result = wechatTransferCustomerResultDOS.stream().collect(Collectors.toMap(WechatTransferCustomerResultDO::getExternalUserId, wechatTransferCustomerResultDO -> wechatTransferCustomerResultDO));
        List<WechatTransferCustomerResponse.CustomerResult> customerResultList = result.getData().getCustomers();
        for (WechatTransferCustomerResponse.CustomerResult customerResult : customerResultList) {
            if (!externalUserId2Result.containsKey(customerResult.getExternalUserId())) {
                continue;
            }

            if (Objects.equals(0, customerResult.getErrCode())) {
                continue;
            }

            WechatTransferCustomerResultDO wechatTransferCustomerResultDO = externalUserId2Result.get(customerResult.getExternalUserId());

            WechatTransferCustomerResultQuery condition = new WechatTransferCustomerResultQuery();
            condition.setTaskId(context.getTaskId());
            condition.setExternalUserId(wechatTransferCustomerResultDO.getExternalUserId());
            condition.setTakeoverUserId(wechatTransferCustomerResultDO.getTakeoverUserId());
            condition.setHandoverUserId(wechatTransferCustomerResultDO.getHandoverUserId());
            condition.setStatusList(Lists.newArrayList(WechatTransferCustomerStatusEnum.INIT));
            WechatTransferCustomerResultDO newWechatTransferCustomerResultDO = new WechatTransferCustomerResultDO();
            newWechatTransferCustomerResultDO.setStatus(WechatTransferCustomerStatusEnum.OTHER.getCode().byteValue());
            wechatTransferCustomerResultService.updateSelective(newWechatTransferCustomerResultDO, condition);
        }
    }

    protected List<WechatTransferCustomerResultDO> buildWechatTransferCustomerResultDOList(String handoverUserId, String takeoverUserId, List<String> externalUserIdList, Long taskId) {
        List<WechatTransferCustomerResultDO> result = Lists.newArrayList();
        for (String externalUserId : externalUserIdList) {
            WechatTransferCustomerResultDO wechatTransferCustomerResultDO = new WechatTransferCustomerResultDO();
            wechatTransferCustomerResultDO.setTaskId(taskId);
            wechatTransferCustomerResultDO.setStatus(WechatTransferCustomerStatusEnum.INIT.getCode().byteValue());
            wechatTransferCustomerResultDO.setExternalUserId(externalUserId);
            wechatTransferCustomerResultDO.setHandoverUserId(handoverUserId);
            wechatTransferCustomerResultDO.setTakeoverUserId(takeoverUserId);
            result.add(wechatTransferCustomerResultDO);
        }

        return result;
    }

    protected String getTransferSuccessMsg(TaskExecuteContext context) {
        String taskExtInfo = context.getTaskInfoDOSnapshot().getExtInfo();
        if (!StringUtils.hasText(taskExtInfo)) {
            return null;
        }

        JSONObject taskExtInfoJson = JSONObject.parseObject(taskExtInfo);
        return MapUtils.getString(taskExtInfoJson, TaskConstant.TRANSFER_SUCCESS_MSG, null);
    }

    /**
     * 接替的客户列表
     */
    protected List<String> getExternalUserIdList(TodoTaskVO todoTaskVO) {
        JSONObject extraInfo = JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo());
        List<String> externalUserIdList = extraInfo.getObject("externalUserIdList", new TypeReference<List<String>>() {
        });

        if (CollectionUtils.isEmpty(externalUserIdList)) {
            throw new TripscrmException(TripSCRMErrorCode.NOT_FOUND_TASK_EXECUTE_DATA);
        }

        // 微信接口一次只能处理100条
        if (externalUserIdList.size() > 100) {
            throw new TripscrmException(TripSCRMErrorCode.TOO_MANY_TASK_EXECUTE_DATA);
        }

        return externalUserIdList;
    }

    /**
     * 接替的企微成员
     */
    protected String getHandoverUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        JSONObject extraInfo = JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo());
        return extraInfo.getObject("handoverUserId", new TypeReference<String>() {
        });
    }

    /**
     * 原来的企微成员
     */
    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return getHandoverUserId(context, todoTaskVO);
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_USER_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        if (!StringUtils.hasText(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("目标Id为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        if (!Objects.equals(taskDataBody.getTargetType(), ActivityTargetTypeEnum.WX_USER_ID.getCode())) {
            PlatformLogUtil.logFail("只能处理WX_USER_ID类型", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        return taskDataBody.getTargetId();
    }

    @Override
    protected String getUniqueKey(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        JSONObject extraInfo = JSONObject.parseObject(todoTaskVO.getData().get(0).getExtInfo());
        if (Objects.isNull(extraInfo) || !extraInfo.containsKey("uuid")) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        String uuid = extraInfo.getObject("uuid", new TypeReference<String>() {
        });

        return TairConstant.LDB_TASK_FINISH_TARGET_DATA_PREFIX + context.getInstanceId() + "_" + todoTaskVO.getData().get(0).getTargetId() + "_" + uuid;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.TRANSFER_CUSTOMER;
    }
}
