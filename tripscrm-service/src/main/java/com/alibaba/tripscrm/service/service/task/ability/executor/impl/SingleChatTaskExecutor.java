package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.domain.task.TaskAsyncExecuteResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TaskSingleDataExecuteResultEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.manager.wechat.EnterpriseWechatManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.bo.TaskExecuteRecordBO;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.request.SendMsgToCustomerRequest;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.service.task.base.TaskExecuteRecordService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import com.taobao.unifiedsession.core.commons.limiter.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
public class SingleChatTaskExecutor extends AbstractTaskExecutor {
    @Autowired
    protected EnterpriseWechatManager enterpriseWechatManager;
    @Autowired
    protected WechatUserService wechatUserService;
    @Autowired
    protected WechatCustomerService wechatCustomerService;
    @Autowired
    protected UicUtils uicUtils;
    @Autowired
    protected TaskExecuteRecordService taskExecuteRecordService;
    @Autowired
    protected LdbTairManager ldbTairManager;
    @Autowired
    protected MetaqProducer metaqProducer;
    @Autowired
    protected IsvRouteService isvRouteService;

    @Switch(description = "每分钟最大消息触达数", name = "sendMessageCountPerMinute")
    public static Integer sendMessageCountPerMinute = 60;

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        List<TaskDataVO.DataBodyVO> taskList = todoTaskVO.getData();
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = taskList.get(0);
        String unionId = getFinalTargetId(context, taskDataBody);
        // 素材
        MaterailInfoDO materailInfoDO = getMaterialInfo(context, todoTaskVO);
        if (Objects.isNull(materailInfoDO)) {
            PlatformLogUtil.logFail("material get fail", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.NOT_EXIST_MATERIAL_IN_TASK);
        }

        String userId = getSendUserId(context, todoTaskVO);
        MaterialContentConvertContext materialContentConvertContext = buildMaterialContentConvertContext(context, todoTaskVO, userId, taskDataBody, materailInfoDO);
        MaterialTrackRelationDTO materialTrackRelationDTO = buildMaterialTrackRelationDTO(context, todoTaskVO, userId, unionId);
        SendMsgToCustomerRequest sendMsgToCustomerRequest = buildSendMsgToCustomerRequest(context, todoTaskVO, materailInfoDO, userId, materialTrackRelationDTO, materialContentConvertContext);
        TripSCRMResult<String> result = sendAsyncRequest(context, todoTaskVO, () -> enterpriseWechatManager.asyncSendMessageToCustomer(sendMsgToCustomerRequest));
        if (!result.isSuccess()) {
            throw new TripscrmException(Optional.of(TripSCRMErrorCode.valueOf(result.getCode())).orElse(TripSCRMErrorCode.PROCESS_FAILED), result.getMsg());
        }

        JSONObject data = new JSONObject();
        data.put("extraInfo", materialContentConvertContext.getExtraInfo());
        data.put("sendUserId", userId);
        data.put("materialId", materailInfoDO.getId());
        data.put("requestId", result.getData());
        todoTaskVO.setSuccess(true);
        context.setResult(data.toJSONString());
        taskDataBody.getContext().put("result", data);
    }

    private SendMsgToCustomerRequest buildSendMsgToCustomerRequest(TaskExecuteContext context, TodoTaskVO todoTaskVO, MaterailInfoDO materailInfoDO, String userId, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext materialContentConvertContext) {
        SendMsgToCustomerRequest sendMsgToCustomerRequest = new SendMsgToCustomerRequest();
        sendMsgToCustomerRequest.setUserId(userId);
        sendMsgToCustomerRequest.setExternalUserId(getExternalUserId(context, todoTaskVO));
        sendMsgToCustomerRequest.setMaterialContentConvertContext(materialContentConvertContext);
        sendMsgToCustomerRequest.setMainTaskInstanceId(todoTaskVO.getMainTaskInstanceId());
        sendMsgToCustomerRequest.setAbTestBucketId(getAbTestBucketId(todoTaskVO));
        sendMsgToCustomerRequest.setMaterialTrackRelationDTO(materialTrackRelationDTO);
        sendMsgToCustomerRequest.setMaterailInfoDO(materailInfoDO);
        sendMsgToCustomerRequest.setTaskType(getTaskType());

        // 指定requestId
        if (Objects.nonNull(context.getExtInfo()) && context.getExtInfo().containsKey("requestId")
                && Objects.nonNull(context.getExtInfo().get("requestId"))
                && context.getExtInfo().get("requestId") instanceof String) {
            sendMsgToCustomerRequest.setRequestId(context.getExtInfo().get("requestId").toString());
        }
        return sendMsgToCustomerRequest;
    }

    private MaterialTrackRelationDTO buildMaterialTrackRelationDTO(TaskExecuteContext context, TodoTaskVO todoTaskVO, String userId, String unionId) {
        MaterialTrackRelationDTO materialTrackRelationDTO = new MaterialTrackRelationDTO();
        materialTrackRelationDTO.setSceneType(getScene());
        materialTrackRelationDTO.setTaskId(todoTaskVO.getTaskId());
        materialTrackRelationDTO.setTaskInsId(NumberUtils.validLong(context.getMainTaskInstanceId()) ? context.getMainTaskInstanceId() : -1L);
        materialTrackRelationDTO.setAbBucketId(String.valueOf(getAbTestBucketId(todoTaskVO)));
        materialTrackRelationDTO.setMaterialId(getMaterialId(context, todoTaskVO));
        materialTrackRelationDTO.setWxUserId(userId);
        materialTrackRelationDTO.setSendUnionId(unionId);
        return materialTrackRelationDTO;
    }

    private MaterialContentConvertContext buildMaterialContentConvertContext(TaskExecuteContext context, TodoTaskVO todoTaskVO, String userId, TaskDataVO.DataBodyVO taskDataBody, MaterailInfoDO materailInfoDO) {
        MaterialContentConvertContext materialContentConvertContext = new MaterialContentConvertContext();
        materialContentConvertContext.setExternalUserId(getExternalUserId(context, todoTaskVO));
        materialContentConvertContext.setWechatUserId(userId);
        materialContentConvertContext.setExtraInfo(context.getExtInfo());
        if (StringUtils.isNotBlank(taskDataBody.getExtInfo())) {
            materialContentConvertContext.getExtraInfo().putAll(JSONObject.parseObject(taskDataBody.getExtInfo()));
        }
        materialContentConvertContext.setImageUrlList(context.getImageUrlList());
        materialContentConvertContext.setOriginContent(materailInfoDO.getContent());
        return materialContentConvertContext;
    }

    protected String getExternalUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("externalUserId")) {
            return (String) taskDataBody.getContext().get("externalUserId");
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (ActivityTargetTypeEnum.WX_EXTERNAL_USERID.equals(activityTargetType)) {
            return taskDataBody.getTargetId();
        }

        String externalUserId = wechatCustomerService.getExternalUserIdByUnionId(getFinalTargetId(context, taskDataBody));
        taskDataBody.getContext().put("externalUserId", externalUserId);
        return externalUserId;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        // 只处理第一条
        if (taskDataBody.getContext().containsKey("unionId")) {
            return (String) taskDataBody.getContext().get("unionId");
        }

        if (StringUtils.isBlank(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("empty targetId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        ActivityTargetTypeEnum activityTargetType = ActivityTargetTypeEnum.codeOf(taskDataBody.getTargetType());
        if (!Lists.newArrayList(ActivityTargetTypeEnum.WX_UNION_ID, ActivityTargetTypeEnum.WX_EXTERNAL_USERID, ActivityTargetTypeEnum.TAOBAO_USER_ID).contains(activityTargetType)) {
            PlatformLogUtil.logFail("only accept unionId/externalUserId/userId", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }

        String unionId = taskDataBody.getTargetId();
        // externalUserId 需要转 unionId
        if (ActivityTargetTypeEnum.WX_EXTERNAL_USERID.equals(activityTargetType)) {
            unionId = wechatCustomerService.getUnionIdByExternalUserId(taskDataBody.getTargetId(), SpaceInfoThreadLocalUtils.getCorpId());
            // 有些企业没有 unionId，只能降级处理了
            if (StringUtils.isBlank(unionId)) {
                taskDataBody.getContext().put("unionId", "");
                return "";
            }
        }

        // 淘宝userId 需要转 unionId
        if (ActivityTargetTypeEnum.TAOBAO_USER_ID.equals(activityTargetType)) {
            unionId = uicUtils.getUnionIdByUid(taskDataBody.getTargetId());
            if (StringUtils.isBlank(unionId)) {
                PlatformLogUtil.logFail("getUnionIdByUserId empty", LogListUtil.newArrayList(taskDataBody));
                throw new TripscrmException(TripSCRMErrorCode.UID_TO_UNION_ID_FAIL);
            }
        }

        if (StringUtils.isBlank(unionId)) {
            PlatformLogUtil.logFail("unionId is empty", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.EMPTY_UNION_ID);
        }

        taskDataBody.getContext().put("unionId", unionId);
        return unionId;
    }



    @Override
    public RateLimiter getRateLimiter(TaskExecuteContext context) {
        boolean isStreamTask = Lists.newArrayList(TaskTriggerTypeEnum.INTERFACE, TaskTriggerTypeEnum.EVENT).contains(context.getTriggerType());
        if (isStreamTask) {
            PlatformLogUtil.logFail("isStreamTask");
            return null;
        }

        double qps = sendMessageCountPerMinute / 60d;
        PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(qps));
        return RateLimiter.create(sendMessageCountPerMinute / 60d);
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        // 上下文缓存取
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            return (String) taskDataBody.getContext().get("sendUserId");
        }
        // 获取指定的发送者id
        String specifiedSendUserId = getSpecifiedSendUserId(context, taskDataBody);
        if (specifiedSendUserId != null) {
            taskDataBody.getContext().put("sendUserId", specifiedSendUserId);
            return specifiedSendUserId;
        }

        // 从任务配置的列表里面匹配
        JSONObject taskExtraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        String sendUserIdListStr = context.getTaskInfoDOSnapshot().getSendUserId();
        if (StringUtils.isBlank(sendUserIdListStr)) {
            sendUserIdListStr = ((String) taskExtraInfoJson.getOrDefault("robotUser", ""));
        }
        List<String> userIdList = Arrays.stream(Optional.ofNullable(sendUserIdListStr).orElse("").split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setRiskActionEnum(RiskActionEnum.CUSTOMER_SEND_MESSAGE);
        isvRouteContext.setExternalUserId(getExternalUserId(context, todoTaskVO));
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        isvRouteContext.setUserIdList(userIdList);
        TripSCRMResult<WechatUserDTO> result = isvRouteService.matchWechatUser(isvRouteContext);
        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData())) {
            PlatformLogUtil.logFail("私聊消息发送失败，根据服务商策略获取发送人失败", LogListUtil.newArrayList(userIdList, taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.ROBOT_USER_OF_TASK_OFFLINE_OR_WITHOUT_FRIENDSHIP_WITH_CUSTOMER);
        }
        String userId = result.getData().getUserId();
        taskDataBody.getContext().put("sendUserId", userId);
        return userId;
    }

    @Nullable
    private String getSpecifiedSendUserId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        String sendUserId;
        // 先取事件源或接口指定的发送者id
        Map<String, Object> extInfo = context.getExtInfo();
        if (extInfo != null && extInfo.containsKey("sendUserId")) {
            sendUserId = (String) extInfo.get("sendUserId");
            PlatformLogUtil.logFail("私聊消息发送过程中从上下文扩展获取sendUserId", LogListUtil.newArrayList(taskDataBody, sendUserId));
            checkSendUserOnline(taskDataBody, sendUserId);
            return sendUserId;
        }

        // 再取离线表指定的发送者id
        JSONObject extInfoJson = JSONObject.parseObject(taskDataBody.getExtInfo());
        if (extInfoJson.containsKey("sendUserId")) {
            sendUserId = extInfoJson.getString("sendUserId");
            PlatformLogUtil.logFail("私聊消息发送过程中从taskDataBody取sendUserId", LogListUtil.newArrayList(taskDataBody, sendUserId));
            checkSendUserOnline(taskDataBody, sendUserId);
            return sendUserId;
        }

        return null;
    }

    @Override
    public TripSCRMResult<Void> checkValid(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        String userId = getSendUserId(context, todoTaskVO);
        if (StringUtils.isBlank(userId)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.ROBOT_USER_OF_TASK_OFFLINE_OR_WITHOUT_FRIENDSHIP_WITH_CUSTOMER);
        }
        return TripSCRMResult.success(null);
    }

    private void checkSendUserOnline(TaskDataVO.DataBodyVO taskDataBody, String sendUserId) {
        // 判断指定的发送者id是否在线
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(sendUserId));
        if (CollectionUtils.isNotEmpty(wechatUserList) && Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
            return;
        }

        PlatformLogUtil.logFail("私聊消息发送过程中指定的发送者id不在线", LogListUtil.newArrayList(taskDataBody, sendUserId));
        throw new TripscrmException(TripSCRMErrorCode.WECHAT_USER_IS_NOT_ONLINE);
    }

    protected String getScene() {
        return MaterialSendSceneTypeConstant.SILIAO_RW;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.ROBOT_CHAT_MESSAGE;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.WX_UNION_ID;
    }

    @Override
    public boolean supportRetry(TripSCRMErrorCode errorCodeEnum) {
        return Lists.newArrayList(TripSCRMErrorCode.BIZ_RATE_LIMIT, TripSCRMErrorCode.BIZ_ACTION_DEGRADE_LIMIT, TripSCRMErrorCode.GATE_WAY_RATE_LIMIT, TripSCRMErrorCode.ISV_RATE_LIMIT).contains(errorCodeEnum);
    }

    @Override
    public void updateAsyncExecuteResult(Long recordId, ScrmCallbackMsg scrmCallbackMsg) {
        sendTaskAsyncExecuteResult(scrmCallbackMsg);

        TaskExecuteRecordBO taskExecuteRecordBO = taskExecuteRecordService.queryByRecordId(recordId);
        if (Objects.isNull(taskExecuteRecordBO)) {
            PlatformLogUtil.logFail("任务执行明细，更新私聊任务异步执行结果，查询任务执行明细为空", LogListUtil.newArrayList(recordId));
            return;
        }

        boolean firstCallback = Lists.newArrayList(TaskSingleDataExecuteResultEnum.SUCCESS).contains(taskExecuteRecordBO.getStatusEnum());

        // 第一个回调，作为异步执行结果
        if (firstCallback) {
            taskExecuteRecordBO.setStatusEnum(scrmCallbackMsg.getResult() ? TaskSingleDataExecuteResultEnum.ASYNC_SUCCESS : TaskSingleDataExecuteResultEnum.ASYNC_FAIL);
            PlatformLogUtil.logInfo("任务执行明细，更新私聊任务异步执行结果，首次更新异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        }

        JSONObject extraInfo = taskExecuteRecordBO.getExtraInfo();
        List<String> asyncExecuteResultList = extraInfo.containsKey("asyncExecuteResult") ? extraInfo.getObject("asyncExecuteResult", new TypeReference<List<String>>() {
        }) : new ArrayList<>();
        if (StringUtils.isNotBlank(scrmCallbackMsg.getContent())) {
            asyncExecuteResultList.add(scrmCallbackMsg.getContent());
            extraInfo.put("asyncExecuteResultList", asyncExecuteResultList);
        }
        if (StringUtils.isNotBlank(scrmCallbackMsg.getMessage())) {
            extraInfo.put("asyncExecuteResultMessage", scrmCallbackMsg.getMessage());
        }
        taskExecuteRecordBO.setExtraInfo(extraInfo);

        PlatformLogUtil.logInfo("任务执行明细，更新私聊任务异步执行结果", LogListUtil.newArrayList(recordId, taskExecuteRecordBO));
        if (firstCallback && NumberUtils.validLong(taskExecuteRecordBO.getSubTaskInstanceId())) {
            String key = scrmCallbackMsg.getResult() ? (TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_SUCCESS_COUNT_PREFIX + taskExecuteRecordBO.getSubTaskInstanceId()) : (TairConstant.SUB_TASK_INSTANCE_ASYNC_EXECUTE_FAIL_COUNT_PREFIX + taskExecuteRecordBO.getSubTaskInstanceId());
            ldbTairManager.incr(key, 1, 0, 86400);
        }
        taskExecuteRecordService.upsert(taskExecuteRecordBO);
    }

    private void sendTaskAsyncExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        // 发送异步执行结果
        TaskAsyncExecuteResult taskAsyncExecuteResult = new TaskAsyncExecuteResult();
        taskAsyncExecuteResult.setRequestId(scrmCallbackMsg.getRequestId());
        taskAsyncExecuteResult.setResult(scrmCallbackMsg.getResult());
        taskAsyncExecuteResult.setMessage(scrmCallbackMsg.getMessage());
        JSONObject extraInfo = new JSONObject();
        JSONObject jsonContent = JSONObject.parseObject(scrmCallbackMsg.getContent());
        extraInfo.put("msgNum", MapUtils.getInteger(jsonContent, "msgNum", null));
        taskAsyncExecuteResult.setExtraInfo(extraInfo);
        metaqProducer.send(MQEnum.TASK_ASYNC_EXECUTE_RESULT, null, scrmCallbackMsg.getPlatformCorpId(), JSONObject.toJSONString(taskAsyncExecuteResult));
    }
}
