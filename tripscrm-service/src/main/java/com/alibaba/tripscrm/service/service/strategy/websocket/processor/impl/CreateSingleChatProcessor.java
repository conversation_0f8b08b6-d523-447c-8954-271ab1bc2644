package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatCustomerDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatUserDO;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.dal.repository.WechatCustomerRepository;
import com.alibaba.tripscrm.dal.repository.WechatGroupRepository;
import com.alibaba.tripscrm.dal.repository.WechatUserRepository;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationCreateParam;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.CreateSingleChatRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.CreateSingleChatResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;

import java.util.List;

import static com.alibaba.tripscrm.service.service.impl.fusionchat.ChatConversationServiceImpl.DEFAULT_GROUP_AVATAR;


/**
 * 发起单聊 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class CreateSingleChatProcessor implements WsEventProcessor {
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private ChatConversationRepository chatConversationRepository;
    @Resource
    private WechatGroupRepository wechatGroupRepository;
    @Resource
    private WechatCustomerRepository wechatCustomerRepository;
    @Resource
    private WechatUserRepository wechatUserRepository;
    @Resource
    private WXCorpStorage wxCorpStorage;
    @Resource
    private WechatUserService wechatUserService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.CREATE_SINGLE_CHAT;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        CreateSingleChatRequest request = wsEvent.getData().toJavaObject(CreateSingleChatRequest.class);
        if (wsEvent.getUserId() == null || request.getChatId() == null || request.getChatType() == null) {
            throw new TripscrmException("缺少必要的参数 userId or chatId or chatType");
        }
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        boolean push = false;
        // 保存用户会话库
        ChatConversationDO chatConversation = chatConversationRepository.getByUniqueKeyWithNull(wsEvent.getUserId(), request.getChatId(), request.getChatType());
        if (chatConversation == null) {
            ChatConversationCreateParam createParam = new ChatConversationCreateParam();
            createParam.setUserId(wsEvent.getUserId());
            createParam.setChatId(request.getChatId());
            createParam.setChatType(request.getChatType());
            createParam.setCorpId(wsEvent.getCorpId());
            chatConversationService.createChatConversation(createParam);
            push = true;
        } else {
            // 若被关闭，则重新唤起
            if (chatConversation.getDeleted()) {
                chatConversation.setDeleted(false);
                chatConversationRepository.update(chatConversation);
                push = true;
            }
        }

        // 已经存在的前端会自己唤起，并不会走到该方法
        if (!push) {
            return;
        }

        CreateSingleChatResponse createSingleChatResponse = new CreateSingleChatResponse();
        createSingleChatResponse.setChatType(request.getChatType());
        createSingleChatResponse.setChatId(request.getChatId());
        createSingleChatResponse.setUpdateTimestamp(System.currentTimeMillis());
        createSingleChatResponse.setTopNo(chatConversation == null ? 0 : chatConversation.getTopNo());
        createSingleChatResponse.setGroupWork(true);
        switch (ChatTypeEnum.parse(request.getChatType())) {
            case GROUP:
                WechatGroupDO wechatGroup = wechatGroupRepository.getByChatIdAndCorpId(request.getChatId(), wsEvent.getCorpId());
                createSingleChatResponse.setChatName(wechatGroup.getName());
                createSingleChatResponse.setChatAvatar(DEFAULT_GROUP_AVATAR);
                break;
            case SINGLE_FOR_CUSTOMER:
                WechatCustomerDO wechatCustomer = wechatCustomerRepository.getByExternalUserIdAndCorpIdWithNull(request.getChatId(), wsEvent.getCorpId());
                if (wechatCustomer != null) {
                    createSingleChatResponse.setChatName(wechatCustomer.getName());
                    createSingleChatResponse.setChatAvatar(wechatCustomer.getAvatar());
                    createSingleChatResponse.setCorpName("微信");
                } else {
                    throw new TripscrmException("请先添加客户为好友");
                }
                break;
            case SINGLE_FOR_USER:
                WechatUserDO wechatUser = wechatUserRepository.getByUserIdAndCorpId(request.getChatId(), wsEvent.getCorpId());
                createSingleChatResponse.setChatName(wechatUser.getName());
                createSingleChatResponse.setChatAvatar(wechatUser.getAvatar());
                createSingleChatResponse.setCorpName(wxCorpStorage.getCorpName(wechatUser.getCorpId()));
                break;
            default:
                break;
        }
        wsEvent.setData((JSONObject) JSONObject.toJSON(createSingleChatResponse));

        Long spaceId = wsContextInfo.getSpaceId();
        // 寻找当前企微号有关系的account列表，推送到websocket
        List<String> accountIds = wechatUserService.listAccountIdWithPermission(wsEvent.getUserId(), spaceId);
        webSocketFactory.pushMessageByDistributedWithAccount(wsEvent, accountIds);
    }

    public void pushMessageByDistributed(String userId, String chatId, ChatTypeEnum chatType, String chatName, String chatAvatar, String corpName, Long timestamp) {
        WsEvent wsEvent = new WsEvent();
        wsEvent.setUserId(userId);
        wsEvent.setType(WsEventTypeEnum.CREATE_SINGLE_CHAT.getValue());
        CreateSingleChatResponse createSingleChatResponse = new CreateSingleChatResponse();
        createSingleChatResponse.setChatId(chatId);
        createSingleChatResponse.setChatType(chatType.getValue());
        createSingleChatResponse.setChatName(chatName);
        createSingleChatResponse.setChatAvatar(chatAvatar);
        createSingleChatResponse.setCorpName(corpName);
        createSingleChatResponse.setUpdateTimestamp(timestamp);
        createSingleChatResponse.setGroupWork(true);
        wsEvent.setData((JSONObject) JSONObject.toJSON(createSingleChatResponse));
        webSocketFactory.pushMessageByDistributed(wsEvent);
    }
}
