package com.alibaba.tripscrm.service.synchronizer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public abstract class BaseSynchronizer<T> {
    public SyncResult<T> sync(BaseSynchronizerContext<T> context) {
        try {
            process(context);
            return SyncResult.success();
        } catch (Exception e) {
            PlatformLogUtil.logException("同步基类，执行异常", e.getMessage(), e, LogListUtil.newArrayList(context));
            return SyncResult.fail(e.getMessage());
        }
    }

    /**
     * 具体的同步处理
     *
     * @param context 上下文
     */
    public abstract void process(BaseSynchronizerContext<T> context);
}
