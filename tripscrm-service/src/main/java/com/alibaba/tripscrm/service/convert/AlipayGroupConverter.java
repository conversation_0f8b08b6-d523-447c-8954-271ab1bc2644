package com.alibaba.tripscrm.service.convert;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.MemberInfoDTO;
import com.alibaba.tripscrm.domain.request.TripSCRMGroupTemplateSyncRequest;
import com.alibaba.tripscrm.service.enums.system.AlipayJoinSceneEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.dto.group.AlipayGroupExtDTO;
import com.alibaba.tripscrm.service.model.dto.group.AlipayGroupInstanceExtDTO;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupDTO;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupDetailDTO;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupInstanceInfoDTO;
import com.alibaba.tripzoo.proxy.model.alipay.group.GroupUserDTO;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.uic.common.domain.BasePaymentAccountDO;
import com.taobao.uic.common.domain.ResultDO;
import com.taobao.uic.common.service.userinfo.client.UicPaymentAccountReadServiceClient;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/18 15:15
 * @Filename：AlipayGroupConverter
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AlipayGroupConverter {
    private final UicPaymentAccountReadServiceClient uicPaymentAccountReadServiceClient;

    public TripSCRMGroupTemplateSyncRequest convert2GroupTemplateBuildRequest(GroupDetailDTO groupDetailDTO) {
        TripSCRMGroupTemplateSyncRequest request = new TripSCRMGroupTemplateSyncRequest();
        request.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);
        request.setDeleted((byte) 0);
        request.setGroupTemplateId(groupDetailDTO.getGroupBaseInfoDTO().getGroupId());
        request.setGroupName(groupDetailDTO.getGroupBaseInfoDTO().getGroupName());
        request.setPlatformType(PlatformTypeEnum.ALI_PAY.getCode());
        request.setAdminInfo(groupDetailDTO.getGroupBaseInfoDTO().getGroupAdminUserIdList().stream().map(String::valueOf).collect(Collectors.joining(",")));
        AlipayGroupExtDTO alipayGroupExtDTO = new AlipayGroupExtDTO();
        alipayGroupExtDTO.setGroupCount(groupDetailDTO.getGroupBaseInfoDTO().getGroupCount());
        alipayGroupExtDTO.setGroupMemberCount(groupDetailDTO.getGroupBaseInfoDTO().getGroupMemberCount());
        alipayGroupExtDTO.setModifyHistoryGroup(groupDetailDTO.getGroupBaseInfoDTO().getModifyHistoryGroup());
        alipayGroupExtDTO.setForbidSendMsg(groupDetailDTO.getGroupBaseInfoDTO().getForbidSendMsg());
        alipayGroupExtDTO.setGroupAdminOpenIdList(groupDetailDTO.getGroupBaseInfoDTO().getGroupAdminOpenIdList());
        alipayGroupExtDTO.setRelatedAppId(groupDetailDTO.getGroupBaseInfoDTO().getRelatedAppId());
        alipayGroupExtDTO.setRelatedAppLogo(groupDetailDTO.getGroupBaseInfoDTO().getRelatedAppLogo());
        alipayGroupExtDTO.setWelcomeText(groupDetailDTO.getWelcomeMsgDTO().getWelcomeText());
        alipayGroupExtDTO.setGroupInstanceMaster(groupDetailDTO.getGroupInstanceInfoDTO().getGroupInstanceMaster().getUserId());
        alipayGroupExtDTO.setAutoCreateGroupInstance(groupDetailDTO.getGroupInstanceInfoDTO().getAutoCreateGroupInstance());
        alipayGroupExtDTO.setOpenInvite(groupDetailDTO.getGroupInstanceInfoDTO().getOpenInvite());
        alipayGroupExtDTO.setForbidAdminChat(groupDetailDTO.getGroupInstanceInfoDTO().getForbidAdminChat());
        alipayGroupExtDTO.setForbidJoinMultipleGroupChat(groupDetailDTO.getGroupInstanceInfoDTO().getForbidJoinMultipleGroupChat());
        alipayGroupExtDTO.setForbidMemberChat(groupDetailDTO.getGroupInstanceInfoDTO().getForbidMemberChat());
        request.setExtraInfo(JSONObject.toJSONString(alipayGroupExtDTO));
        return request;

    }

    public WechatGroupDTO convert2WechatGroupDTO(GroupInstanceInfoDTO groupInstanceInfoDTO) {
        WechatGroupDTO request = new WechatGroupDTO();
        request.setChatId(groupInstanceInfoDTO.getGroupInstanceId());
        request.setName(groupInstanceInfoDTO.getGroupInstanceName());
        request.setCorpId(SwitchConfig.ALIPAY_GROUP_CORP_ID);
        request.setPlatformType(PlatformTypeEnum.ALI_PAY);
        request.setIsDeleted((byte) 0);
        request.setOwnerUser(groupInstanceInfoDTO.getGroupInstanceMaster().getUserId());
        request.setCreateTime(groupInstanceInfoDTO.getGmtCreate());
        request.setGroupTemplateId(groupInstanceInfoDTO.getGroupId());
        //支付宝没有返回, 只能用群主id
        request.setAdminUser(groupInstanceInfoDTO.getGroupInstanceMaster().getUserId());
        request.setNotice(groupInstanceInfoDTO.getNotice());
        AlipayGroupInstanceExtDTO alipayGroupInstanceExtDTO = new AlipayGroupInstanceExtDTO();
        alipayGroupInstanceExtDTO.setAutoCreateGroupInstance(groupInstanceInfoDTO.getAutoCreateGroupInstance());
        alipayGroupInstanceExtDTO.setGroupInstanceMemberCount(groupInstanceInfoDTO.getGroupInstanceMemberCount());
        alipayGroupInstanceExtDTO.setForbidAdminChat(groupInstanceInfoDTO.getForbidAdminChat());
        alipayGroupInstanceExtDTO.setForbidJoinMultipleGroupChat(groupInstanceInfoDTO.getForbidJoinMultipleGroupChat());
        alipayGroupInstanceExtDTO.setForbidMemberChat(groupInstanceInfoDTO.getForbidMemberChat());
        alipayGroupInstanceExtDTO.setOpenInvite(groupInstanceInfoDTO.getOpenInvite());
        alipayGroupInstanceExtDTO.setHeadImg(groupInstanceInfoDTO.getHeadImg());
        alipayGroupInstanceExtDTO.setGroupInstanceDesc(groupInstanceInfoDTO.getGroupInstanceDesc());
        request.setExtraInfo(JSONObject.toJSONString(alipayGroupInstanceExtDTO));
        return request;
    }

    public MemberInfoDTO convert2MemberInfoDTO(GroupUserDTO groupUserDTO) {
        MemberInfoDTO memberInfoDTO = new MemberInfoDTO();
        memberInfoDTO.setUserId(groupUserDTO.getUserId());
        memberInfoDTO.setUserName(groupUserDTO.getUserName());
        memberInfoDTO.setJoinScene(AlipayJoinSceneEnum.getByName(groupUserDTO.getJoinScene()).getCode());
        memberInfoDTO.setInviteId(groupUserDTO.getInviteId());
        memberInfoDTO.setJoinTime(groupUserDTO.getJoinTime());
        JSONObject extraInfo = new JSONObject();
        extraInfo.put("promoteChannelKeys", groupUserDTO.getPromoteChannelKeys());
        ResultDO<BasePaymentAccountDO> result = uicPaymentAccountReadServiceClient.getAccountByUserId(Long.valueOf(groupUserDTO.getUserId()), 0);
        if (result.isSuccess() && Objects.nonNull(result.getModule()) && NumberUtils.biggerThanZero(result.getModule().getUserId())) {
            BasePaymentAccountDO accountDO = result.getModule();
            extraInfo.put("taoUserId", accountDO.getUserId());
        }
        memberInfoDTO.setExtraInfo(extraInfo.toJSONString());
        return memberInfoDTO;
    }
}
