package com.alibaba.tripscrm.service.convert;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.activity.ActivityInfoQuery;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityOptionEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityStatusEnum;
import com.alibaba.tripscrm.service.model.domain.request.SopActivityQueryRequest;
import com.alibaba.tripscrm.service.model.vo.activity.AbstractActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.fliggy.pokemon.common.utils.NumberUtils;
import org.springframework.beans.BeanUtils;

import java.util.Objects;
import java.util.Optional;

public class ActivitySopConverter {

    public static ActivityInfoQuery queryRequest2Do(SopActivityQueryRequest request) {
        ActivityInfoQuery query = new ActivityInfoQuery();
        BeanUtils.copyProperties(request, query);
        query.setId(request.getActivityId());
        query.setName(request.getActivityName());
        query.setManager(request.getMemberIds());
        query.setSpaceId(request.getSpaceId());
        if(!NumberUtils.biggerThanZero(request.getSpaceId())) {
            query.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        }
        if (Objects.nonNull(request.getCreateEndTime())) {
            // createEndTime的后一天
            query.setCreateEndTime(DateUtils.addDays(request.getCreateEndTime(), 1));
        }
        return query;
    }

    public static <T extends AbstractActivitySopConfigVO> ActivitySopVO<T> do2Vo(ActivityInfoDO infoDO) {
        ActivitySopVO<T> vo = new ActivitySopVO<>();
        BeanUtils.copyProperties(infoDO, vo);
        vo.setActivityId(infoDO.getId());
        vo.setActivityName(infoDO.getName());
        ActivityStatusEnum activityStatusEnum = ActivityStatusEnum.getByStatus(infoDO.getStatus());
        SopTypeEnum sopTypeEnum = SopTypeEnum.of(infoDO.getSopType());
        vo.setOpts(ActivityOptionEnum.getActivityOptions(activityStatusEnum, sopTypeEnum));
        String context = Optional.ofNullable(infoDO.getContext()).orElse("{}");
        T config = JSONObject.parseObject(context, new TypeReference<T>() {
        });
        config.setActivityId(infoDO.getId());
        config.setMemberIds(infoDO.getMemberIds());
        config.setMemberNames(infoDO.getMemberNames());
        vo.setConfig(config);
        vo.setEffectStartTime(config.getEffectStartTime());
        vo.setEffectEndTime(config.getEffectEndTime());
        return vo;
    }
}
