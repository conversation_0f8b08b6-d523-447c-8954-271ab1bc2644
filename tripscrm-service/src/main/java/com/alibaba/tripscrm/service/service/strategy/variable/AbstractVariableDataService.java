package com.alibaba.tripscrm.service.service.strategy.variable;

import com.alibaba.tripscrm.service.enums.material.DataSourceTypeEnum;
import com.alibaba.tripscrm.service.model.dto.material.VariableInfoDTO;

import java.util.Map;

/**
 * 抽象变量数据服务
 */
public abstract class AbstractVariableDataService {

    /**
     * 数据来源
     * @return 数据来源枚举
     */
    public abstract DataSourceTypeEnum type();

    /**
     * 获取变量值
     * @param variableInfoDTO 变量信息
     * @param carryMap 上游携带数据
     * @return 变量值
     */
    public abstract String getValue(VariableInfoDTO variableInfoDTO, Map<String, Object> carryMap);

}
