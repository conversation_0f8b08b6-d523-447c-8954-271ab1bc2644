package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.AbTestBucketInfo;
import com.alibaba.tripscrm.domain.MaterialInfoNew;
import com.alibaba.tripscrm.domain.TaskInfo;
import com.alibaba.tripscrm.domain.request.TaskQueryRequest;
import com.alibaba.tripscrm.domain.request.TodoTaskQueryRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMTaskInnerService;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.task.TaskAbTypeEnum;
import com.alibaba.tripscrm.service.model.vo.task.AbTestBucketVO;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.AbTestBucketService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@HSFProvider(serviceInterface = TripSCRMTaskInnerService.class)
public class TripSCRMTaskInnerServiceImpl implements TripSCRMTaskInnerService {
    private final AbTestBucketService abTestBucketService;
    private final MaterialService materialService;
    private final TaskService taskService;

    @Override
    @ServiceLog("内部接口-查询当前线上有效任务")
    public TripSCRMResult<List<TaskInfo>> queryValidOnlineTask(TaskQueryRequest request) {
        try {
            TaskQuery query = new TaskQuery();
            query.setTriggerTypeList(request.getTriggerTypeList().stream().map(String::valueOf).collect(Collectors.toList()));
            List<TaskInfoDO> taskInfoList = taskService.queryValidOnlineTaskList(query);
            if (CollectionUtils.isEmpty(taskInfoList)) {
                return TripSCRMResult.success(null);
            }

            List<TaskInfo> taskInfos = taskInfoList.stream()
                    .map(this::convert)
                    .collect(Collectors.toList());
            return TripSCRMResult.success(taskInfos);
        } catch (Exception e) {
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }
    }

    @Override
    @ServiceLog("内部接口-查询指定任务信息")
    public TripSCRMResult<TaskInfo> queryByTaskId(Long taskId) {
        TaskInfoDO taskInfoDO = taskService.queryTaskById(taskId);
        if (Objects.isNull(taskInfoDO)) {
            return TripSCRMResult.success(null);
        }
        TaskInfo taskInfo = convert(taskInfoDO, () -> TaskConfigProcessorFactory.getExtraInfo(taskInfoDO).toJSONString());
        return TripSCRMResult.success(taskInfo);
    }

    @Override
    @ServiceLog("内部接口-查询指定任务的数据查询SQL")
    public TripSCRMResult<String> queryTodoTaskOdpsSql(Long taskId) {
        return TripSCRMResult.success("");
    }

    @Override
    @ServiceLog("内部接口-查询待执行任务列表")
    public TripSCRMResult<List<TaskInfo>> queryTodoTaskList(TodoTaskQueryRequest request) {
        try {
            TaskQuery query = new TaskQuery();
            query.setInterval(request.getInterval());
            List<TaskInfoDO> taskInfoList = taskService.queryTodoTaskList(query);
            if (CollectionUtils.isEmpty(taskInfoList)) {
                return TripSCRMResult.success(null);
            }

            List<TaskInfo> taskInfos = taskInfoList.stream()
                    .map(this::convert)
                    .collect(Collectors.toList());
            return TripSCRMResult.success(taskInfos);
        } catch (Exception e) {
            return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
        }
    }

    private TaskInfo convert(TaskInfoDO taskInfoDO, Supplier<String> extraInfoSupplier) {
        TaskInfo taskInfo = new TaskInfo();
        BeanUtils.copyProperties(taskInfoDO, taskInfo);
        // 素材
        MaterailInfoDO materailInfoDO = materialService.queryById(taskInfo.getMaterialId());
        taskInfo.setMaterialInfo(convert(materailInfoDO));
        taskInfo.setExtInfo(extraInfoSupplier.get());
        // AB 分桶
        if (Objects.nonNull(TaskAbTypeEnum.of(Optional.ofNullable(taskInfo.getAbType()).orElse((byte) 0).intValue()))) {
            List<AbTestBucketVO> abTestBucketVOList = abTestBucketService.listByTaskId(taskInfo.getId());
            List<AbTestBucketInfo> abTestBucketInfoList = abTestBucketVOList.stream().map(x -> {
                AbTestBucketInfo abTestBucketInfo = new AbTestBucketInfo();
                BeanUtils.copyProperties(x, abTestBucketInfo);
                if (NumberUtils.validLong(abTestBucketInfo.getMaterialId()) && !Objects.equals(abTestBucketInfo.getMaterialId(), -1L)) {
                    abTestBucketInfo.setMaterialInfo(convert(materialService.queryById(abTestBucketInfo.getMaterialId())));
                }
                return abTestBucketInfo;
            }).collect(Collectors.toList());
            taskInfo.setAbTestBucketInfoList(abTestBucketInfoList);
        }
        return taskInfo;
    }

    private TaskInfo convert(TaskInfoDO taskInfoDO) {
        return convert(taskInfoDO, taskInfoDO::getExtInfo);
    }

    private MaterialInfoNew convert(MaterailInfoDO materailInfoDO) {
        if (Objects.isNull(materailInfoDO)) {
            return null;
        }

        MaterialInfoNew materialInfo = new MaterialInfoNew();
        BeanUtils.copyProperties(materailInfoDO, materialInfo);
        return materialInfo;
    }
}
