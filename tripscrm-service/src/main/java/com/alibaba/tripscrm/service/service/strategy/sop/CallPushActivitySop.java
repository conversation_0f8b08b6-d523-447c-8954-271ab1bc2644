package com.alibaba.tripscrm.service.service.strategy.sop;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterialTemplateInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialTemplateInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.convert.TaskConverter;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.callpush.CallStageEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.vo.task.TaskVO;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.activity.CallPushActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTemplateService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;

/**
 * 个人活码SOP
 *
 * <AUTHOR>
 * @date 2023-12-30 14:50:08
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CallPushActivitySop extends AbstractActivitySop<CallPushActivitySopConfigVO> {
    private final TaskService taskService;
    private final MaterialService materialService;
    private final MaterialTemplateService materialTemplateService;
    private final ActivityContextService activityContextService;
    private final TagInfoService tagInfoService;

    @Override
    protected void checkConfigValid(CallPushActivitySopConfigVO config, Boolean isUpdate) {
        TaskTriggerTypeEnum triggerTypeEnum = TaskTriggerTypeEnum.getByCode(config.getTriggerType());
        if (!Sets.newHashSet(TaskTriggerTypeEnum.EXECUTION_PLAN, TaskTriggerTypeEnum.SINGLE_EXECUTE, EVENT).contains(triggerTypeEnum)) {
            throw new TripscrmException("智能外呼只支持单次触发、周期触发和事件触发！");
        }

        // 客户自动打标标签列表
        config.setCustomerAutoAddTagList(Optional.ofNullable(config.getCustomerAutoAddTagList()).orElse(new ArrayList<>()));
        // 客户自动打标标签列表长度不可超过20
        if (config.getCustomerAutoAddTagList().size() > 20) {
            throw new TripscrmException("标签列表长度不可超过20");
        }

        // http链接空格去除
        if (StringUtils.hasText(config.getLandingPageUrl())) {
            config.setLandingPageUrl(config.getLandingPageUrl().trim());
        }

        // 推送人数上限
        if (!NumberUtils.biggerThanZero(config.getCallPushLimit())) {
            config.setCallPushLimit(SwitchConfig.CALL_PUSH_ACTIVITY_DEFAULT_PUSH_LIMIT);
        }

        // 欢迎语
        checkWelcomeTaskMaterial(config);
    }

    private void checkWelcomeTaskMaterial(CallPushActivitySopConfigVO config) {
        // 没有配置欢迎语
        if (Optional.ofNullable(config.getWelcomeTaskMaterialId()).orElse(-1L) == -1L) {
            config.setWelcomeTaskMaterialId(-1L);
            return;
        }

        MaterailInfoDO materailInfoDO = materialService.queryById(config.getWelcomeTaskMaterialId());
        // 欢迎语素材不存在
        if (Objects.isNull(materailInfoDO)) {
            throw new TripscrmException("欢迎语素材不存在");
        }

        MaterialTemplateInfoQuery query = new MaterialTemplateInfoQuery();
        query.setId(materailInfoDO.getMaterialTemplateId());
        List<MaterialTemplateInfoDO> templateInfoDOList = materialTemplateService.list(query);
        if (CollectionUtils.isEmpty(templateInfoDOList)) {
            throw new RuntimeException("素材类型非欢迎语");
        }

        // 素材类型非欢迎语
        if (!Objects.equals(TaskType.PERSONAL_WELCOME, TaskType.getByCode(templateInfoDOList.get(0).getType()))) {
            throw new RuntimeException("素材类型非欢迎语");
        }
    }

    @Override
    protected void createTaskList(CallPushActivitySopConfigVO config, Long activityId) {
        // 1.创建外呼推送任务
        createCallPushTask(config, activityId);
        // 2.创建外呼数据处理任务
        createCallPushDataProcessTask(config, activityId);
        // 3.创建手机号添加好友任务
        createAddCustomerByPhoneTask(config, activityId);
        // 4.创建主加好友客户打标任务
        createWechatCustomerAddTagTask(config, activityId);
        // 5.创建主加好友个人欢迎语任务
        createPersonWelcomeTask(config, activityId);
    }

    @Override
    protected void updateTaskList(CallPushActivitySopConfigVO config) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        // 1.更新外呼推送任务
        if (taskType2TaskInfoDO.containsKey(TaskType.CALL_PUSH.getCode())) {
            TaskInfoDO callPushTask = taskType2TaskInfoDO.get(TaskType.CALL_PUSH.getCode());
            updateCallPushTask(config, callPushTask);
        } else {
            createCallPushTask(config, config.getActivityId());
        }

        // 2.更新外呼数据处理任务
        if (taskType2TaskInfoDO.containsKey(TaskType.CALL_PUSH_DATA_PROCESS.getCode())) {
            TaskInfoDO callPushTask = taskType2TaskInfoDO.get(TaskType.CALL_PUSH_DATA_PROCESS.getCode());
            updateCallPushDataProcessTask(config, callPushTask);
        } else {
            createCallPushDataProcessTask(config, config.getActivityId());
        }

        // 3.更新手机号添加好友任务
        if (taskType2TaskInfoDO.containsKey(TaskType.ADD_WECHAT_CUSTOMER_BY_PHONE.getCode())) {
            TaskInfoDO callPushTask = taskType2TaskInfoDO.get(TaskType.ADD_WECHAT_CUSTOMER_BY_PHONE.getCode());
            updateAddCustomerByPhoneTask(config, callPushTask);
        } else {
            createAddCustomerByPhoneTask(config, config.getActivityId());
        }

        // 4.更新客户打标任务
        if (taskType2TaskInfoDO.containsKey(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode())) {
            TaskInfoDO wechatCustomerAddTagTask = taskType2TaskInfoDO.get(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode());
            updateWechatCustomerAddTagTask(config, wechatCustomerAddTagTask);
        } else {
            createWechatCustomerAddTagTask(config, config.getActivityId());
        }

        // 5.更新个人欢迎语任务
        if (taskType2TaskInfoDO.containsKey(TaskType.PERSONAL_WELCOME.getCode())) {
            TaskInfoDO welcomeTask = taskType2TaskInfoDO.get(TaskType.PERSONAL_WELCOME.getCode());
            updatePersonWelcomeTask(config, welcomeTask);
        } else {
            createPersonWelcomeTask(config, config.getActivityId());
        }
    }

    private void createCallPushTask(CallPushActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.CALL_PUSH).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        fillCallPushTaskConfig(config, taskInfoDO);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建外呼推送任务失败");
        }

        processActivityContext(taskInfoDO);
    }

    private void updateCallPushTask(CallPushActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        fillCallPushTaskConfig(config, taskInfoDO);
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException("更新外呼推送任务失败");
        }
    }

    private void fillCallPushTaskConfig(CallPushActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        TaskVO taskVO = new TaskVO();
        taskVO.setTriggerType(config.getTriggerType());
        taskVO.setTriggerTimeType(config.getTriggerTimeType());
        taskVO.setTriggerTimeValue(config.getTriggerTimeValue());
        // 任务开始时间段、结束时间段
        String executeStartTime = config.getExecuteStartTime();
        String executeEndTime = config.getExecuteEndTime();
        taskVO.setExecuteStartTime(executeStartTime);
        taskVO.setExecuteEndTime(executeEndTime);
        taskVO.setNotDisturbStartTime(config.getNotDisturbStartTime());
        taskVO.setNotDisturbEndTime(config.getNotDisturbEndTime());

        // 时间表达式
        taskInfoDO.setTriggerTimeCron(TaskConverter.generateCron(taskVO));
        taskInfoDO.setTriggerType(config.getTriggerType());

        JSONObject extJs = StringUtils.hasText(taskInfoDO.getExtInfo()) ? JSONObject.parseObject(taskInfoDO.getExtInfo()) : new JSONObject();

        switch (Objects.requireNonNull(TaskTriggerTypeEnum.getByCode(config.getTriggerType()))) {
            case SINGLE_EXECUTE:
                long triggerTime = Long.parseLong(taskVO.getTriggerTimeValue());
                Date currDateStart = org.apache.commons.lang.time.DateUtils.truncate(new Date(triggerTime), Calendar.DATE);
                Date currDateEnd = DateUtils.addDays(currDateStart, 1);
                taskInfoDO.setEffectStartTime(currDateStart);
                taskInfoDO.setEffectEndTime(currDateEnd);
                break;
            case EXECUTION_PLAN:
                taskInfoDO.setExecuteTime(executeStartTime + ";" + executeEndTime);
                taskInfoDO.setEffectStartTime(Optional.ofNullable(config.getEffectStartTime()).orElse(new Date()));
                taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
                break;
            case EVENT:
                taskInfoDO.setEventSourceId(config.getEventSourceId());
                taskInfoDO.setEffectStartTime(Optional.ofNullable(config.getEffectStartTime()).orElse(new Date()));
                taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addDays(new Date(), 7)));
                extJs.put(TaskConstant.CALL_PUSH_IN_POPULATION, config.getInPopulation());
                extJs.put(TaskConstant.CALL_PUSH_CROWD_IDS, config.getCrowdIds());
                break;
            default:
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS, "不支持的触发类型");
        }

        extJs.put(TaskConstant.TRIGGER_TIME_TYPE, taskVO.getTriggerTimeType());
        extJs.put(TaskConstant.TRIGGER_TIME_VALUE, taskVO.getTriggerTimeValue());
        extJs.put(TaskConstant.WECHAT_CONTACT_ME_ACTIVITY_ID, config.getContactMeActivityId());
        extJs.put(TaskConstant.LANDING_PAGE_URL, config.getLandingPageUrl());
        extJs.put(TaskConstant.WECHAT_CONTACT_ME_STATE, getWechatContactMeState(config.getContactMeActivityId()));
        extJs.put(TaskConstant.CROWD_ID_FIELD_KEY, config.getCrowdId());
        // 外呼推送上限
        extJs.put(TaskConstant.CALL_PUSH_LIMIT, config.getCallPushLimit());
        // 留样比例
        extJs.put(TaskConstant.SAMPLING_PERCENT, config.getSamplingPercent());
        taskInfoDO.setExtInfo(extJs.toJSONString());
    }

    private void createCallPushDataProcessTask(CallPushActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.CALL_PUSH_DATA_PROCESS).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addDays(config.getEffectEndTime(), 7));
        taskInfoDO.setEventSourceId(SwitchConfig.callPushDataProcessEventSourceId);
        JSONObject extJs = new JSONObject();
        extJs.put(TaskConstant.ADD_CUSTOMER_BY_PHONE_CONDITION_LIST, config.getAddCustomerByPhoneTagList());
        extJs.put(TaskConstant.WECHAT_CONTACT_ME_ACTIVITY_ID, config.getContactMeActivityId());
        extJs.put(TaskConstant.LANDING_PAGE_URL, config.getLandingPageUrl());
        taskInfoDO.setExtInfo(extJs.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建外呼数据处理任务失败");
        }
    }

    private void updateCallPushDataProcessTask(CallPushActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        JSONObject extJs = new JSONObject();
        extJs.put(TaskConstant.ADD_CUSTOMER_BY_PHONE_CONDITION_LIST, config.getAddCustomerByPhoneTagList());
        extJs.put(TaskConstant.LANDING_PAGE_URL, config.getLandingPageUrl());
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addDays(config.getEffectEndTime(), 7));
        taskInfoDO.setExtInfo(extJs.toJSONString());
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException("更新外呼推送任务失败");
        }
    }

    private void createAddCustomerByPhoneTask(CallPushActivitySopConfigVO config, Long activityId) {
        if (!config.isAddCustomerByPhone()) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.ADD_WECHAT_CUSTOMER_BY_PHONE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addDays(config.getEffectEndTime(), 7));
        taskInfoDO.setEventSourceId(SwitchConfig.addCustomerByPhoneEventSourceId);
        JSONObject extJs = new JSONObject();
        extJs.put(TaskConstant.WECHAT_CONTACT_ME_STATE, getWechatContactMeState(config.getContactMeActivityId()));
        taskInfoDO.setExtInfo(extJs.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建手机号添加好友任务失败");
        }
    }

    private void updateAddCustomerByPhoneTask(CallPushActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了主加好友配置
        if (!config.isAddCustomerByPhone()) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addDays(config.getEffectEndTime(), 7));
        taskInfoDO.setEventSourceId(SwitchConfig.addCustomerByPhoneEventSourceId);
        JSONObject extJs = new JSONObject();
        extJs.put(TaskConstant.WECHAT_CONTACT_ME_STATE, getWechatContactMeState(config.getContactMeActivityId()));
        taskInfoDO.setExtInfo(extJs.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void createPersonWelcomeTask(CallPushActivitySopConfigVO config, Long activityId) {
        // 没有配置欢迎语打标
        if (Objects.isNull(config.getWelcomeTaskMaterialId()) || Objects.equals(-1L, config.getWelcomeTaskMaterialId())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.PERSONAL_WELCOME).getInitConfig(activityId, config.getSpaceId() ,getTaskName(config));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addDays(config.getEffectEndTime(), 7));
        taskInfoDO.setMaterialId(config.getWelcomeTaskMaterialId());
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerEventSourceId);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建欢迎语任务失败");
        }
    }

    private void updatePersonWelcomeTask(CallPushActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了欢迎语配置
        if (Objects.isNull(config.getWelcomeTaskMaterialId()) || Objects.equals(-1L, config.getWelcomeTaskMaterialId())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addDays(config.getEffectEndTime(), 7));
        taskInfoDO.setMaterialId(config.getWelcomeTaskMaterialId());
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.addWechatCustomerEventSourceId);
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void createWechatCustomerAddTagTask(CallPushActivitySopConfigVO config, Long activityId) {
        // 没有配置自动打标
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.WECHAT_CUSTOMER_ADD_TAG).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addDays(config.getEffectEndTime(), 7));
        taskInfoDO.setEventSourceId(SwitchConfig.wechatCustomerAddTagEventSourceId);

        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("tagList", config.getCustomerAutoAddTagList());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException("创建客户打标任务失败");
        }
    }

    private void updateWechatCustomerAddTagTask(CallPushActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了自动打标配置
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(DateUtils.addDays(config.getEffectEndTime(), 7));

        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extInfo.put("tagList", config.getCustomerAutoAddTagList());
        taskInfoDO.setExtInfo(extInfo.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private Long processActivityContext(TaskInfoDO taskInfoDO) {
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        Long contextId = configJson.containsKey("contextId") ? configJson.getLong("contextId") : activityContextService.generateContextId();
        // 插入/更新活动上下文信息（targetType = 活动id类型）
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(contextId);
        activityTaskInfoBO.setActivityId(taskInfoDO.getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_ACTIVITY_ID);
        activityTaskInfoBO.setTargetId(String.valueOf(taskInfoDO.getActivityId()));
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("dataTime", System.currentTimeMillis());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);
        PlatformLogUtil.logFail("外呼推送活动，活动上下文处理", LogListUtil.newArrayList(activityTaskInfoBO));
        return contextId;
    }

    private String getWechatContactMeState(Long wechatContactMeActivityId) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(wechatContactMeActivityId);
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        TaskInfoDO taskInfoDO = taskType2TaskInfoDO.get(TaskType.GET_WECHAT_CONTACT_ME.getCode());
        JSONObject configJson = JSONObject.parseObject(taskInfoDO.getConfig());
        return configJson.getString("contextId");
    }

    @Override
    public SopTypeEnum getSopType() {
        return SopTypeEnum.CALL_PUSH;
    }

    @Override
    public void fillInfo(ActivitySopVO<CallPushActivitySopConfigVO> activitySopVO) {
        CallPushActivitySopConfigVO config = activitySopVO.getConfig();
        // 标签信息
        if (!CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(config.getCustomerAutoAddTagList());
            config.setCustomerAutoAddTagList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
            config.setCustomerAutoAddTagNameList(tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        }

        // 主加条件名称
        if (!CollectionUtils.isEmpty(config.getAddCustomerByPhoneTagList())) {
            List<CallStageEnum> callStageEnumList = CallStageEnum.list().stream().filter(callStageEnum -> config.getAddCustomerByPhoneTagList().contains(callStageEnum.getCode()))
                    .collect(Collectors.toList());
            config.setAddCustomerByPhoneTagList(callStageEnumList.stream().map(CallStageEnum::getCode).collect(Collectors.toList()));
            config.setAddCustomerByPhoneTagNameList(callStageEnumList.stream().map(CallStageEnum::getDesc).collect(Collectors.toList()));
        }

        // 素材信息
        Long materialId = Optional.ofNullable(config.getWelcomeTaskMaterialId()).orElse(-1L);
        if (materialId > 0L) {
            MaterailInfoDO materailInfoDO = materialService.queryById(materialId);
            config.setWelcomeTaskMaterialName(materailInfoDO.getName());
        }
    }
}
