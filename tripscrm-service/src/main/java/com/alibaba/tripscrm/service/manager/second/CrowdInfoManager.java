package com.alibaba.tripscrm.service.manager.second;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.manager.middleware.OssClientManager;
import com.fliggy.crowd.service.TripCrowdCommonService;
import com.fliggy.crowd.service.domain.CrowdDTO;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.platform.protocol.TripCommonPlatformResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Objects;

/**
 * 人群查询工具类
 */
@Component
public class CrowdInfoManager {

    @Resource
    private TripCrowdCommonService tripCrowdCommonService;

    @Resource
    private OssClientManager ossClientManager;

    /**
     * 读取圈人文件
     * @param fileName 文件地址
     * @return
     */
    @AteyeInvoker(description = "读取圈人文件", paraDesc = "文件地址")
    public List<String> readCrowdFile(String fileName) throws IOException {
        if (StringUtils.isBlank(fileName)) {
            PlatformLogUtil.logInfo("读取圈人文件，文件地址为空");
            return null;
        }
        BufferedReader reader = null;
        try {
            InputStream inputStream = ossClientManager.getZhugeOssContent(fileName);
            reader = new BufferedReader(new InputStreamReader(inputStream));

            List<String> list = Lists.newArrayList();
            while (true) {
                String line = reader.readLine();
                if (StringUtils.isBlank(line)) {
                    break;
                }
                list.add(line);
            }
            PlatformLogUtil.logInfo("读取圈人文件完成", fileName, list);
            return list;
        } catch (Exception e) {
            PlatformLogUtil.logException("读取圈人文件出现异常", e.getMessage(), e, LogListUtil.newArrayList(fileName));
            return null;
        } finally {
            if (Objects.nonNull(reader)) {
                try {
                    reader.close();
                } catch (IOException e) {
                    PlatformLogUtil.logException("读取圈人文件关闭数据流异常", e.getMessage(), e, LogListUtil.newArrayList(fileName));
                }
            }
        }
    }

    /**
     * 获取圈人数量
     *
     * @param crowdId
     * @return
     */
    @AteyeInvoker(description = "查询人群数量", paraDesc = "人群id")
    public Long getCrowdCount(Long crowdId) {
        if (crowdId == null) {
            PlatformLogUtil.logInfo("查询人群数量，入参人群id为空");
            return null;
        }
        try {
            // 查询人群文件
            TripCommonPlatformResult<CrowdDTO> crowdInfoResult = tripCrowdCommonService.getCrowdInfo(crowdId);
            if (crowdInfoResult == null || !crowdInfoResult.isSuccess() || crowdInfoResult.getData() == null) {
                PlatformLogUtil.logFail("查询人群数量失败", LogListUtil.newArrayList(crowdId, crowdInfoResult));
                return null;
            }
            PlatformLogUtil.logInfo("查询人群数量成功", crowdId, crowdInfoResult);
            Long crowdCount = crowdInfoResult.getData().getCrowdAmount();
            if (crowdCount == null || crowdCount < 1L) {
                return 0L;
            }
            return crowdCount;
        } catch (Exception e) {
            PlatformLogUtil.logException("查询人群数量出现异常", e.getMessage(), e, LogListUtil.newArrayList(crowdId));
            return null;
        }
    }

    /**
     * 获取圈人文件数据
     *
     * @param crowdId 人群id
     * @return
     */
    @AteyeInvoker(description = "查询人群文件", paraDesc = "人群id")
    public List<String> getCrowdFile(Long crowdId) {
        if (crowdId == null) {
            PlatformLogUtil.logInfo("查询人群文件，入参人群id为空");
            return null;
        }
        try {
            // 查询人群文件
            TripCommonPlatformResult<List<String>> crowdInfoResult = tripCrowdCommonService.getCrowdOssFile(crowdId);
            if (crowdInfoResult == null || !crowdInfoResult.isSuccess()) {
                PlatformLogUtil.logFail("查询人群文件失败", LogListUtil.newArrayList(crowdId, crowdInfoResult));
                return null;
            }
            PlatformLogUtil.logInfo("查询人群文件成功", crowdId, crowdInfoResult);
            return crowdInfoResult.getData();
        } catch (Exception e) {
            PlatformLogUtil.logException("查询人群文件出现异常", e.getMessage(), e, LogListUtil.newArrayList(crowdId));
            return null;
        }
    }
}
