package com.alibaba.tripscrm.service.service.strategy.websocket.param.request;

import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import lombok.Data;

@Data
public class ChatGroupRequest {

    /**
     * 成员ID
     */
    private String userId;

    /**
     * 管理组ID
     */
    private Long groupId;

    /**
     * 管理组名称
     */
    private String groupName;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 聊天类型
     * @see ChatTypeEnum
     */
    private Integer chatType;

    /**
     * 是否移除
     */
    private Boolean remove;

    /**
     * 组织ID
     */
    private String corpId;


}
