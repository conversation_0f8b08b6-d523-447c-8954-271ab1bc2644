package com.alibaba.tripscrm.service.manager.task;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.CycleEnum;
import com.alibaba.tripscrm.service.enums.task.TaskFatigueLimitPriorityEnum;
import com.alibaba.tripscrm.service.enums.task.TaskMessageLimitUnitEnum;
import com.alibaba.tripscrm.service.enums.task.TaskMessageTypeTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbLockManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.task.TaskMessageSceneVO;
import com.alibaba.tripscrm.service.model.vo.task.TaskMessageTypeVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.base.TaskMessageTypeService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.FunctionUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-24 21:40:52
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class FatigueManager {
    private final LdbTairManager ldbTairManager;
    private final LdbLockManager ldbLockManager;
    private final TaskMessageTypeService taskMessageTypeService;

    /**
     * 疲劳度管控白名单
     */
    @Switch(description = "疲劳度管控白名单", name = "fatigueWhiteList")
    private static String fatigueWhiteList = "";

    private static final Integer EXPIRE_TIME = 60 * 60 * 24 * 31;

    private static final Map<CycleEnum, FunctionUtils.Function10<ActivityTargetTypeEnum, String, Long, Date, Integer, Long, TaskMessageLimitUnitEnum, TaskFatigueLimitPriorityEnum, String, Long, Boolean>> FUNCTION_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        FUNCTION_MAP.put(CycleEnum.DAY, this::taskMessageDayLimit);
        FUNCTION_MAP.put(CycleEnum.WEEK, this::taskMessageWeekLimit);
        FUNCTION_MAP.put(CycleEnum.MONTH, this::taskMessageMonthLimit);
    }

    public boolean check(ActivityTargetTypeEnum targetTypeEnum, String targetId, TaskInfoDO taskInfoDO, Integer count, Date date) {
        // 非线上环境直接返回成功
        if (!EnvUtils.isOnline() && !SwitchConfig.TASK_FATIGUE_PRE_TEST_LIST.contains(targetId)) {
            return true;
        }

        // 如果命中了客户的白名单，则记录日志后返回成功
        if (StringUtils.hasLength(targetId) && fatigueWhiteList.contains(targetId)) {
            PlatformLogUtil.logInfo("疲劳度管理，命中客户白名单", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        // 若任务信息未设置扩展信息（即消息类型），则记录日志后返回成功
        if (!StringUtils.hasText(taskInfoDO.getExtInfo())) {
            PlatformLogUtil.logInfo("疲劳度管理，传入的任务没有设置消息类型", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        // 解析任务的扩展信息以获取消息类型ID及场景ID
        JSONObject jsonExtraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        Long taskMessageTypeId = jsonExtraInfo.getLong(TaskConstant.TASK_MESSAGE_TYPE_ID);
        Long taskMessageSceneId = jsonExtraInfo.getLong(TaskConstant.TASK_MESSAGE_SCENE_ID);

        // 如果消息类型ID无效，则记录日志后返回成功
        if (!NumberUtils.validLong(taskMessageTypeId)) {
            PlatformLogUtil.logInfo("疲劳度管理，传入的任务获取消息类型为空", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        // 如果命中了消息类型的白名单，则记录日志后返回成功
        if (SwitchConfig.messageTypeIdFatigueWhiteList.contains(taskMessageTypeId)) {
            PlatformLogUtil.logInfo("疲劳度管理，命中消息类型白名单", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        // 根据任务类型获取所有相关的任务消息类型
        List<TaskMessageTypeVO> taskMessageTypeList = taskMessageTypeService.listCacheByTaskType(TaskType.getByCode(taskInfoDO.getType())).stream()
                .filter(taskMessageTypeVO -> StringUtils.hasLength(taskMessageTypeVO.getName()))
                .collect(Collectors.toList());

        TaskMessageTypeVO totalTaskMessageTypeVO = taskMessageTypeList.stream().filter(x -> Objects.equals(TaskMessageTypeTypeEnum.TOTAL.getCode(), x.getType().intValue())).findFirst().orElse(null);
        if (Objects.isNull(totalTaskMessageTypeVO)) {
            PlatformLogUtil.logInfo("疲劳度管理，总频控信息为空", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return false;
        }
        TaskMessageTypeVO taskMessageTypeVO = taskMessageTypeList.stream().filter(x -> Objects.equals(taskMessageTypeId, x.getId())).findFirst().orElse(null);
        return check(targetTypeEnum, targetId, date, count, taskInfoDO.getId(), taskMessageSceneId, totalTaskMessageTypeVO, taskMessageTypeVO, taskInfoDO.getType(), taskInfoDO.getEventSourceId());
    }


    public boolean acquire(ActivityTargetTypeEnum targetTypeEnum, String targetId, TaskInfoDO taskInfoDO, Integer count, Date date) {
        // 非线上环境直接返回成功
        if (!EnvUtils.isOnline() && !SwitchConfig.TASK_FATIGUE_PRE_TEST_LIST.contains(targetId)) {
            return true;
        }

        // 如果命中了客户的白名单，则记录日志后返回成功
        if (StringUtils.hasLength(targetId) && fatigueWhiteList.contains(targetId)) {
            PlatformLogUtil.logInfo("疲劳度管理，命中客户白名单", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        // 若任务信息未设置扩展信息（即消息类型），则记录日志后返回成功
        if (!StringUtils.hasText(taskInfoDO.getExtInfo())) {
            PlatformLogUtil.logInfo("疲劳度管理，传入的任务没有设置消息类型", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        // 解析任务的扩展信息以获取消息类型ID及场景ID
        JSONObject jsonExtraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        Long taskMessageTypeId = jsonExtraInfo.getLong(TaskConstant.TASK_MESSAGE_TYPE_ID);
        Long taskMessageSceneId = jsonExtraInfo.getLong(TaskConstant.TASK_MESSAGE_SCENE_ID);

        // 如果消息类型ID无效，则记录日志后返回成功
        if (!NumberUtils.validLong(taskMessageTypeId)) {
            PlatformLogUtil.logInfo("疲劳度管理，传入的任务获取消息类型为空", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        // 如果命中了消息类型的白名单，则记录日志后返回成功
        if (SwitchConfig.messageTypeIdFatigueWhiteList.contains(taskMessageTypeId)) {
            PlatformLogUtil.logInfo("疲劳度管理，命中消息类型白名单", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        boolean locked = false;
        try {
            // 尝试对指定的目标进行锁定操作，若锁定失败则记录日志并返回失败
            locked = ldbLockManager.lock(getLockKey(targetTypeEnum, targetId), 5);
            if (!locked) {
                PlatformLogUtil.logInfo("疲劳度管理，传入的任务处理加锁失败", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
                return false;
            }

            // 根据任务类型获取所有相关的任务消息类型
            List<TaskMessageTypeVO> taskMessageTypeList = taskMessageTypeService.listCacheByTaskType(TaskType.getByCode(taskInfoDO.getType())).stream()
                    .filter(taskMessageTypeVO -> StringUtils.hasLength(taskMessageTypeVO.getName()))
                    .collect(Collectors.toList());

            TaskMessageTypeVO totalTaskMessageTypeVO = taskMessageTypeList.stream().filter(x -> Objects.equals(TaskMessageTypeTypeEnum.TOTAL.getCode(), x.getType().intValue())).findFirst().orElse(null);
            if (Objects.isNull(totalTaskMessageTypeVO)) {
                PlatformLogUtil.logInfo("疲劳度管理，总频控信息为空", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
                return false;
            }
            TaskMessageTypeVO taskMessageTypeVO = taskMessageTypeList.stream().filter(x -> Objects.equals(taskMessageTypeId, x.getId())).findFirst().orElse(null);

            // 执行疲劳度检查逻辑，如果不符合要求则返回失败
            if (!check(targetTypeEnum, targetId, date, count, taskInfoDO.getId(), taskMessageSceneId, totalTaskMessageTypeVO, taskMessageTypeVO, taskInfoDO.getType(), taskInfoDO.getEventSourceId())) {
                return false;
            }

            // 获取当天已发送的数据
            TaskType taskType = TaskType.getByCode(taskInfoDO.getType());
            switch (taskType) {
                case MINI_PROGRAM_SUBSCRIBE_MSG:
                    return acquireSubscribeMsgTask(targetTypeEnum, targetId, date, taskInfoDO.getId(), taskMessageTypeId, taskMessageSceneId, count, totalTaskMessageTypeVO.getId(), taskInfoDO.getEventSourceId());
                default:
                    return acquireDefaultTask(targetTypeEnum, targetId, date, taskInfoDO.getId(), taskMessageTypeId, taskMessageSceneId, count, totalTaskMessageTypeVO.getId());
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("疲劳度管理，判断过程出现异常", e.getMessage(), e, LogListUtil.newArrayList(targetTypeEnum, targetId, taskInfoDO.getId(), count, date));
            return false;
        } finally {
            // 最终确保解锁操作被执行
            if (locked) {
                ldbLockManager.unlock(getLockKey(targetTypeEnum, targetId));
            }
        }
    }

    /**
     * 处理默认类型任务的疲劳度控制
     *
     * @param targetTypeEnum         目标类型
     * @param targetId               目标Id
     * @param date                   日期
     * @param taskId                 任务id
     * @param taskMessageTypeId      任务消息类型id
     * @param taskMessageSceneId     任务场景id
     * @param count                  消息数量
     * @param totalTaskMessageTypeId 总频控任务消息类型id
     * @return
     */
    private boolean acquireDefaultTask(ActivityTargetTypeEnum targetTypeEnum, String targetId, Date date, Long taskId, Long taskMessageTypeId, Long taskMessageSceneId, Integer count, Long totalTaskMessageTypeId) {
        String fatigueDayDate = (String) ldbTairManager.get(getFatigueDayDataKey(targetTypeEnum, targetId, date));
        String subFatigueDayDate = (String) ldbTairManager.get(getSubFatigueDayDateKey(targetTypeEnum, targetId, date));
        Map<Long, List<Long>> messageType2SendTimestampList = new HashMap<>();
        Map<Long, List<Long>> subMessageType2SendTimestampList = new HashMap<>();

        if (SwitchConfig.TASK_FATIGUE_CACHE_LOG_SWITCH) {
            PlatformLogUtil.logInfo("疲劳度管理，获取疲劳日期数据", targetTypeEnum, targetId, taskId, fatigueDayDate, subFatigueDayDate);
        }

        // 如果存在当天发送数据，则解析为对应的Map结构
        if (Objects.nonNull(fatigueDayDate)) {
            messageType2SendTimestampList = JSONObject.parseObject(fatigueDayDate, new TypeReference<HashMap<Long, List<Long>>>() {
            });
        }
        if (Objects.nonNull(subFatigueDayDate)) {
            subMessageType2SendTimestampList = JSONObject.parseObject(subFatigueDayDate, new TypeReference<HashMap<Long, List<Long>>>() {
            });
        }

        // 对于不存在的消息类型或子类型，初始化其对应的发送时间戳列表
        messageType2SendTimestampList.putIfAbsent(taskMessageTypeId, new ArrayList<>());
        messageType2SendTimestampList.putIfAbsent(totalTaskMessageTypeId, new ArrayList<>());
        subMessageType2SendTimestampList.putIfAbsent(taskMessageSceneId, new ArrayList<>());

        // 记录当前时刻的时间戳
        Long timestamp = date.getTime();

        // 更新当天发送次数
        for (int i = 0; i < count; i++) {
            messageType2SendTimestampList.get(taskMessageTypeId).add(timestamp);
            messageType2SendTimestampList.get(totalTaskMessageTypeId).add(timestamp);
            if (NumberUtils.validLong(taskMessageSceneId)) {
                subMessageType2SendTimestampList.get(taskMessageSceneId).add(timestamp);
            }
        }

        // 记录此次判断结束的信息
        PlatformLogUtil.logInfo("疲劳度管理，本次判断结束", targetTypeEnum, targetId, taskId, count, date);

        // 将更新后的发送数据存回到缓存中
        boolean result = ldbTairManager.put(getFatigueDayDataKey(targetTypeEnum, targetId, date), JSONObject.toJSONString(messageType2SendTimestampList), EXPIRE_TIME);
        if (!result) {
            PlatformLogUtil.logFail("疲劳度管理，更新当日总频控和消息类型频控发送数据失败");
            return false;
        }

        if (!NumberUtils.validLong(taskMessageSceneId)) {
            return true;
        }
        // 如果消息场景ID有效，则也更新相应的子类型发送数据
        result = ldbTairManager.put(getSubFatigueDayDateKey(targetTypeEnum, targetId, date), JSONObject.toJSONString(subMessageType2SendTimestampList), EXPIRE_TIME);
        if (!result) {
            PlatformLogUtil.logFail("疲劳度管理，更新当日消息子类型频控发送数据失败");
            return false;
        }
        return true;
    }

    /**
     * 处理订阅消息类型任务的疲劳度控制
     *
     * @param targetTypeEnum         目标类型
     * @param targetId               目标Id
     * @param date                   日期
     * @param taskId                 任务id
     * @param taskMessageTypeId      任务消息类型id
     * @param taskMessageSceneId     任务场景id
     * @param count                  消息数量
     * @param totalTaskMessageTypeId 总频控任务消息类型id
     * @param eventSourceId          事件源id
     * @return 处理结果
     */
    private boolean acquireSubscribeMsgTask(ActivityTargetTypeEnum targetTypeEnum, String targetId, Date date, Long taskId, Long taskMessageTypeId, Long taskMessageSceneId, Integer count, Long totalTaskMessageTypeId, Long eventSourceId) {
        String fatigueDayDate = (String) ldbTairManager.get(getFatigueDayDataKey(targetTypeEnum, targetId, date));
        String subFatigueDayDate = (String) ldbTairManager.get(getSubFatigueDayDateKey(targetTypeEnum, targetId, date));
        Map<Long, Map<Long, List<Long>>> messageType2eventIdMap = new HashMap<>();
        Map<Long, Map<Long, List<Long>>> subMessageType2eventIdMap = new HashMap<>();

        if (SwitchConfig.TASK_FATIGUE_CACHE_LOG_SWITCH) {
            PlatformLogUtil.logInfo("疲劳度管理，获取疲劳日期数据", targetTypeEnum, targetId, taskId, fatigueDayDate, subFatigueDayDate);
        }

        // 如果存在当天发送数据，则解析为对应的Map结构
        if (Objects.nonNull(fatigueDayDate)) {
            messageType2eventIdMap = JSONObject.parseObject(fatigueDayDate, new TypeReference<HashMap<Long, Map<Long, List<Long>>>>() {
            });
        }
        if (Objects.nonNull(subFatigueDayDate)) {
            subMessageType2eventIdMap = JSONObject.parseObject(subFatigueDayDate, new TypeReference<HashMap<Long, Map<Long, List<Long>>>>() {
            });
        }

        // 对于不存在的消息类型或子类型，初始化其对应的发送时间戳列表
        messageType2eventIdMap.putIfAbsent(taskMessageTypeId, new HashMap<Long, List<Long>>());
        messageType2eventIdMap.putIfAbsent(totalTaskMessageTypeId, new HashMap<Long, List<Long>>());
        subMessageType2eventIdMap.putIfAbsent(taskMessageSceneId, new HashMap<Long, List<Long>>());

        // 记录当前时刻的时间戳
        Long timestamp = date.getTime();

        // 更新当天发送次数
        for (int i = 0; i < count; i++) {
            Map<Long, List<Long>> event2SendTimestampList = messageType2eventIdMap.get(taskMessageTypeId);
            event2SendTimestampList.putIfAbsent(eventSourceId, new ArrayList<Long>());
            event2SendTimestampList.get(eventSourceId).add(timestamp);
            Map<Long, List<Long>> TotalEvent2SendTimestampList = messageType2eventIdMap.get(totalTaskMessageTypeId);
            TotalEvent2SendTimestampList.putIfAbsent(eventSourceId, new ArrayList<Long>());
            TotalEvent2SendTimestampList.get(eventSourceId).add(timestamp);
            if (NumberUtils.validLong(taskMessageSceneId)) {
                Map<Long, List<Long>> subEvent2SendTimestampList = subMessageType2eventIdMap.get(taskMessageSceneId);
                subEvent2SendTimestampList.putIfAbsent(eventSourceId, new ArrayList<Long>());
                subEvent2SendTimestampList.get(eventSourceId).add(timestamp);
            }
        }

        // 记录此次判断结束的信息
        PlatformLogUtil.logInfo("疲劳度管理，本次判断结束", targetTypeEnum, targetId, taskId, count, date);

        // 将更新后的发送数据存回到缓存中
        boolean result = ldbTairManager.put(getFatigueDayDataKey(targetTypeEnum, targetId, date), JSONObject.toJSONString(messageType2eventIdMap), EXPIRE_TIME);
        if (!result) {
            PlatformLogUtil.logFail("疲劳度管理，更新当日总频控和消息类型频控发送数据失败");
            return false;
        }

        if (!NumberUtils.validLong(taskMessageSceneId)) {
            return true;
        }
        // 如果消息场景ID有效，则也更新相应的子类型发送数据
        result = ldbTairManager.put(getSubFatigueDayDateKey(targetTypeEnum, targetId, date), JSONObject.toJSONString(subMessageType2eventIdMap), EXPIRE_TIME);
        if (!result) {
            PlatformLogUtil.logFail("疲劳度管理，更新当日消息子类型频控发送数据失败");
            return false;
        }
        return true;
    }

    public boolean release(ActivityTargetTypeEnum targetTypeEnum, String targetId, TaskInfoDO taskInfoDO, Integer count, Date date) {
        if (!EnvUtils.isOnline() && !SwitchConfig.TASK_FATIGUE_PRE_TEST_LIST.contains(targetId)) {
            return true;
        }

        // 命中白名单
        if (StringUtils.hasLength(targetId) && fatigueWhiteList.contains(targetId)) {
            return true;
        }

        // 没有设置任务扩展信息
        if (!StringUtils.hasText(taskInfoDO.getExtInfo())) {
            PlatformLogUtil.logInfo("疲劳度回退控制，传入的任务没有设置任务类型", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        // 解析任务扩展信息
        JSONObject jsonExtraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        Long taskMessageTypeId = jsonExtraInfo.getLong(TaskConstant.TASK_MESSAGE_TYPE_ID);
        Long taskMessageSceneId = jsonExtraInfo.getLong(TaskConstant.TASK_MESSAGE_SCENE_ID);
        if (!NumberUtils.validLong(taskMessageTypeId)) {
            PlatformLogUtil.logInfo("疲劳度回退控制，传入的任务获取任务类型失败", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
            return true;
        }

        boolean locked = false;
        try {
            // 加锁处理
            locked = ldbLockManager.lock(getLockKey(targetTypeEnum, targetId), 5);
            if (!locked) {
                PlatformLogUtil.logFail("疲劳度回退控制，传入的任务处理加锁失败", LogListUtil.newArrayList(targetTypeEnum, targetId, taskInfoDO.getId(), count, date));
                return false;
            }
            // 获取总频控信息
            List<TaskMessageTypeVO> taskMessageTypeList = taskMessageTypeService.listCacheByTaskType(TaskType.getByCode(taskInfoDO.getType()));
            TaskMessageTypeVO totalTaskMessageTypeVO = taskMessageTypeList.stream().filter(x -> Objects.equals(TaskMessageTypeTypeEnum.TOTAL.getCode(), x.getType())).findFirst().orElse(null);
            if (Objects.isNull(totalTaskMessageTypeVO)) {
                PlatformLogUtil.logInfo("疲劳度回退控制，获取全部任务类型为空", targetTypeEnum, targetId, taskInfoDO.getId(), count, date);
                return false;
            }
            // 获取任务类型
            TaskType taskType = TaskType.getByCode(taskInfoDO.getType());
            // 根据任务类型分发处理
            if (Objects.equals(taskType, TaskType.MINI_PROGRAM_SUBSCRIBE_MSG)) {
                return releaseSubscribeMsgTask(targetTypeEnum, targetId, date, taskInfoDO.getId(), taskMessageTypeId, taskMessageSceneId, taskInfoDO.getEventSourceId(), totalTaskMessageTypeVO.getId());
            } else {
                return releaseDefaultTask(targetTypeEnum, targetId, date, taskInfoDO.getId(), taskMessageTypeId, taskMessageSceneId, totalTaskMessageTypeVO.getId());
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("疲劳度回退控制，判断过程出现异常", e.getMessage(), e, LogListUtil.newArrayList(targetTypeEnum, targetId, taskInfoDO.getId(), count, date));
            return false;
        } finally {
            // 确保解锁
            if (locked) {
                ldbLockManager.unlock(getLockKey(targetTypeEnum, targetId));
            }
        }
    }

    /**
     * 回退小程序订阅消息类型的发送次数
     *
     * @param targetTypeEnum           目标类型（如用户、设备）
     * @param targetId                 目标ID（如用户ID）
     * @param date                     当前日期
     * @param taskId                   任务ID
     * @param taskMessageTypeId        消息类型ID
     * @param taskMessageSceneId       消息场景ID（子类型）
     * @param eventSourceId            事件源ID（用于EVENT粒度统计）
     * @param totalTaskMessageTypeVOId 总频控任务消息类型id
     * @return 是否回退成功（true=成功，false=失败）
     */
    private boolean releaseSubscribeMsgTask(ActivityTargetTypeEnum targetTypeEnum, String targetId, Date date, Long taskId, Long taskMessageTypeId, Long taskMessageSceneId, Long eventSourceId, Long totalTaskMessageTypeVOId) {
        String fatigueDayDate = (String) ldbTairManager.get(getFatigueDayDataKey(targetTypeEnum, targetId, date));
        String subFatigueDayDate = (String) ldbTairManager.get(getSubFatigueDayDateKey(targetTypeEnum, targetId, date));
        Map<Long, Map<Long, List<Long>>> messageType2eventIdMap = new HashMap<>();
        Map<Long, Map<Long, List<Long>>> subMessageType2eventIdList = new HashMap<>();

        if (SwitchConfig.TASK_FATIGUE_CACHE_LOG_SWITCH) {
            PlatformLogUtil.logInfo("疲劳度回退控制，获取疲劳日期数据", targetTypeEnum, targetId, taskId, fatigueDayDate, subFatigueDayDate);
        }

        // 解析缓存数据
        if (Objects.nonNull(fatigueDayDate)) {
            messageType2eventIdMap = JSONObject.parseObject(fatigueDayDate, new TypeReference<HashMap<Long, Map<Long, List<Long>>>>() {});
        }
        if (Objects.nonNull(subFatigueDayDate)) {
            subMessageType2eventIdList = JSONObject.parseObject(subFatigueDayDate, new TypeReference<HashMap<Long, Map<Long, List<Long>>>>() {});
        }

        // 初始化数据结构
        messageType2eventIdMap.putIfAbsent(taskMessageTypeId, new HashMap<>());
        messageType2eventIdMap.putIfAbsent(totalTaskMessageTypeVOId, new HashMap<>());
        subMessageType2eventIdList.putIfAbsent(taskMessageSceneId, new HashMap<>());

        Map<Long, List<Long>> event2TimestampList = messageType2eventIdMap.get(taskMessageTypeId);
        Map<Long, List<Long>> subEvent2TimestampList = subMessageType2eventIdList.get(taskMessageSceneId);
        Map<Long, List<Long>> totalEvent2TimestampList = messageType2eventIdMap.get(totalTaskMessageTypeVOId);
        event2TimestampList.putIfAbsent(eventSourceId, new ArrayList<>());
        totalEvent2TimestampList.putIfAbsent(eventSourceId, new ArrayList<>());
        subEvent2TimestampList.putIfAbsent(eventSourceId, new ArrayList<>());

        // 回退当日发送数据
        Long timestamp = date.getTime();
        event2TimestampList.get(eventSourceId).removeIf(x -> Objects.equals(x, timestamp));
        totalEvent2TimestampList.get(eventSourceId).removeIf(x -> Objects.equals(x, timestamp));
        if (NumberUtils.validLong(taskMessageSceneId)) {
            subEvent2TimestampList.get(eventSourceId).removeIf(x -> Objects.equals(x, timestamp));
        }
        PlatformLogUtil.logInfo("疲劳度回退控制，回退当日发送数据", targetTypeEnum, targetId, taskId, event2TimestampList, totalEvent2TimestampList, subEvent2TimestampList, date);

        // 更新主类型缓存
        boolean result = ldbTairManager.put(getFatigueDayDataKey(targetTypeEnum, targetId, date), JSONObject.toJSONString(messageType2eventIdMap), EXPIRE_TIME);
        if (!result) {
            PlatformLogUtil.logFail("疲劳度回退控制，更新当日总频控和消息类型频控发送数据失败", LogListUtil.newArrayList(targetTypeEnum, targetId, taskId, event2TimestampList, totalEvent2TimestampList, date));
            return false;
        }

        // 更新子类型缓存
        if (NumberUtils.validLong(taskMessageSceneId)) {
            result = ldbTairManager.put(getSubFatigueDayDateKey(targetTypeEnum, targetId, date), JSONObject.toJSONString(subMessageType2eventIdList), EXPIRE_TIME);
            if (!result) {
                PlatformLogUtil.logFail("疲劳度回退控制，更新当日消息子类型频控发送数据失败", LogListUtil.newArrayList(targetTypeEnum, targetId, taskId, subEvent2TimestampList, date));
                return false;
            }
        }

        return true;
    }

    /**
     * 回退默认任务类型的发送次数
     *
     * @param targetTypeEnum           目标类型（如用户、设备）
     * @param targetId                 目标ID（如用户ID）
     * @param date                     当前日期
     * @param taskId                   任务ID
     * @param taskMessageTypeId        消息类型ID
     * @param taskMessageSceneId       消息场景ID（子类型）
     * @param totalTaskMessageTypeVOId 总频控任务消息类型id
     * @return 是否回退成功（true=成功，false=失败）
     */
    private boolean releaseDefaultTask(ActivityTargetTypeEnum targetTypeEnum, String targetId, Date date, Long taskId, Long taskMessageTypeId, Long taskMessageSceneId, Long totalTaskMessageTypeVOId) {
        String fatigueDayDate = (String) ldbTairManager.get(getFatigueDayDataKey(targetTypeEnum, targetId, date));
        String subFatigueDayDate = (String) ldbTairManager.get(getSubFatigueDayDateKey(targetTypeEnum, targetId, date));
        Map<Long, List<Long>> messageType2SendTimestampList = new HashMap<>();
        Map<Long, List<Long>> subMessageType2SendTimestampList = new HashMap<>();

        if (SwitchConfig.TASK_FATIGUE_CACHE_LOG_SWITCH) {
            PlatformLogUtil.logInfo("疲劳度回退控制，获取疲劳日期数据", targetTypeEnum, targetId, taskId, fatigueDayDate, subFatigueDayDate);
        }

        // 解析缓存数据
        if (Objects.nonNull(fatigueDayDate)) {
            messageType2SendTimestampList = JSONObject.parseObject(fatigueDayDate, new TypeReference<HashMap<Long, List<Long>>>() {});
        }
        if (Objects.nonNull(subFatigueDayDate)) {
            subMessageType2SendTimestampList = JSONObject.parseObject(subFatigueDayDate, new TypeReference<HashMap<Long, List<Long>>>() {});
        }

        Long timestamp = date.getTime();
        messageType2SendTimestampList.putIfAbsent(taskMessageTypeId, new ArrayList<>());
        messageType2SendTimestampList.putIfAbsent(totalTaskMessageTypeVOId, new ArrayList<>());
        subMessageType2SendTimestampList.putIfAbsent(taskMessageSceneId, new ArrayList<>());
        // 更新当日发送数据
        messageType2SendTimestampList.get(taskMessageTypeId).removeIf(x -> Objects.equals(x, timestamp));
        messageType2SendTimestampList.get(totalTaskMessageTypeVOId).removeIf(x -> Objects.equals(x, timestamp));
        if (NumberUtils.validLong(taskMessageSceneId)) {
            subMessageType2SendTimestampList.get(taskMessageSceneId).removeIf(x -> Objects.equals(x, timestamp));
        }

        // 更新主类型缓存
        boolean result = ldbTairManager.put(getFatigueDayDataKey(targetTypeEnum, targetId, date), JSONObject.toJSONString(messageType2SendTimestampList), EXPIRE_TIME);
        if (!result) {
            PlatformLogUtil.logFail("疲劳度回退控制，更新当日总频控和消息类型频控发送数据失败");
            return false;
        }
        if (!NumberUtils.validLong(taskMessageSceneId)) {
            return true;
        }
        // 更新子类型缓存
        if (NumberUtils.validLong(taskMessageSceneId)) {
            result = ldbTairManager.put(getSubFatigueDayDateKey(targetTypeEnum, targetId, date), JSONObject.toJSONString(subMessageType2SendTimestampList), EXPIRE_TIME);
            if (!result) {
                PlatformLogUtil.logFail("疲劳度回退控制，更新当日消息子类型频控发送数据失败");
                return false;
            }
        }

        return true;
    }

    /**
     * 获取总发送次数
     *
     * @param targetTypeEnum               目标类型
     * @param targetId                     目标ID
     * @param id                           任务/消息ID
     * @param currentDate                  当前时间
     * @param days                         查询天数
     * @param taskMessageLimitUnitEnum     频控单位
     * @param taskFatigueLimitPriorityEnum 频控优先级
     * @param taskType                     任务类型
     * @param eventSourceId                事件源ID（用于EVENT粒度统计）
     * @return 总发送次数
     * @throws TripscrmException 解析失败或参数非法时抛出异常
     */
    private Long getTotalSendCount(ActivityTargetTypeEnum targetTypeEnum, String targetId, Long id, Date currentDate, Integer days, TaskMessageLimitUnitEnum taskMessageLimitUnitEnum, TaskFatigueLimitPriorityEnum taskFatigueLimitPriorityEnum, String taskType, Long eventSourceId) {
        long total = 0L;

        for (int dayIndex = 0; dayIndex < days; dayIndex++) {
            // 1. 获取疲劳数据
            String fatigueDayDate = getFatigueData(targetTypeEnum, targetId, currentDate, taskFatigueLimitPriorityEnum);

            // 2. 记录日志
            if (SwitchConfig.TASK_FATIGUE_CACHE_LOG_SWITCH) {
                PlatformLogUtil.logInfo("疲劳度管理，计算总发送次数，获取疲劳日期数据", targetTypeEnum, targetId, id, currentDate, fatigueDayDate, taskMessageLimitUnitEnum, taskFatigueLimitPriorityEnum);
            }

            // 3. 根据任务类型分发处理
            TaskType taskTypeEnum = TaskType.getByCode(taskType);
            if (Objects.equals(taskTypeEnum, TaskType.MINI_PROGRAM_SUBSCRIBE_MSG)) {
                total += processMiniProgramSubMsg(id, fatigueDayDate, taskMessageLimitUnitEnum, eventSourceId);
            } else {
                total += processDefaultTaskType(id, fatigueDayDate, taskMessageLimitUnitEnum);
            }

            // 4. 更新日期为前一天
            currentDate = DateUtils.addDays(currentDate, -1);
        }

        return total;
    }

    /**
     * 根据优先级获取疲劳日期数据
     *
     * @param targetTypeEnum 目标类型
     * @param targetId       目标ID
     * @param currentDate    当前时间
     * @param priorityEnum   频控优先级
     * @return 疲劳日期数据（JSON字符串）
     */
    private String getFatigueData(ActivityTargetTypeEnum targetTypeEnum, String targetId, Date currentDate, TaskFatigueLimitPriorityEnum priorityEnum) {
        if (Objects.equals(priorityEnum, TaskFatigueLimitPriorityEnum.MESSAGE_SUBTYPE)) {
            return (String) ldbTairManager.get(getSubFatigueDayDateKey(targetTypeEnum, targetId, currentDate));
        } else {
            return (String) ldbTairManager.get(getFatigueDayDataKey(targetTypeEnum, targetId, currentDate));
        }
    }

    /**
     * 处理小程序订阅消息任务的统计逻辑
     *
     * @param id                  任务/消息ID
     * @param fatigueDayDate      疲劳日期数据（JSON字符串）
     * @param unitEnum            频控单位
     * @param eventSourceId       事件源ID（用于EVENT粒度统计）
     * @return 当前日期的发送次数
     * @throws TripscrmException 解析失败时抛出异常
     */
    private Long processMiniProgramSubMsg(Long id, String fatigueDayDate, TaskMessageLimitUnitEnum unitEnum, Long eventSourceId) {
        Map<Long, Map<Long, List<Long>>> subscribeMessageTypeToEventMap = new HashMap<>();
        try {
            if (Objects.nonNull(fatigueDayDate)) {
                subscribeMessageTypeToEventMap = JSONObject.parseObject(fatigueDayDate, new TypeReference<HashMap<Long, Map<Long, List<Long>>>>() {});
            }
            Map<Long, List<Long>> eventToTimestampsMap = subscribeMessageTypeToEventMap.getOrDefault(id, new HashMap<>());

            switch (unitEnum) {
                case TASK:
                    return eventToTimestampsMap.values().stream()
                            .mapToLong(list -> list.stream().distinct().count())
                            .sum();
                case MESSAGE:
                    return eventToTimestampsMap.values().stream()
                            .mapToLong(List::size)
                            .sum();
                case EVENT:
                    List<Long> timestampsForEventList = eventToTimestampsMap.getOrDefault(eventSourceId, new ArrayList<>());
                    return (long) timestampsForEventList.size();
                default:
                    throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("疲劳度管理，计算总发送次数，获取疲劳日期数据解析失败", e.getMessage(), e, LogListUtil.newArrayList(fatigueDayDate));
            throw new TripscrmException(TripSCRMErrorCode.TO_JSON_FAIL);
        }
    }

    /**
     * 处理其他类型任务的统计逻辑
     *
     * @param id                  任务/消息ID
     * @param fatigueDayDate      疲劳日期数据（JSON字符串）
     * @param unitEnum            频控单位
     * @return 当前日期的发送次数
     * @throws TripscrmException 解析失败时抛出异常
     */
    private Long processDefaultTaskType(Long id, String fatigueDayDate, TaskMessageLimitUnitEnum unitEnum) {
        Map<Long, List<Long>> messageTypeToTimestampsMap = new HashMap<>();
        try {
            if (Objects.nonNull(fatigueDayDate)) {
                messageTypeToTimestampsMap = JSONObject.parseObject(fatigueDayDate, new TypeReference<HashMap<Long, List<Long>>>() {});
            }
            List<Long> timestampsForMessageTypeList = messageTypeToTimestampsMap.getOrDefault(id, new ArrayList<>());

            if (Objects.equals(unitEnum, TaskMessageLimitUnitEnum.MESSAGE)) {
                return (long) timestampsForMessageTypeList.size();
            } else {
                return timestampsForMessageTypeList.stream().distinct().count();
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("疲劳度管理，计算总发送次数，获取疲劳日期数据解析失败", e.getMessage(), e, LogListUtil.newArrayList(fatigueDayDate));
            throw new TripscrmException(TripSCRMErrorCode.TO_JSON_FAIL);
        }
    }

    private Boolean taskMessageDayLimit(ActivityTargetTypeEnum targetTypeEnum, String targetId, Long id, Date now, Integer count, Long limit, TaskMessageLimitUnitEnum taskMessageLimitUnitEnum, TaskFatigueLimitPriorityEnum taskFatigueLimitPriorityEnum, String taskType, Long eventSourceId) {
        if (Objects.equals(taskMessageLimitUnitEnum, TaskMessageLimitUnitEnum.TASK)) {
            count = 1;
        }
        if (getTotalSendCount(targetTypeEnum, targetId, id, now, 1, taskMessageLimitUnitEnum, taskFatigueLimitPriorityEnum, taskType, eventSourceId) + count <= limit) {
            return true;
        }

        PlatformLogUtil.logInfo("天纬度 频控限制", targetTypeEnum, targetId, id, count, limit, taskMessageLimitUnitEnum, taskFatigueLimitPriorityEnum);
        return false;
    }

    private Boolean taskMessageWeekLimit(ActivityTargetTypeEnum targetTypeEnum, String targetId, Long id, Date now, Integer count, Long limit, TaskMessageLimitUnitEnum taskMessageLimitUnitEnum, TaskFatigueLimitPriorityEnum taskFatigueLimitPriorityEnum, String taskType, Long eventSourceId) {
        if (Objects.equals(taskMessageLimitUnitEnum, TaskMessageLimitUnitEnum.TASK)) {
            count = 1;
        }
        if (getTotalSendCount(targetTypeEnum, targetId, id, now, 7, taskMessageLimitUnitEnum, taskFatigueLimitPriorityEnum, taskType, eventSourceId) + count <= limit) {
            return true;
        }

        PlatformLogUtil.logInfo("周纬度 频控限制", targetTypeEnum, targetId, id, count, limit, taskMessageLimitUnitEnum, taskFatigueLimitPriorityEnum);
        return false;
    }

    private Boolean taskMessageMonthLimit(ActivityTargetTypeEnum targetTypeEnum, String targetId, Long id, Date now, Integer count, Long limit, TaskMessageLimitUnitEnum taskMessageLimitUnitEnum, TaskFatigueLimitPriorityEnum taskFatigueLimitPriorityEnum, String taskType, Long eventSourceId) {
        if (Objects.equals(taskMessageLimitUnitEnum, TaskMessageLimitUnitEnum.TASK)) {
            count = 1;
        }
        if (getTotalSendCount(targetTypeEnum, targetId, id, now, 30, taskMessageLimitUnitEnum, taskFatigueLimitPriorityEnum, taskType, eventSourceId) + count <= limit) {
            return true;
        }

        PlatformLogUtil.logInfo("月纬度 频控限制", targetTypeEnum, targetId, id, count, limit, taskMessageLimitUnitEnum, taskFatigueLimitPriorityEnum);
        return false;
    }

    /**
     * 检查任务是否满足频控
     *
     * @param targetTypeEnum         目标类型
     * @param targetId               目标id
     * @param now                    当前日期
     * @param messageCount           待发送的消息数量
     * @param taskId                 任务id
     * @param taskMessageTypeSceneId 任务消息使用场景id
     * @param totalTaskMessageTypeVO 总频控信息
     * @param taskMessageTypeVO      任务消息类型信息
     * @param taskType               任务类型
     * @param eventSourceId          事件来源id
     * @return
     */
    private Boolean check(ActivityTargetTypeEnum targetTypeEnum, String targetId, Date now, Integer messageCount, Long taskId, Long taskMessageTypeSceneId, TaskMessageTypeVO totalTaskMessageTypeVO, TaskMessageTypeVO taskMessageTypeVO, String taskType, Long eventSourceId) {
        // 检查是否满足总频控
        if (!checkByTypeAndUnitAndPriority(targetTypeEnum, targetId, totalTaskMessageTypeVO.getId(), now, messageCount, totalTaskMessageTypeVO.getLimit(), TaskFatigueLimitPriorityEnum.TOTAL, totalTaskMessageTypeVO.getLimitType(), totalTaskMessageTypeVO.getLimitUnit(), taskType, eventSourceId)) {
            PlatformLogUtil.logInfo("疲劳度管理，总频控限制", targetTypeEnum, targetId, taskId, messageCount, now, totalTaskMessageTypeVO, taskType, eventSourceId);
            return false;
        }
        // 检查是否满足消息类型频控
        if (Objects.isNull(taskMessageTypeVO)) {
            PlatformLogUtil.logInfo("疲劳度管理，消息类型频控信息为空", targetTypeEnum, targetId, taskId, taskMessageTypeVO);
            return true;
        }
        if (!checkByTypeAndUnitAndPriority(targetTypeEnum, targetId, taskMessageTypeVO.getId(), now, messageCount, taskMessageTypeVO.getLimit(), TaskFatigueLimitPriorityEnum.MESSAGE_TYPE, taskMessageTypeVO.getLimitType(), taskMessageTypeVO.getLimitUnit(), taskType, eventSourceId)) {
            PlatformLogUtil.logInfo("疲劳度管理，消息类型限制", targetTypeEnum, targetId, taskId, messageCount, now, taskMessageTypeVO);
            return false;
        }
        // 检查是否满足消息子类型频控
        TaskMessageSceneVO taskMessageSceneVO = taskMessageTypeVO.getMessageScenes().stream().filter(x -> Objects.equals(taskMessageTypeSceneId, x.getId())).findFirst().orElse(null);
        if (Objects.isNull(taskMessageSceneVO)) {
            PlatformLogUtil.logInfo("疲劳度管理，消息子类型频控信息为空", targetTypeEnum, targetId, taskId, taskMessageTypeVO.getMessageScenes(), taskMessageTypeSceneId);
            return true;
        }
        if (!checkByTypeAndUnitAndPriority(targetTypeEnum, targetId, taskMessageSceneVO.getId(), now, messageCount, taskMessageSceneVO.getLimit(), TaskFatigueLimitPriorityEnum.MESSAGE_SUBTYPE, taskMessageSceneVO.getLimitType(), taskMessageSceneVO.getLimitUnit(), taskType, eventSourceId)) {
            PlatformLogUtil.logInfo("疲劳度管理，消息子类型限制", targetTypeEnum, targetId, taskId, messageCount, now, taskMessageSceneVO);
            return false;
        }
        return true;
    }

    /**
     * 根据频控类型、单位、优先级校验任务是否满足频控
     *
     * @param targetTypeEnum               目标类型
     * @param targetId                     目标id
     * @param id                           频控id
     * @param now                          当前日期
     * @param messageCount                 待发送的消息数量
     * @param limit                        频控值
     * @param taskFatigueLimitPriorityEnum 频控优先级 1-总频控 2-消息类型频控 3-消息子类型频控
     * @param limitType                    频控类型 1-天，2-周，3-月
     * @param limitUnit                    频控单位 1-任务，2-消息
     * @param taskType                     任务类型
     * @param eventSourceId                事件来源id
     * @return
     */
    private Boolean checkByTypeAndUnitAndPriority(ActivityTargetTypeEnum targetTypeEnum, String targetId, Long id, Date now, Integer messageCount, Long limit, TaskFatigueLimitPriorityEnum taskFatigueLimitPriorityEnum, Integer limitType, Integer limitUnit, String taskType, Long eventSourceId) {
        CycleEnum subCycleEnum = CycleEnum.of(limitType);
        TaskMessageLimitUnitEnum taskMessageLimitUnitEnum = TaskMessageLimitUnitEnum.of(limitUnit);
        Integer count;
        switch (taskMessageLimitUnitEnum) {
            case TASK:
            case EVENT:
                count = 1;
                break;
            case MESSAGE:
                count = messageCount;
                break;
            default:
                PlatformLogUtil.logFail("非法频控单位", LogListUtil.newArrayList(taskMessageLimitUnitEnum));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_TASK_MESSAGE_LIMIT_UNIT_ENUM);
        }
        return FUNCTION_MAP.get(subCycleEnum).apply(targetTypeEnum, targetId, id, now, count, limit, taskMessageLimitUnitEnum, taskFatigueLimitPriorityEnum, taskType, eventSourceId);
    }

    @AteyeInvoker(description = "获取私聊疲劳度数据", paraDesc = "targetId&targetType")
    public String getFatigueData(String targetId, Integer targetType) {
        Date date = new Date();
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(targetType);
        switch (targetTypeEnum) {
            case WX_UNION_ID:
                Map<String, Map<Long, List<Long>>> unionIdMap = new HashMap<>();

                for (int i = 0; i < 30; i++) {
                    String fatigueDayDataKey = getFatigueDayDataKey(ActivityTargetTypeEnum.WX_UNION_ID, targetId, date);
                    String fatigueDayDate = (String) ldbTairManager.get(fatigueDayDataKey);
                    if (Objects.nonNull(fatigueDayDate)) {
                        unionIdMap.put(fatigueDayDataKey, JSONObject.parseObject(fatigueDayDate, new TypeReference<HashMap<Long, List<Long>>>() {
                        }));
                    }
                    String subFatigueDayDataKey = getSubFatigueDayDateKey(ActivityTargetTypeEnum.WX_UNION_ID, targetId, date);
                    String subFatigueDayDate = (String) ldbTairManager.get(subFatigueDayDataKey);
                    if (Objects.nonNull(fatigueDayDate)) {
                        unionIdMap.put(subFatigueDayDataKey, JSONObject.parseObject(subFatigueDayDate, new TypeReference<HashMap<Long, List<Long>>>() {
                        }));
                    }

                    LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    date = Date.from(localDate.minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                }
                return JSONObject.toJSONString(unionIdMap);
            case TAOBAO_USER_ID:
                Map<String, Map<Long, Map<Long, List<Long>>>> taoIdMap = new HashMap<>();
                for (int i = 0; i < 30; i++) {
                    String fatigueDayDataKey = getFatigueDayDataKey(ActivityTargetTypeEnum.TAOBAO_USER_ID, targetId, date);
                    String fatigueDayDate = (String) ldbTairManager.get(fatigueDayDataKey);
                    if (Objects.nonNull(fatigueDayDate)) {
                        taoIdMap.put(fatigueDayDataKey, JSONObject.parseObject(fatigueDayDate, new TypeReference<HashMap<Long, Map<Long, List<Long>>>>() {
                        }));
                    }
                    String subFatigueDayDataKey = getSubFatigueDayDateKey(ActivityTargetTypeEnum.TAOBAO_USER_ID, targetId, date);
                    String subFatigueDayDate = (String) ldbTairManager.get(subFatigueDayDataKey);
                    if (Objects.nonNull(fatigueDayDate)) {
                        taoIdMap.put(subFatigueDayDataKey, JSONObject.parseObject(subFatigueDayDate, new TypeReference<HashMap<Long, Map<Long, List<Long>>>>() {
                        }));
                    }

                    LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    date = Date.from(localDate.minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
                }
                return JSONObject.toJSONString(taoIdMap);
            default:
                PlatformLogUtil.logFail("非法目标类型", LogListUtil.newArrayList(targetType));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }
    }

    @AteyeInvoker(description = "清除私聊疲劳度数据", paraDesc = "targetId&targetType")
    public List<Integer> cleanFatigueData(String targetId, Integer targetType) {
        int res = 0;
        Date date = new Date();
        ActivityTargetTypeEnum targetTypeEnum = ActivityTargetTypeEnum.codeOf(targetType);
        if (Objects.isNull(targetTypeEnum)) {
            PlatformLogUtil.logFail("非法目标类型", LogListUtil.newArrayList(targetType));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_TARGET_TYPE);
        }
        for (int i = 0; i < 30; i++) {
            String fatigueDayDataKey = getFatigueDayDataKey(targetTypeEnum, targetId, date);
            res += ldbTairManager.delete(fatigueDayDataKey) ? 0 : 1;
            LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            date = Date.from(localDate.minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        }

        int subRes = 0;
        Date subDate = new Date();
        for (int i = 0; i < 30; i++) {
            String fatigueDayDataKey = getSubFatigueDayDateKey(targetTypeEnum, targetId, subDate);
            subRes += ldbTairManager.delete(fatigueDayDataKey) ? 0 : 1;
            LocalDate localDate = subDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            subDate = Date.from(localDate.minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        }
        return Lists.newArrayList(res, subRes);
    }

    private String getLockKey(ActivityTargetTypeEnum targetTypeEnum, String targetId) {
        return TairConstant.LDB_FATIGUE_LOCK_PREFIX + targetTypeEnum.getCode() + "_" + targetId;
    }

    private String getFatigueDayDataKey(ActivityTargetTypeEnum targetTypeEnum, String targetId, Date date) {
        Long dayVersion = DateUtils.getDayVersion(date);
        return TairConstant.LDB_FATIGUE_DAY_DATA_PREFIX + dayVersion + "_" + targetTypeEnum.getCode() + "_" + targetId;
    }

    private String getSubFatigueDayDateKey(ActivityTargetTypeEnum targetTypeEnum, String targetId, Date date) {
        Long dayVersion = DateUtils.getDayVersion(date);
        return TairConstant.LDB_SUB_FATIGUE_DAY_DATA_PREFIX + dayVersion + "_" + targetTypeEnum.getCode() + "_" + targetId;
    }
}
