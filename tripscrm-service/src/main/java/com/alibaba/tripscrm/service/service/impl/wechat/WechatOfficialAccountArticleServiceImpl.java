package com.alibaba.tripscrm.service.service.impl.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatOfficialAccountArticleMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatOfficialAccountArticleDO;
import com.alibaba.tripscrm.service.service.wechat.WechatOfficialAccountArticleService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.WxOfficialAccountService;
import com.alibaba.tripzoo.proxy.model.WechatOfficialFreePublishBO;
import com.alibaba.tripzoo.proxy.request.WechatOfficialAccountBatchGetFreePublishRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/3/26 15:03
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatOfficialAccountArticleServiceImpl implements WechatOfficialAccountArticleService {
    private final WechatOfficialAccountArticleMapper wechatOfficialAccountArticleMapper;
    private final WxOfficialAccountService wxOfficialAccountService;

    @Override
    public WechatOfficialFreePublishBO fetchFreePublishList(Integer offset) {
        WechatOfficialAccountBatchGetFreePublishRequest request = new WechatOfficialAccountBatchGetFreePublishRequest();
        request.setOffset(offset);
        ResultDO<WechatOfficialFreePublishBO> resultDO = wxOfficialAccountService.getFreePublishList(request);
        if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
            PlatformLogUtil.logFail("getFreePublishList fail");
            return null;
        }

        return resultDO.getModel();
    }

    @Override
    public List<WechatOfficialAccountArticleDO> listByParam(Map<String, Object> paramMap) {
        try {
            return wechatOfficialAccountArticleMapper.selectByParam(paramMap);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return new ArrayList<>();
        }
    }

    @Override
    public int countByParam(Map<String, Object> paramMap) {
        try {
            return wechatOfficialAccountArticleMapper.countByParam(paramMap);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return 0;
        }
    }

    @Override
    public Integer insert(WechatOfficialAccountArticleDO wechatOfficialAccountArticleDO) {
        try {
            if (Objects.isNull(wechatOfficialAccountArticleDO)) {
                throw new RuntimeException("参数不合法");
            }

            return wechatOfficialAccountArticleMapper.insert(wechatOfficialAccountArticleDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return 0;
        }
    }

    @Override
    public Integer updateByArticleIdAndUrl(WechatOfficialAccountArticleDO wechatOfficialAccountArticleDO) {
        try {
            if (Objects.isNull(wechatOfficialAccountArticleDO)) {
                throw new RuntimeException("参数不合法");
            }

            return wechatOfficialAccountArticleMapper.updateByArticleIdAndUrl(wechatOfficialAccountArticleDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return 0;
        }
    }

    @Override
    public List<WechatOfficialAccountArticleDO> listByArticleId(String articleId) {
        try {
            if (!StringUtils.hasText(articleId)) {
                throw new RuntimeException("参数不合法");
            }

            return wechatOfficialAccountArticleMapper.selectByArticleId(articleId);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return new ArrayList<>();
        }
    }

    @Override
    public WechatOfficialAccountArticleDO getById(Long id) {
        try {
            return wechatOfficialAccountArticleMapper.selectById(id);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return null;
        }
    }

    @AteyeInvoker(paraDesc = "id", description = "删除文章")
    public Integer deleteById(Long id) {
        try {
            return wechatOfficialAccountArticleMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return 0;
        }
    }
}
