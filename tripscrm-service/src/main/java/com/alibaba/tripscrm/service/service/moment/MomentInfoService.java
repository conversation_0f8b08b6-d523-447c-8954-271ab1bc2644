package com.alibaba.tripscrm.service.service.moment;

import com.alibaba.tripscrm.dal.model.domain.data.MomentInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.MomentInfoQuery;
import com.alibaba.tripscrm.service.model.vo.wechat.MomentInfoVO;

import java.util.Date;
import java.util.List;

/**
 * 微信朋友圈服务
 * <AUTHOR>
 */
public interface MomentInfoService {

    /**
     * 插入朋友圈数据
     * @param momentInfoDO 朋友圈信息
     * @return boolean
     */
    boolean insert(MomentInfoDO momentInfoDO);

    /**
     * 根据朋友圈id和用户id更新数据
     * @param momentInfoDO 朋友圈信息
     * @return boolean
     */
    boolean updateByMomentIdAndUserId(MomentInfoDO momentInfoDO);

    /**
     * 根据id更新数据
     * @param momentInfoDO 朋友圈信息
     * @return boolean
     */
    boolean updateById(MomentInfoDO momentInfoDO);

    MomentInfoDO queryByMomentId(String momentId);

    /**
     * 批量插入朋友圈信息
     * @param momentInfoDOList 朋友圈信息列表
     * @return boolean
     */
    boolean batchInsert(List<MomentInfoDO> momentInfoDOList);

    /**
     * 通过时间获取朋友圈列表
     * @param days 获取朋友圈的天数
     * @return 朋友圈信息
     */
    List<MomentInfoDO> queryMomentInfoByTime(int days);

    /**
     * 获取朋友圈数据列表
     * @param taskInfoDO 任务信息
     * @return MomentInfoVO
     */
    MomentInfoVO queryMomentInfo(TaskInfoDO taskInfoDO);

    /**
     * 查询时间范围内的所有朋友圈
     * @param start 开始时间
     * @param end 结束时间
     * @return 朋友圈列表
     */
    List<MomentInfoDO> queryAllMomentByDate(Date start, Date end);

    /**
     * 条件查询朋友圈
     * @param query 过滤条件
     * @return 查询结果
     */
    List<MomentInfoDO> queryByParam(MomentInfoQuery query);

}
