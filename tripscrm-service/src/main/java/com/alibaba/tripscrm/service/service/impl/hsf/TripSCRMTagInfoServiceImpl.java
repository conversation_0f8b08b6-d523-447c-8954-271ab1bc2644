package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.TagMetaInfo;
import com.alibaba.tripscrm.domain.request.TripSCRMTagInfoRequest;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMTagInfoService;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.model.domain.query.TagInfoQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;

import java.util.Objects;

/**
 * <AUTHOR>
 * 飞猪SCRM-标签HSF服务-实现
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@HSFProvider(serviceInterface = TripSCRMTagInfoService.class)
public class TripSCRMTagInfoServiceImpl implements TripSCRMTagInfoService {
    private final TagInfoService tagInfoService;

    @Override
    @ServiceLog("查询SCRM标签")
    public TripSCRMResult<PageInfoDTO<TagMetaInfo>> pageQueryMetaInfo(TripSCRMTagInfoRequest request) {
        if (Objects.isNull(request)
                || !NumberUtils.validInteger(request.getPageNum())
                || !NumberUtils.validInteger(request.getPageSize())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        try {
            TagInfoQuery query = new TagInfoQuery();
            BeanUtils.copyProperties(request, query);
            if (NumberUtils.validLong(request.getSpaceId())) {
                query.setSpaceIdList(Lists.newArrayList(request.getSpaceId()));
            }
            query.setPageNum(request.getPageNum());
            query.setPageSize(request.getPageSize());
            PageInfo<TagInfoDTO> pageInfo = tagInfoService.pageQuery(query);
            if (Objects.isNull(pageInfo)) {
                return TripSCRMResult.fail(TripSCRMErrorCode.DB_FAILED);
            }

            PageInfoDTO<TagMetaInfo> pageInfoDTO = PageUtils.getPageInfoDTO(pageInfo, this::convertTagInfo);
            if (Objects.isNull(pageInfoDTO)) {
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
            }

            return TripSCRMResult.success(pageInfoDTO);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.SYSTEM_EXCEPTION.getCode(), e.getMessage());
        }
    }

    private TagMetaInfo convertTagInfo(TagInfoDTO d) {
        if (Objects.isNull(d)) {
            return null;
        }

        TagMetaInfo metaInfo = new TagMetaInfo();
        BeanUtils.copyProperties(d, metaInfo);

        return metaInfo;
    }
}
