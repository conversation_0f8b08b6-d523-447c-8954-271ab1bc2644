package com.alibaba.tripscrm.service.model.domain.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/3/16 23:27
 **/
@Data
public class WechatTransferCustomerTaskCalcResponse implements Serializable {
    // 继承数
    private Long allCount;
    // 成功数
    private Long successCount;
    // 拒绝数
    private Long refusedCount;
    // 非好友数
    private Long notFriendCount;
    // 等待继承数
    private Long waitingCount;
    // 到达继承好友上限数
    private Long limitCount;
    // 其他数
    private Long otherCount;
    // 成功率（小数）
    private Double successRate;
}
