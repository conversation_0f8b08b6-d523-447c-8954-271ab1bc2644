package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupListQuery;
import com.alibaba.tripscrm.service.model.domain.request.*;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.result.ResultDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-04 11:37:28
 */
public interface WechatGroupService {
    /**
     * 分页获取企微群聊列表
     *
     * @param query 查询条件
     * @return 企微群聊信息
     */
    PageInfoDTO<WechatGroupVO> listPageInfo(WechatGroupListQuery query);

    /**
     * 群聊批量添加标签
     *
     * @param request 请求
     */
    @Deprecated
    Integer addTag(WechatGroupAddTagRequest request);

    /**
     * 群聊批量删除标签
     *
     * @param request 请求
     */
    @Deprecated
    void deleteTag(WechatGroupDeleteTagRequest request);

    /**
     * 根据 chatId 查询 群聊的信息
     *
     * @param chatId chatId
     * @return 群聊的信息
     */
    WechatGroupVO getWechatGroupVOByChatId(String chatId);

    /**
     * 根据 chatId 查询 群聊的信息
     *
     * @param chatId chatId
     * @return 群聊的信息
     */
    WechatGroupDO getByChatId(String chatId);

    /**
     * 根据 ownerUserId 查询 群聊的信息
     *
     * @param userId userId
     * @return 群聊的信息
     */
    List<WechatGroupDO> listByOwnerUser(String userId, String corpId);

    /**
     * 根据 ownerUserId 查询 群聊的信息 分页
     * @param userId
     * @param corpId
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfoDTO<WechatGroupDO> listByOwnerUserPage(String userId, String corpId, Integer pageNo, Integer pageSize);

    /**
     * 根据 chatId 查询 群聊的信息
     *
     * @param chatIdList chatId
     * @return 群聊的信息
     */
    List<WechatGroupVO> listByChatIdList(List<String> chatIdList, Boolean onlyQueryOwner);

    /**
     * 根据 userId 查询 群聊的信息
     *
     * @param userId userId
     * @return 群聊的信息
     */
    List<WechatGroupVO> listByUserId(String userId);

    /**
     * 根据 userId 列表查询 群聊的信息
     *
     * @param userIdList
     * @return
     */
    List<WechatGroupVO> listByUserIdList(List<String> userIdList);

    /**
     * 查询群聊中的成员
     *
     * @param chatId            chatId
     * @param groupUserTypeEnum 群成员类型
     * @return userIdList
     */
    List<String> listUserIdByChatIdAndUserTypeAndCorpId(String chatId, GroupUserTypeEnum groupUserTypeEnum, String corpId);

    /**
     * 获取企微群内的客户数量
     *
     * @return
     */
    Integer getGroupCustomerCount(List<String> chatIdList, String state);

    /**
     * 获取企微群数量
     *
     * @return
     */
    Integer getGroupCount(List<String> tagIdList);

    /**
     * 获取企微群内的客户数量（有企微成员好友关系）
     *
     * @return
     */
    Integer getGroupCustomerWithFollowUserCount(List<String> chatIdList, String state);


    /**
     * 获取企微群内已退群的客户数量
     *
     * @return
     */
    Integer getGroupLeaveCustomerCount(List<String> chatIdList, String state);

    /**
     * 获取企微群内已退群的客户数量（有企微成员好友关系）
     *
     * @return
     */
    Integer getGroupLeaveCustomerWithFollowUserCount(List<String> chatIdList, String state);

    /**
     * 是否拥有群主权限
     *
     * @param spaceId
     * @param chatId
     * @return
     */
    boolean hasOwnerPermission(Long spaceId, String chatId);

    /**
     * 是否拥有管理员权限
     *
     * @param spaceId
     * @param chatId
     * @return
     */
    boolean hasAdminPermission(Long spaceId, String chatId);

    /**
     * 邀请加入群聊
     *
     * @param request 请求体
     * @return 是否邀请成功
     */
    int inviteJoinGroup(InviteJoinGroupRequest request);

    /**
     * 从群聊中加好友
     *
     * @param request 请求体
     * @return 是否邀请成功
     */
    boolean addCustomerByGroup(AddCustomerByGroupRequest request);

    /**
     * 创建群聊
     *
     * @param request 请求体
     * @return 是否创建成功
     */
    TripSCRMResult<String> createGroup(CreateGroupRequest request);

    /**
     * 修改群备注
     *
     * @param chatId 群聊Id
     * @param userId 企微用户Id
     * @param remark 备注
     * @return 是否修改成功
     */
    boolean updateGroupRemark(String chatId, String userId, String remark);

    /**
     * 过滤群管理任务管理的群聊
     *
     * @param wechatGroupVOList 微信群聊列表
     * @param manageGroupTaskId 群管理任务Id
     * @return 过滤结果
     */
    List<WechatGroupVO> filterManageTaskGroupList(List<WechatGroupVO> wechatGroupVOList, Long manageGroupTaskId);

    /**
     * 查询id大于minId的size条记录，按照id进行排序
     */
    List<WechatGroupVO> listByMinIdLimit(Long minId, Integer size, boolean onlyQueryOwner);

    /**
     * 获取用户关注的群聊chatId列表
     * 【关注】：比邻ISV概念词
     * 在比邻侧只有关注了群聊成员才能够收到群聊的实时消息回调
     *
     * @param userId 企微成员
     * @param corpId 组织ID
     * @return chatId列表
     */
    List<String> getUserWorkChatIdList(String userId, String corpId);

    /**
     * 查询关注群聊的成员ID列表
     *
     * @param chatId 群聊ID
     * @param corpId 组织ID
     * @return 关注群聊的成员ID列表
     */
    List<String> groupWorkUserIdList(String chatId, String corpId);

    /**
     * 操作群聊关注状态
     *
     * @param userId 成员
     * @param corpId 组织ID
     * @param chatId 群聊ID
     * @param status 关注状态
     * @return 结果
     */
    ResultDO<Void> operateGroupWork(String userId, String corpId, String chatId, Boolean status);
}
