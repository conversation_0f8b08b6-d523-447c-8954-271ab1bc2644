package com.alibaba.tripscrm.service.middleware.metaq.consumer.system;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.dal.model.domain.data.SpaceResourceDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.space.SpaceResourceService;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkRobotSingleMessageUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import com.tmall.beehive.common.lang.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-08 17:26:58
 */
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Service(value = "scrmAlarmConsumer")
public class ScrmAlarmConsumer implements MessageListenerConcurrently {
    private final WechatUserService wechatUserService;
    private final WechatGroupService wechatGroupService;
    private final SpaceResourceService spaceResourceService;
    private final TaskService taskService;
    private final SpaceService spaceService;
    private final DingTalkRobotSingleMessageUtils dingTalkRobotSingleMessageUtils;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logFail("receive", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("dealWithMessage fail", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logFail("receive message", LogListUtil.newArrayList(message));
            JSONObject jsonObject = JSONObject.parseObject(message);
            String type = MapUtils.getString(jsonObject, "type", null);
            // 暂定只有任务和机器人两种告警
            if ("robot".equalsIgnoreCase(type)) {
                return handleRobot(jsonObject);
            }
            return handleTask(jsonObject);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        }
    }

    private boolean handleTask(JSONObject jsonObject) throws Exception {
        Long taskId = MapUtils.getLong(jsonObject, "taskId", null);
        String alarmMessage = MapUtils.getString(jsonObject, "message", null);
        if (!NumberUtils.validLong(taskId)) {
            return false;
        }

        TaskInfoDO taskInfoDO = taskService.queryTaskById(taskId);
        if (Objects.isNull(taskInfoDO)) {
            return false;
        }

        String title = "SCRM任务调度：" + alarmMessage;
        String desc = "SCRM任务调度：" + alarmMessage;
        desc += "，任务ID：" + taskId;
        desc += "，任务名称：" + taskInfoDO.getName();

        String empId = Arrays.stream(taskInfoDO.getCreator().split("-")).filter(Strings::isNumeric).findAny().orElse(null);
        dingTalkRobotSingleMessageUtils.send(null, Lists.newArrayList(empId), title, desc);
        return true;
    }

    private boolean handleRobot(JSONObject jsonObject) throws Exception {
        String corpId = MapUtils.getString(jsonObject, "corpId", null);
        String scene = MapUtils.getString(jsonObject, "scene", null);
        String userId = MapUtils.getString(jsonObject, "userId", null);
        String chatId = MapUtils.getString(jsonObject, "chatId", null);
        String alarmMessage = MapUtils.getString(jsonObject, "message", null);

        List<SpaceResourceDO> spaceResourceDOS = spaceResourceService.listByResourceTypeAndResourceId(ResourceTypeEnum.WECHAT_USER, userId);
        if (CollectionUtils.isEmpty(spaceResourceDOS)) {
            return false;
        }

        List<Long> spaceIdList = spaceResourceDOS.stream().map(SpaceResourceDO::getSpaceId).collect(Collectors.toList());
        List<Long> corpSpaceIdList = spaceIdList.stream().filter(spaceId -> Objects.equals(corpId, spaceService.getCorpIdBySpaceId(spaceId))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spaceIdList)) {
            return false;
        }

        SpaceResourceDO spaceResourceDO = spaceResourceDOS.stream().filter(spaceResource -> corpSpaceIdList.contains(spaceResource.getSpaceId())).findAny().orElse(null);
        if (Objects.isNull(spaceResourceDO)) {
            return false;
        }

        String adminList = StringUtils.hasLength(spaceResourceDO.getAdminList()) ? spaceResourceDO.getAdminList() : "";
        String memberList = StringUtils.hasLength(spaceResourceDO.getMemberList()) ? spaceResourceDO.getMemberList() : "";
        List<String> adminIdList = Arrays.stream(adminList.split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());
        List<String> memberIdList = Arrays.stream(memberList.split(",")).filter(StringUtils::hasLength).collect(Collectors.toList());
        List<String> empIdList = Lists.newArrayList(adminIdList, memberIdList).stream().flatMap(Collection::stream).distinct().filter(Strings::isNumeric).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(empIdList)) {
            return false;
        }

        String title = "SCRM机器人监控：" + alarmMessage;
        String desc = "SCRM机器人监控：" + alarmMessage;

        if (StringUtils.hasText(userId)) {
            WechatUserQuery wechatUserQuery = new WechatUserQuery();
            wechatUserQuery.setCorpId(corpId);
            wechatUserQuery.setUserIdList(Lists.newArrayList(userId));
            List<WechatUserDTO> wechatUserList = wechatUserService.listByCondition(wechatUserQuery);
            if (!CollectionUtils.isEmpty(wechatUserList)) {
                WechatUserDTO wechatUser = wechatUserList.get(0);
                desc += "，企微账号UserId：" + wechatUser.getUserId();
                desc += "，企微账号名称：" + wechatUser.getName();
            }
        }

        if (StringUtils.hasText(chatId)) {
            WechatGroupVO wechatGroup = wechatGroupService.getWechatGroupVOByChatId(chatId);
            if (Objects.nonNull(wechatGroup)) {
                desc += "，企微客户群ChatId：" + wechatGroup.getChatId();
                desc += "，企微客户群名称：" + wechatGroup.getName();
            }
        }

        dingTalkRobotSingleMessageUtils.send(scene, empIdList, title, desc);
        return true;
    }
}
