package com.alibaba.tripscrm.service.service.strategy.material;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.enums.material.MaterialContentTypeEnum;
import com.alibaba.tripscrm.service.enums.material.MaterialSupplyTypeEnum;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.material.MaterialSupplyDTO;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.dto.material.SubscribeMsgDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class SubscribeMsgConverter extends AbstractMaterialConverter<SubscribeMsgDTO> {
    @Override
    SubscribeMsgDTO getContentDTO(String content, MaterialTrackRelationDTO materialTrackRelationDTO, MaterialContentConvertContext context) {
        return null;
    }

    @Override
    public List<MaterialSupplyDTO> getMaterialSupplyList(String content) {
        return null;
    }

    @Override
    List<MessageBO> buildMessageBO(SubscribeMsgDTO materialDTO, MaterialContentConvertContext context, String sceneType) {
        return null;
    }

    @Override
    List<WxMessageBO> buildWxMessageBO(SubscribeMsgDTO materialDTO, MaterialContentConvertContext context, Boolean sendMessage) {
        return null;
    }

    @Override
    public MaterialContentTypeEnum contentType() {
        return MaterialContentTypeEnum.SUBSCRIBE_MSG;
    }

    public String buildSubscribeMsgTemplateContent(String content, String paramType) {
        if (StringUtils.isBlank(content) || StringUtils.isBlank(paramType)) {
            PlatformLogUtil.logFail("处理参数内容、埋点失败，参数非法", LogListUtil.newArrayList(content, paramType));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        MaterialSupplyTypeEnum materialSupplyTypeEnum = MaterialSupplyTypeEnum.getSupplyType(paramType);
        if (Objects.isNull(materialSupplyTypeEnum)) {
            PlatformLogUtil.logFail("处理参数内容、埋点失败，供给类型不存在", LogListUtil.newArrayList(paramType));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        // 文本类型直接返回
        if (Objects.equals(MaterialSupplyTypeEnum.TEXT, materialSupplyTypeEnum)) {
            return content;
        }
        Map<String, String> paramMap = JSONObject.parseObject(content, new TypeReference<Map<String, String>>() {});

        String resultContent;
        switch (materialSupplyTypeEnum) {
            case NON_PACKAGE_HOTEL_LISTING:
                resultContent = getNonPackageHotelListingUrl(paramMap);
                break;
            case DMESTIC_FLIGHT_LISTING:
                resultContent = getDemsticFlightListingUrl(paramMap);
                break;
            case INTERNATIONAL_FLIGHT_LISTING:
                resultContent = getInternationalTicketListingUrl(paramMap);
                break;
            default:
                return content;
        }
        return resultContent;
    }
}
