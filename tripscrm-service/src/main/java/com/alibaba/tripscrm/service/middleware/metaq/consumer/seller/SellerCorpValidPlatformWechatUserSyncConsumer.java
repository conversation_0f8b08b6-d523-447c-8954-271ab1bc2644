package com.alibaba.tripscrm.service.middleware.metaq.consumer.seller;


import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.middleware.hts.WechatGroupPoolRefreshTaskExecutor;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.service.seller.SellerCorpValidPlatformWechatUserService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * topic: scrm_seller_corp_valid_platform_wechat_user_sync
 * consumerId: CID_scrm_seller_corp_valid_platform_wechat_user_sync
 *
 * <AUTHOR>
 * @since 2024/6/6 10:46
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerCorpValidPlatformWechatUserSyncConsumer implements MessageListenerConcurrently {
    private final SellerCorpValidPlatformWechatUserService sellerCorpValidPlatformWechatUserService;
    private final WechatGroupPoolRefreshTaskExecutor wechatGroupPoolRefreshTaskExecutor;
    private final WechatUserService wechatUserService;

    @Switch(description = "创建群聊最大延迟秒数", name = "createGroupDelaySeconds")
    public static Integer createGroupDelaySeconds = 1800;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt msg : msgs) {
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("更新商家企业平台运营组企微号可用状态消息，处理失败", LogListUtil.newArrayList(receivedMsg));
                continue;
            }
            PlatformLogUtil.logFail("更新商家企业平台运营组企微号可用状态消息，处理完成", LogListUtil.newArrayList(receivedMsg));
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            if (!StringUtils.hasLength(message)) {
                PlatformLogUtil.logFail("更新商家企业平台运营组企微号可用状态消息为空");
                return false;
            }
            PlatformLogUtil.logInfo("更新商家企业平台运营组企微号可用状态消息", message);
            SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.sellerCorpId);
            JSONObject jsonMessage = JSONObject.parseObject(message);
            String wechatUserId = MapUtils.getString(jsonMessage, "wechatUserId");
            if (!StringUtils.hasLength(wechatUserId)) {
                PlatformLogUtil.logFail("更新商家企业平台运营组企微号可用状态消息，wechatUserId为空", LogListUtil.newArrayList(message));
                return false;
            }

            WechatUserQuery query = new WechatUserQuery();
            query.setCorpId(SwitchConfig.sellerCorpId);
            query.setSpaceId(SwitchConfig.sellerCorpPlatformSpaceId);
            query.setUserIdList(Lists.newArrayList(wechatUserId));
            List<WechatUserDTO> wechatUserList = wechatUserService.listValidWechatUserByCondition(query);
            if (CollectionUtils.isEmpty(wechatUserList)) {
                PlatformLogUtil.logFail("更新商家企业平台运营组企微号可用状态消息，企微号不属于用户运营平台组", LogListUtil.newArrayList());
                return true;
            }

            sellerCorpValidPlatformWechatUserService.sync(wechatUserId);
            Date executeTime = new Date(System.currentTimeMillis() + ThreadLocalRandom.current().nextLong(createGroupDelaySeconds * 1000L));
            wechatGroupPoolRefreshTaskExecutor.register(SwitchConfig.sellerCorpId, wechatUserId, executeTime);
            return true;
        } catch (Exception e) {
            PlatformLogUtil.logInfo("更新商家企业平台运营组企微号可用状态消息，执行出错", e.getMessage(), e, LogListUtil.newArrayList(message));
            return false;
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }
}
