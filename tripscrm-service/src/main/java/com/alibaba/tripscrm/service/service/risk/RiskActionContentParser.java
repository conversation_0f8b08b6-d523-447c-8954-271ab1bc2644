package com.alibaba.tripscrm.service.service.risk;

import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionConfig;
import com.alibaba.tripscrm.service.model.domain.risk.RiskActionHitConfig;
import com.taobao.hsf.invocation.Invocation;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 行动项调用内容限流解析器抽象类，用于解析不同行动项的限流内容
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public abstract class RiskActionContentParser {
    private static final Map<String, RiskActionContentParser> PARSER_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    public void init0() {
        PARSER_MAP.put(riskAction().getActionCode(), this);
    }

    public static RiskActionContentParser get(RiskActionEnum action) {
        return PARSER_MAP.get(action.getActionCode());
    }

    protected abstract RiskActionEnum riskAction();

    /**
     * 获取风控配置：风控配置应该是什么样
     *
     * @return 风控配置
     */
    public abstract RiskActionConfig buildConfig();

    /**
     * 命中风控配置：命中风控配置应该是什么样
     *
     * @return 风控配置
     */
    public abstract RiskActionHitConfig hitRiskConfig(String corpId, String userId, RiskActionConfig configValue, Invocation invocation);
}

