package com.alibaba.tripscrm.service.service.task.base;

import com.alibaba.tripscrm.dal.model.domain.data.TaskInstanceDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskInstanceQuery;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.model.domain.request.TaskInstanceSaveRequest;
import com.alibaba.tripscrm.service.model.vo.task.TaskInstanceExecuteResultVO;

import java.util.List;

/**
 * 主任务实例Service
 *
 * <AUTHOR>
 */
public interface TaskInstanceService {
    /**
     * 根据条件查询最新记录
     */
    TaskInstanceDO getNewest(TaskInstanceQuery query);

    /**
     * 获取任务实例执行结果
     *
     * @param id
     * @return
     */
    TaskInstanceExecuteResultVO getExecuteResult(Long id);

    /**
     * 新增主任务实例信息
     *
     * @param request
     * @return 主键ID
     */
    Long add(TaskInstanceSaveRequest request);

    /**
     * 根据主键ID查询
     *
     * @param id 主键ID
     * @return
     */
    TaskInstanceDO queryById(Long id);

    /**
     * 查询列表（走本地缓存）
     *
     * @param query 查询条件
     * @return
     */
    List<TaskInstanceDO> listWithCache(TaskInstanceQuery query);

    /**
     * 查询列表,不带分页
     *
     * @param query 查询条件
     * @return
     */
    List<TaskInstanceDO> list(TaskInstanceQuery query);

    /**
     * 更新主任务实例执行状态
     *
     * @param id                 主键ID
     * @param taskInstanceStatus 执行状态
     * @return
     */
    Boolean updateStatus(Long id, TaskInstanceStatusEnum taskInstanceStatus);

    /**
     * 下线主任务实例
     *
     * @param taskId                任务id
     * @param newTaskInstanceStatus 实例新状态
     */
    void offlineTaskInstanceByTaskId(Long taskId, TaskInstanceStatusEnum newTaskInstanceStatus);

    /**
     * 根据主键ID删除
     *
     * @param id 主键ID
     * @return
     */
    Integer deleteById(Long id);

    /**
     * 根据任务实例id获取任务Id
     *
     * @return 任务Id
     */
    Long getTaskIdByTaskInstanceId(Long taskId);

    /**
     * 根据任务id更新主任务实例信息
     *
     * @param taskInstanceDO                 主键ID
     * @return
     */
    Boolean updateByPrimaryKey(TaskInstanceDO taskInstanceDO);
}
