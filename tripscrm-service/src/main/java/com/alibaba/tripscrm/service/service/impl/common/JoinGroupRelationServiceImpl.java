package com.alibaba.tripscrm.service.service.impl.common;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ResourceRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatJoinGroupDO;
import com.alibaba.tripscrm.dal.model.domain.result.GroupMemberCountResult;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.service.convert.ResourceRelationConverter;
import com.alibaba.tripscrm.service.enums.system.LogFieldEnum;
import com.alibaba.tripscrm.service.enums.system.LogShowTypeEnum;
import com.alibaba.tripscrm.service.enums.system.OperationLogStatusEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationWayEnum;
import com.alibaba.tripscrm.service.enums.wechat.MemberTypeEnum;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.log.SopLogContentBO;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.domain.request.JoinGroupRelationRequest;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupRelationVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.common.JoinGroupRelationService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.log.OperationLogInfoService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatJoinGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.log.OperatorLogUtils;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/5 20:48
 **/
@Service
public class JoinGroupRelationServiceImpl implements JoinGroupRelationService {

    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private ResourceRelationService resourceRelationService;
    @Resource
    private OperationLogInfoService operationLogInfoService;
    @Resource
    private AccountService accountService;
    @Resource
    private GroupRelationRepository groupRelationRepository;
    @Resource
    private ResourceRelationConverter resourceRelationConverter;
    @Resource
    private WechatJoinGroupService wechatJoinGroupService;


    @Override
    public PageResultVO<WechatGroupRelationVO> list(ResourceRelationQuery query) {
        ArrayList<WechatGroupRelationVO> result = new ArrayList<>();
        Long sumCount = resourceRelationService.count(query);
        // 资源关系
        List<ResourceRelationDO> resourceRelationDOList = resourceRelationService.query(query);
        if (CollectionUtils.isEmpty(resourceRelationDOList)) {
            return PageResultVO.successResult(Lists.newArrayList(), sumCount.intValue(), query.getPageNum(), query.getPageSize());
        }
        // 资源实体
        List<String> chatIdList = resourceRelationDOList.stream().map(ResourceRelationDO::getTargetId).collect(Collectors.toList());
        Map<String, ResourceRelationDO> chatIdInfoMap = resourceRelationDOList.stream().collect(Collectors.toMap(ResourceRelationDO::getTargetId, resourceRelationDO -> resourceRelationDO));
        List<WechatGroupVO> wechatGroupVOList = wechatGroupService.listByChatIdList(chatIdList, false);
        if (CollectionUtils.isEmpty(wechatGroupVOList)) {
            return PageResultVO.successResult(Lists.newArrayList(), sumCount.intValue(), query.getPageNum(), query.getPageSize());
        }
        // 绑定列表
        List<String> groupCodeBindGroupList = new ArrayList<>();
        if (StringUtils.isNotBlank(query.getState())) {
            WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getByState(query.getState());
            if (wechatJoinGroupDO != null) {
                groupCodeBindGroupList = Arrays.stream(Optional.ofNullable(wechatJoinGroupDO.getChatIdList()).orElse("").split(",")).collect(Collectors.toList());
            }
        }
        // 对象整合
        for (WechatGroupVO wechatGroupVO : wechatGroupVOList) {
            WechatGroupRelationVO wechatGroupRelationVO = new WechatGroupRelationVO();
            wechatGroupRelationVO.setChatId(wechatGroupVO.getChatId());
            wechatGroupRelationVO.setGroupName(wechatGroupVO.getName());
            wechatGroupRelationVO.setWay(chatIdInfoMap.get(wechatGroupVO.getChatId()).getWay());
            wechatGroupRelationVO.setDate(chatIdInfoMap.get(wechatGroupVO.getChatId()).getGmtCreate());
            wechatGroupRelationVO.setInGroupCount(wechatGroupVO.getUserCount());
            wechatGroupRelationVO.setId(chatIdInfoMap.get(wechatGroupVO.getChatId()).getId());
            wechatGroupRelationVO.setGroupCodeBindingGroupCount(groupCodeBindGroupList.size());
            wechatGroupRelationVO.setIsBindingGroup(groupCodeBindGroupList.contains(wechatGroupVO.getChatId()));
            List<GroupMemberCountResult> groupMemberCountResults = groupRelationRepository.countByShardingKeyList(
                    Lists.newArrayList(groupRelationRepository.chatId2ShardingKey(wechatGroupVO.getChatId())),
                    Lists.newArrayList(MemberTypeEnum.EnterpriseMember.getType(), MemberTypeEnum.ExternalContact.getType()) ,
                    SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
            if (CollectionUtils.isNotEmpty(groupMemberCountResults)) {
                wechatGroupRelationVO.setInGroupCount(groupMemberCountResults.get(0).getCount());
            }
            wechatGroupRelationVO.setEnterGroupCount(wechatGroupService.getGroupCustomerWithFollowUserCount(Lists.newArrayList(wechatGroupVO.getChatId()), query.getState()));
            result.add(wechatGroupRelationVO);
        }
        return PageResultVO.successResult(result, sumCount.intValue(), query.getPageNum(), query.getPageSize());
    }

    @Override
    public Boolean add(ResourceRelationWayEnum way, String resourceId, String targetId) {
        return resourceRelationService.add(resourceRelationConverter.buildGroupManageTaskResourceRelation(resourceId, targetId, way));
    }

    @Override
    public Boolean delete(Long id) {
        return resourceRelationService.deleteById(id);
    }

    @Override
    public Boolean update(JoinGroupRelationRequest request) {
        ArrayList<SopLogContentBO> sopLogContentList = new ArrayList<>();
        ArrayList<ResourceRelationDO> addRelationList = new ArrayList<>();
        ResourceRelationWayEnum wayEnum = ResourceRelationWayEnum.codeOf(request.getWay());
        // 新增
        if (CollectionUtils.isNotEmpty(request.getAddTargetIdList())) {
            sopLogContentList.add(
                    OperatorLogUtils.buildSopLogContent(LogFieldEnum.BINDING_GROUP.getCode(),
                            LogShowTypeEnum.ADD, null, JSONObject.toJSONString(request.getAddTargetIdList()))
            );
            for (String chatId : request.getAddTargetIdList()) {
                addRelationList.add(
                        resourceRelationConverter.buildGroupManageTaskResourceRelation(request.getResourceId(), chatId, wayEnum)
                );
            }
            resourceRelationService.batchAdd(addRelationList);
        }
        // 移除
        if (CollectionUtils.isNotEmpty(request.getRemoveIdList())) {
            // 落操作日志
            sopLogContentList.add(
                    OperatorLogUtils.buildSopLogContent(LogFieldEnum.BINDING_GROUP.getCode(),
                            LogShowTypeEnum.REMOVE, null, JSONObject.toJSONString(request.getRemoveTargetIdList()))
            );
            removeBindingGroup(request.getRemoveTargetIdList(), request.getState(), request.getRemoveIdList());
        }
        // 日志
        User user = accountService.getUserInWebThread();
        if (user != null && CollectionUtils.isNotEmpty(sopLogContentList)) {
            operationLogInfoService.insert(
                    OperatorLogUtils.buildActivityTypeLogDO(user.getUserId(), user.getUserName(),
                            String.valueOf(request.getActivityId()), OperationLogStatusEnum.FAIL, sopLogContentList)
            );
        }
        return true;
    }

    /**
     * 移除绑定群聊
     * @param chatIdList chatId列表
     * @param state state
     */
    private void removeBindingGroup(List<String> chatIdList, String state, List<Long> needRemoveIdList) {
        if (CollectionUtils.isEmpty(chatIdList) || CollectionUtils.isEmpty(needRemoveIdList)) {
            return ;
        }
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getByState(state);
        if (wechatJoinGroupDO == null || StringUtils.isBlank(wechatJoinGroupDO.getChatIdList())) {
            PlatformLogUtil.logFail("GroupCodeRelationService.filterOnlineBindingChatIdList.error", LogListUtil.newArrayList(state));
            resourceRelationService.batchDelete(needRemoveIdList);
            return ;
        }
        List<String> onlineAllGroupList = Arrays.stream(wechatJoinGroupDO.getChatIdList().split(",")).collect(Collectors.toList());
        List<String> onlineBindingChatIdList =onlineAllGroupList
                .stream()
                .filter(chatIdList::contains)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(onlineBindingChatIdList)) {
            resourceRelationService.batchDelete(needRemoveIdList);
            return ;
        }
        // 低限制校验/同步企微
        if (onlineAllGroupList.size() - onlineBindingChatIdList.size() <= 0) {
            return ;
        }
        wechatJoinGroupService.modifyChatIdList(wechatJoinGroupDO.getId(), Lists.newArrayList(), onlineBindingChatIdList);
        resourceRelationService.batchDelete(needRemoveIdList);
    }

}
