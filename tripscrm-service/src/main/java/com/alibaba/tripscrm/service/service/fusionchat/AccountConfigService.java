package com.alibaba.tripscrm.service.service.fusionchat;

import com.alibaba.tripscrm.service.model.domain.fusionchat.AccountFusionChatConfigBody;

import java.util.Map;

/**
 * 平台账号配置 Manager
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
public interface AccountConfigService {
    /**
     * 保存聚合聊天置顶配置
     *
     * @param accountId accountId
     * @param userId userId
     * @param top top
     */
    void saveFusionChatConfig(String accountId, String userId, Boolean top);

    /**
     * 获取聚合聊天置顶配置
     *
     * @param accountId accountId
     * @return return
     */
    Map<String, AccountFusionChatConfigBody> queryFusionChatConfig(String accountId);
}