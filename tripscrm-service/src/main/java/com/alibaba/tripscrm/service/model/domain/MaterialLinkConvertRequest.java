package com.alibaba.tripscrm.service.model.domain;

import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/8 18:55
 */
@Data
public class MaterialLinkConvertRequest {
    /**
     * 原始链接
     */
    private String original;
    /**
     * 目标链接类型
     */
    private LinkTypeEnum targetLinkType;
    /**
     * 是否转化短链
     */
    private boolean convertShortLink;
    /**
     * 短链标题
     */
    private String title;
    /**
     * 是否微信内使用
     */
    private boolean useInWechat;
    /**
     * 自定义参数列表
     */
    private Map<String, String> customizeParamMap = Maps.newHashMap();
    /**
     * 埋点Id
     */
    private String scrmTrackId;
    /**
     * 素材Id
     */
    private Long materialId;

    /**
     * 场景类型
     */
    private String sceneType;
}
