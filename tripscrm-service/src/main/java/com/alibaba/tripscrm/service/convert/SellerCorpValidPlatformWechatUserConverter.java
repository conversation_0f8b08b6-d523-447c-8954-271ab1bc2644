package com.alibaba.tripscrm.service.convert;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.SellerCorpValidPlatformWechatUserDO;
import com.alibaba.tripscrm.service.model.dto.seller.SellerCorpValidPlatformWechatUserDTO;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Service
public class SellerCorpValidPlatformWechatUserConverter {

    /**
     * DTO模型转换成DO模型
     *
     * @param sellerCorpValidPlatformWechatUserDTO
     */
    public SellerCorpValidPlatformWechatUserDO convertFromDTO(SellerCorpValidPlatformWechatUserDTO sellerCorpValidPlatformWechatUserDTO) {
        SellerCorpValidPlatformWechatUserDO sellerCorpValidPlatformWechatUserDO = new SellerCorpValidPlatformWechatUserDO();
        sellerCorpValidPlatformWechatUserDO.setId(sellerCorpValidPlatformWechatUserDTO.getId());
        sellerCorpValidPlatformWechatUserDO.setGmtCreate(sellerCorpValidPlatformWechatUserDTO.getGmtCreate());
        sellerCorpValidPlatformWechatUserDO.setGmtModified(sellerCorpValidPlatformWechatUserDTO.getGmtModified());
        sellerCorpValidPlatformWechatUserDO.setDeleted(sellerCorpValidPlatformWechatUserDTO.getDeleted());
        sellerCorpValidPlatformWechatUserDO.setUserId(sellerCorpValidPlatformWechatUserDTO.getUserId());
        sellerCorpValidPlatformWechatUserDO.setCorpId(sellerCorpValidPlatformWechatUserDTO.getCorpId());
        if (Objects.nonNull(sellerCorpValidPlatformWechatUserDTO.getExtraInfo())) {
            sellerCorpValidPlatformWechatUserDO.setExtraInfo(JSONObject.toJSONString(sellerCorpValidPlatformWechatUserDTO.getExtraInfo()));
        }
        sellerCorpValidPlatformWechatUserDO.setEnv(sellerCorpValidPlatformWechatUserDTO.getEnv());
        return sellerCorpValidPlatformWechatUserDO;
    }

    /**
     * DO模型转换成DTO模型
     *
     * @param sellerCorpValidPlatformWechatUserDO
     */
    public SellerCorpValidPlatformWechatUserDTO convertFromDO(SellerCorpValidPlatformWechatUserDO sellerCorpValidPlatformWechatUserDO) {
        SellerCorpValidPlatformWechatUserDTO sellerCorpValidPlatformWechatUserDTO = new SellerCorpValidPlatformWechatUserDTO();
        sellerCorpValidPlatformWechatUserDTO.setId(sellerCorpValidPlatformWechatUserDO.getId());
        sellerCorpValidPlatformWechatUserDTO.setGmtCreate(sellerCorpValidPlatformWechatUserDO.getGmtCreate());
        sellerCorpValidPlatformWechatUserDTO.setGmtModified(sellerCorpValidPlatformWechatUserDO.getGmtModified());
        sellerCorpValidPlatformWechatUserDTO.setDeleted(sellerCorpValidPlatformWechatUserDO.getDeleted());
        sellerCorpValidPlatformWechatUserDTO.setUserId(sellerCorpValidPlatformWechatUserDO.getUserId());
        sellerCorpValidPlatformWechatUserDTO.setCorpId(sellerCorpValidPlatformWechatUserDO.getCorpId());
        sellerCorpValidPlatformWechatUserDTO.setExtraInfo(sellerCorpValidPlatformWechatUserDO.getExtraInfo());
        sellerCorpValidPlatformWechatUserDTO.setEnv(sellerCorpValidPlatformWechatUserDO.getEnv());
        return sellerCorpValidPlatformWechatUserDTO;
    }
}