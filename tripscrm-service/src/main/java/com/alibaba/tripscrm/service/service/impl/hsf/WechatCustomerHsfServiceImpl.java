package com.alibaba.tripscrm.service.service.impl.hsf;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.domain.WechatCustomerInfo;
import com.alibaba.tripscrm.domain.request.WechatCustomerQueryRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.manager.opensearch.CustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.model.domain.query.TagGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerListQuery;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.wechat.WechatCustomerHsfService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@HSFProvider(serviceInterface = WechatCustomerHsfService.class)
public class WechatCustomerHsfServiceImpl implements WechatCustomerHsfService {

    @Resource
    private CustomerRelationOpenSearchManager customerRelationOpenSearchManager;

    @Resource
    private WechatCustomerService wechatCustomerService;

    @Resource
    private TagInfoService tagInfoService;

    @Resource
    private TagGroupService tagGroupService;

    @Override
    public TripSCRMResult<List<WechatCustomerInfo>> wechatCustomerQuery(WechatCustomerQueryRequest request) {
        List<WechatCustomerInfo> resultList = new ArrayList<>();
        WechatCustomerListQuery query = new WechatCustomerListQuery();
        BeanUtils.copyProperties(request, query);
        PageInfo<String> pageInfo = customerRelationOpenSearchManager.getCustomerExternalUserIdList(query);
        List<WechatCustomerVO> wechatCustomerVOList = wechatCustomerService.listByExternalUserIdList(pageInfo.getList());
        if (CollectionUtils.isEmpty(wechatCustomerVOList)) {
            return TripSCRMResult.success(resultList);
        }
        for (WechatCustomerVO wechatCustomerVO : wechatCustomerVOList) {
            WechatCustomerInfo wechatCustomerInfo = new WechatCustomerInfo();
            List<WechatCustomerInfo.InnerTagInfo> userTagList = new ArrayList<>();
            wechatCustomerInfo.setUserTagList(userTagList);
            wechatCustomerInfo.setUnionId(wechatCustomerVO.getUnionId());
            resultList.add(wechatCustomerInfo);
            List<TagInfoDTO> tagInfoDTOList = tagInfoService.selectByTagIdList(wechatCustomerVO.getTagIdList());
            if (CollectionUtils.isEmpty(tagInfoDTOList)) {
                continue;
            }
            List<Long> groupIds = tagInfoDTOList.stream().map(TagInfoDTO::getGroupId).collect(Collectors.toList());
            TagGroupQuery tagGroupQuery = new TagGroupQuery();
            tagGroupQuery.setIds(groupIds);
            List<TagGroupDTO> groupDTOList = tagGroupService.selectByCondition(tagGroupQuery);
            if (CollectionUtils.isEmpty(groupDTOList)) {
                continue;
            }
            for (TagInfoDTO tagInfoDTO : tagInfoDTOList) {
                TagGroupDTO tagGroupDTO = groupDTOList.stream().filter(item -> Objects.equals(item.getId(), tagInfoDTO.getGroupId())).findAny().orElse(null);
                if (tagGroupDTO == null) {
                    continue;
                }
                WechatCustomerInfo.InnerTagInfo innerTagInfo = new WechatCustomerInfo.InnerTagInfo();
                innerTagInfo.setTagValue(tagInfoDTO.getName());
                innerTagInfo.setTagGroupName(tagGroupDTO.getName());
                userTagList.add(innerTagInfo);
            }
        }
        return TripSCRMResult.success(resultList);
    }
}
