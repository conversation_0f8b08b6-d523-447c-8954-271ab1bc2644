package com.alibaba.tripscrm.service.service.task.ability.old;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ActivityInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.TaskContext;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.system.MonitorErrorEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.EventMetaInfo;
import com.alibaba.tripscrm.service.model.domain.context.TaskBuildContext;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.activity.ActivityInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.log.task.TaskCoreLogUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * scrm任务模版
 *
 * <AUTHOR>
 * @date 2023/4/14
 */
@Slf4j
@Service
public abstract class AbstractTaskTemplate {

    @Resource
    private ActivityContextService activityContextService;

    @Resource
    private ActivityInfoService activityInfoService;

    public Long buildTask(TaskBuildContext taskBuildContext) {
        // 兼容老逻辑
        taskBuildContext.setTaskInfoDO(compatibleBiz(taskBuildContext.getTaskInfoDO()));
        Long contextId = 0L;
        // 查询活动信息
        ActivityInfoDO activityInfoDO = activityInfoService.queryInfoById(taskBuildContext.getActivityId());
        // 构建活动上下文信息
        ActivityTaskInfoBO taskInfoBO = buildTaskInfo(taskBuildContext);
        if (Objects.isNull(activityInfoDO)) {
            //没有活动无需保存上下文
            PlatformLogUtil.logFail("queryInfoById fail", LogListUtil.newArrayList(taskBuildContext));
            TaskCoreLogUtils.log(MonitorErrorEnum.NO_NEED_ACTIVITY_CONTEXT_SAVE, taskBuildContext.getTaskInfoDO().getType(), taskBuildContext.getTaskInfoDO().getId(), taskBuildContext.getTargetType(), taskBuildContext.getTargetId(), String.format("no activity %s", taskBuildContext.getActivityId()));
        }else{
            // 保存上下文信息
            contextId = saveContext(taskInfoBO);
            if (!NumberUtils.validLong(contextId)) {
                PlatformLogUtil.logFail("saveContext fail", LogListUtil.newArrayList(taskBuildContext));
                TaskCoreLogUtils.log(MonitorErrorEnum.FAIL_ACTIVITY_CONTEXT_SAVE, taskBuildContext.getTaskInfoDO().getType(), taskBuildContext.getTaskInfoDO().getId(),  taskBuildContext.getTargetType(), taskBuildContext.getTargetId(), String.format("saveContext fail %s", taskBuildContext.getActivityId()));
                return contextId;
            }
        }
        if (!taskExe(taskInfoBO, taskBuildContext)) {
            PlatformLogUtil.logFail("taskExe fail", LogListUtil.newArrayList(taskBuildContext));
            TaskCoreLogUtils.log(MonitorErrorEnum.FAIL_EXE_TASK, taskBuildContext.getTaskInfoDO().getType(), taskBuildContext.getTaskInfoDO().getId(),  taskBuildContext.getTargetType(), taskBuildContext.getTargetId(), String.format("saveContext fail %s", taskBuildContext.getActivityId()));
        }
        TaskCoreLogUtils.log(MonitorErrorEnum.TASK_EXE_COUNT, taskBuildContext.getTaskInfoDO().getType(), taskBuildContext.getTaskInfoDO().getId(),  taskBuildContext.getTargetType(), taskBuildContext.getTargetId(), String.format("contextId %s", contextId));
        return contextId;
    }

    public Long saveContext(ActivityTaskInfoBO taskInfoBO) {
        try {
            if (activityContextService.upsert(taskInfoBO) < 1) {
                PlatformLogUtil.logFail("activityTaskInfoService upsert fail", LogListUtil.newArrayList(taskInfoBO));
                return null;
            }
            // 执行活动
            return taskInfoBO.getContextId();
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(taskInfoBO));
            return null;
        }
    }

    private ActivityTaskInfoBO buildTaskInfo(TaskBuildContext taskBuildContext) {
        ActivityTaskInfoBO taskInfoBO = new ActivityTaskInfoBO();
        taskInfoBO.setContextId(activityContextService.generateContextId());
        taskInfoBO.setActivityId(taskBuildContext.getActivityId());
        taskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.codeOf(taskBuildContext.getTargetType()));
        taskInfoBO.setTargetId(taskBuildContext.getTargetId());
        String tagId = Objects.nonNull(taskBuildContext.getSubCode()) ? (taskBuildContext.getTagId() + "_" + taskBuildContext.getSubCode()) : String.valueOf(taskBuildContext.getTagId());
        taskInfoBO.setTagIdList(Lists.newArrayList(tagId));
        JSONObject exJson = JSONObject.parseObject(taskBuildContext.getContext());
        taskInfoBO.setExtraJson(Objects.isNull(exJson)? new JSONObject():exJson);
        if (!taskInfoBO.getExtraJson().containsKey("dataTime")) {
            taskInfoBO.getExtraJson().put("dataTime", System.currentTimeMillis());
        }
        return taskInfoBO;
    }

    /**
     * 字符串替换
     * @param content
     * @param extInfo
     * @return
     */
    protected String valReplace(String content, JSONObject extInfo) {
        if (content.contains("${")) {
            int fromIndex = 0;
            while (content.indexOf("${", fromIndex) >= 0) {
                int start = content.indexOf("${", fromIndex);
                int end = content.indexOf("}", start + 1);
                if (end == -1) {
                    break;
                }
                String pkey = content.substring(start, end + 1);
                String pValue = pkey.substring(2, pkey.length() - 1).trim();
                // 替换动态变量
                String finalValue = extInfo.getString(pValue);
                if (StringUtils.isBlank(finalValue)) {
                    PlatformLogUtil.logFail("cannot replace all value", LogListUtil.newArrayList(content, extInfo));
                    return null;
                }
                content = content.replace(pkey, finalValue);
                fromIndex = end + (finalValue.length() - pkey.length()) + 1;
            }
        }
        return content;
    }

    private TaskInfoDO compatibleBiz(TaskInfoDO taskInfoDO){
        if (Objects.isNull(taskInfoDO)){
            return new TaskInfoDO();
        }
        return taskInfoDO;
    }

    protected abstract boolean taskExe(ActivityTaskInfoBO taskInfoBO, TaskBuildContext taskBuildContext);

    protected abstract EventMetaInfo getCallBackInfo(Long activityId, EventMetaInfo eventMetaInfo, String unionId);

    protected abstract TaskType getTaskType();

    protected abstract TaskContext getContext(JSONObject contextJson);

    protected abstract void setTaskFactory();

}
