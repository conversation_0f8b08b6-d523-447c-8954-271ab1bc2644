package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.middleware.hts.WechatGroupPoolRefreshTaskExecutor;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupPoolQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatGroupPoolDTO;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupPoolService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripzoo.proxy.enums.WechatUserStatusEnum;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @since 2025/4/13 17:48
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerCorpWechatGroupPoolRefreshProcessor extends JavaProcessor {
    private final WechatUserService wechatUserService;
    private final WechatGroupPoolService wechatGroupPoolService;
    private final WechatGroupPoolRefreshTaskExecutor wechatGroupPoolRefreshTaskExecutor;

    @Switch(description = "创建群聊最大延迟秒数", name = "createGroupDelaySeconds")
    public static Integer createGroupDelaySeconds = 3000;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            SpaceInfoThreadLocalUtils.setCorpId(SwitchConfig.sellerCorpId);
            WechatUserQuery query = new WechatUserQuery();
            query.setCorpId(SwitchConfig.sellerCorpId);
            query.setSpaceId(SwitchConfig.sellerCorpPlatformSpaceId);
            query.setStatus(WechatUserStatusEnum.ACTIVE.getCode().byteValue());
            List<WechatUserDTO> wechatUserList = wechatUserService.listValidWechatUserByCondition(query);
            if (CollectionUtils.isEmpty(wechatUserList)) {
                PlatformLogUtil.logFail("定时任务，刷新同步商家企业用户运营组群聊池，企微号列表为空", LogListUtil.newArrayList());
                return new ProcessResult(true);
            }

            wechatGroupPoolService.syncAll();
            int validCount = 0;
            for (WechatUserDTO wechatUserDTO : wechatUserList) {
                validCount += refresh(wechatUserDTO.getUserId());
            }

            if (validCount < SwitchConfig.sellerCorpValidGroupCountForAlarm) {
                DingTalkApi.sendTaskMessage(String.format("【商家企微】可用群聊数量不足，当前可用群聊数量:%d", validCount));
            }
            PlatformLogUtil.logInfo("定时任务，刷新同步商家企业用户运营组群聊池，同步完毕", LogListUtil.newArrayList());
            return new ProcessResult(true);
        } catch (Exception e) {
            PlatformLogUtil.logException("定时任务，刷新同步商家企业用户运营组群聊池，发生异常", e.getMessage(), e, LogListUtil.newArrayList());
            return new ProcessResult(false);
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
    }

    public int refresh(String userId) {
        WechatGroupPoolQuery wechatGroupPoolQuery = new WechatGroupPoolQuery();
        wechatGroupPoolQuery.setOwner(userId);
        wechatGroupPoolQuery.setCorpId(SwitchConfig.sellerCorpId);
        wechatGroupPoolQuery.setDeleted((byte) 0);
        wechatGroupPoolQuery.setStatus((byte) 0);
        List<WechatGroupPoolDTO> wechatGroupPoolList = wechatGroupPoolService.listByCondition(wechatGroupPoolQuery);
        if (wechatGroupPoolList.size() >= SwitchConfig.sellerCorpPlatformUserMinValidGroupCountForPool) {
            PlatformLogUtil.logInfo("定时任务，刷新同步商家企业用户运营组群聊池，企微账号下可用群聊池数量充足", LogListUtil.newArrayList(userId, wechatGroupPoolList.size()));
            return wechatGroupPoolList.size();
        }

        int newGroupPoolCount = SwitchConfig.sellerCorpPlatformUserMinValidGroupCountForPool - wechatGroupPoolList.size();
        PlatformLogUtil.logInfo("定时任务，刷新同步商家企业用户运营组群聊池，企微账号下可用群聊池数量不足，开始提交创建新群聊池hts任务", LogListUtil.newArrayList(userId, wechatGroupPoolList.size(), newGroupPoolCount));

        for (int i = 0; i < Math.min(SwitchConfig.wechatGroupCreateCountPerHour, newGroupPoolCount); i++) {
            Date executeTime = new Date(System.currentTimeMillis() + ThreadLocalRandom.current().nextLong(createGroupDelaySeconds * 1000L));
            wechatGroupPoolRefreshTaskExecutor.register(SwitchConfig.sellerCorpId, userId, executeTime);
        }
        PlatformLogUtil.logInfo("定时任务，刷新同步商家企业用户运营组群聊池，企微账号下可用群聊池数量不足，提交创建新群聊池hts任务完毕", LogListUtil.newArrayList(userId, wechatGroupPoolList.size(), newGroupPoolCount));
        return wechatGroupPoolList.size();
    }
}
