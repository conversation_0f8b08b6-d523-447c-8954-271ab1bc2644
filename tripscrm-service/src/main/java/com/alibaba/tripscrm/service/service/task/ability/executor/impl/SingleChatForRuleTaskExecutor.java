package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.MaterialSendSceneTypeConstant;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-11 13:56:06
 */
@Slf4j
@Service
public class SingleChatForRuleTaskExecutor extends SingleChatTaskExecutor {
    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        // 只处理第一条
        TaskDataVO.DataBodyVO taskDataBody = todoTaskVO.getData().get(0);
        if (taskDataBody.getContext().containsKey("sendUserId")) {
            return (String) taskDataBody.getContext().get("sendUserId");
        }

        JSONObject taskExtraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        String sendUserIdListStr = context.getTaskInfoDOSnapshot().getSendUserId();
        if (StringUtils.isBlank(sendUserIdListStr)) {
            sendUserIdListStr = ((String) taskExtraInfoJson.getOrDefault("robotUser", ""));
        }
        List<String> userIdList = Arrays.stream(Optional.ofNullable(sendUserIdListStr).orElse("").split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setRiskActionEnum(RiskActionEnum.CUSTOMER_SEND_MESSAGE);
        isvRouteContext.setExternalUserId(getExternalUserId(context, todoTaskVO));
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        isvRouteContext.setUserIdList(userIdList);
        TripSCRMResult<WechatUserDTO> result = isvRouteService.matchWechatUser(isvRouteContext);
        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData())) {
            PlatformLogUtil.logFail("私聊消息发送失败，任务配置的机器人中获取sendUserId失败", LogListUtil.newArrayList(isvRouteContext, result, context.getTaskId()));
            throw new TripscrmException(TripSCRMErrorCode.ROBOT_USER_OF_TASK_OFFLINE_OR_WITHOUT_FRIENDSHIP_WITH_CUSTOMER);
        }

        taskDataBody.getContext().put("sendUserId", result.getData());
        return result.getData().getUserId();
    }

    @Override
    protected String getScene() {
        return MaterialSendSceneTypeConstant.SILIAO_RW_FOR_RULE;
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.ROBOT_CHAT_MESSAGE_FOR_RULE;
    }
}
