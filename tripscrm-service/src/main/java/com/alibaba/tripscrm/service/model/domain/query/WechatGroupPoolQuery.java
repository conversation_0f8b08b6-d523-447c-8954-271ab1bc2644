package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class WechatGroupPoolQuery implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 是否删除
     */
    private Byte deleted;

    /**
     * 状态，0未使用，1已使用
     */
    private Byte status;

    /**
     * 群主userId
     */
    private String owner;

    /**
     * 企微corpId
     */
    private String corpId;

    /**
     * 群聊Id
     */
    private List<String> chatIdList;
}