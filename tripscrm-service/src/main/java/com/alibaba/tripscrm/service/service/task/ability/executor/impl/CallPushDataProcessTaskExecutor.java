package com.alibaba.tripscrm.service.service.task.ability.executor.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.CallPushActivityRecordDO;
import com.alibaba.tripscrm.dal.model.domain.data.ScrmBlackListDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.callpush.AddWechatCustomerByMsgStageEnum;
import com.alibaba.tripscrm.service.enums.callpush.AddWechatCustomerByPhoneStageEnum;
import com.alibaba.tripscrm.service.enums.callpush.CallStageEnum;
import com.alibaba.tripscrm.service.enums.callpush.MsgStageEnum;
import com.alibaba.tripscrm.service.enums.system.BlackListTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.CallPushDataUpdateEventTypeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.query.CallPushActivityRecordCondition;
import com.alibaba.tripscrm.service.model.domain.query.CallPushActivityRecordQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.model.vo.task.TaskDataVO;
import com.alibaba.tripscrm.service.model.vo.task.TodoTaskVO;
import com.alibaba.tripscrm.service.service.ScrmBlackListService;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import com.alibaba.tripscrm.service.service.callpush.CallPushActivityRecordService;
import com.alibaba.tripscrm.service.service.task.ability.executor.AbstractTaskExecutor;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripzoo.proxy.enums.CustomerAddWayEnum;
import com.fliggy.pokemon.lock.client.api.TairLockManager;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

/**
 * 外呼数据处理任务执行器
 *
 * <AUTHOR>
 * @since 2024/8/2 18:51
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CallPushDataProcessTaskExecutor extends AbstractTaskExecutor {
    private final ScrmBlackListService scrmBlackListService;
    private final CallPushActivityRecordService callPushActivityRecordService;
    private final MetaqProducer metaqProducer;
    private final TairLockManager tairLockManager;
    private final ActivityContextService activityContextService;

    private final Map<CallPushDataUpdateEventTypeEnum, BiConsumer<TaskExecuteContext, Long>> PROCESS_FUNCTION_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        PROCESS_FUNCTION_MAP.put(CallPushDataUpdateEventTypeEnum.CALL_PHONE_DECRYPT, this::processCallPhoneDecrypt);
        PROCESS_FUNCTION_MAP.put(CallPushDataUpdateEventTypeEnum.SEND_MSG_PHONE_DECRYPT, this::processSendMsgPhoneDecrypt);
        PROCESS_FUNCTION_MAP.put(CallPushDataUpdateEventTypeEnum.CALL_RESULT, this::processCallResult);
        PROCESS_FUNCTION_MAP.put(CallPushDataUpdateEventTypeEnum.SEND_MSG_RESULT, this::processSendMsgResult);
        PROCESS_FUNCTION_MAP.put(CallPushDataUpdateEventTypeEnum.VISIT_LANDING_PAGE, this::processVisitLandingPage);
        PROCESS_FUNCTION_MAP.put(CallPushDataUpdateEventTypeEnum.ADD_CUSTOMER_BY_PHONE, this::processAddCustomerByPhone);
        PROCESS_FUNCTION_MAP.put(CallPushDataUpdateEventTypeEnum.ADD_CUSTOMER_SUCCESS, this::processAddCustomerSuccess);
    }

    @Override
    public void doBiz(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        TaskDataVO.DataBodyVO dataBodyVO = todoTaskVO.getData().get(0);
        CallPushDataUpdateEventTypeEnum callPushDataUpdateEventTypeEnum = CallPushDataUpdateEventTypeEnum.of(context.getExtInfo().getOrDefault("eventType", "").toString());
        if (Objects.isNull(callPushDataUpdateEventTypeEnum)) {
            PlatformLogUtil.logFail("eventType非法", LogListUtil.newArrayList());
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        Long callPushActivityRecordId = getCallPushActivityRecordId(context, dataBodyVO);
        PlatformLogUtil.logInfo("外呼数据处理任务执行器, 处理数据更新事件", LogListUtil.newArrayList(callPushActivityRecordId, callPushDataUpdateEventTypeEnum, context.getTaskId()));
        // 更新数据要加锁，并且外呼推送数据也要在加锁后查询
        tairLockManager.tryRunWithLock("callPushDataProcess_" + callPushActivityRecordId,
                3000L,
                2,
                () -> PROCESS_FUNCTION_MAP.get(callPushDataUpdateEventTypeEnum).accept(context, callPushActivityRecordId),
                () -> PlatformLogUtil.logFail("外呼数据处理任务执行器，获取锁失败", LogListUtil.newArrayList(callPushActivityRecordId))
        );
    }

    private void processCallPhoneDecrypt(TaskExecuteContext context, Long callPushActivityRecordId) {
        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setId(callPushActivityRecordId);
        callPushActivityRecordDO.setCallStage(CallStageEnum.CALL_DECRYPT_SUCCESS.getCode());
        callPushActivityRecordService.updateById(callPushActivityRecordDO);
    }

    private void processSendMsgPhoneDecrypt(TaskExecuteContext context, Long callPushActivityRecordId) {
        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setId(callPushActivityRecordId);
        callPushActivityRecordDO.setMsgStage(MsgStageEnum.MSG_DECRYPT_SUCCESS.getCode());
        callPushActivityRecordService.updateById(callPushActivityRecordDO);
    }

    private void processVisitLandingPage(TaskExecuteContext context, Long callPushActivityRecordId) {
        if (Objects.isNull(context.getExtInfo().get("unionId"))) {
            PlatformLogUtil.logFail("unionId非法", LogListUtil.newArrayList());
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        String unionId = (String) context.getExtInfo().get("unionId");
        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setId(callPushActivityRecordId);
        callPushActivityRecordDO.setUnionId(unionId);
        callPushActivityRecordDO.setAddByMsgStage(AddWechatCustomerByMsgStageEnum.ENTER_JOIN_PAGE.getCode());

        // 只有初始化状态才会被修改
        CallPushActivityRecordCondition condition = CallPushActivityRecordCondition.builder()
                .id(callPushActivityRecordId)
                .addByMsgStage(Lists.newArrayList(AddWechatCustomerByMsgStageEnum.INIT.getCode()))
                .build();
        callPushActivityRecordService.updateByCondition(callPushActivityRecordDO, condition);
        createUnionIdActivityContext(callPushActivityRecordId);
    }

    private void processAddCustomerByPhone(TaskExecuteContext context, Long callPushActivityRecordId) {
        if (Objects.isNull(context.getExtInfo().get("result"))) {
            PlatformLogUtil.logFail("result非法", LogListUtil.newArrayList());
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        Boolean result = (Boolean) context.getExtInfo().get("result");
        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setId(callPushActivityRecordId);
        callPushActivityRecordDO.setAddByPhoneStage(result ? AddWechatCustomerByPhoneStageEnum.SEND_REQUEST_SUCCESS.getCode() : AddWechatCustomerByPhoneStageEnum.SEND_REQUEST_FAIL.getCode());
        callPushActivityRecordService.updateById(callPushActivityRecordDO);
    }

    private void processAddCustomerSuccess(TaskExecuteContext context, Long callPushActivityRecordId) {
        Integer addWay = (Integer) context.getExtInfo().get("addWay");
        if (!Lists.newArrayList(CustomerAddWayEnum.SEARCH_MOBILE_NUMBER.getCode(), CustomerAddWayEnum.SCAN_QR_CODE.getCode()).contains(addWay)) {
            return;
        }

        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setId(callPushActivityRecordId);

        if (Objects.equals(CustomerAddWayEnum.SEARCH_MOBILE_NUMBER.getCode(), addWay)) {
            callPushActivityRecordDO.setAddByPhoneStage(AddWechatCustomerByPhoneStageEnum.ADD_SUCCESS.getCode());
        } else {
            callPushActivityRecordDO.setAddByMsgStage(AddWechatCustomerByMsgStageEnum.JOIN_SUCCESS.getCode());
        }
        callPushActivityRecordService.updateById(callPushActivityRecordDO);
    }

    private void processSendMsgResult(TaskExecuteContext context, Long callPushActivityRecordId) {
        if (Objects.isNull(context.getExtInfo().get("callbackContent"))) {
            PlatformLogUtil.logFail("callbackContent非法", LogListUtil.newArrayList());
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        Boolean result = Boolean.parseBoolean(context.getExtInfo().get("callbackContent").toString());
        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setId(callPushActivityRecordId);
        callPushActivityRecordDO.setMsgStage(result ? MsgStageEnum.MSG_SUCCESS.getCode() : MsgStageEnum.MSG_FAIL.getCode());
        callPushActivityRecordService.updateById(callPushActivityRecordDO);
    }

    private void processCallResult(TaskExecuteContext context, Long callPushActivityRecordId) {
        if (Objects.isNull(context.getExtInfo().get("callbackContent")) || Objects.isNull(context.getExtInfo().get("callStage"))) {
            PlatformLogUtil.logFail("callbackContent或callStage非法", LogListUtil.newArrayList());
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        CallStageEnum callStageEnum = CallStageEnum.of(Byte.valueOf(String.valueOf(context.getExtInfo().get("callStage"))));
        JSONObject callbackContent = (JSONObject) context.getExtInfo().get("callbackContent");
        CallPushActivityRecordDO oldCallPushActivityRecordDO = getCallPushActivityRecordDO(callPushActivityRecordId);

        if (Objects.isNull(callStageEnum)) {
            PlatformLogUtil.logFail("callStage非法", LogListUtil.newArrayList());
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        CallPushActivityRecordDO callPushActivityRecordDO = new CallPushActivityRecordDO();
        callPushActivityRecordDO.setId(callPushActivityRecordId);
        callPushActivityRecordDO.setCallStage(callStageEnum.getCode());
        callPushActivityRecordDO.setCallBackContent(callbackContent.toJSONString());
        callPushActivityRecordService.updateById(callPushActivityRecordDO);

        Boolean hitBlackWord = Optional.ofNullable(context.getExtInfo().get("hitBlackWord")).map(o -> (Boolean) o).orElse(false);
        // 命中敏感词，需要更新黑名单
        if (hitBlackWord) {
            saveBlackList(callPushActivityRecordId, String.valueOf(oldCallPushActivityRecordDO.getUid()), oldCallPushActivityRecordDO.getCellPhone());
            return;
        }

        // 主加好友
        addCustomerByPhone(context, callStageEnum, oldCallPushActivityRecordDO.getCellPhone());
    }

    private void addCustomerByPhone(TaskExecuteContext context, CallStageEnum callStageEnum, String cellPhone) {
        if (Objects.isNull(context.getTaskInfoDOSnapshot().getExtInfo())) {
            PlatformLogUtil.logFail("主加好友条件为空", LogListUtil.newArrayList());
            return;
        }

        JSONObject extraInfoJson = JSONObject.parseObject(context.getTaskInfoDOSnapshot().getExtInfo());
        // 主加好友条件为空
        if (!extraInfoJson.containsKey(TaskConstant.ADD_CUSTOMER_BY_PHONE_CONDITION_LIST)) {
            PlatformLogUtil.logFail("主加好友条件为空", LogListUtil.newArrayList());
            return;
        }

        List<Byte> addCustomerByPhoneConditionList = extraInfoJson.getObject(TaskConstant.ADD_CUSTOMER_BY_PHONE_CONDITION_LIST, new TypeReference<List<Byte>>() {
        });
        if (CollectionUtils.isEmpty(addCustomerByPhoneConditionList)) {
            PlatformLogUtil.logFail("主加好友条件为空", LogListUtil.newArrayList());
            return;
        }

        // 发送手机号添加好友消息
        if (addCustomerByPhoneConditionList.contains(callStageEnum.getCode())) {
            JSONObject message = new JSONObject();
            message.put("cellPhone", cellPhone);
            message.put("corpId", SpaceInfoThreadLocalUtils.getCorpId());
            metaqProducer.send(MQEnum.NOTIFY_WECHAT_USER_ADD_CUSTOMER_BY_PHONE, cellPhone, "", message.toJSONString());
        }
    }

    private void saveBlackList(Long callPushActivityRecordId, String uid, String cellPhone) {
        PlatformLogUtil.logFail("处理外呼成功回调，命中黑名单", LogListUtil.newArrayList(callPushActivityRecordId));

        // 淘宝uid落黑名单
        ScrmBlackListDO scrmBlackListDO = new ScrmBlackListDO();
        scrmBlackListDO.setTargetId(uid);
        scrmBlackListDO.setTargetType(ActivityTargetTypeEnum.TAOBAO_USER_ID.getCode().byteValue());
        scrmBlackListDO.setType(BlackListTypeEnum.CALL_PUSH.getCode());
        scrmBlackListDO.setReason("hitBlackWord");
        scrmBlackListDO.setDeleted((byte) 0);
        scrmBlackListService.upsert(scrmBlackListDO);

        // 手机号落黑名单
        scrmBlackListDO = new ScrmBlackListDO();
        scrmBlackListDO.setTargetId(cellPhone);
        scrmBlackListDO.setTargetType(ActivityTargetTypeEnum.PHONE_MOBILE.getCode().byteValue());
        scrmBlackListDO.setType(BlackListTypeEnum.CALL_PUSH.getCode());
        scrmBlackListDO.setReason("hitBlackWord");
        scrmBlackListDO.setDeleted((byte) 0);
        scrmBlackListService.upsert(scrmBlackListDO);
    }

    private void createUnionIdActivityContext(Long callPushActivityRecordId) {
        // 活动上下文信息
        CallPushActivityRecordDO callPushActivityRecordDO = getCallPushActivityRecordDO(callPushActivityRecordId);
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(activityContextService.generateContextId());
        activityTaskInfoBO.setActivityId(callPushActivityRecordDO.getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.WX_UNION_ID);
        activityTaskInfoBO.setTargetId(callPushActivityRecordDO.getUnionId());
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("dataTime", System.currentTimeMillis());
        extraJson.put("callPushActivityRecordId", callPushActivityRecordDO.getId());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);
    }

    @Override
    protected String getSendUserId(TaskExecuteContext context, TodoTaskVO todoTaskVO) {
        return null;
    }

    @Override
    public ActivityTargetTypeEnum getFinalTargetType(TaskExecuteContext context) {
        return ActivityTargetTypeEnum.TAOBAO_USER_ID;
    }

    @Override
    public String getFinalTargetId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        // 只处理第一条
        if (taskDataBody.getContext().containsKey("uid")) {
            return (String) taskDataBody.getContext().get("uid");
        }

        if (!StringUtils.hasText(taskDataBody.getTargetId())) {
            PlatformLogUtil.logFail("targetId为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.TASK_TARGET_DATA_NOT_FOUND);
        }

        CallPushActivityRecordDO callPushActivityRecordDO = getCallPushActivityRecordDO(getCallPushActivityRecordId(context, taskDataBody));
        String uid = String.valueOf(callPushActivityRecordDO.getUid());
        taskDataBody.getContext().put("uid", uid);
        return uid;
    }

    private Long getCallPushActivityRecordId(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        ActivityTaskInfoBO activityContext = getActivityContext(context, taskDataBody);
        if (Objects.isNull(activityContext)) {
            PlatformLogUtil.logFail("活动上下文为空", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.ACTIVITY_CONTEXT_EMPTY);
        }

        Map<String, Object> extraJson = activityContext.getExtraJson();
        // 是否已经发送过个人欢迎语
        Long callPushActivityRecordId = MapUtils.getLong(extraJson, "callPushActivityRecordId", -1L);
        if (callPushActivityRecordId <= 0) {
            PlatformLogUtil.logFail("活动上下文中缺少外呼推送记录Id", LogListUtil.newArrayList(taskDataBody));
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED);
        }

        return callPushActivityRecordId;
    }

    private CallPushActivityRecordDO getCallPushActivityRecordDO(Long id) {
        CallPushActivityRecordQuery query = CallPushActivityRecordQuery.builder()
                .id(id)
                .build();
        List<CallPushActivityRecordDO> callPushActivityRecordDOS = callPushActivityRecordService.selectByCondition(query);
        if (CollectionUtils.isEmpty(callPushActivityRecordDOS)) {
            PlatformLogUtil.logFail("外呼推送记录不存在于数据库中", LogListUtil.newArrayList(id));
            throw new TripscrmException(TripSCRMErrorCode.PROCESS_FAILED);
        }

        return callPushActivityRecordDOS.get(0);
    }

    @Override
    protected ActivityTaskInfoBO getActivityContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        Map<String, Object> extInfo = context.getExtInfo();

        Long contextId = -1L;
        if (extInfo.containsKey(TaskConstant.CONTEXT_ID) && StringUtils.hasText(String.valueOf(extInfo.getOrDefault(TaskConstant.CONTEXT_ID, "")))) {
            contextId = NumberUtils.toLong(String.valueOf(extInfo.get(TaskConstant.CONTEXT_ID)), -1L);
            // contextId非法
            if (contextId < 0L) {
                PlatformLogUtil.logFail("外呼数据处理任务执行器，contextId非法", LogListUtil.newArrayList(taskDataBody));
                return null;
            }
        }

        Long activityId = context.getTaskInfoDOSnapshot().getActivityId();
        ActivityTaskInfoBO activityTaskInfoBO = null;
        // 指定了上下文id
        if (contextId > 0) {
            activityTaskInfoBO = activityContextService.queryByTaskId(contextId);
        }

        if (Objects.isNull(activityTaskInfoBO) || !Objects.equals(activityTaskInfoBO.getActivityId(), activityId)) {
            // 根据 targetId + targetType + activityId 查询上下文
            activityTaskInfoBO = getTargetActivityContext(context, taskDataBody);
            if (Objects.isNull(activityTaskInfoBO) || CollectionUtils.isEmpty(activityTaskInfoBO.getExtraJson()) || !activityTaskInfoBO.getExtraJson().containsKey("dataTime")) {
                return activityTaskInfoBO;
            }

            Long dataTime = MapUtils.getLong(activityTaskInfoBO.getExtraJson(), "dataTime", 0L);
            if (Math.abs(System.currentTimeMillis() - dataTime) > SwitchConfig.targetActivityContextExpireSeconds * 1000L) {
                PlatformLogUtil.logFail("外呼数据处理任务执行器，上下文已过期", LogListUtil.newArrayList(taskDataBody, activityTaskInfoBO));
                return null;
            }
        }

        // 上下文不存在
        if (Objects.isNull(activityTaskInfoBO)) {
            PlatformLogUtil.logFail("外呼数据处理任务执行器，上下文id在lindorm不存在", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        // 非当前活动
        if (!Objects.equals(activityTaskInfoBO.getActivityId(), activityId)) {
            PlatformLogUtil.logFail("外呼数据处理任务执行器，非当前活动", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        return activityTaskInfoBO;
    }

    @Override
    protected ActivityTaskInfoBO getTargetActivityContext(TaskExecuteContext context, TaskDataVO.DataBodyVO taskDataBody) {
        Long activityId = context.getTaskInfoDOSnapshot().getActivityId();
        ActivityTargetTypeEnum targetType = Optional.ofNullable(context.getExtInfo().get("targetType")).map(a -> (Integer) a).map(ActivityTargetTypeEnum::codeOf).orElse(ActivityTargetTypeEnum.TAOBAO_USER_ID);
        if (!Lists.newArrayList(ActivityTargetTypeEnum.TAOBAO_USER_ID, ActivityTargetTypeEnum.PHONE_MOBILE, ActivityTargetTypeEnum.WX_UNION_ID).contains(targetType)) {
            return null;
        }

        // 根据 targetId + targetType + activityId 查询上下文
        ActivityTaskInfoBO query = new ActivityTaskInfoBO();
        query.setActivityId(activityId);
        String targetId = (String) context.getExtInfo().get("targetId");
        query.setTargetTypeEnum(targetType);
        query.setTargetId(targetId);
        List<ActivityTaskInfoBO> targetActivityTaskInfoBOS = activityContextService.queryByActivityAndTarget(query);
        if (CollectionUtils.isEmpty(targetActivityTaskInfoBOS)) {
            PlatformLogUtil.logFail("外呼数据处理任务执行器，根据 targetId + targetType + activityId 查询上下文为空", LogListUtil.newArrayList(taskDataBody));
            return null;
        }

        return ActivityContextService.getNewestBo(targetActivityTaskInfoBOS);
    }

    @Override
    protected TaskType getTaskType() {
        return TaskType.CALL_PUSH_DATA_PROCESS;
    }
}
