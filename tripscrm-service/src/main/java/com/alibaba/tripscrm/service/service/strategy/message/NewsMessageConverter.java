package com.alibaba.tripscrm.service.service.strategy.message;

import com.ali.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.MaterialJsonKeyConstant;
import com.alibaba.tripscrm.service.model.domain.context.MaterialContentConvertContext;
import com.alibaba.tripscrm.service.model.dto.message.MessageTransferDTO;
import com.alibaba.tripscrm.service.model.dto.message.NewsMessageInfoDTO;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.WxAttachmentTypeEnum;
import com.alibaba.tripzoo.proxy.request.MessageBO;
import com.alibaba.tripzoo.proxy.request.WxMessageBO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/22 14:35
 **/
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class NewsMessageConverter extends AbstractMessageConverter<NewsMessageInfoDTO>{
    @Override
    NewsMessageInfoDTO convert(MessageTransferDTO messageTransferDTO) {
        NewsMessageInfoDTO newsMessageInfoDTO = new NewsMessageInfoDTO();
        if (messageTransferDTO == null || messageTransferDTO.getContent() == null) {
            return newsMessageInfoDTO;
        }
        JSONObject messageContent = messageTransferDTO.getContent();
        if (!messageContent.containsKey(MaterialJsonKeyConstant.url)) {
            return newsMessageInfoDTO;
        }
        newsMessageInfoDTO.setUrl(messageContent.getString(MaterialJsonKeyConstant.url));
        if (!messageContent.containsKey(MaterialJsonKeyConstant.title)) {
            return newsMessageInfoDTO;
        }
        newsMessageInfoDTO.setTitle(messageContent.getString(MaterialJsonKeyConstant.title));
        if (!messageContent.containsKey(MaterialJsonKeyConstant.picture)) {
            return newsMessageInfoDTO;
        }
        List<String> pictureUrlList = messageContent.getObject(MaterialJsonKeyConstant.picture, new TypeReference<List<String>>() {
        });
        if (CollectionUtils.isNotEmpty(pictureUrlList)) {
            newsMessageInfoDTO.setPicture(pictureUrlList.get(0));
        }
        if (!messageContent.containsKey(MaterialJsonKeyConstant.desc)) {
            return newsMessageInfoDTO;
        }
        newsMessageInfoDTO.setDesc(messageContent.getString(MaterialJsonKeyConstant.desc));
        return newsMessageInfoDTO;
    }

    @Override
    List<MessageBO> buildMessageBO(NewsMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, Integer messageIndex) {
        MessageBO messageBO = new MessageBO();
        messageBO.setMsgType(MessageTypeEnum.NEWS);
        messageBO.setHref(messageInfoDTO.getUrl());
        messageBO.setTitle(messageInfoDTO.getTitle());
        messageBO.setDesc(messageInfoDTO.getDesc());
        messageBO.setMsgContent(messageInfoDTO.getPicture());
        messageBO.setMsgNum(messageIndex);
        return Lists.newArrayList(messageBO);
    }

    @Override
    List<WxMessageBO> buildWxMessageBO(NewsMessageInfoDTO messageInfoDTO, MaterialContentConvertContext context, TaskType taskType) {
        WxMessageBO wxMessageBO = new WxMessageBO();
        wxMessageBO.setMsgType(WxAttachmentTypeEnum.LINK);
        wxMessageBO.setTitle(messageInfoDTO.getTitle());
        wxMessageBO.setPicUrl(messageInfoDTO.getPicture());
        wxMessageBO.setDesc(messageInfoDTO.getDesc());
        wxMessageBO.setPathUrl(messageInfoDTO.getUrl());
        return Lists.newArrayList(wxMessageBO);
    }

    @Override
    public MessageTypeEnum getMsgType() {
        return MessageTypeEnum.NEWS;
    }
}
