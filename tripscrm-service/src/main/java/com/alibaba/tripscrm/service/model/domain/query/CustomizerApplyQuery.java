package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.ValidGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/21
 */
@Data
public class CustomizerApplyQuery{
    /**
    * 账号主键id
    */
    @NotNull(message = "账号主键不能为空", groups = {ValidGroup.Insert.class, ValidGroup.Update.class})
    @ApiModelProperty(value = "主键ID", required = true)
    private Long id;

    @NotNull(message = "用户ID", groups = {ValidGroup.Insert.class, ValidGroup.Update.class})
    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;

    @NotBlank(message = "账号名称不能为空", groups = {ValidGroup.Insert.class, ValidGroup.Update.class})
    @ApiModelProperty(value = "账号主键", required = true)
    private String name;

    /**
     * 目的地信息集合
     */
    @NotNull(message = "目的地信息不能为空", groups = {ValidGroup.Insert.class, ValidGroup.Update.class})
    private List<DestinationQuery> destinationQueryList;

    @Data
    public static class DestinationQuery {
        /**
         * 目的地ID
         */
        @NotNull(message = "目的地ID不能为空", groups = {ValidGroup.Insert.class, ValidGroup.Update.class})
        @ApiModelProperty(value = "目的地ID", required = true)
        private Long id;

        /**
         * 目的地名称
         */
        @NotBlank(message = "目的地名称不能为空", groups = {ValidGroup.Insert.class, ValidGroup.Update.class})
        @ApiModelProperty(value = "目的地名称", required = true)
        private String name;
    }
}
