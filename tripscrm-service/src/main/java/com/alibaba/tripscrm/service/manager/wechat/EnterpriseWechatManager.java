package com.alibaba.tripscrm.service.manager.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.request.AddCustomerByGroupRequest;
import com.alibaba.tripscrm.service.model.domain.request.CreateGroupRequest;
import com.alibaba.tripscrm.service.model.domain.request.SendMsgToCustomerRequest;
import com.alibaba.tripscrm.service.model.domain.request.SendMsgToGroupRequest;
import com.alibaba.tripscrm.service.model.dto.wechat.GroupMessageAtDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.material.MaterialTransferService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.GroupMessageAtUtils;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.api.service.EnterpriseWechatMsgService;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.ErrorCodeEnum;
import com.alibaba.tripzoo.proxy.request.*;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.WechatAddMsgTemplateResponse;
import com.alibaba.tripzoo.proxy.result.WechatTransferCustomerResponse;
import com.alibaba.tripzoo.proxy.result.WechatTransferGroupResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 企业微信管理
 *
 * <AUTHOR>
 * @date 2023/6/1
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class EnterpriseWechatManager {
    private final CustomerService customerService;
    private final GroupService groupService;
    private final EnterpriseWechatMsgService enterpriseWechatMsgService;
    private final MaterialTransferService materialTransferService;

    public TripSCRMResult<WechatAddMsgTemplateResponse> addSingleChatGroupMsgTemplate(WechatAddMsgTemplateRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("企业微信管理，创建企微群发任务，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ResultDO<WechatAddMsgTemplateResponse> result = enterpriseWechatMsgService.addMsgTemplate(request);
        if (Objects.isNull(result)) {
            PlatformLogUtil.logFail("企业微信管理，创建企微群发任务结果为空", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (result.getSuccess()) {
            return TripSCRMResult.success(result.getModel(), result.getResultMessage());
        }

        PlatformLogUtil.logFail("企业微信管理，创建企微群发任务失败", LogListUtil.newArrayList(request, result));
        ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(result.getResultCode());
        switch (errorCodeEnum) {
            default:
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
    }

    public TripSCRMResult<WechatTransferCustomerResponse> transferCustomer(WechatTransferCustomerRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("企业微信管理，进行客户继承，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ResultDO<WechatTransferCustomerResponse> result = customerService.transferCustomer(request);
        if (Objects.isNull(result)) {
            PlatformLogUtil.logFail("企业微信管理，进行客户继承，结果为空", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (result.getSuccess()) {
            return TripSCRMResult.success(result.getModel(), result.getResultMessage());
        }

        PlatformLogUtil.logFail("企业微信管理，进行客户继承，执行失败", LogListUtil.newArrayList(request, result));
        return TripSCRMResult.fail(result.getResultCode(),result.getResultMessage());
    }

    public TripSCRMResult<WechatTransferGroupResponse> transferGroup(WechatTransferGroupRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("企业微信管理，进行群组继承，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ResultDO<WechatTransferGroupResponse> result = groupService.transferGroup(request);
        if (Objects.isNull(result)) {
            PlatformLogUtil.logFail("企业微信管理，进行群组继承，结果为空", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (result.getSuccess()) {
            return TripSCRMResult.success(result.getModel(), result.getResultMessage());
        }

        PlatformLogUtil.logFail("企业微信管理，进行群组继承，执行失败", LogListUtil.newArrayList(request, result));
        ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(result.getResultCode());
        switch (errorCodeEnum) {
            default:
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
    }

    public TripSCRMResult<Boolean> sendPersonWelcomeMessage(SendPersonalWelcomeRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("企业微信管理，发个人欢迎语，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ResultDO<Boolean> result = customerService.sendPersonalWelcome(request);
        if (Objects.isNull(result)) {
            PlatformLogUtil.logFail("企业微信管理，发个人欢迎语，结果为空", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (result.getSuccess()) {
            return TripSCRMResult.success(result.getModel(), result.getResultMessage());
        }

        PlatformLogUtil.logFail("企业微信管理，发个人欢迎语，执行失败", LogListUtil.newArrayList(request, result));
        ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(result.getResultCode());
        switch (errorCodeEnum) {
            case INVALID_WELCOME_CODE:
                return TripSCRMResult.fail(TripSCRMErrorCode.PERSONAL_WELCOME_CODE_INVALID);
            case EXTERNAL_USER_HAS_STARTED_CHATTING:
                return TripSCRMResult.fail(TripSCRMErrorCode.EXTERNAL_USER_HAS_STARTED_CHATTING);
            default:
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
    }

    public TripSCRMResult<String> asyncSendMessageToGroup(SendMsgToGroupRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("企业微信管理，异步发送群聊消息，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WechatGroupSendMsgRequest wechatGroupSendMsgRequest = new WechatGroupSendMsgRequest();
        wechatGroupSendMsgRequest.setUserId(request.getUserId());
        wechatGroupSendMsgRequest.setChatId(request.getChatId());
        wechatGroupSendMsgRequest.setRequestId(request.getRequestId());
        List<MessageBO> messageBOS = materialTransferService.buildMessages(request.getMaterailInfoDO(), request.getMaterialTrackRelationDTO(), request.getMaterialContentConvertContext(), request.getTaskType().getCode());
        for (MessageBO messageBO : messageBOS) {
            GroupMessageAtDTO groupMessageAtDTO = GroupMessageAtUtils.getAllAtList(messageBO.getMsgContent(), request.getMaterialContentConvertContext().getExtraInfo());
            if (CollectionUtils.isEmpty(groupMessageAtDTO.getAtList())) {
                continue;
            }
            messageBO.setMsgContent(groupMessageAtDTO.getContent());
            if (Objects.equals(groupMessageAtDTO.getAtList().get(0).getType(), 0)) {
                messageBO.setAt(1);
                messageBO.setAtLocation(0);
            } else {
                messageBO.setAt(2);
                messageBO.setAtUsers(groupMessageAtDTO.getAtList());
                messageBO.setAtLocation(2);

            }
        }
        wechatGroupSendMsgRequest.setMessageList(messageBOS);
        wechatGroupSendMsgRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        if (!StringUtils.hasLength(wechatGroupSendMsgRequest.getRequestId())) {
            wechatGroupSendMsgRequest.setRequestId(UUID.randomUUID().toString());
        }

        ResultDO<String> resultDO = groupService.asyncSendMessage(wechatGroupSendMsgRequest);
        if (Objects.isNull(resultDO)) {
            PlatformLogUtil.logFail("企业微信管理，异步发送群聊消息，结果为空", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (resultDO.getSuccess()) {
            return TripSCRMResult.success(resultDO.getModel(), resultDO.getResultMessage());
        }

        PlatformLogUtil.logFail("企业微信管理，异步发送群聊消息，执行失败", LogListUtil.newArrayList(request, resultDO));
        ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
        switch (errorCodeEnum) {
            case WECHAT_GROUP_DATA_ERROR:
            case ROBOT_NOT_IN_GROUP:
            case WECHAT_USER_NOT_ONLINE:
                return TripSCRMResult.fail(TripSCRMErrorCode.SEND_MESSAGE_FAIL);
            case RATE_LIMIT_FILTER:
                return TripSCRMResult.fail(TripSCRMErrorCode.GATE_WAY_RATE_LIMIT);
            case ISV_RATE_LIMIT:
                return TripSCRMResult.fail(TripSCRMErrorCode.ISV_RATE_LIMIT);
            case BIZ_ACTION_RISK_LIMIT:
                return TripSCRMResult.fail(TripSCRMErrorCode.BIZ_ACTION_DEGRADE_LIMIT);
            case BIZ_RATE_LIMIT:
                return TripSCRMResult.fail(TripSCRMErrorCode.BIZ_RATE_LIMIT);
            default:
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
    }

    public TripSCRMResult<String> asyncSendMessageToCustomer(SendMsgToCustomerRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("企业微信管理，异步发送私聊消息，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WxCustomerSendMsgRequest wxCustomerSendMsgRequest = new WxCustomerSendMsgRequest();
        wxCustomerSendMsgRequest.setUserId(request.getUserId());
        wxCustomerSendMsgRequest.setUnionId(request.getUnionId());
        wxCustomerSendMsgRequest.setExternalUserId(request.getExternalUserId());
        wxCustomerSendMsgRequest.setIsTest(request.getIsTest());
        wxCustomerSendMsgRequest.setRequestId(request.getRequestId());
        if (!StringUtils.hasLength(wxCustomerSendMsgRequest.getRequestId())) {
            wxCustomerSendMsgRequest.setRequestId(UUID.randomUUID().toString());
        }
        List<MessageBO> messageBOS = materialTransferService.buildMessages(request.getMaterailInfoDO(), request.getMaterialTrackRelationDTO(), request.getMaterialContentConvertContext(), request.getTaskType().getCode());
        wxCustomerSendMsgRequest.setMessageList(messageBOS);
        wxCustomerSendMsgRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());

        ResultDO<String> resultDO;
        try {
            resultDO = customerService.asyncSendMessage(wxCustomerSendMsgRequest);
        } catch (TripscrmException e) {
            PlatformLogUtil.logException("企业微信管理，异步发送私聊消息，执行异常", e.getMessage(), e, LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(e.getErrorCodeEnum(), e.getErrorMsg());
        }

        if (Objects.isNull(resultDO)) {
            PlatformLogUtil.logFail("企业微信管理，异步发送私聊消息，结果为空", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (resultDO.getSuccess()) {
            return TripSCRMResult.success(resultDO.getModel(), resultDO.getResultMessage());
        }

        PlatformLogUtil.logFail("企业微信管理，异步发送私聊消息，执行失败", LogListUtil.newArrayList(request, resultDO));
        ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
        switch (errorCodeEnum) {
            case WECHAT_CUSTOMER_DATA_ERROR:
            case NO_FRIEND_RELATION:
            case WECHAT_USER_NOT_ONLINE:
                return TripSCRMResult.fail(TripSCRMErrorCode.SEND_MESSAGE_FAIL);
            case RATE_LIMIT_FILTER:
                return TripSCRMResult.fail(TripSCRMErrorCode.GATE_WAY_RATE_LIMIT);
            case ISV_RATE_LIMIT:
                return TripSCRMResult.fail(TripSCRMErrorCode.ISV_RATE_LIMIT);
            case BIZ_ACTION_RISK_LIMIT:
                return TripSCRMResult.fail(TripSCRMErrorCode.BIZ_ACTION_DEGRADE_LIMIT);
            case BIZ_RATE_LIMIT:
                return TripSCRMResult.fail(TripSCRMErrorCode.BIZ_RATE_LIMIT);
            default:
                return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }
    }

    public TripSCRMResult<String> asyncCreateGroup(CreateGroupRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("企业微信管理，异步创群，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        GroupCreateRequest groupCreateRequest = new GroupCreateRequest();
        groupCreateRequest.setUserId(request.getUserId());
        groupCreateRequest.setGroupName(request.getGroupName());
        groupCreateRequest.setInitUserIdList(request.getInitUserIdList());
        groupCreateRequest.setRequestId(request.getRequestId());
        groupCreateRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());

        if (Objects.nonNull(request.getMaterailInfoDO())) {
            List<MessageBO> messageBOList = materialTransferService.buildMessages(request.getMaterailInfoDO(), request.getMaterialTrackRelationDTO(), request.getMaterialContentConvertContext(), request.getTaskType().getCode());
            groupCreateRequest.setNotice(CollectionUtils.isEmpty(messageBOList) ? null : messageBOList.get(0).getMsgContent());
        }

        ResultDO<String> resultDO;
        try {
            resultDO = groupService.asyncCreate(groupCreateRequest);
        } catch (TripscrmException e) {
            PlatformLogUtil.logException("企业微信管理，异步创群，执行异常", e.getMessage(), e, LogListUtil.newArrayList(request, groupCreateRequest));
            return TripSCRMResult.fail(e.getErrorCodeEnum(), e.getErrorMsg());
        }

        if (Objects.isNull(resultDO)) {
            PlatformLogUtil.logFail("企业微信管理，异步创群，结果为空", LogListUtil.newArrayList(request, groupCreateRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (resultDO.getSuccess()) {
            return TripSCRMResult.success(resultDO.getModel(), resultDO.getResultMessage());
        }

        PlatformLogUtil.logFail("企业微信管理，异步创群，执行失败", LogListUtil.newArrayList(request, resultDO));
        ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
        return TripSCRMResult.fail(resultDO.getResultCode(), errorCodeEnum.getDescCn());
    }

    public TripSCRMResult<String> asyncAddCustomerByGroup(AddCustomerByGroupRequest request) {
        if (Objects.isNull(request)) {
            PlatformLogUtil.logFail("企业微信管理，异步加群好友，参数非法", LogListUtil.newArrayList());
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WechatGroupAddCustomerRequest wechatGroupAddCustomerRequest = new WechatGroupAddCustomerRequest();
        wechatGroupAddCustomerRequest.setUserId(request.getUserId());
        wechatGroupAddCustomerRequest.setExternalUserId(request.getExternalUserId());
        wechatGroupAddCustomerRequest.setChatId(request.getChatId());
        wechatGroupAddCustomerRequest.setMessage(request.getMessage());
        wechatGroupAddCustomerRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());

        ResultDO<String> resultDO;
        try {
            resultDO = groupService.asyncAddCustomerByGroup(wechatGroupAddCustomerRequest);
        } catch (TripscrmException e) {
            PlatformLogUtil.logException("企业微信管理，异步加群好友，执行异常", e.getMessage(), e, LogListUtil.newArrayList(request, wechatGroupAddCustomerRequest));
            return TripSCRMResult.fail(e.getErrorCodeEnum(), e.getErrorMsg());
        }

        if (Objects.isNull(resultDO)) {
            PlatformLogUtil.logFail("企业微信管理，异步加群好友，结果为空", LogListUtil.newArrayList(request, wechatGroupAddCustomerRequest));
            return TripSCRMResult.fail(TripSCRMErrorCode.PROCESS_FAILED);
        }

        if (resultDO.getSuccess()) {
            return TripSCRMResult.success(resultDO.getModel(), resultDO.getResultMessage());
        }

        PlatformLogUtil.logFail("企业微信管理，异步加群好友，执行失败", LogListUtil.newArrayList(request, wechatGroupAddCustomerRequest));
        ErrorCodeEnum errorCodeEnum = ErrorCodeEnum.codeOf(resultDO.getResultCode());
        return TripSCRMResult.fail(resultDO.getResultCode(), errorCodeEnum.getDescCn());
    }
}
