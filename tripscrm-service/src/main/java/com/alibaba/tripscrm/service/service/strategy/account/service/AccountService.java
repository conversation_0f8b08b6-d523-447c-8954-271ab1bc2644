package com.alibaba.tripscrm.service.service.strategy.account.service;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.UserInfoVO;
import com.alibaba.tripscrm.service.service.strategy.account.AccountManager;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/14 17:58
 **/
@Slf4j
@Component
public class AccountService {

    @Resource
    private List<AccountManager> accountManagers;

    /**
     * 获取当前请求的域名
     * @return 当前域名
     */
    private String getCurrentServerName() {
        if (RequestContextHolder.getRequestAttributes() == null) {
            return SwitchConfig.DEFAULT_SERVER_NAME;
        }
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (StringUtils.isBlank(request.getServerName())) {
            return SwitchConfig.DEFAULT_SERVER_NAME;
        }
        return request.getServerName();
    }

    /**
     * 获取当前请求的域名
     * @param request 当前请求
     * @return 域名
     */
    private String getCurrentServerName(ServerHttpRequest request) {
        if (request == null) {
            return SwitchConfig.DEFAULT_SERVER_NAME;
        }
        HttpServletRequest servletRequest = ((ServletServerHttpRequest) request).getServletRequest();
        if (StringUtils.isBlank(servletRequest.getServerName())) {
            return SwitchConfig.DEFAULT_SERVER_NAME;
        }
        return servletRequest.getServerName();
    }

    /**
     * 获取进行处理的账号管理类
     * @return 账号管理类
     */
    private AccountManager getSupportManager() {
        return getSupportManager(getCurrentServerName());
    }

    /**
     * 获取进行处理的账号管理类
     * @param serverName 域名
     * @return 账号管理类
     */
    private AccountManager getSupportManager(String serverName) {
        for (AccountManager accountManager : accountManagers) {
            if (accountManager.support(serverName)) {
                return accountManager;
            }
        }
        return null;
    }

    /**
     * 获取用户信息，只能在web主线程中使用
     * @return 用户信息
     */
    public User getUserInWebThread() {
        AccountManager accountManager = getSupportManager();
        if (accountManager == null) {
            PlatformLogUtil.logFail("获取账号处理器失败");
            return null;
        }
        return accountManager.getUserInWebThread();
    }

    /**
     * 获取用户信息，只能在web主线程中使用
     * @return
     */
    public List<User> getAllUserInWebThread() {
        ArrayList<User> userList = new ArrayList<>();
        for (AccountManager accountManager : accountManagers) {
            User user = accountManager.getUserInWebThread();
            if (user != null) {
                userList.add(user);
            }
        }
        return userList;
    }

    /**
     * 获取用户姓名-账号
     * @return 用户信息
     */
    public String getUserNameAndEmpId() {
        AccountManager accountManager = getSupportManager();
        if (accountManager == null) {
            PlatformLogUtil.logFail("获取账号处理器失败");
            return null;
        }
        return accountManager.getUserNameAndEmpId();
    }

    /**
     * 根据账号获取用户信息
     * @param accountId 账号
     * @return 用户信息
     */
    public UserInfoVO getUserAndAccountIdByAccountId(String accountId) {
        AccountManager accountManager = getSupportManager();
        if (accountManager == null) {
            PlatformLogUtil.logFail("获取账号处理器失败");
            return null;
        }
        return accountManager.getUserAndAccountIdByAccountId(accountId);
    }

    /**
     * 批量根据账号获取用户信息
     * @param accountIds 账号列表
     * @return 用户信息列表
     */
    public List<UserInfoVO> batchGetUserByEmpId(List<String> accountIds) {
        AccountManager accountManager = getSupportManager();
        if (accountManager == null) {
            PlatformLogUtil.logFail("获取账号处理器失败");
            return null;
        }
        return accountManager.batchGetUserByEmpId(accountIds);
    }

    /**
     * 搜索用户信息
     * @param keyword 关键字
     * @return 员工信息
     */
    public List<UserInfoVO> searchUser(String keyword) {
        ArrayList<UserInfoVO> result = new ArrayList<>();
        for (AccountManager accountManager : accountManagers) {
            if (accountManager.supportQuery(getCurrentServerName())) {
                result.addAll(accountManager.searchUser(keyword));
            }
        }
        return result;
    }

    /**
     * 从当前线程中获取用户信息
     * @return UserInfoVO
     */
    public UserInfoVO getUserVoInWebThread() {
        AccountManager accountManager = getSupportManager();
        if (accountManager == null) {
            PlatformLogUtil.logFail("获取账号处理器失败");
            return null;
        }
        return accountManager.getUserVoInWebThread();
    }

    /**
     * 根据用户id获取用户信息
     * @param accountId 账号id
     * @return 用户信息
     */
    @AteyeInvoker(description = "buc/淘宝账号查询", paraDesc = "accountId")
    public User getUserByAccountId(String accountId) {
        if (StringUtils.isEmpty(accountId)) {
            return null;
        }

        for (AccountManager accountManager : accountManagers) {
            User user = accountManager.getUserByAccountId(accountId);
            if (user != null) {
                return user;
            }
        }

        PlatformLogUtil.logFail("invalid accountId", LogListUtil.newArrayList(accountId));
        return null;
    }
}
