package com.alibaba.tripscrm.service.middleware.metaq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.tripscrm.service.CppArticleService;
import com.alibaba.tripscrm.service.manager.opensearch.WxContentOpenSearchManager;
import com.alibaba.tripscrm.service.model.domain.Pair;
import com.alibaba.tripscrm.service.model.domain.query.WxCommunityContentQuery;
import com.taobao.contentplatform.article.base.read.ArticleReadService;
import com.taobao.contentplatform.article.base.read.req.ArticleQueryReq;
import com.taobao.contentplatform.article.base.read.resp.ArticleDTO;
import com.taobao.contentplatform.sdk.base.type.result.Result;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/4/7 23:42
 **/
@Component
public class CppContentChangedMsgConsumer extends AbstractMetaqConsumer {
    @Resource
    private CppArticleService cppArticleService;
    @Resource
    private WxContentOpenSearchManager wxContentOpenSearchManager;
    @Resource
    private ArticleReadService articleReadService;

    @Override
    protected Pair<ConsumeConcurrentlyStatus, String> consume(String tag, byte[] content) {

        Pair<ConsumeConcurrentlyStatus, String> pair = new Pair();
        pair.setFirst(ConsumeConcurrentlyStatus.CONSUME_SUCCESS);
        pair.setSecond("success");

        try {
            String raw = new String(content, StandardCharsets.UTF_8);
            if (StringUtils.isEmpty(raw)) {
                pair.setSecond("empty_data");
                return pair;
            }

            // 把消息转换成上报数据对象
            CppChangedMsg cppChangedMsg = JSON.parseObject(raw, CppChangedMsg.class);
            if (Objects.isNull(cppChangedMsg)) {
                pair.setSecond("empty_data");
                return pair;
            }

            ArticleDTO articleDTO = find(cppChangedMsg.getId());

            // 删除
            if (Objects.isNull(articleDTO) && Objects.equals(cppChangedMsg.getType(), ContentChangeTypeEnum.UPDATE.getCode())) {
                WxCommunityContentQuery query = new WxCommunityContentQuery();
                query.setCppContentId(cppChangedMsg.getId());
                wxContentOpenSearchManager.delete(query);
                return pair;
            }
            // 新增内容
            if (Objects.equals(cppChangedMsg.getType(), ContentChangeTypeEnum.INSERT.getCode())) {
                wxContentOpenSearchManager.update(articleDTO);
            }
            // 更新内容
            if (Objects.equals(cppChangedMsg.getType(), ContentChangeTypeEnum.UPDATE.getCode())) {
                wxContentOpenSearchManager.update(articleDTO);
            }
            return pair;
        } catch (Exception e) {
            pair.setSecond(String.format("biz_exp_%s", e.getMessage()));
            return pair;
        }
    }

    @Override
    protected String topic() {
        return "CPP_CONTENT_FLIGGY_WX_COMMUNITY";
    }

    @Override
    protected String group() {
        return "CID_wx_community_content";
    }

    @Override
    protected String tag() {
        return "content_base_storage_data_changed";
    }

    @Data
    class CppChangedMsg {
        /**
         * 内容ID
         */
        private Long id;
        /**
         * 该内容归属的业务线
         */
        private 	String	bizLine ;
        /**
         * 操作业务线，即当前消息是什么业务线操作发生的
         */
        private String operateBizLine;
        /**
         *
         */
        private String bizCode;
        /**
         * 用户id
         */
        private String userId;
        /**
         * INSERT / UPDATE = 新增/编辑
         */
        private String type;
        /**
         * 内容类型
         */
        private String contentType;
        /**
         * 变更字段
         */
        private Set<String> fields;
    }


    @Getter
    @AllArgsConstructor
    enum ContentStatusEnum {

        EDITING(1, "编辑中"),
        AUDITING(2, "审核中"),
        AUDITING_SUCCESS(3, "审核通过，已上线"),
        AUDITING_FAILED(4, "审核不通过"),
        OFFLINE(5, "下线"),
        AUDITING_SUCCESS_EDITING(6, "已上线（编辑中）"),
        DELETED(7, "已删除"),
        ;

        /**
         * code
         */
        private final Integer code;

        /**
         * 名称
         */
        private final String name;

        /**
         * 根据code查找枚举
         *
         * @param code code
         * @return 枚举，找不到返回null
         */
        public static ContentStatusEnum of(Integer code) {
            return Arrays.stream(values()).filter(x -> Objects.equals(x.code, code)).findFirst().orElse(null);
        }
    }

    @Getter
    @AllArgsConstructor
    enum ContentChangeTypeEnum {

        INSERT("INSERT", "新增"),
        UPDATE("UPDATE", "更新")
        ;

        /**
         * code
         */
        private final String code;

        /**
         * 名称
         */
        private final String name;
    }

    private static final String BIZ_LINE = "fliggywx";

    private ArticleDTO find(Long cppContentId) {
        ArticleQueryReq req = new ArticleQueryReq();
        req.setBizLine(BIZ_LINE);
        req.setId(cppContentId);
        Result<ArticleDTO> result = articleReadService.getById(req);
        if (result.isFail()) {
            throw new RuntimeException(result.getErrMessage());
        }
        return result.getResult();
    }
}
