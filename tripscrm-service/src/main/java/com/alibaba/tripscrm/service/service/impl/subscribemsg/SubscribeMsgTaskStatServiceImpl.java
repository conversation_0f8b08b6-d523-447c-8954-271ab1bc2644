package com.alibaba.tripscrm.service.service.impl.subscribemsg;


import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.model.dto.subscribemsg.MiniProgramSubscribeMsgTaskStatDTO;
import com.alibaba.tripscrm.service.model.query.subscribemsg.MiniProgramSubscribeMsgTaskStatQuery;
import com.alibaba.tripscrm.service.service.subscribemsg.SubscribeMsgTaskStatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小程序订阅消息任务统计服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SubscribeMsgTaskStatServiceImpl implements SubscribeMsgTaskStatService {

    @Override
    public PageInfoDTO<MiniProgramSubscribeMsgTaskStatDTO> pageQuery(MiniProgramSubscribeMsgTaskStatQuery query) {
        return null;
    }

    @Override
    public List<MiniProgramSubscribeMsgTaskStatDTO> list(MiniProgramSubscribeMsgTaskStatQuery query) {
        return null;
    }
}