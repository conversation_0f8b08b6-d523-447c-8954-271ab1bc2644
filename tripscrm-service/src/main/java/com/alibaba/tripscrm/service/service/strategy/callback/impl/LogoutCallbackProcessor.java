package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.service.risk.status.RiskRobotStatusController;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.alibaba.tripscrm.service.enums.system.MQEnum.WECHAT_USER_STATUS;

/**
 * 机器人下线【三方回调】
 * {"content":"ZhangYuanHang","eventType":"LOGOUT_RESULT","message":"指令退出","platformType":"WE_CHAT_ENTERPRISE","requestId":"8afff64b-dd4c-4049-90fc-8bfb2150427f","result":true}
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
@Service
@Slf4j
public class LogoutCallbackProcessor implements ProxyCallbackProcessor {
    @Resource
    private RiskRobotStatusController riskRobotStatusController;

    @Resource
    private MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.LOGOUT_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("机器人下线回调失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return true;
        }
        PlatformLogUtil.logFail("机器人下线回调", LogListUtil.newArrayList(scrmCallbackMsg));
        String userId = scrmCallbackMsg.getContent();
        String corpId = scrmCallbackMsg.getPlatformCorpId();
        JSONObject message = new JSONObject();
        message.put("userId", userId);
        message.put("corpId", corpId);
        message.put("robotStatus", RobotStatusEnum.OFFLINE.getCode());
        message.put("errorMsg", scrmCallbackMsg.getMessage());
        metaqProducer.send(WECHAT_USER_STATUS, null, corpId, JSON.toJSONString(message));
        PlatformLogUtil.logInfo("机器人下线消息发送成功", LogListUtil.newArrayList(message));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("wechatUserId", userId);
        metaqProducer.send(MQEnum.SCRM_SELLER_CORP_VALID_PLATFORM_WECHAT_USER_SYNC, null, null, jsonObject.toJSONString());

        // 风控下线
        riskRobotStatusController.toEscape(corpId, userId);
        return true;
    }
}
