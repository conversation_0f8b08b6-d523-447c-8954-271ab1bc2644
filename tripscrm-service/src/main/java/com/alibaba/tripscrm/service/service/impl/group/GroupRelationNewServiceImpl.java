package com.alibaba.tripscrm.service.service.impl.group;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.GroupRelationNewMapper;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.query.GroupRelationNewParam;
import com.alibaba.tripscrm.service.convert.GroupRelationConverter;
import com.alibaba.tripscrm.service.model.domain.query.GroupRelationQuery;
import com.alibaba.tripscrm.service.model.dto.group.GroupRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.group.GroupRelationNewService;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.taobao.tddl.client.sequence.Sequence;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description：群组关系服务实现类
 * @Author：wangrui
 * @create：2025/8/15 16:07
 * @Filename：GroupRelationServiceImpl
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupRelationNewServiceImpl implements GroupRelationNewService {
    private final GroupRelationNewMapper groupRelationNewMapper;
    private final GroupRelationConverter groupRelationConverter;

    /**
     * 查询群组关系
     *
     * @param query
     * @return
     */
    @Override
    public List<GroupRelationDTO> select(GroupRelationQuery query) {
        GroupRelationNewParam param = buildParam(query);
        List<GroupRelationDO> groupRelationDOS = groupRelationNewMapper.selectByParamWithBLOBs(param);
        if (CollectionUtils.isEmpty(groupRelationDOS)) {
            return Collections.emptyList();
        }
        return groupRelationConverter.convert2DTOs(groupRelationDOS);
    }

    /**
     * 更新群组关系
     *
     * @param groupRelationDTO
     * @return
     */
    @Override
    public Integer updateSelective(GroupRelationDTO groupRelationDTO, GroupRelationQuery query) {
        if (Objects.isNull(groupRelationDTO) || Objects.isNull(groupRelationDTO.getShardingKey())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        GroupRelationNewParam param = buildParam(query);
        GroupRelationDO groupRelationDO = groupRelationConverter.convert2DO(groupRelationDTO);

        return groupRelationNewMapper.updateByParamSelective(groupRelationDO, param);
    }


    /**
     * 删除群组关系
     *
     * @param query
     * @return
     */
    @Override
    public Integer delete(GroupRelationQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        GroupRelationNewParam param = buildParam(query);
        return groupRelationNewMapper.deleteByParam(param);
    }

    /**
     * 批量插入群组关系
     *
     * @param insertList
     * @return
     */
    @Override
    public Integer batchInsert(List<GroupRelationDTO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return 0;
        }
        int count = 0;
        for (GroupRelationDTO insert : insertList) {

            List<GroupRelationDO> insertDOs = groupRelationConverter.convertWithId(insert);
            count += groupRelationNewMapper.batchInsert(insertDOs);
        }
        return count;
    }


    /**
     * 构建查询参数
     *
     * @param query
     * @return
     */
    private GroupRelationNewParam buildParam(GroupRelationQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        GroupRelationNewParam param = new GroupRelationNewParam();
        GroupRelationNewParam.Criteria criteria = param.or();

        if (StringUtils.hasText(query.getShardingKey())) {
            criteria.andShardingKeyEqualTo(query.getShardingKey());
        }
        if (!CollectionUtils.isEmpty(query.getShardingKeyList())) {
            criteria.andShardingKeyIn(query.getShardingKeyList());
        }

        if (Objects.nonNull(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }

        if (!CollectionUtils.isEmpty(query.getIdList())) {
            criteria.andIdIn(query.getIdList());
        }

        if (StringUtils.hasText(query.getUserId())) {
            criteria.andUserIdEqualTo(query.getUserId());
        }

        if (!CollectionUtils.isEmpty(query.getUserIdList())) {
            criteria.andUserIdIn(query.getUserIdList());
        }

        if (StringUtils.hasText(query.getChatId())) {
            criteria.andChatIdEqualTo(query.getChatId());

        }

        if (!CollectionUtils.isEmpty(query.getChatIdList())) {
            criteria.andChatIdIn(query.getChatIdList());

        }


        if (StringUtils.hasText(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }

        if (!CollectionUtils.isEmpty(query.getCorpIdList())) {
            criteria.andCorpIdIn(query.getCorpIdList());
        }
        if (Objects.nonNull(query.getPlatformType())) {
            criteria.andPlatformTypeEqualTo(query.getPlatformType().getCode().byteValue());
        }

        if (Objects.nonNull(query.getUserType())) {
            criteria.andUserTypeEqualTo(query.getUserType());
        }

        if (!CollectionUtils.isEmpty(query.getUserTypeList())) {
            criteria.andUserTypeIn(query.getUserTypeList());
        }

        if (StringUtils.hasText(query.getGroupNameLike())) {
            criteria.andGroupNameLike("%" + query.getGroupNameLike() + "%");
        }

        if (StringUtils.hasText(query.getNameLike())) {
            criteria.andNameLike("%" + query.getNameLike() + "%");
        }

        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }

        if (!CollectionUtils.isEmpty(query.getStatusList())) {
            criteria.andStatusIn(query.getStatusList());
        }

        if (Objects.nonNull(query.getIsDeleted())) {
            criteria.andIsDeletedEqualTo(query.getIsDeleted());
        }

        if (Objects.nonNull(query.getJoinTimeStart())) {
            criteria.andJoinTimeGreaterThanOrEqualTo(query.getJoinTimeStart());
        }

        if (Objects.nonNull(query.getJoinTimeEnd())) {
            criteria.andJoinTimeLessThanOrEqualTo(query.getJoinTimeEnd());
        }

        if (Objects.nonNull(query.getMemberType())) {
            criteria.andMemberTypeEqualTo(query.getMemberType());
        }

        if (!CollectionUtils.isEmpty(query.getMemberTypeList())) {
            criteria.andMemberTypeIn(query.getMemberTypeList());
        }
        param.appendOrderByClause(GroupRelationNewParam.OrderCondition.JOINTIME, GroupRelationNewParam.SortType.DESC);

        return param;
    }

}
