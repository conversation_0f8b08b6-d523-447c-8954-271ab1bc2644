package com.alibaba.tripscrm.service.service.material;

import com.alibaba.tripscrm.service.model.common.ResultDO;
import com.alibaba.tripscrm.service.model.domain.query.AiMaterialAsynTaskQuery;
import com.alibaba.tripscrm.service.model.dto.material.AiMaterialAsynTaskDTO;

import java.util.List;

/**
 * @DECLARATION: THE AUTHORITY OF CODE BELONGS TO ENTERPRISE TECHNOLOGY GROUP OF FEIZHU
 * @NOTE: AI素材异步生产任务服务
 * @AUTHOR: benjamin.zhm
 * @DATE: 2025/7/9
 **/
public interface AiMaterialAsynTaskService {

    /**
     * 根据条件获取素材生成任务
     * @param query
     * @return
     */
    ResultDO<List<AiMaterialAsynTaskDTO>> getAiMaterialAsynTask(AiMaterialAsynTaskQuery query);

    /**
     * 更新或者新增素材生成任务
     * @param aiMaterialAsynTaskDTO
     * @return
     */
    ResultDO<AiMaterialAsynTaskDTO> upsertAiMaterialAsynTask(AiMaterialAsynTaskDTO aiMaterialAsynTaskDTO);
}
