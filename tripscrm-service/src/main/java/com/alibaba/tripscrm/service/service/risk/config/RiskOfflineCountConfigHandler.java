package com.alibaba.tripscrm.service.service.risk.config;

import com.alibaba.tripscrm.service.model.domain.request.AccountRiskConfigUpdateRequest;
import com.alibaba.tripscrm.service.model.domain.risk.RiskOfflineProtect;
import com.alibaba.tripscrm.service.model.domain.risk.UserRiskSchema;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigDetailVO;
import com.alibaba.tripscrm.service.model.vo.risk.AccountRiskConfigVO;
import com.alibaba.tripscrm.service.model.exception.WarnException;
import com.alibaba.tripscrm.service.service.risk.RiskConfigHandler;
import org.springframework.stereotype.Component;

/**
 * 机器人掉线次数 配置处理器
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class RiskOfflineCountConfigHandler extends RiskConfigHandler {

    @Override
    public void buildListShow(UserRiskSchema schema, AccountRiskConfigVO riskConfig) {
        if (schema != null) {
            RiskOfflineProtect riskOfflineProtect = schema.getRiskOfflineProtect();
            riskConfig.setOfflineCount(riskOfflineProtect.getEnable() ?
                    "当离线次数大于" + riskOfflineProtect.getOfflineCount() + "次时处于保护状态" : "未启用");
        } else {
            riskConfig.setOfflineCount("遵循默认风控");
        }
    }

    @Override
    public void buildDetailShow(UserRiskSchema schema, AccountRiskConfigDetailVO result) {
        // 设置掉线次数阈值
        RiskOfflineProtect riskOfflineProtect = new RiskOfflineProtect(null, false);
        if (schema != null) {
            riskOfflineProtect = schema.getRiskOfflineProtect();
        }
        result.setRiskOfflineProtect(riskOfflineProtect);
    }

    @Override
    public void updateConfig(UserRiskSchema schema, AccountRiskConfigUpdateRequest param, AccountRiskConfigVO.RiskIdInfo riskIdInfo) {
        // 设置账号掉线次数
        RiskOfflineProtect riskOfflineProtect = schema.getRiskOfflineProtect();
        if (param.getRiskOfflineProtect() != null) {
            riskOfflineProtect = param.getRiskOfflineProtect();
            if (riskOfflineProtect.getEnable() && (riskOfflineProtect.getOfflineCount() == null || riskOfflineProtect.getOfflineCount() == 0)) {
                throw new WarnException("请填写掉线次数阈值");
            }
        }
        schema.setRiskOfflineProtect(riskOfflineProtect);
    }
}

