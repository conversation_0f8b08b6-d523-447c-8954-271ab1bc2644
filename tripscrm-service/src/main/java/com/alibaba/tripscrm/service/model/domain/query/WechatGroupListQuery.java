package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 微信群聊信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WechatGroupListQuery extends BasePageRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 群聊名称
     */
    private String name;

    /**
     * 群主
     */
    private String ownerUserId;

    /**
     * 管理员
     */
    private String adminUserId;

    /**
     * 标签类型（0：包含全部，1：包含任意，2：未打标签）
     */
    private Integer tagType = 0;

    /**
     * 标签Id列表
     */
    private List<Long> tagIdList;

    /**
     * 部门列表
     */
    private List<Integer> departmentIdList;

    /**
     * 业务空间 Id
     */
    private Long spaceId;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 创建时间（左区间）
     */
    private Date createStartTime;

    /**
     * 创建时间（右区间）
     */
    private Date createEndTime;

    /**
     * 需要过滤数据的群管理任务id
     */
    private List<Long> filterGroupManageTaskId;

    /**
     * 平台类型，微信：1，支付宝：4
     */
    private Integer platformType = 1;
}
