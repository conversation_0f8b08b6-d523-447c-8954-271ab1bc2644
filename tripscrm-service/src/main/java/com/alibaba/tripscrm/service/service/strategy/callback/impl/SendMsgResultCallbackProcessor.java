package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.CallPushDataUpdateEventTypeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripzoo.proxy.constant.CallbackConstant;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/8/6 16:56
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SendMsgResultCallbackProcessor implements ProxyCallbackProcessor {
    private final MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.SEND_MSG_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        JSONObject jsonContent = JSONObject.parseObject(scrmCallbackMsg.getContent());
        String cellPhone = jsonContent.getString(CallbackConstant.CELL_PHONE);
        String result = jsonContent.getString(CallbackConstant.RESULT);

        JSONObject data = new JSONObject();
        data.put("targetId", cellPhone);
        data.put("targetType", ActivityTargetTypeEnum.PHONE_MOBILE.getCode());
        data.put("callbackContent", result);
        data.put("eventType", CallPushDataUpdateEventTypeEnum.SEND_MSG_RESULT.getCode());
        data.put("corpId", WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
        if (jsonContent.containsKey(TaskConstant.ACTIVITY_ID)) {
            data.put(TaskConstant.ACTIVITY_ID, jsonContent.getLong(TaskConstant.ACTIVITY_ID));
        }
        metaqProducer.send(MQEnum.SCRM_CALL_PUSH_DATA_PROCESS, cellPhone, CallPushDataUpdateEventTypeEnum.SEND_MSG_RESULT.getCode(), data.toJSONString());
        return true;
    }
}
