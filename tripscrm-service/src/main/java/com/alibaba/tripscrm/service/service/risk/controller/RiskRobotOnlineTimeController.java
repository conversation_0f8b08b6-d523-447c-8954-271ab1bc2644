package com.alibaba.tripscrm.service.service.risk.controller;

import com.alibaba.tripscrm.service.enums.risk.RiskItemEnum;
import com.alibaba.tripscrm.service.model.domain.request.RiskOnlineTimeRequest;
import com.alibaba.tripscrm.service.model.domain.risk.RiskOnlineProtect;
import com.alibaba.tripscrm.service.model.domain.risk.UserRiskSchema;
import com.alibaba.tripscrm.service.service.risk.RiskController;
import com.alibaba.tripscrm.service.service.risk.status.RiskRobotStatusController;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 机器人 在线时长 风控控制器
 *
 * <AUTHOR>
 * @date 2024/06/26
 */
@Component
public class RiskRobotOnlineTimeController extends RiskController<RiskOnlineTimeRequest> {
    @Resource
    private RiskRobotStatusController riskRobotStatusController;

    @Override
    protected RiskItemEnum riskItem() {
        return RiskItemEnum.ONLINE_TIME;
    }

    /**
     * 机器人进入保护
     *
     * @param param param
     * @return return
     */
    @Override
    public Boolean judgeIntoProtect(RiskOnlineTimeRequest param) {
        String corpId = param.getCorpId();
        String userId = param.getUserId();
        UserRiskSchema riskSchema = getRiskSchema(corpId, userId);
        // 先进入可用，并初始化
        riskRobotStatusController.toNormal(corpId, userId, true, true);
        if (riskSchema != null) {
            RiskOnlineProtect riskOnlineProtect = riskSchema.getRiskOnlineProtect();
            if (riskOnlineProtect != null && riskOnlineProtect.getEnable() && riskOnlineProtect.getOnlineDurationMinute() != null && riskOnlineProtect.getOnlineDurationMinute() != 0) {
                riskRobotStatusController.toProtect(corpId, userId, true);
                PlatformLogUtil.logInfo("风控控制-机器人在线时长规则进入保护", LogListUtil.newArrayList(corpId, userId));
                return true;
            }
        }
        PlatformLogUtil.logInfo("风控控制-机器人正常可用", LogListUtil.newArrayList(corpId, userId));
        return false;
    }

    /**
     * 机器人退出保护
     *
     * @param param param
     * @return return
     */
    @Override
    public Boolean judgeOutProtect(RiskOnlineTimeRequest param) {
        String corpId = param.getCorpId();
        String userId = param.getUserId();
        UserRiskSchema riskSchema = getRiskSchema(corpId, userId);
        if (riskSchema != null) {
            RiskOnlineProtect riskOnlineProtect = riskSchema.getRiskOnlineProtect();
            if (riskOnlineProtect != null && riskOnlineProtect.getEnable() && riskOnlineProtect.getOnlineDurationMinute() != null && riskOnlineProtect.getOnlineDurationMinute() != 0) {
                Long loginTime = param.getRiskObject().getLoginTime();
                if (loginTime != null &&
                        (System.currentTimeMillis() - loginTime > riskOnlineProtect.getOnlineDurationMinute() * 60 * 1000)) {
                    riskRobotStatusController.toNormal(corpId, userId, false, false);
                    PlatformLogUtil.logInfo("风控状态更新-机器人满足在线时长规则，退出保护", LogListUtil.newArrayList(corpId, userId));
                    return true;
                }
            }
        }
        PlatformLogUtil.logInfo("风控状态更新-机器人未满足在线时长规则，无法退出保护", LogListUtil.newArrayList(corpId, userId));
        return false;
    }
}

