package com.alibaba.tripscrm.service.service.strategy.variable.inner;

import com.alibaba.tripscrm.service.enums.material.InnerServiceEnum;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 获取系统时间内部服务
 *
 * <AUTHOR>
 * @since 2025/3/24 11:08
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SystemTimeInnerService extends AbstractVariableDataInnerService {
    @Override
    public String doGetValue(Map<String, Object> paramMap) {
        Date date = new Date();
        return DateUtils.getDetailDateString(date);
    }

    @Override
    public InnerServiceEnum getInnerServiceEnum() {
        return InnerServiceEnum.SYSTEM_TIME;
    }
}
