package com.alibaba.tripscrm.service.config;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.taobao.trip.train.config.VIPSeatClient;
import com.taobao.trip.train.service.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * hsf服务的统一个Config类，在其它需要使用的地方，直接@Autowired注入即可。详情见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
 *
 * <AUTHOR>
 */
@Configuration
public class TrainClientConfig {
    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private SupportCardTairService supportCardTairClientService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainCityStationService trainCityStationClientService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainBusyNoService trainBusyNoClientService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrSearchCountService trSearchCountClientService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private EmilyRuleService emilyRuleClientService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrafficTrainItemsService trafficTrainItemsService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainPreTuleCrmService trainPreTuleCrmService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainCrawlingService trainCrawlingService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainOfficeService trainOfficeService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainInfoService trainInfoService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainBaseInfoService trainBaseInfoService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private OfflineCustomAgentService offlineCustomAgentService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainTransPlanInfoService trainTransPlanInfoService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainDataModifyService trainDataModifyService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainAgentOrderAlipayService trainAgentOrderAlipayService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private SpeedItemConfigService speedItemConfigService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TransPlanOrderService transPlanOrderService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TransferInfoService transferInfoService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainRcmdItemService trainRcmdItemService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainDistributionService trainDistributionService;

    @HSFConsumer(serviceVersion = "${spring.hsf.version.traindc}")
    private TrainUserPoolService trainUserPoolService;

    @Bean(initMethod = "init")
    public VIPSeatClient vipSeatClient() {
        return new VIPSeatClient();
    }
}
