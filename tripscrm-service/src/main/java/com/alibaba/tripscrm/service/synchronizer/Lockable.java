package com.alibaba.tripscrm.service.synchronizer;

import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;

public interface Lockable<T>{
    /**
     * 如果处理过程中需要加锁，重写该方法，返回锁对应 key
     *
     * @param context 参数
     * @return 锁对应 key
     */
    String getLockKey(BaseSynchronizerContext<T> context);

    /**
     * 加锁时间
     *
     * @param context 执行上下文
     * @return 加锁时间
     */
    default Integer getLockSecond(BaseSynchronizerContext<T> context) {
        return 10;
    }

    /**
     * 重试等待时间
     *
     * @param context 执行上下文
     * @return 重试等待时间
     */
    default Long getWaitMilliseconds(BaseSynchronizerContext<T> context) {
        return 5000L;
    }
}
