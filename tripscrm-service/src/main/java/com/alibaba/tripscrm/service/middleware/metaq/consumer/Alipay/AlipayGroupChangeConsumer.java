package com.alibaba.tripscrm.service.middleware.metaq.consumer.Alipay;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description：
 * @Author：wangrui
 * @create：2025/8/21 20:02
 * @Filename：AlipayGroupChangeConsumer
 */

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AlipayGroupChangeConsumer implements MessageListenerConcurrently {
    /**
     * It is not recommend to throw exception,rather than returning ConsumeConcurrentlyStatus.RECONSUME_LATER if
     * consumption failure
     *
     * @param msgs    msgs.size() >= 1<br> DefaultMQPushConsumer.consumeMessageBatchMaxSize=1,you can modify here
     * @param context
     * @return The consume status
     */
    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {

        try {
            for (MessageExt msg : msgs) {
                PlatformLogUtil.logInfo("接收到支付宝群变更消息", msg);
                String tag = msg.getTags();
                String message = new String(msg.getBody());
                // 根据tag执行不同方法
                switch (tag) {
                    case "add_member":
                        handleAddMember(message);
                        break;
                    case "del_member":
                        handleDelMember(message);
                        break;
                    case "change_name":
                        handleChangeName(message);
                        break;
                    default:
                        PlatformLogUtil.logFail("非法消息类型", LogListUtil.newArrayList(tag, message));
                        break;
                }
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("接收到支付宝群变更消息同步异常", e.getMessage(), e, LogListUtil.newArrayList());
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


    private void handleChangeName(String message) {
        JSONObject jsonObject = JSONObject.parseObject(message);
        String chatId = jsonObject.getString("groupId");
        String userId = jsonObject.getString("userId");
        //TODO 返回的好像是taoid,不是支付宝id



    }

    private void handleDelMember(String message) {
    }

    private void handleAddMember(String message) {
    }

}


