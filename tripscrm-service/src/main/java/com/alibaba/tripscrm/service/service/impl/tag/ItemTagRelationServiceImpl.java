package com.alibaba.tripscrm.service.service.impl.tag;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.ItemTagRelationMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ItemTagRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.ItemTagRelationParam;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.convert.ItemTagRelationConverter;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationCoverRequest;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.tag.ItemTagRelationService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripzoo.proxy.api.service.TagService;
import com.alibaba.tripzoo.proxy.request.MarkTagRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.mtop.api.util.StringUtil;
import com.taobao.tddl.client.sequence.Sequence;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 此表根据 item_id 进行分表，所有db操作必须包含 item_id 字段！！！
 *
 * <AUTHOR>
 */
@Service
public class ItemTagRelationServiceImpl implements ItemTagRelationService {
    private final ItemTagRelationMapper itemTagRelationMapper;
    private final TagInfoService tagInfoService;
    private final Sequence itemTagRelationIdSequence;
    private final ItemTagRelationConverter itemTagRelationConverter;
    private final TagService tagService;

    public ItemTagRelationServiceImpl(ItemTagRelationMapper itemTagRelationMapper, TagInfoService tagInfoService, @Qualifier("itemTagRelationIdSequence") Sequence itemTagRelationIdSequence, ItemTagRelationConverter itemTagRelationConverter, TagService tagService) {
        this.itemTagRelationMapper = itemTagRelationMapper;
        this.tagInfoService = tagInfoService;
        this.itemTagRelationIdSequence = itemTagRelationIdSequence;
        this.itemTagRelationConverter = itemTagRelationConverter;
        this.tagService = tagService;
    }

    @AteyeInvoker(description = "删除指定标签关系", paraDesc = "itemId&itemType&tagId")
    @TairLock(value = "'updateItemTagRelation_' + #itemId + '_' + #itemType", waitMilliseconds = 2000L)
    public Integer deleted(String itemId, Integer itemType, String tagId) {
        if (!StringUtils.hasText(itemId) || !NumberUtils.validInteger(itemType) || !StringUtils.hasText(tagId)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ItemTagRelationQuery query = new ItemTagRelationQuery();
        query.setItemId(itemId);
        query.setItemType(itemType);
        query.setTag(tagId);
        return itemTagRelationMapper.deleteByParam(buildParam(query));
    }

    @Override
    public List<ItemTagRelationDTO> selectByCondition(ItemTagRelationQuery query) {
        ItemTagRelationParam param = buildParam(query);
        return Optional.ofNullable(itemTagRelationMapper.selectByParam(param)).orElse(new ArrayList<>()).stream().map(itemTagRelationConverter::convert2DTO).collect(Collectors.toList());
    }

    @Override
    public List<TagInfoDTO> selectTagInfoByCondition(ItemTagRelationQuery query) {
        List<TagInfoDTO> tagInfoList = new ArrayList<>();
        List<ItemTagRelationDTO> itemTagRelationList = selectByCondition(query);
        if (CollectionUtils.isEmpty(itemTagRelationList)) {
            return tagInfoList;
        }

        for (ItemTagRelationDTO tagRelation : itemTagRelationList) {
            TagInfoDTO tagInfoDTO = tagInfoService.selectByTagId(String.valueOf(tagRelation.getTag()));
            if (Objects.isNull(tagInfoDTO)) {
                PlatformLogUtil.logInfo("物料标签数据查询，标签不存在", LogListUtil.newArrayList(tagRelation));
                continue;
            }
            tagInfoList.add(tagInfoDTO);
        }
        return tagInfoList;
    }

    @Override
    public PageInfo<ItemTagRelationDTO> pageQuery(ItemTagRelationQuery query) {
        try {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
            ItemTagRelationParam param = buildParam(query);
            param.appendOrderByClause(ItemTagRelationParam.OrderCondition.GMTMODIFIED, ItemTagRelationParam.SortType.DESC);
            List<ItemTagRelationDO> list = itemTagRelationMapper.selectByParam(param);
            // 结果处理
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }

            PageInfo<ItemTagRelationDO> pageInfo = new PageInfo<>(list);
            return PageUtils.getPageInfo(pageInfo, itemTagRelationConverter::convert2DTO);
        } catch (Exception e) {
            PlatformLogUtil.logException("物料与标签关系数据分页查询出错", e.getMessage(), e, LogListUtil.newArrayList(query));
            throw e;
        }
    }

    @Override
    public Integer batchUpsertSelective(List<ItemTagRelationDTO> list) {
        int result = 0;
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        for (ItemTagRelationDTO itemTagRelationDTO : list) {
            if (upsertSelective(itemTagRelationDTO) < 1) {
                PlatformLogUtil.logFail("写入物料与标签关系数据失败", LogListUtil.newArrayList(itemTagRelationDTO));
            } else {
                result++;
            }
        }

        return result;
    }

    @Override
    public Integer upsertSelective(ItemTagRelationDTO record) {
        if (Objects.isNull(record) || !StringUtils.hasText(record.getItemId())
                || !NumberUtils.validInteger(record.getItemType())
                || !NumberUtils.validLong(record.getTagId())
        ) {
            PlatformLogUtil.logFail("物料与标签关系数据写入参数错误", LogListUtil.newArrayList(record));
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ItemTagRelationQuery query = new ItemTagRelationQuery();
        query.setItemId(record.getItemId());
        query.setItemType(record.getItemType());
        query.setTag(record.getTag());
        query.setRelationId(record.getRelationId());
        query.setDeleted((byte) 0);
        List<ItemTagRelationDTO> itemTagRelationList = selectByCondition(query);
        if (!CollectionUtils.isEmpty(itemTagRelationList)) {
            PlatformLogUtil.logFail("物料标签数据写入失败，标签已存在", LogListUtil.newArrayList(record));
            return 0;
        }

        record.setId(itemTagRelationIdSequence.nextValue());
        if (NumberUtils.isEqual(record.getItemType(), BizTypeEnum.WECHAT_CUSTOMER_RELATION.getCode()) && (Objects.isNull(record.getIsSync()))) {
            if (!updateCustomerTag(record)) {
                return 0;
            }
        }
        try {
            ItemTagRelationDO itemTagRelationDO = itemTagRelationConverter.convert2DO(record);
            Integer effectLines = itemTagRelationMapper.upsertSelective(itemTagRelationDO);
            return effectLines;
        }
        catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.UPSERT_TAG_RELATION_FAIL.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(record));
            return 0;
        }
    }

    @Override
    public Integer updateSelective(ItemTagRelationDTO record, ItemTagRelationQuery condition) {
        ItemTagRelationParam param = buildParam(condition);
        ItemTagRelationDO itemTagRelationDO = itemTagRelationConverter.convert2DO(record);
        itemTagRelationDO.setItemType(null);
        itemTagRelationDO.setItemId(null);
        itemTagRelationDO.setId(null);
        // 如果为企微标签类型，需要同步企微端
        if (NumberUtils.isEqual(condition.getItemType(), BizTypeEnum.WECHAT_CUSTOMER_RELATION.getCode()) && Objects.isNull(record.getIsSync())) {
            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            itemTagRelationDTO.setTag(String.valueOf(condition.getTagId()));
            itemTagRelationDTO.setItemId(condition.getItemId());
            itemTagRelationDTO.setItemType(condition.getItemType());
            itemTagRelationDTO.setRelationId(condition.getRelationId());
            itemTagRelationDTO.setDeleted(record.getDeleted());
            if (!updateCustomerTag(itemTagRelationDTO)) {
                return 0;
            }
        }
        return itemTagRelationMapper.updateByParamSelective(itemTagRelationDO, param);
    }

    @Override
    public void cover(ItemTagRelationCoverRequest request) {
        if (Objects.isNull(request) || !NumberUtils.validInteger(request.getItemType()) || StringUtil.isEmpty(request.getItemId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<ItemTagRelationDTO> newItemTagRelationList = Optional.ofNullable(request.getTagIdList()).orElse(Lists.newArrayList())
                .stream()
                .map(tagId -> {
                    ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                    itemTagRelationDTO.setTag(tagId);
                    itemTagRelationDTO.setItemId(request.getItemId());
                    itemTagRelationDTO.setItemType(request.getItemType());
                    itemTagRelationDTO.setCreatorId(request.getCreatorId());
                    itemTagRelationDTO.setCreatorName(request.getCreatorName());
                    itemTagRelationDTO.setDeleted((byte) 0);
                    return itemTagRelationDTO;
                })
                .collect(Collectors.toList());

        ItemTagRelationQuery condition = new ItemTagRelationQuery();
        condition.setItemId(request.getItemId());
        condition.setItemType(request.getItemType());
        List<ItemTagRelationDTO> oldItemTagRelationList = selectByCondition(condition);

        List<String> newItemTagList = newItemTagRelationList.stream().map(ItemTagRelationDTO::getTag).collect(Collectors.toList());
        List<ItemTagRelationDTO> deletedList = oldItemTagRelationList.stream().filter(oldItemTagRelationDO -> !newItemTagList.contains(oldItemTagRelationDO.getTag())).collect(Collectors.toList());
        List<ItemTagRelationDTO> addList = newItemTagRelationList.stream()
                .filter(newItemTagRelation -> !oldItemTagRelationList.stream().map(ItemTagRelationDTO::getTag).collect(Collectors.toSet()).contains(newItemTagRelation.getTag()))
                .collect(Collectors.toList());

        PlatformLogUtil.logInfo("覆盖物料的标签关系数据", LogListUtil.newArrayList(request, deletedList, addList));
        for (ItemTagRelationDTO itemTagRelationDTO : deletedList) {
            condition.setTag(itemTagRelationDTO.getTag());
            itemTagRelationDTO.setDeleted((byte) 1);
            updateSelective(itemTagRelationDTO, condition);
        }

        batchUpsertSelective(addList);
    }

    @Override
    public void addBasicTag(String externalUserId, String corpId, String uid) {
    }

    private static ItemTagRelationParam buildParam(ItemTagRelationQuery query) {
        if (Objects.isNull(query) || !StringUtils.hasText(query.getItemId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        ItemTagRelationParam param = new ItemTagRelationParam();
        ItemTagRelationParam.Criteria criteria = param.or();
        criteria.andItemIdEqualTo(query.getItemId());

        if (NumberUtils.validLong(query.getTagId())) {
            criteria.andTagIdEqualTo(query.getTagId());
        }

        if (Objects.nonNull(query.getItemType())) {
            criteria.andItemTypeEqualTo(query.getItemType());
        }

        if (!CollectionUtils.isEmpty(query.getSubCodeList())) {
            criteria.andSubCodeIn(query.getSubCodeList());
        }

        if (StringUtils.hasText(query.getRelationId())) {
            criteria.andRelationIdEqualTo(query.getRelationId());
        }

        if (Objects.nonNull(query.getDeleted())) {
            criteria.andDeletedEqualTo(query.getDeleted());
        }
        return param;
    }

    private Boolean updateCustomerTag(ItemTagRelationDTO record) {
        try {
            if (Objects.isNull(record) || !StringUtils.hasLength(record.getItemId()) || !StringUtils.hasLength(record.getRelationId()) || !NumberUtils.validLong(record.getTagId()) || !NumberUtils.isEqual(record.getItemType(), BizTypeEnum.WECHAT_CUSTOMER_RELATION.getCode())) {
                PlatformLogUtil.logFail("企微标签关系变更同步企微端失败，参数非法", LogListUtil.newArrayList(record));
                return false;
            }
            TagInfoDTO tagInfoDTO = tagInfoService.selectByTagId(String.valueOf(record.getTagId()));
            if (Objects.isNull(tagInfoDTO) || !StringUtils.hasLength(tagInfoDTO.getSource()) || !NumberUtils.isEqual(tagInfoDTO.getTagType(), TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode())) {
                PlatformLogUtil.logFail("企微标签关系变更同步企微端失败，标签不存在", LogListUtil.newArrayList(record, tagInfoDTO));
                return false;
            }
            MarkTagRequest request = new MarkTagRequest();
            List<String> itemIdList = Arrays.asList(record.getItemId().split("_"));
            if (itemIdList.size() <= 1) {
                PlatformLogUtil.logFail("企微标签关系变更同步企微端失败，itemId格式不正确", LogListUtil.newArrayList(record));
                return false;
            }
            request.setExternalUserId(removeLastSubstring(record.getItemId()));
            request.setUserId(record.getRelationId());
            request.setCorpId(WxConstants.DEFAULT_ENTERPRISE_CORP_ID);
            switch(IsDeleteEnum.of(record.getDeleted())) {
                case YES:
                    request.setRemoveTagList(Lists.newArrayList(tagInfoDTO.getSource()));
                    break;
                case NO:
                    request.setAddTagList(Lists.newArrayList(tagInfoDTO.getSource()));
                    break;
                default:
                    return false;
            }
            ResultDO<Boolean> result  = tagService.markTag(request);
            if (Objects.isNull(result) || !result.getSuccess()) {
                PlatformLogUtil.logFail("企微标签关系变更同步企微端失败，调用企微端打标接口失败", LogListUtil.newArrayList(request, result));
                return false;
            }

        } catch (Exception e) {
            PlatformLogUtil.logException("企微标签关系变更同步企微端失败", e.getMessage(), e, LogListUtil.newArrayList(record));
            return false;
        }
        return true;
    }

    public static String removeLastSubstring(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }

        int lastUnderscoreIndex = str.lastIndexOf('_');

        // 如果没有找到下划线，则直接返回原字符串
        if (lastUnderscoreIndex == -1) {
            return str;
        }

        // 截取字符串直到最后一个下划线之前的部分
        return str.substring(0, lastUnderscoreIndex);
    }
}