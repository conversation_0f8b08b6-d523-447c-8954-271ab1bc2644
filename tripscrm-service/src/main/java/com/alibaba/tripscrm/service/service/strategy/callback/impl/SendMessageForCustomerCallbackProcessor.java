package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.domain.enums.EnterpriseWechatChatMessageTypeEnum;
import com.alibaba.tripscrm.domain.message.EnterpriseWechatChatMessageDTO;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatMessageTypeEnum;
import com.alibaba.tripscrm.service.enums.fusionchat.FusionChatSenderTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.request.ChatConversationUpdateLastMessageParam;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageCreateParam;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.SendMessageRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.AsyncSendMessageProcessor;
import com.alibaba.tripscrm.service.service.task.ability.sub.callback.TaskAsyncRequestCallbackProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.result.AbstractWechatMessageBO;
import com.alibaba.tripzoo.proxy.result.WechatCustomerMessageBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

import static com.alibaba.tripscrm.service.constant.TairConstant.MESSAGE_ID_MAPPING_PREFIX;

/**
 * 发送消息到私聊【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class SendMessageForCustomerCallbackProcessor implements ProxyCallbackProcessor {
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private AsyncSendMessageProcessor asyncSendMessageProcessor;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private TaskAsyncRequestCallbackProcessor taskAsyncRequestCallbackProcessor;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private AccountService accountService;
    @Resource
    private MetaqProducer metaqProducer;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.SEND_MESSAGE_TO_CUSTOMER_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        processTaskExecuteResult(scrmCallbackMsg);
        processFusionChat(scrmCallbackMsg);
        return true;
    }

    private void processMessageFromOther(ScrmCallbackMsg scrmCallbackMsg) {
        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            // 若失败
            PlatformLogUtil.logFail("企微成员发送私聊消息结果回调处理，结果为失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        boolean persist = content.containsKey("persist") ? content.getBoolean("persist") : true;
        if (!persist) {
            PlatformLogUtil.logFail("企微成员发送私聊消息结果回调处理，无需处理", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        JSONObject jsonObject = JSONObject.parseObject(scrmCallbackMsg.getContent());
        if (!jsonObject.containsKey("msgId") || !jsonObject.containsKey("message")) {
            PlatformLogUtil.logFail("企微成员发送私聊消息结果回调处理，缺少msgId或message", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        String msgId = jsonObject.getString("msgId");
        if (!jsonObject.containsKey("message")) {
            PlatformLogUtil.logFail("企微成员发送私聊消息结果回调处理，缺少消息内容信息", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        WechatCustomerMessageBO wechatCustomerMessage = jsonObject.getObject("message", new TypeReference<WechatCustomerMessageBO>() {
        });

        String userId = wechatCustomerMessage.getUserId();
        String externalUserId = wechatCustomerMessage.getExternalUserId();
        if (!StringUtils.hasText(userId)) {
            PlatformLogUtil.logFail("企微成员发送私聊消息结果回调处理，缺少企微成员信息", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        if (!StringUtils.hasText(externalUserId)) {
            PlatformLogUtil.logFail("企微成员发送私聊消息结果回调处理，缺少客户信息", LogListUtil.newArrayList(scrmCallbackMsg));
            return;
        }

        String senderId = wechatCustomerMessage.getSenderId();
        ChatTypeEnum chatType = ChatTypeEnum.SINGLE_FOR_CUSTOMER;
        Long timestamp = wechatCustomerMessage.getTimestamp() * 1000;
        MessageTypeEnum messageTypeEnum = wechatCustomerMessage.getMessageTypeEnum();
        AbstractWechatMessageBO.MessageContent messageContent = wechatCustomerMessage.getMessageContent();
        // 将外部消息格式转换成聚合聊天消息格式
        FusionChatMessageBody fusionChatMessageBody = MessageUtils.sendMessage2fusionChatMessage(messageTypeEnum, messageContent);
        if (fusionChatMessageBody == null) {
            PlatformLogUtil.logFail("企微成员发送私聊消息结果回调处理，将外部消息格式转换成聚合聊天消息格式失败", LogListUtil.newArrayList(scrmCallbackMsg, messageTypeEnum.getCode(), userId, messageContent));
            return;
        }

        // 获取发送人信息
        Integer senderType = FusionChatSenderTypeEnum.USER.getValue();
        // 保存聊天记录
        String lockUser = wechatUserService.queryLockUserWithCache(userId, scrmCallbackMsg.getPlatformCorpId());
        User lockAccount = accountService.getUserByAccountId(lockUser);
        String lockUserName = Objects.nonNull(lockAccount) ? lockAccount.getUserName() : null;
        ChatMessageCreateParam createParam = new ChatMessageCreateParam();
        createParam.setSenderId(senderId);
        createParam.setSenderType(senderType);
        createParam.setReceiveId(chatMessageService.chatId2ReceiveId(userId, externalUserId, chatType.getValue()));
        createParam.setReceiveType(chatType.getReceiveType().getValue());
        createParam.setContent(MessageUtils.fusionChatMessage2DbContent(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
        createParam.setTimestamp(timestamp);
        createParam.setLockUser(lockUser);
        createParam.setLockUserName(lockUserName);
        createParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
        createParam.setRequestId(scrmCallbackMsg.getRequestId());
        String messageId = chatMessageService.createChatMessage(createParam);
        // 自己发的消息，在LDB中增加msgId与messageId的映射，用于消息撤回，与企微客户的私聊限2分钟内
        ldbTairManager.put(MESSAGE_ID_MAPPING_PREFIX + messageId, msgId, 60 * 2);
        ldbTairManager.put(MESSAGE_ID_MAPPING_PREFIX + msgId, messageId, 60 * 10);
        PlatformLogUtil.logInfo("企微成员发送私聊消息结果回调处理，保存聊天记录成功", LogListUtil.newArrayList(scrmCallbackMsg, userId, msgId, messageId));
        processSendMessage(scrmCallbackMsg.getPlatformCorpId(), userId, externalUserId, messageTypeEnum, fusionChatMessageBody, messageId, timestamp, scrmCallbackMsg.getRequestId());
    }

    private void processSendMessage(String corpId, String userId, String externalUserId, String fusionChatMsgType, String msgContent, Long timestamp, String messageId, String requestId) {
        EnterpriseWechatChatMessageDTO message = new EnterpriseWechatChatMessageDTO();
        message.setSenderId(userId);
        message.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
        message.setReceiverId(externalUserId);
        message.setReceiverType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
        FusionChatMessageTypeEnum messageTypeEnum = FusionChatMessageTypeEnum.parse(fusionChatMsgType);
        message.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(messageTypeEnum.name()));
        message.setMsgContent(MessageUtils.fusionChatMessage2DbContent(fusionChatMsgType, msgContent));
        message.setMessageId(messageId);
        message.setTimestamp(timestamp);
        message.setCorpId(corpId);
        message.setRequestId(requestId);
        PlatformLogUtil.logInfo("企微成员发送企业微信聊天记录消息，发送私聊消息", LogListUtil.newArrayList(message));
        metaqProducer.send(MQEnum.ENTERPRISE_WECHAT_CHAT_MESSAGE, null, corpId, JSONObject.toJSONString(message));
    }

    private void processSendMessage(String corpId, String userId, String externalUserId, MessageTypeEnum messageTypeEnum, FusionChatMessageBody fusionChatMessageBody, String messageId, Long timestamp, String requestId) {
        EnterpriseWechatChatMessageDTO message = new EnterpriseWechatChatMessageDTO();
        message.setSenderId(userId);
        message.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
        message.setReceiverId(externalUserId);
        message.setReceiverType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
        message.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(messageTypeEnum.name()));
        message.setMsgContent(fusionChatMessageBody.getMsgContent());
        message.setMessageId(messageId);
        message.setTimestamp(timestamp);
        message.setCorpId(corpId);
        message.setRequestId(requestId);
        PlatformLogUtil.logInfo("企微成员发送企业微信聊天记录消息，发送私聊消息", LogListUtil.newArrayList(message));
        metaqProducer.send(MQEnum.ENTERPRISE_WECHAT_CHAT_MESSAGE, null, corpId, JSONObject.toJSONString(message));
    }

    private void processMessageFromFusionChat(ScrmCallbackMsg scrmCallbackMsg, WsEvent wsEvent) {
        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            // 若失败
            PlatformLogUtil.logFail("企微成员发送私聊消息结果回调处理【聚合聊天】，结果为失败", LogListUtil.newArrayList(scrmCallbackMsg, wsEvent.getUserId()));
            wsEvent.setSuccess(false);
            wsEvent.setErrorMessage(scrmCallbackMsg.getMessage() + "_" + scrmCallbackMsg.getContent());
            webSocketFactory.pushMessageByDistributed(wsEvent);
            return;
        }

        PlatformLogUtil.logInfo("企微成员发送私聊消息结果回调处理【聚合聊天】，结果为成功", LogListUtil.newArrayList(scrmCallbackMsg, wsEvent.getUserId()));
        SendMessageRequest sendMessageRequest = wsEvent.getData().toJavaObject(SendMessageRequest.class);
        String userId = wsEvent.getUserId();
        String chatId = sendMessageRequest.getChatId();
        ChatTypeEnum chatType = ChatTypeEnum.SINGLE_FOR_CUSTOMER;
        String msgType = sendMessageRequest.getMsgType();
        String msgContent = sendMessageRequest.getMsgContent();
        FusionChatUserBody fusionChatUserBody = chatMessageService.getUserInfo(userId, FusionChatSenderTypeEnum.USER.getValue());
        // 获取发送人信息
        String lockUser = wechatUserService.queryLockUserWithCache(userId, scrmCallbackMsg.getPlatformCorpId());
        User lockAccount = accountService.getUserByAccountId(lockUser);
        Long timestamp = System.currentTimeMillis();
        String messageId = processChatMessage(scrmCallbackMsg, wsEvent, lockAccount, userId, chatId, chatType, msgType, msgContent, timestamp, lockUser, fusionChatUserBody);
        // 推送ws
        asyncSendMessageProcessor.pushMessageByDistributed(wsEvent, chatId, chatType, messageId, userId, lockAccount, timestamp, fusionChatUserBody, new FusionChatMessageBody(msgType, msgContent));
        // 发送成功后，更新sendMessageRecord
        asyncSendMessageProcessor.deleteSendMessageRecord(scrmCallbackMsg.getRequestId());
    }

    private String processChatMessage(ScrmCallbackMsg scrmCallbackMsg, WsEvent wsEvent, User lockAccount, String userId, String chatId, ChatTypeEnum chatType, String msgType, String msgContent, Long timestamp, String lockUser, FusionChatUserBody fusionChatUserBody) {
        JSONObject content = JSONObject.parseObject(scrmCallbackMsg.getContent());
        boolean persist = content.containsKey("persist") ? content.getBoolean("persist") : true;
        if (!persist) {
            return "";
        }

        String msgId = content.containsKey("msgId") ? content.getString("msgId") : "";
        String lockUserName = Objects.nonNull(lockAccount) ? lockAccount.getUserName() : null;
        // 保存消息到会话消息表
        ChatMessageCreateParam createParam = new ChatMessageCreateParam();
        createParam.setSenderId(userId);
        createParam.setSenderType(GroupUserTypeEnum.USER.getCode());
        createParam.setReceiveId(chatMessageService.chatId2ReceiveId(userId, chatId, chatType.getValue()));
        createParam.setReceiveType(chatType.getReceiveType().getValue());
        createParam.setContent(MessageUtils.fusionChatMessage2DbContent(msgType, msgContent));
        createParam.setTimestamp(timestamp);
        createParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
        createParam.setLockUser(lockUser);
        createParam.setLockUserName(lockUserName);
        createParam.setRequestId(scrmCallbackMsg.getRequestId());
        String messageId = chatMessageService.createChatMessage(createParam);
        // 自己发的消息，在LDB中增加msgId与messageId的映射，用于消息撤回，与企微客户的私聊限2分钟内
        ldbTairManager.put(MESSAGE_ID_MAPPING_PREFIX + messageId, msgId, 60 * 2);
        ldbTairManager.put(MESSAGE_ID_MAPPING_PREFIX + msgId, messageId, 60 * 10);

        PlatformLogUtil.logInfo("企微成员发送私聊消息结果回调处理【聚合聊天】，在LDB中存储msgId与messageId映射", LogListUtil.newArrayList(scrmCallbackMsg, wsEvent.getUserId(), msgId, messageId));
        // 收到消息，更新用户会话的最后一条消息
        ChatConversationUpdateLastMessageParam updateLastMessageParam = new ChatConversationUpdateLastMessageParam();
        updateLastMessageParam.setSenderId(userId);
        updateLastMessageParam.setUserId(userId);
        updateLastMessageParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
        updateLastMessageParam.setChatId(chatId);
        updateLastMessageParam.setChatType(chatType.getValue());
        updateLastMessageParam.setLastMessageSenderName(fusionChatUserBody.getUserName());
        updateLastMessageParam.setLastMessageContent(MessageUtils.fusionChatMessage2Text(msgType, msgContent));
        // 自己发的消息，不更新会话排序和最后一条消息事件，否则在SCRM群发的情况下，聚合聊天会出现一堆会话
        // updateLastMessageParam.setLastMessageCreateTime(new Date(createParam.getTimestamp()));
        // updateLastMessageParam.setUpdateTimestamp(createParam.getTimestamp());
        chatConversationService.updateChatConversationLastMessage(updateLastMessageParam);
        processSendMessage(scrmCallbackMsg.getPlatformCorpId(), userId, chatId, msgType, msgContent, timestamp, messageId, scrmCallbackMsg.getRequestId());
        return messageId;
    }

    private void processFusionChat(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            // 执行主动发送消息的后置动作
            WsEvent wsEvent = asyncSendMessageProcessor.afterCreate(scrmCallbackMsg.getRequestId());
            if (wsEvent == null) {
                processMessageFromOther(scrmCallbackMsg);
                return;
            }
            processMessageFromFusionChat(scrmCallbackMsg, wsEvent);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行发送私聊消息回调处理异常", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private void processTaskExecuteResult(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            taskAsyncRequestCallbackProcessor.process(scrmCallbackMsg);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行私聊任务异步请求回调处理异常", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }
}
