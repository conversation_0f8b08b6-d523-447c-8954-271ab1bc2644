package com.alibaba.tripscrm.service.service.task.ability.sub.post;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.SubTaskInstanceDO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.TaskExecuteLog;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.task.TaskInstanceStatusEnum;
import com.alibaba.tripscrm.service.enums.task.TaskStatusEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.context.TaskExecuteContext;
import com.alibaba.tripscrm.service.model.domain.request.SubTaskInstanceQueryRequest;
import com.alibaba.tripscrm.service.service.task.ability.ExecuteProcessor;
import com.alibaba.tripscrm.service.service.task.base.SubTaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskInstanceService;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;

/**
 * 任务执行_子任务_执行完成后更新主任务实例状态
 *
 * <AUTHOR>
 * @since 2024/4/22 16:51
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MainTaskStatusUpdateProcessor implements ExecuteProcessor {
    private final SubTaskInstanceService subTaskInstanceService;
    private final TaskInstanceService taskInstanceService;
    private final TaskService taskService;
    private final LdbTairManager ldbTairManager;

    @Override
    @TaskExecuteLog("任务执行_子任务_执行完成后更新主任务实例状态")
    public TripSCRMResult<Void> process(TaskExecuteContext context) {
        if (!NumberUtils.validLong(context.getInstanceId())) {
            return TripSCRMResult.success(null);
        }

        if (!NumberUtils.validLong(context.getMainTaskInstanceId())) {
            PlatformLogUtil.logFail("任务执行_子任务_执行完成后更新主任务实例状态，当前子任务暂未运行", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            return TripSCRMResult.success(null);
        }

        SubTaskInstanceQueryRequest request = new SubTaskInstanceQueryRequest();
        request.setMainTaskInstanceId(context.getMainTaskInstanceId());
        List<SubTaskInstanceDO> subTaskInstanceDOList = subTaskInstanceService.list(request);
        if (CollectionUtils.isEmpty(subTaskInstanceDOList)) {
            PlatformLogUtil.logFail("任务执行_子任务_执行完成后更新主任务实例状态，无子任务实例", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            return TripSCRMResult.fail(TripSCRMErrorCode.NOT_EXIST_SUB_TASK_INSTANCE_DATA);
        }

        boolean allFinished = subTaskInstanceDOList.stream()
                .map(SubTaskInstanceDO::getStatus)
                .map(TaskInstanceStatusEnum::getByStatus)
                .allMatch(x -> Lists.newArrayList(TaskInstanceStatusEnum.RUN_SUCCESS, TaskInstanceStatusEnum.RUN_FAIL).contains(x));
        if (!allFinished) {
            PlatformLogUtil.logFail("任务执行_子任务_执行完成后更新主任务实例状态，主任务下尚有子任务在执行", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            return TripSCRMResult.success(null);
        }

        if (!taskInstanceService.updateStatus(context.getMainTaskInstanceId(), TaskInstanceStatusEnum.RUN_SUCCESS)) {
            PlatformLogUtil.logFail("任务执行_子任务_执行完成后更新主任务实例状态，更新主任务实例失败", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            return TripSCRMResult.fail(TripSCRMErrorCode.UPDATE_MAIN_TASK_INSTANCE_STATUS_ERROR);
        }

        // 以下针对任务实例执行过程中，操作下线的处理，此时任务状态为下线中，需要通过乐观锁更新任务状态
        int ret = taskService.compareAndUpdateStatus(context.getTaskId(), TaskStatusEnum.OFFLINE_IN_PROGRESS, TaskStatusEnum.EDITING);
        if (ret > 0) {
            PlatformLogUtil.logFail("任务执行_子任务_执行完成后更新任务状态，任务执行完毕", LogListUtil.newArrayList(context.getTaskId(), context.getInstanceId()));
            ldbTairManager.put(TairConstant.LDB_TASK_STATUS_PREFIX + context.getTaskId(), TaskStatusEnum.EDITING.getStatus(), EXPIRE_TIME_DAY_UNIT);
        }
        return TripSCRMResult.success(null);
    }
}
