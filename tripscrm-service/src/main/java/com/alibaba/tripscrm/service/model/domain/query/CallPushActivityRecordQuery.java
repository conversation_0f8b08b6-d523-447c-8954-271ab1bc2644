package com.alibaba.tripscrm.service.model.domain.query;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/6/6 19:43
 */
@Data
@Builder
public class CallPushActivityRecordQuery {
    /**
     * Id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 客户淘宝id
     */
    private Long uid;

    /**
     * 加密后的客户淘宝id
     */
    private String uidEncrypt;

    /**
     * 客户淘宝id关联的手机号
     */
    private String cellPhone;

    /**
     * 客户淘宝id关联的加密后手机号
     */
    private String cellPhoneEncrypt;

    /**
     * 客户微信unionId
     */
    private String unionId;

    /**
     * 外呼推送状态，0：初始化，1：外推成功，2：外推失败
     */
    private List<Byte> pushStage;

    /**
     * 外呼状态，0：初始化，1：外呼解密成功，2：外呼成功，3：外呼失败
     */
    private List<Byte> callStage;

    /**
     * 最早创建时间
     */
    private Date startCreateTime;
}
