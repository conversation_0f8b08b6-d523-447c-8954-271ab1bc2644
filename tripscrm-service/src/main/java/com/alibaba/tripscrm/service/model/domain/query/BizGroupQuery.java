package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.ValidGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分组查询
 * <AUTHOR>
 * @date 2023/4/7
 */
@Data
public class BizGroupQuery implements Serializable {

    private static final long serialVersionUID = -2475055282857290994L;

    @NotNull(message = "id不能为空", groups = {ValidGroup.Update.class})
    private long id;

    /**
     * 分组名
     */
    @NotNull(message = "分组名不能为空", groups = {ValidGroup.Insert.class, ValidGroup.Update.class})
    private String name;

    /**
     * 业务类别
     */
    private String bizType;

    /**
     * 迁移时原来分组
     */
    @NotNull(message = "groupId不能为空", groups = {ValidGroup.Delete.class})
    private long groupId;

    /**
     * 删除移动的目标分组
     */
    @NotNull(message = "targetGroupId不能为空", groups = {ValidGroup.Delete.class})
    private Long targetGroupId;
}
