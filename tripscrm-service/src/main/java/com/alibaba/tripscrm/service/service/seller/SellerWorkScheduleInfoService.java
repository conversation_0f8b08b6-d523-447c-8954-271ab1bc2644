package com.alibaba.tripscrm.service.service.seller;

import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.query.seller.SellerWorkScheduleInfoQuery;
import com.alibaba.tripscrm.service.model.dto.seller.SellerWorkScheduleInfoDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SellerWorkScheduleInfoService {
    /**
     * 根据参数查询
     *
     * @param query
     */
    SellerWorkScheduleInfoDTO find(SellerWorkScheduleInfoQuery query);

    /**
     * 列表查询
     *
     * @param query
     */
    List<SellerWorkScheduleInfoDTO> list(SellerWorkScheduleInfoQuery query);

    /**
     * 创建
     *
     * @param record
     */
    int createSelective(SellerWorkScheduleInfoDTO record);

    /**
     * 选择性修改
     *
     * @param dto
     * @param query
     */
    int updateSelective(SellerWorkScheduleInfoDTO dto, SellerWorkScheduleInfoQuery query);

    /**
     * 根据ID删除
     */
    int delete(SellerWorkScheduleInfoQuery condition);

    /**
     * 带事务的更新或是新增
     */
    int createOrUpdateSelective(SellerWorkScheduleInfoDTO dto, SellerWorkScheduleInfoQuery query);

    /**
     * 获取商家当前值班账号
     *
     * @param sellerId
     * @param userIdList
     * @return
     */
    TripSCRMResult<List<String>> queryScheduleWechatUserIdList(String sellerId, List<String> userIdList, Date date);

    void deleteByWeekDate(String sellerId, Integer weekDate);

}