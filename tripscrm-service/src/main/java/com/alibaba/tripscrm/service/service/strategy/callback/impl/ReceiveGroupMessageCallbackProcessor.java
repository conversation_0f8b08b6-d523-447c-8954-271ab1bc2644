package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.model.domain.data.MaterailInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.dal.repository.WechatGroupRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.enums.EnterpriseWechatChatMessageTypeEnum;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.domain.message.EnterpriseWechatChatMessageDTO;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.constant.TaskConstant;
import com.alibaba.tripscrm.service.enums.task.AutoResponseMsgSourceEnum;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatMessageBody;
import com.alibaba.tripscrm.service.model.domain.fusionchat.FusionChatUserBody;
import com.alibaba.tripscrm.service.model.domain.request.ChatMessageCreateParam;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.fusionchat.ChatMessageService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.CreateSingleChatProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl.ReceiveMessageProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.wx.MessageUtils;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.enums.MessageTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.result.AbstractWechatMessageBO;
import com.alibaba.tripzoo.proxy.result.WechatGroupMessageBO;
import com.fliggy.pokemon.common.utils.DateUtils;
import com.fliggy.pokemon.lock.client.api.TairLockManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.alibaba.tripscrm.service.service.impl.fusionchat.ChatConversationServiceImpl.DEFAULT_GROUP_AVATAR;

/**
 * 收到群聊的消息【三方回调】
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
@Slf4j(topic = "FusionChatLog")
public class ReceiveGroupMessageCallbackProcessor implements ProxyCallbackProcessor {
    @Resource
    private WechatGroupRepository wechatGroupRepository;
    @Resource
    private ChatMessageService chatMessageService;
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private ReceiveMessageProcessor receiveMessageProcessor;
    @Resource
    private ChatConversationRepository chatConversationRepository;
    @Resource
    private CreateSingleChatProcessor createSingleChatProcessor;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private MetaqProducer metaqProducer;
    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private TairLockManager tairLockManager;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private MaterialService materialService;
    //公共MaterialTemplateId
    private static final Long MATERIAL_TEMPLATED_ID = 22L;
    //公共场景ID
    private static final Long SCENCE_ID = 4L;

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.RECEIVE_GROUP_MESSAGE;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        // 校验回调是否成功
        if (!scrmCallbackMsg.getResult()) {
            PlatformLogUtil.logFail("接收群聊消息回调结果为失败", LogListUtil.newArrayList(scrmCallbackMsg));
            return true;
        }
        PlatformLogUtil.logInfo("接收群聊消息回调结果为成功", LogListUtil.newArrayList(scrmCallbackMsg));

        // 自动回复处理
        processAutoResponse(scrmCallbackMsg);
        // 群监控处理
        processMonitorRecord(scrmCallbackMsg);
        //收录聊天记录素材
        processRecordChatMessage(scrmCallbackMsg);
        // 处理聚合聊天
        processFusionChat(scrmCallbackMsg);
        return true;
    }

    private void processFusionChat(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            WechatGroupMessageBO wechatGroupMessage = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatGroupMessageBO.class);
            // 只处理客户消息和自己在其他端发的消息，以及不在线的企微成员消息
            if (Objects.equals(GroupUserTypeEnum.USER, wechatGroupMessage.getSenderType()) && !Optional.ofNullable(wechatGroupMessage.getIsCurrentUserSend()).orElse(false)) {
                List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(wechatGroupMessage.getSenderId()));
                if (CollectionUtils.isEmpty(wechatUserList) || Objects.equals(RobotStatusEnum.ONLINE, RobotStatusEnum.of(wechatUserList.get(0).getOnlineStatus()))) {
                    return;
                }
            }

            String groupUniqueKey = TairConstant.UPDATE_GROUP_MESSAGE_LOCK
                    + wechatGroupMessage.getChatId()
                    + scrmCallbackMsg.getPlatformCorpId();

            // 处理群聊聊天记录，必须加锁，不然有线程安全问题
            tairLockManager.tryRunWithLock(
                    groupUniqueKey,
                    5000,
                    () -> processFusionChatMessage(wechatGroupMessage, scrmCallbackMsg),
                    () -> PlatformLogUtil.logFail("接收群聊消息，更新聊天记录，获取锁失败！！", LogListUtil.newArrayList(scrmCallbackMsg, groupUniqueKey))
            );
        } catch (Exception e) {
            PlatformLogUtil.logException("接收到群聊消息，执行出错", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    public void processFusionChatMessage(WechatGroupMessageBO wechatGroupMessage, ScrmCallbackMsg scrmCallbackMsg) {
        try {
            MessageTypeEnum messageTypeEnum = wechatGroupMessage.getMessageTypeEnum();
            List<String> messageUniqueKeyList = MessageUtils.getMessageUniqueKeyList(messageTypeEnum, wechatGroupMessage);
            for (String messageUniqueKey : messageUniqueKeyList) {
                String tairKey = TairConstant.RECEIVE_MESSAGE_UNIQUE_KEY_LOCK
                        + wechatGroupMessage.getChatId()
                        + scrmCallbackMsg.getPlatformCorpId()
                        + "_" + messageUniqueKey.hashCode()
                        + "_" + wechatGroupMessage.getSenderId();
                if (ldbTairManager.incr(tairKey, 1, 0, SwitchConfig.CHAT_RECORD_DUPLICATE_TIME_THRESHOLD) > 1) {
                    PlatformLogUtil.logInfo("接收群聊消息，更新聊天记录，已经被其他线程处理！！", LogListUtil.newArrayList(scrmCallbackMsg, tairKey));
                    return;
                }
            }

            AbstractWechatMessageBO.MessageContent messageContent = wechatGroupMessage.getMessageContent();
            String userId = wechatGroupMessage.getUserId();
            String chatId = wechatGroupMessage.getChatId();
            ChatTypeEnum chatType = ChatTypeEnum.GROUP;
            String senderId = wechatGroupMessage.getSenderId();
            GroupUserTypeEnum senderType = wechatGroupMessage.getSenderType();
            Long timestamp = wechatGroupMessage.getTimestamp() * 1000;

            if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(chatId) || StringUtils.isEmpty(senderId)) {
                PlatformLogUtil.logFail("接收群聊消息，参数异常", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            // 将外部消息格式转换成聚合聊天消息格式
            FusionChatMessageBody fusionChatMessageBody = MessageUtils.receiveMessage2fusionChatMessage(messageTypeEnum, messageContent);
            if (fusionChatMessageBody == null) {
                PlatformLogUtil.logFail("接收群聊消息，将外部消息格式转换成聚合聊天消息格式失败", LogListUtil.newArrayList(messageTypeEnum, messageContent));
                return;
            }

            // 保存聊天记录
            ChatMessageCreateParam createParam = new ChatMessageCreateParam();
            createParam.setSenderId(senderId);
            createParam.setSenderType(senderType.getCode());
            createParam.setReceiveId(chatMessageService.chatId2ReceiveId(userId, chatId, chatType.getValue()));
            createParam.setReceiveType(chatType.getReceiveType().getValue());
            if (Lists.newArrayList(MessageTypeEnum.TEXT, MessageTypeEnum.REFERENCE).contains(messageTypeEnum)) {
                String msgAndAtInfo = MessageUtils.buildTextMessageJsonStr(messageContent.getAtAll(), messageContent.getAtList(), messageContent.getAtExternalUserIdList(), messageContent.getContent());
                createParam.setContent(MessageUtils.fusionChatMessage2DbContent(fusionChatMessageBody.getMsgType(), msgAndAtInfo));
            } else {
                createParam.setContent(MessageUtils.fusionChatMessage2DbContent(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
            }
            createParam.setTimestamp(timestamp);
            createParam.setCorpId(scrmCallbackMsg.getPlatformCorpId());
            createParam.setRequestId(scrmCallbackMsg.getRequestId());
            String messageId = chatMessageService.createChatMessage(createParam);
            List<String> userIdListInGroup = wechatGroupService.listUserIdByChatIdAndUserTypeAndCorpId(chatId, GroupUserTypeEnum.USER, scrmCallbackMsg.getPlatformCorpId());
            // 获取发送人信息
            FusionChatUserBody fusionChatUserBody = chatMessageService.getUserInfo(senderId, senderType.getCode());

            // 群内每个企微成员都要执行一遍
            for (String userIdInGroup : userIdListInGroup) {
                // 收到消息，更新用户会话的最后一条消息
                ChatConversationDO chatConversation = chatConversationRepository.getByUniqueKeyWithNull(userIdInGroup, chatId, chatType.getValue());
                boolean pushCreateConversation = Objects.isNull(chatConversation) || Optional.ofNullable(chatConversation.getDeleted()).orElse(false);
                Date now = new Date();
                ChatConversationDO chatConversationDO = new ChatConversationDO();
                chatConversationDO.setUserId(userIdInGroup);
                chatConversationDO.setChatId(chatId);
                chatConversationDO.setChatType(chatType.getValue());
                chatConversationDO.setCorpId(scrmCallbackMsg.getPlatformCorpId());
                chatConversationDO.setLastMessageSenderName(fusionChatUserBody.getUserName());
                chatConversationDO.setLastMessageContent(MessageUtils.fusionChatMessage2Text(fusionChatMessageBody.getMsgType(), fusionChatMessageBody.getMsgContent()));
                chatConversationDO.setLastMessageCreateTime(new Date(timestamp));
                chatConversationDO.setDnd(false);
                chatConversationDO.setTopNo(0);
                chatConversationDO.setUnreadCount(0);
                chatConversationDO.setGmtCreate(now);
                chatConversationDO.setGmtModified(now);
                chatConversationDO.setUpdateTimestamp(timestamp);
                chatConversationDO.setDeleted(false);
                chatConversationService.upsertChatConversation(chatConversationDO);

                // 创建会话，推送至前端
                if (pushCreateConversation) {
                    WechatGroupDO wechatGroup = wechatGroupRepository.getByChatIdAndCorpId(chatId, scrmCallbackMsg.getPlatformCorpId());
                    createSingleChatProcessor.pushMessageByDistributed(userIdInGroup, chatId, chatType, wechatGroup.getName(), DEFAULT_GROUP_AVATAR, null, timestamp);
                }

                // 更新会话未读数
                chatConversationService.addChatConversationUnreadCount(userIdInGroup, senderId, chatId, scrmCallbackMsg.getPlatformCorpId(), chatConversationDO.getLastMessageContent());
                // 消息推送到前端 messageId暂用timestamp
                if (getIntValueFromTair(TairConstant.FUSION_CHAT_SEND_MESSAGE_UNIQUE_KEY_PREFIX + scrmCallbackMsg.getRequestId()) <= 0) {
                    receiveMessageProcessor.pushMessageByDistributed(userIdInGroup, chatId, chatType, senderId, null, timestamp.toString(), timestamp, scrmCallbackMsg.getPlatformCorpId(), fusionChatMessageBody, fusionChatUserBody);
                }
            }
            processSendMessage(scrmCallbackMsg.getPlatformCorpId(), senderId, chatId, userId, messageTypeEnum, fusionChatMessageBody, messageId, timestamp, scrmCallbackMsg.getRequestId(), messageContent.getAtList(), messageContent.getAtExternalUserIdList(), messageContent.getAtAll());
        } catch (Exception e) {
            PlatformLogUtil.logException("接收群聊消息，更新聊天记录失败", e.getMessage(), e, LogListUtil.newArrayList(wechatGroupMessage, scrmCallbackMsg));
        }
    }

    private void processSendMessage(String corpId, String senderId, String chatId, String userId, MessageTypeEnum messageTypeEnum, FusionChatMessageBody fusionChatMessageBody, String messageId, Long timestamp, String requestId, List<String> atUserIdList, List<String> atExternalUserIdList, Boolean atAll) {
        EnterpriseWechatChatMessageDTO message = new EnterpriseWechatChatMessageDTO();
        message.setSenderId(senderId);
        message.setReceiverId(chatId);
        message.setReceiverType(ActivityTargetTypeEnum.WX_CHAT_ID.getCode());
        List<WechatUserDTO> wechatUserDTOS = wechatUserService.listById(Lists.newArrayList(senderId));
        if (CollectionUtils.isEmpty(wechatUserDTOS)) {
            message.setSenderType(ActivityTargetTypeEnum.WX_EXTERNAL_USERID.getCode());
        } else {
            message.setSenderType(ActivityTargetTypeEnum.WX_USER_ID.getCode());
        }
        message.setMsgType(EnterpriseWechatChatMessageTypeEnum.valueOf(messageTypeEnum.name()));
        message.setMsgContent(fusionChatMessageBody.getMsgContent());
        message.setMessageId(messageId);
        message.setTimestamp(timestamp);
        message.setCorpId(corpId);
        message.setRequestId(requestId);
        setAtInfo(atAll, atUserIdList, atExternalUserIdList, message);
        PlatformLogUtil.logInfo("发送企业微信聊天记录消息，接收群聊消息", LogListUtil.newArrayList(message));
        metaqProducer.send(MQEnum.ENTERPRISE_WECHAT_CHAT_MESSAGE, null, corpId, JSONObject.toJSONString(message));
    }

    /**
     * 设置@信息
     *
     * @param atAll 是否at所有人
     * @param atUserIdList 被at的成员id列表
     * @param atExternalUserIdList 被at的客户id列表
     * @param message 消息体
     */
    private void setAtInfo(Boolean atAll, List<String> atUserIdList, List<String> atExternalUserIdList, EnterpriseWechatChatMessageDTO message) {
        if (CollectionUtils.isNotEmpty(atExternalUserIdList)) {
            message.setAtExternalUserIdList(String.join(",", atExternalUserIdList));
        }
        if (CollectionUtils.isNotEmpty(atUserIdList)) {
            message.setAtUserIdList(String.join(",", atUserIdList));
        }
        if (Objects.nonNull(atAll) && atAll) {
            message.setAtUserIdList(SwitchConfig.GROUP_CHAT_AT_ALL_STR);
            message.setAtExternalUserIdList(SwitchConfig.GROUP_CHAT_AT_ALL_STR);
        }
    }

    /**
     * 处理自动回复的消息
     *
     * @param scrmCallbackMsg 回调消息
     */
    private void processAutoResponse(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (StringUtils.isBlank(scrmCallbackMsg.getContent())) {
                PlatformLogUtil.logFail("处理自动回复的消息为空", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }
            WechatGroupMessageBO wechatGroupMessageBO = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatGroupMessageBO.class);
            if (wechatGroupMessageBO == null) {
                PlatformLogUtil.logFail("处理自动回复的消息失败", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }

            // 过滤自动回复消息
            if (skip(wechatGroupMessageBO)) {
                return;
            }

            // 消息推送
            pushAutoResponseMessageToMQ(wechatGroupMessageBO, scrmCallbackMsg.getPlatformCorpId());
        } catch (Exception e) {
            PlatformLogUtil.logException("接收群聊消息，处理自动回复失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    /**
     * 推送自动回复消息到MQ中供下游任务消费
     *
     * @param wechatGroupMessageBO 微信群聊消息
     * @param corpId               组织ID
     */
    private void pushAutoResponseMessageToMQ(WechatGroupMessageBO wechatGroupMessageBO, String corpId) {
        List<String> messageUniqueKeyList = MessageUtils.getMessageUniqueKeyList(wechatGroupMessageBO.getMessageTypeEnum(), wechatGroupMessageBO);
        for (String messageUniqueKey : messageUniqueKeyList) {
            // 同一条消息多个回调只取一条
            String onlyKey = TairConstant.AUTO_RESPONSE_MESSAGE_UNIQUE_KEY_LOCK
                    + wechatGroupMessageBO.getChatId()
                    + "_" + messageUniqueKey.hashCode()
                    + "_" + wechatGroupMessageBO.getSenderId();
            if (ldbTairManager.incr(onlyKey, 1, 0, 30) > 1) {
                PlatformLogUtil.logFail("自动回复消息接收重复，不做处理", LogListUtil.newArrayList(wechatGroupMessageBO, corpId, onlyKey));
                return;
            }
        }

        JSONObject data = new JSONObject();
        data.put("sendUserId", wechatGroupMessageBO.getUserId());
        data.put("msgSourceType", AutoResponseMsgSourceEnum.GROUP_CUSTOMER_MESSAGE.getCode());
        data.put("message", wechatGroupMessageBO.getMessageContent().getContent());
        data.put("atUserIdList", wechatGroupMessageBO.getMessageContent().getAtList());
        data.put("chatId", wechatGroupMessageBO.getChatId());
        data.put("externalUserId", wechatGroupMessageBO.getSenderId());
        data.put("corpId", corpId);
        metaqProducer.send(MQEnum.RECEIVED_MESSAGE_CONTENT, "", "group_message", data.toJSONString());
    }

    /**
     * 跳过自动回复
     *
     * @param wechatGroupMessageBO 客户消息对象
     * @return 是否跳过
     */
    private Boolean skip(WechatGroupMessageBO wechatGroupMessageBO) {
        String senderId = wechatGroupMessageBO.getSenderId();
        String userId = wechatGroupMessageBO.getUserId();
        // 过滤自己消息
        if (StringUtils.isNotBlank(senderId) && StringUtils.isNotBlank(userId) && senderId.equals(userId)) {
            PlatformLogUtil.logInfo("自动回复过滤自己消息", LogListUtil.newArrayList(senderId, wechatGroupMessageBO));
            return true;
        }
        // 过滤企业成员消息
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(Lists.newArrayList(senderId));
        if (CollectionUtils.isNotEmpty(wechatUserList)) {
            PlatformLogUtil.logInfo("自动回复过滤企业成员消息", LogListUtil.newArrayList(wechatUserList.get(0).getUserId(), wechatGroupMessageBO));
            return true;
        }
        // 过滤系统消息
        if (Objects.nonNull(wechatGroupMessageBO.getMessageTypeEnum())
                && MessageTypeEnum.SYSTEM_MESSAGE.getCode().equals(wechatGroupMessageBO.getMessageTypeEnum().getCode())) {
            PlatformLogUtil.logInfo("自动回复过滤系统消息", LogListUtil.newArrayList(userId, wechatGroupMessageBO));
            return true;
        }
        return false;
    }


    /**
     * 推送群监控消息到MQ中供下游任务消费
     *
     * @param wechatGroupMessageBO 微信群聊消息
     * @param corpId               组织ID
     */
    private boolean pushMonitorRecordMessageToMQ(WechatGroupMessageBO wechatGroupMessageBO, String corpId) {

        JSONObject data = new JSONObject();
        data.put("userId", wechatGroupMessageBO.getUserId());
        data.put("chatId", wechatGroupMessageBO.getChatId());
        data.put("targetId", wechatGroupMessageBO.getSenderId());
        data.put("msgType", wechatGroupMessageBO.getMessageTypeEnum().getCode());
        data.put("senderType", wechatGroupMessageBO.getSenderType().getCode());
        data.put("message", wechatGroupMessageBO.getMessageContent().getContent());
        data.put("corpId", corpId);
        if (wechatGroupMessageBO.getMessageTypeEnum() == MessageTypeEnum.MINI_PROGRAM) {
            data.put("message", wechatGroupMessageBO.getMessageContent().getAppId() + "," + wechatGroupMessageBO.getMessageContent().getAppName());
        }
        return metaqProducer.send(MQEnum.WECHAT_GROUP_ANTI_INFILTRATION_MONITOR, "", "", data.toJSONString());
    }

    /**
     * 处理群监控的消息
     *
     * @param scrmCallbackMsg 回调消息
     */
    private void processMonitorRecord(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (StringUtils.isBlank(scrmCallbackMsg.getContent())) {
                PlatformLogUtil.logFail("处理群监控的消息为空", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }
            WechatGroupMessageBO wechatGroupMessageBO = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatGroupMessageBO.class);
            if (wechatGroupMessageBO == null) {
                PlatformLogUtil.logFail("处理群监控的消息失败", LogListUtil.newArrayList(scrmCallbackMsg, wechatGroupMessageBO));
                return;
            }

            // 过滤群监控消息
            if (skip(wechatGroupMessageBO)) {
                PlatformLogUtil.logFail("群监控过滤规则【过滤自身消息】", LogListUtil.newArrayList(scrmCallbackMsg, wechatGroupMessageBO));
                return;
            }
            // 消息推送
            boolean sendRes = pushMonitorRecordMessageToMQ(wechatGroupMessageBO, scrmCallbackMsg.getPlatformCorpId());
            PlatformLogUtil.logInfo("接收群聊消息，处理群监控", sendRes, scrmCallbackMsg, wechatGroupMessageBO);
        } catch (Exception e) {
            PlatformLogUtil.logException("处理群监控的消息失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    /**
     * 收录聊天记录素材
     *
     * @param scrmCallbackMsg 回调消息
     */
    private void processRecordChatMessage(ScrmCallbackMsg scrmCallbackMsg) {
        try {
            if (StringUtils.isBlank(scrmCallbackMsg.getContent())) {
                PlatformLogUtil.logFail("收录聊天记录素材为空", LogListUtil.newArrayList(scrmCallbackMsg));
                return;
            }
            WechatGroupMessageBO wechatGroupMessageBO = JSONObject.parseObject(scrmCallbackMsg.getContent(), WechatGroupMessageBO.class);
            if (wechatGroupMessageBO == null) {
                PlatformLogUtil.logFail("收录聊天记录素材失败", LogListUtil.newArrayList(scrmCallbackMsg, wechatGroupMessageBO));
                return;
            }

            if (!wechatGroupMessageBO.getMessageTypeEnum().equals(MessageTypeEnum.CHAT_HISTORY) || !SwitchConfig.MESSAGE_FORWARD_RECEIVE_GROUP_ID.containsKey(wechatGroupMessageBO.getUserId()) && wechatGroupMessageBO.getChatId().equals(SwitchConfig.MESSAGE_FORWARD_RECEIVE_GROUP_ID.get(wechatGroupMessageBO.getUserId()))) {
                return;
            }

            MaterailInfoDO materialInfoDO = new MaterailInfoDO();
            // 获取发送人信息
            FusionChatUserBody fusionChatUserBody = chatMessageService.getUserInfo(wechatGroupMessageBO.getSenderId(), wechatGroupMessageBO.getSenderType().getCode());
            materialInfoDO.setCreator(fusionChatUserBody.getUserName());
            materialInfoDO.setDeleted(IsDeleteEnum.NO.getCode());
            //加一个配置
            materialInfoDO.setSpaceId(SwitchConfig.CHAT_HISTORY_MATERIAL_SPACE_ID);
            Long timestamp = wechatGroupMessageBO.getTimestamp() * 1000;
            materialInfoDO.setGmtCreate(new Date(timestamp));
            materialInfoDO.setGmtModified(new Date());
            materialInfoDO.setSceneId(SCENCE_ID);
            //构造内容
            JSONObject messageListJson = new JSONObject();
            JSONObject messageJson = new JSONObject();
            messageJson.put(TaskConstant.MESSAGE_TYPE, MessageTypeEnum.CHAT_HISTORY.getDesc());
            messageJson.put(TaskConstant.MSG_ID, wechatGroupMessageBO.getMsgId());
            messageListJson.put("messageList", Arrays.asList(messageJson));
            materialInfoDO.setContent(messageListJson.toJSONString());
            materialInfoDO.setType(TaskType.FORWARD_MESSAGE.getCode());
            materialInfoDO.setMaterialTemplateId(MATERIAL_TEMPLATED_ID);
            materialInfoDO.setName(DateUtils.format(new Date(timestamp), "yyyy-MM-dd HH:mm:ss"));
            Long upsert = materialService.upsert(materialInfoDO);
            PlatformLogUtil.logInfo("接收群聊消息，收录聊天记录素材", materialInfoDO, scrmCallbackMsg, wechatGroupMessageBO);
        } catch (Exception e) {
            PlatformLogUtil.logException("收录聊天记录素材失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
        }
    }

    private Integer getIntValueFromTair(String key) {
        return Optional.ofNullable(ldbTairManager.get(key))
                .map(String::valueOf)
                .map(Integer::valueOf)
                .orElse(0);
    }
}
