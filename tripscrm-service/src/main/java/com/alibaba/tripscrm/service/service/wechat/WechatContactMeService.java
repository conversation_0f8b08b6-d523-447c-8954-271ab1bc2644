package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.dal.model.domain.data.WechatContactMeDO;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatContactMeQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-01 14:03:45
 */
public interface WechatContactMeService {
    /**
     * 新增个人活码
     *
     * @param userIdList 承接企微成员Id
     * @param skipVerify 是否免验证通过
     * @param state      自定义参数
     * @return Id
     */
    WechatContactMeDO create(List<String> userIdList, Boolean skipVerify, String state, Long spaceId);

    /**
     * 更新个人活码
     *
     * @param id         主键
     * @param userIdList 承接企微成员Id
     * @param skipVerify 是否免验证通过
     * @return 影响行数
     */
    Integer update(Long id, List<String> userIdList, Boolean skipVerify);

    /**
     * 更新个人活码
     *
     * @param id 主键
     * @return 影响行数
     */
    Integer delete(Long id);

    /**
     * 根据id查询个人活码
     *
     * @param id 主键
     * @return WechatContactMeDO
     */
    WechatContactMeDO getById(Long id);

    /**
     * 根据state查询个人活码
     *
     * @param state 自定义参数
     * @return WechatContactMeDO
     */
    WechatContactMeDO getByState(String state);

    /**
     * 根据条件查询个人活码
     *
     * @param query 查询条件
     * @return WechatContactMeDO
     */
    List<WechatContactMeDO> getByCondition(WechatContactMeQuery query);

    /**
     * 构建页匠页链接
     */
    String buildMakerPage(Long spaceId, String state);
}
