package com.alibaba.tripscrm.service.repository.crowd;

import com.alibaba.tripscrm.service.model.domain.query.CrowdTagConvertTaskQuery;
import com.alibaba.tripscrm.service.model.dto.crowd.CrowdTagConvertTaskDTO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 人群标签转换任务Repository接口
 *
 * <AUTHOR>
 * @create 2025/10/11 17:41
 */
public interface CrowdTagConvertTaskRepository {

    /**
     * 查询人群标签转换任务
     *
     * @param query 查询条件
     * @return 任务列表
     */
    List<CrowdTagConvertTaskDTO> select(CrowdTagConvertTaskQuery query);

    /**
     * 根据ID查询人群标签转换任务
     *
     * @param id 任务ID
     * @return 任务信息
     */
    CrowdTagConvertTaskDTO selectById(Long id);

    /**
     * 插入人群标签转换任务
     *
     * @param crowdTagConvertTaskDTO 任务信息
     * @return 影响行数
     */
    Integer insert(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO);

    /**
     * 选择性插入人群标签转换任务
     *
     * @param crowdTagConvertTaskDTO 任务信息
     * @return 影响行数
     */
    Integer insertSelective(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO);

    /**
     * 批量插入人群标签转换任务
     *
     * @param insertList 任务列表
     * @return 影响行数
     */
    Integer batchInsert(List<CrowdTagConvertTaskDTO> insertList);

    /**
     * 更新人群标签转换任务
     *
     * @param crowdTagConvertTaskDTO 任务信息
     * @param query 更新条件
     * @return 影响行数
     */
    Integer updateSelective(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO, CrowdTagConvertTaskQuery query);

    /**
     * 根据ID更新人群标签转换任务
     *
     * @param crowdTagConvertTaskDTO 任务信息
     * @return 影响行数
     */
    Integer updateByIdSelective(CrowdTagConvertTaskDTO crowdTagConvertTaskDTO);

    /**
     * 删除人群标签转换任务
     *
     * @param query 删除条件
     * @return 影响行数
     */
    Integer delete(CrowdTagConvertTaskQuery query);

    /**
     * 根据ID删除人群标签转换任务
     *
     * @param id 任务ID
     * @return 影响行数
     */
    Integer deleteById(Long id);

    /**
     * 统计人群标签转换任务数量
     *
     * @param query 查询条件
     * @return 任务数量
     */
    Long count(CrowdTagConvertTaskQuery query);

    /**
     * 分页查询人群标签转换任务
     *
     * @param query 查询条件（包含分页参数）
     * @return 分页结果
     */
    PageInfo<CrowdTagConvertTaskDTO> selectWithPage(CrowdTagConvertTaskQuery query);
}