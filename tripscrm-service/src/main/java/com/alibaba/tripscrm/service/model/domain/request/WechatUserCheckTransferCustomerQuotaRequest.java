package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class WechatUserCheckTransferCustomerQuotaRequest implements Serializable {
    /**
     * 原企微成员
     */
    @NotEmpty(message = "原企微成员列表为空")
    private List<String> handoverUserIdList;

    /**
     * 接替的企微成员
     */
    @NotEmpty(message = "接替的企微成员列表为空")
    private List<String> takeoverUserIdList;
}
