package com.alibaba.tripscrm.service.service.risk.handler;

import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskActionLimitTargetEnum;
import com.alibaba.tripscrm.service.manager.middleware.RedisManager;
import com.alibaba.tripscrm.service.model.domain.risk.*;
import com.alibaba.tripscrm.service.service.risk.RiskActionLimitTargetHandler;
import com.alibaba.tripscrm.service.util.system.BeanCopyUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.google.common.collect.Lists;
import com.taobao.hsf.invocation.Invocation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 行动项API调用次数 限流处理器
 *
 * <AUTHOR>
 * @date 2024/07/04
 */
@Component
public class RiskActionLimitTargetApiHandler extends RiskActionLimitTargetHandler {
    @Autowired(required = false)
    private RedisManager redisManager;

    @Override
    protected RiskActionLimitTargetEnum riskTarget() {
        return RiskActionLimitTargetEnum.API;
    }

    @Override
    public RiskActionConfig fillConfig(RiskActionEnum riskAction, RiskActionConfig configValue) {
        if (configValue == null) {
            RiskActionConfig actionConfig = new RiskActionConfig();
            actionConfig.setActionCode(riskAction.getActionCode());
            actionConfig.setActionName(riskAction.getName());
            actionConfig.setEnable(false);
            return actionConfig;
        }
        return configValue;
    }

    @Override
    public RiskActionHitConfig hitRiskConfig(String corpId, String userId, RiskActionConfig configValue, Invocation invocation) {
        RiskActionHitConfig hitConfig = BeanCopyUtils.copy(configValue, RiskActionConfig.class, RiskActionHitConfig.class);
        hitConfig.setHit(1);
        return hitConfig;
    }

    @Override
    public RateLimitResult limitControl(String corpId, String userId, RiskActionHitConfig hitConfig) {
        RateLimitResult limitResult;
        if (hitConfig.getEnable()) {
            // 构建限流业务标记和参数
            List<String> keys = new ArrayList<>();
            List<String> args = new ArrayList<>();
            LimitKeys limitKeys = buildLimitKeys(corpId, userId, hitConfig.getActionCode());
            keys.add(limitKeys.getDayKey());
            keys.add(limitKeys.getHourKey());
            keys.add(limitKeys.getMinuteKey());
            args.add(getRateLimitValue(hitConfig.getDayRateLimit()));
            args.add(getRateLimitValue(hitConfig.getHourRateLimit()));
            args.add(getRateLimitValue(hitConfig.getMinuteRateLimit()));
            args.add(hitConfig.getHit().toString());
            AtomicReference<String> result = new AtomicReference<>();
            // 执行redis
            redisManager.execute(jedis -> {
                // Lua 脚本，限流控制-丢弃式限流-固定窗口
                String fileName = "lua/riskApiLimit.lua";
                String script = REDIS_SCRIPT.get(fileName);
                if (StringUtils.isBlank(script)) {
                    script = readFileAsString(fileName);
                    REDIS_SCRIPT.put(fileName, script);
                }
                // 执行 Lua 脚本
                result.set((String) jedis.eval(script, keys,
                        Lists.newArrayList(args)));
                PlatformLogUtil.logInfo("风控控制-风控API限流判断", LogListUtil.newArrayList(keys, args, result, corpId, userId, hitConfig.getActionCode(), hitConfig));
            });
            limitResult = buildResult(result.get());
        } else {
            limitResult = new RateLimitResult(false);
        }
        if (limitResult.getLimit()) {
            PlatformLogUtil.logFail("风控控制-行动项API触发限流", LogListUtil.newArrayList(corpId, userId, hitConfig.getActionCode(), limitResult.getLimitType()));
        } else {
            PlatformLogUtil.logInfo("风控控制-行动项API未触发限流", LogListUtil.newArrayList(corpId, userId, hitConfig.getActionCode()));
        }
        return limitResult;
    }

    @Override
    public RiskActionLimitProcess getLimitProcess(String corpId, String userId, RiskActionConfig config) {
        // 构建限流业务标记和参数
        LimitKeys limitKeys = buildLimitKeys(corpId, userId, config.getActionCode());
        RiskActionLimitProcess limitProcess = new RiskActionLimitProcess();
        limitProcess.setActionName(config.getActionName());
        limitProcess.setActionCode(config.getActionCode());
        List<String> values = redisManager.mget(Lists.newArrayList(limitKeys.getMinuteKey(), limitKeys.getHourKey(), limitKeys.getDayKey()));
        if (values.get(0) != null) {
            limitProcess.setMinuteRemainCount(Integer.parseInt(values.get(0)));
        } else {
            limitProcess.setMinuteRemainCount(config.getMinuteRateLimit());
        }
        if (values.get(1) != null) {
            limitProcess.setHourRemainCount(Integer.parseInt(values.get(1)));
        } else {
            limitProcess.setHourRemainCount(config.getHourRateLimit());
        }
        if (values.get(2) != null) {
            limitProcess.setDayRemainCount(Integer.parseInt(values.get(2)));
        } else {
            limitProcess.setDayRemainCount(config.getDayRateLimit());
        }
        return limitProcess;
    }

    @Override
    public void resetLimit(String corpId, String userId, RiskActionConfig config) {
        // 构建限流业务标记和参数
        List<String> keys = new ArrayList<>();
        List<String> args = new ArrayList<>();
        LimitKeys limitKeys = buildLimitKeys(corpId, userId, config.getActionCode());
        keys.add(limitKeys.getDayKey());
        keys.add(limitKeys.getHourKey());
        keys.add(limitKeys.getMinuteKey());
        // 执行redis
        redisManager.execute(jedis -> {
            // Lua 脚本，限流控制-丢弃式限流-固定窗口
            String fileName = "lua/deleteApiLimit.lua";
            String script = REDIS_SCRIPT.get(fileName);
            if (StringUtils.isBlank(script)) {
                script = readFileAsString(fileName);
                REDIS_SCRIPT.put(fileName, script);
            }
            // 执行 Lua 脚本
            jedis.eval(script, keys, args);
        });
    }
}
