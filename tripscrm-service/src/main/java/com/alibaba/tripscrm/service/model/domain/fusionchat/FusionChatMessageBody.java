package com.alibaba.tripscrm.service.model.domain.fusionchat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 聚合聊天消息
 *
 * <AUTHOR>
 * @date 2023/10/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FusionChatMessageBody {

    /**
     * 消息类型
     * @see FusionChatMessageTypeEnum
     */
    private String msgType;

    /**
     * 消息内容
     */
    private String msgContent;

    /**
     * 被at的企微成员id列表，
     */
    private List<String> atUserIdList;

    /**
     * 被at的客户id列表
     */
    private List<String> atExternalUserIdList;

    /**
     * 是否at所有人
     */
    private Boolean atAll = false;

    public FusionChatMessageBody(String msgType, String msgContent) {
        this.msgType = msgType;
        this.msgContent = msgContent;
    }
}