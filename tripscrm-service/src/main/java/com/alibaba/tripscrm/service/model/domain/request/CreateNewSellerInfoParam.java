package com.alibaba.tripscrm.service.model.domain.request;

import lombok.Data;

import java.util.List;

@Data
public class CreateNewSellerInfoParam {


    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 商家Id
     */
    private String sellerId;

    /**
     * 商家登录名
     */
    private String sellerLoginName;

    /**
     * 绑定部门List
     */
    private List<String> departmentList;

    /**
     * 创建人ID
     */
    private String createId;

    /**
     * 创建人名称
     */
    private String crateName;

}
