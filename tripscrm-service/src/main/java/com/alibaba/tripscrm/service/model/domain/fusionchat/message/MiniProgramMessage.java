package com.alibaba.tripscrm.service.model.domain.fusionchat.message;
import com.alibaba.tripscrm.service.enums.material.PageTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2024/6/24 11:17
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class MiniProgramMessage extends FusionChatMessage{
    /**
     * 页面类型
     * @see PageTypeEnum
     */
    private String pageType;

    /**
     * 目标Id
     */
    private String targetId;

    /**
     * 小程序标题
     */
    private String title;

    /**
     * 小程序封面图
     */
    private String coverPicture;

    /**
     * 小程序跳转链接
     */
    private String pageUrl;

}
