package com.alibaba.tripscrm.service.service.strategy.isv.route.group;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.google.common.collect.Lists;
import com.taobao.csp.courier.StringUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/27 10:45
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupRenameActionRouteStrategy extends AbstractGroupActionRouteStrategy {
    @Override
    protected TripSCRMResult<List<String>> getAllWechatUserIdList(IsvRouteContext isvRouteContext) {
        if (!StringUtils.hasText(isvRouteContext.getChatId())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        List<String> userIdList = getGroupUserList(isvRouteContext);
        if (CollectionUtils.isEmpty(userIdList)) {
            PlatformLogUtil.logFail("获取修改群名称可执行行动项对应的企微号，没有匹配到企微账号", LogListUtil.newArrayList(isvRouteContext));
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }

        return TripSCRMResult.success(userIdList);
    }

    @Override
    protected List<RiskActionEnum> getRiskActionEnumList() {
        return Lists.newArrayList(RiskActionEnum.GROUP_RENAME);
    }

    @Override
    protected boolean onlyOwnerPermission(IsvRouteContext isvRouteContext) {
        return false;
    }

    @Override
    protected boolean needOwnerOrAdminPermission(IsvRouteContext isvRouteContext) {
        return true;
    }
}