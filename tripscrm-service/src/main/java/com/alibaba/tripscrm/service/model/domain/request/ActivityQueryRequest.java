package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ActivityQueryRequest extends BasePageRequest implements Serializable {
    private static final long serialVersionUID = -1105957566436551983L;
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 创建者
     */
    private String creator;
    /**
     * 管理者
     */
    private String manager;
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 业务空间 Id
     */
    private Long spaceId;

    /**
     * 任务类型
     */
    private String taskTypeCode;

    /**
     * 任务类型列表
     */
    private List<String> taskTypeCodeList;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 标签Id
     */
    private String tagIds;

    /**
     * 创建时间（开始时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createStartTime;

    /**
     * 创建时间（结束时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createEndTime;

    /**
     * 需要查询公共活动
     */
    private Boolean needPublicActivity = false;
}
