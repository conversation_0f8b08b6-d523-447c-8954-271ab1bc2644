package com.alibaba.tripscrm.service.service.strategy.callback.impl;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.tripscrm.service.enums.wechat.WechatUserApplyStatusEnum;
import com.alibaba.tripscrm.service.model.dto.wechat.CreateWeComRecordDTO;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatUserApplyRecordDTO;
import com.alibaba.tripscrm.service.service.CreateWeComRecordService;
import com.alibaba.tripscrm.service.service.strategy.callback.ProxyCallbackProcessor;
import com.alibaba.tripscrm.service.service.wechat.WechatUserApplyRecordService;
import com.alibaba.tripzoo.proxy.enums.EventTypeEnum;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.model.ScrmCallbackMsg;
import com.alibaba.tripzoo.proxy.result.WeComRobotByLInkResultBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 链接上号结果【比邻回调】
 *
 * <AUTHOR>
 * @date 2025-01-21 23:57
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WeComRobotByLinkResultCallbackProcessor implements ProxyCallbackProcessor {

    private final CreateWeComRecordService createWeComRecordService;
    private final WechatUserApplyRecordService wechatUserApplyRecordService;
    private static final String APPLY_SUCCESS_TIME = "applySuccessTime";
    private static final String ROBOT_NAME = "robotName";
    private static final String APPLY_NAME = "applyName";

    @Override
    public EventTypeEnum type() {
        return EventTypeEnum.WE_COM_ROBOT_BY_LINK_RESULT;
    }

    @Override
    public Boolean handle(ScrmCallbackMsg scrmCallbackMsg) {
        if (Objects.isNull(scrmCallbackMsg.getContent()) || !StringUtils.hasLength(scrmCallbackMsg.getRequestId()) || Objects.isNull(scrmCallbackMsg.getResult())) {
            PlatformLogUtil.logFail("接收链接上号结果回调失败，回调参数非法", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        // 根据requestId匹配上号记录
        CreateWeComRecordDTO createWeComRecordDTO = createWeComRecordService.selectByDateAndOrderId(new Date(System.currentTimeMillis() -  24L * 3 * 60 * 60 * 1000), new Date(), scrmCallbackMsg.getRequestId());
        if (Objects.isNull(createWeComRecordDTO) || !Objects.equals(IsvTypeEnum.valueOf(createWeComRecordDTO.getIsvType()), IsvTypeEnum.BI_LIN)) {
            PlatformLogUtil.logFail("未查询到上号申请记录", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        // 写入上号记录
        WeComRobotByLInkResultBO weComRobotByLinkResultBO;
        try {
            weComRobotByLinkResultBO = JSONObject.parseObject(scrmCallbackMsg.getContent(), new TypeReference<WeComRobotByLInkResultBO>() {});
        } catch (Exception e) {
            PlatformLogUtil.logException("上号记录数据格式转换失败", e.getMessage(), e, LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        if (!StringUtils.hasLength(weComRobotByLinkResultBO.getIsvId())) {
            PlatformLogUtil.logFail("接收链接上号结果回调失败, 缺少isvId", LogListUtil.newArrayList(scrmCallbackMsg));
            return false;
        }
        WechatUserApplyRecordDTO wechatUserApplyRecordDTO = new WechatUserApplyRecordDTO();
        wechatUserApplyRecordDTO.setCreateWeComRecordId(createWeComRecordDTO.getId());
        wechatUserApplyRecordDTO.setIsvType(createWeComRecordDTO.getIsvType());
        wechatUserApplyRecordDTO.setStatus(scrmCallbackMsg.getResult() ? WechatUserApplyStatusEnum.SUCCESS.getCode() : WechatUserApplyStatusEnum.FAIL.getCode());
        wechatUserApplyRecordDTO.setIsvId(weComRobotByLinkResultBO.getIsvId());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(APPLY_SUCCESS_TIME, weComRobotByLinkResultBO.getApplySuccessTime());
        jsonObject.put(ROBOT_NAME, weComRobotByLinkResultBO.getName());
        jsonObject.put(APPLY_NAME, weComRobotByLinkResultBO.getRealName());
        wechatUserApplyRecordDTO.setExtInfo(jsonObject.toJSONString());
        Integer effectLines = wechatUserApplyRecordService.upsertSelective(wechatUserApplyRecordDTO);
        if (Objects.isNull(effectLines) || effectLines <= 0) {
            PlatformLogUtil.logFail("上号记录写入失败", LogListUtil.newArrayList(wechatUserApplyRecordDTO));
            return false;
        }
        return true;
    }
}
