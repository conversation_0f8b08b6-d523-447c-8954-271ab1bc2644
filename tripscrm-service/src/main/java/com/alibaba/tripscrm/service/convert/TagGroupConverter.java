package com.alibaba.tripscrm.service.convert;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TagGroupDO;
import com.alibaba.tripscrm.domain.enums.TagBizTypeEnum;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.tag.TagGroupVO;
import com.alibaba.tripscrm.service.model.dto.tag.TagGroupDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * TagGroup相关数据防腐层
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TagGroupConverter {
    /**
     * TagGroupDTO 转 TagGroupVO
     */
    public TagGroupDTO convert2DTO(TagGroupVO tagGroupVO) {
        if (Objects.isNull(tagGroupVO)) {
            return null;
        }
        TagGroupDTO tagGroupDTO = new TagGroupDTO();
        tagGroupDTO.setId(tagGroupVO.getId());
        tagGroupDTO.setName(tagGroupVO.getName());
        tagGroupDTO.setDescription(tagGroupVO.getDescription());
        tagGroupDTO.setSpaceId(tagGroupVO.getSpaceId());
        tagGroupDTO.setTagType(tagGroupVO.getTagType());
        tagGroupDTO.setSource(tagGroupVO.getSource());
        tagGroupDTO.setCreatorId(tagGroupVO.getCreatorId());
        tagGroupDTO.setCreatorName(tagGroupVO.getCreatorName());
        tagGroupDTO.setLastOperatorId(tagGroupVO.getLastOperatorId());
        tagGroupDTO.setLastOperatorName(tagGroupVO.getLastOperatorName());
        tagGroupDTO.setSort(tagGroupVO.getSort());
        tagGroupDTO.setPush(tagGroupVO.getPush());
        tagGroupDTO.setTagBizTypeEnum(TagBizTypeEnum.of(tagGroupVO.getTagBizTypeCode()));
        if (Objects.isNull(tagGroupDTO.getTagBizTypeEnum())) {
            return tagGroupDTO;
        }
        switch (tagGroupDTO.getTagBizTypeEnum()) {
            case SYSTEM:
            case CUSTOMER:
                tagGroupDTO.setTagType(TagTypeEnum.CUSTOMER.getCode());
                break;
            case CROWD_TAG:
                tagGroupDTO.setTagType(TagTypeEnum.CROWD_TAG.getCode());
                break;
            case ENTERPRISE_WECHAT_TAG:
                tagGroupDTO.setTagType(TagTypeEnum.ENTERPRISE_WECHAT_TAG.getCode());
                break;
            default:
                PlatformLogUtil.logFail("标签组业务类型转换失败", LogListUtil.newArrayList(tagGroupVO));
                throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        return tagGroupDTO;
    }

    /**
     * TagGroupDTO 转 TagGroupVO
     */
    public TagGroupVO convert2VO(TagGroupDTO tagGroupDTO) {
        if (Objects.isNull(tagGroupDTO)) {
            return null;
        }
        TagGroupVO tagGroupVO = new TagGroupVO();
        tagGroupVO.setId(tagGroupDTO.getId());
        tagGroupVO.setName(tagGroupDTO.getName());
        tagGroupVO.setDescription(tagGroupDTO.getDescription());
        tagGroupVO.setSpaceId(tagGroupDTO.getSpaceId());
        tagGroupVO.setTagType(tagGroupDTO.getTagType());
        tagGroupVO.setSource(tagGroupDTO.getSource());
        tagGroupVO.setCreatorId(tagGroupDTO.getCreatorId());
        tagGroupVO.setCreatorName(tagGroupDTO.getCreatorName());
        tagGroupVO.setLastOperatorId(tagGroupDTO.getLastOperatorId());
        tagGroupVO.setLastOperatorName(tagGroupDTO.getLastOperatorName());
        tagGroupVO.setSort(tagGroupDTO.getSort());
        tagGroupVO.setPush(tagGroupDTO.getPush());
        tagGroupVO.setGmtCreate(tagGroupDTO.getGmtCreate());
        tagGroupVO.setGmtModified(tagGroupDTO.getGmtModified());
        tagGroupVO.setTagBizTypeCode(tagGroupDTO.getTagBizTypeEnum().getCode());
        tagGroupVO.setTagBizTypeDesc(tagGroupDTO.getTagBizTypeEnum().getDesc());
        return tagGroupVO;
    }

    /**
     * TagGroupDTO 转 TagGroupDO
     */
    public TagGroupDO convert2DO(TagGroupDTO tagGroupDTO) {
        if (Objects.isNull(tagGroupDTO)) {
            return null;
        }

        TagGroupDO tagGroupDO = new TagGroupDO();
        tagGroupDO.setId(tagGroupDTO.getId());
        tagGroupDO.setDeleted(tagGroupDTO.getDeleted());
        tagGroupDO.setGmtCreate(tagGroupDTO.getGmtCreate());
        tagGroupDO.setGmtModified(tagGroupDTO.getGmtModified());
        tagGroupDO.setName(tagGroupDTO.getName());
        tagGroupDO.setDescription(tagGroupDTO.getDescription());
        tagGroupDO.setSpaceId(tagGroupDTO.getSpaceId());
        tagGroupDO.setSort(tagGroupDTO.getSort());
        tagGroupDO.setPush(tagGroupDTO.getPush());
        tagGroupDO.setTagType(tagGroupDTO.getTagType());
        tagGroupDO.setSource(tagGroupDTO.getSource());
        tagGroupDO.setCreatorId(tagGroupDTO.getCreatorId());
        tagGroupDO.setCreatorName(tagGroupDTO.getCreatorName());
        tagGroupDO.setLastOperatorId(tagGroupDTO.getLastOperatorId());
        tagGroupDO.setLastOperatorName(tagGroupDTO.getLastOperatorName());
        return tagGroupDO;
    }

    /**
     * TagGroupDO 转 TagGroupDTO
     */
    public TagGroupDTO convert2DTO(TagGroupDO tagGroupDO) {
        if (Objects.isNull(tagGroupDO)) {
            return null;
        }

        TagGroupDTO tagGroupDTO = new TagGroupDTO();
        tagGroupDTO.setId(tagGroupDO.getId());
        tagGroupDTO.setDeleted(tagGroupDO.getDeleted());
        tagGroupDTO.setGmtCreate(tagGroupDO.getGmtCreate());
        tagGroupDTO.setGmtModified(tagGroupDO.getGmtModified());
        tagGroupDTO.setName(tagGroupDO.getName());
        tagGroupDTO.setDescription(tagGroupDO.getDescription());
        tagGroupDTO.setSpaceId(tagGroupDO.getSpaceId());
        tagGroupDTO.setSort(tagGroupDO.getSort());
        tagGroupDTO.setPush(tagGroupDO.getPush());
        tagGroupDTO.setTagType(tagGroupDO.getTagType());
        tagGroupDTO.setSource(tagGroupDO.getSource());
        tagGroupDTO.setCreatorId(tagGroupDO.getCreatorId());
        tagGroupDTO.setCreatorName(tagGroupDO.getCreatorName());
        tagGroupDTO.setLastOperatorId(tagGroupDO.getLastOperatorId());
        tagGroupDTO.setLastOperatorName(tagGroupDO.getLastOperatorName());

        TagTypeEnum tagTypeEnum = TagTypeEnum.of(tagGroupDO.getTagType());

        switch (tagTypeEnum) {
            case CUSTOMER:
                if (Objects.equals(SwitchConfig.SCRM_BASE_SPACE_ID, tagGroupDO.getSpaceId())) {
                    tagGroupDTO.setTagBizTypeEnum(TagBizTypeEnum.SYSTEM);
                    break;
                }
                tagGroupDTO.setTagBizTypeEnum(TagBizTypeEnum.CUSTOMER);
                break;
            case DIVISION_CODE:
                tagGroupDTO.setTagBizTypeEnum(TagBizTypeEnum.SYSTEM);
                break;
            case CROWD_TAG:
                tagGroupDTO.setTagBizTypeEnum(TagBizTypeEnum.CROWD_TAG);
                break;
            case ENTERPRISE_WECHAT_TAG:
                tagGroupDTO.setTagBizTypeEnum(TagBizTypeEnum.ENTERPRISE_WECHAT_TAG);
                break;
            default:
                PlatformLogUtil.logFail("标签组业务类型转换失败", LogListUtil.newArrayList(tagTypeEnum));
                throw new TripscrmException(TripSCRMErrorCode.DB_FAILED);
        }

        return tagGroupDTO;
    }
}
