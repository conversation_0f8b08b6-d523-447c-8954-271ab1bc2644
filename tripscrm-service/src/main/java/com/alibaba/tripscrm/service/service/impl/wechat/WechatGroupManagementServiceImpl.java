package com.alibaba.tripscrm.service.service.impl.wechat;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.GroupRelationDO;
import com.alibaba.tripscrm.dal.model.domain.data.ManagementGroupDO;
import com.alibaba.tripscrm.dal.model.domain.data.OperationLogInfoDO;
import com.alibaba.tripscrm.dal.repository.GroupRelationRepository;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.system.IsDeleteEnum;
import com.alibaba.tripscrm.service.enums.system.OperationLogStatusEnum;
import com.alibaba.tripscrm.service.enums.system.OperationLogTypeEnum;
import com.alibaba.tripscrm.service.enums.task.ManagementGroupTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.model.domain.log.BatchOperateLogContentBO;
import com.alibaba.tripscrm.service.model.domain.request.CreateGroupRequest;
import com.alibaba.tripscrm.service.model.vo.log.UpdateDetailVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.service.common.ManagementGroupService;
import com.alibaba.tripscrm.service.service.isv.IsvRouteService;
import com.alibaba.tripscrm.service.service.log.BatchOperateLogInfoService;
import com.alibaba.tripscrm.service.service.material.MaterialContentGetService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.strategy.account.service.AccountService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupManagementService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.log.OperatorLogUtils;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.ErrorCodeEnum;
import com.alibaba.tripzoo.proxy.enums.GroupRelationStatusEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.request.*;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.alibaba.tripzoo.proxy.result.response.OperatorResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/14 11:12
 **/
@Service
public class WechatGroupManagementServiceImpl implements WechatGroupManagementService {

    @Resource
    private GroupService groupService;
    @Resource
    private MaterialService materialService;
    @Resource
    private MaterialContentGetService materialContentGetService;
    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private GroupRelationRepository groupRelationRepository;
    @Resource
    private WechatUserService wechatUserService;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private AccountService accountService;
    @Resource
    private BatchOperateLogInfoService batchOperateLogInfoService;
    @Resource
    private ManagementGroupService managementGroupService;
    @Resource
    private IsvRouteService isvRouteService;

    @Override
    public String createGroup(CreateGroupRequest request) {
        // 请求构建
        GroupCreateRequest groupCreateRequest = new GroupCreateRequest();
        groupCreateRequest.setGroupName(request.getGroupName());
        groupCreateRequest.setUserId(request.getUserId());
        groupCreateRequest.setInitUserIdList(request.getInitUserIdList());
        groupCreateRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        if (!StringUtils.isBlank(request.getNotice()) && StringUtils.isNumeric(request.getNotice())) {
            String noticeContent = materialContentGetService.getGroupNoticeContent(materialService.queryById(Long.valueOf(request.getNotice())), request.getUserId());
            groupCreateRequest.setNotice(noticeContent);
        }
        // 外调
        ResultDO<String> resultDO = groupService.asyncCreate(groupCreateRequest);
        if (resultDO.getSuccess()) {
            return resultDO.getModel();
        }
        PlatformLogUtil.logFail("WechatGroupManagementServiceImpl.error", LogListUtil.newArrayList(resultDO));
        throw new RuntimeException(resultDO.getResultMessage());
    }

    @Override
    public List<UpdateDetailVO> batchUpdateGroupName(List<String> chatIdList, String groupNamePre, Long groupNameIndex) {
        List<UpdateDetailVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(chatIdList)) {
            return result;
        }
        ManagementGroupDO manageGroup = createBatchOperateManageGroup(ManagementGroupTypeEnum.BATCH_UPDATE_GROUP_NAME);
        for (String chatId : chatIdList) {
            result.add(groupRename(chatId, getGroupName(groupNamePre, groupNameIndex), manageGroup));
            if (Objects.nonNull(groupNameIndex)) {
                groupNameIndex ++;
            }
        }
        return result;
    }

    /**
     * 群名修改
     * @param chatId 群聊id
     * @param groupName 群名称
     * @return 更新详情
     */
    private UpdateDetailVO groupRename(String chatId, String groupName, ManagementGroupDO managementGroupDO) {
        GroupRenameRequest request = new GroupRenameRequest();
        request.setChatId(chatId);
        request.setGroupName(groupName);
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setChatId(chatId);
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        isvRouteContext.setRiskActionEnum(RiskActionEnum.GROUP_RENAME);
        TripSCRMResult<WechatUserDTO> wechatUserResult = isvRouteService.matchWechatUser(isvRouteContext);
        if (!wechatUserResult.isSuccess() || Objects.isNull(wechatUserResult.getData())) {
            PlatformLogUtil.logFail("修改群名失败，根据服务商策略匹配企微号失败", LogListUtil.newArrayList(chatId, isvRouteContext, wechatUserResult));
            ResultDO<OperatorResult> resultDO = ResultDO.falseInstance(ErrorCodeEnum.WECHAT_USER_DATA_ERROR, "根据服务商策略匹配企微号失败");
            return getUpdateGroupDetail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_NAME, resultDO, managementGroupDO);
        }
        request.setUserId(wechatUserResult.getData().getUserId());
        ResultDO<OperatorResult> resultDO = groupService.asyncGroupRename(request);
        PlatformLogUtil.logInfo("改群名外调结果", LogListUtil.newArrayList(chatId, groupName, resultDO));
        return getUpdateGroupDetail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_NAME, resultDO, managementGroupDO);
    }

    /**
     * 获取群聊名称
     * @param groupNamePre 群聊名称前缀
     * @param groupNameIndex 群聊名称下标
     * @return 群聊名称
     */
    private String getGroupName(String groupNamePre, Long groupNameIndex) {
        if (Objects.isNull(groupNameIndex)) {
            return groupNamePre;
        }
        return groupNamePre + groupNameIndex;
    }

    @Override
    public List<UpdateDetailVO> batchPushGroupNotice(List<String> chatIdList, Long noticeMaterialId) {
        List<UpdateDetailVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(chatIdList) || noticeMaterialId == null) {
            return result;
        }
        ManagementGroupDO manageGroup = createBatchOperateManageGroup(ManagementGroupTypeEnum.BATCH_UPDATE_GROUP_NOTICE);
        for (String chatId : chatIdList) {
            result.add(pushGroupNotice(chatId, noticeMaterialId, manageGroup));
        }
        return result;
    }

    /**
     * 发布群公告
     * @param chatId 群聊id
     * @param noticeMaterialId 群公告素材id
     * @return UpdateDetailVO
     */
    private UpdateDetailVO pushGroupNotice(String chatId, Long noticeMaterialId, ManagementGroupDO managementGroupDO) {
        WechatGroupVO wechatGroupVO = wechatGroupService.getWechatGroupVOByChatId(chatId);
        if (wechatGroupVO == null) {
            PlatformLogUtil.logFail("批量改群公告找不到指定群聊", LogListUtil.newArrayList(chatId));
            return UpdateDetailVO.fail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_NOTICE, OperationLogStatusEnum.FAIL, "群聊信息不存在");
        }
        String noticeContent = materialContentGetService.getGroupNoticeContent(materialService.queryById(noticeMaterialId), wechatGroupVO.getOwnerUserId());
        return doPush(noticeContent, chatId, wechatGroupVO.getOwnerUserId(), managementGroupDO);
    }

    @Override
    public List<UpdateDetailVO> batchUpdateGroupOwner(List<String> chatIdList, String ownerUserId) {
        List<UpdateDetailVO> result = new ArrayList<>();
        // 创建管理组
        ManagementGroupDO manageGroup = createBatchOperateManageGroup(ManagementGroupTypeEnum.BATCH_UPDATE_GROUP_OWNER);
        // 数据分流
        List<String> newOwnerInGroupChatIdList = chatIdList.stream().filter(chatId -> inGroup(chatId, ownerUserId)).collect(Collectors.toList());
        List<String> newOwnerNotInGroupChatIdList = new ArrayList<>(CollectionUtils.subtract(chatIdList, newOwnerInGroupChatIdList));
        // 业务分流
        result.addAll(batchUpdateInGroupOwner(newOwnerInGroupChatIdList, ownerUserId, manageGroup));
        result.addAll(batchUpdateNotInGroupOwner(newOwnerNotInGroupChatIdList, ownerUserId, manageGroup));
        return result;
    }

    /**
     * 批量更新群主（群主不在群内）
     * @param chatIdList 群聊列表
     * @param ownerUserId 群主
     * @return 操作详情
     */
    private List<UpdateDetailVO> batchUpdateNotInGroupOwner(List<String> chatIdList, String ownerUserId, ManagementGroupDO managementGroupDO) {
        ArrayList<UpdateDetailVO> updateGroupDetailList = new ArrayList<>();
        for (String chatId : chatIdList) {
            PlatformLogUtil.logFail("批量操作不在群成员的群主失败", LogListUtil.newArrayList(chatId, ownerUserId, managementGroupDO));
            // 邀请进群
            UpdateDetailVO updateDetailVO = inviteJoinGroup(chatId, Lists.newArrayList(ownerUserId), OperationLogTypeEnum.BATCH_UPDATE_GROUP_OWNER, managementGroupDO);
            // 邀请后置处理
            postProcessInvite(chatId, ownerUserId, null, updateDetailVO.getRequestId(), TairConstant.BATCH_UPDATE_OWNER_INVITE_JOIN_GROUP);
            updateGroupDetailList.add(updateDetailVO);
        }
        return updateGroupDetailList;
    }


    /**
     * 创建批量操作管理组
     * @param managementGroupTypeEnum 管理组类型枚举
     * @return 管理组DO对象
     */
    private ManagementGroupDO createBatchOperateManageGroup(ManagementGroupTypeEnum managementGroupTypeEnum) {
        User user = Optional.ofNullable(accountService.getUserInWebThread()).orElse(new User());
        // 操作组创建
        ManagementGroupDO managementGroupDO = new ManagementGroupDO();
        managementGroupDO.setGmtCreate(new Date());
        managementGroupDO.setGmtModified(new Date());
        managementGroupDO.setIsDelete(IsDeleteEnum.NO.getCode());
        managementGroupDO.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        managementGroupDO.setType(managementGroupTypeEnum.getCode());
        managementGroupDO.setName(user.getUserId());
        managementGroupDO.setCreatorId(user.getUserId());
        if (!managementGroupService.add(managementGroupDO)) {
            PlatformLogUtil.logFail("批量群操作处理日志失败", LogListUtil.newArrayList(managementGroupDO));
        }
        return managementGroupDO;
    }

    /**
     * 邀请加入群聊
     * @param chatId 群聊id
     * @param userIdList 成员id列表
     * @return 批量操作日志内容列表
     */
    private List<BatchOperateLogContentBO> inviteJoinGroup(String chatId, List<String> userIdList) {
        GroupInviteJoinRequest request = new GroupInviteJoinRequest();
        try {
            List<String> onlineUserIdList = getOnlineUserId(wechatGroupService.getWechatGroupVOByChatId(chatId));
            if (CollectionUtils.isEmpty(onlineUserIdList)) {
                return Lists.newArrayList(BatchOperateLogContentBO.fail("群内无可用在线操作成员"));
            }
            request.setChatId(chatId);
            request.setUserId(onlineUserIdList.get(0));
            request.setUserIdList(userIdList);
            request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
            ResultDO<OperatorResult> resultDO = groupService.asyncInvite(request);
            return getOperateLogContentList(resultDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("邀请成员入群失败", e.getMessage(), e, LogListUtil.newArrayList(request));
            return Lists.newArrayList(BatchOperateLogContentBO.fail(e.getMessage()));
        }
    }

    /**
     * 邀请加入群聊
     * @param chatId 群聊id
     * @param userIdList 邀请的成员id列表
     * @param typeEnum 操作的类型
     * @return 结果详情
     */
    private UpdateDetailVO inviteJoinGroup(String chatId, List<String> userIdList, OperationLogTypeEnum typeEnum, ManagementGroupDO managementGroupDO) {
        try {
            List<BatchOperateLogContentBO> operateLogContentList = inviteJoinGroup(chatId, userIdList);
            UpdateDetailVO updateGroupDetail = getUpdateGroupDetail(chatId, typeEnum, operateLogContentList, managementGroupDO);
            // 该场景下邀请成员需要后续动作，requestId带出
            List<String> requestIdList = operateLogContentList.stream()
                    .filter(batchOperateLogContentBO -> StringUtils.isNotBlank(batchOperateLogContentBO.getRequestId()))
                    .map(BatchOperateLogContentBO::getRequestId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(requestIdList)) {
                return updateGroupDetail;
            }
            updateGroupDetail.setRequestId(requestIdList.get(0));
            return updateGroupDetail;
        } catch (Exception e) {
            PlatformLogUtil.logException("批量操作群主失败", e.getMessage(), e, LogListUtil.newArrayList(chatId, userIdList));
            return UpdateDetailVO.fail(chatId, typeEnum, OperationLogStatusEnum.FAIL, e.getMessage());
        }
    }

    /**
     * 批量更新群主（群主在群内）
     * @param chatIdList 群聊列表
     * @param ownerUserId 群主
     * @return 操作详情
     */
    private List<UpdateDetailVO> batchUpdateInGroupOwner(List<String> chatIdList, String ownerUserId, ManagementGroupDO managementGroupDO) {
        List<UpdateDetailVO> updateGroupDetailList = new ArrayList<>();
        for (String chatId : chatIdList) {
            PlatformLogUtil.logInfo("批量操作在群成员的群主", LogListUtil.newArrayList(chatId, ownerUserId, managementGroupDO.getId()));
            updateGroupDetailList.add(updateInfoGroupOwner(chatId, ownerUserId, managementGroupDO));
        }
        return updateGroupDetailList;
    }

    /**
     * 更新群主操作
     * @param chatId 群聊id
     * @param ownerUserId 群主id
     * @return 操作详情
     */
    private UpdateDetailVO updateInfoGroupOwner(String chatId, String ownerUserId, ManagementGroupDO managementGroupDO) {
        // 群主未发生改变
        List<WechatGroupVO> wechatGroupList = wechatGroupService.listByChatIdList(Lists.newArrayList(chatId), false);
        if (CollectionUtils.isNotEmpty(wechatGroupList) && StringUtils.isNotBlank(wechatGroupList.get(0).getOwnerUserId())
                && wechatGroupList.get(0).getOwnerUserId().equals(ownerUserId)) {
            return getUpdateGroupDetail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_OWNER, "设置的群主和当前群主相同", managementGroupDO);
        }
        // 群主发生改变
        SingleGroupUpdateRequest request = new SingleGroupUpdateRequest();
        request.setChatId(chatId);
        request.setOwnerUserId(ownerUserId);
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        try {
            ResultDO<OperatorResult> resultDO = groupService.asyncSingleUpdate(request);
            return getUpdateGroupDetail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_OWNER, resultDO, managementGroupDO);
        } catch (Exception e) {
            PlatformLogUtil.logException("更新群管理员出错", e.getMessage(), e, LogListUtil.newArrayList(request));
            return UpdateDetailVO.fail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_OWNER, OperationLogStatusEnum.FAIL, e.getMessage());
        }
    }

    /**
     * 获取批量更新群聊的详情（内部拦截掉后获取详情）
     * @param chatId 群聊id
     * @param typeEnum 操作日志类型
     * @param message 报错消息
     * @param managementGroupDO 管理组
     * @return 操作详情
     */
    public UpdateDetailVO getUpdateGroupDetail(String chatId, OperationLogTypeEnum typeEnum, String message, ManagementGroupDO managementGroupDO) {
        List<BatchOperateLogContentBO> operateLogContentList = Lists.newArrayList(BatchOperateLogContentBO.fail(message));
        return batchOperateLogInfoService.convertToVO(dealOperateLog(chatId, operateLogContentList, typeEnum, managementGroupDO));
    }

    /**
     * 获取批量更新群聊的详情
     * @param chatId 群聊Id
     * @param resultDO 请求结果
     * @return 操作详情
     */
    public UpdateDetailVO getUpdateGroupDetail(String chatId, OperationLogTypeEnum typeEnum, ResultDO<OperatorResult> resultDO, ManagementGroupDO managementGroupDO) {
        return getUpdateGroupDetail(chatId, typeEnum, getOperateLogContentList(resultDO), managementGroupDO);
    }

    /**
     * 获取批量更新群聊的详情
     * @param chatId 群聊id
     * @param typeEnum 操作日志类型枚举
     * @param operateLogContentList 操作日志内容列表
     * @param managementGroupDO 日志记录管理组
     * @return 操作详情
     */
    public UpdateDetailVO getUpdateGroupDetail(String chatId, OperationLogTypeEnum typeEnum, List<BatchOperateLogContentBO> operateLogContentList, ManagementGroupDO managementGroupDO) {
        if (CollectionUtils.isEmpty(operateLogContentList)) {
            return null;
        }
        return batchOperateLogInfoService.convertToVO(dealOperateLog(chatId, operateLogContentList, typeEnum, managementGroupDO));
    }

    /**
     * 获取操作日志内容列表
     * @param resultDO 外调结果
     * @return 操作日志内容列表
     */
    public List<BatchOperateLogContentBO> getOperateLogContentList(ResultDO<OperatorResult> resultDO) {
        List<BatchOperateLogContentBO> operateLogList = OperatorLogUtils.getOperateLogList(resultDO);
        if (CollectionUtils.isNotEmpty(operateLogList)) {
            return operateLogList;
        }
        return Lists.newArrayList(BatchOperateLogContentBO.fail("未知错误"));
    }

    /**
     * 处理操作日志记录
     * @param chatId 群聊
     * @param operateLogList 操作日志列表
     * @return 操作日志DO对象
     */
    private OperationLogInfoDO dealOperateLog(String chatId, List<BatchOperateLogContentBO> operateLogList
            , OperationLogTypeEnum typeEnum, ManagementGroupDO managementGroupDO) {
        User user = accountService.getUserInWebThread();
        if (user == null) {
            PlatformLogUtil.logFail("批量处理群操作", LogListUtil.newArrayList(chatId, operateLogList));
            user = new User();
        }
        // 操作日志创建
        OperationLogInfoDO operationLogInfoDO = OperatorLogUtils.buildBatchOperateGroupTypeLogDO(user.getUserId()
                , user.getUserName(), chatId, OperatorLogUtils.getOperationLogStatus(operateLogList), operateLogList, typeEnum);
        operationLogInfoDO.setGroupId(managementGroupDO.getId());
        batchOperateLogInfoService.insert(operationLogInfoDO);
        // 做映射
        doMapping(operateLogList, operationLogInfoDO.getId());
        return operationLogInfoDO;
    }

    /**
     * 请求id和记载日志之间的映射关系
     * @param operateLogList 操作日志列表
     * @param targetId 映射目标id
     */
    private void doMapping(List<BatchOperateLogContentBO> operateLogList, Long targetId) {
        List<String> requestIdList = operateLogList.stream()
                .filter(batchOperateLogContentBO -> StringUtils.isNotBlank(batchOperateLogContentBO.getRequestId()))
                .map(BatchOperateLogContentBO::getRequestId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(requestIdList)) {
            return ;
        }
        for (String requestId : requestIdList) {
            ldbTairManager.put(TairConstant.BATCH_GROUP_OPERATE_MAPPING + requestId, targetId, 60*60);
        }
    }

    /**
     * 成员在群内
     * @param chatId 群聊id
     * @param userId 成员id
     * @return 是否在群内
     */
    private Boolean inGroup(String chatId, String userId) {
        return CollectionUtils.isNotEmpty(groupRelationRepository.listByShardingKeyAndUserIdList(
                groupRelationRepository.chatId2ShardingKey(chatId), Lists.newArrayList(userId)
                , SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue()));
    }


    @Override
    public List<UpdateDetailVO> batchUpdateGroupAdmin(List<String> chatIdList, List<String> adminList) {
        List<UpdateDetailVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(chatIdList) || CollectionUtils.isEmpty(adminList)) {
            return result;
        }
        ManagementGroupDO managementGroupDO = createBatchOperateManageGroup(ManagementGroupTypeEnum.BATCH_UPDATE_GROUP_ADMIN);
        for (String chatId : chatIdList) {
            PlatformLogUtil.logInfo("批量操作群管理员", LogListUtil.newArrayList(chatId, adminList, managementGroupDO.getId()));
            result.add(updateGroupAdmin(chatId, adminList, managementGroupDO));
        }
        return result;
    }

    /**
     * 更新单个群聊的管理员
     * @param chatId 群聊id
     * @param adminList 管理员id列表
     * @return 请求id列表
     */
    private UpdateDetailVO updateGroupAdmin(String chatId, List<String> adminList, ManagementGroupDO managementGroupDO) {
        List<BatchOperateLogContentBO> operateLogContentList = new ArrayList<>();
        // 在群
        List<String> inGroupUserList = getInGroupUserList(chatId, adminList);
        if (CollectionUtils.isNotEmpty(inGroupUserList)) {
            operateLogContentList.addAll(updateAdminInGroup(chatId, inGroupUserList));
        }
        // 不在群
        List<String> notInGroupUserList = adminList.stream().filter(userId -> !inGroupUserList.contains(userId)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notInGroupUserList)) {
            operateLogContentList.addAll(updateAdminNotInGroup(chatId, notInGroupUserList));
        }
        return getUpdateGroupDetail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_ADMIN, operateLogContentList, managementGroupDO);
    }

    /**
     * 更新未在群内的成员未管理员
     * @param chatId 群聊id
     * @param adminList 管理员id列表
     * @return 批量操作日志列表
     */
    private List<BatchOperateLogContentBO> updateAdminNotInGroup(String chatId, List<String> adminList) {
        try {
            // 邀请条件
            List<String> onlineUserIdList = getOnlineUserId(wechatGroupService.getWechatGroupVOByChatId(chatId));
            if (CollectionUtils.isEmpty(onlineUserIdList)) {
                return Lists.newArrayList(BatchOperateLogContentBO.fail("无可用在线机器人拉取未在群内成员"));
            }
            List<BatchOperateLogContentBO> inviteOperateLogList = inviteJoinGroup(chatId, adminList);
            List<String> requestIdList = inviteOperateLogList.stream()
                    .filter(batchOperateLogContentBO -> StringUtils.isNotBlank(batchOperateLogContentBO.getRequestId()))
                    .map(BatchOperateLogContentBO::getRequestId).collect(Collectors.toList());
            // 暂存回调处理使用的数据
            postProcessInvite(chatId, null, adminList, CollectionUtils.isNotEmpty(requestIdList) ? requestIdList.get(0) : null, TairConstant.BATCH_UPDATE_ADMIN_INVITE_JOIN_GROUP);
            return inviteOperateLogList;
        } catch (Exception e) {
            PlatformLogUtil.logException("更新未在群内的管理员失败", e.getMessage(), e, LogListUtil.newArrayList(chatId, adminList));
            return Lists.newArrayList(BatchOperateLogContentBO.fail(e.getMessage()));
        }
    }

    /**
     * 当一个群聊中涉及到多个请求操作的时候获取操作状态
     *  1、存在任意一个等待 --> 等待中
     *  2、存在任意一个失败 --> 失败
     *  3、全部成功 --> 成功
     * @param updateDetailList 群聊的更新详情列表
     * @return 更新状态
     */
    private OperationLogStatusEnum getOperateStatus(List<UpdateDetailVO> updateDetailList) {
        if (CollectionUtils.isEmpty(updateDetailList)) {
            return OperationLogStatusEnum.FAIL;
        }
        Map<Byte, List<UpdateDetailVO>> groupMap = updateDetailList.stream().collect(Collectors.groupingBy(UpdateDetailVO::getStatus));
        int failCount = Optional.ofNullable(groupMap.get(OperationLogStatusEnum.FAIL.getCode())).orElse(new ArrayList<>()).size();
        int waitCount = Optional.ofNullable(groupMap.get(OperationLogStatusEnum.WAIT.getCode())).orElse(new ArrayList<>()).size();
        if (waitCount > 0) {
            return OperationLogStatusEnum.WAIT;
        }
        if (failCount > 0) {
            return OperationLogStatusEnum.FAIL;
        }
        return OperationLogStatusEnum.SUCCESS;
    }


    /**
     * 更新群聊管理员
     * @param chatId 群聊id
     * @param userIdList 管理员列表
     * @return 请求id
     */
    private List<BatchOperateLogContentBO> updateAdminInGroup(String chatId, List<String> userIdList) {
        SingleGroupUpdateRequest request = new SingleGroupUpdateRequest();
        request.setChatId(chatId);
        request.setAdminUserIdList(userIdList);
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        try {
            return getOperateLogContentList(groupService.asyncSingleUpdate(request));
        } catch (Exception e) {
            PlatformLogUtil.logException("更新群管理员失败", e.getMessage(), e, LogListUtil.newArrayList(request));
            return Lists.newArrayList(BatchOperateLogContentBO.fail(e.getMessage()));
        }
    }

    /**
     * 请求体塞入缓存(回调中需要串联处理)
     * @param chatId 群聊id
     * @param ownerUserId 群主
     * @param adminList 管理员列表
     * @param requestId 请求id
     */
    private void postProcessInvite(String chatId, String ownerUserId, List<String> adminList, String requestId, String keyPre) {
        if (StringUtils.isBlank(requestId)) {
            return;
        }
        SingleGroupUpdateRequest request = new SingleGroupUpdateRequest();
        request.setChatId(chatId);
        request.setOwnerUserId(ownerUserId);
        request.setAdminUserIdList(adminList);
        ldbTairManager.put( keyPre + requestId, JSONObject.toJSONString(request), 60*60*10);
    }

    /**
     * 获取在群成员列表
     * @param chatId 群聊id
     * @param adminList 管理员id列表
     * @return 在群的成员列表
     */
    private List<String> getInGroupUserList(String chatId, List<String> adminList) {
        List<String> inGroupUserList = new ArrayList<>();
        for (String adminUserId : adminList) {
            List<GroupRelationDO> groupRelationList = groupRelationRepository.listByShardingKeyAndUserIdList(groupRelationRepository.chatId2ShardingKey(chatId)
                    , Lists.newArrayList(adminUserId), SpaceInfoThreadLocalUtils.getCorpId(), GroupRelationStatusEnum.MEMBER.getCode().byteValue());
            if (CollectionUtils.isEmpty(groupRelationList)) {
                continue;
            }
            inGroupUserList.add(adminUserId);
        }
        return inGroupUserList;
    }

    /**
     * 获取群聊中在线的成员
     * @param wechatGroupVO 群聊信息
     * @return 在线的UserId
     */
    private List<String> getOnlineUserId(WechatGroupVO wechatGroupVO) {
        if (wechatGroupVO == null) {
            return Lists.newArrayList();
        }
        List<String> userIdList = Optional.ofNullable(wechatGroupVO.getAdminUserIdList()).orElse(new ArrayList<>());
        if (!StringUtils.isBlank(wechatGroupVO.getOwnerUserId())) {
            userIdList.add(wechatGroupVO.getOwnerUserId());
        }
        List<WechatUserDTO> wechatUserList = wechatUserService.listById(userIdList);
        if (CollectionUtils.isEmpty(wechatUserList)) {
            return null;
        }
        return wechatUserList.stream().filter(wechatUserDTO -> RobotStatusEnum.ONLINE.getCode()
                .equals(wechatUserDTO.getOnlineStatus())).map(WechatUserDTO::getUserId).collect(Collectors.toList());
    }

    /**
     * 发布群公告
     * @param noticeContent 群公告内容
     * @param chatId 群聊id
     * @param userId 用户id
     * @return 请求id
     */
    private UpdateDetailVO doPush(String noticeContent, String chatId, String userId, ManagementGroupDO managementGroupDO) {
        IsvRouteContext isvRouteContext = new IsvRouteContext();
        isvRouteContext.setChatId(chatId);
        isvRouteContext.setSpaceId(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId());
        isvRouteContext.setRiskActionEnum(RiskActionEnum.GROUP_PUBLISH_NOTICE);
        TripSCRMResult<WechatUserDTO> wechatUserResult = isvRouteService.matchWechatUser(isvRouteContext);
        if (!wechatUserResult.isSuccess() || Objects.isNull(wechatUserResult.getData()) || StringUtils.isEmpty(wechatUserResult.getData().getUserId())) {
            PlatformLogUtil.logFail("修改群公告失败，根据服务商策略获取企微号失败", LogListUtil.newArrayList(chatId, isvRouteContext, wechatUserResult));
            ResultDO<OperatorResult> resultDO = ResultDO.falseInstance(ErrorCodeEnum.WECHAT_USER_DATA_ERROR, "根据服务商策略获取企微号失败");
            return getUpdateGroupDetail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_NOTICE, resultDO, managementGroupDO);
        }
        WechatGroupPublishNoticeRequest request = new WechatGroupPublishNoticeRequest();
        request.setNotice(noticeContent);
        request.setChatId(chatId);
        request.setUserId(wechatUserResult.getData().getUserId());
        request.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        try {
            ResultDO<OperatorResult> resultDO = groupService.asyncGroupPublishNotice(request);
            PlatformLogUtil.logInfo("批量修改群公告", LogListUtil.newArrayList(noticeContent, chatId, userId, resultDO));
            return getUpdateGroupDetail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_NOTICE, resultDO, managementGroupDO);
        } catch (Exception e) {
            ResultDO<OperatorResult> resultDO = ResultDO.falseInstance(ErrorCodeEnum.ISV_EXCEPTION, ErrorCodeEnum.ISV_EXCEPTION.getDescCn());
            PlatformLogUtil.logException("修改群公告失败，调用第三方接口出错", e.getMessage(), e, LogListUtil.newArrayList(noticeContent, chatId, userId, resultDO));
            return getUpdateGroupDetail(chatId, OperationLogTypeEnum.BATCH_UPDATE_GROUP_NOTICE, resultDO, managementGroupDO);

        }
    }
}
