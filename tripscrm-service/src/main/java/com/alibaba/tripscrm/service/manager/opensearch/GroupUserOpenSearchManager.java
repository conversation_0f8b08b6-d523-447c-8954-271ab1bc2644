package com.alibaba.tripscrm.service.manager.opensearch;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.FieldCountInfo;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupListQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatInfoQuery;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.util.PageUtils;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import com.aliyun.opensearch.sdk.generated.search.*;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/24 18:54
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupUserOpenSearchManager extends BaseOpenSearchManager {
    public List<String> getChatIds(WechatInfoQuery query) {
        if (CollectionUtils.isEmpty(query.getChatIdList())) {
            return handleGetChatIds(query);
        }

        List<String> result = new ArrayList<>();
        // chatId多个的情况下，需要考虑分片读取，不然openSearch会有性能问题或者链接超长问题
        WechatInfoQuery newQuery = new WechatInfoQuery();
        BeanUtils.copyProperties(query, newQuery);

        List<List<String>> partition = Lists.partition(query.getChatIdList(), 10);
        for (List<String> chatIdList : partition) {
            newQuery.setChatIdList(chatIdList);
            List<String> stringPageInfo = handleGetChatIds(newQuery);
            result.addAll(stringPageInfo);
        }

        return result;
    }

    private List<String> handleGetChatIds(WechatInfoQuery query) {
        try {
            SearchParams searchParams = buildPageSearchParams(query, "duniqfield:chat_id");
            // 查询条件
            List<String> queryList = buildUserGroupRelationQueryList(query);
            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            // 条件过滤
            List<String> filterList = buildUserGroupRelationFilterList(query);
            searchParams.setFilter(String.join(" AND ", filterList));

            // "dist_key:chat_id,dist_count:1,dist_times:1,reserved:false"
            Distinct distinct = new Distinct();
            distinct.setKey("chat_id").setUpdateTotalHit(false).setDistCount(1).setDistTimes(1).setReserved(false);
            searchParams.setDistincts(Sets.newHashSet(distinct));

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);
            PageInfo<String> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convert2ChatId);
            PlatformLogUtil.logFail("Opensearch查询企微用户的chatId成功", LogListUtil.newArrayList(searchParams, pageInfo));
            return pageInfo.getList();
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询企微用户的chatId异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }
        return Lists.newArrayList();
    }

    /**
     * 查询成员的群聊数量
     *
     * @param query 查询条件
     * @return 数量
     */
    public List<FieldCountInfo> queryUserGroupCount(WechatInfoQuery query) {
        if (CollectionUtils.isEmpty(query.getCountFields())) {
            PlatformLogUtil.logFail("求和的分组字段必填", LogListUtil.newArrayList(query));
            return Lists.newArrayList();
        }
        try {
            SearchParams searchParams = buildSearchParams();
            // 查询子句
            List<String> queryList = buildUserGroupRelationQueryList(query);
            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }
            // 过滤子句
            List<String> filterList = buildUserGroupRelationFilterList(query);
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }
            // 统计子句
            if (!CollectionUtils.isEmpty(query.getCountFields())) {
                searchParams.setAggregates(buildAggregate(query.getCountFields()));
            }
            // 结果处理
            return getFacetCount(getSearcherClient().execute(searchParams));
        } catch (Exception e) {
            PlatformLogUtil.logException("OpenSearch查询成员群聊数量异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return Lists.newArrayList();
        }
    }

    public Long getGroupCount(List<String> tagIdList) {
        try {
            if (CollectionUtils.isEmpty(tagIdList)) {
                return 0L;
            }

            SearchParams searchParams = buildSearchParams();
            Set<Aggregate> aggregates = new HashSet<>();
            Aggregate aggregate = new Aggregate();
            aggregate.setGroupKey("owner_user").setAggFun("distinct_count(chat_id)");
            aggregates.add(aggregate);
            searchParams.setAggregates(aggregates);

            List<String> queryList = new ArrayList<>();

            // 标签
            if (SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
                queryList.add("(" + tagIdList.stream().map(tagId -> String.format("tag:'%s'", tagId)).collect(Collectors.joining(" OR ")) + ")");
            } else {
                queryList.add("(" + tagIdList.stream().map(tagId -> String.format("group_tag_id:'%s'", tagId)).collect(Collectors.joining(" OR ")) + ")");
            }
            searchParams.setQuery(String.join(" AND ", queryList));
            // 添加租户过滤参数
            List<String> filterList = new ArrayList<>();
            String corpId = SpaceInfoThreadLocalUtils.getCorpId();
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            filterList.add(String.format("platform_type=%d", PlatformTypeEnum.WE_CHAT_ENTERPRISE.getCode()));
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }
            Map<String, Map<String, Long>> facet = getResultFacetDistinctCount(getSearcherClient().execute(searchParams));
            if (!facet.containsKey("owner_user")) {
                return 0L;
            }

            Map<String, Long> ownerUserId2Count = facet.get("owner_user");
            PlatformLogUtil.logFail("Opensearch查询聊天分组数成功", LogListUtil.newArrayList(searchParams, facet));
            return ownerUserId2Count.values().stream().mapToLong(Long::longValue).sum();
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询聊天分组数异常", e.getMessage(), e, LogListUtil.newArrayList(tagIdList));
        }

        return 0L;
    }

    /**
     * 查询群聊信息
     *
     * @param query 查询条件
     * @return PageInfoDTO<WechatGroupVO>
     */
    public PageInfo<WechatGroupVO> getGroupList(WechatGroupListQuery query) {
        List<WechatGroupVO> res = new ArrayList<>();
        try {
            SearchParams searchParams = buildPageSearchParams(query, "duniqfield:chat_id");

            List<String> queryList = new ArrayList<>();
            List<String> filterList = new ArrayList<>();
            // 查询条件
            if (StringUtils.hasText(query.getName())) {
                queryList.add(String.format("name:'%s'", query.getName()));
            }
            if (Objects.nonNull(query.getAdminUserId())) {
                queryList.add(String.format("admin_user:'%s'", query.getAdminUserId()));
            }
            if (Objects.nonNull(query.getOwnerUserId())) {
                queryList.add(String.format("owner_user:'%s'", query.getOwnerUserId()));
            }
            if (!CollectionUtils.isEmpty(query.getTagIdList()) && Objects.nonNull(query.getTagType()) && query.getTagType() != 2) {
                // 标签
                if (SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
                    queryList.add("(" + query.getTagIdList().stream().map(tagId -> String.format("tag:'%s'", tagId)).collect(Collectors.joining(query.getTagType() == 0 ? " AND " : " OR ")) + ")");
                } else {
                    queryList.add("(" + query.getTagIdList().stream().map(tagId -> String.format("group_tag_id:'%s'", tagId)).collect(Collectors.joining(query.getTagType() == 0 ? " AND " : " OR ")) + ")");
                }
            }

            // 创建时间
            if (Objects.nonNull(query.getCreateStartTime()) && Objects.nonNull(query.getCreateEndTime())) {
                queryList.add(String.format("create_time:[%d,%d]", query.getCreateStartTime().getTime(), query.getCreateEndTime().getTime()));
            }
            if (Objects.nonNull(query.getCreateStartTime()) && Objects.isNull(query.getCreateEndTime())) {
                queryList.add(String.format("create_time:[%d,)", query.getCreateStartTime().getTime()));
            }
            if (Objects.isNull(query.getCreateStartTime()) && Objects.nonNull(query.getCreateEndTime())) {
                queryList.add(String.format("create_time:(,%d]", query.getCreateEndTime().getTime()));
            }

            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            if (Objects.nonNull(query.getDepartmentIdList())) {
                filterList.add("(" + query.getDepartmentIdList().stream().map(code -> String.format("main_department='%d'", code)).collect(Collectors.joining(" OR ")) + ")");
            }
            if (Objects.nonNull(query.getTagType()) && query.getTagType() == 2) {
                if (SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
                    filterList.add("fieldlen(tags)=0");
                } else {
                    filterList.add("fieldlen(group_tag_ids)=0");
                }
            }
            // 添加租户过滤参数
            String corpId = SpaceInfoThreadLocalUtils.getCorpId();
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            if(NumberUtils.biggerThanZero(query.getPlatformType())){
                filterList.add(String.format("platform_type=%d", query.getPlatformType()));
            }
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // "dist_key:chat_id,dist_count:1,dist_times:1,reserved:false"
            Distinct distinct = new Distinct();
            distinct.setKey("chat_id").setDistCount(1).setDistTimes(1).setReserved(false);
            searchParams.setDistincts(Sets.newHashSet(distinct));

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("create_time", Order.DECREASE));
            searchParams.setSort(sorter);
            PageInfo<WechatGroupVO> pageInfo = getPageSearchResultItems(query, getSearcherClient().execute(searchParams), this::convert2WechatGroup);
            PlatformLogUtil.logFail("Opensearch查询群聊信息成功", LogListUtil.newArrayList(searchParams, pageInfo));
            return pageInfo;
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询群聊信息异常", e.getMessage(), e, LogListUtil.newArrayList(query));
        }

        return PageUtils.getPageInfo(res, a -> a);
    }

    /**
     * 构建用户群聊关系过滤列表
     *
     * @param query 查询条件
     * @return 查询结果
     */
    private List<String> buildUserGroupRelationFilterList(WechatInfoQuery query) {
        List<String> filterList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(query.getUserIdList())) {
            filterList.add("(" + query.getUserIdList().stream().map(userId -> String.format("user_id=\"%s\"", userId)).collect(Collectors.joining(" OR ")) + ")");
        }
        if (StringUtils.hasText(query.getCorpId())) {
            filterList.add(String.format("corp_id=\"%s\"", query.getCorpId()));
        }
        if(NumberUtils.biggerThanZero(query.getPlatformType())){
            filterList.add(String.format("platform_type=%d", query.getPlatformType()));
        }
        if (Objects.nonNull(query.getStatusEnum())) {
            filterList.add(String.format("status=%d", query.getStatusEnum().getCode()));
        }
        return filterList;
    }

    /**
     * 构建用户群聊关系查询列表
     *
     * @param query 查询条件
     * @return 查询结果
     */
    private List<String> buildUserGroupRelationQueryList(WechatInfoQuery query) {
        List<String> queryList = new ArrayList<>();
        if (StringUtils.hasText(query.getName())) {
            queryList.add(String.format("name:'%s'", query.getName()));
        }

        if (!CollectionUtils.isEmpty(query.getChatIdList())) {
            queryList.add("(" + query.getChatIdList().stream().map(chatId -> String.format("chat_id:'%s'", chatId)).collect(Collectors.joining(" OR ")) + ")");
        }

        if (!CollectionUtils.isEmpty(query.getUserIdList())) {
            queryList.add("(" + query.getUserIdList().stream().map(userId -> String.format("user_id:'%s'", userId)).collect(Collectors.joining(" OR ")) + ")");
        }

        if (StringUtils.hasText(query.getUserId())) {
            queryList.add(String.format("(user_id:'%s')", query.getUserId()));
        }
        return queryList;
    }

    private String convert2ChatId(JSONObject o) {
        return o.getString("chat_id");
    }

    private WechatGroupVO convert2WechatGroup(JSONObject o) {
        WechatGroupVO wechatGroupVO = new WechatGroupVO();
        wechatGroupVO.setChatId(o.getString("chat_id"));
        wechatGroupVO.setName(o.getString("name"));
        wechatGroupVO.setCreateTime(o.getDate("create_time"));
        wechatGroupVO.setOwnerUserId(o.getString("owner_user"));
        wechatGroupVO.setUserId(o.getString("user_id"));
        wechatGroupVO.setUserType(o.getString("user_type"));
        wechatGroupVO.setAdminUserIdList(Lists.newArrayList(o.getString("admin_user").split("\t")).stream().filter(StringUtils::hasText).collect(Collectors.toList()));
        if (SwitchConfig.ITEM_TAG_RELATION_OLD_DATA_READ_SWITCH) {
            wechatGroupVO.setTagIdList(Lists.newArrayList(o.getString("tags").split("\t")).stream().filter(StringUtils::hasText).collect(Collectors.toList()));
        } else {
            wechatGroupVO.setTagIdList(Lists.newArrayList(Optional.of(o.getString("group_tag_ids")).orElse("").split("\t")).stream().filter(StringUtils::hasText).collect(Collectors.toList()));
        }
        return wechatGroupVO;
    }


    @Override
    protected String getAppName() {
        return "scrm_group_user_full";
    }

    @Override
    protected List<String> getFetchFields() {
        return Lists.newArrayList("id", "corp_id", "chat_id", "name", "create_time", "owner_user", "admin_user", "tags", "main_department", "user_id", "user_type", "group_tag_ids","group_template_id", "platform_type");
    }
}
