package com.alibaba.tripscrm.service.manager.opensearch;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.FollowUserInfo;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.query.FollowUserQuery;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.wx.WXCorpStorage;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.aliyun.opensearch.sdk.generated.search.*;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * scrm客户关系表opensearch管理类，该表由db直接同步
 *
 * <AUTHOR>
 * @since 2025/06/30
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SCRMCustomerRelationOpenSearchManager extends BaseOpenSearchManager{

    private final WXCorpStorage wxCorpStorage;

    /**
     * 查询客户的企微成员好友
     *
     * @param query 查询条件
     * @return FollowUserInfo
     */
    public TripSCRMResult<FollowUserInfo> getFollowUserInfo(FollowUserQuery query) {
        try {
            SearchParams searchParams = buildGetFollowInfoSearchParams();

            List<String> queryList = new ArrayList<>();
            // 查询条件
            if (StringUtils.hasText(query.getUnionId())) {
                queryList.add(String.format("union_id:'%s'", query.getUnionId()));
            }
            if (StringUtils.hasText(query.getExternalUserId())) {
                queryList.add(String.format("external_user_id:'%s'", query.getExternalUserId()));
            }
            if (!CollectionUtils.isEmpty(query.getUserIdList())) {
                queryList.add("(" + query.getUserIdList().stream().map(userId -> String.format("user_id:'%s'", userId)).collect(Collectors.joining(" OR ")) + ")");
            }

            if (!CollectionUtils.isEmpty(queryList)) {
                searchParams.setQuery(String.join(" AND ", queryList));
            }

            List<String> filterList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(query.getStatusList())) {
                String statusStr = query.getStatusList().stream()
                        .filter(s -> Objects.nonNull(CustomerRelationStatusEnum.of(s)))
                        .map(code -> "status=" + code)
                        .collect(Collectors.joining(" OR ", "(", ")"));
                filterList.add(statusStr);
            }
            String corpId = query.getCorpId();
            if (StringUtils.hasText(corpId)) {
                filterList.add(String.format("corp_id=\"%s\"", corpId));
            }
            if (!CollectionUtils.isEmpty(filterList)) {
                searchParams.setFilter(String.join(" AND ", filterList));
            }

            // 排序方式
            Sort sorter = new Sort();
            sorter.addToSortFields(new SortField("id", Order.DECREASE));
            searchParams.setSort(sorter);

            List<FollowUserInfo.CustomerRelation> result = getSearchResultItems(getSearcherClient().execute(searchParams), this::convert2CustomerRelation);
            return TripSCRMResult.success(new FollowUserInfo(result));
        } catch (Exception e) {
            PlatformLogUtil.logException("Opensearch查询客户的企微成员好友异常", e.getMessage(), e, LogListUtil.newArrayList(query));
            return TripSCRMResult.fail(e.getMessage());
        }
    }

    private FollowUserInfo.CustomerRelation convert2CustomerRelation(JSONObject o) {
        FollowUserInfo.CustomerRelation customerRelation = new FollowUserInfo.CustomerRelation();
        customerRelation.setUserId(o.getString("user_id"));
        customerRelation.setStatus(o.getInteger("status"));
        customerRelation.setRemark(o.getString("remark"));
        customerRelation.setAddTime(o.getDate("add_time"));
        customerRelation.setCorpName(wxCorpStorage.getCorpName(SpaceInfoThreadLocalUtils.getCorpId()));
        return customerRelation;
    }

    @Override
    protected String getAppName() {
        return "trip_scrm_customer_relation";
    }


    protected List<String> getFollowInfoFetchFields() {
        return Lists.newArrayList(
                "user_id",
                "corp_id",
                "remark",
                "add_time",
                "status"
        );
    }

    @Override
    protected List<String> getFetchFields() {
        return Lists.newArrayList(
                "id",
                "user_id",
                "external_user_id",
                "corp_id",
                "remark",
                "add_way",
                "add_time",
                "name",
                "status",
                "is_deleted",
                "gmt_create",
                "gmt_modified",
                "gender",
                "type",
                "union_id",
                "state",
                "delete_time",
                "description",
                "tag_name",
                "avatar"
        );
    }

    @AteyeInvoker(description = "查询客户的企微成员好友", paraDesc = "externalUserId&unionId&userIdList&corpId&statusList")
    public FollowUserInfo getFollowUserInfo(String externalUserId, String unionId, String userIdList, String corpId, String statusList){
        FollowUserQuery query = new FollowUserQuery();
        query.setExternalUserId(externalUserId);
        query.setUnionId(unionId);
        if (StringUtils.hasLength(userIdList)) {
            query.setUserIdList(Lists.newArrayList(userIdList.split(",")));
        }
        query.setStatusList(Lists.newArrayList(statusList.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList()));
        TripSCRMResult<FollowUserInfo> result = getFollowUserInfo(query);
        return result.getData();
    }

    private SearchParams buildGetFollowInfoSearchParams() {
        Config config = new Config(Lists.newArrayList(getAppName()));
        config.setSearchFormat(SearchFormat.JSON);
        config.setFetchFields(getFollowInfoFetchFields());
        return new SearchParams(config);
    }

}
