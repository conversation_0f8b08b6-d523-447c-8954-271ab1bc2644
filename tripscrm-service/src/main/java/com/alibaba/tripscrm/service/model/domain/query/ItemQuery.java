package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/6/28 18:00
 **/
@Data
public class ItemQuery {

    /**
     * 商家ID
     */
    private String sellerId;

    /**
     * 物料ID
     */
    private String itemId;

    /**
     * 物料类型
     */
    private ItemTypeEnum itemTypeEnum;

    /**
     * 物料名称
     */
    private String name;

    /**
     * 搜索词
     */
    private String searchKey;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页大小
     */
    private Integer pageSize;

}
