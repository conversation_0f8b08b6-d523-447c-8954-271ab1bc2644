package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.data.RiskObjectDO;
import com.alibaba.tripscrm.dal.repository.RiskObjectRepository;
import com.alibaba.tripscrm.service.enums.risk.RiskActionEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskStatusEnum;
import com.alibaba.tripscrm.service.enums.risk.RiskTargetEnum;
import com.alibaba.tripscrm.service.model.domain.request.RiskActionRequest;
import com.alibaba.tripscrm.service.model.domain.request.RiskOnlineTimeRequest;
import com.alibaba.tripscrm.service.service.risk.controller.RiskActionLimitController;
import com.alibaba.tripscrm.service.service.risk.controller.RiskRobotOnlineTimeController;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 风控状态更新（1分钟执行一次）
 *
 * <AUTHOR>
 * @date 2024/07/11
 */
@Service
public class RiskStatusProcessor extends JavaProcessor {
    @Resource
    private RiskObjectRepository riskObjectRepository;
    @Resource
    private RiskRobotOnlineTimeController riskRobotOnlineTimeController;
    @Resource
    private RiskActionLimitController riskActionLimitController;

    @Override
    public ProcessResult process(JobContext jobContext) throws Exception {
        List<RiskObjectDO> riskObjects = riskObjectRepository.listByRiskStatus(RiskStatusEnum.PROTECT.getCode());
        Map<Integer, List<RiskObjectDO>> riskObjectGroup = riskObjects.stream().collect(Collectors.groupingBy(RiskObjectDO::getTargetType));
        List<RiskObjectDO> actionRiskObjects = riskObjectGroup.getOrDefault(RiskTargetEnum.ACTION.getValue(), new ArrayList<>());
        List<RiskObjectDO> robotRiskObjects = riskObjectGroup.getOrDefault(RiskTargetEnum.ROBOT.getValue(), new ArrayList<>());
        int actionOutProtect = 0;
        int robotOutProtect = 0;
        // 行动项限流判断
        for (RiskObjectDO riskObject : actionRiskObjects) {
            RiskObjectDO.TargetIdInfo targetIdInfo = riskObject.getTargetIdInfo();
            RiskActionEnum riskAction = RiskActionEnum.parse(targetIdInfo.getActionCode());
            if (riskAction != null) {
                if (riskActionLimitController.judgeOutProtect(new RiskActionRequest(targetIdInfo.getCorpId(), targetIdInfo.getUserId(),
                        riskAction, riskObject))) {
                    actionOutProtect++;
                }
            }
        }
        // 在线时长风控判断
        for (RiskObjectDO riskObject : robotRiskObjects) {
            RiskObjectDO.TargetIdInfo targetIdInfo = riskObject.getTargetIdInfo();
            if (riskRobotOnlineTimeController.judgeOutProtect(new RiskOnlineTimeRequest(targetIdInfo.getCorpId(), targetIdInfo.getUserId(), riskObject))) {
                robotOutProtect++;
            }
        }
        String message = "保护中的机器人有" + robotRiskObjects.size() + "个，有" + robotOutProtect + "个退出保护。保护中的行动项有" + actionRiskObjects.size() + "个，有" + actionOutProtect + "个退出保护";
        PlatformLogUtil.logInfo("风控状态更新", LogListUtil.newArrayList(message));
        return new ProcessResult(true);
    }
}
