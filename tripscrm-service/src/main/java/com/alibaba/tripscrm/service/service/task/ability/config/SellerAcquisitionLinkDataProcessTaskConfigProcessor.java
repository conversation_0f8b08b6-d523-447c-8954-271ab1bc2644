package com.alibaba.tripscrm.service.service.task.ability.config;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.activity.ActivityTargetTypeEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.bo.ActivityTaskInfoBO;
import com.alibaba.tripscrm.service.service.activity.ActivityContextService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/4/9 14:04
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerAcquisitionLinkDataProcessTaskConfigProcessor extends AbstractTaskConfigProcessor {
    private final ActivityContextService activityContextService;

    @Override
    public TaskInfoDO getInitConfig(Long activityId, Long spaceId, String taskName) {
        TaskInfoDO taskInfoDO = super.getInitConfig(activityId, spaceId, taskName);
        taskInfoDO.setEffectStartTime(new Date());
        Date effectEndTime = Date.from(LocalDate.of(2040, 12, 30).atStartOfDay(ZoneId.systemDefault()).toInstant());
        taskInfoDO.setEffectEndTime(effectEndTime);
        taskInfoDO.setEventSourceId(SwitchConfig.sellerAcquisitionLinkDataProcessEventSourceId);
        taskInfoDO.setTriggerType(Integer.parseInt(TaskTriggerTypeEnum.EVENT.getCode()));
        return taskInfoDO;
    }

    @Override
    public void postCreate(TaskInfoDO taskInfoDO) {
        processActivityContext(taskInfoDO);
    }

    @Override
    public void postUpdate(TaskInfoDO taskInfoDO) {
    }

    @Override
    public void postDelete(TaskInfoDO taskInfoDO) {

    }

    private void processActivityContext(TaskInfoDO taskInfoDO) {
        // 活动上下文信息
        ActivityTaskInfoBO activityTaskInfoBO = new ActivityTaskInfoBO();
        activityTaskInfoBO.setContextId(activityContextService.generateContextId());
        activityTaskInfoBO.setActivityId(taskInfoDO.getActivityId());
        activityTaskInfoBO.setTargetTypeEnum(ActivityTargetTypeEnum.SCRM_ACTIVITY_ID);
        activityTaskInfoBO.setTargetId(String.valueOf(taskInfoDO.getActivityId()));
        activityTaskInfoBO.setTagIdList(new ArrayList<>());
        JSONObject extraJson = new JSONObject();
        extraJson.put("dataTime", System.currentTimeMillis());
        activityTaskInfoBO.setExtraJson(extraJson);
        activityContextService.upsert(activityTaskInfoBO);
    }

    @Override
    public TaskType getTaskType() {
        return TaskType.SELLER_ACQUISITION_LINK_DATA_PROCESS;
    }
}
