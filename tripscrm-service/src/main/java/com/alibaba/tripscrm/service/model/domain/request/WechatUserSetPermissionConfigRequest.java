package com.alibaba.tripscrm.service.model.domain.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class WechatUserSetPermissionConfigRequest implements Serializable {
    private static final long serialVersionUID = 272489295037340714L;

    /**
     * 企微 userId
     */
    @NotBlank(message = "userId不可为空")
    private String userId;

    /**
     * 机器人使用成员列表
     */
    @NotEmpty(message = "使用成员列表不可为空")
    private List<String> memberList;

    /**
     * 当前操作用户的员工号
     */
    @JsonIgnore
    private transient String operatorId;

    /**
     * 业务空间Id
     */
    @JsonIgnore
    private transient Long spaceId;
}
