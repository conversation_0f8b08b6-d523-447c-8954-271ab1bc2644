package com.alibaba.tripscrm.service.service.impl.group;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.mapper.tddl.WechatGroupNewMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.dal.model.domain.query.WechatGroupNewParam;
import com.alibaba.tripscrm.domain.result.PageInfoDTO;
import com.alibaba.tripscrm.service.convert.WechatGroupConverter;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupNewQuery;
import com.alibaba.tripscrm.service.model.dto.group.WechatGroupDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.group.GroupNewService;
import com.alibaba.tripscrm.service.util.PageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @description：微信群组服务实现类
 * @Author：wangrui
 * @create：2025/8/15 16:07
 * @Filename：WechatGroupServiceImpl
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class GroupNewServiceImpl implements GroupNewService {
    private final WechatGroupNewMapper wechatGroupNewMapper;
    private final WechatGroupConverter wechatGroupConverter;

    /**
     * 新增微信群组
     *
     * @param wechatGroupDTO
     * @return
     */
    @Override
    public Integer insertSelective(WechatGroupDTO wechatGroupDTO) {
        if (Objects.isNull(wechatGroupDTO)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatGroupDO wechatGroupDO = wechatGroupConverter.convert2DO(wechatGroupDTO);
        return wechatGroupNewMapper.insertSelective(wechatGroupDO);
    }

    @Override
    public PageInfoDTO<WechatGroupDTO> pageQuery(WechatGroupNewQuery query) {
        WechatGroupNewParam param = buildParam(query);
        long count = wechatGroupNewMapper.countByParam(param);
        List<WechatGroupDO> dataDOList = wechatGroupNewMapper.selectByParamWithBLOBs(param);
        if (count <= 0 || CollectionUtils.isEmpty(dataDOList)) {
            return new PageInfoDTO<WechatGroupDTO>();
        }
        PageInfoDTO<WechatGroupDTO> pageInfo = PageUtils.getPageInfoDTO(dataDOList, wechatGroupConverter::convert2DTO);
        pageInfo.setPageNum(query.getPageNum());
        pageInfo.setPageSize(query.getPageSize());
        pageInfo.setTotal(count);
        return pageInfo;
    }

    /**
     * 查询微信群组
     *
     * @param query
     * @return
     */
    @Override
    public List<WechatGroupDTO> select(WechatGroupNewQuery query) {
        WechatGroupNewParam param = buildParam(query);
        List<WechatGroupDO> wechatGroupDOS = wechatGroupNewMapper.selectByParamWithBLOBs(param);
        if (CollectionUtils.isEmpty(wechatGroupDOS)) {
            return Collections.emptyList();
        }
        return wechatGroupConverter.convert2DTOs(wechatGroupDOS);
    }

    /**
     * 更新微信群组
     *
     * @param wechatGroupDTO
     * @return
     */
    @Override
    public Integer updateSelective(WechatGroupDTO wechatGroupDTO) {
        if (Objects.isNull(wechatGroupDTO) || Objects.isNull(wechatGroupDTO.getId())) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatGroupDO wechatGroupDO = wechatGroupConverter.convert2DO(wechatGroupDTO);
        return wechatGroupNewMapper.updateByPrimaryKeySelective(wechatGroupDO);
    }

    /**
     * 删除微信群组
     *
     * @param query
     * @return
     */
    @Override
    public Integer delete(WechatGroupNewQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        WechatGroupNewParam param = buildParam(query);
        return wechatGroupNewMapper.deleteByParam(param);
    }

    /**
     * 构建查询参数
     * @param query
     * @return
     */
    private WechatGroupNewParam buildParam(WechatGroupNewQuery query) {
        if (Objects.isNull(query)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }

        WechatGroupNewParam param = new WechatGroupNewParam();
        WechatGroupNewParam.Criteria criteria = param.or();

        if (Objects.nonNull(query.getId())) {
            criteria.andIdEqualTo(query.getId());
        }

        if (!CollectionUtils.isEmpty(query.getIdList())) {
            criteria.andIdIn(query.getIdList());
        }

        if (org.springframework.util.StringUtils.hasText(query.getChatId())) {
            criteria.andChatIdEqualTo(query.getChatId());
        }

        if (!CollectionUtils.isEmpty(query.getChatIdList())) {
            criteria.andChatIdIn(query.getChatIdList());
        }

        if (org.springframework.util.StringUtils.hasText(query.getCorpId())) {
            criteria.andCorpIdEqualTo(query.getCorpId());
        }

        if (!CollectionUtils.isEmpty(query.getCorpIdList())) {
            criteria.andCorpIdIn(query.getCorpIdList());
        }

        if (org.springframework.util.StringUtils.hasText(query.getName())) {
            criteria.andNameEqualTo(query.getName());
        }

        if (org.springframework.util.StringUtils.hasText(query.getNameLike())) {
            criteria.andNameLike("%" + query.getNameLike() + "%");
        }

        if (org.springframework.util.StringUtils.hasText(query.getOwnerUser())) {
            criteria.andOwnerUserEqualTo(query.getOwnerUser());
        }

        if (!CollectionUtils.isEmpty(query.getOwnerUserList())) {
            criteria.andOwnerUserIn(query.getOwnerUserList());
        }

        if (Objects.nonNull(query.getIsDeleted())) {
            criteria.andIsDeletedEqualTo(query.getIsDeleted());
        }

        if (org.springframework.util.StringUtils.hasText(query.getGroupTemplateId())) {
            criteria.andGroupTemplateIdEqualTo(query.getGroupTemplateId());
        }

        if (!CollectionUtils.isEmpty(query.getGroupTemplateIdList())) {
            criteria.andGroupTemplateIdIn(query.getGroupTemplateIdList());
        }

        if (Objects.nonNull(query.getPlatformType())) {
            criteria.andPlatformTypeEqualTo(query.getPlatformType().getCode().byteValue());
        }


        if (Objects.nonNull(query.getCreateTimeStart())) {
            criteria.andCreateTimeGreaterThanOrEqualTo(query.getCreateTimeStart());
        }

        if (Objects.nonNull(query.getCreateTimeEnd())) {
            criteria.andCreateTimeLessThanOrEqualTo(query.getCreateTimeEnd());
        }

        if (Objects.nonNull(query.getIsDeleted())) {
            criteria.andIsDeletedEqualTo(query.getIsDeleted());
        }

        if(Objects.nonNull(query.getPageNum()) && Objects.nonNull(query.getPageSize())){
            param.setPage(true);
            param.setPageSize(query.getPageSize());
            param.setPageStart(query.getPageNum());
        }
        param.appendOrderByClause(WechatGroupNewParam.OrderCondition.CREATETIME, WechatGroupNewParam.SortType.DESC);

        return param;
        }
}