package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.domain.FieldCountInfo;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.CustomerRelationOpenSearchManager;
import com.alibaba.tripscrm.service.manager.opensearch.GroupUserOpenSearchManager;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.domain.query.WechatCustomerListQuery;
import com.alibaba.tripscrm.service.model.domain.query.WechatInfoQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.query.SupportChatListQuery;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.SupportChatListRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.SupportChatListResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.service.strategy.websocket.vo.SupportChatVO;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripzoo.proxy.enums.CustomerRelationStatusEnum;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/6/25 16:20
 **/
@Service
public class SupportChatListProcessor implements WsEventProcessor {

    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private GroupUserOpenSearchManager groupUserOpenSearchManager;
    @Resource
    private CustomerRelationOpenSearchManager customerRelationOpenSearchManager;
    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private WechatCustomerService wechatCustomerService;

    public static final String DEFAULT_GROUP_AVATAR = "https://img.alicdn.com/imgextra/i3/O1CN01p7WVQd1dtFAFybKo7_!!6000000003793-2-tps-200-200.png";

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.SUPPORT_CHAT_LIST;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        if (StringUtils.isBlank(wsEvent.getUserId()) || wsEvent.getData() == null) {
            throw new TripscrmException("缺少必要参数");
        }
        SupportChatListRequest request = wsEvent.getData().toJavaObject(SupportChatListRequest.class);
        SupportChatListResponse response = new SupportChatListResponse();
        if (request == null || StringUtils.isBlank(request.getChatName())) {
            response.setSupportChatList(Lists.newArrayList());
            response.setCount(0L);

        } else {
            // 查询用户有关系的可聊天列表
            SupportChatListQuery query = new SupportChatListQuery();
            query.setChatName(request.getChatName());
            query.setUserId(wsEvent.getUserId());
            query.setPageNum(request.getPageNum());
            query.setPageSize(request.getPageSize());
            query.setChatType(request.getChatType());
            query.setCorpId(Optional.ofNullable(wsEvent.getCorpId()).orElse(SpaceInfoThreadLocalUtils.getCorpId()));
            // 结果填充
            response.setSupportChatList(querySupportChatList(query));
            response.setCount(querySupportChatCount(query));
        }
        // 推送到websocket
        wsEvent.setData((JSONObject) JSONObject.toJSON(response));
        webSocketFactory.pushMessageBySession(session, wsEvent, false);
    }

    @AteyeInvoker(description = "获取查询列表")
    public String query(String jsonStr, String userId) {
        SupportChatListRequest request = JSONObject.parseObject(jsonStr).toJavaObject(SupportChatListRequest.class);
        SupportChatListResponse response = new SupportChatListResponse();
        // 查询用户有关系的可聊天列表
        SupportChatListQuery query = new SupportChatListQuery();
        query.setChatName(request.getChatName());
        query.setUserId(userId);
        query.setPageNum(request.getPageNum());
        query.setPageSize(request.getPageSize());
        query.setChatType(request.getChatType());
        query.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
        // 结果填充
        response.setSupportChatList(querySupportChatList(query));
        response.setCount(querySupportChatCount(query));
        return JSONObject.toJSONString(response);
    }

    /**
     * 查询支持的群聊列表
     * @param query 查询条件
     * @return 支持聊天的对象列表
     */
    private List<SupportChatVO> querySupportChatList(SupportChatListQuery query) {
        // 群聊列表查询
        List<SupportChatVO> supportChatList = Optional.ofNullable(queryWechatGroupList(query))
                .orElse(new ArrayList<>())
                .stream().map(wechatGroupVO -> convert(wechatGroupVO, query.getCorpId())).collect(Collectors.toList());
        // 客户列表查询
        List<SupportChatVO> supportCustomerList = Optional.ofNullable(queryWechatCustomerList(query))
                .orElse(new ArrayList<>())
                .stream().map(this::convert).collect(Collectors.toList());
        return Lists.newArrayList(CollectionUtils.union(supportChatList, supportCustomerList));
    }

    /**
     * 构建查询群聊的查询参数
     * @param query 交互查询条件
     * @return 群聊查询条件
     */
    private WechatInfoQuery buildWechatInfoQuery(SupportChatListQuery query) {
        WechatInfoQuery wechatInfoQuery = new WechatInfoQuery();
        wechatInfoQuery.setUserId(query.getUserId());
        wechatInfoQuery.setCorpId(query.getCorpId());
        wechatInfoQuery.setPageNum(query.getPageNum());
        wechatInfoQuery.setPageSize(query.getPageSize());
        wechatInfoQuery.setName(query.getChatName());
        wechatInfoQuery.setCountFields(Lists.newArrayList("user_Id"));
        return wechatInfoQuery;
    }

    /**
     * 构建客户查询参数
     * @param query 交互查询条件
     * @return 客户查询条件
     */
    private WechatCustomerListQuery buildWechatCustomerQuery(SupportChatListQuery query) {
        WechatCustomerListQuery customerListQuery = new WechatCustomerListQuery();
        customerListQuery.setUserId(query.getUserId());
        customerListQuery.setName(query.getChatName());
        customerListQuery.setPageNum(query.getPageNum());
        customerListQuery.setPageSize(query.getPageSize());
        customerListQuery.setStatusList(Lists.newArrayList(CustomerRelationStatusEnum.FRIEND.getCode()));
        customerListQuery.setCountFields(Lists.newArrayList("user_id"));
        return customerListQuery;
    }

    /**
     * 对象转化
     * @param wechatGroupVO 群聊信息
     * @param corpId 组织ID
     * @return 可支持聊天视图对象
     */
    private SupportChatVO convert(WechatGroupVO wechatGroupVO, String corpId) {
        SupportChatVO supportChatVO = new SupportChatVO();
        supportChatVO.setChatType(ChatTypeEnum.GROUP.getValue());
        supportChatVO.setChatId(wechatGroupVO.getChatId());
        supportChatVO.setChatName(wechatGroupVO.getName());
        supportChatVO.setChatAvatar(DEFAULT_GROUP_AVATAR);
        supportChatVO.setGroupWork(CollectionUtils.isNotEmpty(wechatGroupService.groupWorkUserIdList(wechatGroupVO.getChatId(), corpId)));
        return supportChatVO;
    }

    /**
     * 对象转化
     * @param wechatCustomerVO 客户信息
     * @return 可支持聊天视图对象
     */
    private SupportChatVO convert(WechatCustomerVO wechatCustomerVO) {
        SupportChatVO supportChatVO = new SupportChatVO();
        supportChatVO.setChatType(ChatTypeEnum.SINGLE_FOR_CUSTOMER.getValue());
        supportChatVO.setChatId(wechatCustomerVO.getExternalUserId());
        supportChatVO.setChatName(wechatCustomerVO.getName());
        supportChatVO.setChatAvatar(wechatCustomerVO.getAvatarUrl());
        return supportChatVO;
    }

    /**
     * 群聊信息查询
     * @param query 查询条件
     * @return 群聊列表
     */
    private List<WechatGroupVO> queryWechatGroupList(SupportChatListQuery query) {
        if (CollectionUtils.isEmpty(query.getChatType()) || !query.getChatType().contains(ChatTypeEnum.GROUP.getValue())) {
            return Lists.newArrayList();
        }
        List<String> chatIdList = groupUserOpenSearchManager.getChatIds(buildWechatInfoQuery(query));
        if (CollectionUtils.isEmpty(chatIdList)) {
            return Lists.newArrayList();
        }
        return wechatGroupService.listByChatIdList(chatIdList, false);
    }

    /**
     * 查询群聊数量
     * @param query 查询条件
     * @return 群聊数量
     */
    private Long queryGroupCount(SupportChatListQuery query) {
        if (CollectionUtils.isEmpty(query.getChatType()) || !query.getChatType().contains(ChatTypeEnum.GROUP.getValue())) {
            return 0L;
        }
        List<FieldCountInfo> groupCountInfoList = groupUserOpenSearchManager.queryUserGroupCount(buildWechatInfoQuery(query));
        if (!CollectionUtils.isEmpty(groupCountInfoList) && MapUtils.isNotEmpty(groupCountInfoList.get(0).getCountMap())) {
            return groupCountInfoList.get(0).getCountMap().getOrDefault(query.getUserId(), 0L);
        }
        return 0L;
    }

    /**
     * 客户信息查询
     * @param query 查询条件
     * @return 客户列表
     */
    private List<WechatCustomerVO> queryWechatCustomerList(SupportChatListQuery query) {
        if (CollectionUtils.isEmpty(query.getChatType()) || !query.getChatType().contains(ChatTypeEnum.SINGLE_FOR_CUSTOMER.getValue())) {
            return Lists.newArrayList();
        }
        PageInfo<String> customerInfo = customerRelationOpenSearchManager.getCustomerExternalUserIdList(buildWechatCustomerQuery(query));
        if (customerInfo == null || CollectionUtils.isEmpty(customerInfo.getList())) {
            return Lists.newArrayList();
        }
        return wechatCustomerService.listByExternalUserIdList(customerInfo.getList());
    }

    /**
     * 客户数量查询
     * @param query 查询条件
     * @return 客户数量
     */
    private Long queryWechatCustomerCount(SupportChatListQuery query) {
        if (CollectionUtils.isEmpty(query.getChatType()) || !query.getChatType().contains(ChatTypeEnum.SINGLE_FOR_CUSTOMER.getValue())) {
            return 0L;
        }
        List<FieldCountInfo> customerCountInfoList = customerRelationOpenSearchManager.queryCustomerCount(buildWechatCustomerQuery(query));
        if (!CollectionUtils.isEmpty(customerCountInfoList) && MapUtils.isNotEmpty(customerCountInfoList.get(0).getCountMap())) {
            return customerCountInfoList.get(0).getCountMap().get(query.getUserId());
        }
        return 0L;
    }

    /**
     * 查询支持的群聊列表数量
     * @param query 查询条件
     * @return 结果数量
     */
    private Long querySupportChatCount(SupportChatListQuery query) {
        long result = 0L;
        // 群聊数据查询
        result += queryGroupCount(query);
        // 客户数据查询
        result += queryWechatCustomerCount(query);
        return result;
    }

}
