package com.alibaba.tripscrm.service.service.strategy.isv.route.group;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.WechatGroupDO;
import com.alibaba.tripscrm.domain.RobotDTO;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.model.domain.context.isv.IsvRouteContext;
import com.alibaba.tripscrm.service.service.space.SpaceService;
import com.alibaba.tripscrm.service.service.strategy.isv.route.AbstractActionRouteStrategy;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;
import com.alibaba.tripzoo.proxy.enums.IsvTypeEnum;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.alibaba.tripzoo.proxy.model.UserGroupConfigBO;
import com.alibaba.tripzoo.proxy.request.UserGroupConfigRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/2/27 10:17
 */
@Component
public abstract class AbstractGroupActionRouteStrategy extends AbstractActionRouteStrategy {
    @Resource
    private GroupService groupService;
    @Resource
    private WechatGroupService wechatGroupService;
    @Resource
    private SpaceService spaceService;

    protected static final List<IsvTypeEnum> NEED_SAVE_GROUP_ISV_TYPE_LIST = Lists.newArrayList(IsvTypeEnum.BI_LIN);

    @Override
    protected TripSCRMResult<List<WechatUserDTO>> getValidOnlineUserList(IsvRouteContext isvRouteContext) {
        TripSCRMResult<List<WechatUserDTO>> validOnlineUserList = super.getValidOnlineUserList(isvRouteContext);
        if (Objects.isNull(validOnlineUserList) || !validOnlineUserList.isSuccess() || CollectionUtils.isEmpty(validOnlineUserList.getData())) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_VALID_WECHAT_USER);
        }
        List<WechatUserDTO> wechatUserList = validOnlineUserList.getData();
        Map<IsvTypeEnum, List<String>> saveGroupUser = getSaveGroupUser(isvRouteContext.getChatId(), spaceService.getCorpIdBySpaceId(isvRouteContext.getSpaceId()));

        List<WechatUserDTO> result = new ArrayList<>();

        for (WechatUserDTO wechatUserDTO : wechatUserList) {
            if (CollectionUtils.isEmpty(wechatUserDTO.getRobotList())) {
                continue;
            }

            RobotDTO onlineRobotInfo = wechatUserDTO.getRobotList().stream().filter(robot -> Objects.equals(robot.getRobotStatus(), RobotStatusEnum.ONLINE.getCode())).findFirst().orElse(null);
            if (Objects.isNull(onlineRobotInfo)) {
                continue;
            }

            // 判断保存通讯录
            if (!NEED_SAVE_GROUP_ISV_TYPE_LIST.contains(IsvTypeEnum.valueOf(onlineRobotInfo.getIsvType()))) {
                result.add(wechatUserDTO);
                continue;
            }

            if (saveGroupUser.getOrDefault(IsvTypeEnum.valueOf(onlineRobotInfo.getIsvType()), new ArrayList<>()).contains(wechatUserDTO.getUserId())) {
                result.add(wechatUserDTO);
            }
        }

        return TripSCRMResult.success(result);
    }

    protected List<String> getGroupUserList(IsvRouteContext isvRouteContext) {
        if (onlyOwnerPermission(isvRouteContext) || needOwnerOrAdminPermission(isvRouteContext)) {
            WechatGroupDO wechatGroupDO = wechatGroupService.getByChatId(isvRouteContext.getChatId());
            if (Objects.isNull(wechatGroupDO)) {
                return new ArrayList<>();
            }
            List<String> userIdList = new ArrayList<>();
            userIdList.add(wechatGroupDO.getOwnerUser());
            if (needOwnerOrAdminPermission(isvRouteContext)) {
                List<String> adminUserIdList = Arrays.stream(Optional.ofNullable(wechatGroupDO.getAdminUser()).orElse("").split(",")).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                userIdList.addAll(adminUserIdList);
            }

            if (CollectionUtils.isEmpty(isvRouteContext.getUserIdList())) {
                return userIdList;
            }
            // 对userIdList和isvRouteContext.getUserIdList做交集
            return userIdList.stream().filter(isvRouteContext.getUserIdList()::contains).collect(Collectors.toList());
        }

        List<String> userIdList = wechatGroupService.listUserIdByChatIdAndUserTypeAndCorpId(isvRouteContext.getChatId(), GroupUserTypeEnum.USER, spaceService.getCorpIdBySpaceId(isvRouteContext.getSpaceId()));
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(isvRouteContext.getUserIdList())) {
            return userIdList;
        }
        // 对userIdList和isvRouteContext.getUserIdList做交集
        return userIdList.stream().filter(isvRouteContext.getUserIdList()::contains).collect(Collectors.toList());
    }

    private Map<IsvTypeEnum, List<String>> getSaveGroupUser(String chatId, String corpId) {
        Map<IsvTypeEnum, List<String>> result = new HashMap<>();
        for (IsvTypeEnum isvTypeEnum : NEED_SAVE_GROUP_ISV_TYPE_LIST) {
            UserGroupConfigRequest request = new UserGroupConfigRequest();
            request.setChatIdList(Lists.newArrayList(chatId));
            request.setCorpId(corpId);
            request.setIsvType(isvTypeEnum.getCode());
            ResultDO<List<UserGroupConfigBO>> resultDO = groupService.getUserGroupConfigInfo(request);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess() || CollectionUtils.isEmpty(resultDO.getModel())) {
                PlatformLogUtil.logFail("获取执行行动项对应的企微号，获取用户群配置失败/无数据", LogListUtil.newArrayList(request, resultDO));
                continue;
            }

            List<String> saveUserIdList = resultDO.getModel().stream()
                    .filter(userGroupConfigBO -> Objects.equals(1, userGroupConfigBO.getInConcat().intValue()) || Objects.equals(1, userGroupConfigBO.getIsOpen().intValue()))
                    .map(UserGroupConfigBO::getUserId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(saveUserIdList)) {
                PlatformLogUtil.logFail("获取执行行动项对应的企微号，群中不存在保存群到通讯录的企微成员", LogListUtil.newArrayList(request, resultDO));
                continue;
            }

            result.put(isvTypeEnum, saveUserIdList);
        }
        return result;
    }

    /**
     * 必须拥有群主权限
     */
    protected abstract boolean onlyOwnerPermission(IsvRouteContext isvRouteContext);

    /**
     * 必须拥有群主或管理员权限
     */
    protected abstract boolean needOwnerOrAdminPermission(IsvRouteContext isvRouteContext);
}
