package com.alibaba.tripscrm.service.service.strategy.sop;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.*;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.material.MaterialTemplateInfoQuery;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.convert.ResourceRelationConverter;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceRelationWayEnum;
import com.alibaba.tripscrm.service.enums.system.ResourceTypeEnum;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.ResourceRelationQuery;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatJoinGroupActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.common.ManagementGroupService;
import com.alibaba.tripscrm.service.service.common.ResourceRelationService;
import com.alibaba.tripscrm.service.service.material.MaterialService;
import com.alibaba.tripscrm.service.service.material.MaterialTemplateService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.service.wechat.WechatJoinGroupService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;
import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.INTERFACE;

/**
 * 群活码SOP
 *
 * <AUTHOR>
 * @date 2023-12-30 14:50:08
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatJoinGroupActivitySop extends AbstractActivitySop<WechatJoinGroupActivitySopConfigVO> {
    private final TaskService taskService;
    private final MaterialService materialService;
    private final MaterialTemplateService materialTemplateService;
    private final TagInfoService tagInfoService;
    private final ResourceRelationService resourceRelationService;
    private final ManagementGroupService managementGroupService;
    private final ResourceRelationConverter resourceRelationConverter;
    private final WechatJoinGroupService wechatJoinGroupService;

    @Override
    protected void checkConfigValid(WechatJoinGroupActivitySopConfigVO config, Boolean isUpdate) {
        // 客户自动打标标签列表
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            config.setCustomerAutoAddTagList(new ArrayList<>());
        }

        // 客户自动打标标签列表长度不可超过20
        if (config.getCustomerAutoAddTagList().size() > 20) {
            throw new RuntimeException("标签列表长度不可超过20");
        }

        // 企微客户群列表长度不可超过5
        if (!isUpdate && (CollectionUtils.isEmpty(config.getChatIdList()) || config.getChatIdList().size() > 5)) {
            throw new RuntimeException("企微客户群列表长度为1-5");
        }

        // 群成员上限不可为空
        if (Objects.isNull(config.getMaxGroupMemberCount())) {
            throw new RuntimeException("群成员上限不可为空");
        }

        // 群成员上限范围为1-195（正常200，留点临界的余地）
        if (config.getMaxGroupMemberCount() < 1 || config.getMaxGroupMemberCount() > 195) {
            throw new RuntimeException("群成员上限范围为1-195");
        }

        // 自动建群策略
        config.setAutoCreateGroup(Optional.ofNullable(config.getAutoCreateGroup()).orElse(false));
        if (config.getAutoCreateGroup()) {
            // 自动建群策略为true时，必填自动建群名称前缀
            if (!StringUtils.hasText(config.getAutoCreateGroupNamePrefix())) {
                throw new RuntimeException("自动建群名称前缀不可为空");
            }

            // 建群名称规则，前缀
            if (config.getAutoCreateGroupNamePrefix().length() > 12) {
                throw new RuntimeException("自动建群名称前缀不可超12字符");
            }

            // 建群名称规则，起始序号
            if (Objects.isNull(config.getAutoCreateGroupNameFirstIndex()) || config.getAutoCreateGroupNameFirstIndex() < 0 || config.getAutoCreateGroupNameFirstIndex() > 9999) {
                throw new RuntimeException("自动建群名称起始序号范围：0-9999");
            }

            // 当可用群聊少于此值时，自动建群
            if (Objects.isNull(config.getAutoCreateGroupMinValidCount()) || config.getAutoCreateGroupMinValidCount() <= 0 || config.getAutoCreateGroupMinValidCount() > 5) {
                throw new RuntimeException("可用群聊少于的范围为：1-5");
            }

//            // 自动建群，群主企微号不可为空
//            if (!StringUtils.hasText(config.getGroupOwnerUserId())) {
//                throw new RuntimeException("自动建群，群主企微号不可为空");
//            }

            // 自动建群，初始群成员列表
            if (config.getManageGroupId() == null) {
                throw new RuntimeException("群成员管理组不能为空");
            }
        }

        // 没有配置群欢迎语
        if (!NumberUtils.validLong(config.getGroupWelcomeTaskMaterialId())) {
            config.setGroupWelcomeTaskMaterialId(-1L);
            return;
        }

        MaterailInfoDO materailInfoDO = materialService.queryById(config.getGroupWelcomeTaskMaterialId());
        // 群欢迎语素材不存在
        if (Objects.isNull(materailInfoDO)) {
            throw new RuntimeException("群欢迎语素材不存在");
        }

        MaterialTemplateInfoQuery query = new MaterialTemplateInfoQuery();
        query.setId(materailInfoDO.getMaterialTemplateId());
        List<MaterialTemplateInfoDO> materialTemplateInfoDOList = materialTemplateService.list(query);
        if (CollectionUtils.isEmpty(materialTemplateInfoDOList)) {
            throw new RuntimeException("素材类型非群欢迎语");
        }

        // 素材类型非群欢迎语
        if (!Objects.equals(TaskType.GROUP_WELCOME, TaskType.getByCode(materialTemplateInfoDOList.get(0).getType()))) {
            throw new RuntimeException("素材类型非群欢迎语");
        }

        // 群码绑定群聊为空
        if (StringUtils.hasText(config.getState()) && !CollectionUtils.isEmpty(config.getChatIdList())) {
            WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getByState(config.getState());
            if (wechatJoinGroupDO != null && Optional.ofNullable(wechatJoinGroupDO.getChatIdList()).orElse(",").split(",").length <= config.getChatIdList().size()) {
                throw new RuntimeException("群码最低需要一个绑定群聊");
            }
        }
    }

    @Override
    protected void createTaskList(WechatJoinGroupActivitySopConfigVO config, Long activityId) {
        // 1.创建获取群活码任务
        createGetWechatJoinGroupTask(config, activityId);
        // 2.创建群活码管理任务
        createManagerWechatJoinGroupTask(config, activityId);
        // 3.创建群欢迎语任务
        createGroupWelcomeTask(config, activityId);
        // 4.创建客户打标任务
        createWechatCustomerAddTagTask(config, activityId);
        // 5.创建群欢迎语（私聊）任务
        createGroupWelcomeByRobotTask(config, activityId);
    }

    @Override
    protected void updateTaskList(WechatJoinGroupActivitySopConfigVO config) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        // 1.更新获取群活码任务
        if (taskType2TaskInfoDO.containsKey(TaskType.GET_WECHAT_JOIN_GROUP.getCode())) {
            TaskInfoDO getWechatJoinGroupTask = taskType2TaskInfoDO.get(TaskType.GET_WECHAT_JOIN_GROUP.getCode());
            updateGetWechatJoinGroupTask(config, getWechatJoinGroupTask);
        } else {
            createGetWechatJoinGroupTask(config, config.getActivityId());
        }
        // 2.更新群活码管理任务
        if (taskType2TaskInfoDO.containsKey(TaskType.MANAGER_WECHAT_JOIN_GROUP.getCode())) {
            TaskInfoDO managerWechatJoinGroupTask = taskType2TaskInfoDO.get(TaskType.MANAGER_WECHAT_JOIN_GROUP.getCode());
            updateManagerWechatJoinGroupTask(config, managerWechatJoinGroupTask);
        } else {
            createManagerWechatJoinGroupTask(config, config.getActivityId());
        }
        // 3.更新群欢迎语任务
        if (taskType2TaskInfoDO.containsKey(TaskType.GROUP_WELCOME.getCode())) {
            TaskInfoDO groupWelcomeTask = taskType2TaskInfoDO.get(TaskType.GROUP_WELCOME.getCode());
            updateGroupWelcomeTask(config, groupWelcomeTask);
        } else {
            createGroupWelcomeTask(config, config.getActivityId());
        }
        // 4.更新客户打标任务
        if (taskType2TaskInfoDO.containsKey(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode())) {
            TaskInfoDO wechatCustomerAddTagTask = taskType2TaskInfoDO.get(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode());
            updateWechatCustomerAddTagTask(config, wechatCustomerAddTagTask);
        } else {
            createWechatCustomerAddTagTask(config, config.getActivityId());
        }
        // 5.更新群欢迎语（私聊）任务
        if (taskType2TaskInfoDO.containsKey(TaskType.ROBOT_CHAT_MESSAGE.getCode())) {
            TaskInfoDO groupWelcomeByRobotTask = taskType2TaskInfoDO.get(TaskType.ROBOT_CHAT_MESSAGE.getCode());
            updateGroupWelcomeByRobotTask(config, groupWelcomeByRobotTask);
        } else {
            createGroupWelcomeByRobotTask(config, config.getActivityId());
        }
    }

    private void createGetWechatJoinGroupTask(WechatJoinGroupActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.GET_WECHAT_JOIN_GROUP).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("userIdList", config.getUserIdList());
        extraInfo.put("chatIdList", getNewestChatIdList(config.getState(), config.getChatIdList()));
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建获取群活码任务失败");
        }
    }

    private void createManagerWechatJoinGroupTask(WechatJoinGroupActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.MANAGER_WECHAT_JOIN_GROUP).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setMaterialId(config.getGroupNoticeMaterialId());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        if (config.getGroupWelcomeTaskMaterialId() > 0) {
            extraInfo.put("materialId", config.getGroupWelcomeTaskMaterialId());
        }

        extraInfo.put("groupMemberUserIdList", config.getGroupMemberUserIdList());
        extraInfo.put("autoCreateGroup", config.getAutoCreateGroup());
        extraInfo.put("autoCreateGroupNamePrefix", config.getAutoCreateGroupNamePrefix());
        extraInfo.put("autoCreateGroupNameFirstIndex", config.getAutoCreateGroupNameFirstIndex());
        extraInfo.put("autoCreateGroupMinValidCount", config.getAutoCreateGroupMinValidCount());
        extraInfo.put("maxGroupMemberCount", config.getMaxGroupMemberCount());
        extraInfo.put("groupAutoAddTagList", config.getGroupAutoAddTagList());
        extraInfo.put("manageGroupId", config.getManageGroupId());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建群活码管理任务失败");
        }
        // 绑定资源
        createResource(config, taskInfoDO.getId());
    }

    /**
     * 创建资源
     * @param config SOP配置信息
     * @param taskId 任务Id
     */
    private void createResource(WechatJoinGroupActivitySopConfigVO config, Long taskId) {
        // 群聊资源
        createGroupResource(config.getChatIdList(), taskId);
        // 管理组资源
        createManagementGroupResource(config.getManageGroupId(), taskId);
    }

    /**
     * 创建群聊资源
     * @param chatIdList 群聊id列表
     * @param taskId 任务id
     */
    private void createGroupResource(List<String> chatIdList, Long taskId) {
        if (CollectionUtils.isEmpty(chatIdList) || !NumberUtils.validLong(taskId)) {
            return ;
        }
        ArrayList<ResourceRelationDO> resourceRelationList = new ArrayList<>();
        for (String chatId : chatIdList) {
            resourceRelationList.add(resourceRelationConverter.buildGroupManageTaskResourceRelation(String.valueOf(taskId), chatId, ResourceRelationWayEnum.INIT_CREATE));
        }
        resourceRelationService.batchAdd(resourceRelationList);
    }

    /**
     * 创建管理组资源
     * @param managementId 管理组Id
     * @param taskId 任务Id
     */
    private void createManagementGroupResource(Long managementId, Long taskId) {
        if (!NumberUtils.validLong(managementId) || !NumberUtils.validLong(taskId)) {
            return ;
        }
        resourceRelationService.add(resourceRelationConverter.buildManagementGroupResourceRelation(String.valueOf(managementId), String.valueOf(taskId), ResourceRelationWayEnum.INIT_CREATE));
    }

    private void createGroupWelcomeTask(WechatJoinGroupActivitySopConfigVO config, Long activityId) {
        // 没有配置群欢迎语
        if (Objects.isNull(config.getGroupWelcomeTaskMaterialId()) || Objects.equals(-1L, config.getGroupWelcomeTaskMaterialId())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.GROUP_WELCOME).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setMaterialId(config.getGroupWelcomeTaskMaterialId());
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        if (config.getSendWelcomeMessageIntervalSeconds() > 0) {
            extraInfo.put("intervalSeconds", config.getSendWelcomeMessageIntervalSeconds());
        }
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建群欢迎语任务失败");
        }
    }

    private void createWechatCustomerAddTagTask(WechatJoinGroupActivitySopConfigVO config, Long activityId) {
        // 没有配置自动打标
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.WECHAT_CUSTOMER_ADD_TAG).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setEventSourceId(SwitchConfig.wechatCustomerAddTagEventSourceId);

        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("tagList", config.getCustomerAutoAddTagList());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建客户打标任务失败");
        }
    }


    private void createGroupWelcomeByRobotTask(WechatJoinGroupActivitySopConfigVO config, Long activityId) {
        // 没有配置群欢迎语(私聊)
        if (Objects.isNull(config.getGroupWelcomeByRobotTaskMaterialId()) || Objects.equals(-1L, config.getGroupWelcomeByRobotTaskMaterialId())) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.ROBOT_CHAT_MESSAGE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setMaterialId(config.getGroupWelcomeByRobotTaskMaterialId());
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        taskInfoDO.setEventSourceId(SwitchConfig.wechatCustomerJoinGroupWelcomeByRobotEventSourceId);
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("taskMessageTypeId", SwitchConfig.TASK_MESSAGE_TYPE_IM);
        extraInfo.put("taskMessageSceneId", SwitchConfig.TASK_MESSAGE_SCENE_QUNMA_SILIAO_RW);
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new RuntimeException("创建群欢迎语(私聊)任务失败");
        }
    }

    private void updateGetWechatJoinGroupTask(WechatJoinGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.put("chatIdList", getNewestChatIdList(config.getState(), config.getChatIdList()));
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        taskInfoDO.setTriggerType(Integer.parseInt(INTERFACE.getCode()));
        taskInfoDO.setEventSourceId(-1L);
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    /**
     * 获取最新的群聊id列表
     * @param state state
     * @param needRemoveChatIdList 需要移除的群聊id列表
     * @return 最新chatId列表
     */
    private List<String> getNewestChatIdList(String state, List<String> needRemoveChatIdList) {
        if (!StringUtils.hasText(state)) {
            return needRemoveChatIdList;
        }
        WechatJoinGroupDO wechatJoinGroupDO = wechatJoinGroupService.getByState(state);
        if (wechatJoinGroupDO == null) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(needRemoveChatIdList)) {
            return Arrays.stream(Optional.ofNullable(wechatJoinGroupDO.getChatIdList()).orElse("").split(",")).collect(Collectors.toList());
        }
        List<String> oldChatIdList = Arrays.stream(Optional.ofNullable(wechatJoinGroupDO.getChatIdList()).orElse("").split(",")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oldChatIdList)) {
            return Lists.newArrayList();
        }
        oldChatIdList.removeAll(needRemoveChatIdList);
        return oldChatIdList;
    }

    private void updateManagerWechatJoinGroupTask(WechatJoinGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        taskInfoDO.setMaterialId(config.getGroupNoticeMaterialId());
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extraInfo.remove("groupOwnerUserId");
        extraInfo.put("groupOwnerUserId", config.getGroupOwnerUserId());
        extraInfo.put("manageGroupId", config.getManageGroupId());
        extraInfo.put("groupAutoAddTagList", config.getGroupAutoAddTagList());
        extraInfo.put("groupMemberUserIdList", config.getGroupMemberUserIdList());
        extraInfo.put("maxGroupMemberCount", config.getMaxGroupMemberCount());
        extraInfo.put("autoCreateGroup", config.getAutoCreateGroup());
        extraInfo.put("autoCreateGroupNamePrefix", config.getAutoCreateGroupNamePrefix());
        extraInfo.put("autoCreateGroupNameFirstIndex", config.getAutoCreateGroupNameFirstIndex());
        extraInfo.put("autoCreateGroupMinValidCount", config.getAutoCreateGroupMinValidCount());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void updateGroupWelcomeTask(WechatJoinGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了欢迎语配置
        if (Objects.isNull(config.getGroupWelcomeTaskMaterialId()) || Objects.equals(-1L, config.getGroupWelcomeTaskMaterialId())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        taskInfoDO.setMaterialId(config.getGroupWelcomeTaskMaterialId());
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        if (!CollectionUtils.isEmpty(config.getUserIdList())) {
            taskInfoDO.setSendUserId(String.join(",", config.getUserIdList()));
            extraInfo.put("robotUser", String.join(",", config.getUserIdList()));
        }
        extraInfo.put("intervalSeconds", config.getSendWelcomeMessageIntervalSeconds());
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void updateWechatCustomerAddTagTask(WechatJoinGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了打标配置
        if (CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        extInfo.put("tagList", config.getCustomerAutoAddTagList());
        taskInfoDO.setExtInfo(extInfo.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    private void updateGroupWelcomeByRobotTask(WechatJoinGroupActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 删除了欢迎语(私聊)配置
        if (Objects.isNull(config.getGroupWelcomeByRobotTaskMaterialId()) || Objects.equals(-1L, config.getGroupWelcomeByRobotTaskMaterialId())) {
            TaskInfoDO record = new TaskInfoDO();
            record.setId(taskInfoDO.getId());
            record.setDeleted(1);
            taskService.updateTaskInfo(record, false);
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        taskInfoDO.setMaterialId(config.getGroupWelcomeByRobotTaskMaterialId());
        taskInfoDO.setEffectStartTime(config.getEffectStartTime());
        taskInfoDO.setEffectEndTime(config.getEffectEndTime());
        JSONObject extraInfo = JSONObject.parseObject(taskInfoDO.getExtInfo());
        if (!CollectionUtils.isEmpty(config.getUserIdList())) {
            taskInfoDO.setSendUserId(String.join(",", config.getUserIdList()));
            extraInfo.put("robotUser", String.join(",", config.getUserIdList()));
        }
        taskInfoDO.setExtInfo(extraInfo.toJSONString());
        taskService.updateTaskInfo(taskInfoDO, false);
    }

    @Override
    public void fillInfo(ActivitySopVO<WechatJoinGroupActivitySopConfigVO> activitySopVO) {
        WechatJoinGroupActivitySopConfigVO config = activitySopVO.getConfig();
        // 客户标签信息
        if (!CollectionUtils.isEmpty(config.getCustomerAutoAddTagList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(config.getCustomerAutoAddTagList());
            config.setCustomerAutoAddTagList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
            config.setCustomerAutoAddTagNameList(tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        }

        // 群聊标签信息
        if (!CollectionUtils.isEmpty(config.getGroupAutoAddTagList())) {
            List<TagInfoDTO> tagInfoList = tagInfoService.selectByTagIdList(config.getGroupAutoAddTagList());
            config.setGroupAutoAddTagList(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.toList()));
            config.setGroupAutoAddTagNameList(tagInfoList.stream().map(TagInfoDTO::getName).collect(Collectors.toList()));
        }

        // 群管理组
        if (config.getManageGroupId() != null) {
            ManagementGroupDO managementGroupDO = managementGroupService.queryById(config.getManageGroupId());
            config.setManageGroupName(managementGroupDO != null ? managementGroupDO.getName() : null);
        }

        // 群欢迎语素材信息
        Long groupWelcomeTaskMaterialId = Optional.ofNullable(config.getGroupWelcomeTaskMaterialId()).orElse(-1L);
        if (groupWelcomeTaskMaterialId > 0L) {
            MaterailInfoDO materailInfoDO = materialService.queryById(groupWelcomeTaskMaterialId);
            config.setGroupWelcomeTaskMaterialName(materailInfoDO.getName());
        }

        // 群公告素材信息
        Long groupNoticeTaskMaterialId = Optional.ofNullable(config.getGroupNoticeMaterialId()).orElse(-1L);
        if (groupNoticeTaskMaterialId > 0L) {
            MaterailInfoDO materailInfoDO = materialService.queryById(groupNoticeTaskMaterialId);
            config.setGroupNoticeMaterialName(materailInfoDO.getName());
        }

        // 任务数据统计
        TaskInfoDO getWechatJoinGroupTask = getTaskInfo(TaskType.GET_WECHAT_JOIN_GROUP, config.getActivityId());
        if (Objects.isNull(getWechatJoinGroupTask)) {
            return;
        }

        // 群欢迎语（私聊）素材信息
        Long groupWelcomeByRobotTaskMaterialId = Optional.ofNullable(config.getGroupWelcomeByRobotTaskMaterialId()).orElse(-1L);
        if (groupWelcomeByRobotTaskMaterialId > 0L) {
            MaterailInfoDO materailInfoDO = materialService.queryById(groupWelcomeByRobotTaskMaterialId);
            config.setGroupWelcomeByRobotTaskMaterialName(materailInfoDO.getName());
        }

        JSONObject extraInfo = TaskConfigProcessorFactory.getExtraInfo(getWechatJoinGroupTask);
        activitySopVO.getExtraInfo().put("qrCodeUrl", extraInfo.getString("qrCodeUrl"));
        activitySopVO.getExtraInfo().put("joinCount", extraInfo.getInteger("joinCount"));
        activitySopVO.getExtraInfo().put("joinWithFollowUserCount", extraInfo.getInteger("joinWithFollowUserCount"));
        activitySopVO.getExtraInfo().put("leaveCount", extraInfo.getInteger("leaveCount"));
        activitySopVO.getExtraInfo().put("leaveWithFollowUserCount", extraInfo.getInteger("leaveWithFollowUserCount"));
        activitySopVO.getExtraInfo().put("state", extraInfo.getString("state"));

        TaskInfoDO manageGroupTask = getTaskInfo(TaskType.MANAGER_WECHAT_JOIN_GROUP, config.getActivityId());
        if (Objects.isNull(manageGroupTask)) {
            return;
        }
        ResourceRelationQuery query = new ResourceRelationQuery();
        query.setType(ResourceRelationTypeEnum.WECHAT_JOIN_GROUP.getCode());
        query.setPage(false);
        query.setSourceType(ResourceTypeEnum.TASK.getCode().byteValue());
        query.setSourceId(String.valueOf(manageGroupTask.getId()));
        List<ResourceRelationDO> resourceRelationList = Optional.ofNullable(resourceRelationService.query(query)).orElse(new ArrayList<>());
        activitySopVO.getExtraInfo().put("groupCount", resourceRelationList.size());
    }

    /**
     * 获取任务信息
     * @param taskType 任务类型
     * @param activityId 活动类型
     * @return 任务
     */
    private TaskInfoDO getTaskInfo(TaskType taskType, Long activityId) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(activityId);
        query.setType(taskType.getCode());
        List<TaskInfoDO> taskList = taskService.query(query);
        return taskList.stream().max(Comparator.comparing(TaskInfoDO::getGmtCreate)).orElse(null);
    }

    @Override
    public SopTypeEnum getSopType() {
        return SopTypeEnum.WECHAT_JOIN_GROUP;
    }
}
