package com.alibaba.tripscrm.service.service.impl.hsf.seller;

import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.request.seller.acquisition.SellerAcquisitionUnLimitQrCodeRequest;
import com.alibaba.tripscrm.domain.response.seller.acquisition.SellerAcquisitionUnLimitQrCodeResponse;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.material.ItemTypeEnum;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.seller.SellerInfoQuery;
import com.alibaba.tripscrm.service.model.dto.seller.SellerInfoDTO;
import com.alibaba.tripscrm.service.seller.SellerAcquisitionService;
import com.alibaba.tripscrm.service.service.second.ShortLinkService;
import com.alibaba.tripscrm.service.service.seller.SellerInfoService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.material.MaterialUtils;
import com.alibaba.tripzoo.proxy.api.service.WxMiniProgramService;
import com.alibaba.tripzoo.proxy.request.GetWechatMiniProgramUnlimitedQrCodeRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.fliggy.pokemon.common.utils.NumberUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/3/5 20:34
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@HSFProvider(serviceInterface = SellerAcquisitionService.class)
public class SellerAcquisitionServiceImpl implements SellerAcquisitionService {
    private static final String CATEGORY_ID = "categoryId";
    private static final String CHANNEL_ID = "channelId";
    private static final String ITEM_ID = "itemId";
    private static final String ITEM_TYPE = "itemType";
    private static final String ITEM_NAME = "itemName";
    private static final String ARR_CITY_DIVISION_ID = "arrCityDivisionId";
    private static final String ARR_CITY_NAME = "arrCityName";
    private static final String ACTIVITY_ID = "activityId";
    private static final String SELLER_ID = "sellerId";

    private final SellerInfoService sellerInfoService;
    private final ShortLinkService shortLinkService;
    private final WxMiniProgramService wxMiniProgramService;

    @Override
    @ServiceLog("获取商家获客活动链微信太阳码")
    public TripSCRMResult<SellerAcquisitionUnLimitQrCodeResponse> getAcquisitionUnLimitQrCode(SellerAcquisitionUnLimitQrCodeRequest request) {
        if (Objects.isNull(request)
                || !NumberUtils.biggerThanZero(request.getSellerId())
                || !SwitchConfig.sellerAcquisitionChannelIdList.contains(String.valueOf(request.getChannelId()))
                || !Lists.newArrayList(ItemTypeEnum.GOODS.getItemType(), ItemTypeEnum.HOTEL.getItemType()).contains(request.getItemType())
                || !StringUtils.hasText(request.getItemId())
                || !StringUtils.hasText(request.getCategoryId())
        ) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_PARAMS);
        }

        // 先校验商家信息
        SellerInfoQuery sellerInfoQuery = new SellerInfoQuery();
        sellerInfoQuery.setSellerId(String.valueOf(request.getSellerId()));
        sellerInfoQuery.setEnabled((byte) 1);
        sellerInfoQuery.setHasSignedAgreement((byte) 1);
        List<SellerInfoDTO> sellerInfoList = sellerInfoService.listByCondition(sellerInfoQuery);
        if (CollectionUtils.isEmpty(sellerInfoList)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.INVALID_SELLER_INFO_IN_SCRM);
        }

        Map<String, Long> defaultSellerAcquisitionActivityInfo = sellerInfoList.get(0).getExtraInfo().getDefaultSellerAcquisitionActivityInfo();

        Long defaultSellerAcquisitionActivityId = MapUtils.getLong(defaultSellerAcquisitionActivityInfo, EnvUtils.getEnvironment());
        if (!NumberUtils.biggerThanZero(defaultSellerAcquisitionActivityId)) {
            return TripSCRMResult.fail(TripSCRMErrorCode.NO_DEFAULT_SELLER_ACQUISITION_ACTIVITY);
        }

        String link = SwitchConfig.sellerAcquisitionLinkUrl;
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(CATEGORY_ID, String.valueOf(request.getCategoryId()));
        paramMap.put(CHANNEL_ID, String.valueOf(request.getChannelId()));
        paramMap.put(SELLER_ID, String.valueOf(request.getSellerId()));
        paramMap.put(ITEM_ID, String.valueOf(request.getItemId()));
        paramMap.put(ITEM_NAME, String.valueOf(request.getItemName()));
        paramMap.put(ITEM_TYPE, String.valueOf(request.getItemType()));
        paramMap.put(ARR_CITY_DIVISION_ID, String.valueOf(request.getArrCityDivisionId()));
        paramMap.put(ARR_CITY_NAME, request.getArrCityName());
        paramMap.put(ACTIVITY_ID, String.valueOf(defaultSellerAcquisitionActivityId));

        String unlimitQrCodeScene = shortLinkService.getUnlimitQrCodeScene(MaterialUtils.concatHttpUrlParam(link, paramMap));
        if (!StringUtils.hasText(unlimitQrCodeScene)) {
            PlatformLogUtil.logFail("获取商家获客活动链微信太阳码，生成微信太阳码scene失败");
            return TripSCRMResult.fail(TripSCRMErrorCode.GENERATE_WECHAT_UN_LIMIT_QR_CODE_FAIL);
        }

        GetWechatMiniProgramUnlimitedQrCodeRequest getWechatMiniProgramUnlimitedQrCodeRequest = new GetWechatMiniProgramUnlimitedQrCodeRequest();
        getWechatMiniProgramUnlimitedQrCodeRequest.setPage(SwitchConfig.unlimitQrCodeSceneUrl);
        getWechatMiniProgramUnlimitedQrCodeRequest.setScene(unlimitQrCodeScene);
        ResultDO<String> unlimitedQrCode = wxMiniProgramService.getUnlimitedQrCode(getWechatMiniProgramUnlimitedQrCodeRequest);
        if (Objects.isNull(unlimitedQrCode) || !unlimitedQrCode.getSuccess() || !StringUtils.hasText(unlimitedQrCode.getModel())) {
            PlatformLogUtil.logFail("获取商家获客活动链微信太阳码，生成微信太阳码失败");
            return TripSCRMResult.fail(TripSCRMErrorCode.GENERATE_WECHAT_UN_LIMIT_QR_CODE_FAIL);
        }

        SellerAcquisitionUnLimitQrCodeResponse sellerAcquisitionUnLimitQrCodeResponse = new SellerAcquisitionUnLimitQrCodeResponse();
        sellerAcquisitionUnLimitQrCodeResponse.setLink(unlimitedQrCode.getModel());
        return TripSCRMResult.success(sellerAcquisitionUnLimitQrCodeResponse);
    }
}
