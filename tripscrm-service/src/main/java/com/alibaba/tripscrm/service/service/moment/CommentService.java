package com.alibaba.tripscrm.service.service.moment;

import com.alibaba.tripscrm.service.model.domain.request.SendCommentRequest;
import com.alibaba.tripscrm.service.model.vo.wechat.MomentCommentVO;

import java.util.List;

/**
 * 朋友圈评论
 *
 * <AUTHOR>
 */
public interface CommentService {

    /**
     * 发送评论
     * @param request
     * @return
     */
    Boolean sendComent(SendCommentRequest request);

    /**
     * 删除评论
     * @param commentId
     * @param userId
     */
    Boolean deleteComment(Long commentId, String userId, Long taskId);

    /**
     * 获取评论列表
     * @param id
     * @return
     */
    List<MomentCommentVO> listByTaskId(Long id, String userIds);


}
