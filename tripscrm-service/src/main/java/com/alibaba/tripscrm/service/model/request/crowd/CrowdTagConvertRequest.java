package com.alibaba.tripscrm.service.model.request.crowd;

import com.alibaba.tripzoo.proxy.enums.PlatformTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 人群打标转换请求
 *
 * <AUTHOR>
 * @date 2024/10/13
 */
@Data
public class CrowdTagConvertRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 源平台：5-诸葛、6-图灵
     */
    private Integer sourcePlatform;

    /**
     * 目标平台：5-诸葛、6-图灵
     */
    private Integer targetPlatform;

    /**
     * 源人群ID（诸葛人群ID）
     */
    @NotBlank(message = "源人群ID不能为空")
    private String sourceCrowdId;

    /**
     * 目标人群ID（图灵人群ID）
     */
    @NotBlank(message = "目标人群ID不能为空")
    private String targetCrowdId;

    /**
     * 一级标签
     */
    @NotBlank(message = "一级标签不能为空")
    private String firstLevelTag;

    /**
     * 二级标签
     */
    private String secondLevelTag;

    /**
     * 执行类型(0-单次 1-周期 2-源人群有效时间)
     */
    @NotNull(message = "执行类型不能为空")
    private Integer executeType;
    /**
     * 同步开始日期
     */
    private String startDate;

    /**
     * 同步结束日期
     */
    private String endDate;

    /**
     * 创建人
     */
    private String creatorId;
}