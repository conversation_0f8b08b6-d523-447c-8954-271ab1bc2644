package com.alibaba.tripscrm.service.service.strategy.websocket.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.model.domain.data.ChatConversationDO;
import com.alibaba.tripscrm.dal.repository.ChatConversationRepository;
import com.alibaba.tripscrm.service.enums.wechat.ChatTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WsEventTypeEnum;
import com.alibaba.tripscrm.service.service.fusionchat.ChatConversationService;
import com.alibaba.tripscrm.service.service.strategy.websocket.WebSocketFactory;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsContextInfo;
import com.alibaba.tripscrm.service.service.strategy.websocket.domain.WsEvent;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.request.AsyncUpdateUserConversationConfigRequest;
import com.alibaba.tripscrm.service.service.strategy.websocket.param.response.AsyncUpdateUserConversationConfigResponse;
import com.alibaba.tripscrm.service.service.strategy.websocket.processor.WsEventProcessor;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.api.service.CustomerService;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.request.MarkAsReadMessageForCustomerRequest;
import com.alibaba.tripzoo.proxy.request.MarkAsReadMessageForGroupRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.Resource;

/**
 * 更新用户会话配置 ws事件处理器
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
@Service
public class AsyncUpdateUserConversationConfigProcessor implements WsEventProcessor {
    /**
     * 异步操作传输数据的过期时间 30分钟
     */
    public static final Integer ASYNC_OPERATE_EXPIRE_SECOND = 30 * 60;
    public static final String ASYNC_CACHE_KEY = "updateUserConversationConfig|";
    @Resource
    private ChatConversationRepository chatConversationRepository;
    @Resource
    private LdbTairManager ldbTairManager;
    @Resource
    private GroupService groupService;
    @Resource
    private CustomerService customerService;
    @Resource
    private ChatConversationService chatConversationService;
    @Resource
    private WebSocketFactory webSocketFactory;
    @Resource
    private WechatUserService wechatUserService;

    @Override
    public WsEventTypeEnum type() {
        return WsEventTypeEnum.ASYNC_UPDATE_USER_CONVERSATION_CONFIG;
    }

    @Override
    public void handle(WebSocketSession session, WsEvent wsEvent) {
        AsyncUpdateUserConversationConfigRequest request = wsEvent.getData().toJavaObject(AsyncUpdateUserConversationConfigRequest.class);
        if (wsEvent.getUserId() == null || request.getChatId() == null || request.getChatType() == null) {
            throw new TripscrmException("缺少必要的参数 userId or chatId or chatType");
        }
        WsContextInfo wsContextInfo = webSocketFactory.getWsContextInfo(session);
        if (!wechatUserService.checkHasLock(wsEvent.getUserId(), wsContextInfo.getAccount().getUserId(), wsContextInfo.getSpaceId())) {
            throw new TripscrmException("未锁定当前企微账号，无法执行该操作");
        }
        ChatConversationDO chatConversation = chatConversationRepository.getByUniqueKey(wsEvent.getUserId(), request.getChatId(), request.getChatType());
        ResultDO<String> result = this.markAsReadMessage(chatConversation, request.getMarkRead());
        if (result != null && result.getSuccess()) {
            // 保存消息上下文，以便回调时取到消息数据，key为requestId
            wsEvent.setTimestamp(System.currentTimeMillis());
            ldbTairManager.put(ASYNC_CACHE_KEY + result.getModel(), JSONObject.toJSONString(wsEvent), ASYNC_OPERATE_EXPIRE_SECOND);
            PlatformLogUtil.logFail("更新用户会话配置 ws事件处理器，标记已读/未读请求调用成功", LogListUtil.newArrayList(result));
        } else {
            PlatformLogUtil.logFail("更新用户会话配置 ws事件处理器，标记已读/未读请求调用失败", LogListUtil.newArrayList(result));
            throw new TripscrmException("更新用户会话配置失败：" + result.getResultMessage());
        }
    }

    public ResultDO<String> markAsReadMessage(ChatConversationDO chatConversation, Boolean markRead) {
        switch (ChatTypeEnum.parse(chatConversation.getChatType())) {
            case GROUP:
                MarkAsReadMessageForGroupRequest markAsReadMessageForGroupRequest = new MarkAsReadMessageForGroupRequest();
                markAsReadMessageForGroupRequest.setUserId(chatConversation.getUserId());
                markAsReadMessageForGroupRequest.setChatId(chatConversation.getChatId());
                markAsReadMessageForGroupRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
                markAsReadMessageForGroupRequest.setType(markRead ? 1 : 0);
                return groupService.asyncMarkAsReadMessage(markAsReadMessageForGroupRequest);
            case SINGLE_FOR_CUSTOMER:
                MarkAsReadMessageForCustomerRequest markAsReadMessageForCustomerRequest = new MarkAsReadMessageForCustomerRequest();
                markAsReadMessageForCustomerRequest.setUserId(chatConversation.getUserId());
                markAsReadMessageForCustomerRequest.setExternalUserId(chatConversation.getChatId());
                markAsReadMessageForCustomerRequest.setCorpId(SpaceInfoThreadLocalUtils.getCorpId());
                markAsReadMessageForCustomerRequest.setType(markRead ? 1 : 0);
                return customerService.asyncMarkAsReadMessage(markAsReadMessageForCustomerRequest);
            default:
                break;
        }
        return null;
    }

    /**
     * 成功发送消息后置动作
     */
    public WsEvent afterCreate(String requestId) {
        // 获取消息上下文
        String eventJson = (String) ldbTairManager.get(ASYNC_CACHE_KEY + requestId);
        if (eventJson != null) {
            return JSONObject.parseObject(eventJson, WsEvent.class);
        }
        return null;
    }

    public void pushMessageByDistributed(WsEvent wsEvent, Boolean markRead, ChatConversationDO chatConversation) {
        AsyncUpdateUserConversationConfigResponse asyncUpdateUserConversationConfigResponse = new AsyncUpdateUserConversationConfigResponse();
        asyncUpdateUserConversationConfigResponse.setChatId(chatConversation.getChatId());
        asyncUpdateUserConversationConfigResponse.setChatType(chatConversation.getChatType());
        asyncUpdateUserConversationConfigResponse.setMarkRead(markRead);
        asyncUpdateUserConversationConfigResponse.setUnreadCount(chatConversationService.getWechatUserConversationUnreadCount(chatConversation.getCorpId(), chatConversation.getUserId(), chatConversation.getChatId()));
        asyncUpdateUserConversationConfigResponse.setAllUnreadCount(chatConversationService.getWechatUserUnreadCount(chatConversation.getCorpId(), chatConversation.getUserId()));
        wsEvent.setData((JSONObject) JSONObject.toJSON(asyncUpdateUserConversationConfigResponse));
        webSocketFactory.pushMessageByDistributed(wsEvent);
    }
}
