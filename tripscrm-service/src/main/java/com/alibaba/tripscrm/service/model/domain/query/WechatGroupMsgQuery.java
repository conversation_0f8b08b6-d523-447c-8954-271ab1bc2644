package com.alibaba.tripscrm.service.model.domain.query;


import com.alibaba.tripzoo.proxy.enums.WechatMsgTemplateChatTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class WechatGroupMsgQuery implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 删除状态，0：正常，1：删除
     */
    private Byte deleted;

    /**
     * 企业Id
     */
    private String corpId;

    /**
     * 企业群发消息的id，可用于获取企业群发成员执行结果
     */
    private String msgId;

    /**
     * 群发任务的类型，single表示发送给客户，group表示发送给客户群
     */
    private WechatMsgTemplateChatTypeEnum chatType;

    /**
     * 最早创建时间
     */
    private Date startCreateTime;

    /**
     * 群发消息创建来源。0：企业 1：个人
     */
    private Byte createType;

    /**
     * 任务Id
     */
    private Long taskId;
}