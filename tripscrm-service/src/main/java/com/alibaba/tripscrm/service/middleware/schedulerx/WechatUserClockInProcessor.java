package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.dal.model.domain.query.wechat.WechatUserQuery;
import com.alibaba.tripscrm.domain.WechatUserDTO;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.context.DelayMetaqContext;
import com.alibaba.tripscrm.service.service.wechat.WechatUserService;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripzoo.proxy.enums.RobotStatusEnum;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 企微成员自动打卡
 *
 * <AUTHOR>
 * @since 2024/5/20 15:17
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatUserClockInProcessor extends JavaProcessor {
    private final WechatUserService wechatUserService;
    private final MetaqProducer metaqProducer;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        List<String> corpDepartmentIdList = Optional.ofNullable(SwitchConfig.wechatUserClockInCorpDepartmentIdList).orElse(new ArrayList<>());
        // 遍历所有部门
        for (String corpDepartmentId : corpDepartmentIdList) {
            String[] idList = corpDepartmentId.split("_");
            String corpId = idList[0];
            int departmentId = Integer.parseInt(idList[1]);
            WechatUserQuery query = new WechatUserQuery();
            query.setCorpId(corpId);
            query.setDepartmentIdList(Collections.singletonList(departmentId));
            query.setOnlineStatusList(Lists.newArrayList(RobotStatusEnum.ONLINE.getCode().byteValue(), RobotStatusEnum.OFFLINE.getCode().byteValue()));
            query.setStatus((byte) 1);
            // 获取部门下所有企微成员
            List<WechatUserDTO> wechatUserList = wechatUserService.listByCondition(query);
            for (WechatUserDTO wechatUser : wechatUserList) {
                Long triggerTime = getRandomTriggerTime();
                DelayMetaqContext delayMetaqContext = buildDelayMetaqContext(wechatUser.getUserId(), wechatUser.getCorpId(), triggerTime);
                boolean sendMqResult = metaqProducer.send(MQEnum.DELAY_MQ_SCHEDULING, "delay_wechat_user_clock_in_" + wechatUser.getUserId(), JSON.toJSONString(delayMetaqContext));
                if (!sendMqResult) {
                    PlatformLogUtil.logFail("企微成员自动打卡消息发送失败", LogListUtil.newArrayList(corpId, wechatUser.getUserId(), triggerTime, delayMetaqContext));
                    continue;
                }
                PlatformLogUtil.logFail("企微成员自动打卡消息发送成功", LogListUtil.newArrayList(corpId, wechatUser.getUserId(), triggerTime, delayMetaqContext));
            }
        }

        return new ProcessResult(true);
    }

    private static Long getRandomTriggerTime() {
        // 获取当前时间
        LocalTime now = LocalTime.now();
        LocalTime beginTime = LocalTime.of(9, 0, 0);
        LocalTime endTime = LocalTime.of(18, 0, 0);
        if (beginTime.isBefore(now)) {
            beginTime = now;
        }
        // 计算当前时间到结束时间的秒数
        long secondsUntilTarget = Duration.between(beginTime, endTime).getSeconds();
        long beginTimeStamp = LocalDateTime.now().with(beginTime).atZone(ZoneId.systemDefault()).toEpochSecond() * 1000L;

        return ThreadLocalRandom.current().nextLong(secondsUntilTarget * 1000L) + beginTimeStamp;
    }

    public static void main(String[] args) {
        for (int i = 0; i < 100; i++) {
            System.out.println(new Date(getRandomTriggerTime()));
        }
    }

    private static DelayMetaqContext buildDelayMetaqContext(String userId, String corpId, Long triggerTime) {
        DelayMetaqContext delayMetaqContext = new DelayMetaqContext();
        delayMetaqContext.setTriggerTime(triggerTime);
        delayMetaqContext.setTriggerType(TriggerTimeEnum.CUSTOM.getCode());
        delayMetaqContext.setSecondDelayLevel(false);
        delayMetaqContext.setFunctionType(DelayScheduleMethodEnum.WECHAT_USER_CLOCK_IN);
        delayMetaqContext.setDelayKey("delay_wechat_user_clock_in_" + userId);

        JSONObject param = new JSONObject();
        param.put("corpId", corpId);
        param.put("userId", userId);
        delayMetaqContext.setParam(JSON.toJSONString(param));
        return delayMetaqContext;
    }
}
