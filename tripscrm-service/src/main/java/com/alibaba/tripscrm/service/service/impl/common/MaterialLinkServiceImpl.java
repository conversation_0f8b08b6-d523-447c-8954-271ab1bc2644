package com.alibaba.tripscrm.service.service.impl.common;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripbaymax.client.object.result.ResultDO;
import com.alibaba.tripbaymaxbusi.client.model.request.PromoteLinkRequest;
import com.alibaba.tripbaymaxbusi.client.model.response.PromoteLinkResponse;
import com.alibaba.tripbaymaxbusi.client.service.PromoteSupplyService;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.MaterialConstant;
import com.alibaba.tripscrm.service.enums.material.LinkTypeEnum;
import com.alibaba.tripscrm.service.manager.task.MaterialTrackIdRelationManager;
import com.alibaba.tripscrm.service.model.domain.MaterialLinkConvertRequest;
import com.alibaba.tripscrm.service.model.domain.response.BaseResult;
import com.alibaba.tripscrm.service.model.dto.material.MaterialTrackRelationDTO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.common.MaterialLinkService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.system.FunctionUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig.*;

/**
 * 该类中只负责SCRM素材中链接的默认处理
 * ，通用链接处理能力写在父类中
 *
 * <AUTHOR>
 * @Date 2024/7/5 17:27
 * @see CommonLinkServiceImpl
 * ，后续如果有素材之外的其他场景有特殊的埋点和参数需要实现可切换成策略模式
 **/
@Component
public class MaterialLinkServiceImpl extends CommonLinkServiceImpl implements MaterialLinkService {

    @Switch(description = "fptid", name = "fptid")
    public String ftid = "10349";
    private static final Map<LinkTypeEnum, FunctionUtils.Function2<String, Map<String, String>, String>> TARGET_LINK_FUNCTION = new HashMap<>();

    @Resource
    private MaterialTrackIdRelationManager materialTrackIdRelationManager;
    @Resource
    private PromoteSupplyService promoteSupplyService;

    @PostConstruct
    void init() {
        TARGET_LINK_FUNCTION.put(LinkTypeEnum.H5_LINK, this::dealHttpOriginalUrl);
        TARGET_LINK_FUNCTION.put(LinkTypeEnum.MINI_PROGRAM_LIKE, this::dealMiniProgramUrl);
        TARGET_LINK_FUNCTION.put(LinkTypeEnum.ALIPAY_LINK, this::dealAlipayHttpOriginalUrl);
    }

    @AteyeInvoker(description = "链接处理工具", paraDesc = "原始链接&目标链接类型(h5/miniProgram)&是否短链&短链标题&微信内使用")
    public String convertLink(String original, String targetType, Boolean convertShortLink, String title, Boolean useInWechat) {
        MaterialLinkConvertRequest request = new MaterialLinkConvertRequest();
        request.setOriginal(original);
        request.setTargetLinkType(LinkTypeEnum.codeOf(targetType));
        request.setConvertShortLink(convertShortLink);
        request.setTitle(title);
        request.setUseInWechat(useInWechat);
        request.setCustomizeParamMap(Maps.newHashMap());
        return convertLink(request);
    }

    @Override
    public String convertLink(MaterialLinkConvertRequest request) {
        // 参数校验
        if (Objects.isNull(request)
                || StringUtils.isBlank(request.getOriginal())
                || Objects.isNull(request.getTargetLinkType())
        ) {
            PlatformLogUtil.logFail("转换链接参数缺失", LogListUtil.newArrayList(request));
            throw new TripscrmException(TripSCRMErrorCode.BUILD_SUPPLY_LINK_FAIL);
        }

        String link = request.getOriginal();
        Map<String, String> paramMap = Optional.ofNullable(request.getCustomizeParamMap()).orElse(new HashMap<>());
        // 四海通转化埋点
        link = getSiHaiTongLink(request, paramMap, link);
        // 埋点
        if (StringUtils.isNotBlank(request.getScrmTrackId())) {
            paramMap.put("scrmTrackId", request.getScrmTrackId());
        }
        //拼接素材id
        if (NumberUtils.validLong(request.getMaterialId())) {
            paramMap.put("materialId", String.valueOf(request.getMaterialId()));
        }

        String result = TARGET_LINK_FUNCTION.get(request.getTargetLinkType()).apply(link, paramMap);
        // 转短链
        //判断是否为支付宝端的链接
        if(Objects.equals(LinkTypeEnum.ALIPAY_LINK, request.getTargetLinkType())){
            return buildAlipayShortLink(result,paramMap);
        }
        if (request.isConvertShortLink()) {
            result = buildShortLink(result, request.getTitle(), request.isUseInWechat());
        }
        return result;
    }

    private String getSiHaiTongLink(MaterialLinkConvertRequest request, Map<String, String> paramMap, String link) {
        if (!SHORT_LINK_SIHAITONG_SPACE_V2.contains(SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId())
                || !StringUtils.isNotBlank(request.getScrmTrackId())) {
            return link;
        }

        if (Objects.equals(request.getTargetLinkType(), LinkTypeEnum.MINI_PROGRAM_LIKE)) {
            paramMap.put("outUserId", request.getScrmTrackId());
            // 四海通取链，塞入跟踪trackId
            String originalAddTrackId = TARGET_LINK_FUNCTION.get(request.getTargetLinkType()).apply(request.getOriginal(), paramMap);
            return this.promoteLinkSiHaiTong(originalAddTrackId, request.getTargetLinkType(), SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(), SHORT_LINK_SIHAITONG_V2);
        }
        if (Objects.equals(request.getTargetLinkType(), LinkTypeEnum.ALIPAY_LINK)) {
            paramMap.put("outUserId", request.getScrmTrackId());
            // 四海通取链，塞入跟踪trackId
            String originalAddTrackId = TARGET_LINK_FUNCTION.get(request.getTargetLinkType()).apply(request.getOriginal(), paramMap);
            LinkTypeEnum linkType = isH5Url(originalAddTrackId) ? LinkTypeEnum.H5_LINK : LinkTypeEnum.MINI_PROGRAM_LIKE;
            return this.promoteLinkSiHaiTong(originalAddTrackId, linkType, SpaceInfoThreadLocalUtils.getSpaceInfo().getSpaceId(), ALIPAY_SHORT_LINK_SIHAITONG_V2);
        }
        return link;
    }

    @Override
    public String getBuriedPointId(MaterialTrackRelationDTO buriedPointInfo) {
        if (buriedPointInfo == null) {
            PlatformLogUtil.logFail("获取埋点id失败，参数为空");
            return null;
        }
        BaseResult<String> result = materialTrackIdRelationManager.insertMaterialRelationAndGetTrackId(buriedPointInfo);
        if (!result.isSuccess() || StringUtils.isBlank(result.getData())) {
            PlatformLogUtil.logFail("获取埋点id失败，埋点信息落库失败", LogListUtil.newArrayList(buriedPointInfo, result));
            return null;
        }
        return result.getData();
    }

    /**
     * 处理小程序链接
     *
     * @param originUrl    小程序原始链接
     * @param customizeMap 自定义参数
     * @return 处理链接
     */
    private String dealMiniProgramUrl(String originUrl, Map<String, String> customizeMap) {
        HashMap<String, String> innerParamMap = new HashMap<>(5);
        innerParamMap.put("fpt", String.format("ftid(%s)", ftid));
        innerParamMap.putAll(customizeMap);

        HashMap<String, String> outerParamMap = new HashMap<>(5);
        outerParamMap.putAll(customizeMap);
        outerParamMap.put("fpt", ftid);

        try {
            String finalUrl = isH5Url(originUrl) ?
                    concatMiniProgramParam(codePath(originUrl, innerParamMap), outerParamMap)
                    : concatMiniProgramParam(originUrl, outerParamMap);
            PlatformLogUtil.logInfo("素材链接处理，处理小程序链接", LogListUtil.newArrayList(originUrl, customizeMap, finalUrl));
            return finalUrl;
        } catch (Exception e) {
            PlatformLogUtil.logException("素材链接处理，素材小程序链接处理失败", e.getMessage(), e, LogListUtil.newArrayList(originUrl, customizeMap));
            return "";
        }
    }

    /**
     * 处理http原始链接
     *
     * @param originUrl         原始链接
     * @param customizeParamMap 自定义参数
     * @return 处理链接
     */
    private String dealHttpOriginalUrl(String originUrl, Map<String, String> customizeParamMap) {
        HashMap<String, String> innerParamMap = new HashMap<>(1);
        innerParamMap.put("fpt", String.format("ftid(%s)", ftid));
        String tempUrl = concatHttpUrlParam(originUrl, innerParamMap);
        try {
            HashMap<String, String> outerParamMap = new HashMap<>(2);
            outerParamMap.put("url", URLEncoder.encode(tempUrl, StandardCharsets.UTF_8.name()));
            outerParamMap.putAll(customizeParamMap);
            String routeUrl = concatHttpUrlParam(MaterialConstant.H5_ROUTE_PRE, outerParamMap);
            PlatformLogUtil.logInfo("素材链接处理，素材http链接处理成功", LogListUtil.newArrayList(originUrl, customizeParamMap, routeUrl));
            return routeUrl;
        } catch (Exception e) {
            PlatformLogUtil.logException("素材链接处理，素材http链接处理失败", e.getMessage(), e, LogListUtil.newArrayList(originUrl, customizeParamMap));
            return "";
        }
    }

    /**
     * 处理http原始链接
     *
     * @param originUrl         原始链接
     * @param customizeParamMap 自定义参数
     * @return 处理链接
     */
    private String dealAlipayHttpOriginalUrl(String originUrl, Map<String, String> customizeParamMap) {
        try {
            //不是http链接，则直接返回
            if(!isH5Url(originUrl)){
                String tempUrl = concatMiniProgramParam(originUrl, customizeParamMap);
                PlatformLogUtil.logInfo("素材链接处理，支付宝素材小程序原生页链接处理成功", LogListUtil.newArrayList(originUrl, customizeParamMap, tempUrl));
                return tempUrl;
            }
            HashMap<String, String> innerParamMap = new HashMap<>(1);
            innerParamMap.put("fpt", String.format("ftid(%s)", ALIPAY_FPTID));
            innerParamMap.put("ttid", ALIPAY_TTID);
            innerParamMap.putAll(customizeParamMap);
            String tempUrl = concatHttpUrlParam(originUrl, innerParamMap);
            PlatformLogUtil.logInfo("素材链接处理，支付宝素材http链接处理成功", LogListUtil.newArrayList(originUrl, customizeParamMap, tempUrl));
            return tempUrl;
        } catch (Exception e) {
            PlatformLogUtil.logException("素材链接处理，素材http链接处理失败", e.getMessage(), e, LogListUtil.newArrayList(originUrl, customizeParamMap));
            return "";
        }


    }

    /**
     * 链接接入四海通
     *
     * @param original       {@link String}
     * @param targetLinkType {@link LinkTypeEnum}
     * @return {@link String}
     */
    private String promoteLinkSiHaiTong(String original, LinkTypeEnum targetLinkType, Long spaceId,Map<Long, Map<String, String>> shortLinkSihaitongMap) {
//        Map<Long, Map<String, String>> shortLinkSihaitongMap = SwitchConfig.SHORT_LINK_SIHAITONG_V2;
        Map<String, String> shortLinkSihaitong = shortLinkSihaitongMap.get(spaceId);
        PromoteLinkRequest promoteLinkRequest = new PromoteLinkRequest();
        PromoteLinkRequest.UrlInfo urlInfo = new PromoteLinkRequest.UrlInfo();
        urlInfo.setUrl(original);
        urlInfo.setUrlType(targetLinkType.getLinkType());
        urlInfo.setUniqueKey(UUID.randomUUID().toString());
        promoteLinkRequest.setUrlList(Lists.newArrayList(urlInfo));
        promoteLinkRequest.setActivityId(MapUtils.getString(shortLinkSihaitong, "activityId"));
        promoteLinkRequest.setFpid(MapUtils.getLong(shortLinkSihaitong, "fpid"));
        promoteLinkRequest.setMediaId(MapUtils.getLong(shortLinkSihaitong, "mediaId"));
        promoteLinkRequest.setPositionId(MapUtils.getLong(shortLinkSihaitong, "positionId"));
        try {
            ResultDO<List<PromoteLinkResponse>> promoteLink = promoteSupplyService.getPromoteLink(promoteLinkRequest);
            PlatformLogUtil.logInfo("素材链接处理，链接接入四海通", LogListUtil.newArrayList(promoteLinkRequest, promoteLink));
            if (promoteLink.getSuccess() && CollectionUtils.isNotEmpty(promoteLink.getModel())) {
                original = promoteLink.getModel().get(0).getPromoteLink();
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("素材链接处理，链接接入四海通失败", e.getMessage(), e, LogListUtil.newArrayList(promoteLinkRequest));
        }

        return original;
    }
}
