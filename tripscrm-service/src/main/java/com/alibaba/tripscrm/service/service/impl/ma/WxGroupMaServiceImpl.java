package com.alibaba.tripscrm.service.service.impl.ma;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.dal.mapper.tddl.WxGroupMaInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.WxEnterpriseConsumerGroupInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WxGroupMaInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.WxGroupMaInfoParam;
import com.alibaba.tripscrm.domain.enums.TagBizTypeEnum;
import com.alibaba.tripscrm.service.constant.WxAccountConstant;
import com.alibaba.tripscrm.service.constant.WxConstants;
import com.alibaba.tripscrm.service.constant.WxUrlConstant;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.wechat.WxAccessTokenType;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.PageResultVO;
import com.alibaba.tripscrm.service.model.domain.User;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupVO;
import com.alibaba.tripscrm.service.model.vo.wechat.WxGroupInfoVO;
import com.alibaba.tripscrm.service.model.domain.query.ConsumerGroupQuery;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationCoverRequest;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.WxGroupQuery;
import com.alibaba.tripscrm.service.model.dto.tag.ItemTagRelationDTO;
import com.alibaba.tripscrm.service.model.dto.tag.TagInfoDTO;
import com.alibaba.tripscrm.service.service.ma.WxGroupMaService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupService;
import com.alibaba.tripscrm.service.service.wechat.WxGroupService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.trip.wireless.mc.client.hsf.EnterpriseWeChatService;
import com.taobao.trip.wireless.mc.client.hsf.domain.weChat.WxResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig.RAND_GROUP_MA;

/**
 * <AUTHOR>
 * @date 2023/4/10
 */
@Slf4j
@HSFProvider(serviceInterface = WxGroupMaService.class)
public class WxGroupMaServiceImpl implements WxGroupMaService {
    @Autowired
    private EnterpriseWeChatService enterpriseWeChatService;
    @Autowired
    private WxGroupMaInfoMapper wxGroupMaInfoMapper;
    @Autowired
    private TagRelationService tagRelationService;
    @Autowired
    private WxGroupService wxGroupService;
    @Autowired
    private WechatGroupService wechatGroupService;

    @Override
    public PageResultVO<Long> saveGroupMa(WxGroupInfoVO wxGroupInfoVO, User user) {
        try {
            if (wxGroupInfoVO.getMaxMemberNum() > 195) {
                return PageResultVO.failResult("群人数最高195人");
            }
            String[] chatIds = wxGroupInfoVO.getChatIdList().split(",");
            if (chatIds.length > 5) {
                PlatformLogUtil.logFail("chatIds must less than 5", LogListUtil.newArrayList(wxGroupInfoVO));
                return PageResultVO.failResult("chatIds必须小于等于5个");
            }
            // 关闭微信原生自动建群开关
            if (!SwitchConfig.AUTO_CREATE_ROOM) {
                wxGroupInfoVO.setAutoCreateRoom((byte) 0);
            }
            // state字段由服务端生成, 活码名前三个字符+uuid
            Random random = new Random();
            String state = System.currentTimeMillis() + "_" + random.nextInt(100);
            wxGroupInfoVO.setState(state);
            List<String> chatIdList = new ArrayList<>(Arrays.asList(chatIds));
            JSONObject data = JSONObject.parseObject(JSON.toJSONString(wxGroupInfoVO));
            data.put("chat_id_list", chatIdList);
            WxResponse wxResponseJoin = enterpriseWeChatService.postExternalContact(WxUrlConstant.GROUP_ADD_JOIN_WAY, WxAccountConstant.groupId_jimei, WxAccessTokenType.external_contacts.name(), JSON.toJSONString(data));
            if (!wxResponseJoin.getSuccess()) {
                PlatformLogUtil.logFail("add_join_way fail", LogListUtil.newArrayList(wxGroupInfoVO, wxResponseJoin));
                return PageResultVO.failResult("配置群活码失败");
            }
            WxResponse wxResponseGet = enterpriseWeChatService.postExternalContact(WxUrlConstant.GROUP_GET_JOIN_WAY, WxAccountConstant.groupId_jimei, WxAccessTokenType.external_contacts.name(), wxResponseJoin.getWxRepSourceMsg());
            if (!wxResponseGet.getSuccess()) {
                PlatformLogUtil.logFail("get_join_way fail", LogListUtil.newArrayList(wxGroupInfoVO, wxResponseJoin));
                return PageResultVO.failResult("获取群活码详情失败");
            }
            JSONObject jsonWayJson = JSONObject.parseObject(wxResponseGet.getWxRepSourceMsg()).getJSONObject("join_way");
            WxGroupMaInfoDO wxGroupMaInfoDO = new WxGroupMaInfoDO();
            wxGroupMaInfoDO.setConfigId(jsonWayJson.getString("config_id"));
            wxGroupMaInfoDO.setScene(jsonWayJson.getByte("scene"));
            wxGroupMaInfoDO.setRemark(jsonWayJson.getString("remark"));
            wxGroupMaInfoDO.setAutoCreateRoom(jsonWayJson.getByte("auto_create_room"));
            wxGroupMaInfoDO.setRoomBaseName(jsonWayJson.getString("room_base_name"));
            wxGroupMaInfoDO.setRoomBaseId(jsonWayJson.getInteger("room_base_id"));
            if (StringUtils.isNotBlank(wxGroupInfoVO.getChatIdList())) {
                wxGroupMaInfoDO.setChatIdList(wxGroupInfoVO.getChatIdList().replace("\"", "").replace("[", "").replace("]", ""));
            }
            wxGroupMaInfoDO.setQrCode(jsonWayJson.getString("qr_code"));
            wxGroupMaInfoDO.setState(jsonWayJson.getString("state"));
            wxGroupMaInfoDO.setDeleted((byte) WxConstants.not_deleted);
            wxGroupMaInfoDO.setManagers(wxGroupInfoVO.getManagers());
            wxGroupMaInfoDO.setMaxMemberNum(wxGroupInfoVO.getMaxMemberNum());
            wxGroupMaInfoDO.setSpaceId(wxGroupInfoVO.getSpaceId());
            wxGroupMaInfoMapper.insert(wxGroupMaInfoDO);
            if (wxGroupMaInfoDO.getId() == null) {
                PlatformLogUtil.logFail("save group ma fail", LogListUtil.newArrayList(wxGroupInfoVO, wxGroupMaInfoDO));
                return PageResultVO.failResult("保存群码失败");
            }
            //标签关联关系
            if (StringUtils.isNotBlank(wxGroupInfoVO.getTagIds())) {
                ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                itemTagRelationDTO.setItemType(BizTypeEnum.GROUP_MA_OLD.getCode());
                itemTagRelationDTO.setItemId(String.valueOf(wxGroupMaInfoDO.getId()));
                itemTagRelationDTO.setCreatorId(user.getUserId());
                itemTagRelationDTO.setCreatorName(user.getUserName());
                for (String tagId : wxGroupInfoVO.getTagIds().split(",")) {
                    if (StringUtils.isNumeric(tagId)) {
                        itemTagRelationDTO.setTagId(Long.parseLong(tagId));
                    } else {
                        String[] split = tagId.split("_");
                        itemTagRelationDTO.setTagId(Long.parseLong(split[0]));
                        itemTagRelationDTO.setSubCode(split[1]);
                    }
                    tagRelationService.upsertSelective(itemTagRelationDTO);
                }
            }
            PlatformLogUtil.logFail("success", LogListUtil.newArrayList(wxGroupInfoVO, wxGroupMaInfoDO));
            return PageResultVO.successResult(1L);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(wxGroupInfoVO));
            return PageResultVO.failResult("系统异常");
        }
    }

    @AteyeInvoker(description = "更新群码业务空间", paraDesc = "id&spaceId")
    public int updateSpace(Long id, Long spaceId) {
        WxGroupMaInfoDO record = new WxGroupMaInfoDO();
        record.setId(id);
        record.setSpaceId(spaceId);
        return wxGroupMaInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public PageResultVO<Long> updateGroupMa(WxGroupInfoVO wxGroupInfoVO, User user) {
        try {
            WxGroupMaInfoDO wxGroupMaInfoDO = wxGroupMaInfoMapper.selectByPrimaryKey(wxGroupInfoVO.getId());
            if (!Objects.equals(wxGroupMaInfoDO.getSpaceId(), wxGroupInfoVO.getSpaceId())) {
                return PageResultVO.failResult("群活码不存在");
            }

            String[] chatIds = wxGroupInfoVO.getChatIdList().split(",");
            if (chatIds.length > 5) {
                PlatformLogUtil.logFail("chatIds must less than 5", LogListUtil.newArrayList(wxGroupInfoVO));
                return PageResultVO.failResult("chatIds必须小于等于5个");
            }
            List<String> chatIdList = new ArrayList<>(Arrays.asList(chatIds));
            JSONObject data = JSONObject.parseObject(JSON.toJSONString(wxGroupInfoVO));
            data.put("chat_id_list", chatIdList);
            WxResponse wxResponse = enterpriseWeChatService.postExternalContact(WxUrlConstant.GROUP_UPDATE_JOIN_WAY, WxAccountConstant.groupId_jimei, WxAccessTokenType.external_contacts.name(), JSON.toJSONString(data));
            if (!wxResponse.getSuccess()) {
                PlatformLogUtil.logFail("update_join_way fail", LogListUtil.newArrayList(wxGroupInfoVO, wxResponse));
                return PageResultVO.failResult("更新群活码失败");
            }
            wxGroupMaInfoDO = new WxGroupMaInfoDO();
            BeanUtils.copyProperties(wxGroupInfoVO, wxGroupMaInfoDO);
            wxGroupMaInfoDO.setDeleted((byte) WxConstants.not_deleted);
            PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(wxGroupInfoVO, wxGroupMaInfoDO));
            if (StringUtils.isNotBlank(wxGroupMaInfoDO.getChatIdList())) {
                wxGroupMaInfoDO.setChatIdList(wxGroupMaInfoDO.getChatIdList().replace("\"", "").replace("[", "").replace("]", ""));
            }
            int ret = wxGroupMaInfoMapper.updateByPrimaryKey(wxGroupMaInfoDO);
            if (ret < 1) {
                PlatformLogUtil.logFail("db update fail", LogListUtil.newArrayList(wxGroupInfoVO, wxGroupMaInfoDO));
                return PageResultVO.failResult("DB更新群活码失败");
            }
            //更新标签内容
            if (StringUtils.isNotBlank(wxGroupMaInfoDO.getTagIds())) {
                List<String> toCoverTagIds = Arrays.stream(wxGroupMaInfoDO.getTagIds().split(",")).collect(Collectors.toList());
                ItemTagRelationCoverRequest itemTagRelationCoverRequest = new ItemTagRelationCoverRequest();
                itemTagRelationCoverRequest.setItemId(String.valueOf(wxGroupMaInfoDO.getId()));
                itemTagRelationCoverRequest.setItemType(BizTypeEnum.GROUP_MA_OLD.getCode());
                itemTagRelationCoverRequest.setTagIdList(toCoverTagIds);
                itemTagRelationCoverRequest.setCreatorId(user.getUserId());
                itemTagRelationCoverRequest.setCreatorName(user.getUserName());
                tagRelationService.cover(itemTagRelationCoverRequest);
            } else {
                // 删除群码标签
                ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                itemTagRelationDTO.setDeleted((byte)1);
                ItemTagRelationQuery condition = new ItemTagRelationQuery();
                condition.setItemType(BizTypeEnum.GROUP_MA_OLD.getCode());
                condition.setItemId(String.valueOf(wxGroupMaInfoDO.getId()));
                tagRelationService.updateSelective(itemTagRelationDTO, condition);
            }
            return PageResultVO.successResult((long) ret);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(wxGroupInfoVO));
            return PageResultVO.failResult("系统异常");
        }
    }

    @Override
    public PageResultVO<Long> delGroupMa(WxGroupInfoVO wxGroupInfoVO, User user) {
        try {
            WxGroupMaInfoDO wxGroupMaInfoDO = wxGroupMaInfoMapper.selectByPrimaryKey(wxGroupInfoVO.getId());
            if (!Objects.equals(wxGroupMaInfoDO.getSpaceId(), wxGroupInfoVO.getSpaceId())) {
                return PageResultVO.failResult("群活码不存在");
            }

            WxResponse wxResponse = enterpriseWeChatService.postExternalContact(WxUrlConstant.GROUP_DEL_JOIN_WAY, WxAccountConstant.groupId_jimei, WxAccessTokenType.external_contacts.name(), JSON.toJSONString(wxGroupInfoVO));
            if (!wxResponse.getSuccess()) {
                PlatformLogUtil.logFail("del_join_way fail", LogListUtil.newArrayList(wxGroupInfoVO, wxResponse));
                return PageResultVO.failResult("删除群活码失败");
            }

            wxGroupMaInfoDO.setDeleted((byte) WxConstants.deleted);
            wxGroupMaInfoDO.setId(wxGroupInfoVO.getId());
            PlatformLogUtil.logFail("错误日志", LogListUtil.newArrayList(wxGroupInfoVO, wxGroupMaInfoDO));
            int ret = wxGroupMaInfoMapper.updateByPrimaryKey(wxGroupMaInfoDO);
            if (ret < 1) {
                PlatformLogUtil.logFail("db del fail", LogListUtil.newArrayList(wxGroupInfoVO, wxGroupMaInfoDO));
                return PageResultVO.failResult("db删除活码失败");
            }
            //标签关联关系
            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            itemTagRelationDTO.setDeleted((byte)1);
            ItemTagRelationQuery condition = new ItemTagRelationQuery();
            condition.setItemType(BizTypeEnum.GROUP_MA_OLD.getCode());
            condition.setItemId(String.valueOf(wxGroupMaInfoDO.getId()));
            tagRelationService.updateSelective(itemTagRelationDTO, condition);
            return PageResultVO.successResult((long) ret);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(wxGroupInfoVO));
            return PageResultVO.failResult("系统异常");
        }
    }

    @Override
    public PageResultVO<WxGroupInfoVO> listGroupMa(WxGroupQuery query) {
        WxGroupMaInfoParam wxGroupMaInfoParam = new WxGroupMaInfoParam();
        // 查询条件
        WxGroupMaInfoParam.Criteria criteria = wxGroupMaInfoParam.createCriteria().andDeletedEqualTo((byte) WxConstants.not_deleted);
        wxGroupMaInfoParam.appendOrderByClause(WxGroupMaInfoParam.OrderCondition.ID, WxGroupMaInfoParam.SortType.DESC);
        if (StringUtils.isNotBlank(query.getRemark())) {
            criteria.andRemarkLike("%" + query.getRemark() + "%");
        }
        if (StringUtils.isNotBlank(query.getState())) {
            criteria.andStateLike("%" + query.getState() + "%");
        }
        criteria.andSpaceIdEqualTo(query.getSpaceId());
        //分页信息
        wxGroupMaInfoParam.setPagination(query.getPageNum(), query.getPageSize());
        long count = wxGroupMaInfoMapper.countByParam(wxGroupMaInfoParam);
        List<WxGroupMaInfoDO> wxGroupMaInfoDOS = wxGroupMaInfoMapper.selectByParam(wxGroupMaInfoParam);
        List<WxGroupInfoVO> wxGroupInfoVOS = new ArrayList<>();
        for (WxGroupMaInfoDO wxGroupMaInfoDO : wxGroupMaInfoDOS) {
            WxGroupInfoVO wxGroupInfoVO = new WxGroupInfoVO();
            BeanUtils.copyProperties(wxGroupMaInfoDO, wxGroupInfoVO);
            // 从关联表标签ID和标签名
            ItemTagRelationQuery itemTagRelationQuery = new ItemTagRelationQuery();
            itemTagRelationQuery.setItemId(String.valueOf(wxGroupMaInfoDO.getId()));
            itemTagRelationQuery.setItemType(BizTypeEnum.GROUP_MA_OLD.getCode());
            List<TagInfoDTO> tagInfoList = tagRelationService.selectTagInfoByCondition(itemTagRelationQuery);
            tagInfoList = tagInfoList.stream()
                    .filter(tagInfo -> Objects.equals(tagInfo.getSpaceId(), query.getSpaceId()) || Objects.equals(TagBizTypeEnum.SYSTEM, tagInfo.getTagBizTypeEnum()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tagInfoList)) {
                wxGroupInfoVO.setTagIds(tagInfoList.stream().map(TagInfoDTO::getTag).collect(Collectors.joining(",")));
                wxGroupInfoVO.setTagNames(tagInfoList.stream().map(p -> String.valueOf(p.getName())).collect(Collectors.joining(",")));
            }
            wxGroupInfoVO.setCreateTime(DateUtils.getDetailDateString(wxGroupMaInfoDO.getGmtCreate()));
            wxGroupInfoVO.setChatIdList(StringUtils.isNotBlank(wxGroupInfoVO.getChatIdList()) ? wxGroupInfoVO.getChatIdList() : "");
            if (StringUtils.isNotEmpty(query.getTagId())
                    && (StringUtils.isBlank(wxGroupInfoVO.getTagIds()) || !wxGroupInfoVO.getTagIds().contains(query.getTagId()))) {
                continue;
            }
            wxGroupInfoVOS.add(wxGroupInfoVO);
        }
        List<String> chatIdList = wxGroupInfoVOS
                .stream()
                .map(WxGroupInfoVO::getChatIdList)
                .map(x -> Arrays.stream(x.split(",")))
                .flatMap((Function<Stream<String>, Stream<String>>) stringStream -> stringStream)
                .distinct()
                .collect(Collectors.toList());

        // 获取群码
        List<WechatGroupVO> wechatGroupList = wechatGroupService.listByChatIdList(chatIdList, false).stream().filter(wechatGroupVO -> StringUtils.isNotBlank(wechatGroupVO.getQrCode())).collect(Collectors.toList());
        MultiValueMap<String, WxGroupInfoVO> groupMap = new LinkedMultiValueMap<>();
        wxGroupInfoVOS.forEach(t -> Arrays.stream(t.getChatIdList().split(",")).forEach(chatId -> groupMap.add(chatId, t)));
        wechatGroupList.forEach(wechatGroup -> groupMap.getOrDefault(wechatGroup.getChatId(), new ArrayList<>()).forEach(x -> x.getRealQrCodeList().add(wechatGroup.getQrCode())));

        return PageResultVO.successResult(wxGroupInfoVOS, (int) count, query.getPageNum(), query.getPageSize());
    }

    @Override
    public WxGroupInfoVO queryWxGroupMaInfoById(Long id) {
        return queryWxGroupMaInfoByIdAndSpaceId(id, null);
    }

    @Override
    public WxGroupInfoVO queryWxGroupMaInfoByIdAndSpaceId(Long id, Long spaceId) {
        WxGroupMaInfoDO wxGroupMaInfoDO = wxGroupMaInfoMapper.selectByPrimaryKey(id);
        if (wxGroupMaInfoDO == null) {
            return null;
        }

        if (Objects.nonNull(spaceId) && !Objects.equals(wxGroupMaInfoDO.getSpaceId(), spaceId)) {
            return null;
        }

        WxGroupInfoVO wxGroupInfoVO = new WxGroupInfoVO();
        BeanUtils.copyProperties(wxGroupMaInfoDO, wxGroupInfoVO);
        return wxGroupInfoVO;
    }

    @Override
    public PageResultVO<Long> delGroupMaDB(WxGroupInfoVO wxGroupInfoVO) {
        try {
            WxGroupMaInfoDO wxGroupMaInfoDO = wxGroupMaInfoMapper.selectByPrimaryKey(wxGroupInfoVO.getId());
            if (Objects.isNull(wxGroupMaInfoDO) || !Objects.equals(wxGroupMaInfoDO.getSpaceId(), wxGroupInfoVO.getSpaceId())) {
                return PageResultVO.failResult("群活码不存在");
            }

            WxResponse wxResponse = enterpriseWeChatService.postExternalContact(WxUrlConstant.GROUP_DEL_JOIN_WAY, WxAccountConstant.groupId_jimei, WxAccessTokenType.external_contacts.name(), JSON.toJSONString(wxGroupInfoVO));
            int ret = wxGroupMaInfoMapper.deleteByPrimaryKey(wxGroupInfoVO.getId());
            if (!wxResponse.getSuccess() || ret < 1) {
                PlatformLogUtil.logFail("del_join_way fail", LogListUtil.newArrayList(wxGroupInfoVO, wxResponse, ret));
                return PageResultVO.failResult("删除群活码失败" + wxResponse.getSuccess() + ";ret:" + ret);
            }
            return PageResultVO.successResult(1L);
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(wxGroupInfoVO));
            return PageResultVO.failResult("系统异常");
        }
    }

    @Override
    public WxGroupInfoVO findFirstNotFullGroupMa(String tagId) {
        return findFirstNotFullGroupMaBySpaceId(tagId, null);
    }

    @Override
    public WxGroupInfoVO findFirstNotFullGroupMaBySpaceId(String tagId, Long spaceId) {
        // 查找交叉关系
        ItemTagRelationQuery tagRelationQuery = new ItemTagRelationQuery();
        if (StringUtils.isNumeric(tagId)) {
            tagRelationQuery.setTagId(Long.parseLong(tagId));
        } else {
            String[] split = tagId.split("_");
            tagRelationQuery.setTagId(Long.parseLong(split[0]));
            tagRelationQuery.setSubCodeList(Lists.newArrayList(split[1]));
        }
        tagRelationQuery.setItemType(BizTypeEnum.GROUP_MA_OLD.getCode());
        PageInfo<ItemTagRelationDTO> tagRelationPageInfo = tagRelationService.pageQuery(tagRelationQuery);
        if (tagRelationPageInfo == null || CollectionUtils.isEmpty(tagRelationPageInfo.getList())) {
            PlatformLogUtil.logFail("pageQuery fail", LogListUtil.newArrayList(tagId, tagRelationQuery, tagRelationPageInfo));
            return null;
        }
        // 找到所有未满的群码
        List<WxGroupInfoVO> notFullMas = new ArrayList<>();
        for (ItemTagRelationDTO tagRelationDTO : tagRelationPageInfo.getList()) {
            WxGroupInfoVO wxGroupInfoV = queryWxGroupMaInfoByIdAndSpaceId(Long.parseLong(tagRelationDTO.getItemId()), spaceId);
            if (wxGroupInfoV == null) {
                PlatformLogUtil.logFail("queryWxGroupMaInfoById fail", LogListUtil.newArrayList(tagId, tagRelationDTO));
                return null;
            }
            PlatformLogUtil.logFail("queryWxGroupMaInfoById success", LogListUtil.newArrayList(tagId, tagRelationDTO, wxGroupInfoV));
            // 校验群码是否已满
            String chatIdList = wxGroupInfoV.getChatIdList();
            for (String chatId : chatIdList.split(",")) {
                ConsumerGroupQuery query = new ConsumerGroupQuery();
                query.setChatId(chatId);
                PageResultVO<WxEnterpriseConsumerGroupInfoDO> pageResultVO = wxGroupService.listWxEnterpriseGroup(query);
                if (!pageResultVO.isSuccess() || CollectionUtils.isEmpty(pageResultVO.getData())) {
                    PlatformLogUtil.logFail("listWxEnterpriseGroup success", LogListUtil.newArrayList(tagId, query));
                    continue;
                }
                if (pageResultVO.getData().get(0).getMemberNum() < wxGroupInfoV.getMaxMemberNum()) {
                    // 码下面有群未满，可以返回
                    PlatformLogUtil.logFail("find group not full success", LogListUtil.newArrayList(tagId, chatId));
                    notFullMas.add(wxGroupInfoV);
                    break;
                }
            }
        }
        // 没有未满的群码
        if (CollectionUtils.isEmpty(notFullMas)) {
            PlatformLogUtil.logFail("all group is full", LogListUtil.newArrayList(tagId, tagRelationPageInfo));
            return null;
        }
        WxGroupInfoVO ret;
        // 随机取一个
        if (RAND_GROUP_MA) {
            Random random = new Random();
            ret = notFullMas.get(random.nextInt(notFullMas.size()));
        } else {
            ret = notFullMas.get(0);
        }
        PlatformLogUtil.logFail("find final group", LogListUtil.newArrayList(tagId, ret));
        return ret;
    }

    @Override
    public Integer updateGroupMaxNum(Long id, Long spaceId) {
        WxGroupMaInfoDO wxGroupMaInfoDO = wxGroupMaInfoMapper.selectByPrimaryKey(id);
        if (!Objects.equals(wxGroupMaInfoDO.getSpaceId(), spaceId)) {
            return 0;
        }

        wxGroupMaInfoDO.setMaxMemberNum(180);
        return wxGroupMaInfoMapper.updateByPrimaryKey(wxGroupMaInfoDO);
    }

    @AteyeInvoker(description = "修改群码群聊Id数据", paraDesc = "id&chatIdList")
    public Integer updateChatIdList(Long id, String chatIdList) {
        WxGroupMaInfoDO wxGroupMaInfoDO = new WxGroupMaInfoDO();
        wxGroupMaInfoDO.setId(id);
        wxGroupMaInfoDO.setChatIdList(chatIdList);
        return wxGroupMaInfoMapper.updateByPrimaryKey(wxGroupMaInfoDO);
    }

    @AteyeInvoker(description = "软删除群码", paraDesc = "id")
    public Integer delete(Long id) {
        WxGroupMaInfoDO wxGroupMaInfoDO = new WxGroupMaInfoDO();
        wxGroupMaInfoDO.setId(id);
        wxGroupMaInfoDO.setDeleted((byte) 1);
        return wxGroupMaInfoMapper.updateByPrimaryKey(wxGroupMaInfoDO);
    }
}
