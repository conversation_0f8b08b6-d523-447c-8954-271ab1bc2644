package com.alibaba.tripscrm.service.model.domain.request;

import com.alibaba.tripscrm.service.model.vo.tag.TagInfoVO;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-17 10:28:46
 */
@Data
public class WechatCustomerAddTagRequest implements Serializable {
    private static final long serialVersionUID = 138454969298064375L;

    /**
     * 客户 externalUserId
     */
    @NotEmpty(message = "客户Id不可为空")
    private List<String> externalUserIdList;

    /**
     * 标签列表
     */
    private List<TagInfoVO> tagList;


    /**
     * 标签id列表
     */
    private List<String> tagIdList;

    /**
     * 业务空间Id
     */
    private Long spaceId;

    /**
     * 批量操作（批量标签只做新增，非批量做覆盖）
     */
    private boolean batchOperate = true;
}
