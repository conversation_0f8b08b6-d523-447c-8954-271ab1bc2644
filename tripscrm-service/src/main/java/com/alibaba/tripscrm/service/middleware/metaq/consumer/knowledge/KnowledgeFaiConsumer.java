package com.alibaba.tripscrm.service.middleware.metaq.consumer.knowledge;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.dal.model.domain.data.KnowledgeDocumentDO;
import com.alibaba.tripscrm.service.enums.system.KnowledgeDocumentStatusEnum;
import com.alibaba.tripscrm.service.model.dto.knowledge.KnowledgeDocumentDTO;
import com.alibaba.tripscrm.service.service.knowledge.KnowledgeDocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * topic：TOPIC_FAI_DOCUMENT_PROCESS_EVENT
 * consumer：CID_TOPIC_FAI_DOCUMENT_PROCESS_EVENT
 * tag：${libraryId}，预发环境：1337，线上环境：？
 *
 * FAI平台新增、修改、删除知识成功后，会发送MQ消息
 *
 * <AUTHOR>
 * @desc 知识文档fai消费
 * @date 2025/9/18
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class KnowledgeFaiConsumer implements MessageListenerConcurrently {

    private final KnowledgeDocumentService knowledgeDocumentService;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : list) {
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("更新知识文档上/下架fai处理结果消息，处理失败", LogListUtil.newArrayList(receivedMsg));
                continue;
            }
            PlatformLogUtil.logInfo("更新知识文档上/下架fai处理结果消息，处理完成", LogListUtil.newArrayList(receivedMsg));
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /*{
        // 处理任务类型: 新增 | 修改 | 删除
        "type": "CREATE | MODIFY | DELETE "
        // 处理任务ID
        "taskId": "unique-task-id-12345",
        // 知识库ID
        "libraryId": 98765,
        // 知识文档ID
        "documentId": "doc-id-67890",
        // 业务标识ID
        "businessId": "biz-id-abcde"
    }*/
    private boolean dealWithMessage(String receivedMsg) {
        PlatformLogUtil.logInfo("接收知识文档处理结果消息", LogListUtil.newArrayList(receivedMsg));
        JSONObject jsonObject = JSONObject.parseObject(receivedMsg);
        Long libraryId = jsonObject.getLong("libraryId");
        String documentId = jsonObject.getString("documentId");
        String businessId = jsonObject.getString("businessId");
        String type = jsonObject.getString("type");
        if (Objects.isNull(libraryId) || StringUtils.isBlank(documentId) || StringUtils.isBlank(businessId) || StringUtils.isBlank(type)) {
            PlatformLogUtil.logFail("参数校验失败", LogListUtil.newArrayList(receivedMsg));
            return false;
        }
        KnowledgeDocumentDTO documentDTO = knowledgeDocumentService.selectById(Long.valueOf(businessId));
        if (Objects.isNull(documentDTO)) {
            PlatformLogUtil.logFail("知识文档不存在", LogListUtil.newArrayList(receivedMsg));
            return false;
        }
        switch (type) {
            case "CREATE":
                documentDTO.setStatus(KnowledgeDocumentStatusEnum.ONLINE.getCode());
                break;
            case "DELETE":
                documentDTO.setStatus(KnowledgeDocumentStatusEnum.EDITING.getCode());
                break;
            default:
                PlatformLogUtil.logFail("未知处理任务类型", LogListUtil.newArrayList(receivedMsg));
                return false;
        }
        return knowledgeDocumentService.update(documentDTO) > 0;
    }
}
