package com.alibaba.tripscrm.service.service.wechat;

import com.alibaba.tripscrm.service.model.vo.wechat.WechatGroupInfoMemberVO;
import com.alibaba.tripzoo.proxy.enums.GroupUserTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 群成员 Manager
 *
 * <AUTHOR>
 * @date 2023/10/08
 */
public interface GroupRelationService {
    /**
     * 批量查询群成员数量
     *
     * @param chatIdList 群列表
     * @return chatId -> memberCount
     */
    Map<String, Integer> batchGetGroupMemberCount(List<String> chatIdList);

    /**
     * 根据群聊Id查询群聊成员信息
     * @param chatId 群聊Id
     * @param corpId 组织Id
     * @return 群成员信息
     */
    List<WechatGroupInfoMemberVO> listByChatId(String chatId, String corpId);

    /**
     * 根据群聊id和用户类型查询
     * @param chatId 群聊id
     * @param userTypeEnumList 用户类型
     * @return 群成员列表
     */
    List<WechatGroupInfoMemberVO> listByChatIdAndUserType(String chatId, List<GroupUserTypeEnum> userTypeEnumList);
}