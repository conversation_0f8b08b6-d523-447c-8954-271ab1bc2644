package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.service.enums.system.OdpsProjectEnum;
import com.alibaba.tripscrm.service.enums.system.OneIdKeyTypeEnum;
import com.alibaba.tripscrm.service.enums.system.ScrmOneIdOpType;
import com.alibaba.tripscrm.service.manager.middleware.OdpsManager;
import com.alibaba.tripscrm.service.model.domain.bo.OneIdMappingBO;
import com.alibaba.tripscrm.service.model.domain.bo.ScrmOneIdBO;
import com.alibaba.tripscrm.service.service.OneIdMappingService;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.tunnel.InstanceTunnel;
import com.aliyun.odps.tunnel.io.TunnelRecordReader;
import com.taobao.ateye.annotation.AteyeInvoker;
import com.taobao.ateye.annotation.Switch;
import com.taobao.tddl.client.sequence.Sequence;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.alibaba.tripscrm.dal.constants.LindormConstant.ScrmOneIdColumnKey.*;

/**
 * scrm one id 生成器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ScrmOneIdGenerateProcessor extends JavaProcessor {

    private static final String SCRM_ID_SUMMARY_DIFF_ODPS_TABLE = "trip_scrm_id_summary_diff";
    private static final long PAGE_SIZE = 1000L;

    @Autowired
    private OdpsManager odpsManager;
    @Resource
    private Sequence scrmOneIdSequence;
    @Resource
    private OneIdMappingService oneIdMappingService;

    @Switch
    private static String SCRM_ID_SUMMARY_LIST = "scrm_one_id,user_id,union_id,utdid,device_id,imei,imsi,imeisi,oaid,official_account_open_id,mini_app_open_id,we_work_open_id,external_user_id_1";

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            String param = context.getInstanceParameters();
            ScrmOneIdOpType opTypeParam = ScrmOneIdOpType.getByType(param);
            if (null != opTypeParam) {
                return new ProcessResult(executeProcess(opTypeParam));
            }

            int successCnt = 0;
            for (ScrmOneIdOpType opType : ScrmOneIdOpType.values()) {
                if (!executeProcess(opType)) {
                    PlatformLogUtil.logFail("scrmOneId生成，执行失败", LogListUtil.newArrayList(opType.getOpType()));
                }
                successCnt += 1;
            }

            return new ProcessResult(successCnt == ScrmOneIdOpType.values().length);
        } catch (Exception e) {
            PlatformLogUtil.logException("scrmOneId生成，执行异常", e.getMessage(), e, LogListUtil.newArrayList());
            return new ProcessResult(false, "ScrmOneIdGenerateProcessor failed " + e.getMessage());
        }
    }

    private boolean executeProcess(ScrmOneIdOpType opType) throws Exception {
        // 读取odps数据
        InstanceTunnel.DownloadSession downloadSession = odpsManager.getDefaultDownloadSession(generateOpdsReadSql(opType));
        if (InstanceTunnel.DownloadStatus.NORMAL != downloadSession.getStatus()) {
            PlatformLogUtil.logFail("scrmOneId生成，读取odps数据失败", LogListUtil.newArrayList(downloadSession.getStatus().name()));
            return false;
        }

        long recordCount = downloadSession.getRecordCount();

        // 分页查询
        long pageSize = PAGE_SIZE;
        int totalPage = Long.valueOf((recordCount + pageSize - 1) / pageSize).intValue();
        for (int page = 0; page < totalPage; page++) {
            long start = page * pageSize;
            TunnelRecordReader recordReader = downloadSession.openRecordReader(start, pageSize);
            List<ScrmOneIdBO> scrmOneIdBOList = readRecords(opType, recordReader);
            if (scrmOneIdBOList.isEmpty()) {
                recordReader.close();
                continue;
            }

            // 写入lindorm
            List<OneIdMappingBO> oneIdMappingBOList = convertToOneIdMappingBOList(scrmOneIdBOList);
            if (ScrmOneIdOpType.DEL == opType) {
                oneIdMappingService.batchDelete(oneIdMappingBOList);
            } else {
                oneIdMappingService.batchUpsert(oneIdMappingBOList);
            }
            recordReader.close();
        }
        return true;
    }

    /**
     * 读取odps数据
     * 生成 scrm oneid
     */
    private List<ScrmOneIdBO> readRecords(ScrmOneIdOpType opType, TunnelRecordReader recordReader) throws IOException {
        List<ScrmOneIdBO> scrmOneIdBOList = new ArrayList<>();
        Record record;
        while ((record = recordReader.read()) != null) {
            long scrmOneId = -1L;
            if (ScrmOneIdOpType.ADD == opType) {
                // 生成oneid
                scrmOneId = scrmOneIdSequence.nextValue();
            }

            ScrmOneIdBO scrmOneIdBO = convertToScrmOneIdBO(scrmOneId, record);
            scrmOneIdBOList.add(scrmOneIdBO);
        }
        return scrmOneIdBOList;
    }

    private ScrmOneIdBO convertToScrmOneIdBO(long scrmOneId, Record record) {
        ScrmOneIdBO scrmOneIdBO = new ScrmOneIdBO();
        if (scrmOneId == -1L) {
            scrmOneId = NumberUtils.toLong(record.getString(SCRM_ONE_ID));
        }
        scrmOneIdBO.setReverseScrmOneId(NumberUtils.toReverseStr(scrmOneId));
        scrmOneIdBO.setScrmOneId(scrmOneId == -1L ? record.getString(SCRM_ONE_ID) : String.valueOf(scrmOneId));
        scrmOneIdBO.setUserId(record.getString(USER_ID));
        scrmOneIdBO.setUnionId(record.getString(UNION_ID));
        scrmOneIdBO.setUtdid(record.getString(UTDID));
        scrmOneIdBO.setDeviceId(record.getString(DEVICE_ID));
        scrmOneIdBO.setImei(record.getString(IMEI));
        scrmOneIdBO.setImsi(record.getString(IMSI));
        scrmOneIdBO.setImeisi(record.getString(IMEISI));
        scrmOneIdBO.setOaid(record.getString(OAID));
        scrmOneIdBO.setOfficialAccountOpenId(record.getString(OFFICIAL_ACCOUNT_OPEN_ID));
        scrmOneIdBO.setMiniAppOpenId(record.getString(MINI_APP_OPEN_ID));
        scrmOneIdBO.setWeWorkOpenId(record.getString(WE_WORK_OPEN_ID));
        scrmOneIdBO.setExternalUserId1(record.getString(EXTERNAL_USER_ID_1));
        return scrmOneIdBO;
    }

    private List<OneIdMappingBO> convertToOneIdMappingBOList(List<ScrmOneIdBO> scrmOneIdBOList) throws Exception {
        List<OneIdMappingBO> oneIdMappingBOList = new ArrayList<>();
        for (ScrmOneIdBO scrmOneIdBO : scrmOneIdBOList) {
            for (OneIdKeyTypeEnum keyType : OneIdKeyTypeEnum.values()) {
                Field field = scrmOneIdBO.getClass().getDeclaredField(keyType.getPropertyCode());
                field.setAccessible(true);
                if (null == field.get(scrmOneIdBO)) {
                    continue;
                }

                OneIdMappingBO oneIdMappingBO = new OneIdMappingBO();
                oneIdMappingBO.setOneId(scrmOneIdBO.getScrmOneId());
                oneIdMappingBO.setOneIdReverse(scrmOneIdBO.getReverseScrmOneId());
                oneIdMappingBO.setKeyType(keyType.getLindormColumnCode());
                oneIdMappingBO.setKeyValue((String) field.get(scrmOneIdBO));
                oneIdMappingBOList.add(oneIdMappingBO);
            }
        }
        return oneIdMappingBOList;
    }

    /**
     * 生成odps读取sql
     */
    private String generateOpdsReadSql(ScrmOneIdOpType opType) {
        String odpsGuid = OdpsProjectEnum.TRIP_PROFILE.getProjectName() + "." + SCRM_ID_SUMMARY_DIFF_ODPS_TABLE;
        String sqlFormat = "SELECT %s FROM %s WHERE ds=MAX_PT('%s') AND op_type='%s';";
        return String.format(sqlFormat, SCRM_ID_SUMMARY_LIST, odpsGuid, odpsGuid, opType.getOpType());
    }

    @AteyeInvoker(description = "updateIdMappingTable", paraDesc = "sql&opTypeStr")
    public void updateIdMappingTable(String sql, String opTypeStr) {
        ScrmOneIdOpType opType = ScrmOneIdOpType.getByType(opTypeStr);
        if (opType == ScrmOneIdOpType.DEL) {
            return;
        }

        if (Objects.isNull(sql)) {
            sql = "SELECT * FROM trip_profile_dev.trip_scrm_id_summary_diff WHERE ds=MAX_PT('trip_profile_dev.trip_scrm_id_summary_diff');";
        }

        try {
            InstanceTunnel.DownloadSession downloadSession = odpsManager.getDefaultDownloadSession(sql);
            long recordCount = downloadSession.getRecordCount();
            // 分页查询
            long pageSize = PAGE_SIZE;
            int totalPage = Long.valueOf((recordCount + pageSize - 1) / pageSize).intValue();
            for (int page = 0; page < totalPage; page++) {
                long start = page * pageSize;
                TunnelRecordReader recordReader = downloadSession.openRecordReader(start, pageSize);
                List<ScrmOneIdBO> scrmOneIdBOList = readRecords(opType, recordReader);
                if (scrmOneIdBOList.isEmpty()) {
                    recordReader.close();
                    continue;
                }

                List<OneIdMappingBO> oneIdMappingBOList = convertToOneIdMappingBOList(scrmOneIdBOList);
                oneIdMappingService.batchUpsert(oneIdMappingBOList);
                recordReader.close();
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("failed", e.getMessage(), e, LogListUtil.newArrayList());
        }
    }
}
