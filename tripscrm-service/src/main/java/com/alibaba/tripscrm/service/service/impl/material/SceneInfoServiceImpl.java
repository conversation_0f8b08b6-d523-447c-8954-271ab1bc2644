package com.alibaba.tripscrm.service.service.impl.material;

import com.alibaba.tripscrm.dal.mapper.tddl.SceneInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.SceneInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.SceneInfoParam;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.SceneInfoQuery;
import com.alibaba.tripscrm.service.model.vo.material.SceneTreeVO;
import com.alibaba.tripscrm.service.service.material.SceneInfoService;
import com.alibaba.tripscrm.service.util.material.ModelConvertUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class SceneInfoServiceImpl implements SceneInfoService {

    private final SceneInfoMapper sceneInfoMapper;

    @Override
    public Boolean add(SceneInfoDO sceneInfoDO) {
        sceneInfoDO.setGmtCreate(new Date());
        sceneInfoDO.setGmtModified(new Date());
        return sceneInfoMapper.insert(sceneInfoDO) > 0;
    }

    @Override
    public Boolean delete(Long id) {
        return sceneInfoMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public Boolean updateById(SceneInfoDO sceneInfoDO) {
        return sceneInfoMapper.updateByPrimaryKeySelective(sceneInfoDO) > 0;
    }

    @Override
    public List<SceneInfoDO> list(SceneInfoQuery query) {
        if (query == null) {
            return Lists.newArrayList();
        }
        SceneInfoParam param = new SceneInfoParam();
        SceneInfoParam.Criteria criteria = param.createCriteria();
        if (query.getId() != null) {
            criteria.andIdEqualTo(query.getId());
        }
        if (StringUtils.isNotBlank(query.getName())) {
            criteria.andNameLike(query.getName());
        }
        if (query.getIndustryType() != null) {
            criteria.andIndustryEqualTo(query.getIndustryType());
        }
        return sceneInfoMapper.selectByParam(param);
    }

    @Override
    public SceneInfoDO queryById(Long id) {
        return sceneInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SceneTreeVO> tree() {
        ArrayList<SceneTreeVO> sceneTreeList = new ArrayList<>();
        for (Map.Entry<Byte, String> entry : SwitchConfig.INDUSTRY_INFO_MAPPING.entrySet()) {
            sceneTreeList.add(getIndustryScene(entry.getKey(), entry.getValue()));
        }
        return sceneTreeList;
    }

    /**
     * 获取行业的场景
     * @param industry 行业
     * @param industryName 行业名称
     * @return 行业及场景
     */
    private SceneTreeVO getIndustryScene(Byte industry, String industryName) {
        SceneTreeVO sceneTreeVO = new SceneTreeVO();
        sceneTreeVO.setIndustry(industry);
        sceneTreeVO.setIndustryName(industryName);
        SceneInfoQuery query = new SceneInfoQuery();
        query.setIndustryType(industry);
        sceneTreeVO.setSceneList(list(query).stream().map(ModelConvertUtils::convertVO).collect(Collectors.toList()));
        return sceneTreeVO;
    }

}
