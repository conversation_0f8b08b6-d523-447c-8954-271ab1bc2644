package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 微信客户关系信息查询
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WxCustomizerRelatedRecordQuery extends BasePageRequest {
    /**
     *   主键
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *   创建时间
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     *   修改时间
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     *   目的地城市code
     *
     * @mbg.generated
     */
    private String destCityCode;

    /**
     *   目的地城市名称
     *
     * @mbg.generated
     */
    private String destCityName;

    /**
     *   出发城市code
     *
     * @mbg.generated
     */
    private String originCityCode;

    /**
     *   出发城市名称
     *
     * @mbg.generated
     */
    private String originCityName;

    /**
     *   扩展信息
     *
     * @mbg.generated
     */
    private String extInfo;

    /**
     *   用户id
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     *   机器人主键id
     *
     * @mbg.generated
     */
    private Long wechatUserId;

    /**
     *   活码唯一标识
     *
     * @mbg.generated
     */
    private String state;

    /**
     *   微信用户唯一ID
     *
     * @mbg.generated
     */
    private String unionId;

    /**
     * 首次发送消息标识  0-没发；1-已发
     *
     * @mbgated
     */
    private Short sendMsgFlag;
}
