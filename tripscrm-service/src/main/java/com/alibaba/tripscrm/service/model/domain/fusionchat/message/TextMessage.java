package com.alibaba.tripscrm.service.model.domain.fusionchat.message;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * <AUTHOR>
 * @Date 2024/6/24 11:09
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class TextMessage extends FusionChatMessage {
    /**
     * @$$代表此处@人
     */
    private String content;

    /**
     * 被at人列表
     */
    private List<AtMemberInfo> atMembers;

    @Data
    public static class AtMemberInfo{
        /**
         * 被at人的userId
         */
        private String userId;
        /**
         * 被at人名称
         */
        private String name;
        /**
         * 0代表所有人；1代表成员；2代表客户
         */
        private Integer type;
    }
}
