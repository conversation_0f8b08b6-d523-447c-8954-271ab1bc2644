package com.alibaba.tripscrm.service.model.domain.query;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorRecordQuery extends BasePageRequest {
    /**
     * Id
     */
    private Long id;

    /**
     * 目标Id
     */
    private String targetId;

    /**
     * 目标类型
     */
    private Byte targetType;

    /**
     * 删除状态，0：正常，1：删除
     */
    private Byte deleted;

    /**
     *   创建人工号
     */
    private String creatorId;

    /**
     *   最近一次更新工号
     */
    private String lastOperatorId;

    /**
     *   录入方式，0：自动，1：手动
     */
    private Byte entryMethod;

    /**
     *   额外字段
     */
    private String extraInfo;

    /**
     *   业务id
     */
    private String bizId;

    /**
     *   操作，0：确认，1：已确认
     */
    private Byte status;

    /**
     *  空间id，0:全局
     */
    private Long spaceId;

    /**
     *  行业
     */
    private Byte industry;

    /**
     *  场景
     */
    private Byte scene;

    /**
     * 创建时间（开始时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createStartTime;

    /**
     * 创建时间（结束时间）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createEndTime;
}
