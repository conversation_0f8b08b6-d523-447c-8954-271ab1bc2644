package com.alibaba.tripscrm.service.service.impl.material;

import com.alibaba.tripscrm.dal.mapper.tddl.ServiceInfoMapper;
import com.alibaba.tripscrm.dal.model.domain.data.ServiceInfoDO;
import com.alibaba.tripscrm.dal.model.domain.data.ServiceInfoParam;
import com.alibaba.tripscrm.service.model.domain.query.ServiceInfoQuery;
import com.alibaba.tripscrm.service.model.dto.material.service.ServiceInfoDTO;
import com.alibaba.tripscrm.service.service.material.ServiceInfoService;
import com.alibaba.tripscrm.service.util.material.ModelConvertUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ServiceInfoServiceImpl implements ServiceInfoService {

    private final ServiceInfoMapper serviceInfoMapper;

    @Override
    public Boolean add(ServiceInfoDTO serviceInfoDTO) {
        ServiceInfoDO serviceInfoDO = ModelConvertUtils.convertDO(serviceInfoDTO);
        serviceInfoDO.setGmtCreate(new Date());
        serviceInfoDO.setGmtModified(new Date());
        return serviceInfoMapper.insert(serviceInfoDO) > 0;
    }

    @Override
    public Boolean deleteById(Long id) {
        return serviceInfoMapper.deleteByPrimaryKey(id) > 0;
    }

    @Override
    public Boolean updateById(ServiceInfoDTO serviceInfoDTO) {
        return serviceInfoMapper.updateByPrimaryKeySelective(ModelConvertUtils.convertDO(serviceInfoDTO)) > 0;
    }

    @Override
    public List<ServiceInfoDTO> list(ServiceInfoQuery query) {
        if (query == null) {
            return Lists.newArrayList();
        }
        ServiceInfoParam param = new ServiceInfoParam();
        ServiceInfoParam.Criteria criteria = param.createCriteria();
        if (StringUtils.isNotBlank(query.getName())) {
            criteria.andNameLike(String.format("%%%s%%", query.getName()));
        }
        if (query.getType() != null) {
            criteria.andTypeEqualTo(query.getType());
        }
        if (query.getIndustry() != null) {
            criteria.andIndustryEqualTo(query.getIndustry());
        }
        return Optional.ofNullable(serviceInfoMapper.selectByParam(param))
                .orElse(new ArrayList<>()).stream().map(ModelConvertUtils::convertDTO).collect(Collectors.toList());
    }

    @Override
    public ServiceInfoDTO queryById(Long id) {
        return ModelConvertUtils.convertDTO(serviceInfoMapper.selectByPrimaryKey(id));
    }

}
