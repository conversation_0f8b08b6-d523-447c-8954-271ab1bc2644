package com.alibaba.tripscrm.service.model.domain.request.knowledge;

import com.alibaba.tripscrm.dal.model.domain.base.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/9/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CategoryReq extends BasePageRequest {

    private List<Long> ids;

    private Long spaceId;

    private String name;
}
