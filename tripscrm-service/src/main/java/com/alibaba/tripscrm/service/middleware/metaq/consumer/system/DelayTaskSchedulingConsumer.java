package com.alibaba.tripscrm.service.middleware.metaq.consumer.system;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import com.alibaba.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import com.alibaba.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import com.alibaba.rocketmq.common.message.MessageExt;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.DelayTaskContext;
import com.alibaba.tripscrm.service.service.task.ability.old.TaskFactory;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripscrm.service.util.system.MetaQDelayLevelUtil;
import com.alibaba.tripscrm.service.util.system.MetaQDeleyLevel;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 企微事件回调消息消费
 */
@Slf4j
@Service(value = "delayTaskSchedulingConsumer")
public class DelayTaskSchedulingConsumer implements MessageListenerConcurrently {

    @Resource
    private MetaqProducer metaqProducer;

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        for (MessageExt msg : msgs) {
            PlatformLogUtil.logFail("receive", LogListUtil.newArrayList(msg));
            String receivedMsg = new String(msg.getBody());
            if (!dealWithMessage(receivedMsg)) {
                PlatformLogUtil.logFail("receiveMsg", LogListUtil.newArrayList(receivedMsg));
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理消息
     */
    private boolean dealWithMessage(String message) {
        try {
            // 解析数据
            PlatformLogUtil.logInfo("receive message",  LogListUtil.newArrayList(message));
            DelayTaskContext delayTaskContext = JSONObject.parseObject(message, DelayTaskContext.class);
            // 立即执行
            if (TriggerTimeEnum.REAL_TIME.getCode().equals(Integer.parseInt(delayTaskContext.getTriggerType()))) {
                doTask(delayTaskContext);
                return true;
            }
            // 自定义执行时间
            delayTaskContext.setDelayNum(delayTaskContext.getDelayNum() + 1);
            // 方便追溯metaq消息以及接力次数
            String key = delayTaskContext.getDelayKey() + "_" + delayTaskContext.getDelayNum();
            // 超过最大时间,要接力,按照最大时间来投递
            if (delayTaskContext.getTriggerTime() - DateUtils.addSeconds(new Date(), MetaQDeleyLevel.LEVEL_18.getSeconds()).getTime() / 1000 > 0) {
                metaqProducer.send(MQEnum.DELAY_TASK_SCHEDULING, key, "", message, MetaQDeleyLevel.LEVEL_18.getLevel());
                return true;
            } else {
                // 超过1分钟
                if (delayTaskContext.getTriggerTime() - DateUtils.addSeconds(new Date(), 60).getTime()/1000 > 0) {
                    int currentSecond = (int)(System.currentTimeMillis()/1000);
                    metaqProducer.send(MQEnum.DELAY_TASK_SCHEDULING, key, "", message, MetaQDelayLevelUtil.getBestDelayLevel(delayTaskContext.getTriggerTime().intValue() - currentSecond));
                    return true;
                }
                // 需要精确到秒级延迟
                if (delayTaskContext.getSecondDelayLevel() && delayTaskContext.getTriggerTime() - DateUtils.addSeconds(new Date(), 1).getTime() / 1000 > 0) {
                    int currentSecond = (int) (System.currentTimeMillis() / 1000);
                    metaqProducer.send(MQEnum.DELAY_TASK_SCHEDULING, key, "", message, MetaQDelayLevelUtil.getBestDelayLevel(delayTaskContext.getTriggerTime().intValue() - currentSecond));
                    return true;
                }

                // 当前时间一分钟之内，执行任务
                doTask(delayTaskContext);
            }
        } catch (Exception e) {
            PlatformLogUtil.logException("执行异常", e.getMessage(), e, LogListUtil.newArrayList(message));
        }
        return true;
    }

    private void doTask(DelayTaskContext delayTaskContext) {
        // 任务是否免打扰
        TaskInfoDO taskInfoDO = delayTaskContext.getTaskBuildContext().getTaskInfoDO();
        if (StringUtils.isNotBlank(taskInfoDO.getNotDisturbTime())) {
            String[] notDisturbTimeStr = taskInfoDO.getNotDisturbTime().split(";");
            Integer currentHour = DateUtils.getHourOfDate(new Date());
            Integer currentMinute = DateUtils.getMinuteOfDate(new Date());
            Date currentHm = DateUtils.parseToHourMinterOfDay(currentHour + ":" + currentMinute);
            boolean inWorkTime = DateUtils.parseToHourMinterOfDay(notDisturbTimeStr[0]).before(currentHm) && DateUtils.parseToHourMinterOfDay(notDisturbTimeStr[1]).after(currentHm);
            if (!inWorkTime) {
                PlatformLogUtil.logFail("not in work time", LogListUtil.newArrayList(delayTaskContext));
            }
        }
        // 执行任务
        Long taskInstantId = TaskFactory
                .getTaskBeanByType(TaskType.getByCode(taskInfoDO.getType()))
                .buildTask(delayTaskContext.getTaskBuildContext());
        if (!NumberUtils.validLong(taskInstantId)) {
            PlatformLogUtil.logFail("buildTask fail", LogListUtil.newArrayList(delayTaskContext));
            return;
        }
        PlatformLogUtil.logFail("buildTask success", LogListUtil.newArrayList(delayTaskContext));
    }
}
