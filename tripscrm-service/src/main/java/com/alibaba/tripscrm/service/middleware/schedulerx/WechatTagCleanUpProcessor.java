package com.alibaba.tripscrm.service.middleware.schedulerx;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/4/10 19:09
 **/
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WechatTagCleanUpProcessor extends JavaProcessor {
    @Override
    public ProcessResult process(JobContext context) throws Exception {
        return new ProcessResult(true);
    }
}
