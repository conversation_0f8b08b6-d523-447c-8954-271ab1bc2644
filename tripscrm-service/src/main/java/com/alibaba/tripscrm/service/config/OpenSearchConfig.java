package com.alibaba.tripscrm.service.config;

import com.aliyun.opensearch.DocumentClient;
import com.aliyun.opensearch.OpenSearchClient;
import com.aliyun.opensearch.SearcherClient;
import com.aliyun.opensearch.sdk.generated.OpenSearch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023-07-02 15:37:36
 */
@Slf4j
@Configuration
public class OpenSearchConfig {
    @Value("${opensearch.access_key}")
    public String accessKey;

    @Value("${opensearch.secret}")
    public String secret;

    @Value("${opensearch.host}")
    public String host;

    @Bean
    public SearcherClient searchClient() {
        OpenSearch openSearch = new OpenSearch(accessKey, secret, host);
        OpenSearchClient serviceClient = new OpenSearchClient(openSearch);
        return new SearcherClient(serviceClient);
    }

    @Bean
    public DocumentClient documentClient() {
        OpenSearch openSearch = new OpenSearch(accessKey, secret, host);
        OpenSearchClient serviceClient = new OpenSearchClient(openSearch);
        return new DocumentClient(serviceClient);
    }
}
