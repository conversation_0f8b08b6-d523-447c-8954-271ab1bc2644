package com.alibaba.tripscrm.service.middleware.hts;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.domain.query.WechatGroupPoolQuery;
import com.alibaba.tripscrm.service.model.dto.wechat.WechatGroupPoolDTO;
import com.alibaba.tripscrm.service.service.wechat.WechatGroupPoolService;
import com.alibaba.tripscrm.service.util.SpaceInfoThreadLocalUtils;
import com.alibaba.tripscrm.service.util.dingtalk.DingTalkApi;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import com.alibaba.tripzoo.proxy.api.service.GroupService;
import com.alibaba.tripzoo.proxy.request.GroupCreateRequest;
import com.alibaba.tripzoo.proxy.result.ResultDO;
import com.taobao.tddl.client.sequence.Sequence;
import com.taobao.trip.hts.client.domain.result.HTSTaskResult;
import com.taobao.trip.hts.client.domain.task.Task;
import com.taobao.trip.hts.client.executor.TaskCallback;
import com.taobao.trip.hts.client.executor.TaskExecutor;
import com.taobao.trip.hts.client.service.TaskScheduleClient;
import com.taobao.trip.hts.schema.spring.context.annotation.Executor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/3/10 16:42
 */
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Executor("wechatGroupPoolRefreshTaskExecutor")
public class WechatGroupPoolRefreshTaskExecutor implements TaskExecutor {
    private final LdbTairManager ldbTairManager;
    private final WechatGroupPoolService wechatGroupPoolService;
    private final TaskScheduleClient taskScheduleClient;
    private final GroupService groupService;
    @Resource
    private Sequence wechatGroupPoolNameSequence;

    @Override
    public TaskCallback execute(Task task) {
        Map<String, Serializable> param = task.getParam();
        try {
            PlatformLogUtil.logInfo("企微群聊池刷新hts任务执行，开始执行", task);
            String corpId = (String) param.get("corpId");
            String userId = (String) param.get("userId");
            if (!StringUtils.hasText(corpId) || !StringUtils.hasText(userId)) {
                PlatformLogUtil.logFail("企微群聊池刷新hts任务执行，参数非法", TripSCRMErrorCode.INVALID_PARAMS.getCode(), LogListUtil.newArrayList(param));
                return new TaskCallback();
            }

            SpaceInfoThreadLocalUtils.setCorpId(corpId);
            Long minuteWindowVersion = DateUtils.getMinuteVersion(new Date());
            Long hourWindowVersion = minuteWindowVersion / 60;
            Long dayWindowVersion = DateUtils.getDayVersion(new Date());
            String minuteWindowKey = TairConstant.WECHAT_USER_CREATE_GROUP_COUNT_IN_WINDOW_PREFIX + minuteWindowVersion + "_m_" + corpId + "_" + userId;
            String hourWindowKey = TairConstant.WECHAT_USER_CREATE_GROUP_COUNT_IN_WINDOW_PREFIX + hourWindowVersion + "_h_" + corpId + "_" + userId;
            String dayWindowKey = TairConstant.WECHAT_USER_CREATE_GROUP_COUNT_IN_WINDOW_PREFIX + dayWindowVersion + "_d_" + corpId + "_" + userId;
            Integer hourCreateCount = (Integer) ldbTairManager.get(hourWindowKey);
            if (Optional.ofNullable(hourCreateCount).orElse(0) > SwitchConfig.wechatGroupCreateCountPerHour) {
                PlatformLogUtil.logFail("企微群聊池刷新hts任务执行，当前账号触发小时级创建群聊数量上限", LogListUtil.newArrayList(hourWindowKey, hourCreateCount, userId, corpId));
                return new TaskCallback();
            }
            Integer minuteCreateCount = (Integer) ldbTairManager.get(minuteWindowKey);
            if (Optional.ofNullable(minuteCreateCount).orElse(0) > SwitchConfig.wechatGroupCreateCountPerMinute) {
                PlatformLogUtil.logFail("企微群聊池刷新hts任务执行，当前账号触发分钟级创建群聊数量上限", LogListUtil.newArrayList(minuteWindowKey, minuteCreateCount, userId, corpId));
                return new TaskCallback();
            }
            Integer dayCreateCount = (Integer) ldbTairManager.get(dayWindowKey);
            if (Optional.ofNullable(dayCreateCount).orElse(0) > SwitchConfig.wechatGroupCreateCountPerDay) {
                PlatformLogUtil.logFail("企微群聊池刷新hts任务执行，当前账号触发天级创建群聊数量上限", LogListUtil.newArrayList(dayWindowKey, dayCreateCount, userId, corpId));
                return new TaskCallback();
            }

            WechatGroupPoolQuery wechatGroupPoolQuery = new WechatGroupPoolQuery();
            wechatGroupPoolQuery.setOwner(userId);
            wechatGroupPoolQuery.setCorpId(SwitchConfig.sellerCorpId);
            wechatGroupPoolQuery.setDeleted((byte) 0);
            wechatGroupPoolQuery.setStatus((byte) 0);
            List<WechatGroupPoolDTO> wechatGroupPoolList = wechatGroupPoolService.listByCondition(wechatGroupPoolQuery);
            if (wechatGroupPoolList.size() >= SwitchConfig.sellerCorpPlatformUserMinValidGroupCountForPool) {
                PlatformLogUtil.logInfo("企微群聊池刷新hts任务执行，企微账号下可用群聊池数量充足", userId, wechatGroupPoolList.size());
                return new TaskCallback();
            }

            GroupCreateRequest groupCreateRequest = new GroupCreateRequest();
            groupCreateRequest.setCorpId(corpId);
            groupCreateRequest.setUserId(userId);
            groupCreateRequest.setGroupName("用户-商家沟通群" + wechatGroupPoolNameSequence.nextValue());
            groupCreateRequest.setDisableAddContact(true);
            groupCreateRequest.setDisableRename(true);
            groupCreateRequest.setEnableInviteConfirm(false);
            ResultDO<String> resultDO = groupService.asyncCreate(groupCreateRequest);
            if (Objects.isNull(resultDO) || !resultDO.getSuccess()) {
                PlatformLogUtil.logFail("企微群聊池刷新hts任务执行，创建群聊失败", TripSCRMErrorCode.SYSTEM_EXCEPTION.getCode(), LogListUtil.newArrayList(resultDO));
                DingTalkApi.sendTaskMessage(String.format("【企微群聊池刷新】创建群聊失败，userId:%s，corpId:%s", userId, corpId));
                return new TaskCallback();
            }

            ldbTairManager.incr(minuteWindowKey, 1, 0, 100);
            ldbTairManager.incr(hourWindowKey, 1, 0, 4000);
            ldbTairManager.incr(dayWindowKey, 1, 0, 90000);
            String requestId = resultDO.getModel();
            ldbTairManager.put(TairConstant.GROUP_POOL_REFRESH_ASYNC_CREATE_REQUEST_ID_PREFIX + requestId, userId, 60 * 5);
            PlatformLogUtil.logInfo("企微群聊池刷新hts任务执行，异步创建群聊请求发送成功", LogListUtil.newArrayList(resultDO));
        } catch (Exception e) {
            PlatformLogUtil.logException("企微群聊池刷新hts任务执行，执行异常", e.getMessage(), e, LogListUtil.newArrayList(task));
        } finally {
            SpaceInfoThreadLocalUtils.remove();
        }
        return new TaskCallback();
    }

    public void register(String corpId, String userId, Date executeTime) {
        Task task = new Task();
        task.setTaskExecutor(this);
        task.setExecuteTime(executeTime);
        task.setTimeout(10000);
        Map<String, Serializable> param = new HashMap<>();
        param.put("corpId", corpId);
        param.put("userId", userId);
        param.put("UUID", UUID.randomUUID().toString());
        task.setParam(param);
        HTSTaskResult<Boolean> result = taskScheduleClient.registerTask(task);
        if (Objects.isNull(result) || !result.isSuccess()) {
            PlatformLogUtil.logFail("企微群聊池刷新hts任务注册失败", LogListUtil.newArrayList(task, result));
            return;
        }
        PlatformLogUtil.logInfo("企微群聊池刷新hts任务注册成功", task, result);
    }
}
