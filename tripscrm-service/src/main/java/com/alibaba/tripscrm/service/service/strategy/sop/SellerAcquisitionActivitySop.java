package com.alibaba.tripscrm.service.service.strategy.sop;

import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.dal.model.domain.data.TaskInfoDO;
import com.alibaba.tripscrm.dal.model.domain.query.task.TaskQuery;
import com.alibaba.tripscrm.domain.enums.TaskType;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.task.SopTypeEnum;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.vo.activity.ActivitySopVO;
import com.alibaba.tripscrm.service.model.vo.seller.SellerAcquisitionActivitySopConfigVO;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.activity.ActivitySopService;
import com.alibaba.tripscrm.service.service.task.ability.factory.TaskConfigProcessorFactory;
import com.alibaba.tripscrm.service.service.task.base.TaskService;
import com.alibaba.tripscrm.service.util.system.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum.EVENT;

/**
 * <AUTHOR>
 * @since 2025/4/9 10:51
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SellerAcquisitionActivitySop extends AbstractActivitySop<SellerAcquisitionActivitySopConfigVO> {
    private final LdbTairManager ldbTairManager;
    private final TaskService taskService;
    private final ActivitySopService activitySopService;

    @Override
    protected void checkConfigValid(SellerAcquisitionActivitySopConfigVO config, Boolean isUpdate) {
        if (Objects.isNull(config)) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
    }

    @Override
    protected void createTaskList(SellerAcquisitionActivitySopConfigVO config, Long activityId) {
        // 1.创建商家获客链接数据处理任务
        createSellerAcquisitionLinkDataProcessTask(config, activityId);
        // 2.创建客户打标任务
        createWechatCustomerAddTagTask(config, activityId);
        // 3.创建个人欢迎语任务
        createPersonWelcomeTask(config, activityId);
        // 4.创建初始化客户-商家沟通群聊任务
        createInitialCustomerSellerGroupTask(config, activityId);
        // 5.创建初始化客户-商家沟通群聊成员任务
        createInitialCustomerSellerGroupMemberTask(config, activityId);
        // 6.创建发送群聊消息任务
        createGroupChatTask(config, activityId);
        // 7.创建发送私聊消息任务
        createSingleChatTask(config, activityId);
    }

    @Override
    protected void updateTaskList(SellerAcquisitionActivitySopConfigVO config) {
        TaskQuery query = new TaskQuery();
        query.setActivityId(config.getActivityId());
        List<TaskInfoDO> taskList = taskService.query(query);
        Map<String, TaskInfoDO> taskType2TaskInfoDO = taskList.stream().collect(Collectors.toMap(TaskInfoDO::getType, a -> a, (t1, t2) -> t1.getGmtCreate().compareTo(t2.getGmtCreate()) > 0 ? t1 : t2));
        // 1.更新创建商家获客链接数据处理任务
        if (taskType2TaskInfoDO.containsKey(TaskType.SELLER_ACQUISITION_LINK_DATA_PROCESS.getCode())) {
            TaskInfoDO task = taskType2TaskInfoDO.get(TaskType.SELLER_ACQUISITION_LINK_DATA_PROCESS.getCode());
            updateSellerAcquisitionLinkDataProcessTask(config, task);
        } else {
            createSellerAcquisitionLinkDataProcessTask(config, config.getActivityId());
        }

        // 2.更新客户打标任务
        if (taskType2TaskInfoDO.containsKey(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode())) {
            TaskInfoDO task = taskType2TaskInfoDO.get(TaskType.WECHAT_CUSTOMER_ADD_TAG.getCode());
            updateWechatCustomerAddTagTask(config, task);
        } else {
            createWechatCustomerAddTagTask(config, config.getActivityId());
        }

        // 3.更新个人欢迎语任务
        if (taskType2TaskInfoDO.containsKey(TaskType.PERSONAL_WELCOME.getCode())) {
            TaskInfoDO task = taskType2TaskInfoDO.get(TaskType.PERSONAL_WELCOME.getCode());
            updatePersonWelcomeTask(config, task);
        } else {
            createPersonWelcomeTask(config, config.getActivityId());
        }

        // 4.更新初始化客户-商家沟通群聊任务
        if (taskType2TaskInfoDO.containsKey(TaskType.INITIAL_CUSTOMER_SELLER_GROUP.getCode())) {
            TaskInfoDO task = taskType2TaskInfoDO.get(TaskType.INITIAL_CUSTOMER_SELLER_GROUP.getCode());
            updateInitialCustomerSellerGroupTask(config, task);
        } else {
            createInitialCustomerSellerGroupTask(config, config.getActivityId());
        }

        // 5.更新初始化客户-商家沟通群聊成员任务
        if (taskType2TaskInfoDO.containsKey(TaskType.INITIAL_CUSTOMER_SELLER_GROUP_MEMBER.getCode())) {
            TaskInfoDO task = taskType2TaskInfoDO.get(TaskType.INITIAL_CUSTOMER_SELLER_GROUP_MEMBER.getCode());
            updateInitialCustomerSellerGroupMemberTask(config, task);
        } else {
            createInitialCustomerSellerGroupMemberTask(config, config.getActivityId());
        }

        // 6.更新发送群聊消息任务
        if (taskType2TaskInfoDO.containsKey(TaskType.GROUP_CHAT_MESSAGE.getCode())) {
            TaskInfoDO task = taskType2TaskInfoDO.get(TaskType.GROUP_CHAT_MESSAGE.getCode());
            updateGroupChatTask(config, task);
        } else {
            createGroupChatTask(config, config.getActivityId());
        }

        // 7.更新发送私聊消息任务
        if (taskType2TaskInfoDO.containsKey(TaskType.ROBOT_CHAT_MESSAGE.getCode())) {
            TaskInfoDO task = taskType2TaskInfoDO.get(TaskType.ROBOT_CHAT_MESSAGE.getCode());
            updateSingleChatTask(config, task);
        } else {
            createSingleChatTask(config, config.getActivityId());
        }
    }

    private void createSellerAcquisitionLinkDataProcessTask(SellerAcquisitionActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.SELLER_ACQUISITION_LINK_DATA_PROCESS).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setEventSourceId(SwitchConfig.sellerAcquisitionLinkDataProcessEventSourceId);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void updateSellerAcquisitionLinkDataProcessTask(SellerAcquisitionActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }

        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setEventSourceId(SwitchConfig.sellerAcquisitionLinkDataProcessEventSourceId);
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void createWechatCustomerAddTagTask(SellerAcquisitionActivitySopConfigVO config, Long activityId) {
        if (!config.getIsDefaultActivity()) {
            return;
        }

        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.WECHAT_CUSTOMER_ADD_TAG).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        // 任务基本配置
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setEventSourceId(SwitchConfig.notifyInitSellerAndCustomerGroupEventSourceId);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void updateWechatCustomerAddTagTask(SellerAcquisitionActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        if (!config.getIsDefaultActivity()) {
            return;
        }

        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setEventSourceId(SwitchConfig.notifyInitSellerAndCustomerGroupEventSourceId);
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void createPersonWelcomeTask(SellerAcquisitionActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.PERSONAL_WELCOME).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setMaterialId(SwitchConfig.sellerAcquisitionActivityPersonalWelcomeMaterialId);
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.addCustomerBySellerAcquisitionLinkEventSourceId);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void updatePersonWelcomeTask(SellerAcquisitionActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setMaterialId(SwitchConfig.sellerAcquisitionActivityPersonalWelcomeMaterialId);
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.addCustomerBySellerAcquisitionLinkEventSourceId);
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void createInitialCustomerSellerGroupTask(SellerAcquisitionActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.INITIAL_CUSTOMER_SELLER_GROUP).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.notifyInitSellerAndCustomerGroupEventSourceId);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void updateInitialCustomerSellerGroupTask(SellerAcquisitionActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.notifyInitSellerAndCustomerGroupEventSourceId);
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void createInitialCustomerSellerGroupMemberTask(SellerAcquisitionActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.INITIAL_CUSTOMER_SELLER_GROUP_MEMBER).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.notifyInviteSellerAndCustomerJoinGroupEventSourceId);
        if (CollectionUtils.isNotEmpty(config.getUserIdList())) {
            taskInfoDO.setSendUserId(String.join(",", config.getUserIdList()));
        }
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void updateInitialCustomerSellerGroupMemberTask(SellerAcquisitionActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setEventSourceId(SwitchConfig.notifyInviteSellerAndCustomerJoinGroupEventSourceId);
        if (CollectionUtils.isNotEmpty(config.getUserIdList())) {
            taskInfoDO.setSendUserId(String.join(",", config.getUserIdList()));
        }
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void createGroupChatTask(SellerAcquisitionActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.GROUP_CHAT_MESSAGE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setMaterialId(SwitchConfig.sellerAcquisitionActivityInitGroupChatMaterialId);
        taskInfoDO.setEventSourceId(SwitchConfig.scrmInviteSellerCustomerJoinGroupSuccessForGroupChat);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void updateGroupChatTask(SellerAcquisitionActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setMaterialId(SwitchConfig.sellerAcquisitionActivityInitGroupChatMaterialId);
        taskInfoDO.setEventSourceId(SwitchConfig.scrmInviteSellerCustomerJoinGroupSuccessForGroupChat);
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void createSingleChatTask(SellerAcquisitionActivitySopConfigVO config, Long activityId) {
        TaskInfoDO taskInfoDO = TaskConfigProcessorFactory.getDataProcessorByType(TaskType.ROBOT_CHAT_MESSAGE).getInitConfig(activityId, config.getSpaceId(), getTaskName(config));
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setMaterialId(SwitchConfig.sellerAcquisitionActivityJoinGroupSuccessSingleChatMaterialId);
        taskInfoDO.setEventSourceId(SwitchConfig.scrmInviteSellerCustomerJoinGroupSuccessForSingleChat);
        Integer effectLines = taskService.addTaskInfo(taskInfoDO);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    private void updateSingleChatTask(SellerAcquisitionActivitySopConfigVO config, TaskInfoDO taskInfoDO) {
        // 活动改名字了
        if (!taskInfoDO.getName().startsWith(config.getName())) {
            taskInfoDO.setName(getTaskName(config));
        }
        taskInfoDO.setEffectStartTime(new Date());
        taskInfoDO.setEffectEndTime(Optional.ofNullable(config.getEffectEndTime()).orElse(DateUtils.addTenYear(new Date())));
        taskInfoDO.setTriggerType(Integer.parseInt(EVENT.getCode()));
        taskInfoDO.setMaterialId(SwitchConfig.sellerAcquisitionActivityJoinGroupSuccessSingleChatMaterialId);
        taskInfoDO.setEventSourceId(SwitchConfig.scrmInviteSellerCustomerJoinGroupSuccessForSingleChat);
        Integer effectLines = taskService.updateTaskInfo(taskInfoDO, false);
        if (effectLines == 0) {
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TASK_FAIL);
        }
    }

    /**
     * 更新活动，并按顺序更新活动下的任务
     *
     * @param activitySopVO 活动配置
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ActivitySopVO<SellerAcquisitionActivitySopConfigVO> activitySopVO) {
        if (!Optional.ofNullable(activitySopVO.getConfig().getIsDefaultActivity()).orElse(false)) {
            super.update(activitySopVO);
            return;
        }

        activitySopService.offline(activitySopVO.getActivityId());
        super.update(activitySopVO);
        activitySopService.online(activitySopVO.getActivityId());
    }

    @Override
    public SopTypeEnum getSopType() {
        return SopTypeEnum.SELLER_ACQUISITION;
    }

    @Override
    public void fillInfo(ActivitySopVO<SellerAcquisitionActivitySopConfigVO> activitySopVO) {
        Object o = ldbTairManager.get(TairConstant.SELLER_ACQUISITION_ACTIVITY_ADD_CUSTOMER_COUNT_PREFIX + activitySopVO.getActivityId());
        activitySopVO.getConfig().setAddCustomerCount(Long.parseLong(String.valueOf(Optional.ofNullable(o).orElse(0))));

        if (activitySopVO.getConfig().getIsDefaultActivity()) {
            activitySopVO.setCreatorName("系统");
            activitySopVO.setLastOperatorName("系统");
        }
    }
}
