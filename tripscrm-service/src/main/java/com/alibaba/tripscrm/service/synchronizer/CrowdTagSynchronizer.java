package com.alibaba.tripscrm.service.synchronizer;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.enums.TagTypeEnum;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.constant.TairConstant;
import com.alibaba.tripscrm.service.enums.tag.BizTypeEnum;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TriggerTimeEnum;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.middleware.swtich.SwitchConfig;
import com.alibaba.tripscrm.service.model.dto.tag.*;
import com.alibaba.tripscrm.service.model.vo.wechat.WechatCustomerVO;
import com.alibaba.tripscrm.service.model.domain.context.BaseSynchronizerContext;
import com.alibaba.tripscrm.service.model.domain.context.DelayMetaqContext;
import com.alibaba.tripscrm.service.model.domain.query.ItemTagRelationQuery;
import com.alibaba.tripscrm.service.model.domain.query.TagGroupQuery;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.fusionchat.ProfileService;
import com.alibaba.tripscrm.service.service.tag.TagGroupService;
import com.alibaba.tripscrm.service.service.tag.TagInfoService;
import com.alibaba.tripscrm.service.service.tag.TagRelationService;
import com.alibaba.tripscrm.service.service.wechat.WechatCustomerService;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.alibaba.tripscrm.service.util.system.NumberUtils;
import com.alibaba.tripscrm.service.util.UicUtils;
import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.fliggy.pokemon.lock.client.api.annotation.TairLock;
import com.google.common.collect.Lists;
import com.taobao.ateye.annotation.Switch;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 诸葛标签关系同步器
 *
 * <AUTHOR>
 * @since 2025/01/02 10:46
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class CrowdTagSynchronizer extends BaseLockedSynchronizer<CrowdTagSynchronizerDTO> {

    private final WechatCustomerService wechatCustomerService;
    private final ProfileService profileService;
    private final TagGroupService tagGroupService;
    private final TagInfoService tagInfoService;
    private final UicUtils uicUtils;
    private final MetaqProducer metaqProducer;
    private final TagRelationService tagRelationService;

    @Switch(description = "获取uid失败后重试等待时间", name = "delayRetrySeconds")
    public static Integer delayRetrySeconds = 60 * 2;

    private volatile Set<String> systemTagSyncLbsPermanentCitySet;

    private volatile Set<String> systemTagSyncLbsPermanentProvinceSet;

    @Override
    public void process(BaseSynchronizerContext<CrowdTagSynchronizerDTO> context) {
        try {
            if (EnvUtils.isPre() && !SwitchConfig.TAG_SYNC_PRE_WHITE_LIST.stream().anyMatch(s -> s.equals(context.getExternalUserId()))){
                PlatformLogUtil.logFail("诸葛标签同步失败，用户不在预发白名单中", LogListUtil.newArrayList(context, getLockKey(context)));
                return;
            }
            Boolean queryResult = query(context);
            if (!queryResult) {
                PlatformLogUtil.logFail("诸葛标签同步器参数查询失败");
                return;
            }
            syncTagInfo(context);
            syncTagRelation(context);
            CrowdTagSynchronizerDTO data = context.getData();
            List<CrowdTagDTO> checkNeedToCreateTagInfo = new ArrayList<>(data.getPicassoLabelInfo());
            checkNeedToCreateTagInfo.addAll(context.getData().getCrowdTagInfo());
            if (CollectionUtils.isEmpty(checkNeedToCreateTagInfo)) {
                return;
            }
            data.setNewTagIdList(checkNeedToCreateTagInfo.stream().map(x -> getTagId(x.getTagName(), x.getTagValue())).collect(Collectors.toList()));
            context.setData(data);
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.SYNCHRONIZER_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(context));
            throw new TripscrmException(TripSCRMErrorCode.SYNCHRONIZER_ERROR);
        }

    }

    /**
     * 从诸葛和DB中查询诸葛标签信息
     *
     * @param context 上下文
     * @return
     */
    public Boolean query(BaseSynchronizerContext<CrowdTagSynchronizerDTO> context) throws Exception {
        try {
            if (!StringUtils.hasLength(context.getUnionId()) && StringUtils.hasLength(context.getExternalUserId())) {
                PlatformLogUtil.logFail("查询失败, unionId和externalUserId都为空", LogListUtil.newArrayList(context));
                return false;
            }
            // 查询客户信息
            WechatCustomerVO wechatCustomerInfo = getWechatCustomerInfo(context.getUnionId(), context.getExternalUserId());
            if (Objects.isNull(wechatCustomerInfo)) {
                PlatformLogUtil.logFail("查询客户信息失败");
                PlatformLogUtil.logFail("查询客户信息失败", LogListUtil.newArrayList(context));
                return false;
            }
            CrowdTagSynchronizerDTO crowdTagSynchronizerDTO = context.getData();
            if (Objects.isNull(context.getData().getIsRetry())) {
                crowdTagSynchronizerDTO.setIsRetry(false);
            }

            // 从诸葛查询标签
            List<CrowdTagDTO> picassoLabelInfo = getPicassoLabelInfo(wechatCustomerInfo, context.getCorpId(), context.getData().getIsRetry());
            if (CollectionUtils.isEmpty(picassoLabelInfo)) {
                PlatformLogUtil.logFail("查询诸葛标签结果为空", LogListUtil.newArrayList(context));
                return false;
            }
            // 从db查询标签
            List<CrowdTagDTO> crowdTagInfo = getCustomerSystemTagInfo(wechatCustomerInfo.getTagIdList());
            PlatformLogUtil.logInfo("查询诸葛标签结果", LogListUtil.newArrayList(picassoLabelInfo, crowdTagInfo));
            crowdTagSynchronizerDTO.setPicassoLabelInfo(picassoLabelInfo);
            crowdTagSynchronizerDTO.setCrowdTagInfo(crowdTagInfo);
            crowdTagSynchronizerDTO.setWechatCustomerInfo(wechatCustomerInfo);
            context.setData(crowdTagSynchronizerDTO);

        } catch (Exception e) {
            PlatformLogUtil.logException("查询失败", e.getMessage(), e, LogListUtil.newArrayList(context));
            return false;
        }
        return true;
    }

    /**
     * 同步标签信息
     *
     * @param context 上下文
     */
    public void syncTagInfo(BaseSynchronizerContext<CrowdTagSynchronizerDTO> context) {
        List<CrowdTagDTO> newTagInfo = getNewTagInfo(context.getData().getPicassoLabelInfo(), context.getData().getCrowdTagInfo());
        List<CrowdTagDTO> updateTagInfo = getUpdateTagInfo(context.getData().getPicassoLabelInfo(), context.getData().getCrowdTagInfo());
        List<CrowdTagDTO> deleteTagInfo = getDeleteTagInfo(context.getData().getPicassoLabelInfo(), context.getData().getCrowdTagInfo());
        PlatformLogUtil.logInfo("标签比对结果", LogListUtil.newArrayList(newTagInfo, updateTagInfo, deleteTagInfo));
        List<CrowdTagDTO> checkNeedToCreateTagInfo = new ArrayList<>();
        checkNeedToCreateTagInfo.addAll(newTagInfo);
        checkNeedToCreateTagInfo.addAll(updateTagInfo);
        for (CrowdTagDTO crowdTagDTO : checkNeedToCreateTagInfo) {
            TagGroupDTO tagGroupDTO = tagGroupService.selectBySpaceIdAndTagTypeAndSourceWithCache(SwitchConfig.SCRM_BASE_SPACE_ID, TagTypeEnum.CROWD_TAG, crowdTagDTO.getTagName());
            if (Objects.isNull(tagGroupDTO)) {
                PlatformLogUtil.logFail("查询标签组结果为空，无法处理", LogListUtil.newArrayList(crowdTagDTO.getTagName()));
                continue;
            }

            createTagIfAbsent(tagGroupDTO.getId(), crowdTagDTO.getTagValue(), context);
        }
    }

    /**
     * 同步标签关系
     *
     * @param context 上下文
     */
    public void syncTagRelation(BaseSynchronizerContext<CrowdTagSynchronizerDTO> context) {

        if (Objects.isNull(context.getData().getPicassoLabelInfo()) || Objects.isNull(context.getData().getCrowdTagInfo()) || Objects.isNull(context.getData().getWechatCustomerInfo().getTagIdList())) {
            PlatformLogUtil.logFail("标签比对结果为空", LogListUtil.newArrayList(context));
            return;
        }
        // 获取标签Id
        List<String> picassoTagIdList = context.getData().getPicassoLabelInfo().stream().map(x -> getTagId(x.getTagName(), x.getTagValue())).collect(Collectors.toList());
        List<String> customerSystemTagIdList = context.getData().getCrowdTagInfo().stream().map(x -> getTagId(x.getTagName(), x.getTagValue())).collect(Collectors.toList());

        WechatCustomerVO wechatCustomerInfo = context.getData().getWechatCustomerInfo();

        List<String> oldCustomerTagIdList = wechatCustomerInfo.getTagIdList();
        List<String> newCustomerTagIdList = wechatCustomerInfo.getTagIdList().stream()
                .filter(x -> !customerSystemTagIdList.contains(x))
                .collect(Collectors.toList());
        newCustomerTagIdList.addAll(picassoTagIdList);

        List<String> customerDeleteTagIdList = oldCustomerTagIdList.stream()
                .filter(x -> !newCustomerTagIdList.contains(x))
                .collect(Collectors.toList());

        List<String> customerAddTagIdList = newCustomerTagIdList.stream()
                .filter(x -> !oldCustomerTagIdList.contains(x))
                .collect(Collectors.toList());

        List<ItemTagRelationDTO> addList = customerAddTagIdList.stream()
                .map(id -> {
                    ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
                    itemTagRelationDTO.setItemId(wechatCustomerInfo.getExternalUserId() + "_" + wechatCustomerInfo.getCorpId());
                    itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_CUSTOMER.getCode());
                    itemTagRelationDTO.setTag(id);
                    itemTagRelationDTO.setDeleted((byte) 0);
                    return itemTagRelationDTO;
                })
                .collect(Collectors.toList());
        tagRelationService.batchUpsertSelective(addList);

        ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
        itemTagRelationDTO.setDeleted((byte) 1);
        itemTagRelationDTO.setIsSync(false);
        for (String deleteTagId : customerDeleteTagIdList) {
            ItemTagRelationQuery condition = new ItemTagRelationQuery();
            condition.setItemId(wechatCustomerInfo.getExternalUserId() + "_" + wechatCustomerInfo.getCorpId());
            condition.setItemType(BizTypeEnum.WECHAT_CUSTOMER.getCode());
            condition.setTag(deleteTagId);
            tagRelationService.updateSelective(itemTagRelationDTO, condition);
        }
    }


    /**
     * 获取微信客户信息
     *
     * @param unionId unionId
     * @param externalUserId externalUserId
     * @return
     */
    private WechatCustomerVO getWechatCustomerInfo(String unionId, String externalUserId) {
        if (!StringUtils.hasLength(externalUserId)) {
            externalUserId = wechatCustomerService.getExternalUserIdByUnionId(unionId);
        }

        if (!StringUtils.hasLength(externalUserId)) {
            PlatformLogUtil.logFail("根据unionId查询externalUserId失败");
            return null;
        }

        List<WechatCustomerVO> wechatCustomerList = wechatCustomerService.listByExternalUserIdList(Lists.newArrayList(externalUserId));
        if (CollectionUtils.isEmpty(wechatCustomerList)) {
            return null;
        }

        return wechatCustomerList.get(0);
    }

    private List<CrowdTagDTO> getPicassoLabelInfo(WechatCustomerVO wechatCustomerVO, String corpId, Boolean isRetry) {
        try {
            String uid = uicUtils.getUidByUnionId(wechatCustomerVO.getUnionId());
            // 没有绑定uid
            if (!StringUtils.hasLength(uid)) {
                // 需要稍后重试
                if (!isRetry) {
                    Long triggerTime = System.currentTimeMillis() + delayRetrySeconds * 1000L;
                    DelayMetaqContext delayMetaqContext = buildDelayMetaqContext(wechatCustomerVO.getUnionId(), corpId, wechatCustomerVO.getExternalUserId(), triggerTime);
                    metaqProducer.send(MQEnum.DELAY_MQ_SCHEDULING, "wechat_customer_crowd_tag_change" + wechatCustomerVO.getUnionId(), "", JSON.toJSONString(delayMetaqContext), null);
                }

                throw new TripscrmException(TripSCRMErrorCode.ID_MAPPING_FAIL);
            }

            // 从tag_group中查询type = 3的标签作为labelNameList
            TagGroupQuery query = new TagGroupQuery();
            query.setTagType(Lists.newArrayList(TagTypeEnum.CROWD_TAG.getCode()));
            query.setSpaceIdList(Lists.newArrayList(SwitchConfig.SCRM_BASE_SPACE_ID));
            List<TagGroupDTO> tagGroupDOS = tagGroupService.selectByCondition(query);
            if (CollectionUtils.isEmpty(tagGroupDOS)) {
                PlatformLogUtil.logFail("从db中查询诸葛标签组结果为空，无法处理", LogListUtil.newArrayList(wechatCustomerVO));
                throw new TripscrmException(TripSCRMErrorCode.QUERY_PICASSO_LABEL_INFO_ERROR);
            }
            List<String> labelNameList = tagGroupDOS.stream().map(TagGroupDTO::getSource).collect(Collectors.toList());

// 获取标签名列表tag
//            List<String> labelNameList = SwitchConfig.CROWD_TAG_SYNC_LABEL_LIST;
            List<List<String>> labelNameListPartition = Lists.partition(labelNameList, 10);

            Map<String, String> combinedResults = new HashMap<>();

            for (List<String> partition : labelNameListPartition) {
                TripSCRMResult<Map<String, String>> result = profileService.getProfileValue(uid, partition);
                if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
                    PlatformLogUtil.logFail("查询客户诸葛标签失败", LogListUtil.newArrayList(uid, partition, result));
                    continue;
                }
                PlatformLogUtil.logInfo("查询客户诸葛标签成功", LogListUtil.newArrayList(uid, partition, result));
                combinedResults.putAll(result.getData());
            }
            PlatformLogUtil.logInfo("查询客户诸葛标签结果", LogListUtil.newArrayList(uid, labelNameList, combinedResults));
            return combinedResults.entrySet()
                    .stream()
                    .filter(entry -> {
                        if (!StringUtils.hasLength(entry.getValue())) {
                            return false;
                        }

                        if (Lists.newArrayList("未知", "unknown").contains(entry.getValue())) {
                            return false;
                        }

                        if ("lbs_permanent_city".equals(entry.getKey())) {
                            return getSystemTagSyncLbsPermanentCitySet().contains(entry.getValue());
                        }

                        if ("lbs_permanent_province".equals(entry.getKey())) {
                            return getSystemTagSyncLbsPermanentProvinceSet().contains(entry.getValue());
                        }

                        return true;
                    })
                    .map(entry -> new CrowdTagDTO(entry.getKey(), entry.getValue()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            PlatformLogUtil.logException("查询客户诸葛标签失败", e.getMessage(), e, LogListUtil.newArrayList(wechatCustomerVO, corpId, isRetry));
            PlatformLogUtil.logFail("查询客户诸葛标签失败");
            throw new TripscrmException(TripSCRMErrorCode.QUERY_PICASSO_LABEL_INFO_ERROR);
        }
    }

    private static DelayMetaqContext buildDelayMetaqContext(String unionId, String corpId, String externalUserId, Long triggerTime) {
        DelayMetaqContext delayMetaqContext = new DelayMetaqContext();
        delayMetaqContext.setTriggerTime(triggerTime);
        delayMetaqContext.setTriggerType(TriggerTimeEnum.CUSTOM.getCode());
        delayMetaqContext.setSecondDelayLevel(true);
        delayMetaqContext.setFunctionType(DelayScheduleMethodEnum.SEND_WECHAT_CUSTOMER_SYSTEM_TAG_CHANGE_MESSAGE);
        delayMetaqContext.setDelayKey("delay_wechat_customer_system_tag_change" + unionId);

        JSONObject param = new JSONObject();
        param.put("corpId", corpId);
        param.put("unionId", unionId);
        param.put("externalUserId", externalUserId);
        delayMetaqContext.setParam(JSON.toJSONString(param));
        return delayMetaqContext;
    }

    private Set<String> getSystemTagSyncLbsPermanentCitySet() {
        if (Objects.nonNull(systemTagSyncLbsPermanentCitySet)) {
            return systemTagSyncLbsPermanentCitySet;
        }

        synchronized (this) {
            if (Objects.nonNull(systemTagSyncLbsPermanentCitySet)) {
                return systemTagSyncLbsPermanentCitySet;
            }

            systemTagSyncLbsPermanentCitySet = Arrays.stream(SwitchConfig.SYSTEM_TAG_SYNC_LBS_PERMANENT_CITY_LIST_STR.split(",")).filter(StringUtils::hasLength).collect(Collectors.toSet());
        }

        return systemTagSyncLbsPermanentCitySet;
    }

    private Set<String> getSystemTagSyncLbsPermanentProvinceSet() {
        if (Objects.nonNull(systemTagSyncLbsPermanentProvinceSet)) {
            return systemTagSyncLbsPermanentProvinceSet;
        }

        synchronized (this) {
            if (Objects.nonNull(systemTagSyncLbsPermanentProvinceSet)) {
                return systemTagSyncLbsPermanentProvinceSet;
            }

            systemTagSyncLbsPermanentProvinceSet = Arrays.stream(SwitchConfig.SYSTEM_TAG_SYNC_LBS_PERMANENT_PROVINCE_LIST_STR.split(",")).filter(StringUtils::hasLength).collect(Collectors.toSet());
        }

        return systemTagSyncLbsPermanentProvinceSet;
    }

    private List<CrowdTagDTO> getCustomerSystemTagInfo(List<String> tagIdList) {
        List<CrowdTagDTO> customerTagInfo = new ArrayList<>();
        if (CollectionUtils.isEmpty(tagIdList)) {
            return customerTagInfo;
        }

        for (String tagId : tagIdList) {
            TagInfoDTO tagInfoDTO = tagInfoService.queryByIdWithCache(tagId);
            // 只处理诸葛标签
            if (Objects.isNull(tagInfoDTO) || !Objects.equals(TagTypeEnum.CROWD_TAG, TagTypeEnum.of(tagInfoDTO.getTagType()))) {
                continue;
            }

            TagGroupDTO tagGroupDTO = tagGroupService.selectByIdWithCache(tagInfoDTO.getGroupId());
            // 只处理诸葛标签
            if (Objects.isNull(tagGroupDTO) || !Objects.equals(TagTypeEnum.CROWD_TAG, TagTypeEnum.of(tagInfoDTO.getTagType()))) {
                continue;
            }

            String labelName = tagGroupDTO.getSource();
            String labelValue = tagInfoDTO.getSource();
            customerTagInfo.add(new CrowdTagDTO(labelName, labelValue));
        }

        return customerTagInfo;
    }

    private List<CrowdTagDTO> getNewTagInfo(List<CrowdTagDTO> picassoLabelInfo, List<CrowdTagDTO> customerSystemTagInfo) {
        List<CrowdTagDTO> result = new ArrayList<>();

        // 将输入的 List<CrowdTagBO> 转换成 Map<String, String> 方便比较
        Map<String, String> picassoLabelMap = picassoLabelInfo.stream()
                .collect(Collectors.toMap(CrowdTagDTO::getTagName, CrowdTagDTO::getTagValue));

        Map<String, String> customerSystemTagMap = customerSystemTagInfo.stream()
                .collect(Collectors.toMap(CrowdTagDTO::getTagName, CrowdTagDTO::getTagValue));

        // 遍历 picassoLabelMap，找出不在 customerSystemTagMap 中的标签
        for (Map.Entry<String, String> entry : picassoLabelMap.entrySet()) {
            if (!customerSystemTagMap.containsKey(entry.getKey())) {
                result.add(new CrowdTagDTO(entry.getKey(), entry.getValue()));
            }
        }
        return result;
    }

    private List<CrowdTagDTO> getUpdateTagInfo(List<CrowdTagDTO> picassoLabelInfo, List<CrowdTagDTO> customerSystemTagInfo) {
        List<CrowdTagDTO> result = new ArrayList<>();

        // 将输入的 List<CrowdTagBO> 转换成 Map<String, String> 方便比较
        Map<String, String> picassoLabelMap = picassoLabelInfo.stream()
                .collect(Collectors.toMap(CrowdTagDTO::getTagName, CrowdTagDTO::getTagValue));

        Map<String, String> customerSystemTagMap = customerSystemTagInfo.stream()
                .collect(Collectors.toMap(CrowdTagDTO::getTagName, CrowdTagDTO::getTagValue));

        // 遍历 picassoLabelMap，找出需要更新的标签
        for (Map.Entry<String, String> entry : picassoLabelMap.entrySet()) {
            if (customerSystemTagMap.containsKey(entry.getKey()) && !Objects.equals(entry.getValue(), customerSystemTagMap.get(entry.getKey()))) {
                result.add(new CrowdTagDTO(entry.getKey(), entry.getValue()));
            }
        }

        return result;
    }

    private List<CrowdTagDTO> getDeleteTagInfo(List<CrowdTagDTO> picassoLabelInfo, List<CrowdTagDTO> customerSystemTagInfo) {
        List<CrowdTagDTO> result = new ArrayList<>();

        // 将输入的 List<CrowdTagBO> 转换成 Map<String, String> 方便比较
        Map<String, String> picassoLabelMap = picassoLabelInfo.stream()
                .collect(Collectors.toMap(CrowdTagDTO::getTagName, CrowdTagDTO::getTagValue));

        Map<String, String> customerSystemTagMap = customerSystemTagInfo.stream()
                .collect(Collectors.toMap(CrowdTagDTO::getTagName, CrowdTagDTO::getTagValue));

        // 遍历 customerSystemTagMap，找出需要删除的标签
        for (Map.Entry<String, String> entry : customerSystemTagMap.entrySet()) {
            if (!picassoLabelMap.containsKey(entry.getKey())) {
                result.add(new CrowdTagDTO(entry.getKey(), entry.getValue()));
            }
        }

        return result;
    }

    /**
     * 加个小锁，怕并发冲突
     */
    @TairLock(value = "'checkCreateNewTag_' + #groupId + '_' + #source")
    public void createTagIfAbsent(Long groupId, String source, BaseSynchronizerContext<CrowdTagSynchronizerDTO> context) {
        try {
            TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(groupId, source);
            Long tagId;
            if (Objects.isNull(tagInfoDTO)) {
                PlatformLogUtil.logInfo("查询标签结果为空，需要新建", LogListUtil.newArrayList(groupId, source));
                List<TagInfoDTO> records = new ArrayList<>();
                TagInfoDTO record = new TagInfoDTO();
                record.setName(source);
                record.setDescription("");
                record.setParentId(0L);
                record.setTagType(TagTypeEnum.CROWD_TAG.getCode());
                record.setSpaceId(SwitchConfig.SCRM_BASE_SPACE_ID);
                record.setGroupId(groupId);
                record.setSource(source);
                records.add(record);
                // int effectLines = tagInfoService.batchInsertSelective(records);
                tagInfoService.batchInsertSelective(records, false);

                TagInfoDTO newTagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(groupId, source);
                if (Objects.isNull(newTagInfoDTO)) {
                    PlatformLogUtil.logFail("标签创建失败", LogListUtil.newArrayList(groupId, source));
                    return;
                }
                // 标签创建成功，需要新建关系
                PlatformLogUtil.logInfo("标签创建成功，需要新建关系", LogListUtil.newArrayList(newTagInfoDTO));
                tagId = newTagInfoDTO.getId();
            } else {
                tagId = tagInfoDTO.getId();
            }

            ItemTagRelationDTO itemTagRelationDTO = new ItemTagRelationDTO();
            itemTagRelationDTO.setTagId(tagId);
            itemTagRelationDTO.setItemId(context.getExternalUserId() + "_" + context.getCorpId());
            itemTagRelationDTO.setItemType(BizTypeEnum.WECHAT_CUSTOMER.getCode());
            itemTagRelationDTO.setIsSync(false);
            Integer effectLines = tagRelationService.upsertSelective(itemTagRelationDTO);
            if (effectLines <= 0) {
                PlatformLogUtil.logFail("标签关系创建失败", LogListUtil.newArrayList(itemTagRelationDTO));
            }
        } catch (Exception e) {
            PlatformLogUtil.logException(TripSCRMErrorCode.CREATE_TAG_INFO_ERROR.getDescCn(), e.getMessage(), e, LogListUtil.newArrayList(groupId, source, context.getExternalUserId(), context.getCorpId()));
            throw new TripscrmException(TripSCRMErrorCode.CREATE_TAG_INFO_ERROR);
        }


    }

    private String getTagId(String tagName, String tagValue) {
        TagGroupDTO tagGroupDTO = tagGroupService.selectBySpaceIdAndTagTypeAndSourceWithCache(SwitchConfig.SCRM_BASE_SPACE_ID, TagTypeEnum.CROWD_TAG, tagName);
        TagInfoDTO tagInfoDTO = tagInfoService.queryByGroupIdAndSourceWithCache(tagGroupDTO.getId(), tagValue);
        return String.valueOf(tagInfoDTO.getId());
    }


    @Override
    public String getLockKey(BaseSynchronizerContext<CrowdTagSynchronizerDTO> context) {
        return TairConstant.CROWD_TAG_SYNCHRONIZER_LOCK_PREFIX + context.getExternalUserId() + "_" + context.getCorpId();
    }

    private List<List<String>> splitIntoPartition(List<String> list, Integer partition) {
        if (!NumberUtils.validInteger(partition)) {
            partition = 10;
        }
        List<List<String>> chunks = new ArrayList<>();
        for (int i = 0; i < list.size(); i += partition) {
            chunks.add(list.subList(i, Math.min(i + partition, list.size())));
        }
        return chunks;
    }


}
