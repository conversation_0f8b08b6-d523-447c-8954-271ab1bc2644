package com.alibaba.tripscrm.service.middleware.schedulerx.alipay;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.alibaba.tripscrm.service.manager.middleware.OdpsManager;
import com.alibaba.tripscrm.service.manager.tair.LdbTairManager;
import com.alibaba.tripscrm.service.model.domain.query.GroupRelationQuery;
import com.alibaba.tripscrm.service.service.group.GroupRelationNewService;
import com.fliggy.pokemon.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.alibaba.tripscrm.service.constant.AlipayConstant.CURRENT_MEMBER_COUNT;
import static com.alibaba.tripscrm.service.constant.AlipayConstant.TOTAL_MEMBER_COUNT;
import static com.alibaba.tripscrm.service.constant.TairConstant.ALIPAY_GROUP_MEMBER_COUNT_CACHE;
import static com.alibaba.tripscrm.service.constant.TairConstant.EXPIRE_TIME_DAY_UNIT;

/**
 * @description：支付宝群成员数量统计处理器
 * @Author：wangrui
 * @create：2025/8/24 21:18
 * @Filename：AlipayGroupMemberCountProcessor
 */
@Component
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class AlipayGroupMemberCountProcessor  extends JavaProcessor {
    private final OdpsManager odpsManager;
    private final LdbTairManager ldbTairManager;

    private static final String ALL_GROUP_MEMBER_COUNT_SQL = "SELECT COUNT(DISTINCT user_id) FROM trip_ods.s_group_relation_tripscrm_app_v2 WHERE ds = MAX_PT('trip_ods.s_group_relation_tripscrm_app_v2') AND platform_type = 4;";
    private static final String ONLIN_GROUP_MEMBER_COUNT_SQL = "SELECT COUNT(DISTINCT user_id) FROM trip_ods.s_group_relation_tripscrm_app_v2 WHERE ds = MAX_PT('trip_ods.s_group_relation_tripscrm_app_v2') AND platform_type = 4 AND status = 1;";

    @Override
    public ProcessResult process(JobContext context) throws Exception {

        Long totalMemberCount = odpsManager.getCount(ALL_GROUP_MEMBER_COUNT_SQL);
        Long currentMemberCount = odpsManager.getCount(ONLIN_GROUP_MEMBER_COUNT_SQL);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(TOTAL_MEMBER_COUNT, totalMemberCount);
        jsonObject.put(CURRENT_MEMBER_COUNT, currentMemberCount);

        String date = DateUtils.format(new Date(), "yyyy-MM-dd");

        ldbTairManager.put(ALIPAY_GROUP_MEMBER_COUNT_CACHE+ date, jsonObject.toJSONString(), EXPIRE_TIME_DAY_UNIT);


        return new ProcessResult(true);
    }




}
