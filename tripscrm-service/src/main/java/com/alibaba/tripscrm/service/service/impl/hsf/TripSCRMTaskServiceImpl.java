package com.alibaba.tripscrm.service.service.impl.hsf;

import com.ali.fliggy.china.common.util.list.LogListUtil;
import com.ali.fliggy.china.common.util.platformlog.PlatformLogUtil;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.tripscrm.TripSCRMErrorCode;
import com.alibaba.tripscrm.domain.request.TripSCRMBatchCreateTaskRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMCreateTaskRequest;
import com.alibaba.tripscrm.domain.request.TripSCRMTaskBuildRequest;
import com.alibaba.tripscrm.domain.result.TripSCRMResult;
import com.alibaba.tripscrm.service.TripSCRMTaskService;
import com.alibaba.tripscrm.service.annotation.ServiceLog;
import com.alibaba.tripscrm.service.enums.system.DelayScheduleMethodEnum;
import com.alibaba.tripscrm.service.enums.system.MQEnum;
import com.alibaba.tripscrm.service.enums.task.TaskTriggerTypeEnum;
import com.alibaba.tripscrm.service.manager.opensearch.TaskInfoOpenSearchManager;
import com.alibaba.tripscrm.service.middleware.metaq.producer.MetaqProducer;
import com.alibaba.tripscrm.service.model.domain.context.DelayMetaqContext;
import com.alibaba.tripscrm.service.model.domain.query.TaskInfoQuery;
import com.alibaba.tripscrm.service.model.vo.task.TaskExecuteParam;
import com.alibaba.tripscrm.service.model.exception.TripscrmException;
import com.alibaba.tripscrm.service.service.task.ability.TaskExecuteStrategy;
import com.alibaba.tripscrm.service.util.env.EnvUtils;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.tddl.client.sequence.Sequence;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/6/1
 */
@Slf4j
@HSFProvider(serviceInterface = TripSCRMTaskService.class)
public class TripSCRMTaskServiceImpl implements TripSCRMTaskService {
    private final MetaqProducer metaqProducer;
    private final Sequence sequence;
    private final TaskInfoOpenSearchManager taskInfoOpenSearchManager;
    private final TaskExecuteStrategy taskExecuteStrategy;


    public TripSCRMTaskServiceImpl(MetaqProducer metaqProducer, @Qualifier("asyncInterfaceTriggerTaskTraceIdSequence") Sequence sequence, TaskInfoOpenSearchManager taskInfoOpenSearchManager, TaskExecuteStrategy taskExecuteStrategy) {
        this.metaqProducer = metaqProducer;
        this.sequence = sequence;
        this.taskInfoOpenSearchManager = taskInfoOpenSearchManager;
        this.taskExecuteStrategy = taskExecuteStrategy;
    }

    @Override
    @ServiceLog("任务触发（旧）")
    public TripSCRMResult<String> buildTask(TripSCRMTaskBuildRequest request) {
        TripSCRMCreateTaskRequest tripSCRMCreateTaskRequest = new TripSCRMCreateTaskRequest();
        tripSCRMCreateTaskRequest.setTaskId(request.getTaskId());
        tripSCRMCreateTaskRequest.setTargetId(request.getTargetId());
        tripSCRMCreateTaskRequest.setTargetType(request.getTargetType());
        tripSCRMCreateTaskRequest.setExtInfo(request.getExtInfo());
        tripSCRMCreateTaskRequest.setImageUrlList(request.getImageUrlList());
        return createSyncTask(tripSCRMCreateTaskRequest);
    }

    @Override
    @ServiceLog("任务批量触发")
    public TripSCRMResult<List<String>> batchCreateSyncTask(TripSCRMBatchCreateTaskRequest request) {
        TaskInfoQuery query = new TaskInfoQuery();
        query.setEnv(EnvUtils.getEnvironment());
        query.setTags(request.getTagId());
        query.setTaskTypeCodeList(Lists.newArrayList(request.getTaskType().getCode()));
        PageInfo<Long> pageInfo = taskInfoOpenSearchManager.getTaskIdList(query);
        List<Long> taskIdList = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(taskIdList)) {
            PlatformLogUtil.logFail("任务批量触发，任务数据不存在", LogListUtil.newArrayList(request));
            return TripSCRMResult.fail(TripSCRMErrorCode.NOT_EXIST_TASK_INFO_DATA);
        }

        List<String> result = new ArrayList<>();
        TripSCRMCreateTaskRequest tripSCRMCreateTaskRequest = new TripSCRMCreateTaskRequest();
        for (Long taskId : taskIdList) {
            tripSCRMCreateTaskRequest.setTaskId(taskId);
            result.add(createSyncTask(tripSCRMCreateTaskRequest).getData());
        }
        return TripSCRMResult.success(result);
    }

    @Override
    @ServiceLog("任务触发")
    public TripSCRMResult<String> createSyncTask(TripSCRMCreateTaskRequest request) {
        // extraInfo 中存在非法字符串
        if (Optional.ofNullable(request.getExtInfo()).orElse(new HashMap<>()).values().stream().anyMatch(x -> Objects.isNull(x) || !(x instanceof String) || !StringUtils.hasText((String) x))) {
            throw new TripscrmException(TripSCRMErrorCode.INVALID_PARAMS);
        }
        TaskExecuteParam param = TaskExecuteParam.buildParam(new Date(), request.getTaskId(), TaskTriggerTypeEnum.INTERFACE, request.getTargetType(), request.getTargetId(), true, request.getExtInfo(), request.getImageUrlList());
        return taskExecuteStrategy.runMain(param);
    }

    @Override
    @ServiceLog("任务异步触发")
    public TripSCRMResult<String> createAsyncTask(TripSCRMCreateTaskRequest request) {
        // 请求唯一Id
        long sequenceId = sequence.nextValue();
        // 构建参数
        DelayMetaqContext delayMetaqContext = buildDelayMetaqContext(request, sequenceId);
        // 发送异步消息来执行任务
        metaqProducer.send(MQEnum.DELAY_MQ_SCHEDULING, "hsf_delay_" + sequenceId, "", JSON.toJSONString(delayMetaqContext), null);
        return TripSCRMResult.success(String.valueOf(sequenceId));
    }

    private DelayMetaqContext buildDelayMetaqContext(TripSCRMCreateTaskRequest request, Long sequenceId) {
        DelayMetaqContext delayMetaqContext = new DelayMetaqContext();
        delayMetaqContext.setTriggerTime(request.getTriggerTime());
        delayMetaqContext.setTriggerType(request.getTriggerTimeType());
        delayMetaqContext.setSecondDelayLevel(request.getSecondDelayLevel());
        delayMetaqContext.setFunctionType(DelayScheduleMethodEnum.MAIN_TASK_EXECUTE);
        delayMetaqContext.setDelayKey("delayTaskRetry%interface%" + request.getTaskId());
        TaskExecuteParam param = TaskExecuteParam.buildParam(new Date(), request.getTaskId(), TaskTriggerTypeEnum.INTERFACE, request.getTargetType(), request.getTargetId(), sequenceId, true, request.getExtInfo(), request.getImageUrlList());
        delayMetaqContext.setParam(JSON.toJSONString(param));
        return delayMetaqContext;
    }
}
