<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.alibaba.tripscrm</groupId>
        <artifactId>tripscrm</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>tripscrm-service</artifactId>
    <packaging>jar</packaging>
    <name>tripscrm-service</name>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.tripscrm</groupId>
            <artifactId>tripscrm-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.fmp.mms</groupId>
            <artifactId>mms-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripscrm</groupId>
            <artifactId>tripscrm-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripzoo.proxy</groupId>
            <artifactId>tripzoo.proxy-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripzoo.admin</groupId>
            <artifactId>tripzoo-admin-client</artifactId>
            <version>${tripzoo-admin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-ons-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-tair-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-metaq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-notify-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.travel</groupId>
            <artifactId>travelitems-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-diamond-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zaxxer</groupId>
                    <artifactId>HikariCP</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fliggy.pokemon</groupId>
            <artifactId>pokemon-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.pokemon</groupId>
            <artifactId>pokemon-lock-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>acl.api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.persistence</groupId>
            <artifactId>jakarta.persistence-api</artifactId>
        </dependency>
        <!-- Normandy 资源凭证 SDK -->
        <dependency>
            <groupId>com.alibaba.normandy.credential</groupId>
            <artifactId>normandy-credential-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.tair</groupId>
            <artifactId>tairjedis-sdk-singlepath</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.ihr</groupId>
            <artifactId>amdplatform-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.intl.app</groupId>
            <artifactId>micro-front-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>buc.sso.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-buc-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.trip.tripdivision</groupId>
            <artifactId>tripdivision-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.trip.wireless</groupId>
            <artifactId>tripwmc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx-plugin-trace-eagleeye</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.wireless.mtop</groupId>
            <artifactId>mtop-pandora-export-agent</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.taobao.trippc</groupId>
            <artifactId>trippc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>

        <!--   Stream模式的包     -->
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>app-stream-client</artifactId>
        </dependency>
        <!-- mtop spi for wx -->
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>mtop-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripzoo</groupId>
            <artifactId>tripzoo-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.csp</groupId>
            <artifactId>switchcenter</artifactId>
        </dependency>

        <!--飞猪玩法平台-->
        <dependency>
            <groupId>com.fliggy.fliggyplaycore</groupId>
            <artifactId>fliggyplaycore-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fliggy</groupId>
            <artifactId>fliggy-award-upgrade-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fliggy</groupId>
            <artifactId>f-marketing-play-client</artifactId>
        </dependency>
        <!--淘系 类目hsf客户端 -->
        <dependency>
            <groupId>com.alibaba.category</groupId>
            <artifactId>category-hsf-client-starter</artifactId>
        </dependency>

        <!--问酒-->
        <dependency>
            <groupId>com.taobao.trip</groupId>
            <artifactId>hcp-client</artifactId>
        </dependency>

        <!--交易-->
        <dependency>
            <groupId>com.taobao.payment</groupId>
            <artifactId>finance-member-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastvalidator-generator</artifactId>
                    <groupId>com.alibaba.fastvalidator</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--summary-->
        <dependency>
            <groupId>com.taobao.travel</groupId>
            <artifactId>travelsummary-service-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.taobao.common.uic.uic-common</groupId>
            <artifactId>uic-client-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.ateye</groupId>
            <artifactId>ateye-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.csp</groupId>
            <artifactId>sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fliggy</groupId>
            <artifactId>furl-client</artifactId>
        </dependency>
        <!--加密服务-->
        <dependency>
            <groupId>com.taobao.keycenter</groupId>
            <artifactId>keycenter-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.trip.tripgalaxy</groupId>
            <artifactId>tripgalaxy-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.fic</groupId>
            <artifactId>fic-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.opensearch</groupId>
            <artifactId>aliyun-sdk-opensearch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.dataworks</groupId>
            <artifactId>data-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.work.alipmc</groupId>
            <artifactId>alipmc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cronutils</groupId>
            <artifactId>cron-utils</artifactId>
        </dependency>

        <!-- 圈人 -->
        <dependency>
            <groupId>com.taobao.tripupf</groupId>
            <artifactId>tripupf-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy</groupId>
            <artifactId>crowd-client</artifactId>
        </dependency>
        <!-- OSS -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!-- okHttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-gson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>4.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-asr</artifactId>
            <version>3.1.872</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.triptower</groupId>
            <artifactId>triptower-all-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fliggy.trade</groupId>
            <artifactId>fliggy-trade-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.trade</groupId>
            <artifactId>logger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.travel</groupId>
            <artifactId>travelitems-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.trip.hpc</groupId>
            <artifactId>hpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.fc</groupId>
            <artifactId>fc-open-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.contentplatform</groupId>
            <artifactId>content-api-fliggywx</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-buc-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.trip.trippoi</groupId>
            <artifactId>fliggypoi-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.quicka</groupId>
            <artifactId>quicka-tianquan-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripc</groupId>
            <artifactId>tripcdest-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.tbsession</groupId>
            <artifactId>tbsession</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.tbsession</groupId>
            <artifactId>tbsession-springboot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.ffa.touch</groupId>
            <artifactId>ffa-customize-touch-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripbaymaxbusi</groupId>
            <artifactId>tripbaymaxbusi-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.train</groupId>
            <artifactId>traindc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.train</groupId>
            <artifactId>trc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.traffic</groupId>
            <artifactId>cross-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.tccp</groupId>
            <artifactId>tccp-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.trip</groupId>
            <artifactId>hts-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.trip</groupId>
            <artifactId>tripjourneyop-service-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fliggy.hotel</groupId>
            <artifactId>hotel-seller-base-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.tccpos</groupId>
            <artifactId>tccpos-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.diamond</groupId>
                    <artifactId>diamond-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.mmp</groupId>
            <artifactId>mmp-client-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.csp</groupId>
            <artifactId>sentinel-annotation-aspectj</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripchannel</groupId>
            <artifactId>trip-channel-ai-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.travel</groupId>
            <artifactId>travelvc-common</artifactId>
        </dependency>
    </dependencies>
</project>
