# 基础镜像的Dockerfile ： https://yuque.antfin.com/aone/docker/rm2g1d
# 基于基础镜像
FROM reg.docker.alibaba-inc.com/alibase/alios7u2-min

# 增加这一行，保证下面的内容都不用cache
ADD http://yum.tbsite.net/config/yum/centos/Centos.repo /dev/shm/

# 配置强依赖，勿删除，如需要其他jdk版本，请另行安装，这里不会冲突
RUN set -eux &&\
    yum install -b current -y ajdk git &&\
    yum install -y jq -b current &&\
    rpm -e --noscripts --justdb ajdk &&\
    yum history new &&\
    yum clean all &&\
    rm -rf /var/cache/yum &&\
    mkdir -p /opt/taobao/cise &&\
    mv -f "$(realpath /opt/taobao/java)" /opt/taobao/cise/java &&\
    rm -f /opt/taobao/java &&\
    ln -s /opt/taobao/cise/java/bin/* /usr/local/bin &&\
    echo -e '[global]\nindex-url = http://mirrors.aliyun.com/pypi/simple/\ntrusted-host = mirrors.aliyun.com' > /etc/pip.conf


