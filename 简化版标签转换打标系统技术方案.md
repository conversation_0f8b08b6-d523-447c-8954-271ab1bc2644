# 简化版标签转换打标系统技术方案

## 1. 方案概述

本方案提供一套简化的标签转换和打标系统，支持多种标签平台之间的人群转换，具备基本的大数据量处理能力和故障恢复机制。

### 1.1 核心功能
- **多平台支持**：支持诸葛、图灵、支付宝、微信等多个标签平台
- **人群转换**：实现不同平台间的人群数据转换和标签打标
- **大数据处理**：支持千万级用户数据的批量处理
- **故障恢复**：具备任务重试和断点续传能力
- **进度跟踪**：实时监控任务执行进度和状态

### 1.2 技术特点
- 采用简化设计，降低实现复杂度
- 基于异步处理，提升系统吞吐量
- 支持水平扩展，满足业务增长需求
- 开发周期短（2-3周），适合快速上线

## 2. 核心表设计

### 2.1 通用转换任务表

```sql
-- 简化的转换任务表
CREATE TABLE `tag_convert_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID，全局唯一',
  `source_platform` varchar(32) NOT NULL COMMENT '源平台：zhuge、turing、alipay等',
  `target_platform` varchar(32) NOT NULL COMMENT '目标平台',
  `source_crowd_id` varchar(64) NOT NULL COMMENT '源人群ID',
  `target_crowd_id` varchar(64) DEFAULT NULL COMMENT '目标人群ID',
  `crowd_name` varchar(128) NOT NULL COMMENT '人群名称',
  `total_count` bigint(20) DEFAULT 0 COMMENT '总用户数',
  `success_count` bigint(20) DEFAULT 0 COMMENT '成功数',
  `fail_count` bigint(20) DEFAULT 0 COMMENT '失败数',
  `status` tinyint(4) DEFAULT 0 COMMENT '状态：0待处理,1处理中,2成功,3失败',
  `tag_info` json DEFAULT NULL COMMENT '标签信息',
  `progress` int(11) DEFAULT 0 COMMENT '进度百分比',
  `operator` varchar(64) NOT NULL COMMENT '操作人',
  `error_msg` text COMMENT '错误信息',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_source_platform` (`source_platform`),
  KEY `idx_operator` (`operator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签转换任务表';
```

### 2.2 用户打标记录表

```sql
-- 简化的用户打标记录表
CREATE TABLE `user_tag_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(64) NOT NULL COMMENT '任务ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `platform` varchar(32) NOT NULL COMMENT '平台',
  `tag_operation` varchar(16) NOT NULL COMMENT '操作：add、remove',
  `tag_name` varchar(128) NOT NULL COMMENT '标签名称',
  `status` tinyint(4) DEFAULT 0 COMMENT '状态：0待处理,1成功,2失败',
  `error_msg` varchar(512) DEFAULT NULL COMMENT '错误信息',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `gmt_create` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户打标记录表';
```

## 3. 核心数据结构

### 3.1 请求响应对象

```java
// 转换任务请求
@Data
public class TagConvertRequest {
    private String sourcePlatform;      // 源平台
    private String targetPlatform;      // 目标平台
    private String sourceCrowdId;       // 源人群ID
    private String crowdName;           // 人群名称
    private TagInfo tagInfo;            // 标签信息
    private String operator;            // 操作人
}

// 标签信息
@Data
public class TagInfo {
    private String tagName;             // 标签名称
    private String tagType;             // 标签类型
    private String firstLevelTag;       // 一级标签
    private String secondLevelTag;      // 二级标签
    private Map<String, Object> extra;  // 扩展信息
}

// 任务进度响应
@Data
public class TaskProgress {
    private String taskId;              // 任务ID
    private Integer status;             // 状态
    private Integer progress;           // 进度百分比
    private Long totalCount;            // 总数
    private Long successCount;          // 成功数
    private Long failCount;             // 失败数
    private String errorMsg;            // 错误信息
    private Date startTime;             // 开始时间
    private Date endTime;               // 结束时间
}
```

## 4. 大数据量处理方案

### 4.1 简单分批处理

```java
@Service
public class SimpleBatchProcessor {
    
    private static final int BATCH_SIZE = 1000; // 每批1000个用户
    
    @Autowired
    private TagConvertMapper tagConvertMapper;
    
    @Autowired
    private UserTagRecordMapper userTagRecordMapper;
    
    public void processBigData(String taskId, List<String> allUserIds) {
        // 更新任务状态为处理中
        updateTaskStatus(taskId, 1);
        
        // 简单分批处理
        List<List<String>> batches = Lists.partition(allUserIds, BATCH_SIZE);
        int totalBatches = batches.size();
        
        for (int i = 0; i < totalBatches; i++) {
            List<String> batch = batches.get(i);
            
            try {
                // 处理一批用户
                BatchResult result = processBatch(taskId, batch);
                
                // 更新统计信息
                updateTaskCounts(taskId, result.getSuccessCount(), result.getFailCount());
                
                // 更新进度
                int progress = (i + 1) * 100 / totalBatches;
                updateTaskProgress(taskId, progress);
                
                log.info("批次处理完成: taskId={}, batch={}/{}, progress={}%", 
                    taskId, i + 1, totalBatches, progress);
                
            } catch (Exception e) {
                log.error("批次处理失败: taskId={}, batch={}", taskId, i, e);
                // 记录失败，继续处理下一批
                updateTaskCounts(taskId, 0, batch.size());
            }
        }
        
        // 更新任务状态为完成
        updateTaskStatus(taskId, 2);
    }
    
    private BatchResult processBatch(String taskId, List<String> userIds) {
        BatchResult result = new BatchResult();
        
        for (String userId : userIds) {
            try {
                // 处理单个用户打标
                boolean success = processUserTag(taskId, userId);
                if (success) {
                    result.incrementSuccess();
                } else {
                    result.incrementFail();
                }
            } catch (Exception e) {
                log.error("用户打标失败: userId={}", userId, e);
                result.incrementFail();
            }
        }
        
        return result;
    }
}
```

### 4.2 异步处理配置

```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean("tagConvertExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);          // 核心线程数
        executor.setMaxPoolSize(20);           // 最大线程数
        executor.setQueueCapacity(1000);       // 队列容量
        executor.setKeepAliveSeconds(60);      // 线程空闲时间
        executor.setThreadNamePrefix("tag-convert-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}

@Service
public class AsyncTagService {
    
    @Autowired
    private SimpleBatchProcessor batchProcessor;
    
    @Async("tagConvertExecutor")
    public CompletableFuture<Void> processAsync(String taskId, List<String> userIds) {
        try {
            batchProcessor.processBigData(taskId, userIds);
            log.info("异步任务完成: taskId={}", taskId);
        } catch (Exception e) {
            log.error("异步任务失败: taskId={}", taskId, e);
            // 更新任务状态为失败
            updateTaskStatus(taskId, 3);
        }
        return CompletableFuture.completedFuture(null);
    }
}
```

## 5. 故障恢复机制

### 5.1 简单重试服务

```java
@Service
public class SimpleRetryService {
    
    @Autowired
    private TagConvertMapper tagConvertMapper;
    
    @Autowired
    private AsyncTagService asyncTagService;
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void checkFailedTasks() {
        // 查找处理中但超过30分钟没更新的任务
        DateTime timeout = DateTime.now().minusMinutes(30);
        List<TagConvertTask> timeoutTasks = findTimeoutTasks(timeout);
        
        for (TagConvertTask task : timeoutTasks) {
            log.warn("发现超时任务，准备重试: taskId={}", task.getTaskId());
            retryTask(task);
        }
        
        // 查找失败的任务（可手动重试）
        List<TagConvertTask> failedTasks = findFailedTasks();
        log.info("当前失败任务数量: {}", failedTasks.size());
    }
    
    public void retryTask(String taskId) {
        TagConvertTask task = tagConvertMapper.selectByTaskId(taskId);
        if (task != null) {
            retryTask(task);
        }
    }
    
    private void retryTask(TagConvertTask task) {
        try {
            // 重置任务状态
            task.setStatus(0); // 待处理
            task.setProgress(0);
            task.setErrorMsg(null);
            task.setGmtModified(new Date());
            tagConvertMapper.updateById(task);
            
            // 获取用户列表并重新提交处理
            List<String> userIds = getUserIds(task);
            asyncTagService.processAsync(task.getTaskId(), userIds);
            
            log.info("任务重试提交成功: taskId={}", task.getTaskId());
            
        } catch (Exception e) {
            log.error("任务重试失败: taskId={}", task.getTaskId(), e);
        }
    }
    
    private List<TagConvertTask> findTimeoutTasks(DateTime timeout) {
        return tagConvertMapper.selectList(
            Wrappers.<TagConvertTask>lambdaQuery()
                .eq(TagConvertTask::getStatus, 1) // 处理中
                .lt(TagConvertTask::getGmtModified, timeout)
        );
    }
}
```

### 5.2 断点续传服务

```java
@Service
public class CheckpointService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    // 保存处理进度
    public void saveProgress(String taskId, int processedCount) {
        String key = "progress:" + taskId;
        redisTemplate.opsForValue().set(key, String.valueOf(processedCount), Duration.ofDays(7));
    }
    
    // 获取处理进度
    public int getProgress(String taskId) {
        String key = "progress:" + taskId;
        String progress = redisTemplate.opsForValue().get(key);
        return progress != null ? Integer.parseInt(progress) : 0;
    }
    
    // 从断点继续处理
    public void resumeFromCheckpoint(String taskId, List<String> allUserIds) {
        int processedCount = getProgress(taskId);
        
        if (processedCount > 0 && processedCount < allUserIds.size()) {
            log.info("从断点继续处理: taskId={}, processedCount={}", taskId, processedCount);
            
            // 从断点继续
            List<String> remainingUsers = allUserIds.subList(processedCount, allUserIds.size());
            batchProcessor.processBigData(taskId, remainingUsers);
        } else {
            log.info("重新开始处理: taskId={}", taskId);
            batchProcessor.processBigData(taskId, allUserIds);
        }
    }
    
    // 清除进度记录
    public void clearProgress(String taskId) {
        String key = "progress:" + taskId;
        redisTemplate.delete(key);
    }
}
```

## 6. 平台适配器设计

### 6.1 适配器接口

```java
// 平台适配器接口
public interface PlatformAdapter {
    /**
     * 获取人群用户列表
     */
    List<String> getCrowdUsers(String crowdId);
    
    /**
     * 给用户打标
     */
    boolean tagUser(String userId, TagInfo tagInfo);
    
    /**
     * 批量给用户打标
     */
    BatchTagResult batchTagUsers(List<String> userIds, TagInfo tagInfo);
}

// 批量打标结果
@Data
public class BatchTagResult {
    private int successCount;
    private int failCount;
    private List<String> failedUserIds;
    private String errorMessage;
}
```

### 6.2 具体适配器实现

```java
// 诸葛平台适配器
@Component("zhugeAdapter")
public class ZhugePlatformAdapter implements PlatformAdapter {
    
    @Autowired
    private TripCrowdCommonService tripCrowdCommonService;
    
    @Override
    public List<String> getCrowdUsers(String crowdId) {
        try {
            // 调用诸葛接口获取用户列表
            TripCommonPlatformResult<List<String>> result = 
                tripCrowdCommonService.getCrowdUsers(Long.valueOf(crowdId));
            
            if (result.isSuccess()) {
                return result.getData();
            } else {
                log.error("获取诸葛人群用户失败: crowdId={}, error={}", crowdId, result.getErrorMsg());
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("获取诸葛人群用户异常: crowdId={}", crowdId, e);
            return Collections.emptyList();
        }
    }
    
    @Override
    public boolean tagUser(String userId, TagInfo tagInfo) {
        // 诸葛平台作为数据源，不需要打标
        return true;
    }
    
    @Override
    public BatchTagResult batchTagUsers(List<String> userIds, TagInfo tagInfo) {
        // 诸葛平台作为数据源，不需要打标
        BatchTagResult result = new BatchTagResult();
        result.setSuccessCount(userIds.size());
        result.setFailCount(0);
        return result;
    }
}

// 图灵平台适配器
@Component("turingAdapter")
public class TuringPlatformAdapter implements PlatformAdapter {
    
    @Autowired
    private TuringTagService turingTagService;
    
    @Override
    public List<String> getCrowdUsers(String crowdId) {
        // 图灵平台通常作为目标平台，不需要获取用户
        return Collections.emptyList();
    }
    
    @Override
    public boolean tagUser(String userId, TagInfo tagInfo) {
        try {
            // 调用图灵打标接口
            TuringTagRequest request = new TuringTagRequest();
            request.setUserId(userId);
            request.setTagName(tagInfo.getTagName());
            request.setTagType(tagInfo.getTagType());
            
            ResultDO<Boolean> result = turingTagService.addTag(request);
            return result.isSuccess() && Boolean.TRUE.equals(result.getData());
            
        } catch (Exception e) {
            log.error("图灵打标失败: userId={}, tagInfo={}", userId, tagInfo, e);
            return false;
        }
    }
    
    @Override
    public BatchTagResult batchTagUsers(List<String> userIds, TagInfo tagInfo) {
        BatchTagResult result = new BatchTagResult();
        List<String> failedUserIds = new ArrayList<>();
        
        for (String userId : userIds) {
            if (tagUser(userId, tagInfo)) {
                result.setSuccessCount(result.getSuccessCount() + 1);
            } else {
                result.setFailCount(result.getFailCount() + 1);
                failedUserIds.add(userId);
            }
        }
        
        result.setFailedUserIds(failedUserIds);
        return result;
    }
}
```

## 7. 核心业务服务

### 7.1 主要业务逻辑

```java
@Service
public class TagConvertService {
    
    @Autowired
    private TagConvertMapper tagConvertMapper;
    
    @Autowired
    private AsyncTagService asyncTagService;
    
    @Autowired
    private Map<String, PlatformAdapter> platformAdapters;
    
    /**
     * 提交转换任务
     */
    public String submitConvertTask(TagConvertRequest request) {
        // 1. 参数校验
        validateRequest(request);
        
        // 2. 创建任务记录
        String taskId = generateTaskId();
        TagConvertTask task = new TagConvertTask();
        task.setTaskId(taskId);
        task.setSourcePlatform(request.getSourcePlatform());
        task.setTargetPlatform(request.getTargetPlatform());
        task.setSourceCrowdId(request.getSourceCrowdId());
        task.setCrowdName(request.getCrowdName());
        task.setTagInfo(JSON.toJSONString(request.getTagInfo()));
        task.setOperator(request.getOperator());
        task.setStatus(0); // 待处理
        task.setStartTime(new Date());
        
        tagConvertMapper.insert(task);
        
        // 3. 获取源人群用户列表
        List<String> userIds = getCrowdUserIds(request.getSourcePlatform(), 
                                              request.getSourceCrowdId());
        
        // 4. 更新总数并异步处理
        task.setTotalCount(userIds.size());
        tagConvertMapper.updateById(task);
        
        asyncTagService.processAsync(taskId, userIds);
        
        log.info("转换任务提交成功: taskId={}, totalCount={}", taskId, userIds.size());
        return taskId;
    }
    
    /**
     * 获取任务进度
     */
    public TaskProgress getTaskProgress(String taskId) {
        TagConvertTask task = tagConvertMapper.selectByTaskId(taskId);
        if (task == null) {
            throw new BusinessException("任务不存在: " + taskId);
        }
        
        TaskProgress progress = new TaskProgress();
        progress.setTaskId(taskId);
        progress.setStatus(task.getStatus());
        progress.setProgress(task.getProgress());
        progress.setTotalCount(task.getTotalCount());
        progress.setSuccessCount(task.getSuccessCount());
        progress.setFailCount(task.getFailCount());
        progress.setErrorMsg(task.getErrorMsg());
        progress.setStartTime(task.getStartTime());
        progress.setEndTime(task.getEndTime());
        
        return progress;
    }
    
    /**
     * 获取人群用户ID列表
     */
    private List<String> getCrowdUserIds(String platform, String crowdId) {
        PlatformAdapter adapter = platformAdapters.get(platform + "Adapter");
        if (adapter == null) {
            throw new BusinessException("不支持的平台: " + platform);
        }
        
        return adapter.getCrowdUsers(crowdId);
    }
    
    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "task_" + DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss").format(LocalDateTime.now()) 
               + "_" + RandomStringUtils.randomNumeric(4);
    }
}
```

## 8. 接口设计

### 8.1 REST接口

```java
@RestController
@RequestMapping("/api/tag-convert")
@Slf4j
public class TagConvertController {
    
    @Autowired
    private TagConvertService tagConvertService;
    
    @Autowired
    private SimpleRetryService retryService;
    
    /**
     * 提交转换任务
     */
    @PostMapping("/submit")
    public BaseResult<String> submitTask(@RequestBody @Valid TagConvertRequest request) {
        try {
            String taskId = tagConvertService.submitConvertTask(request);
            return BaseResult.success(taskId);
        } catch (Exception e) {
            log.error("提交转换任务失败", e);
            return BaseResult.fail("提交失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务进度
     */
    @GetMapping("/progress/{taskId}")
    public BaseResult<TaskProgress> getProgress(@PathVariable String taskId) {
        try {
            TaskProgress progress = tagConvertService.getTaskProgress(taskId);
            return BaseResult.success(progress);
        } catch (Exception e) {
            log.error("查询任务进度失败: taskId={}", taskId, e);
            return BaseResult.fail("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 重试失败任务
     */
    @PostMapping("/retry/{taskId}")
    public BaseResult<Void> retryTask(@PathVariable String taskId) {
        try {
            retryService.retryTask(taskId);
            return BaseResult.success();
        } catch (Exception e) {
            log.error("重试任务失败: taskId={}", taskId, e);
            return BaseResult.fail("重试失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务列表
     */
    @GetMapping("/list")
    public BaseResult<PageInfo<TagConvertTask>> getTaskList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize,
            @RequestParam(required = false) String operator,
            @RequestParam(required = false) Integer status) {
        
        try {
            PageInfo<TagConvertTask> pageInfo = tagConvertService.getTaskList(
                pageNum, pageSize, operator, status);
            return BaseResult.success(pageInfo);
        } catch (Exception e) {
            log.error("查询任务列表失败", e);
            return BaseResult.fail("查询失败: " + e.getMessage());
        }
    }
}
```

## 9. 配置文件

### 9.1 应用配置

```yaml
# application.yml
server:
  port: 8080

spring:
  datasource:
    url: ****************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
        
  task:
    execution:
      pool:
        core-size: 10
        max-size: 20
        queue-capacity: 1000
        keep-alive: 60s
        thread-name-prefix: tag-convert-

# 自定义配置
tag-convert:
  batch-size: 1000                    # 批处理大小
  max-retry-count: 3                  # 最大重试次数
  timeout-minutes: 30                 # 任务超时时间（分钟）
  checkpoint-interval: 1000           # 检查点保存间隔
  
# 日志配置
logging:
  level:
    com.alibaba.tripscrm.service.tag: INFO
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## 10. 使用示例

### 10.1 API调用示例

```bash
# 1. 提交诸葛到图灵的转换任务
curl -X POST http://localhost:8080/api/tag-convert/submit \
  -H "Content-Type: application/json" \
  -d '{
    "sourcePlatform": "zhuge",
    "targetPlatform": "turing", 
    "sourceCrowdId": "12345",
    "crowdName": "高价值用户",
    "tagInfo": {
      "tagName": "高价值用户",
      "tagType": "behavior",
      "firstLevelTag": "用户画像",
      "secondLevelTag": "价值分层"
    },
    "operator": "张三"
  }'

# 响应示例
{
  "success": true,
  "data": "task_20250929_143022_1234",
  "message": "success"
}

# 2. 查询任务进度
curl -X GET http://localhost:8080/api/tag-convert/progress/task_20250929_143022_1234

# 响应示例
{
  "success": true,
  "data": {
    "taskId": "task_20250929_143022_1234",
    "status": 1,
    "progress": 65,
    "totalCount": 100000,
    "successCount": 65000,
    "failCount": 0,
    "startTime": "2025-09-29 14:30:22",
    "endTime": null
  }
}

# 3. 重试失败任务
curl -X POST http://localhost:8080/api/tag-convert/retry/task_20250929_143022_1234
```

### 10.2 Java客户端示例

```java
@Service
public class TagConvertClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    private static final String BASE_URL = "http://localhost:8080/api/tag-convert";
    
    /**
     * 提交转换任务
     */
    public String submitTask(String sourcePlatform, String targetPlatform, 
                           String sourceCrowdId, String crowdName, 
                           TagInfo tagInfo, String operator) {
        
        TagConvertRequest request = new TagConvertRequest();
        request.setSourcePlatform(sourcePlatform);
        request.setTargetPlatform(targetPlatform);
        request.setSourceCrowdId(sourceCrowdId);
        request.setCrowdName(crowdName);
        request.setTagInfo(tagInfo);
        request.setOperator(operator);
        
        BaseResult<String> result = restTemplate.postForObject(
            BASE_URL + "/submit", request, BaseResult.class);
            
        if (result.isSuccess()) {
            return result.getData();
        } else {
            throw new RuntimeException("提交任务失败: " + result.getMessage());
        }
    }
    
    /**
     * 查询任务进度
     */
    public TaskProgress getProgress(String taskId) {
        BaseResult<TaskProgress> result = restTemplate.getForObject(
            BASE_URL + "/progress/" + taskId, BaseResult.class);
            
        if (result.isSuccess()) {
            return result.getData();
        } else {
            throw new RuntimeException("查询进度失败: " + result.getMessage());
        }
    }
}
```

## 11. 监控和运维

### 11.1 关键指标监控

```java
@Component
public class TagConvertMetrics {
    
    private final MeterRegistry meterRegistry;
    
    public TagConvertMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    /**
     * 记录任务提交
     */
    public void recordTaskSubmitted(String sourcePlatform, String targetPlatform) {
        meterRegistry.counter("tag.convert.task.submitted",
            "source", sourcePlatform,
            "target", targetPlatform).increment();
    }
    
    /**
     * 记录任务完成
     */
    public void recordTaskCompleted(String status, long duration) {
        meterRegistry.counter("tag.convert.task.completed",
            "status", status).increment();
            
        meterRegistry.timer("tag.convert.task.duration")
            .record(duration, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 记录用户打标
     */
    public void recordUserTagged(String platform, String status) {
        meterRegistry.counter("tag.convert.user.tagged",
            "platform", platform,
            "status", status).increment();
    }
    
    /**
     * 定时收集指标
     */
    @Scheduled(fixedRate = 60000)
    public void collectMetrics() {
        // 活跃任务数
        long activeTasks = tagConvertService.countActiveTasks();
        meterRegistry.gauge("tag.convert.active.tasks", activeTasks);
        
        // 今日处理用户数
        long todayProcessed = tagConvertService.countTodayProcessed();
        meterRegistry.gauge("tag.convert.today.processed", todayProcessed);
    }
}
```

### 11.2 健康检查

```java
@Component
public class TagConvertHealthIndicator implements HealthIndicator {
    
    @Autowired
    private TagConvertService tagConvertService;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Override
    public Health health() {
        try {
            // 检查数据库连接
            long taskCount = tagConvertService.countTotalTasks();
            
            // 检查Redis连接
            redisTemplate.opsForValue().get("health:check");
            
            // 检查线程池状态
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) 
                applicationContext.getBean("tagConvertExecutor");
            int activeCount = executor.getActiveCount();
            int poolSize = executor.getPoolSize();
            
            return Health.up()
                .withDetail("totalTasks", taskCount)
                .withDetail("activeThreads", activeCount)
                .withDetail("poolSize", poolSize)
                .build();
                
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

## 12. 部署方案

### 12.1 Docker部署

```dockerfile
# Dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app

COPY target/tag-convert-service.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  tag-convert-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=mysql
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=tripscrm
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 12.2 Kubernetes部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tag-convert-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tag-convert-service
  template:
    metadata:
      labels:
        app: tag-convert-service
    spec:
      containers:
      - name: tag-convert-service
        image: tag-convert-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: "mysql-service"
        - name: REDIS_HOST
          value: "redis-service"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: tag-convert-service
spec:
  selector:
    app: tag-convert-service
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

## 13. 方案对比

| 特性 | 复杂方案 | 简化方案 |
|------|----------|----------|
| **表结构** | 4张表，字段丰富 | 2张表，核心字段 |
| **分片策略** | 动态分片+消息队列 | 简单分批+线程池 |
| **故障恢复** | 心跳检测+自动恢复 | 定时检查+手动重试 |
| **监控告警** | 完整监控体系 | 基础进度跟踪 |
| **开发周期** | 2-3个月 | 2-3周 |
| **维护成本** | 较高 | 较低 |
| **扩展性** | 高 | 中等 |
| **性能** | 极高 | 中高 |
| **适用场景** | 大型企业，海量数据 | 中小型项目，快速上线 |

## 14. 实施计划

### 14.1 第一阶段（1周）
- 创建数据库表结构
- 实现基础的DO、DTO、Mapper
- 搭建项目基础框架

### 14.2 第二阶段（1周）
- 实现核心业务逻辑
- 实现平台适配器
- 实现异步处理机制

### 14.3 第三阶段（1周）
- 实现故障恢复机制
- 实现REST接口
- 添加监控和健康检查

### 14.4 第四阶段（1周）
- 单元测试和集成测试
- 性能测试和优化
- 部署和上线

## 15. 总结

本简化方案在保证核心功能的前提下，大幅降低了实现复杂度：

**优势：**
- 实现简单，易于理解和维护
- 开发周期短，可快速上线
- 支持基本的大数据量处理
- 具备故障恢复能力
- 支持多平台扩展

**适用场景：**
- 中小型项目
- 快速原型验证
- 数据量在千万级别以内
- 对实时性要求不是特别高的场景

通过这个简化方案，可以快速搭建起一套可用的标签转换打标系统，后续可以根据业务发展需要逐步优化和扩展。