package com.alibaba.tripscrm.dal.mapper.tddl;

import com.alibaba.tripscrm.dal.model.domain.data.TaskMessageTypeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Mapper
public interface TaskMessageTypeMapper {
    /**
     * 插入单条记录
     *
     * @param record
     * @return
     */
    int insert(TaskMessageTypeDO record);

    /**
     * upsert单条记录
     *
     * @param record
     * @return
     */
    int upsert(TaskMessageTypeDO record);

    /**
     * 根据主键查询
     *
     * @param id
     * @return
     */
    TaskMessageTypeDO selectByPrimaryKey(Long id);

    /**
     * 根据 taskType + name 查询
     *
     * @param taskType
     * @param name
     * @return
     */
    TaskMessageTypeDO selectByTaskTypeAndName(@Param("taskType") String taskType, @Param("name") String name);

    /**
     * 根据任务类型查询
     *
     * @param taskType 任务类型
     * @return List<TaskMessageTypeDO>
     */
    List<TaskMessageTypeDO> listByTaskType(String taskType);

    /**
     * 根据主键更新，不更新长文本字段
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(TaskMessageTypeDO record);

    /**
     * 根据主键删除
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);
}