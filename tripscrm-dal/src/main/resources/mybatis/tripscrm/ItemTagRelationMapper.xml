<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.tripscrm.dal.mapper.tddl.ItemTagRelationMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.tripscrm.dal.model.domain.data.ItemTagRelationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="tag_id" jdbcType="BIGINT" property="tagId" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="item_id" jdbcType="VARCHAR" property="itemId" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="sub_code" jdbcType="VARCHAR" property="subCode" />
    <result column="relation_id" jdbcType="VARCHAR" property="relationId" />
    <result column="index_key" jdbcType="VARCHAR" property="indexKey" />
  </resultMap>
  <sql id="Param_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Param_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="itemTagRelationParam.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, deleted, gmt_create, gmt_modified, tag_id, item_type, item_id, creator_id, creator_name,
    sub_code, relation_id, index_key
  </sql>
  <select id="selectByParam" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinctSql != null">
      distinct ${@com.alibaba.security.SecurityUtil@trimSql4OrderBy(distinctSql)}
    </if>
    <if test="distinctSql == null">
      <include refid="Base_Column_List" />
    </if>
    from item_tag_relation
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${@com.alibaba.security.SecurityUtil@trimSql4OrderBy(orderByClause)}
    </if>
    <if test="page">
      limit #{pageIndex},#{pageSize}
    </if>
  </select>
  <delete id="deleteByParam">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from item_tag_relation
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
    <if test="page">
      limit #{pageSize}
    </if>
  </delete>
  <insert id="insertSelective" parameterType="com.alibaba.tripscrm.dal.model.domain.data.ItemTagRelationDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into item_tag_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="subCode != null">
        sub_code,
      </if>
      <if test="relationId != null">
        relation_id,
      </if>
      <if test="indexKey != null">
        index_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        now(),
      </if>
      <if test="gmtModified != null">
        now(),
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=BIGINT},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="subCode != null">
        #{subCode,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="indexKey != null">
        #{indexKey,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="upsertSelective" parameterType="com.alibaba.tripscrm.dal.model.domain.data.ItemTagRelationDO">
    insert into item_tag_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="tagId != null">
        tag_id,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="subCode != null">
        sub_code,
      </if>
      <if test="relationId != null">
        relation_id,
      </if>
      <if test="indexKey != null">
        index_key,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        now(),
      </if>
      <if test="gmtModified != null">
        now(),
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=BIGINT},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="subCode != null">
        #{subCode,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=VARCHAR},
      </if>
      <if test="indexKey != null">
        #{indexKey,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix=" on duplicate key update " suffixOverrides=",">
        deleted = 0,
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByParam" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from item_tag_relation
    <if test="_parameter != null">
      <include refid="Param_Where_Clause" />
    </if>
  </select>
  <update id="updateByParamSelective">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update item_tag_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = now(),
      </if>
      <if test="record.gmtModified != null">
        gmt_modified = now(),
      </if>
      <if test="record.tagId != null">
        tag_id = #{record.tagId,jdbcType=BIGINT},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.subCode != null">
        sub_code = #{record.subCode,jdbcType=VARCHAR},
      </if>
      <if test="record.relationId != null">
        relation_id = #{record.relationId,jdbcType=VARCHAR},
      </if>
      <if test="record.indexKey != null">
        index_key = #{record.indexKey,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Param_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="com.alibaba.tripscrm.dal.model.domain.data.ItemTagRelationDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into item_tag_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,deleted,gmt_create,gmt_modified,tag_id,item_type,item_id,creator_id,creator_name,sub_code,relation_id,index_key,
    </trim>
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (
      <trim suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},#{item.deleted,jdbcType=TINYINT},now(),now(),#{item.tagId,jdbcType=BIGINT},#{item.itemType,jdbcType=INTEGER},#{item.itemId,jdbcType=VARCHAR},#{item.creatorId,jdbcType=VARCHAR},#{item.creatorName,jdbcType=VARCHAR},#{item.subCode,jdbcType=VARCHAR},#{item.relationId,jdbcType=VARCHAR},#{item.indexKey,jdbcType=VARCHAR},
      </trim>
      )
    </foreach>
  </insert>
</mapper>