<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.taobao</groupId>
        <artifactId>parent</artifactId>
        <version>2.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.alibaba.tripscrm</groupId>
    <artifactId>tripscrm</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>tripscrm</name>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mybatis-starter.version>2.1.0</mybatis-starter.version>
        <maven-antrun.version>1.8</maven-antrun.version>
        <spring-boot.version>2.5.12</spring-boot.version>
        <pandora-boot.version>2025-02-release-fix</pandora-boot.version>
        <pandora-boot-maven-plugin.version>10.0.7</pandora-boot-maven-plugin.version>
        <velocity.starter.version>1.0.4.RELEASE</velocity.starter.version>
        <knife4j-spring-boot-starter.version>4.3.0</knife4j-spring-boot-starter.version>
        <error-prone.version>1.4.2</error-prone.version>
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
        <jacoco-plugin.version>0.8.8</jacoco-plugin.version>
        <amdplatform.version>2.0.79</amdplatform.version>
        <category-hsf-client-starter.version>8.5.1</category-hsf-client-starter.version>
        <tripzoo-proxy.version>1.1.02-20251105-SNAPSHOT</tripzoo-proxy.version>
        <tripzoo-admin.version>1.0.5-2025-10-22</tripzoo-admin.version>
        <mmp.client.version>3.8.55</mmp.client.version>
        <hcp-client.version>2.0.7</hcp-client.version>
        <finance-member-client.version>2.3.2-20230718</finance-member-client.version>
        <travelsummary-service-common.version>1.1.43</travelsummary-service-common.version>
        <tripscrm-client.version>1.0.23</tripscrm-client.version>
        <pokemon.version>2.0.1</pokemon.version>
        <tbsession.version>4.0.30</tbsession.version>
        <buc.version>1.7.4-partitioned</buc.version>
        <mms-client.version>1.1.96</mms-client.version>
        <fai-client.version>1.1.55</fai-client.version>
    </properties>

    <modules>
        <module>tripscrm-client</module>
        <module>tripscrm-dal</module>
        <module>tripscrm-service</module>
        <module>tripscrm-start</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fliggy.fmp.mms</groupId>
                <artifactId>mms-client</artifactId>
                <version>${mms-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.hotel</groupId>
                <artifactId>hotel-seller-base-client</artifactId>
                <version>2.1.1</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-starter-bom</artifactId>
                <version>${pandora-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
                <version>${knife4j-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripscrm</groupId>
                <artifactId>tripscrm-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripscrm</groupId>
                <artifactId>tripscrm-dal</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripscrm</groupId>
                <artifactId>tripscrm-client</artifactId>
                <version>${tripscrm-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.platform.shared</groupId>
                <artifactId>buc.sso.client</artifactId>
                <version>${buc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>5.3.18</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>velocity-spring-boot-starter</artifactId>
                <version>${velocity.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>99.0-does-not-exist</version>
            </dependency>

            <dependency>
                <groupId>servlet-api</groupId>
                <artifactId>servlet-api</artifactId>
                <version>999-no-exist-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>999-not-exist-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>2.1.10</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>2.0.3</version>
            </dependency>

            <!--   Stream模式的包     -->
            <dependency>
                <groupId>com.dingtalk.open</groupId>
                <artifactId>app-stream-client</artifactId>
                <version>1.3.1</version>
            </dependency>

            <dependency>
                <artifactId>spring-jcl</artifactId>
                <groupId>org.springframework</groupId>
                <version>999-not-exist-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>1.7.26</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>
            <!--acl-->
            <dependency>
                <groupId>com.alibaba.platform.shared</groupId>
                <artifactId>acl.api</artifactId>
                <version>2.9.3</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.26</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.pokemon</groupId>
                <artifactId>pokemon-starter</artifactId>
                <version>${pokemon.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.pokemon</groupId>
                <artifactId>pokemon-lock-client</artifactId>
                <version>${pokemon.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.4.6</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.dt.op</groupId>
                <artifactId>pandora-boot-starter-onelog</artifactId>
                <version>4.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.normandy.credential</groupId>
                <artifactId>normandy-credential-spring-boot-starter</artifactId>
                <version>1.0.23</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.tair</groupId>
                <artifactId>tairjedis-sdk-singlepath</artifactId>
                <version>2.4.9</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.ihr</groupId>
                <artifactId>amdplatform-service-api</artifactId>
                <version>${amdplatform.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson2-extension</artifactId>
                        <groupId>com.alibaba.fastjson2</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>aopalliance</groupId>
                        <artifactId>aopalliance</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.intl.app</groupId>
                <artifactId>micro-front-api</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelitems-common</artifactId>
                <version>2.02.42</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.keycenter.keycenter-client</groupId>
                        <artifactId>common-keycenter-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.j2ee</groupId>
                        <artifactId>j2ee</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mortbay.jetty</groupId>
                        <artifactId>servlet-api-2.5</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP-java6</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish.web</groupId>
                        <artifactId>javax.el</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.el</groupId>
                        <artifactId>javax.el-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.inject</groupId>
                        <artifactId>javax.inject</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>maven-lombok-plugin</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.annotation</groupId>
                        <artifactId>jsr250-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.geronimo.specs</groupId>
                        <artifactId>geronimo-annotation_1.0_spec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>forest-domain</artifactId>
                        <groupId>com.taobao.forest</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>forest-common</artifactId>
                        <groupId>com.taobao.forest</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>forest-sqlite-jdbc</artifactId>
                        <groupId>com.taobao.forest</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.searchengine</groupId>
                        <artifactId>common-searchengine</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.trip.tripdivision</groupId>
                <artifactId>tripdivision-client</artifactId>
                <version>1.2.10</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.newmedia</groupId>
                <artifactId>tripnmwx-client</artifactId>
                <version>1.0.0-2025-09-25</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip.wireless</groupId>
                <artifactId>tripwmc-client</artifactId>
                <version>1.5.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>acl.api</artifactId>
                        <groupId>com.alibaba.platform.shared</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>lombok</artifactId>
                        <groupId>org.projectlombok</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>LightApi</artifactId>
                        <groupId>com.taobao.hsf</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mtop-common-service</artifactId>
                        <groupId>com.taobao.wireless</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>schedulerx-worker</artifactId>
                        <groupId>com.alibaba.schedulerx</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tripw-gendoc-annotaion</artifactId>
                        <groupId>com.taobao.trip.wireless</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tripw-sensible-if-client</artifactId>
                        <groupId>com.taobao.trip.wireless</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>unitrouter</artifactId>
                        <groupId>com.alibaba.unit.rule</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>maven-lombok-plugin</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.mmp</groupId>
                <artifactId>mmp-client-core</artifactId>
                <version>${mmp.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripzoo.proxy</groupId>
                <artifactId>tripzoo.proxy-client</artifactId>
                <version>${tripzoo-proxy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripzoo.admin</groupId>
                <artifactId>tripzoo-admin-client</artifactId>
                <version>${tripzoo-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.schedulerx</groupId>
                <artifactId>schedulerx2-spring-boot-starter</artifactId>
                <version>1.9.2</version>
                <!--如果用的是logback，需要把log4j和log4j2排掉 -->
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun.schedulerx</groupId>
                <artifactId>schedulerx-plugin-trace-eagleeye</artifactId>
                <version>1.0.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.2.11</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.2.11</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.wireless.mtop</groupId>
                <artifactId>mtop-pandora-export-agent</artifactId>
                <version>1.1.0</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.security</groupId>
                <artifactId>security-all</artifactId>
                <version>2.3.9</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.lindorm</groupId>
                <artifactId>lindorm-client</artifactId>
                <version>2.4.3</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>2.0.35</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.2.5.Final</version>
            </dependency>
            <dependency>
                <groupId>jakarta.persistence</groupId>
                <artifactId>jakarta.persistence-api</artifactId>
                <version>3.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trippc</groupId>
                <artifactId>trippc-client</artifactId>
                <version>1.8.6</version>
                <exclusions>
                    <exclusion>
                        <artifactId>lombok</artifactId>
                        <groupId>org.projectlombok</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- mtop spi for wx -->
            <dependency>
                <groupId>com.taobao.wireless</groupId>
                <artifactId>mtop-spi</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripzoo</groupId>
                <artifactId>tripzoo-common</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.csp</groupId>
                <artifactId>switchcenter</artifactId>
                <version>2.1.4</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.fliggyplaycore</groupId>
                <artifactId>fliggyplaycore-client</artifactId>
                <version>1.1.29</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>fliggy-award-upgrade-client</artifactId>
                <version>1.0.77</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>f-marketing-play-client</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.common.uic.uic-common</groupId>
                <artifactId>uic-client-starter</artifactId>
                <version>5.0.32-ttl-fix</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.ateye</groupId>
                <artifactId>ateye-client</artifactId>
                <version>2.4.1</version>
            </dependency>
            <!--sentinel 版本不得低于3.9.15-->
            <dependency>
                <groupId>com.taobao.csp</groupId>
                <artifactId>sentinel</artifactId>
                <version>3.9.34</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cglib-nodep</artifactId>
                        <groupId>cglib</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.csp</groupId>
                <artifactId>sentinel-annotation-aspectj</artifactId>
                <version>3.9.34</version>
            </dependency>
            <!--加密服务-->
            <dependency>
                <groupId>com.taobao.keycenter</groupId>
                <artifactId>keycenter-client</artifactId>
                <version>2.3.24</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.odps</groupId>
                <artifactId>odps-sdk-core</artifactId>
                <version>0.45.0</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip.tripgalaxy</groupId>
                <artifactId>tripgalaxy-client</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okhttp</groupId>
                        <artifactId>okhttp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.forest</groupId>
                        <artifactId>forest-client</artifactId>
                    </exclusion>
                </exclusions>
                <version>1.0.14</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>hcp-client</artifactId>
                <version>${hcp-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.payment</groupId>
                <artifactId>finance-member-client</artifactId>
                <version>${finance-member-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelsummary-service-common</artifactId>
                <version>${travelsummary-service-common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.travel</groupId>
                        <artifactId>travelitems-domain-item</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fliggy.fic</groupId>
                        <artifactId>fic-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.travel</groupId>
                        <artifactId>travelitems-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>aopalliance</artifactId>
                        <groupId>aopalliance</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fliggy.fic</groupId>
                <artifactId>fic-common</artifactId>
                <version>1.0.23</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy</groupId>
                <artifactId>furl-client</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-nop</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.opensearch</groupId>
                <artifactId>aliyun-sdk-opensearch</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.dataworks</groupId>
                <artifactId>data-service-client</artifactId>
                <version>1.1.4</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.14.3</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.work.alipmc</groupId>
                <artifactId>alipmc-api</artifactId>
                <version>3.3.6</version>
            </dependency>
            <!-- okHttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>4.10.0</version>
            </dependency>
            <!-- Retrofit 依赖项 -->
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>3.5.0</version>
            </dependency>
            <!-- Retrofit 的 Gson 转换器依赖项，用于将 JSON 转换为 Java 对象 -->
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-gson</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.cronutils</groupId>
                <artifactId>cron-utils</artifactId>
                <version>9.2.1</version>
            </dependency>
            <!-- 圈人 -->
            <dependency>
                <groupId>com.taobao.tripupf</groupId>
                <artifactId>tripupf-client</artifactId>
                <version>1.5.2</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy</groupId>
                <artifactId>crowd-client</artifactId>
                <version>1.3.1</version>
            </dependency>
            <!-- OSS -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>3.5.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>net.sf.json-lib</groupId>
                        <artifactId>json-lib</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.codehaus.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>2.4.7</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.triptower</groupId>
                <artifactId>triptower-all-client</artifactId>
                <version>1.0.30</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fliggy.trade</groupId>
                <artifactId>fliggy-trade-common-utils</artifactId>
                <version>1.2.1-USER-PRODUCT-20231024</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.trade</groupId>
                <artifactId>logger</artifactId>
                <version>1.2.2-fliggy</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelitems-client</artifactId>
                <version>2.02.31</version>
                <exclusions>
                    <exclusion>
                        <artifactId>categorycenter-share</artifactId>
                        <groupId>com.alibaba.catcenter</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip.hpc</groupId>
                <artifactId>hpc-client</artifactId>
                <version>1.5.29</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.fc</groupId>
                <artifactId>fc-open-api</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.trip.trippoi</groupId>
                <artifactId>fliggypoi-client</artifactId>
                <version>1.3.2</version>
            </dependency>
            <!-- excel导入-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.1.1</version>
            </dependency>
            <!-- 泰坦静态配置 -->
            <dependency>
                <groupId>com.fliggy.fceadmin</groupId>
                <artifactId>fceadmin-client-static-resource</artifactId>
                <version>1.0.6</version>
            </dependency>
            <!--cpp业务二方包-->
            <dependency>
                <groupId>com.taobao.contentplatform</groupId>
                <artifactId>content-api-fliggywx</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripc</groupId>
                <artifactId>tripcdest-facade</artifactId>
                <version>1.0.15</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.2-jre</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.tbsession</groupId>
                <artifactId>tbsession</artifactId>
                <version>${tbsession.version}</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.tbsession</groupId>
                <artifactId>tbsession-springboot-starter</artifactId>
                <version>${tbsession.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fliggy.ffa.touch</groupId>
                <artifactId>ffa-customize-touch-client</artifactId>
                <version>1.3.0</version>
            </dependency>

            <!-- 单元测试框架需要 start -->
            <dependency>
                <groupId>com.alibaba.film</groupId>
                <artifactId>film-component-speedup-pandora</artifactId>
                <version>2.1-SNAPSHOT</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-io</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- jetty包 -->
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-server</artifactId>
                <version>7.4.0.v20110414</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-io</artifactId>
                <version>7.4.0.v20110414</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-http</artifactId>
                <version>7.4.0.v20110414</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-continuation</artifactId>
                <version>7.4.0.v20110414</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-security</artifactId>
                <version>7.4.0.v20110414</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-servlet</artifactId>
                <version>7.4.0.v20110414</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-util</artifactId>
                <version>7.4.0.v20110414</version>
                <scope>test</scope>
            </dependency>
            <!-- 单元测试框架需要 end -->


            <dependency>
                <groupId>com.aliyun.quicka</groupId>
                <artifactId>quicka-tianquan-client</artifactId>
                <version>1.0.7</version>
                <exclusions>
                    <exclusion>
                        <artifactId>com.alibaba.databank.gaia</artifactId>
                        <groupId>gaia-backend-client</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>com.alibaba.fastvalidator</artifactId>
                        <groupId>fastvalidator-jsr</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripbaymaxbusi</groupId>
                <artifactId>tripbaymaxbusi-client</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.train</groupId>
                <artifactId>traindc-client</artifactId>
                <version>3.6.0-cache-optimize</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.train</groupId>
                <artifactId>trc-client</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.fliggy.traffic</groupId>
                <artifactId>cross-common</artifactId>
                <version>1.0.3-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.tccp</groupId>
                <artifactId>tccp-client</artifactId>
                <version>2.6.0</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>hts-client</artifactId>
                <version>2.1.9-eagleeye</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.diamond</groupId>
                        <artifactId>diamond-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.trip</groupId>
                <artifactId>tripjourneyop-service-facade</artifactId>
                <version>1.8.4</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf.app.spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.trip.wireless</groupId>
                        <artifactId>wdelivery-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.taobao.tccpos</groupId>
                <artifactId>tccpos-client</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.tripchannel</groupId>
                <artifactId>trip-channel-ai-client</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.travel</groupId>
                <artifactId>travelvc-common</artifactId>
                <version>1.3.43</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.trip.trippc2</groupId>
                <artifactId>trippc2-client</artifactId>
                <version>1.0.1</version>
            </dependency>

            <!--fai-->
            <dependency>
                <groupId>com.fliggy.fai</groupId>
                <artifactId>fai-client</artifactId>
                <version>${fai-client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.alibaba.maven.plugins</groupId>
                    <artifactId>error-prone-maven-plugin</artifactId>
                    <version>${error-prone.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>check</goal>
                                <goal>testCheck</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.taobao.pandora</groupId>
                    <artifactId>pandora-boot-maven-plugin</artifactId>
                    <version>${pandora-boot-maven-plugin.version}</version>
                </plugin>
                <!-- 测试用例扫描插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                    <configuration>
                        <!-- 这个argLine会报红，但不能删除，是jacoco插入代理用的占位符，对应jacoco插件里的propertyName-->
                        <!--suppress UnresolvedMavenProperty -->
                        <argLine>${argLine} -Dfile.encoding=UTF-8</argLine>
                    </configuration>
                </plugin>
                <!-- 测试覆盖率计算插件 -->
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-plugin.version}</version>
                    <configuration>
                        <!-- 对应maven-surefire-plugin的argLine里jacoco插件的占位符名，省略时默认为argLine -->
                        <propertyName>argLine</propertyName>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>default-report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                                <goal>report-aggregate</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>3.1.2</version>
                    <configuration>
                        <configLocation>google_checks.xml</configLocation>
                        <encoding>UTF-8</encoding>
                        <consoleOutput>true</consoleOutput>
                        <failsOnError>true</failsOnError>
                        <linkXRef>false</linkXRef>
                    </configuration>
                    <executions>
                        <execution>
                            <id>validate</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.alibaba.maven.plugins</groupId>
                <artifactId>error-prone-maven-plugin</artifactId>
            </plugin>
            <!-- 全局启用jacoco，子模块会继承父模块配置，如果只希望某些子模块启用，将父模块的删除，在特定子模块配置该项 -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
