<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.alibaba.tripscrm</groupId>
        <artifactId>tripscrm</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>tripscrm-start</artifactId>
    <packaging>jar</packaging>
    <name>tripscrm-start</name>

    <dependencies>
        <dependency>
            <groupId>com.fliggy.hotel</groupId>
            <artifactId>hotel-seller-base-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripscrm</groupId>
            <artifactId>tripscrm-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-compress</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.pandora</groupId>
            <artifactId>taobao-hsf.sar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>velocity-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-sentinel-spring-boot-starter</artifactId>
        </dependency>
        <!-- 鹰眼 -->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-eagleeye-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.business.qa</groupId>
            <artifactId>biz-simulator-sdk</artifactId>
            <version>3.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.pandora</groupId>
            <artifactId>pandora-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 单元测试框架需要 start -->
        <dependency>
            <groupId>com.alibaba.film</groupId>
            <artifactId>film-component-speedup-pandora</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- jetty包 -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-io</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-http</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-continuation</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-security</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-util</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 单元测试框架需要 end -->

        <!-- 因为uic、switchcenter、sentinel强依赖log4j，所以要排掉log4j-over-slf4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
            <version>999-not-exist-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.dt.op</groupId>
            <artifactId>pandora-boot-starter-onelog</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.ateye</groupId>
            <artifactId>ateye-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.csp</groupId>
            <artifactId>sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fliggy.trade</groupId>
            <artifactId>fliggy-trade-common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.trade</groupId>
            <artifactId>logger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.tripc</groupId>
            <artifactId>tripcdest-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!-- 泰坦静态配置 -->
        <dependency>
            <groupId>com.fliggy.fceadmin</groupId>
            <artifactId>fceadmin-client-static-resource</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 解压fat jar到target/${project-name}目录 -->
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <unzip
                                        src="${project.build.directory}/${project.build.finalName}.${project.packaging}"
                                        dest="${project.build.directory}/tripscrm"/>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
