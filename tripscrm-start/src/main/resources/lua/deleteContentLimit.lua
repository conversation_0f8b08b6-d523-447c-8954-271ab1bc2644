-- 删除限流
local dayKey = KEYS[1]
local hourKey = KEYS[2]
local minuteKey = KEYS[3]
local subConfigSize = tonumber(ARGV[1])

redis.call('DEL', dayKey)
redis.call('DEL', hourKey)
redis.call('DEL', minuteKey)

for i = 1, subConfigSize do
    local keyIndex = i*3

    local subDayKey = KEYS[keyIndex+1]
    local subHourKey = KEYS[keyIndex+2]
    local subMinuteKey = KEYS[keyIndex+3]

    redis.call('DEL', subDayKey)
    redis.call('DEL', subHourKey)
    redis.call('DEL', subMinuteKey)
end

return "1"