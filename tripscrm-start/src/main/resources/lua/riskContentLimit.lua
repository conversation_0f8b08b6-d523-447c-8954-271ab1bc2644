-- 限流控制-丢弃式限流-固定窗口

-- 批量回滚key
local function returnToken(keys, acquireToken)
    for i, key in ipairs(keys) do
        if key and key ~= "" then
        -- 判断Redis中是否存在key，存在则增加值
            if redis.call('EXISTS', key) == 1 then
                redis.call('INCRBY', key, acquireToken)
            end
        end
    end
end

-- 获取token
local function acquireToken(key, initValue, acquireToken, expireTime)
    -- 判断initValue是否为空，如果为空则直接返回1
    if not initValue or initValue == '' then
        return 1
    end
    -- 判断Redis中是否存在key，如果不存在则初始化
    if redis.call('EXISTS', key) == 0 then
        redis.call('SET', key, initValue, 'EX', expireTime)
    end
    -- 减少key的值，返回新的值
    local current = redis.call('DECRBY', key, acquireToken)
    -- 如果剩余的tokens（即当前key的值）不小于0，则返回1，否则返回0
    if current >= 0 then return 1 end return 0
end

local dayKey = KEYS[1]
local hourKey = KEYS[2]
local minuteKey = KEYS[3]

local subConfigSize = tonumber(ARGV[1])
local dayInit = tonumber(ARGV[2])
local hourInit = tonumber(ARGV[3])
local minuteInit = tonumber(ARGV[4])
local hit = tonumber(ARGV[5])
local returnKeyArray = {}

local dayResult = acquireToken(dayKey, dayInit, hit, 60 * 60 * 24)
table.insert(returnKeyArray, dayKey)
if dayResult == 0 then
    -- 回滚天级token
    returnToken(returnKeyArray, hit)
    return "0|day|"..dayKey
end


local hourResult = acquireToken(hourKey, hourInit, hit, 60 * 60)
table.insert(returnKeyArray, hourKey)
if hourResult == 0 then
    -- 回滚天级、小时级token
    returnToken(returnKeyArray, hit)
    return "0|hour|"..hourKey
end

local minuteResult = acquireToken(minuteKey, minuteInit, hit, 60)
table.insert(returnKeyArray, minuteKey)
if minuteResult == 0 then
    -- 回滚天级、小时级、分钟级token
    returnToken(returnKeyArray, hit)
    return "0|minute|"..minuteKey
end

for i = 1, subConfigSize do
    local keyIndex = i*3
    local argIndex = i*4+1

    local subDayKey = KEYS[keyIndex+1]
    local subHourKey = KEYS[keyIndex+2]
    local subMinuteKey = KEYS[keyIndex+3]

    local subDayInit = tonumber(ARGV[argIndex+1])
    local subHourInit = tonumber(ARGV[argIndex+2])
    local subMinuteInit = tonumber(ARGV[argIndex+3])
    local subHit = tonumber(ARGV[argIndex+4])

    local subDayResult = acquireToken(subDayKey, subDayInit, subHit, 60 * 60 * 24)
    table.insert(returnKeyArray, subDayKey)
    if subDayResult == 0 then
        -- 回滚其他token
        returnToken(returnKeyArray, hit)
        return "0|day|"..subDayKey
    end

    local subHourResult = acquireToken(subHourKey, subHourInit, subHit, 60 * 60)
    table.insert(returnKeyArray, subHourKey)
    if subHourResult == 0 then
         -- 回滚其他token
         returnToken(returnKeyArray, hit)
        return "0|hour|"..subHourKey
    end

    local subMinuteResult = acquireToken(subMinuteKey, subMinuteInit, subHit, 60)
    table.insert(returnKeyArray, subMinuteKey)
    if subMinuteResult == 0 then
        -- 回滚其他token
        returnToken(returnKeyArray, hit)
        return "0|minute|"..subMinuteKey
    end
end

-- 没有限流
return "1"