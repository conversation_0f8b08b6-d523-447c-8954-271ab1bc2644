# ç¯å¢å±æ§éç½®å¨<PERSON>mond;
spring.diamond.data-id=com.alibaba.tripscrm.profileconfig
# ä»¥ä¸ä¸ºä¸éçç¯å¢ååãå¿é¡»åå¸æè½è°æ´çå±æ§
project.name=tripscrm
# httpæå¡å¨ç«¯å£
server.port=7001
# endpointéç½®
management.server.port=7002
management.endpoints.web.exposure.include=caches
# tddléç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tddl
spring.tddl.app=TRIPSCRM_APP
spring.tddl.sharding=true
# mybatis
mybatis.config-location=classpath:/mybatis/mybatis-config.xml
mybatis.mapper-locations=classpath:mybatis/tripscrm/*Mapper.xml
# sentinel web filteréç½®
spring.sentinel.filter.urlPatterns=*.htm
# Velocityéç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-velocity
spring.velocity.resource-loader-path=classpath:/velocity/templates
spring.velocity.toolbox-config-location=/velocity/toolbox.xml
spring.velocity.layout-url=/velocity/layout/default.vm
spring.velocity.tools-base-packages=com.alibaba.tripscrm.start.utils.velocity
server.error.whitelabel.enabled=false
# tair
spring.tair.user-names.ldbTair=c7ba8e0d894d4269
spring.tair.user-names.mdbTair=5348dcf6319d43dd
spring.tair.timeout=10000
# knife4j
knife4j.enable=true
knife4j.setting.language=zh-CN
# metaQ-consumer
#spring.metaq.enabled=false
# tair-lock-manager
spring.pokemon.tair.lock.enabled=true
spring.pokemon.tair.lock.username=c7ba8e0d894d4269
spring.pokemon.tair.lock.namespace=1870
spring.pokemon.tair.lock.default-lock-expire-seconds=10
# tbsession filteréç½®
tbsession.filter.enabled=true
tbsession.filter.name=sessionFilter
tbsession.filter.url-patterns=/*
tbsession.filter.session-config-group=online_new
tbsession.filter.filter-intercept-url-excludes=/tbsession,/favicon.ico,/checkpreload.htm,/status.taobao,/agent.ateye
tbsession.filter.tbpass-open=true
tbsession.filter.tbpass-domains=.alibaba-inc.com;.fliggy.com
tbsession.authorization.enabled=true
tbsession.authorization.exclude-paths=/checkpreload.htm,/status.taobao,/agent.ateye
tbsession.authorization.login-url=https://login.taobao.com/member/login.jhtml?sub=true&redirectURL=
tbsession.authorization.interceptor-class=com.alibaba.tripscrm.start.filter.AuthFilter
# Tccp-Trade-Result Topicçæ¶æ¯å¤çé»è¾ï¼è¯¦è§https://yuque.antfin-inc.com/fplat/nrompq/zy0v2v
# æ­£åæ¶æ¯ãæ¯ä»è®¢åã
spring.notify.subscribers[0].beanName=orderNotifyMessageSubscriber
spring.notify.subscribers[0].group-id=S-tripscrm-event
spring.notify.subscribers[0].messageListener=orderNotifyConsumer
#åç´æ æ´æ°æ¶æ¯
spring.notify.subscribers[0].bindings[0].topic=Tccp-Trade
spring.notify.subscribers[0].bindings[0].key=UPDATE_VERTICAL_ATTRIBUTE
#éåº è®¢ååå»ºæä¿äº¤æ
spring.notify.subscribers[0].bindings[1].topic=Tccp-Trade-Result
spring.notify.subscribers[0].bindings[1].key=HOTEL_CLOSE_ORDER
#éåº éåºè®¢ååå»ºæä¿äº¤ææ¶æ¯
spring.notify.subscribers[0].bindings[2].topic=Tccp-Trade-Result
spring.notify.subscribers[0].bindings[2].key=HOTEL_ENABLE_ORDER
#äº¤ææå
spring.notify.subscribers[0].bindings[3].topic=Tccp-Trade
spring.notify.subscribers[0].bindings[3].key=MULTI_RESOURCE_ORDER_CONFIRM_SUCC