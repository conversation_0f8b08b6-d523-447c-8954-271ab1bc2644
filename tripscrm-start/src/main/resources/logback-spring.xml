<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v1.5.13.RELEASE/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <property name="APP_NAME" value="tripscrm"/>
    <property name="LOG_PATH" value="${user.home}/${APP_NAME}/logs"/>
    <property name="LOG_FILE" value="${LOG_PATH}/application.log"/>
    <property name="TT_CUSTOMER_ACQUISITION_LINK_ADD_INFO_FILE"
              value="${LOG_PATH}/tt/wechat_customer_acquisition_link_add_info/wechat_customer_acquisition_link_add_info.log"/>
    <property name="TT_MOBILE_USER_BIND_FILE" value="${LOG_PATH}/tt/mobile_user_bind.log"/>
    <property name="TT_CUSTOMER_ACQUISITION_LINK_INFO_FILE"
              value="${LOG_PATH}/tt/wechat_customer_acquisition_link_info/wechat_customer_acquisition_link_info.log"/>
    <property name="TT_TASK_AB_FILE" value="${LOG_PATH}/tt/task_ab/task_ab.log"/>
    <property name="TT_TASK_EXECUTE_FILE" value="${LOG_PATH}/tt/task_execute/task_execute.log"/>
    <property name="TT_CUSTOMER_ACQUISITION_LINK_INFO_FILE" value="${LOG_PATH}/tt/wechat_customer_acquisition_link_info/wechat_customer_acquisition_link_info.log"/>
    <property name="TT_EVENT_TRACK_FILE" value = "${LOG_PATH}/tt/event_track/event_track.log"/>
    <!-- 新版任务上线后迁移完成，删掉原有任务日志文件配置 -->
    <property name="TASK_CORE_LOG_FILE" value="${LOG_PATH}/tripscrm_task_core.log"/>
    <property name="ODPS_LOG_FILE" value="${LOG_PATH}/tripscrm_odps.log"/>
    <property name="TASK_LOG_FILE" value="${LOG_PATH}/tripscrm_task.log"/>
    <property name="MONITOR_LOG_FILE" value="${LOG_PATH}/monitor/monitor.log"/>
    <property name="TASK_MONITOR_LOG_FILE" value="${LOG_PATH}/monitor/task_monitor.log"/>
    <!-- 聚合聊天日志 -->
    <property name="FUSION_CHAT_LOG_FILE" value="${LOG_PATH}/fusion_chat.log"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoder的默认实现类是ch.qos.logback.classic.encoder.PatternLayoutEncoder -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{5} - %msg%n</pattern>
        </encoder>
    </appender>
    <appender name="APPLICATION"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- MonitorLogger日志 -->
    <appender name="MonitorLogger" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%X{EAGLEEYE_TRACE_ID}|%X{EAGLEEYE_RPC_ID}|%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <file>${MONITOR_LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${MONITOR_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- TaskMonitorLogger日志 -->
    <appender name="TaskMonitorLogger" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%X{EAGLEEYE_TRACE_ID}|%X{EAGLEEYE_RPC_ID}|%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <file>${TASK_MONITOR_LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TASK_MONITOR_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="TRACE_LOGGER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_TRACE_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_TRACE_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_TRACE_LOGGER" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="TRACE_LOGGER"/>
    </appender>

    <appender name="TT_MOBILE_USER_BIND_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TT_MOBILE_USER_BIND_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TT_MOBILE_USER_BIND_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="TT_CUSTOMER_ACQUISITION_LINK_INFO_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TT_CUSTOMER_ACQUISITION_LINK_INFO_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TT_CUSTOMER_ACQUISITION_LINK_INFO_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="TT_CUSTOMER_ACQUISITION_LINK_ADD_INFO_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TT_CUSTOMER_ACQUISITION_LINK_ADD_INFO_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TT_CUSTOMER_ACQUISITION_LINK_ADD_INFO_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="TT_TASK_AB_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TT_TASK_AB_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TT_TASK_AB_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_TT_TASK_AB_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="TT_TASK_AB_LOG"/>
    </appender>

    <appender name="TT_TASK_EXECUTE_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TT_TASK_EXECUTE_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TT_TASK_EXECUTE_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_TT_TASK_EXECUTE_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="TT_TASK_EXECUTE_LOG"/>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_TT_CUSTOMER_ACQUISITION_LINK_ADD_INFO_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="TT_CUSTOMER_ACQUISITION_LINK_ADD_INFO_LOG"/>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_TT_CUSTOMER_ACQUISITION_LINK_INFO_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="TT_CUSTOMER_ACQUISITION_LINK_INFO_LOG"/>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_TT_MOBILE_USER_BIND_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="TT_MOBILE_USER_BIND_LOG"/>
    </appender>

    <appender name="TASK_CORE_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TASK_CORE_LOG_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TASK_CORE_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_TASK_CORE_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="TASK_CORE_LOG"/>
    </appender>

    <appender name="ODPS_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${ODPS_LOG_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${ODPS_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_ODPS_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="ODPS_LOG"/>
    </appender>

    <appender name="FUSION_CHAT_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${FUSION_CHAT_LOG_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${FUSION_CHAT_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!--异步appender -->
    <appender name="ASYNC_FUSION_CHAT_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <!--内部实现是一个有界ArrayBlockingQueue,queueSize是队列大小。该值会影响性能.默认值为256-->
        <queueSize>512</queueSize>
        <!--当队列的剩余容量小于这个阈值并且当前日志level TRACE, DEBUG or INFO，则丢弃这些日志。默认为queueSize大小的20%。-->
        <discardingThreshold>0</discardingThreshold>
        <!--neverBlock=true则写日志队列时候会调用阻塞队列的offer方法而不是put，如果队列满则直接返回，而不是阻塞，即日志被丢弃。-->
        <neverBlock>true</neverBlock>
        <!--实际负责写日志的appender,最多只能添加一个-->
        <appender-ref ref="FUSION_CHAT_LOG"/>
    </appender>

    <!--【新】任务核心链路监控日志-->
    <appender name="TASK_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TASK_LOG_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TASK_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="TT_EVENT_TRACK_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TT_EVENT_TRACK_FILE}</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}|%p|%logger{2}|%X{EAGLEEYE_TRACE_ID}|%m%n</pattern>
            <charset class="java.nio.charset.Charset">utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TT_EVENT_TRACK_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC_TT_EVENT_TRACK_LOG" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <neverBlock>true</neverBlock>
        <appender-ref ref="TT_EVENT_TRACK_LOG"/>
    </appender>

    <logger name="traceLogger" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_TRACE_LOGGER"/>
    </logger>

    <!--任务AB实验埋点日志-->
    <logger name="TtTaskAbLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_TT_TASK_AB_LOG"/>
    </logger>

    <!--任务AB实验埋点日志-->
    <logger name="TtTaskExecuteLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_TT_TASK_EXECUTE_LOG"/>
    </logger>

    <!--获客助手添加数据统计-->
    <logger name="TtCustomerAcquisitionLinkAddInfoLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_TT_CUSTOMER_ACQUISITION_LINK_ADD_INFO_LOG"/>
    </logger>

    <!--获客助手链接数据统计-->
    <logger name="TtCustomerAcquisitionLinkInfoLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_TT_CUSTOMER_ACQUISITION_LINK_INFO_LOG"/>
    </logger>

    <!--手机号用户绑定关系日志-->
    <logger name="TtMobileUserBindLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_TT_MOBILE_USER_BIND_LOG"/>
    </logger>

    <!--任务核心链路监控日志-->
    <logger name="TaskCoreLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_TASK_CORE_LOG"/>
    </logger>

    <!--任务核心链路监控日志-->
    <logger name="odpsLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_ODPS_LOG"/>
    </logger>

    <!--聚合聊天监控日志-->
    <logger name="FusionChatLog" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_FUSION_CHAT_LOG"/>
    </logger>

    <logger name="MonitorLogger" level="INFO" additivity="false">
        <!-- <appender-ref ref="console" /> -->
        <appender-ref ref="MonitorLogger"/>
    </logger>

    <logger name="TaskMonitorLogger" level="INFO" additivity="false">
        <!-- <appender-ref ref="console" /> -->
        <appender-ref ref="TaskMonitorLogger"/>
    </logger>

    <logger name="TtEventTrackLogger" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_TT_EVENT_TRACK_LOG"/>
    </logger>


    <!--【新】任务核心链路监控日志-->
    <logger name="TaskLog" level="INFO" additivity="false">
        <appender-ref ref="TASK_LOG"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="APPLICATION"/>
<!--        <appender-ref ref="STDOUT"/>-->
    </root>

</configuration>