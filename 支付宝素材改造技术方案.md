## 支付宝素材改造技术方案

## 1. 项目概述

### 1.1 背景

为支持支付宝群发消息和定向触达功能，需要对现有SCRM系统的素材管理模块进行扩展，新增支付宝相关的素材模版类型，并建立统一的素材物料管理体系。

### 1.2 目标

- 支持支付宝群发消息（优惠券）
- 支持支付宝定向触达（优惠券推荐、红包推荐、活动推荐）
- 对现有素材代码做改造，支持素材可以让支付宝的消息接口发送
- 新增素材物料数据库表，用于存放素材中会需要选的一些物料
- 实现支付宝优惠券数据同步到素材物料数据库表中
- 提供完整的物料增删改查功能，支持可用时间范围、名字等查询
- 新增图片上传到支付宝端的接口，实时返回图片id，不需要缓存

### 1.3 技术方案原则

- **复用优先**：基础消息类型（文本、图片、红包、小程序、图文）复用现有MessageBO结构，通过扩展字段支持支付宝平台
- **统一输出**：所有转换器统一输出MessageBO对象，保持架构一致性
- **平台适配**：通过平台标识和字段扩展区分微信和支付宝的差异

## 2. 现有素材架构分析

### 2.1 核心组件

**素材管理核心组件：**

- `MaterialTemplateService`：素材模版管理服务，负责模版的增删改查
- `MaterialTransferService`：素材转换服务，将素材内容转换为消息对象
- `MaterialService`：素材业务服务，提供素材的核心业务逻辑
- `MaterialContentConvertService`：素材内容转换服务，处理动态参数和变量替换

**转换器体系：**

- `AbstractMaterialConverter`：抽象素材转换器基类
- `MaterialConvertFactory`：素材转换器工厂，管理各种转换器
- 具体转换器：`TextMaterialConverter`、`ImageMaterialConverter`、`MiniProgramMaterialConverter`等

### 2.2 现有支持的消息类型

- TEXT：文本消息
- PICTURE：图片消息
- VIDEO：视频消息
- MINI_PROGRAM：小程序卡片
- NEWS：图文消息

### 2.3 需要做映射的消息类型

根据支付宝平台要求，需要支持以下消息类型的映射：

1. **文本消息** - 支持纯文本内容发送
2. **图片消息** - 支持图片上传和发送
3. **红包** - 支持红包推荐和发送
4. **小程序卡片** - 支持小程序跳转
5. **图文(链接)** - 支持图文链接消息

### 2.4 现有消息发送流程

- 微信企业号消息发送
- 群聊消息发送（`GroupChatTaskExecutor`）
- 私聊消息发送（`SingleChatTaskExecutor`）
- 自动回复消息（`AutoResponseTaskExecutor`）

### 2.5 新增支付宝素材模版类型

#### 2.5.1 支付宝群发消息

- **优惠券** - 支持单券和券包两种模式的群发消息

#### 2.5.2 支付宝定向触达

- **优惠券推荐** - 基于人群定向推荐优惠券
- **红包推荐** - 基于用户定向推荐红包
- **活动推荐** - 基于人群定向推荐营销活动

## 3. 功能改动点分析

### 3.1 核心改动点

#### 3.1.1 素材模版类型扩展

**改动点：** 新增支付宝相关的素材模版类型

- 支付宝群发消息 - 优惠券
- 支付宝定向触达 - 优惠券推荐
- 支付宝定向触达 - 红包推荐
- 支付宝定向触达 - 活动推荐

**影响范围：** 素材模版管理、素材创建流程、素材转换逻辑

#### 3.1.2 消息类型映射扩展

**改动点：** 支持5种消息类型的支付宝平台映射

- 文本消息 → 支付宝文本消息
- 图片消息 → 支付宝图片消息（需图片上传）
- 红包 → 支付宝红包推荐
- 小程序卡片 → 支付宝小程序跳转
- 图文(链接) → 支付宝图文链接消息

**影响范围：** 消息转换器、消息发送逻辑、平台适配层

#### 3.1.3 现有素材代码改造

**改动点：** 扩展现有转换器和执行器支持支付宝

- MessageBO扩展平台标识和扩展字段
- 现有转换器增加平台判断逻辑
- TaskExecutor增加支付宝消息发送分支

**影响范围：** 核心转换逻辑、任务执行器、消息发送流程

#### 3.1.4 素材物料管理

**改动点：** 新增统一的物料管理体系

- 新增物料数据库表
- 物料增删改查功能
- 支付宝优惠券数据同步
- 多维度查询支持

**影响范围：** 数据存储层、业务服务层、接口层

#### 3.1.5 支付宝图片上传

**改动点：** 新增实时图片上传功能

- 实时上传到支付宝平台
- 直接返回图片ID
- 不使用缓存机制

**影响范围：** 媒体服务、图片处理流程

### 3.2 字段映射方案

#### 3.2.1 MessageBO扩展字段设计

**现有字段保持不变：**

- msgType：消息类型
- msgContent：消息内容
- msgNum：消息序号
- title：标题
- desc：描述
- href：链接地址
- voiceTime：语音时长
- atLocation：@位置
- at：是否@

**新增扩展字段：**

- platformType：平台类型标识（wechat/alipay）
- extFields：扩展字段Map，存储平台特有数据

#### 3.2.2 支付宝群发优惠券消息映射

**字段映射关系：**

| 支付宝字段                     | MessageBO字段                            | 映射说明                        |
|---------------------------|----------------------------------------|-----------------------------|
| multi_coupon              | extFields["multi_coupon"]              | 是否为券包，存储在扩展字段中              |
| title                     | title                                  | 群消息标题，直接映射到MessageBO.title  |
| image_id                  | extFields["image_id"]                  | 支付宝图片ID，存储在扩展字段中            |
| activity_id               | extFields["activity_id"]               | 单券活动ID，存储在扩展字段中             |
| activity_id_list          | extFields["activity_id_list"]          | 券包活动ID列表，存储在扩展字段中           |
| long_introduce_text       | desc                                   | 长文本介绍，映射到MessageBO.desc     |
| short_introduce_text_list | extFields["short_introduce_text_list"] | 短文本列表，存储在扩展字段中              |
| -                         | msgType                                | 固定设置为MessageTypeEnum.COUPON |
| -                         | platformType                           | 固定设置为"alipay"               |
| -                         | msgContent                             | 构建基础消息内容JSON字符串             |

#### 3.2.3 支付宝定向优惠券推荐消息映射

**字段映射关系：**

| 支付宝字段            | MessageBO字段                   | 映射说明                         |
|------------------|-------------------------------|------------------------------|
| crowd_code       | extFields["crowd_code"]       | 人群code，存储在扩展字段中              |
| recommend_text   | msgContent                    | 推荐内容，映射到MessageBO.msgContent |
| multi_coupon     | extFields["multi_coupon"]     | 是否为券包，存储在扩展字段中               |
| activity_id      | extFields["activity_id"]      | 单券活动ID，存储在扩展字段中              |
| activity_id_list | extFields["activity_id_list"] | 券包活动ID列表，存储在扩展字段中            |
| -                | msgType                       | 固定设置为MessageTypeEnum.COUPON  |
| -                | platformType                  | 固定设置为"alipay"                |

#### 3.2.4 支付宝红包推荐消息映射

**字段映射关系：**

| 支付宝字段           | MessageBO字段                                      | 映射说明                            |
|-----------------|--------------------------------------------------|---------------------------------|
| recommend_text  | msgContent                                       | 推荐内容，映射到MessageBO.msgContent    |
| red_packet_list | extFields["red_packet_list"]                     | 红包列表，存储在扩展字段中                   |
| red_packet_id   | extFields["red_packet_list"][i]["red_packet_id"] | 红包ID，存储在红包列表中                   |
| visible_users   | extFields["red_packet_list"][i]["visible_users"] | 可见用户，存储在红包列表中                   |
| -               | msgType                                          | 固定设置为MessageTypeEnum.RED_PACKET |
| -               | platformType                                     | 固定设置为"alipay"                   |

#### 3.2.5 支付宝活动推荐消息映射

**字段映射关系：**

| 支付宝字段          | MessageBO字段                                  | 映射说明                          |
|----------------|----------------------------------------------|-------------------------------|
| recommend_text | msgContent                                   | 推荐内容，映射到MessageBO.msgContent  |
| crowd_code     | extFields["crowd_code"]                      | 人群code，存储在扩展字段中               |
| activity_list  | extFields["activity_list"]                   | 活动列表，存储在扩展字段中                 |
| title          | extFields["activity_list"][i]["title"]       | 活动标题，存储在活动列表中                 |
| desc           | extFields["activity_list"][i]["desc"]        | 活动描述，存储在活动列表中                 |
| image_id       | extFields["activity_list"][i]["image_id"]    | 图片ID，存储在活动列表中                 |
| action_name    | extFields["activity_list"][i]["action_name"] | 行动点名称，存储在活动列表中                |
| action_url     | extFields["activity_list"][i]["action_url"]  | 跳转地址，存储在活动列表中                 |
| -              | msgType                                      | 固定设置为MessageTypeEnum.ACTIVITY |
| -              | platformType                                 | 固定设置为"alipay"                 |

#### 3.2.6 基础消息类型扩展映射

**文本消息映射：**

| 支付宝字段        | MessageBO字段  | 映射说明                           |
|--------------|--------------|--------------------------------|
| text_content | msgContent   | 文本内容，直接映射到MessageBO.msgContent |
| -            | msgType      | 固定设置为MessageTypeEnum.TEXT      |
| -            | platformType | 固定设置为"alipay"                  |

**图片消息映射：**

| 支付宝字段        | MessageBO字段           | 映射说明                         |
|--------------|-----------------------|------------------------------|
| image_id     | extFields["image_id"] | 支付宝图片ID，通过实时上传获取             |
| original_url | href                  | 原始图片URL，映射到MessageBO.href    |
| -            | msgType               | 固定设置为MessageTypeEnum.PICTURE |
| -            | platformType          | 固定设置为"alipay"                |

**小程序消息映射：**

| 支付宝字段     | MessageBO字段            | 映射说明                              |
|-----------|------------------------|-----------------------------------|
| app_id    | extFields["app_id"]    | 支付宝小程序ID，存储在扩展字段中                 |
| page_path | extFields["page_path"] | 小程序页面路径，存储在扩展字段中                  |
| title     | title                  | 小程序标题，映射到MessageBO.title          |
| desc      | desc                   | 小程序描述，映射到MessageBO.desc           |
| thumb_url | href                   | 缩略图URL，映射到MessageBO.href          |
| -         | msgType                | 固定设置为MessageTypeEnum.MINI_PROGRAM |
| -         | platformType           | 固定设置为"alipay"                     |

**图文链接消息映射：**

| 支付宝字段    | MessageBO字段           | 映射说明                      |
|----------|-----------------------|---------------------------|
| title    | title                 | 图文标题，映射到MessageBO.title   |
| desc     | desc                  | 图文描述，映射到MessageBO.desc    |
| image_id | extFields["image_id"] | 图文封面图片ID                  |
| link_url | href                  | 跳转链接，映射到MessageBO.href    |
| -        | msgType               | 固定设置为MessageTypeEnum.NEWS |
| -        | platformType          | 固定设置为"alipay"             |

## 4. 改动方式和步骤

### 4.1 改动方式

#### 4.1.1 向后兼容的扩展方式

- **保持现有结构不变**：MessageBO原有字段保持不变，通过新增字段扩展
- **渐进式改造**：现有转换器逻辑保持不变，仅增加平台判断分支
- **统一输出格式**：所有转换器统一输出MessageBO，通过适配器处理平台差异

#### 4.1.2 分层改造策略

- **数据层**：新增物料表，扩展MessageBO结构
- **服务层**：扩展转换器，新增支付宝专用服务
- **接口层**：新增支付宝相关接口，扩展现有接口
- **执行层**：改造TaskExecutor，增加平台分发逻辑

#### 4.1.3 平台适配模式

- **转换器层面**：通过平台标识区分处理逻辑
- **适配器层面**：MessageBO到支付宝格式的转换适配
- **发送层面**：根据平台类型选择不同的发送通道

### 4.2 改动步骤

#### 第一阶段：基础设施准备（3人天）

**改动内容：**

1. **数据库设计**
    - 设计material_supply物料表结构
    - 创建表和索引
    - 设计物料类型枚举

2. **MessageBO扩展**
    - 增加platformType字段
    - 增加extFields扩展字段Map
    - 保持原有字段不变

3. **枚举扩展**
    - MaterialSupplyTypeEnum增加支付宝类型
    - MaterialTemplateTypeConstant增加支付宝模版常量
    - MessageTypeEnum增加支付宝消息类型（如需要）

#### 第二阶段：转换器改造（6人天）

**改动内容：**

1. **现有转换器扩展**
    - TextMaterialConverter增加平台判断逻辑
    - ImageMaterialConverter增加支付宝图片处理
    - MiniProgramMaterialConverter增加支付宝小程序处理
    - NewsMaterialConverter增加支付宝图文处理

2. **新增支付宝专用转换器**
    - AlipayGroupCouponConverter（群发优惠券）
    - AlipayTargetedCouponConverter（定向优惠券推荐）
    - AlipayRedPacketConverter（红包推荐）
    - AlipayActivityConverter（活动推荐）

3. **转换器工厂扩展**
    - MaterialConvertFactory注册新转换器
    - 支持根据平台类型选择转换器

#### 第三阶段：物料管理功能（5人天）

**改动内容：**

1. **物料管理服务**
    - MaterialSupplyService（增删改查）
    - 支持按时间范围查询
    - 支持按名称模糊查询
    - 支持多条件组合查询

2. **支付宝数据同步**
    - AlipaySupplySyncService（优惠券同步）
    - 定时同步任务
    - 手动同步接口
    - 同步状态管理

3. **物料管理接口**
    - 物料CRUD接口
    - 查询接口（分页、条件查询）
    - 同步管理接口

#### 第四阶段：支付宝服务集成（4人天）

**改动内容：**

1. **图片上传服务**
    - AlipayMediaService（实时上传，不缓存）
    - 支持单张和批量上传
    - 直接返回支付宝图片ID

2. **消息发送适配**
    - AlipayMessageAdapter（MessageBO到支付宝格式转换）
    - 支持不同消息类型的适配
    - 处理扩展字段的提取和映射

3. **支付宝API集成**
    - 图片上传API对接
    - 消息发送API对接
    - 优惠券查询API对接

#### 第五阶段：执行器改造（3人天）

**改动内容：**

1. **TaskExecutor扩展**
    - GroupChatTaskExecutor增加支付宝分支
    - SingleChatTaskExecutor增加支付宝分支
    - 根据MessageBO的platformType选择发送通道

2. **MaterialTransferService扩展**
    - 增加平台类型判断逻辑
    - 支持支付宝素材转换
    - 保持现有微信逻辑不变

3. **消息发送流程整合**
    - 统一的消息发送入口
    - 平台路由逻辑
    - 错误处理和重试机制

#### 第六阶段：测试和优化（3人天）

**改动内容：**

1. **功能测试**
    - 支付宝消息发送测试
    - 物料管理功能测试
    - 图片上传功能测试

2. **兼容性测试**
    - 确保现有微信功能不受影响
    - 数据迁移测试
    - 性能影响评估

3. **集成测试**
    - 端到端流程测试
    - 异常场景测试
    - 压力测试

**总工作量：25人天**

### 4.3 关键改动文件清单

#### 4.3.1 需要修改的现有文件

1. **MessageBO** - 扩展platformType和extFields字段
2. **MaterialSupplyTypeEnum** - 添加支付宝物料类型
3. **MaterialTemplateTypeConstant** - 添加支付宝模版常量
4. **TextMaterialConverter** - 添加支付宝平台支持
5. **ImageMaterialConverter** - 添加支付宝图片处理
6. **MiniProgramMaterialConverter** - 添加支付宝小程序支持
7. **NewsMaterialConverter** - 添加支付宝图文支持
8. **MaterialConvertFactory** - 注册支付宝转换器
9. **MaterialTransferService** - 添加平台判断逻辑
10. **GroupChatTaskExecutor** - 添加支付宝发送分支
11. **SingleChatTaskExecutor** - 添加支付宝发送分支

#### 4.3.2 需要新增的文件

1. **material_supply.sql** - 物料表创建脚本
2. **AlipayGroupCouponConverter** - 群发优惠券转换器
3. **AlipayTargetedCouponConverter** - 定向优惠券转换器
4. **AlipayRedPacketConverter** - 红包推荐转换器
5. **AlipayActivityConverter** - 活动推荐转换器
6. **MaterialSupplyService** - 物料管理服务
7. **AlipaySupplySyncService** - 支付宝数据同步服务
8. **AlipayMediaService** - 支付宝图片上传服务
9. **AlipayMessageAdapter** - 消息格式适配器
10. **MaterialSupplyController** - 物料管理接口
11. **AlipayMediaController** - 图片上传接口
12. **AlipaySupplySyncController** - 数据同步接口

### 4.4 改造后UML时序图

#### 4.4.1 支付宝素材转换流程（复用MessageBO）

```plantuml
@startuml 支付宝素材转换流程
participant "TaskExecutor" as Executor
participant "MaterialTransferService" as Transfer
participant "MaterialConvertFactory" as Factory
participant "AlipayMaterialConverter" as AlipayConverter
participant "MessageBO" as Message
participant "AlipayMessageAdapter" as Adapter
participant "AlipayAPI" as API

Executor -> Transfer: buildMessages(materialDO, context)
Transfer -> Transfer: 判断平台类型为支付宝
Transfer -> Factory: getMaterialContentConverter(type)
Factory -> AlipayConverter: 返回支付宝转换器
Transfer -> AlipayConverter: buildMessageBO(content, context)
AlipayConverter -> AlipayConverter: 处理支付宝特有字段
note right: 设置platformType=alipay\n填充extFields支付宝字段
AlipayConverter -> Message: 构建统一MessageBO
Message -> AlipayConverter: 返回MessageBO对象
AlipayConverter -> Transfer: 返回转换结果
Transfer -> Executor: 返回MessageBO列表
Executor -> Adapter: 适配支付宝消息格式
Adapter -> API: 调用支付宝消息接口
API -> Executor: 返回发送结果
@enduml
```

#### 4.4.2 支付宝图片上传流程（实时返回，不缓存）

```plantuml
@startuml 支付宝图片上传流程
participant "用户" as User
participant "AlipayMediaController" as Controller
participant "AlipayMediaService" as Service
participant "Cache" as Cache
participant "AlipayAPI" as API

User -> Controller: 上传图片请求
Controller -> Service: uploadImageAndGetId(imageUrl)
Service -> Service: 下载原图
Service -> API: 调用支付宝图片上传接口
API -> Service: 返回支付宝imageId
Service -> Controller: 返回imageId
Controller -> User: 返回上传结果
@enduml
```

#### 4.4.3 URL转换流程

```plantuml
@startuml URL转换流程
participant "用户" as User
participant "UrlConvertController" as Controller
participant "UrlConvertService" as Service
participant "HsfGatewayService" as HSF
participant "AliyunFC" as FC

User -> Controller: URL转换请求
Controller -> Service: convertUrl(pageType, url, params)
Service -> Service: 根据pageType判断转换类型
alt pageType = miniapp
    Service -> HSF: 调用小程序路径转换
    HSF -> FC: 调用阿里云FC API
    FC -> HSF: 返回转换结果
    HSF -> Service: 返回小程序路径
else pageType = webview/h5
    Service -> HSF: 调用H5链接转换
    HSF -> FC: 调用阿里云FC API
    FC -> HSF: 返回转换结果
    HSF -> Service: 返回H5链接
end
Service -> Service: 处理页面参数
Service -> Controller: 返回转换结果
Controller -> User: 返回转换后的URL
@enduml
```

    Cache -> Service: 返回缓存的短链

else 缓存未命中
Service -> API: 调用支付宝短链生成接口
API -> Service: 返回短链URL
Service -> Cache: 缓存短链结果
end
Service -> Controller: 返回短链URL
Controller -> User: 返回转换结果
@enduml

```

#### 4.4.4 支付宝优惠券同步流程

```plantuml
@startuml 支付宝优惠券同步流程
participant "定时任务" as Scheduler
participant "AlipaySupplySyncService" as SyncService
participant "AlipayAPI" as API
participant "MaterialSupplyService" as MaterialService
participant "Database" as DB

Scheduler -> SyncService: 触发定时同步
SyncService -> SyncService: 获取待同步券列表
loop 遍历优惠券
    SyncService -> API: 查询优惠券信息
    API -> SyncService: 返回券详情
    SyncService -> SyncService: 转换为内部格式
    SyncService -> MaterialService: 创建物料记录
    MaterialService -> DB: 保存物料信息
    SyncService -> DB: 记录同步状态
end
SyncService -> Scheduler: 返回同步结果
@enduml
```

#### 4.4.5 支付宝群发消息流程（统一MessageBO）

```plantuml
@startuml 支付宝群发消息流程
participant "用户" as User
participant "TaskExecutor" as Executor
participant "MaterialTransferService" as Transfer
participant "AlipayGroupCouponConverter" as Converter
participant "MessageBO" as Message
participant "AlipayMessageAdapter" as Adapter
participant "AlipayAPI" as API

User -> Executor: 触发群发任务
Executor -> Executor: 获取素材信息
Executor -> Transfer: buildMessages(materialDO)
Transfer -> Converter: 转换群发优惠券素材
Converter -> Converter: 处理券包逻辑
note right: 设置platformType=alipay\n根据multi_coupon设置对应字段
Converter -> Message: 构建统一MessageBO
Message -> Transfer: 返回MessageBO
Transfer -> Executor: 返回转换结果
Executor -> Adapter: 适配支付宝消息格式
note right: 从MessageBO.extFields提取\n支付宝特有字段
Adapter -> API: 调用支付宝群发接口
API -> Adapter: 返回发送结果
Adapter -> Executor: 返回执行结果
Executor -> User: 返回任务执行状态
@enduml
```

#### 4.4.6 支付宝定向触达流程（统一MessageBO）

```plantuml
@startuml 支付宝定向触达流程
participant "用户" as User
participant "TaskExecutor" as Executor
participant "MaterialTransferService" as Transfer
participant "AlipayTargetedConverter" as Converter
participant "MessageBO" as Message
participant "AlipayMessageAdapter" as Adapter
participant "AlipayAPI" as API

User -> Executor: 触发定向触达任务
Executor -> Executor: 获取素材信息
Executor -> Transfer: buildMessages(materialDO)
Transfer -> Converter: 转换定向触达素材
alt 优惠券推荐
    Converter -> Converter: 设置crowd_code和activity_id
else 红包推荐
    Converter -> Converter: 设置red_packet_list
else 活动推荐
    Converter -> Converter: 设置activity_list
end
Converter -> Message: 构建统一MessageBO
note right: platformType=alipay\nextFields包含支付宝特有字段
Message -> Transfer: 返回MessageBO
Transfer -> Executor: 返回转换结果
Executor -> Adapter: 适配支付宝消息格式
Adapter -> API: 调用支付宝定向触达接口
API -> Adapter: 返回发送结果
Adapter -> Executor: 返回执行结果
Executor -> User: 返回任务执行状态
@enduml
```

## 5. 数据库设计（简化版）

### 5.1 素材物料主表（唯一数据表）

```sql
CREATE TABLE `material_supply`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `supply_code`      varchar(64)  NOT NULL COMMENT '物料编码，全局唯一',
    `supply_name`      varchar(256) NOT NULL COMMENT '物料名称',
    `supply_type`      varchar(32)  NOT NULL COMMENT '物料类型：COUPON-优惠券，RED_PACKET-红包，ACTIVITY-活动',
    `platform_type`    tinyint(4) NOT NULL DEFAULT 2 COMMENT '平台类型：1-微信，2-支付宝，3-通用',
    `status`           tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    `valid_start_time` datetime              DEFAULT NULL COMMENT '有效开始时间',
    `valid_end_time`   datetime              DEFAULT NULL COMMENT '有效结束时间',
    `supply_content`   text COMMENT '物料内容JSON',
    `supply_config`    text COMMENT '物料配置JSON',
    `image_url`        varchar(512)          DEFAULT NULL COMMENT '物料图片URL',
    `alipay_image_id`  varchar(128)          DEFAULT NULL COMMENT '支付宝图片ID',
    `jump_url`         varchar(512)          DEFAULT NULL COMMENT '跳转链接',
    `short_url`        varchar(256)          DEFAULT NULL COMMENT '短链接',
    `description`      varchar(1024)         DEFAULT NULL COMMENT '物料描述',
    `tags`             varchar(256)          DEFAULT NULL COMMENT '标签，逗号分隔',
    `sort_order`       int(11) DEFAULT 0 COMMENT '排序权重',
    `creator`          varchar(64)           DEFAULT NULL COMMENT '创建人',
    `space_id`         bigint(20) DEFAULT NULL COMMENT '业务空间ID',
    `ext_info`         text COMMENT '扩展信息JSON',
    `gmt_create`       datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `deleted`          tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_supply_code` (`supply_code`),
    KEY                `idx_supply_type` (`supply_type`),
    KEY                `idx_platform_type` (`platform_type`),
    KEY                `idx_status` (`status`),
    KEY                `idx_valid_time` (`valid_start_time`, `valid_end_time`),
    KEY                `idx_space_id` (`space_id`),
    KEY                `idx_name` (`supply_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='素材物料表';
```

### 5.2 物料类型说明

**通过supply_type字段区分不同物料类型：**

#### 5.2.1 优惠券类型 (supply_type = 'COUPON')

```json
{
  "supply_content": {
    "activityId": "2024061000826004663530144391",
    "couponName": "满100减20优惠券",
    "faceValue": 20.00,
    "threshold": 100.00,
    "useScope": "全场通用",
    "validStartTime": "2024-01-01 00:00:00",
    "validEndTime": "2024-12-31 23:59:59"
  },
  "supply_config": {
    "multiCoupon": false,
    "longIntroduceText": "先领券再下单，满100立减20",
    "shortIntroduceTextList": [
      "先领券再下单",
      "满100立减20"
    ]
  }
}
```

#### 5.2.2 红包类型 (supply_type = 'RED_PACKET')

```json
{
  "supply_content": {
    "redPacketId": "276351723763834",
    "redPacketName": "新年红包",
    "amount": 10.00,
    "totalCount": 100,
    "receivedCount": 0,
    "validStartTime": "2024-01-01 00:00:00",
    "validEndTime": "2024-12-31 23:59:59"
  },
  "supply_config": {
    "visibleUsers": "23546457567",
    "sendRule": "先到先得"
  }
}
```

#### 5.2.3 活动类型 (supply_type = 'ACTIVITY')

```json
{
  "supply_content": {
    "activityId": "ACT_123456",
    "activityName": "春节大促",
    "activityDesc": "全场5折起",
    "validStartTime": "2024-01-01 00:00:00",
    "validEndTime": "2024-12-31 23:59:59"
  },
  "supply_config": {
    "actionName": "去查看",
    "actionUrl": "alipays://platformapi/startapp?appId=xxx&url=xxx",
    "crowdCode": "2296000000000000001"
  }
}
```

### 5.3 数据存储示例

**优惠券记录示例：**

```sql
INSERT INTO material_supply (supply_code, supply_name, supply_type, platform_type, status,
                             valid_start_time, valid_end_time, supply_content, supply_config,
                             image_url, alipay_image_id, jump_url, short_url, description,
                             creator, space_id)
VALUES ('COUPON_2024_001', '满100减20优惠券', 'COUPON', 2, 1,
        '2024-01-01 00:00:00', '2024-12-31 23:59:59',
        '{"activityId":"2024061000826004663530144391","couponName":"满100减20优惠券","faceValue":20.00,"threshold":100.00}',
        '{"multiCoupon":false,"longIntroduceText":"先领券再下单，满100立减20"}',
        'https://example.com/coupon.jpg', 'A*_ICJQIOnyT8AAAAAAAAAAAAADl54AQ',
        'https://example.com/coupon/detail', 'https://ur.alipay.com/abc123',
        '满100元可使用，立减20元', 'admin', 123);
```

**红包记录示例：**

```sql
INSERT INTO material_supply (supply_code, supply_name, supply_type, platform_type, status,
                             valid_start_time, valid_end_time, supply_content, supply_config,
                             description, creator, space_id)
VALUES ('RED_PACKET_2024_001', '新年红包', 'RED_PACKET', 2, 1,
        '2024-01-01 00:00:00', '2024-12-31 23:59:59',
        '{"redPacketId":"276351723763834","redPacketName":"新年红包","amount":10.00,"totalCount":100}',
        '{"visibleUsers":"23546457567","sendRule":"先到先得"}',
        '新年红包，10元随机金额', 'admin', 123);
```

## 6. 具体改动点分析

### 6.1 枚举扩展

#### 6.1.1 MaterialSupplyTypeEnum扩展

**MaterialSupplyTypeEnum扩展：**

- ALIPAY_COUPON("alipayCoupon", "支付宝优惠券")
- ALIPAY_RED_PACKET("alipayRedPacket", "支付宝红包")
- ALIPAY_ACTIVITY("alipayActivity", "支付宝活动")

#### 6.1.2 MaterialTemplateTypeConstant扩展

```java
// 支付宝群发消息 - 优惠券
public static final String ALIPAY_GROUP_COUPON = "alipay_group_coupon";

// 支付宝定向触达
public static final String ALIPAY_TARGETED_COUPON_RECOMMEND = "alipay_targeted_coupon_recommend";
public static final String ALIPAY_TARGETED_RED_PACKET_RECOMMEND = "alipay_targeted_red_packet_recommend";
public static final String ALIPAY_TARGETED_ACTIVITY_RECOMMEND = "alipay_targeted_activity_recommend";
```

### 6.2 数据模型新增

#### 6.2.1 支付宝群发优惠券内容模型

```java

@Data
public class AlipayGroupCouponContent {
    private Boolean multiCoupon;                    // 是否为券包
    private String title;                          // 群消息标题
    private String imageId;                        // 图片ID
    private String activityId;                     // 活动ID（单券）
    private String longIntroduceText;              // 长文本介绍
    private List<String> shortIntroduceTextList;   // 短文本介绍列表
    private List<String> activityIdList;          // 活动ID列表（券包）
}
```

#### 6.2.2 支付宝定向优惠券推荐内容模型

```java

@Data
public class AlipayTargetedCouponContent {
    private String crowdCode;                      // 人群code
    private String recommendText;                  // 推荐内容
    private Boolean multiCoupon;                   // 是否为券包
    private String activityId;                     // 活动ID（单券）
    private List<String> activityIdList;          // 活动ID列表（券包）
}
```

#### 6.2.3 支付宝红包推荐内容模型

```java

@Data
public class AlipayRedPacketRecommendContent {
    private String recommendText;                  // 推荐内容
    private List<AssistantRedPacketVO> redPacketList; // 红包列表
}

@Data
public class AssistantRedPacketVO {
    private String redPacketId;                    // 红包ID
    private String visibleUsers;                   // 可见用户
}
```

#### 6.2.4 支付宝活动推荐内容模型

```java

@Data
public class AlipayActivityRecommendContent {
    private String recommendText;                  // 推荐内容
    private List<AssistantActivityVO> activityList; // 活动列表
    private String crowdCode;                      // 人群code
}

@Data
public class AssistantActivityVO {
    private String title;                          // 活动标题
    private String desc;                           // 活动描述
    private String imageId;                        // 图片ID
    private String actionName;                     // 行动点名称
    private String actionUrl;                      // 跳转地址
}
```

### 6.3 转换器实现

#### 6.3.1 支付宝群发优惠券转换器

```java

@Component
public class AlipayGroupCouponMaterialConverter extends AbstractMaterialConverter<AlipayGroupCouponContent> {

    @Override
    public MaterialContentConvertResult convert(MaterialContentConvertContext context) {
        AlipayGroupCouponContent content = parseContent(context.getOriginContent());

        // 构建支付宝群发消息
        JSONObject messageContent = new JSONObject();
        messageContent.put("multi_coupon", content.getMultiCoupon());
        messageContent.put("title", content.getTitle());
        messageContent.put("image_id", content.getImageId());
        messageContent.put("long_introduce_text", content.getLongIntroduceText());
        messageContent.put("short_introduce_text_list", content.getShortIntroduceTextList());

        if (content.getMultiCoupon()) {
            messageContent.put("activity_id_list", content.getActivityIdList());
        } else {
            messageContent.put("activity_id", content.getActivityId());
        }

        return buildConvertResult(messageContent.toJSONString(), MessageTypeEnum.COUPON);
    }

    @Override
    public String getSupplyType() {
        return MaterialSupplyTypeEnum.ALIPAY_COUPON.getType();
    }
}
```

#### 6.3.2 支付宝定向优惠券推荐转换器

```java

@Component
public class AlipayTargetedCouponMaterialConverter extends AbstractMaterialConverter<AlipayTargetedCouponContent> {

    @Override
    public MaterialContentConvertResult convert(MaterialContentConvertContext context) {
        AlipayTargetedCouponContent content = parseContent(context.getOriginContent());

        JSONObject messageContent = new JSONObject();
        messageContent.put("crowd_code", content.getCrowdCode());
        messageContent.put("recommend_text", content.getRecommendText());
        messageContent.put("multi_coupon", content.getMultiCoupon());

        if (content.getMultiCoupon()) {
            messageContent.put("activity_id_list", content.getActivityIdList());
        } else {
            messageContent.put("activity_id", content.getActivityId());
        }

        return buildConvertResult(messageContent.toJSONString(), MessageTypeEnum.COUPON);
    }
}
```

### 6.4 服务层扩展

#### 6.4.1 素材物料管理服务接口

```java
public interface MaterialSupplyService {
    Long createSupply(MaterialSupplyCreateRequest request);

    Boolean updateSupply(MaterialSupplyUpdateRequest request);

    Boolean deleteSupply(Long supplyId);

    MaterialSupplyVO getSupplyById(Long supplyId);

    PageResult<MaterialSupplyVO> pageQuerySupply(MaterialSupplyPageQuery query);

    List<MaterialSupplyVO> queryByTimeRange(Date startTime, Date endTime);

    List<MaterialSupplyVO> queryByName(String name);
}
```

#### 6.4.2 支付宝图片上传服务

```java

@Service
public class AlipayMediaServiceImpl implements AlipayMediaService {

    @Override
    public String uploadImageAndGetId(String imageUrl) {
        // 1. 检查缓存
        String cachedImageId = getCachedImageId(imageUrl);
        if (StringUtils.isNotBlank(cachedImageId)) {
            return cachedImageId;
        }

        // 2. 下载图片并上传到支付宝
        String imageId = doUploadToAlipay(imageUrl);

        // 3. 缓存结果
        if (StringUtils.isNotBlank(imageId)) {
            cacheImageId(imageUrl, imageId);
        }

        return imageId;
    }

    private String doUploadToAlipay(String imageUrl) {
        // 调用支付宝开放平台图片上传API
        AlipayOpenFileUploadRequest request = new AlipayOpenFileUploadRequest();
        request.setBizCode("MARKETING_MATERIAL");

        FileItem fileItem = new FileItem(imageUrl.substring(imageUrl.lastIndexOf("/") + 1),
                downloadImage(imageUrl), "image/jpeg");
        request.setFileContent(fileItem);

        try {
            AlipayOpenFileUploadResponse response = alipayClient.execute(request);
            if (response.isSuccess()) {
                return response.getFileId();
            } else {
                throw new RuntimeException("支付宝图片上传失败: " + response.getSubMsg());
            }
        } catch (AlipayApiException e) {
            throw new RuntimeException("支付宝图片上传异常", e);
        }
    }
}
```

#### 6.4.3 支付宝优惠券同步服务

```java

@Service
public class AlipaySupplySyncServiceImpl implements AlipaySupplySyncService {

    @Override
    public Boolean syncAlipayCoupon(String activityId) {
        try {
            // 1. 调用支付宝API查询优惠券信息
            AlipayMarketingActivityOrdervoucherQueryRequest request =
                    new AlipayMarketingActivityOrdervoucherQueryRequest();
            request.setActivityId(activityId);

            AlipayMarketingActivityOrdervoucherQueryResponse response =
                    alipayClient.execute(request);

            if (!response.isSuccess()) {
                throw new RuntimeException("查询支付宝优惠券失败: " + response.getSubMsg());
            }

            // 2. 转换为内部物料格式
            MaterialSupplyCreateRequest createRequest = convertToMaterialSupply(response);

            // 3. 保存到物料表
            Long supplyId = materialSupplyService.createSupply(createRequest);

            // 4. 记录同步状态
            recordSyncResult(activityId, supplyId, true, null);

            return true;
        } catch (Exception e) {
            recordSyncResult(activityId, null, false, e.getMessage());
            return false;
        }
    }

    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    @Override
    public void scheduledSyncAlipayCoupon() {
        // 定时同步逻辑
        List<String> pendingActivityIds = getPendingActivityIds();
        for (String activityId : pendingActivityIds) {
            syncAlipayCoupon(activityId);
        }
    }
}
```

### 6.5 MaterialTransferService改造

```java
// 在 MaterialTransferServiceImpl 中新增方法
public List<MessageBO> convertForAlipay(MaterailInfoDO materailInfoDO,
                                        MaterialTrackRelationDTO materialTrackRelationDTO,
                                        MaterialContentConvertContext context) {
    // 解析素材内容
    JSONObject jsonContent = JSONObject.parseObject(materailInfoDO.getContent());
    JSONArray messageList = jsonContent.getJSONArray(MaterialJsonKeyConstant.messageList);

    List<MessageBO> messageBOList = new ArrayList<>();

    for (int i = 0; i < messageList.size(); i++) {
        JSONObject messageObj = messageList.getJSONObject(i);
        String messageType = messageObj.getString(MaterialJsonKeyConstant.messageType);

        // 根据消息类型选择对应的支付宝转换器
        AbstractMaterialConverter<?> converter = materialConvertFactory
                .getMaterialContentConverter(MaterialContentTypeEnum.codeOf(messageType));

        if (converter != null) {
            MaterialContentConvertResult result = converter.convert(context);
            MessageBO messageBO = new MessageBO();
            messageBO.setMsgType(result.getMessageType());
            messageBO.setMsgContent(result.getContent());
            messageBOList.add(messageBO);
        }
    }

    return messageBOList;
}
```

### 6.6 支付宝消息发送适配器

```java

@Component
public class AlipayMessageSendAdapter {

    /**
     * 发送支付宝群发消息
     */
    public ResultDO<String> sendGroupMessage(AlipayGroupMessageRequest request) {
        // 调用支付宝群发消息接口
        // 处理不同的消息类型：文本、图片、红包、小程序、图文
        return null;
    }

    /**
     * 发送支付宝定向消息
     */
    public ResultDO<String> sendTargetedMessage(AlipayTargetedMessageRequest request) {
        // 调用支付宝定向消息接口
        return null;
    }
}
```

## 7. 接口设计

### 7.1 物料管理接口

#### 7.1.1 创建物料

- **接口路径**: `POST /material/supply/create`
- **功能说明**: 创建新的素材物料
- **请求参数**:

```json
{
  "supplyName": "满100减20优惠券",
  "supplyType": "COUPON",
  "platformType": 2,
  "validStartTime": "2024-01-01 00:00:00",
  "validEndTime": "2024-12-31 23:59:59",
  "supplyContent": "{\"activityId\":\"2024061000826004663530144391\",\"faceValue\":20,\"threshold\":100}",
  "imageUrl": "https://example.com/coupon.jpg",
  "description": "满100元可使用，立减20元",
  "creator": "admin",
  "spaceId": 123
}
```

- **返回结果**: 物料ID和创建状态

#### 7.1.2 查询物料

- **接口路径**: `POST /material/supply/page`
- **功能说明**: 分页查询物料列表，支持可用时间范围、名字等查询条件
- **请求参数**:

```json
{
  "supplyName": "优惠券",
  "supplyType": "COUPON",
  "platformType": 2,
  "status": 1,
  "validStartTime": "2024-01-01 00:00:00",
  "validEndTime": "2024-12-31 23:59:59",
  "pageNum": 1,
  "pageSize": 20
}
```

- **返回结果**: 分页数据和物料详情

#### 7.1.3 按时间范围查询物料

- **接口路径**: `POST /material/supply/queryByTimeRange`
- **功能说明**: 根据可用时间范围查询物料
- **请求参数**:

```json
{
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59",
  "supplyType": "COUPON"
}
```

#### 7.1.4 按名称查询物料

- **接口路径**: `POST /material/supply/queryByName`
- **功能说明**: 根据物料名称模糊查询
- **请求参数**:

```json
{
  "name": "优惠券",
  "supplyType": "COUPON"
}
```

#### 7.1.5 更新物料

- **接口路径**: `POST /material/supply/update`
- **功能说明**: 更新物料信息

#### 7.1.6 删除物料

- **接口路径**: `POST /material/supply/delete`
- **功能说明**: 删除物料（逻辑删除）

### 7.2 支付宝图片上传接口（实时返回，不缓存）

#### 7.2.1 单张图片上传

- **接口路径**: `POST /alipay/media/upload/image`
- **功能说明**: 实时上传图片到支付宝平台，直接返回图片ID，不进行缓存
- **请求参数**:

```json
{
  "imageUrl": "https://example.com/image.jpg"
}
```

- **返回结果**:

```json
{
  "success": true,
  "data": "A*_ICJQIOnyT8AAAAAAAAAAAAADl54AQ",
  "errorCode": null,
  "errorMsg": null
}
```

#### 7.2.2 批量图片上传

- **接口路径**: `POST /alipay/media/upload/batch`
- **功能说明**: 批量实时上传图片到支付宝平台
- **请求参数**:

```json
{
  "imageUrls": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ]
}
```

- **返回结果**: URL与图片ID的映射关系

### 7.3 支付宝优惠券同步接口

#### 7.3.1 同步单个优惠券

- **接口路径**: `POST /alipay/supply/sync/coupon`
- **功能说明**: 同步支付宝优惠券信息
- **请求参数**:

```json
{
  "activityId": "2024061000826004663530144391"
}
```

- **返回结果**: 同步状态和结果

### 7.4 URL转换接口

#### 7.4.1 URL转换

- **接口路径**: `POST /url/convert`
- **功能说明**: 统一URL转换，支持多种页面类型转换
- **依赖配置**:

```xml
<dependency>
    <groupId>com.aliyun.fc</groupId>
    <artifactId>fc-open-api</artifactId>
    <version>1.0.0</version>
</dependency>
```

- **HSF服务**: `com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0`

- **请求参数**:

```json
{
  "pageType": "miniapp",
  "url": "/pages/index/index",
  "params": "id=123&name=test"
}
```

**参数说明：**

| 参数名      | 类型     | 必填 | 说明     | 枚举值                                      |
|----------|--------|----|--------|------------------------------------------|
| pageType | string | 是  | 通投页面类型 | miniapp(小程序页), webview(套壳H5页), h5(通用H5页) |
| url      | string | 是  | 页面地址   | pageType为miniapp时填写小程序Path，其他情况填写H5链接    |
| params   | string | 否  | 页面参数   | 相关参数将会带到页面                               |

- **返回结果**:

```json
{
  "success": true,
  "data": {
    "convertedUrl": "alipays://platformapi/startapp?appId=xxx&page=/pages/index/index&query=id%3D123%26name%3Dtest",
    "pageType": "miniapp",
    "originalUrl": "/pages/index/index",
    "params": "id=123&name=test"
  },
  "errorCode": null,
  "errorMsg": null
}
```

#### 7.4.2 批量URL转换

- **接口路径**: `POST /url/convert/batch`
- **功能说明**: 批量URL转换
- **请求参数**:

```json
{
  "urls": [
    {
      "pageType": "miniapp",
      "url": "/pages/index/index",
      "params": "id=123"
    },
    {
      "pageType": "h5",
      "url": "https://example.com/page",
      "params": "name=test"
    }
  ]
}
```

- **返回结果**: URL转换结果列表

## 6. 技术实现设计

### 6.1 依赖配置

#### 6.1.1 Maven依赖

```xml
<!-- 阿里云FC开放API -->
<dependency>
    <groupId>com.aliyun.fc</groupId>
    <artifactId>fc-open-api</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### 6.1.2 HSF服务配置

**URL转换服务：**

- 服务接口：`com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0`
- 功能：提供统一的URL转换能力
- 支持：小程序页、套壳H5页、通用H5页转换

### 6.2 URL转换服务设计

#### 6.2.1 页面类型配置

**页面类型枚举：**

```json
{
  "properties": {
    "pageType": {
      "title": "通投页面",
      "type": "string",
      "required": true,
      "default": "",
      "enum": [
        "miniapp",
        "webview", 
        "h5"
      ],
      "enumNames": [
        "小程序页",
        "套壳H5页",
        "通用H5页"
      ]
    },
    "url": {
      "title": "{{ formData.pageType == 'miniapp' ? '小程序Path' : 'H5链接'}}",
      "type": "string"
    },
    "params": {
      "title": "页面参数",
      "type": "string",
      "tooltip": "相关参数将会带到页面"
    }
  }
}
```

#### 6.2.2 转换逻辑设计

**转换规则：**

1. **小程序页 (miniapp)**：将小程序路径转换为支付宝小程序跳转链接
2. **套壳H5页 (webview)**：将H5链接包装为支付宝内嵌webview链接
3. **通用H5页 (h5)**：将普通H5链接转换为支付宝兼容的链接格式

**参数处理：**

- 支持动态参数传递
- 自动进行URL编码
- 参数合并和去重

### 6.3 服务集成架构

#### 6.3.1 服务调用链路

```
UrlConvertController → UrlConvertService → HsfGatewayService → AliyunFC API
```

#### 6.3.2 错误处理机制

- **超时处理**：设置合理的超时时间
- **重试机制**：支持失败重试
- **降级策略**：服务不可用时的降级处理
- **监控告警**：异常情况的监控和告警

## 8. 支付宝素材模版类型详细设计

### 8.1 支付宝群发消息 - 优惠券

**字段定义：**

- `multi_coupon`：是否为券包（boolean，必选）
- `title`：群消息标题（string，1-15字符，可选）
- `image_id`：图片ID（string，1-512字符，可选）
- `activity_id`：活动ID（string，1-512字符，可选）
- `long_introduce_text`：长文本介绍（string，1-35字符，可选）
- `short_introduce_text_list`：短文本介绍列表（array，1-3个元素，每个1-11字符，可选）
- `activity_id_list`：活动ID列表（array，0-5个元素，每个1-64字符，可选）

**业务逻辑：**

- 当`multi_coupon=true`时，使用`activity_id_list`字段
- 当`multi_coupon=false`时，使用`activity_id`字段

### 8.2 支付宝定向触达 - 优惠券推荐

**字段定义：**

- `crowd_code`：人群code（string，1-20字符，必选）
- `recommend_text`：推荐内容（string，1-36字符，必选）
- `multi_coupon`：是否为券包（boolean，必选）
- `activity_id`：活动ID（string，1-512字符，可选）
- `activity_id_list`：活动ID列表（array，0-5个元素，可选）

### 8.3 支付宝定向触达 - 红包推荐

**字段定义：**

- `recommend_text`：推荐内容（string，1-64字符，必选）
- `red_packet_list`：红包列表（array，1-5个元素，必选）
    - `red_packet_id`：红包ID（string，1-128字符，必选）
    - `visible_users`：可见用户（string，1-128字符，必选）

### 8.4 支付宝定向触达 - 活动推荐

**字段定义：**

- `recommend_text`：活动推荐内容（string，1-32字符，必选）
- `activity_list`：活动列表（array，1-5个元素，必选）
    - `title`：活动标题（string，1-10字符，必选）
    - `desc`：活动描述（string，1-14字符，必选）
    - `image_id`：图片ID（string，1-128字符，必选）
    - `action_name`：行动点名称（string，1-4字符，必选）
    - `action_url`：跳转地址（string，1-512字符，必选）
- `crowd_code`：人群code（string，1-20字符，必选）

## 10. 素材物料数据库表设计说明

### 10.1 设计原则

- **统一存储**：只需要一张物料表，通过supply_type字段区分优惠券、红包、活动等不同类型
- **扩展性强**：设计得综合一点，方便以后其他物料也可以使用
- **查询友好**：支持按可用时间范围、名字等条件查询

### 10.2 物料类型扩展

当前支持的物料类型：

- `COUPON`：优惠券类型
- `RED_PACKET`：红包类型
- `ACTIVITY`：活动类型

未来可扩展的物料类型：

- `PRODUCT`：商品类型
- `SERVICE`：服务类型
- `CONTENT`：内容类型

### 10.3 优惠券数据同步

支付宝优惠券数据将通过以下方式同步到素材物料数据库表中：

1. **定时同步**：每天凌晨2点自动同步
2. **手动同步**：提供接口支持手动触发同步
3. **增量同步**：只同步新增和变更的优惠券数据
4. **数据映射**：将支付宝优惠券字段映射到统一的物料表结构

### 10.4 物料管理功能

#### 10.4.1 增删改查功能

- **创建物料**：支持手动创建和API同步创建
- **更新物料**：支持物料信息的修改
- **删除物料**：逻辑删除，保留历史数据
- **查询物料**：支持多种查询条件

#### 10.4.2 查询条件支持

- **按名称查询**：支持物料名称的模糊匹配
- **按类型查询**：支持按supply_type筛选
- **按时间范围查询**：支持按valid_start_time和valid_end_time筛选
- **按状态查询**：支持按启用/禁用状态筛选
- **按平台查询**：支持按platform_type筛选

## 11. 实施计划

### 11.1 开发阶段

#### 第一阶段：基础设施建设（3人天）

- 数据库表设计和创建（素材物料表）
- MessageBO扩展字段设计
- 基础数据模型定义
- 配置文件扩展

#### 第二阶段：现有代码改造（6人天）

- 现有转换器改造（TextMaterialConverter、ImageMaterialConverter等）
- TaskExecutor改造，支持支付宝消息发送
- MaterialTransferService扩展
- 新增支付宝专用转换器开发

#### 第三阶段：物料管理功能开发（5人天）

- 物料管理服务开发（增删改查）
- 支付宝优惠券数据同步功能
- 物料查询功能（按时间范围、名字等）
- 物料管理接口开发

#### 第四阶段：支付宝接口集成（4人天）

- 支付宝API集成
- 图片上传功能（实时返回，不缓存）
- 支付宝消息发送适配器

#### 第五阶段：测试和优化（3人天）

- 单元测试
- 集成测试
- 兼容性测试（确保现有微信功能不受影响）
- 性能优化

#### 第六阶段：部署和验收（3人天）

- 生产环境部署
- 功能验收测试
- 性能监控配置
- 文档整理

**总工作量：25人天**
